<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="40" cy="40" r="40" fill="url(#avatarGradient)"/>
  
  <!-- 用户图标 -->
  <g fill="white" transform="translate(40, 40)">
    <!-- 头部 -->
    <circle cx="0" cy="-8" r="8"/>
    
    <!-- 身体 -->
    <path d="M -12 8 Q -12 0 -8 0 L 8 0 Q 12 0 12 8 L 12 16 L -12 16 Z"/>
  </g>
</svg>
