import { httpClient } from '../common/http/HttpClient';
import {
  WalletInfo,
  WalletRechargeRequest,
  WalletWithdrawRequest,
  WalletTransferRequest
} from '../common/types/index';

/**
 * 钱包相关API接口
 */
export class WalletApi {

  /**
   * 查询钱包余额
   */
  static async getBalance(userId: number): Promise<WalletInfo> {
    const response = await httpClient.get<number>(`/bank/balance/${userId}`);
    return {
      userId: userId,
      balance: response.data,
      lastUpdateTime: new Date().toISOString()
    };
  }

  /**
   * 钱包充值（从银行卡充值到钱包）
   */
  static async recharge(data: WalletRechargeRequest): Promise<void> {
    const formData = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/deposit', formData);
  }

  /**
   * 钱包提现（从钱包提现到银行卡）
   */
  static async withdraw(data: WalletWithdrawRequest): Promise<void> {
    const formData = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/withdraw', formData);
  }

  /**
   * 钱包转账
   */
  static async transfer(data: WalletTransferRequest): Promise<void> {
    const formData = {
      fromUserId: data.fromUserId.toString(),
      targetAccount: data.targetAccount,
      amount: data.amount.toString(),
      paymentMethod: data.paymentMethod || 'WALLET',
      cardId: data.cardId?.toString()
    };
    await httpClient.postForm<void>('/bank/transfer', formData);
  }

  /**
   * 钱包支付
   */
  static async payWithWallet(userId: number, amount: number, paymentChannel: string, targetAccount: string): Promise<void> {
    const formData = {
      userId: userId.toString(),
      amount: amount.toString(),
      paymentChannel: paymentChannel,
      targetAccount: targetAccount
    };
    await httpClient.postForm<void>('/bank/pay/wallet', formData);
  }
}



// 类型定义已移至 common/types/index.ets
