{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29910", "checkEntry": "true", "localPropertiesPath": "F:\\e-wallet\\harmony\\local.properties", "Path": "E:\\hongmeng\\HM\\DevEco Studio\\tools\\node\\", "aceProfilePath": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "F:\\e-wallet\\harmony\\build-profile.json5", "watchMode": "true", "appResource": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets", "aceSoPath": "F:\\e-wallet\\harmony\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "F:\\e-wallet\\harmony\\entry\\.preview\\cache\\.default", "aceModuleBuild": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/LoginPage\",\"pages/MyBankCardPage\",\"pages/TransferPage\",\"pages/BankCardDetailPage\",\"pages/AddBankCardPage\",\"pages/EditBankCardPage\",\"pages/RechargePage\",\"pages/WithdrawPage\",\"pages/TransactionListPage\",\"pages/SettingsPage\",\"pages/PaymentCenterPage\",\"pages/WalletPage\",\"pages/WalletPaymentPage\",\"pages/BankCardPaymentPage\",\"pages/QRCodePaymentPage\",\"pages/NFCPaymentPage\",\"pages/TestPaymentPage\"]}"]}}