{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 528500, "PreviewProcessResource": 4756300, "PreviewCompileResource": 648448500, "PreviewHookCompileResource": 378000, "CopyPreviewProfile": 7078700, "ReplacePreviewerPage": 389500, "buildPreviewerResource": 226500, "PreviewUpdateAssets": 4638000}}, "TOTAL_TIME": 34834394300, "BUILD_ID": "202506261350539910", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750917088800"}}}