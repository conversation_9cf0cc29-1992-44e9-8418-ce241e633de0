{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 1187900, "PreviewProcessResource": 8174600, "PreviewCompileResource": 540286300, "PreviewHookCompileResource": 729500, "CopyPreviewProfile": 18543400, "ReplacePreviewerPage": 1216700, "buildPreviewerResource": 731200, "PreviewUpdateAssets": 9106900, "PreviewArkTS": 24461523400, "PreviewBuild": 439900}}, "TOTAL_TIME": 25934748400, "BUILD_ID": "202506262158249350"}}