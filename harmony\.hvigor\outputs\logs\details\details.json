{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 122556500, "PreCheckSyscap": 3295100, "PreviewProcessResource": 12126400, "PreviewCompileResource": 1370708500, "PreviewHookCompileResource": 2042400, "CopyPreviewProfile": 19069000, "ReplacePreviewerPage": 1554900, "buildPreviewerResource": 300600, "PreviewUpdateAssets": 4065400}}, "TOTAL_TIME": 82975483900, "BUILD_ID": "202506261235382370", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750912621240"}}}