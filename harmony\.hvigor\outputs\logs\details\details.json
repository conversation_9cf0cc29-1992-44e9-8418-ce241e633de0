{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 395700, "PreviewProcessResource": 5241700, "PreviewCompileResource": 637908200, "PreviewHookCompileResource": 373000, "CopyPreviewProfile": 8230400, "ReplacePreviewerPage": 838000, "buildPreviewerResource": 357300, "PreviewUpdateAssets": 4487100}}, "TOTAL_TIME": 28523974300, "BUILD_ID": "202506262137587140", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750945107184"}}}