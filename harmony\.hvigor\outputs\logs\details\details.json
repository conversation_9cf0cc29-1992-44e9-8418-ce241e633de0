{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 1296200, "PreviewProcessResource": 4422900, "PreviewCompileResource": 475215500, "PreviewHookCompileResource": 556700, "CopyPreviewProfile": 10605500, "ReplacePreviewerPage": 545900, "buildPreviewerResource": 327800, "PreviewUpdateAssets": 5869200}}, "TOTAL_TIME": 17971391900, "BUILD_ID": "202506261110219330", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750907439897"}}}