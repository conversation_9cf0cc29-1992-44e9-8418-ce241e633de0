{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 2757900, "PreviewProcessResource": 4929200, "PreviewCompileResource": 713531800, "PreviewHookCompileResource": 389600, "CopyPreviewProfile": 7579200, "ReplacePreviewerPage": 442200, "buildPreviewerResource": 294500, "PreviewUpdateAssets": 4002000}}, "TOTAL_TIME": 57877977400, "BUILD_ID": "202506261023132290", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750904651108"}}}