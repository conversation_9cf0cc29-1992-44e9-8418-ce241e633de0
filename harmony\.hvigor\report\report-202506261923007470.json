{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "b4ba1976-1710-42ab-aa4f-5e33c25e0d70", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277812106800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e65f858-80cd-4c4c-a429-e636a8cb68bf", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12278118781700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4ca74d-271b-489a-afc2-d2eda1817e01", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12278119216800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c528fd22-7089-4ec4-9455-bec2bb3b10b5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769428899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769438989700, "endTime": 12769891004400}, "additional": {"children": ["e0f1aae1-60b2-48ea-ad3b-7c9aff572371", "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "73975e68-9e03-4096-9471-b47be3055001", "83197598-de84-48ca-b520-f2bdd8c95511", "fb503a29-18fd-4d85-89d2-2b43c31ed580", "73f36ad0-b2bf-46b7-8e78-8440c879c3e5", "36e2f9ae-da0b-449a-8b3b-87b7b1b19b0e"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0f1aae1-60b2-48ea-ad3b-7c9aff572371", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769438992000, "endTime": 12769469602900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "55feedcf-62d4-4005-a437-05d278752da2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769469876000, "endTime": 12769889218500}, "additional": {"children": ["a2e71e44-e0f0-4a1b-bab4-bccaec3eae15", "9a3b2f05-8311-4564-84f5-6c4a30ed82a0", "dd59184a-289d-4ea4-93b4-dde79ee5145f", "38292494-329f-4eba-80e6-aee24e192aa1", "a557748e-4ad2-44f3-ad3a-c1b5f6f5aa85", "b2210417-11e2-4d46-8214-bb0f98df51c7", "6edcf1f7-9d4c-490e-aa92-305eb9ad69d0", "22f5e1be-da0b-476e-ae2a-f01e8f3a580e", "414c0a5f-d5f6-4351-969c-5f827ef4b9b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "8921b17e-c656-4c91-8ed0-43f483454a69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73975e68-9e03-4096-9471-b47be3055001", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769889249500, "endTime": 12769890982900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "d25d7aa2-e1d1-48b8-b047-52fb29446283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83197598-de84-48ca-b520-f2bdd8c95511", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769890989700, "endTime": 12769890998900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "b4316d5e-b124-4252-83d7-05bf5483c072"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb503a29-18fd-4d85-89d2-2b43c31ed580", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769445214000, "endTime": 12769445252600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "49e653f1-2db1-45ce-89e7-508c7ab55387"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49e653f1-2db1-45ce-89e7-508c7ab55387", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769445214000, "endTime": 12769445252600}, "additional": {"logType": "info", "children": [], "durationId": "fb503a29-18fd-4d85-89d2-2b43c31ed580", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "73f36ad0-b2bf-46b7-8e78-8440c879c3e5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769461930900, "endTime": 12769461954100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "f32cf393-7086-4074-a52d-c15136506d26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f32cf393-7086-4074-a52d-c15136506d26", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769461930900, "endTime": 12769461954100}, "additional": {"logType": "info", "children": [], "durationId": "73f36ad0-b2bf-46b7-8e78-8440c879c3e5", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "12596a88-b4e8-4aa5-bcb6-0f80fa3cc132", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769462015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d391343-a05b-4b1a-8253-119539f7cdf4", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769469386300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55feedcf-62d4-4005-a437-05d278752da2", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769438992000, "endTime": 12769469602900}, "additional": {"logType": "info", "children": [], "durationId": "e0f1aae1-60b2-48ea-ad3b-7c9aff572371", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "a2e71e44-e0f0-4a1b-bab4-bccaec3eae15", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769496174400, "endTime": 12769496198100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "e74897e6-f888-49b8-a95e-679b817402a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a3b2f05-8311-4564-84f5-6c4a30ed82a0", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769496240200, "endTime": 12769514907100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "e7413b5c-7e0a-417f-812a-c60e83e47594"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd59184a-289d-4ea4-93b4-dde79ee5145f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769514922400, "endTime": 12769770970100}, "additional": {"children": ["098bf20a-4064-41ec-8eeb-798c119025da", "1232087d-d22f-44c4-ac51-a2ae47715a76", "188d83ea-0d6d-4079-a9e7-02768039c5d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "c98ec178-312f-48fb-81d7-49de7a6b50ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38292494-329f-4eba-80e6-aee24e192aa1", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769770989300, "endTime": 12769835155000}, "additional": {"children": ["c6b7e1a6-49c3-4db8-b059-cd03befe1735"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "bc90f405-24f6-4f7a-ad4a-7f1dcadb8927"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a557748e-4ad2-44f3-ad3a-c1b5f6f5aa85", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769835166800, "endTime": 12769866910100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "a20766d2-30ee-4e86-8a83-3ad3ed7ce65d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2210417-11e2-4d46-8214-bb0f98df51c7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769867992700, "endTime": 12769876855300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "89fc5e80-0427-4baf-9352-e5c7fd6d5d11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6edcf1f7-9d4c-490e-aa92-305eb9ad69d0", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769876879100, "endTime": 12769889007100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "fbb822bc-48e3-4c1d-b4d6-02d2031ca7bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22f5e1be-da0b-476e-ae2a-f01e8f3a580e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769889054900, "endTime": 12769889202600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "6180884c-7397-4a5d-ad0c-e29968f0ad42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e74897e6-f888-49b8-a95e-679b817402a9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769496174400, "endTime": 12769496198100}, "additional": {"logType": "info", "children": [], "durationId": "a2e71e44-e0f0-4a1b-bab4-bccaec3eae15", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "e7413b5c-7e0a-417f-812a-c60e83e47594", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769496240200, "endTime": 12769514907100}, "additional": {"logType": "info", "children": [], "durationId": "9a3b2f05-8311-4564-84f5-6c4a30ed82a0", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "098bf20a-4064-41ec-8eeb-798c119025da", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769515743300, "endTime": 12769515761800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dd59184a-289d-4ea4-93b4-dde79ee5145f", "logId": "d8068fdf-db58-4bd1-bd3c-01513be10348"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8068fdf-db58-4bd1-bd3c-01513be10348", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769515743300, "endTime": 12769515761800}, "additional": {"logType": "info", "children": [], "durationId": "098bf20a-4064-41ec-8eeb-798c119025da", "parent": "c98ec178-312f-48fb-81d7-49de7a6b50ab"}}, {"head": {"id": "1232087d-d22f-44c4-ac51-a2ae47715a76", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769518212800, "endTime": 12769770037500}, "additional": {"children": ["4ca6ef7d-6935-4501-989b-1cf4652eb3f6", "c6f76b4a-c38d-4506-833d-08d001f3ec36"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dd59184a-289d-4ea4-93b4-dde79ee5145f", "logId": "43ed1825-a910-46bb-b93c-b76a648fdea9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca6ef7d-6935-4501-989b-1cf4652eb3f6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769518214000, "endTime": 12769554683200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1232087d-d22f-44c4-ac51-a2ae47715a76", "logId": "95defea1-a59a-4823-b4da-1ec7a1ece804"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6f76b4a-c38d-4506-833d-08d001f3ec36", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769554704100, "endTime": 12769770021200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1232087d-d22f-44c4-ac51-a2ae47715a76", "logId": "5ce16f0d-6f3e-4c95-8ab1-37d0ea4b9e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28c33eaf-829c-46cf-81ca-9ae5c6f7e209", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769518222800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4349c415-9517-43a4-9d9f-7cb1700d51ca", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769554537300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95defea1-a59a-4823-b4da-1ec7a1ece804", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769518214000, "endTime": 12769554683200}, "additional": {"logType": "info", "children": [], "durationId": "4ca6ef7d-6935-4501-989b-1cf4652eb3f6", "parent": "43ed1825-a910-46bb-b93c-b76a648fdea9"}}, {"head": {"id": "484aa6ad-ced2-4813-8824-1d7b39577e87", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769554719000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1c9272-097c-4e98-8d71-466673165c95", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769573879400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9643476-62c2-4626-b5fb-594a871bca8f", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769573999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18156e90-ee07-473a-8f27-eafe5427e884", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769574138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b88260-3693-45a9-8f8c-fa3db162bc8e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769574258300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2f7a53-5776-4984-a6eb-77b8b935c8c7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769576377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc4fc0fe-870a-4f75-bb2e-4f6142df071b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769598465000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f83ad346-da93-4807-afda-353ee66c33ee", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769632166700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b7569a-0718-4b21-a04b-d83b454b6bae", "name": "Sdk init in 114 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769713148000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fff18ca-93f0-4abc-8e8b-95fa82ac4390", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769713306300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 23}, "markType": "other"}}, {"head": {"id": "b1428755-1bdd-484d-885a-cdb4033de149", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769713324600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 23}, "markType": "other"}}, {"head": {"id": "52fcf08e-583e-42bf-b354-7fb1e53f87c5", "name": "Project task initialization takes 55 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769769708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148378ac-1990-43e2-98dd-7c00bd310200", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769769836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86e837dd-68f8-4dd0-829d-5670fa88f77d", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769769909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9e5229d-af17-4188-b4dc-68d8abfece6d", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769769967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce16f0d-6f3e-4c95-8ab1-37d0ea4b9e35", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769554704100, "endTime": 12769770021200}, "additional": {"logType": "info", "children": [], "durationId": "c6f76b4a-c38d-4506-833d-08d001f3ec36", "parent": "43ed1825-a910-46bb-b93c-b76a648fdea9"}}, {"head": {"id": "43ed1825-a910-46bb-b93c-b76a648fdea9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769518212800, "endTime": 12769770037500}, "additional": {"logType": "info", "children": ["95defea1-a59a-4823-b4da-1ec7a1ece804", "5ce16f0d-6f3e-4c95-8ab1-37d0ea4b9e35"], "durationId": "1232087d-d22f-44c4-ac51-a2ae47715a76", "parent": "c98ec178-312f-48fb-81d7-49de7a6b50ab"}}, {"head": {"id": "188d83ea-0d6d-4079-a9e7-02768039c5d6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769770932400, "endTime": 12769770949900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dd59184a-289d-4ea4-93b4-dde79ee5145f", "logId": "b5ba0492-868a-42a5-b95a-85aa256e88e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5ba0492-868a-42a5-b95a-85aa256e88e1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769770932400, "endTime": 12769770949900}, "additional": {"logType": "info", "children": [], "durationId": "188d83ea-0d6d-4079-a9e7-02768039c5d6", "parent": "c98ec178-312f-48fb-81d7-49de7a6b50ab"}}, {"head": {"id": "c98ec178-312f-48fb-81d7-49de7a6b50ab", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769514922400, "endTime": 12769770970100}, "additional": {"logType": "info", "children": ["d8068fdf-db58-4bd1-bd3c-01513be10348", "43ed1825-a910-46bb-b93c-b76a648fdea9", "b5ba0492-868a-42a5-b95a-85aa256e88e1"], "durationId": "dd59184a-289d-4ea4-93b4-dde79ee5145f", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "c6b7e1a6-49c3-4db8-b059-cd03befe1735", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769771826600, "endTime": 12769835135100}, "additional": {"children": ["2db74522-d539-4bf0-926b-455e03f0beb2", "518039b0-85a2-436c-9c6a-70c1a4f02474", "0e6f863f-9504-4997-8d40-4bdd86476a27"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38292494-329f-4eba-80e6-aee24e192aa1", "logId": "8fb4ec02-d362-400a-a064-ca62afb23ea7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2db74522-d539-4bf0-926b-455e03f0beb2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769776079400, "endTime": 12769776099500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6b7e1a6-49c3-4db8-b059-cd03befe1735", "logId": "67298aad-70fb-43d9-b9cd-7e913775a99b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67298aad-70fb-43d9-b9cd-7e913775a99b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769776079400, "endTime": 12769776099500}, "additional": {"logType": "info", "children": [], "durationId": "2db74522-d539-4bf0-926b-455e03f0beb2", "parent": "8fb4ec02-d362-400a-a064-ca62afb23ea7"}}, {"head": {"id": "518039b0-85a2-436c-9c6a-70c1a4f02474", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769778424800, "endTime": 12769832948200}, "additional": {"children": ["c554ba71-fa35-4138-b6d4-6dbd190eb214", "ccc081b6-2bef-4e52-9b0a-473c3c12ea90"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6b7e1a6-49c3-4db8-b059-cd03befe1735", "logId": "28f35303-7591-42c8-bcf9-61c7d180b4fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c554ba71-fa35-4138-b6d4-6dbd190eb214", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769778426100, "endTime": 12769784352300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "518039b0-85a2-436c-9c6a-70c1a4f02474", "logId": "4d834d73-e498-4c5b-985d-ff6298d16be8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccc081b6-2bef-4e52-9b0a-473c3c12ea90", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769784379000, "endTime": 12769832931700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "518039b0-85a2-436c-9c6a-70c1a4f02474", "logId": "3b7cb92f-e0d9-419b-bc02-5e5a0010a74b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06587751-7ef3-4488-842a-9b6cb25d0392", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769778433000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3a46f5-4025-4451-89be-a82756ec985c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769784199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d834d73-e498-4c5b-985d-ff6298d16be8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769778426100, "endTime": 12769784352300}, "additional": {"logType": "info", "children": [], "durationId": "c554ba71-fa35-4138-b6d4-6dbd190eb214", "parent": "28f35303-7591-42c8-bcf9-61c7d180b4fe"}}, {"head": {"id": "b5ee1317-34e9-46de-8815-c52d96ba256a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769784397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae4e143-e5d2-4d98-b1e1-cbf72b3456c5", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769812793400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad7f49a-f207-4ef3-8fdc-c646d40af9a0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769812988200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e8d0acb-5c9f-4859-8fb3-dac2dddfd581", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769814576800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b465d7b0-36cb-4b51-9249-e3d9c50fbff2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769814965800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a909ca00-43c7-409e-b7f4-45acbb457900", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769815116200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc4d1a2-19be-4d34-8658-6630e2af9eb5", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769815195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baeb6f7e-d977-4ec0-863d-45e52b9da366", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769815286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11db22bf-b7bf-4928-a86e-84662b8b3d71", "name": "Module entry task initialization takes 15 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769832557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaac7c60-33ed-4ce6-9eaa-9aabfae94db4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769832753400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cfe9a0d-de54-4ebd-9ee7-03b3426ca0bf", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769832822600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393eff08-6791-4e25-a357-6418f40124b0", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769832878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7cb92f-e0d9-419b-bc02-5e5a0010a74b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769784379000, "endTime": 12769832931700}, "additional": {"logType": "info", "children": [], "durationId": "ccc081b6-2bef-4e52-9b0a-473c3c12ea90", "parent": "28f35303-7591-42c8-bcf9-61c7d180b4fe"}}, {"head": {"id": "28f35303-7591-42c8-bcf9-61c7d180b4fe", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769778424800, "endTime": 12769832948200}, "additional": {"logType": "info", "children": ["4d834d73-e498-4c5b-985d-ff6298d16be8", "3b7cb92f-e0d9-419b-bc02-5e5a0010a74b"], "durationId": "518039b0-85a2-436c-9c6a-70c1a4f02474", "parent": "8fb4ec02-d362-400a-a064-ca62afb23ea7"}}, {"head": {"id": "0e6f863f-9504-4997-8d40-4bdd86476a27", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769835098500, "endTime": 12769835114500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6b7e1a6-49c3-4db8-b059-cd03befe1735", "logId": "cf1f0f9e-c79b-420a-a40c-d675f2a31475"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf1f0f9e-c79b-420a-a40c-d675f2a31475", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769835098500, "endTime": 12769835114500}, "additional": {"logType": "info", "children": [], "durationId": "0e6f863f-9504-4997-8d40-4bdd86476a27", "parent": "8fb4ec02-d362-400a-a064-ca62afb23ea7"}}, {"head": {"id": "8fb4ec02-d362-400a-a064-ca62afb23ea7", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769771826600, "endTime": 12769835135100}, "additional": {"logType": "info", "children": ["67298aad-70fb-43d9-b9cd-7e913775a99b", "28f35303-7591-42c8-bcf9-61c7d180b4fe", "cf1f0f9e-c79b-420a-a40c-d675f2a31475"], "durationId": "c6b7e1a6-49c3-4db8-b059-cd03befe1735", "parent": "bc90f405-24f6-4f7a-ad4a-7f1dcadb8927"}}, {"head": {"id": "bc90f405-24f6-4f7a-ad4a-7f1dcadb8927", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769770989300, "endTime": 12769835155000}, "additional": {"logType": "info", "children": ["8fb4ec02-d362-400a-a064-ca62afb23ea7"], "durationId": "38292494-329f-4eba-80e6-aee24e192aa1", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "aef1c73a-61a5-49fe-9ea5-3c539b3d394d", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769866377800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ea4a70-1cbd-4578-9aa1-715cded78707", "name": "hvigorfile, resolve hvigorfile dependencies in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769866794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20766d2-30ee-4e86-8a83-3ad3ed7ce65d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769835166800, "endTime": 12769866910100}, "additional": {"logType": "info", "children": [], "durationId": "a557748e-4ad2-44f3-ad3a-c1b5f6f5aa85", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "414c0a5f-d5f6-4351-969c-5f827ef4b9b7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769867797900, "endTime": 12769867977900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "logId": "6e9008ae-58f4-40e7-89a1-2bc2029b65ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cb07370-66ba-44fd-a8b1-0a7930cd6204", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769867822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9008ae-58f4-40e7-89a1-2bc2029b65ea", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769867797900, "endTime": 12769867977900}, "additional": {"logType": "info", "children": [], "durationId": "414c0a5f-d5f6-4351-969c-5f827ef4b9b7", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "a413dbc2-3436-468a-bc38-4512088976a0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769869379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35362bee-35f0-4021-a0cc-0c22b01d8036", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769875908400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89fc5e80-0427-4baf-9352-e5c7fd6d5d11", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769867992700, "endTime": 12769876855300}, "additional": {"logType": "info", "children": [], "durationId": "b2210417-11e2-4d46-8214-bb0f98df51c7", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "e2827d47-2df8-4d2a-bfdd-baa9e427b85e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769876895400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2398905-2986-415c-878c-840cd4b4ec04", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769883063800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e50c12ac-ee20-4862-b403-f1b99cc32880", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769883179800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25e4437b-506d-42f6-b8db-cb340abd2a52", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769883544600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79acda05-1d74-4423-a2e1-5775220002cf", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769885875300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1354b1-7809-4405-98e1-e89582673a14", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769885954200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb822bc-48e3-4c1d-b4d6-02d2031ca7bf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769876879100, "endTime": 12769889007100}, "additional": {"logType": "info", "children": [], "durationId": "6edcf1f7-9d4c-490e-aa92-305eb9ad69d0", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "19185810-16c6-429e-b353-a572943da413", "name": "Configuration phase cost:393 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769889087800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6180884c-7397-4a5d-ad0c-e29968f0ad42", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769889054900, "endTime": 12769889202600}, "additional": {"logType": "info", "children": [], "durationId": "22f5e1be-da0b-476e-ae2a-f01e8f3a580e", "parent": "8921b17e-c656-4c91-8ed0-43f483454a69"}}, {"head": {"id": "8921b17e-c656-4c91-8ed0-43f483454a69", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769469876000, "endTime": 12769889218500}, "additional": {"logType": "info", "children": ["e74897e6-f888-49b8-a95e-679b817402a9", "e7413b5c-7e0a-417f-812a-c60e83e47594", "c98ec178-312f-48fb-81d7-49de7a6b50ab", "bc90f405-24f6-4f7a-ad4a-7f1dcadb8927", "a20766d2-30ee-4e86-8a83-3ad3ed7ce65d", "89fc5e80-0427-4baf-9352-e5c7fd6d5d11", "fbb822bc-48e3-4c1d-b4d6-02d2031ca7bf", "6180884c-7397-4a5d-ad0c-e29968f0ad42", "6e9008ae-58f4-40e7-89a1-2bc2029b65ea"], "durationId": "b8cfebd6-dbd4-4cce-a18a-57e00ce9882c", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "36e2f9ae-da0b-449a-8b3b-87b7b1b19b0e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769890950900, "endTime": 12769890968800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d", "logId": "ba903d73-8a2d-4895-9be4-78478e22b3eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba903d73-8a2d-4895-9be4-78478e22b3eb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769890950900, "endTime": 12769890968800}, "additional": {"logType": "info", "children": [], "durationId": "36e2f9ae-da0b-449a-8b3b-87b7b1b19b0e", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "d25d7aa2-e1d1-48b8-b047-52fb29446283", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769889249500, "endTime": 12769890982900}, "additional": {"logType": "info", "children": [], "durationId": "73975e68-9e03-4096-9471-b47be3055001", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "b4316d5e-b124-4252-83d7-05bf5483c072", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769890989700, "endTime": 12769890998900}, "additional": {"logType": "info", "children": [], "durationId": "83197598-de84-48ca-b520-f2bdd8c95511", "parent": "b25776e5-cdda-49b9-9232-a5d1b3300af4"}}, {"head": {"id": "b25776e5-cdda-49b9-9232-a5d1b3300af4", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769438989700, "endTime": 12769891004400}, "additional": {"logType": "info", "children": ["55feedcf-62d4-4005-a437-05d278752da2", "8921b17e-c656-4c91-8ed0-43f483454a69", "d25d7aa2-e1d1-48b8-b047-52fb29446283", "b4316d5e-b124-4252-83d7-05bf5483c072", "49e653f1-2db1-45ce-89e7-508c7ab55387", "f32cf393-7086-4074-a52d-c15136506d26", "ba903d73-8a2d-4895-9be4-78478e22b3eb"], "durationId": "c8f55055-59fd-4ede-b5fb-4c973cdc1a8d"}}, {"head": {"id": "fa871214-c8cd-43b4-9625-21e358b5fc6a", "name": "Configuration task cost before running: 457 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769891133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a889d4-2b25-4ba7-bb7d-8cc363434219", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769896347700, "endTime": 12769905504400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2b558588-8365-4a0d-807b-91cb621adf97", "logId": "17bcc2f9-e5d0-4642-944a-65d8e11e8d41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b558588-8365-4a0d-807b-91cb621adf97", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769892894400}, "additional": {"logType": "detail", "children": [], "durationId": "73a889d4-2b25-4ba7-bb7d-8cc363434219"}}, {"head": {"id": "24a3bbdb-c8a4-4141-a7f7-593b52642909", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769893409700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e05cf89-ec33-4b97-a070-a15418405005", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769893508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72de9bbe-d822-4576-b029-671b327deb18", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769896359400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf254781-c490-4601-b129-20361a3e2e2a", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769905239200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc34919-86cf-4d61-a47e-8ff68497e186", "name": "entry : default@PreBuild cost memory 0.29840850830078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769905407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17bcc2f9-e5d0-4642-944a-65d8e11e8d41", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769896347700, "endTime": 12769905504400}, "additional": {"logType": "info", "children": [], "durationId": "73a889d4-2b25-4ba7-bb7d-8cc363434219"}}, {"head": {"id": "1936dd76-c281-4dde-912f-ad4f5713151b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769911499100, "endTime": 12769913770000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "36104b02-26f9-4eec-a46e-5b35ac88f7cd", "logId": "c7d03034-b3c1-4ed1-a32b-f1613e467306"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36104b02-26f9-4eec-a46e-5b35ac88f7cd", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769910248800}, "additional": {"logType": "detail", "children": [], "durationId": "1936dd76-c281-4dde-912f-ad4f5713151b"}}, {"head": {"id": "49b26585-c807-4be4-9230-12456c48af2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769910746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17503484-93f7-44c0-89a8-a40c36d88b20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769910829700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7507e3e-fe23-4c43-9be3-ff45d8f430ab", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769911508100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d9ee52-7e1e-4144-b563-4b5048dffe95", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769913581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9181ccce-ba43-4601-947d-1efeba9f43cc", "name": "entry : default@MergeProfile cost memory 0.11702728271484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769913696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d03034-b3c1-4ed1-a32b-f1613e467306", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769911499100, "endTime": 12769913770000}, "additional": {"logType": "info", "children": [], "durationId": "1936dd76-c281-4dde-912f-ad4f5713151b"}}, {"head": {"id": "f4d717cf-1f2a-4f3a-b1b6-701b294557fb", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769917400600, "endTime": 12769920072000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "79a51c65-2799-4c54-a7c4-65cb7ea05933", "logId": "4f4ba67d-06a7-4602-b602-309ecf0ee360"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79a51c65-2799-4c54-a7c4-65cb7ea05933", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769915719300}, "additional": {"logType": "detail", "children": [], "durationId": "f4d717cf-1f2a-4f3a-b1b6-701b294557fb"}}, {"head": {"id": "74e9d893-00c4-4d22-a94a-ba3d5237a88e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769916396900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53917ac6-d264-4b14-b13f-9c552d160658", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769916511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32361fe-3bb9-4316-ab7a-1328e8cc9429", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769917412400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f11d14-a6f5-47e8-b7a2-9b3e5820d00b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769918485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ffd17a-f47f-449e-9351-f45691654aae", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769919869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dda14c7-ee77-4af1-a1ae-4e1935b63033", "name": "entry : default@CreateBuildProfile cost memory 0.09946441650390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769919974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f4ba67d-06a7-4602-b602-309ecf0ee360", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769917400600, "endTime": 12769920072000}, "additional": {"logType": "info", "children": [], "durationId": "f4d717cf-1f2a-4f3a-b1b6-701b294557fb"}}, {"head": {"id": "3ee297f0-0594-4a9a-9dae-ce4c4e83f30d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923224100, "endTime": 12769923676400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e8674c19-dfef-4835-a954-8d8a411d85b4", "logId": "9badfeee-9b7f-4ed5-9633-6fa9c7422611"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8674c19-dfef-4835-a954-8d8a411d85b4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769921723500}, "additional": {"logType": "detail", "children": [], "durationId": "3ee297f0-0594-4a9a-9dae-ce4c4e83f30d"}}, {"head": {"id": "ced95d09-e4d8-4d8e-bb94-0dd8cd950ccf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769922244800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18704ebf-2030-479d-9ee3-69db2f4a2d30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769922359000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695c0c1b-fdf7-443f-b200-8c1402cea3b4", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923234900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f65a12b-b5da-4405-92dc-434f0054b403", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82329e04-c818-43f2-aa3f-22b7e3bf4061", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923433300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46af6f1-4436-4381-8242-ac7477a4975e", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c20155e-9eaf-4b59-86ea-875c6ba8e136", "name": "runTaskFromQueue task cost before running: 490 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9badfeee-9b7f-4ed5-9633-6fa9c7422611", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769923224100, "endTime": 12769923676400, "totalTime": 366500}, "additional": {"logType": "info", "children": [], "durationId": "3ee297f0-0594-4a9a-9dae-ce4c4e83f30d"}}, {"head": {"id": "bc7f8391-b60d-4bb9-8876-e044eeba7122", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769934095800, "endTime": 12769935219400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6e8b4c10-22ba-4319-8de8-d2295288b84b", "logId": "b3d07102-610a-4584-93e0-8325532fc957"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e8b4c10-22ba-4319-8de8-d2295288b84b", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769925741000}, "additional": {"logType": "detail", "children": [], "durationId": "bc7f8391-b60d-4bb9-8876-e044eeba7122"}}, {"head": {"id": "94ec5032-229e-48a5-b8dd-c0a49c987216", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769926355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29009d0-61e7-4603-aa4d-c4fb3f16876a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769926472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99b0e27-27e0-40c3-b4e7-c4a61d23189c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769934111100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e655398f-9257-419a-b65d-966745066a14", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769934337000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39f9fa6-d688-4088-ac36-f2149a3072d1", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769935038000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0e85c50-32c7-4892-8e71-36c3037737d0", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06705474853515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769935146000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d07102-610a-4584-93e0-8325532fc957", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769934095800, "endTime": 12769935219400}, "additional": {"logType": "info", "children": [], "durationId": "bc7f8391-b60d-4bb9-8876-e044eeba7122"}}, {"head": {"id": "53e57b0d-a105-4555-82d8-665597158d42", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769939125400, "endTime": 12769941375400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e4e816ee-7f11-4a38-9c3d-d99cff298f98", "logId": "06b55510-5def-4999-8cb4-73a6d923eca3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4e816ee-7f11-4a38-9c3d-d99cff298f98", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769937010200}, "additional": {"logType": "detail", "children": [], "durationId": "53e57b0d-a105-4555-82d8-665597158d42"}}, {"head": {"id": "1a5a0a5f-4644-49f8-a7f2-972487735ec8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769937581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35df9d8b-812e-4dde-b039-22a819def666", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769937690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e0542f-23f0-425e-a4e5-7c6fcdccfe24", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769939145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac3d734-ca57-4f03-8f72-f35995b1a791", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769940946200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b03c78-a5b5-4467-ad0e-d577c7692e4d", "name": "entry : default@ProcessProfile cost memory 0.05889129638671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769941182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06b55510-5def-4999-8cb4-73a6d923eca3", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769939125400, "endTime": 12769941375400}, "additional": {"logType": "info", "children": [], "durationId": "53e57b0d-a105-4555-82d8-665597158d42"}}, {"head": {"id": "f6d5d771-0bff-482f-a10b-1485db4053ac", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769948557000, "endTime": 12769956467700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "41fd7dec-966c-49e1-9fff-3872127c13b6", "logId": "28be5b72-c5bf-43b4-aa6d-fd2b04dcede5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41fd7dec-966c-49e1-9fff-3872127c13b6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769944050800}, "additional": {"logType": "detail", "children": [], "durationId": "f6d5d771-0bff-482f-a10b-1485db4053ac"}}, {"head": {"id": "803864be-7859-4601-a8e6-7ca4b25f98e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769944794600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cbaa12-00f1-427c-bae5-cb2f521fa4c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769945060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5c5917-de4b-4b30-a1c2-f73835659a87", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769948577500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7750b7-4b56-42af-8725-0876a7597ca5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769956202200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb17a21a-4143-46ce-a8de-8507646b0125", "name": "entry : default@ProcessRouterMap cost memory 0.19387054443359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769956383800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28be5b72-c5bf-43b4-aa6d-fd2b04dcede5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769948557000, "endTime": 12769956467700}, "additional": {"logType": "info", "children": [], "durationId": "f6d5d771-0bff-482f-a10b-1485db4053ac"}}, {"head": {"id": "9bd0ad0c-d845-4fb5-bd1e-8a74db3c3876", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769968131300, "endTime": 12769972535000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "68acb4a2-1437-4643-815e-d2808ed26142", "logId": "d16f5509-71da-4e41-bc97-64af6db7a915"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68acb4a2-1437-4643-815e-d2808ed26142", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769961538300}, "additional": {"logType": "detail", "children": [], "durationId": "9bd0ad0c-d845-4fb5-bd1e-8a74db3c3876"}}, {"head": {"id": "6ed12d40-68b1-483d-aa77-9198d397ab62", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769962388700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e755367-cbd0-449c-8d17-3b97e089b34e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769962565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5ffefe-82ff-42f3-96dc-c25d82efdb96", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769964373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade11dfa-cdcc-44cf-a6dc-e815079dd712", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769970058900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4be4a93e-22f4-4753-96a3-5ef0e5c69100", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769970250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db3a70c4-65fe-4bc0-86e1-14f7c08ccbef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769970328600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0137f9-6ea0-45a0-8143-791d1ac2a0dd", "name": "entry : default@PreviewProcessResource cost memory 0.0683746337890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769970421700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c881b29-4774-4157-bea3-c9cc233b7bb5", "name": "runTaskFromQueue task cost before running: 539 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769972338700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16f5509-71da-4e41-bc97-64af6db7a915", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769968131300, "endTime": 12769972535000, "totalTime": 2425200}, "additional": {"logType": "info", "children": [], "durationId": "9bd0ad0c-d845-4fb5-bd1e-8a74db3c3876"}}, {"head": {"id": "d36a9da8-8747-4a04-85f5-e4414fe7f0f1", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769982208300, "endTime": 12770004404800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fc62f326-471f-40e3-9735-87e5a5f86c74", "logId": "86186931-c63d-422d-a352-98b2cf44dd15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc62f326-471f-40e3-9735-87e5a5f86c74", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769976781400}, "additional": {"logType": "detail", "children": [], "durationId": "d36a9da8-8747-4a04-85f5-e4414fe7f0f1"}}, {"head": {"id": "9ad72b9f-3800-48cd-838d-957cda1ab485", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769977303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3430628-8a45-45fd-bfcb-fd18439f9265", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769977419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "773d0772-dbec-4525-9954-0840103<PERSON><PERSON>e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769982231000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70cc05a7-8182-4d1c-a197-4832b4f3f932", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770004176700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ac261c-0b94-48d6-83b6-d5fe1324ef8e", "name": "entry : default@GenerateLoaderJson cost memory -0.9671401977539062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770004324600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86186931-c63d-422d-a352-98b2cf44dd15", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769982208300, "endTime": 12770004404800}, "additional": {"logType": "info", "children": [], "durationId": "d36a9da8-8747-4a04-85f5-e4414fe7f0f1"}}, {"head": {"id": "1c459b43-e085-468d-b34f-b7a5c770f799", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770016415800, "endTime": 12770569953500}, "additional": {"children": ["cfb68368-5c5b-4aca-8cbd-d93a0f88c361", "1bc8e7e7-680a-4060-8956-8d17a372b7cf", "b93bb0e5-2f4d-48fc-a0fc-8e57e26a0875", "c6625f63-d13a-42f7-855e-ceec0049be91"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "a5389714-ab3d-43aa-aea9-e72f1fd11ea6", "logId": "7472b671-db1e-4e7a-a50e-ba1837cf271f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5389714-ab3d-43aa-aea9-e72f1fd11ea6", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770012333700}, "additional": {"logType": "detail", "children": [], "durationId": "1c459b43-e085-468d-b34f-b7a5c770f799"}}, {"head": {"id": "82c517d5-609f-417d-bc12-f85d3c59d169", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770012868200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8fd1a6-e4fd-4820-8f2f-0fb6cb990c0f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770012980400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546e698c-cc15-4b9a-a9ba-49ddb8d37773", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770014070600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9bcd33-164d-4ded-95cf-0356c032ed1b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770016443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41cf6b10-b6fd-48b2-8b23-3e04665d8ac5", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770048388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849410c9-5e8a-458a-84a7-bad6824c87d7", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 32 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770048538100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb68368-5c5b-4aca-8cbd-d93a0f88c361", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770049630900, "endTime": 12770115966900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c459b43-e085-468d-b34f-b7a5c770f799", "logId": "ee5a86a5-0f80-472f-bb98-61ad17b34f73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee5a86a5-0f80-472f-bb98-61ad17b34f73", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770049630900, "endTime": 12770115966900}, "additional": {"logType": "info", "children": [], "durationId": "cfb68368-5c5b-4aca-8cbd-d93a0f88c361", "parent": "7472b671-db1e-4e7a-a50e-ba1837cf271f"}}, {"head": {"id": "ffe622f1-54d6-46e5-bff2-cc46939d9ae4", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770116416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc8e7e7-680a-4060-8956-8d17a372b7cf", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770118162300, "endTime": 12770266654700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c459b43-e085-468d-b34f-b7a5c770f799", "logId": "9c6e5e02-4362-42ed-b3ca-0bc7524f80c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad70e885-8145-4342-98a1-98b3a1ed388d", "name": "current process  memoryUsage: {\n  rss: 112676864,\n  heapTotal: 120619008,\n  heapUsed: 110512448,\n  external: 3141707,\n  arrayBuffers: 135572\n} os memoryUsage :6.696170806884766", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770119924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9806da0-bf44-4444-8741-b75e29ab9ee5", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770264000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6e5e02-4362-42ed-b3ca-0bc7524f80c1", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770118162300, "endTime": 12770266654700}, "additional": {"logType": "info", "children": [], "durationId": "1bc8e7e7-680a-4060-8956-8d17a372b7cf", "parent": "7472b671-db1e-4e7a-a50e-ba1837cf271f"}}, {"head": {"id": "7b1a9d35-ac8a-4df4-8765-2ae930945be8", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770266838300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b93bb0e5-2f4d-48fc-a0fc-8e57e26a0875", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770268316800, "endTime": 12770365562500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c459b43-e085-468d-b34f-b7a5c770f799", "logId": "a08cebd3-de2c-4ffc-9466-ef1d21897622"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "354af4af-3f2f-442e-b368-c9679c6d481f", "name": "current process  memoryUsage: {\n  rss: 112726016,\n  heapTotal: 120619008,\n  heapUsed: 110797992,\n  external: 3141833,\n  arrayBuffers: 135713\n} os memoryUsage :6.7115325927734375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770269465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabbcaf0-df90-48c2-8ac2-f69fd0edb4ae", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770363250400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a08cebd3-de2c-4ffc-9466-ef1d21897622", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770268316800, "endTime": 12770365562500}, "additional": {"logType": "info", "children": [], "durationId": "b93bb0e5-2f4d-48fc-a0fc-8e57e26a0875", "parent": "7472b671-db1e-4e7a-a50e-ba1837cf271f"}}, {"head": {"id": "6292e9e0-5da8-4968-8faf-d1b302072937", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770365940800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6625f63-d13a-42f7-855e-ceec0049be91", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770367558400, "endTime": 12770568122800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c459b43-e085-468d-b34f-b7a5c770f799", "logId": "b8622907-cb4c-4757-80b1-451d7d6a07e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cca6c03b-6886-47d2-bf44-498e5588d8b6", "name": "current process  memoryUsage: {\n  rss: 112758784,\n  heapTotal: 120619008,\n  heapUsed: 111069784,\n  external: 3141959,\n  arrayBuffers: 136653\n} os memoryUsage :6.713840484619141", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770369022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d128e0f1-4626-4779-8005-ab366259df9d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770564280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8622907-cb4c-4757-80b1-451d7d6a07e1", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770367558400, "endTime": 12770568122800}, "additional": {"logType": "info", "children": [], "durationId": "c6625f63-d13a-42f7-855e-ceec0049be91", "parent": "7472b671-db1e-4e7a-a50e-ba1837cf271f"}}, {"head": {"id": "aba639aa-18d0-422f-b1bb-c652fa0c125e", "name": "entry : default@PreviewCompileResource cost memory 0.1153411865234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770569514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "049e7cbb-31e8-4125-9058-6e945e13d054", "name": "runTaskFromQueue task cost before running: 1 s 136 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770569797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7472b671-db1e-4e7a-a50e-ba1837cf271f", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770016415800, "endTime": 12770569953500, "totalTime": 553305200}, "additional": {"logType": "info", "children": ["ee5a86a5-0f80-472f-bb98-61ad17b34f73", "9c6e5e02-4362-42ed-b3ca-0bc7524f80c1", "a08cebd3-de2c-4ffc-9466-ef1d21897622", "b8622907-cb4c-4757-80b1-451d7d6a07e1"], "durationId": "1c459b43-e085-468d-b34f-b7a5c770f799"}}, {"head": {"id": "102f027c-6261-4901-8b5b-d35b1d810517", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574584300, "endTime": 12770575018000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4ae960c7-1f72-47d9-84b9-7ae1d818d399", "logId": "3b72e861-aca3-4c72-a2c2-ad253cbb5101"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ae960c7-1f72-47d9-84b9-7ae1d818d399", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770573571300}, "additional": {"logType": "detail", "children": [], "durationId": "102f027c-6261-4901-8b5b-d35b1d810517"}}, {"head": {"id": "77b036c9-bb91-44de-8fe4-ca5068992cae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656956ff-9315-47af-ba13-8c015ce5360a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574453900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460cbf0c-0f1a-4546-add2-a5e17bc8409d", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174f4a96-f2e5-4f06-bfda-2a7304c889fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574718100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b48b88-8505-455d-bd83-4a2f8f717386", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574779900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f2ea28e-cbc6-4606-b7e3-0e5265a5d554", "name": "entry : default@PreviewHookCompileResource cost memory 0.03851318359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b51fae72-6b71-4bfd-a3f2-45ace793d028", "name": "runTaskFromQueue task cost before running: 1 s 141 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b72e861-aca3-4c72-a2c2-ad253cbb5101", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770574584300, "endTime": 12770575018000, "totalTime": 342900}, "additional": {"logType": "info", "children": [], "durationId": "102f027c-6261-4901-8b5b-d35b1d810517"}}, {"head": {"id": "ee077307-c811-4856-a043-312997b0588d", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770580758200, "endTime": 12770591932600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "53b25426-1661-4498-b218-32aaa9765d58", "logId": "e23ba14c-f3a1-46e9-bc97-e709024256c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53b25426-1661-4498-b218-32aaa9765d58", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770577732200}, "additional": {"logType": "detail", "children": [], "durationId": "ee077307-c811-4856-a043-312997b0588d"}}, {"head": {"id": "dc1ee79a-ede1-4c85-813e-471ebe5ccf71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770579299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2962e905-64df-45c4-99dd-12ab37baebb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770579488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc513360-3d7d-492f-b234-c5c4884e155f", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770580785800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7a175f-db98-42fd-b6a1-1d1120e20374", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770583342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97cb4d8f-0be0-48db-bce9-b628d6b0bbea", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770583573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460c7cd3-c371-42d3-9d14-677b10873b85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770583761200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e739aab-1fa4-4c2b-970a-c2bc1bd342f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770583877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec0959e1-219f-4fe4-84df-1a902f0e9149", "name": "entry : default@CopyPreviewProfile cost memory 0.2129974365234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770590911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d1a5b0-fde9-4036-b65e-e397594d0edf", "name": "runTaskFromQueue task cost before running: 1 s 158 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770591831600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23ba14c-f3a1-46e9-bc97-e709024256c2", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770580758200, "endTime": 12770591932600, "totalTime": 11024700}, "additional": {"logType": "info", "children": [], "durationId": "ee077307-c811-4856-a043-312997b0588d"}}, {"head": {"id": "a13b2002-e418-490c-b9dd-b6dbc0cd6e5c", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595359700, "endTime": 12770595819100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ce075d60-378f-43b7-8a53-ae2f2f82ef84", "logId": "e7d34195-7432-4898-9a8a-8639ae664c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce075d60-378f-43b7-8a53-ae2f2f82ef84", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770594001600}, "additional": {"logType": "detail", "children": [], "durationId": "a13b2002-e418-490c-b9dd-b6dbc0cd6e5c"}}, {"head": {"id": "bb06d2ef-5f6a-4482-a042-aa7c14fd86e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770594490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4e7cc7-882f-4b6e-89f0-b1c7d07af792", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770594588100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5d8573-64a9-4c99-9824-ec302c08000b", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595393000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ad58e3-d3fe-4344-8d95-03e2fc6b4cb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595509100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "890d7268-106a-4fea-b410-a542ae69c110", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99facce7-d95f-4eb2-8e3b-9ca047f72fd9", "name": "entry : default@ReplacePreviewerPage cost memory 0.038482666015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595663100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571b0dfe-23f2-4414-bf8d-cfd32af4aa60", "name": "runTaskFromQueue task cost before running: 1 s 162 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595759600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d34195-7432-4898-9a8a-8639ae664c8d", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770595359700, "endTime": 12770595819100, "totalTime": 375300}, "additional": {"logType": "info", "children": [], "durationId": "a13b2002-e418-490c-b9dd-b6dbc0cd6e5c"}}, {"head": {"id": "0c4e4bb5-baac-44b1-b8b6-1ac440967af7", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770597853800, "endTime": 12770598199800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2c1c7b4a-a1f0-4dc9-8e46-f163d62141aa", "logId": "a301ce9f-fb5d-4b3b-b7be-ae5f5afd59fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c1c7b4a-a1f0-4dc9-8e46-f163d62141aa", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770597791000}, "additional": {"logType": "detail", "children": [], "durationId": "0c4e4bb5-baac-44b1-b8b6-1ac440967af7"}}, {"head": {"id": "842b40fe-46b1-4486-b02f-1471f279bf3f", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770597864200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09511c88-aba2-4edd-a1b8-0f2cd52abaca", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770598003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f21e6da-3537-4cd1-b254-9ee01ef26e29", "name": "runTaskFromQueue task cost before running: 1 s 164 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770598129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a301ce9f-fb5d-4b3b-b7be-ae5f5afd59fd", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770597853800, "endTime": 12770598199800, "totalTime": 246700}, "additional": {"logType": "info", "children": [], "durationId": "0c4e4bb5-baac-44b1-b8b6-1ac440967af7"}}, {"head": {"id": "ae688f2b-f56d-4519-9b4d-7c7a0bf394a5", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770602965700, "endTime": 12770607162200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "2424c6be-aa79-413d-b914-af4badf75a6b", "logId": "1f73f52d-d3c2-4a38-a1ba-1d1c8359fb15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2424c6be-aa79-413d-b914-af4badf75a6b", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770600510000}, "additional": {"logType": "detail", "children": [], "durationId": "ae688f2b-f56d-4519-9b4d-7c7a0bf394a5"}}, {"head": {"id": "e0497fc7-d63a-4f1c-9d11-ae7c8e36cde3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770601337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e5399b-40e7-4c67-8cc5-7448873ef59b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770601515700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24110c9c-91ca-4acb-937e-fb161f6c50b3", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770602987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3540032-b659-4784-924c-0156a4ae99f0", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770605653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe0ea90-8010-49b5-81df-056e3018826e", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770605811700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281231ba-fda5-4115-9cd5-672abdd890ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770605918800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87bc645a-dfd0-4464-9a35-c6fc7a29f9b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770605982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f93d1961-54db-4841-b326-7dd3e2c721bf", "name": "entry : default@PreviewUpdateAssets cost memory 0.1311492919921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770606920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a065245-5ddd-4099-b9f3-cbb7e425d460", "name": "runTaskFromQueue task cost before running: 1 s 173 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770607065000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f73f52d-d3c2-4a38-a1ba-1d1c8359fb15", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770602965700, "endTime": 12770607162200, "totalTime": 4073100}, "additional": {"logType": "info", "children": [], "durationId": "ae688f2b-f56d-4519-9b4d-7c7a0bf394a5"}}, {"head": {"id": "2659a3b2-c150-4e73-9375-3e7120bf4cbe", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770617003600, "endTime": 12791927246700}, "additional": {"children": ["86e0215f-487c-4a0f-9522-9057742c20a9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ae8c2bf1-3bb9-4c1a-94d4-c2d020ce8b0d", "logId": "5b7a0696-8809-4d45-911b-574d0534d89a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae8c2bf1-3bb9-4c1a-94d4-c2d020ce8b0d", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770610644600}, "additional": {"logType": "detail", "children": [], "durationId": "2659a3b2-c150-4e73-9375-3e7120bf4cbe"}}, {"head": {"id": "8e51854c-57dc-4638-8886-5853591d3614", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770611314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc50882-7aed-48b8-86bc-8a9a3c453a21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770611431400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a18a75-4aa0-48d6-bda6-5d7f58378659", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770617016100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86e0215f-487c-4a0f-9522-9057742c20a9", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12770642166300, "endTime": 12791924459900}, "additional": {"children": ["b5736545-8a32-454f-be46-6aeb292133cb", "f433ff7d-c9a1-4953-bbb3-ae04918350f2", "ad8c0a8e-b967-463d-bdf9-36d636504801", "2fcf219e-6ce8-4a60-9bf2-fbbca04eba11", "d85e9c25-74e9-4362-a2d8-28db59982241"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2659a3b2-c150-4e73-9375-3e7120bf4cbe", "logId": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2793c518-182c-43e3-a18c-18cd676b913b", "name": "entry : default@PreviewArkTS cost memory -0.8056716918945312", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770645766200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde2cf67-3943-4d53-9b5f-6fc427a8a2b6", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12775187005500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5736545-8a32-454f-be46-6aeb292133cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12775188061100, "endTime": 12775188076900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "0d510050-cf05-43b7-b17a-c4fcd7646256"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d510050-cf05-43b7-b17a-c4fcd7646256", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12775188061100, "endTime": 12775188076900}, "additional": {"logType": "info", "children": [], "durationId": "b5736545-8a32-454f-be46-6aeb292133cb", "parent": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}}, {"head": {"id": "b642c2ad-a6b3-428d-af8e-305ad9984e59", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791922984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f433ff7d-c9a1-4953-bbb3-ae04918350f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12791924087500, "endTime": 12791924122500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "b7629d78-25c6-49ec-9812-f09d5207ae0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7629d78-25c6-49ec-9812-f09d5207ae0a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791924087500, "endTime": 12791924122500}, "additional": {"logType": "info", "children": [], "durationId": "f433ff7d-c9a1-4953-bbb3-ae04918350f2", "parent": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}}, {"head": {"id": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12770642166300, "endTime": 12791924459900}, "additional": {"logType": "info", "children": ["0d510050-cf05-43b7-b17a-c4fcd7646256", "b7629d78-25c6-49ec-9812-f09d5207ae0a", "f4a9da99-8388-4385-be52-f8bc9c6fd89e", "7450395a-5480-4f90-baaf-59918ea42adc", "72beb2e3-f8c0-4919-bd10-b4c989d77caf"], "durationId": "86e0215f-487c-4a0f-9522-9057742c20a9", "parent": "5b7a0696-8809-4d45-911b-574d0534d89a"}}, {"head": {"id": "ad8c0a8e-b967-463d-bdf9-36d636504801", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12773769683900, "endTime": 12775164849700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "f4a9da99-8388-4385-be52-f8bc9c6fd89e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4a9da99-8388-4385-be52-f8bc9c6fd89e", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12773769683900, "endTime": 12775164849700}, "additional": {"logType": "info", "children": [], "durationId": "ad8c0a8e-b967-463d-bdf9-36d636504801", "parent": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}}, {"head": {"id": "2fcf219e-6ce8-4a60-9bf2-fbbca04eba11", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12775165056500, "endTime": 12775165195200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "7450395a-5480-4f90-baaf-59918ea42adc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7450395a-5480-4f90-baaf-59918ea42adc", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12775165056500, "endTime": 12775165195200}, "additional": {"logType": "info", "children": [], "durationId": "2fcf219e-6ce8-4a60-9bf2-fbbca04eba11", "parent": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}}, {"head": {"id": "d85e9c25-74e9-4362-a2d8-28db59982241", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker4", "startTime": 12775165319500, "endTime": 12791923076600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "72beb2e3-f8c0-4919-bd10-b4c989d77caf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72beb2e3-f8c0-4919-bd10-b4c989d77caf", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12775165319500, "endTime": 12791923076600}, "additional": {"logType": "info", "children": [], "durationId": "d85e9c25-74e9-4362-a2d8-28db59982241", "parent": "63dfca4b-ba6e-4e56-a68e-063b12e3fc47"}}, {"head": {"id": "5b7a0696-8809-4d45-911b-574d0534d89a", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12770617003600, "endTime": 12791927246700, "totalTime": 21310226600}, "additional": {"logType": "info", "children": ["63dfca4b-ba6e-4e56-a68e-063b12e3fc47"], "durationId": "2659a3b2-c150-4e73-9375-3e7120bf4cbe"}}, {"head": {"id": "97c75c62-cbcc-445b-994f-31116ea08003", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932525800, "endTime": 12791932819500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "041df191-ec30-4f10-86bb-f9b52d7baa86", "logId": "e35fd98f-ba59-4d88-911e-f39c747a417e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "041df191-ec30-4f10-86bb-f9b52d7baa86", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932480400}, "additional": {"logType": "detail", "children": [], "durationId": "97c75c62-cbcc-445b-994f-31116ea08003"}}, {"head": {"id": "0a0f807c-7791-4c90-9592-e94e8dc22690", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b729d636-a4e1-4f75-a5f8-defe9bdd103e", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de07843d-0302-4522-8ddf-8df5d41b8910", "name": "runTaskFromQueue task cost before running: 22 s 499 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35fd98f-ba59-4d88-911e-f39c747a417e", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791932525800, "endTime": 12791932819500, "totalTime": 208200}, "additional": {"logType": "info", "children": [], "durationId": "97c75c62-cbcc-445b-994f-31116ea08003"}}, {"head": {"id": "320f3ff9-10a6-459e-9b2b-279911b6aa34", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791938975000, "endTime": 12791938993600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a336910-2977-4495-95ae-dd9cb5720392", "logId": "601a0cb6-1bd1-4757-aa02-306310e64cec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "601a0cb6-1bd1-4757-aa02-306310e64cec", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791938975000, "endTime": 12791938993600}, "additional": {"logType": "info", "children": [], "durationId": "320f3ff9-10a6-459e-9b2b-279911b6aa34"}}, {"head": {"id": "f28e7d1b-2a2c-43d9-a50c-ea9a3f4f3eda", "name": "BUILD SUCCESSFUL in 22 s 505 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939033600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "dae653e3-a33d-4fdb-8b2d-abf62e8dae42", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12769434273500, "endTime": 12791939234300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 23}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9b7b6fdb-6d51-4161-98fb-2893f60ca676", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939251200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba427ca6-1002-41d8-8240-c247a0576341", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939306900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac999b43-2335-4052-b49e-0907e13e264a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c291ee0-047c-4521-b515-7f405fced3f2", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939401900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9264ba5f-097a-467e-a188-dedce2d11bc3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939445000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f929f660-0d1b-4750-bae2-e598985e4b7a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a35a02-45f0-4406-9041-3bde3e573d21", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791939526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33cdaae4-51e2-41f1-9fa2-6207255a1c6f", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791940226700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2dc5796-162d-4fbb-820c-fccc6aeb8934", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791948405100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b9634d-9d3d-462a-afe2-e3922a91061a", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791948696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9691a0-d578-4adf-9bf5-1d378559eca2", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791961453800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ac890e-f974-4b52-89b6-360b9cb5b946", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791962178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2fc9757-bbb3-4145-ad10-de39c9a0c9f1", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791962393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dea39fe-0128-48b7-b0f5-319f8bdd71c6", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791963285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d551a965-fa9f-49a8-bfba-abf2897ea6dd", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791964180300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c411b354-33c6-4dd0-a270-5723691149ca", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791964562000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a75da59b-7497-4e0d-8048-96b8ba515eec", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791964926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "330fb66d-79c9-40f7-8727-a71b26af601c", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791965322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea09565a-6bff-49c4-add9-bb1f9230459b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791968523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb30f312-29a9-4f18-aa49-d79af1d29373", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791969560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46beff56-1ab3-492a-9f3e-ea1b717fc71c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791969873700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e6d40c-8965-4332-9d93-05f0c5213433", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791970249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90dcf32-2604-4ae6-b15f-2e67d83a2e87", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791971319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "becba56d-5c11-41ff-8ce2-d8b392bfed7b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791983759800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63b75a7-e921-43a7-b279-c7f7739be48e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791984273200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c6f800-88b8-4433-a66b-4f58e736e55c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791984612100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3542793-0c45-49fe-853c-61b2935e77c7", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791985001100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8cbf61c-7f11-4f43-8f6a-f96a0e319f7e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12791985277700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}