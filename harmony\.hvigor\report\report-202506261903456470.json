{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "0373a0bd-f90c-4ead-aa36-c3173d4dc1b3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 10926519877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd46647-b57a-4422-932e-a347c12af446", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11498704781100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c30ce49-93fd-4f85-87d8-0c59f62b6093", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11499426128000, "endTime": 11499426172800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "05f0d44b-ca7f-417f-8141-8cc7c84717e3", "logId": "959de65d-aa11-497e-ab85-1e4ae1bfffc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "959de65d-aa11-497e-ab85-1e4ae1bfffc9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11499426128000, "endTime": 11499426172800}, "additional": {"logType": "info", "children": [], "durationId": "5c30ce49-93fd-4f85-87d8-0c59f62b6093"}}, {"head": {"id": "37b8e75e-f360-4cfc-a9de-66ea7b62c035", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11519969056800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336dfccd-3868-4c84-8aba-fc169907dd0d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11519970331900, "endTime": 11519970353100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "05f0d44b-ca7f-417f-8141-8cc7c84717e3", "logId": "2300a441-50f1-43c2-b3a5-6c033e75cef6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2300a441-50f1-43c2-b3a5-6c033e75cef6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11519970331900, "endTime": 11519970353100}, "additional": {"logType": "info", "children": [], "durationId": "336dfccd-3868-4c84-8aba-fc169907dd0d"}}, {"head": {"id": "380fb4b1-8e25-4ca4-9e59-8907419c91d6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11613280748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11614620550100, "endTime": 11621293069500}, "additional": {"children": ["0fa557bc-eef3-4622-b0ad-cdc222ce8ec2", "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "77a0ef02-03d2-4886-800f-8754b0d14560", "de854647-765d-4909-adb7-d38e4fbbfff2", "9e8bd39d-ae8a-46b9-b3e6-eac25d33843f", "6b2b1abb-4ea4-499e-b179-a4c245c804a8", "35627a64-793a-41e2-8ec7-63ae07b37db0"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "78f86695-b988-44e3-96b6-35329745a226"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fa557bc-eef3-4622-b0ad-cdc222ce8ec2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11614620551700, "endTime": 11615767219800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "d7664c28-6ae5-45fb-b873-799debe71021"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615767243100, "endTime": 11621257702800}, "additional": {"children": ["af5270af-7869-4aae-a196-2e954dac0bfe", "9949151a-e2fe-416f-9cd1-d0255b0cc43b", "15b8574c-d2e7-46c0-97c5-ba68ca659873", "42f39d45-6691-4a9f-ad8a-ba49b555dbb7", "f7067c22-0bc8-4ecf-8e90-6b02aba447e2", "27111deb-6742-4f5e-b97f-7217e0922611", "f6018d52-5fca-4079-8678-2594750b1911", "819e2b1d-6bbb-4db2-b2ce-6ca6d725773d", "ecc73da8-c91c-410c-992f-178629dd6dde"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "35680990-698e-4cd0-b5f4-41cdec5f562b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77a0ef02-03d2-4886-800f-8754b0d14560", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621257750900, "endTime": 11621293036700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "e2b07bde-79cc-4687-8dfd-e29d577b059e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de854647-765d-4909-adb7-d38e4fbbfff2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621293043500, "endTime": 11621293065100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "5eabb6d6-c552-4b37-85db-9d96bee78ba0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e8bd39d-ae8a-46b9-b3e6-eac25d33843f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615140469500, "endTime": 11615140524500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "8487f1c8-0dd5-4609-8df7-756f86171020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8487f1c8-0dd5-4609-8df7-756f86171020", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615140469500, "endTime": 11615140524500}, "additional": {"logType": "info", "children": [], "durationId": "9e8bd39d-ae8a-46b9-b3e6-eac25d33843f", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "6b2b1abb-4ea4-499e-b179-a4c245c804a8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615171146500, "endTime": 11615171800300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "54702b5e-88cb-4273-89b4-80e422b8bc61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54702b5e-88cb-4273-89b4-80e422b8bc61", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615171146500, "endTime": 11615171800300}, "additional": {"logType": "info", "children": [], "durationId": "6b2b1abb-4ea4-499e-b179-a4c245c804a8", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "3ebd9a16-4c6c-47de-9382-3528c70aca93", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615240449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1281a040-e215-4565-a421-28154a91ff21", "name": "Cache service initialization finished in 477 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615767031300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7664c28-6ae5-45fb-b873-799debe71021", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11614620551700, "endTime": 11615767219800}, "additional": {"logType": "info", "children": [], "durationId": "0fa557bc-eef3-4622-b0ad-cdc222ce8ec2", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "af5270af-7869-4aae-a196-2e954dac0bfe", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615792031400, "endTime": 11615792043100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "fa545ae4-c066-40fd-9b67-247df02662e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9949151a-e2fe-416f-9cd1-d0255b0cc43b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615792059700, "endTime": 11615871590300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "ec1d421c-c5d9-46ec-a659-59c2ccaf1df3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15b8574c-d2e7-46c0-97c5-ba68ca659873", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615871655400, "endTime": 11619552294300}, "additional": {"children": ["b6db8d19-9139-49dc-b44a-ecd79c09c3cf", "2bcc6f3a-cfda-4eed-944d-ee060fc4cfad", "89155f68-edb5-4c16-a2d5-4cfbe428c5b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "ca8c2289-77b0-4201-b507-cb52ec567544"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42f39d45-6691-4a9f-ad8a-ba49b555dbb7", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619552313800, "endTime": 11619660970400}, "additional": {"children": ["a747b87b-905e-4abc-9580-87cf12382756"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "b4e5fedb-31b2-4ce1-a69a-09d6b144a82d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7067c22-0bc8-4ecf-8e90-6b02aba447e2", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619660980100, "endTime": 11620886980100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "83d6085d-788b-4fa8-a78b-c23f9ea5028f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27111deb-6742-4f5e-b97f-7217e0922611", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620888278000, "endTime": 11621136022000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "a04ed73a-a3a0-4ccc-962f-c46f60b1dcaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6018d52-5fca-4079-8678-2594750b1911", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621136088000, "endTime": 11621257365100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "fa91b9e4-3e1f-4d34-a648-749cfe9b651b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "819e2b1d-6bbb-4db2-b2ce-6ca6d725773d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621257407100, "endTime": 11621257674700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "4248fd66-e62c-4a2a-9082-aaadfc4f92b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa545ae4-c066-40fd-9b67-247df02662e2", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615792031400, "endTime": 11615792043100}, "additional": {"logType": "info", "children": [], "durationId": "af5270af-7869-4aae-a196-2e954dac0bfe", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "ec1d421c-c5d9-46ec-a659-59c2ccaf1df3", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615792059700, "endTime": 11615871590300}, "additional": {"logType": "info", "children": [], "durationId": "9949151a-e2fe-416f-9cd1-d0255b0cc43b", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "b6db8d19-9139-49dc-b44a-ecd79c09c3cf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615992350800, "endTime": 11615992371500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15b8574c-d2e7-46c0-97c5-ba68ca659873", "logId": "ffd0ac00-4c39-4608-84a1-94417d30d0e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffd0ac00-4c39-4608-84a1-94417d30d0e2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615992350800, "endTime": 11615992371500}, "additional": {"logType": "info", "children": [], "durationId": "b6db8d19-9139-49dc-b44a-ecd79c09c3cf", "parent": "ca8c2289-77b0-4201-b507-cb52ec567544"}}, {"head": {"id": "2bcc6f3a-cfda-4eed-944d-ee060fc4cfad", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615994586600, "endTime": 11619551329300}, "additional": {"children": ["801e4848-5f56-4bbe-8f27-12d78cca4a9d", "0848eca3-40f3-466e-a7b1-52a0fb9a716e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15b8574c-d2e7-46c0-97c5-ba68ca659873", "logId": "fb14c66a-f5be-43b6-9ef9-bfb18b12bb2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "801e4848-5f56-4bbe-8f27-12d78cca4a9d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615994587500, "endTime": 11617879964800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcc6f3a-cfda-4eed-944d-ee060fc4cfad", "logId": "897a64f4-a92e-4c30-8be4-b9d310654bb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0848eca3-40f3-466e-a7b1-52a0fb9a716e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11617879985600, "endTime": 11619551300900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcc6f3a-cfda-4eed-944d-ee060fc4cfad", "logId": "54852a03-5661-47fe-a426-e4683fb8b9ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e618f47-84b6-4dfa-9ede-8cb1328ade30", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615994594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb607974-e383-4b4c-8bcd-be7e98b866d3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11617879776000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897a64f4-a92e-4c30-8be4-b9d310654bb9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615994587500, "endTime": 11617879964800}, "additional": {"logType": "info", "children": [], "durationId": "801e4848-5f56-4bbe-8f27-12d78cca4a9d", "parent": "fb14c66a-f5be-43b6-9ef9-bfb18b12bb2a"}}, {"head": {"id": "2d693d47-235e-410e-a72b-d483e335b4cc", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11617880004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4172e94a-9d92-4e3c-b26a-ea7af1492865", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11618543818400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300c364d-bde3-43a4-9976-562e5f9e3f9a", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11618544280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4a186e-9d81-449e-8223-a8be85bb48dc", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11618544825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0967ae93-99c1-4ac3-9a0f-7155e2176be1", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11618551809600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc526b1-edab-4ab3-97a3-bb09d2f92ef9", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11618796478600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5819569-03ec-4a21-94c0-0359203d58de", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619009864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c911db83-556f-4bc6-b78e-a1ebb7b2e04e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619175379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f6d2fc1-6619-4b7e-8e0b-03e697face89", "name": "Sdk init in 386 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619429388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f7ca4e-254d-4183-8eb5-0a452b34b326", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619429598900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 3}, "markType": "other"}}, {"head": {"id": "45dcb870-3d5e-4adb-a035-f75cc6916bea", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619429621500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 3}, "markType": "other"}}, {"head": {"id": "fc82ca95-6ee1-4545-a0e5-36ccdfa41ebe", "name": "Project task initialization takes 76 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619530792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd6cad7-93a2-41d4-a13c-1a9efdcc81f6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619530952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68e8a008-3086-461d-9594-b5b9609229ad", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619531056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e491e4-cd1c-4668-9a0a-ea8cc49b03c4", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619531125800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54852a03-5661-47fe-a426-e4683fb8b9ff", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11617879985600, "endTime": 11619551300900}, "additional": {"logType": "info", "children": [], "durationId": "0848eca3-40f3-466e-a7b1-52a0fb9a716e", "parent": "fb14c66a-f5be-43b6-9ef9-bfb18b12bb2a"}}, {"head": {"id": "fb14c66a-f5be-43b6-9ef9-bfb18b12bb2a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615994586600, "endTime": 11619551329300}, "additional": {"logType": "info", "children": ["897a64f4-a92e-4c30-8be4-b9d310654bb9", "54852a03-5661-47fe-a426-e4683fb8b9ff"], "durationId": "2bcc6f3a-cfda-4eed-944d-ee060fc4cfad", "parent": "ca8c2289-77b0-4201-b507-cb52ec567544"}}, {"head": {"id": "89155f68-edb5-4c16-a2d5-4cfbe428c5b7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619552257600, "endTime": 11619552275500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15b8574c-d2e7-46c0-97c5-ba68ca659873", "logId": "18374d4e-7f3c-4f8a-aa85-d87d30f54f19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18374d4e-7f3c-4f8a-aa85-d87d30f54f19", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619552257600, "endTime": 11619552275500}, "additional": {"logType": "info", "children": [], "durationId": "89155f68-edb5-4c16-a2d5-4cfbe428c5b7", "parent": "ca8c2289-77b0-4201-b507-cb52ec567544"}}, {"head": {"id": "ca8c2289-77b0-4201-b507-cb52ec567544", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615871655400, "endTime": 11619552294300}, "additional": {"logType": "info", "children": ["ffd0ac00-4c39-4608-84a1-94417d30d0e2", "fb14c66a-f5be-43b6-9ef9-bfb18b12bb2a", "18374d4e-7f3c-4f8a-aa85-d87d30f54f19"], "durationId": "15b8574c-d2e7-46c0-97c5-ba68ca659873", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "a747b87b-905e-4abc-9580-87cf12382756", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619553074000, "endTime": 11619660954800}, "additional": {"children": ["4c0e01e1-3cfc-40ae-ae26-cc8958a229d6", "3e2e9049-4c8e-489f-86a9-c7eb2bbb6735", "f1bd5c93-16a7-4af6-afdb-5f78c647d2a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42f39d45-6691-4a9f-ad8a-ba49b555dbb7", "logId": "2a125787-15e1-4ea2-b3e0-34833feb4a74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c0e01e1-3cfc-40ae-ae26-cc8958a229d6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619558602900, "endTime": 11619558631300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a747b87b-905e-4abc-9580-87cf12382756", "logId": "b2203a17-fe7d-46e4-a872-f41ed2f55ef7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2203a17-fe7d-46e4-a872-f41ed2f55ef7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619558602900, "endTime": 11619558631300}, "additional": {"logType": "info", "children": [], "durationId": "4c0e01e1-3cfc-40ae-ae26-cc8958a229d6", "parent": "2a125787-15e1-4ea2-b3e0-34833feb4a74"}}, {"head": {"id": "3e2e9049-4c8e-489f-86a9-c7eb2bbb6735", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619562425400, "endTime": 11619659246500}, "additional": {"children": ["76d68d41-84b5-4fce-b9ad-713a6fce43bc", "ae736aa8-ad6e-4f48-a1c0-fe739aeb5ed6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a747b87b-905e-4abc-9580-87cf12382756", "logId": "9a2b5b3c-d3c7-4e67-b42a-1728d06a9d40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76d68d41-84b5-4fce-b9ad-713a6fce43bc", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619562427000, "endTime": 11619567598600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e2e9049-4c8e-489f-86a9-c7eb2bbb6735", "logId": "4b923e48-4a61-44d0-9fe1-445006910966"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae736aa8-ad6e-4f48-a1c0-fe739aeb5ed6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619567620000, "endTime": 11619659228500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e2e9049-4c8e-489f-86a9-c7eb2bbb6735", "logId": "454ad542-9286-40f5-aefb-7713d12e90fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "027559e2-1686-4064-9a47-69ee77409357", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619562436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e15c889-eddb-4101-a56a-832e7299a7b9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619567446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b923e48-4a61-44d0-9fe1-445006910966", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619562427000, "endTime": 11619567598600}, "additional": {"logType": "info", "children": [], "durationId": "76d68d41-84b5-4fce-b9ad-713a6fce43bc", "parent": "9a2b5b3c-d3c7-4e67-b42a-1728d06a9d40"}}, {"head": {"id": "f1f1a3ef-9c44-4e31-afa9-17ab21327e50", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619567634500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19ab0ba3-a6bf-4727-a08f-7d0035904fd8", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619601377700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08623a4a-f6ff-4f0d-8933-4fe7980125bc", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619601567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342ab507-96bc-4f75-81db-9b83c4a7ade4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619601852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b23c30c-e183-4775-9370-828fde0af31e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619602040600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6753b0-5507-4807-8378-cc9ee8ed3e9e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619602125300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00eb5549-80c2-42e9-ba43-b1ba8c347616", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619602191800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a4d0bf-7307-4ac2-b20c-a72048d7122a", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619638114600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd486869-2a5c-408e-9842-d25176421297", "name": "Module entry task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619658833500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0905ee-e49c-4deb-b855-1ded68aa0365", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619659038000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20352b9-b636-470e-acbc-f214c98f8491", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619659110100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a44e01-6a56-4984-aeb1-1e214ac542b1", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619659171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454ad542-9286-40f5-aefb-7713d12e90fd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619567620000, "endTime": 11619659228500}, "additional": {"logType": "info", "children": [], "durationId": "ae736aa8-ad6e-4f48-a1c0-fe739aeb5ed6", "parent": "9a2b5b3c-d3c7-4e67-b42a-1728d06a9d40"}}, {"head": {"id": "9a2b5b3c-d3c7-4e67-b42a-1728d06a9d40", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619562425400, "endTime": 11619659246500}, "additional": {"logType": "info", "children": ["4b923e48-4a61-44d0-9fe1-445006910966", "454ad542-9286-40f5-aefb-7713d12e90fd"], "durationId": "3e2e9049-4c8e-489f-86a9-c7eb2bbb6735", "parent": "2a125787-15e1-4ea2-b3e0-34833feb4a74"}}, {"head": {"id": "f1bd5c93-16a7-4af6-afdb-5f78c647d2a2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619660921600, "endTime": 11619660935900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a747b87b-905e-4abc-9580-87cf12382756", "logId": "c118979f-b37a-46a3-a317-f3d4a43b57c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c118979f-b37a-46a3-a317-f3d4a43b57c9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619660921600, "endTime": 11619660935900}, "additional": {"logType": "info", "children": [], "durationId": "f1bd5c93-16a7-4af6-afdb-5f78c647d2a2", "parent": "2a125787-15e1-4ea2-b3e0-34833feb4a74"}}, {"head": {"id": "2a125787-15e1-4ea2-b3e0-34833feb4a74", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619553074000, "endTime": 11619660954800}, "additional": {"logType": "info", "children": ["b2203a17-fe7d-46e4-a872-f41ed2f55ef7", "9a2b5b3c-d3c7-4e67-b42a-1728d06a9d40", "c118979f-b37a-46a3-a317-f3d4a43b57c9"], "durationId": "a747b87b-905e-4abc-9580-87cf12382756", "parent": "b4e5fedb-31b2-4ce1-a69a-09d6b144a82d"}}, {"head": {"id": "b4e5fedb-31b2-4ce1-a69a-09d6b144a82d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619552313800, "endTime": 11619660970400}, "additional": {"logType": "info", "children": ["2a125787-15e1-4ea2-b3e0-34833feb4a74"], "durationId": "42f39d45-6691-4a9f-ad8a-ba49b555dbb7", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "5e536a78-2cd2-4763-8ebe-d087c5a90889", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620867798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b76504a4-7376-42bf-8ddc-cae4e3a6ff00", "name": "hvigorfile, resolve hvigorfile dependencies in 1 s 226 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620886825400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d6085d-788b-4fa8-a78b-c23f9ea5028f", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11619660980100, "endTime": 11620886980100}, "additional": {"logType": "info", "children": [], "durationId": "f7067c22-0bc8-4ecf-8e90-6b02aba447e2", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "ecc73da8-c91c-410c-992f-178629dd6dde", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620887979400, "endTime": 11620888230300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "logId": "cf6cb759-0f43-4f8d-938e-9f3c618a49af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7db8f26b-ccf9-4635-89ea-f3776be1094c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620888015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6cb759-0f43-4f8d-938e-9f3c618a49af", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620887979400, "endTime": 11620888230300}, "additional": {"logType": "info", "children": [], "durationId": "ecc73da8-c91c-410c-992f-178629dd6dde", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "eff4b5c9-3e1e-46af-a9a3-ae1c5909ad44", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620890131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c164582-649e-4182-a371-74ea7a93b71c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621132692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04ed73a-a3a0-4ccc-962f-c46f60b1dcaa", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11620888278000, "endTime": 11621136022000}, "additional": {"logType": "info", "children": [], "durationId": "27111deb-6742-4f5e-b97f-7217e0922611", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "e3221b11-1eba-4132-8ea1-93cd669c88f8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621136152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf1e714-5f64-44c1-bf3e-f9970b2e7dd3", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621183254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54d5a48-870b-49af-9d41-07ca6e7c0bcf", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621184681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb1f9cd5-832d-470f-87de-841bb2436f8c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621233811200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "524b4a80-4ed9-4a8c-b324-64a35b0f0564", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621245017600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c41e2cd-08cc-4ae9-b18e-d40659ab68e9", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621245421400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa91b9e4-3e1f-4d34-a648-749cfe9b651b", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621136088000, "endTime": 11621257365100}, "additional": {"logType": "info", "children": [], "durationId": "f6018d52-5fca-4079-8678-2594750b1911", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "6c551d90-2c50-4876-8a71-7abeb4b5800f", "name": "Configuration phase cost:5 s 466 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621257466800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4248fd66-e62c-4a2a-9082-aaadfc4f92b4", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621257407100, "endTime": 11621257674700}, "additional": {"logType": "info", "children": [], "durationId": "819e2b1d-6bbb-4db2-b2ce-6ca6d725773d", "parent": "35680990-698e-4cd0-b5f4-41cdec5f562b"}}, {"head": {"id": "35680990-698e-4cd0-b5f4-41cdec5f562b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11615767243100, "endTime": 11621257702800}, "additional": {"logType": "info", "children": ["fa545ae4-c066-40fd-9b67-247df02662e2", "ec1d421c-c5d9-46ec-a659-59c2ccaf1df3", "ca8c2289-77b0-4201-b507-cb52ec567544", "b4e5fedb-31b2-4ce1-a69a-09d6b144a82d", "83d6085d-788b-4fa8-a78b-c23f9ea5028f", "a04ed73a-a3a0-4ccc-962f-c46f60b1dcaa", "fa91b9e4-3e1f-4d34-a648-749cfe9b651b", "4248fd66-e62c-4a2a-9082-aaadfc4f92b4", "cf6cb759-0f43-4f8d-938e-9f3c618a49af"], "durationId": "965e8e53-e98c-4489-b566-3dd5d4bb56ad", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "35627a64-793a-41e2-8ec7-63ae07b37db0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621293000600, "endTime": 11621293020100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1", "logId": "8e4c5c68-79c3-4541-a3e8-fb805c4ffd34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e4c5c68-79c3-4541-a3e8-fb805c4ffd34", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621293000600, "endTime": 11621293020100}, "additional": {"logType": "info", "children": [], "durationId": "35627a64-793a-41e2-8ec7-63ae07b37db0", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "e2b07bde-79cc-4687-8dfd-e29d577b059e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621257750900, "endTime": 11621293036700}, "additional": {"logType": "info", "children": [], "durationId": "77a0ef02-03d2-4886-800f-8754b0d14560", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "5eabb6d6-c552-4b37-85db-9d96bee78ba0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621293043500, "endTime": 11621293065100}, "additional": {"logType": "info", "children": [], "durationId": "de854647-765d-4909-adb7-d38e4fbbfff2", "parent": "78f86695-b988-44e3-96b6-35329745a226"}}, {"head": {"id": "78f86695-b988-44e3-96b6-35329745a226", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11614620550100, "endTime": 11621293069500}, "additional": {"logType": "info", "children": ["d7664c28-6ae5-45fb-b873-799debe71021", "35680990-698e-4cd0-b5f4-41cdec5f562b", "e2b07bde-79cc-4687-8dfd-e29d577b059e", "5eabb6d6-c552-4b37-85db-9d96bee78ba0", "8487f1c8-0dd5-4609-8df7-756f86171020", "54702b5e-88cb-4273-89b4-80e422b8bc61", "8e4c5c68-79c3-4541-a3e8-fb805c4ffd34"], "durationId": "7ecc7ca6-5480-4cc7-913a-f7148ef30dc1"}}, {"head": {"id": "1258e731-27b7-47af-8c99-99579b6a675c", "name": "Configuration task cost before running: 6 s 701 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621308523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de0c7cd-de4b-4690-ae66-6783d3f20b7e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621344990400, "endTime": 11621409721800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "dd164e00-c4a7-4606-88d7-95b2bc2ca566", "logId": "f879e652-11ad-46c8-a866-c967c53703d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd164e00-c4a7-4606-88d7-95b2bc2ca566", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621332563200}, "additional": {"logType": "detail", "children": [], "durationId": "3de0c7cd-de4b-4690-ae66-6783d3f20b7e"}}, {"head": {"id": "f7eef8cb-851b-4a20-9efb-8e28b9d1e31b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621334303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fda2aa4d-d8a2-41ff-ba81-bc3fd84fa241", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621334621600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b9bb58f-6fcd-4f7e-9a48-dbbfd205734b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621345035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da11e676-f4ea-4851-95c2-142fce638760", "name": "Incremental task entry:default@PreBuild pre-execution cost: 59 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621409441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb2f721-228e-43e2-8944-da8c148e3020", "name": "entry : default@PreBuild cost memory 0.513824462890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621409625700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f879e652-11ad-46c8-a866-c967c53703d5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621344990400, "endTime": 11621409721800}, "additional": {"logType": "info", "children": [], "durationId": "3de0c7cd-de4b-4690-ae66-6783d3f20b7e"}}, {"head": {"id": "212b84df-1723-4905-a4ad-77fb30fc5847", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621415872100, "endTime": 11621419195600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "35a68c7e-4a05-46f4-9663-722e997c47ad", "logId": "5bccce62-d496-4b37-a984-16b4a2a61fd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35a68c7e-4a05-46f4-9663-722e997c47ad", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621414351000}, "additional": {"logType": "detail", "children": [], "durationId": "212b84df-1723-4905-a4ad-77fb30fc5847"}}, {"head": {"id": "1527ff9b-12c7-44bf-9d06-a3c5d554f402", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621414951800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21f44be-1efc-484b-8f79-46f631534c49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621415083400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0539eced-5a1f-4500-9180-dee9079405c6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621415882800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa9fed8-6938-488d-97fa-c9c6bfd1fcb4", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621419009100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fe7b2b-8771-47d7-8430-2cea89d7cbf7", "name": "entry : default@MergeProfile cost memory 0.11554718017578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621419114400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bccce62-d496-4b37-a984-16b4a2a61fd5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621415872100, "endTime": 11621419195600}, "additional": {"logType": "info", "children": [], "durationId": "212b84df-1723-4905-a4ad-77fb30fc5847"}}, {"head": {"id": "bcb771bf-241c-40ec-a760-e3b61677a7c0", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621422881600, "endTime": 11621502419300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9a0e7c40-3f97-4de8-8f06-c9a145374fd0", "logId": "f2d59769-99ce-403f-9a25-9bdcbbf570bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a0e7c40-3f97-4de8-8f06-c9a145374fd0", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621421383100}, "additional": {"logType": "detail", "children": [], "durationId": "bcb771bf-241c-40ec-a760-e3b61677a7c0"}}, {"head": {"id": "480dcdb2-4b26-4d45-9945-a4191c548989", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621421916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05260a92-c20b-4348-997c-abd27b199740", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621422028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "292c0148-1981-4d6f-88e5-a4ffe48d5fa9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621422904500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5365441-318c-4631-b744-151c3d5a434b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 52 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621474965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca4826e-dbc1-4173-a5cf-77e00d9e3585", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621501980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af5b337-e600-49f5-9f95-93dd48b0eed3", "name": "entry : default@CreateBuildProfile cost memory 0.10140228271484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621502232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d59769-99ce-403f-9a25-9bdcbbf570bd", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621422881600, "endTime": 11621502419300}, "additional": {"logType": "info", "children": [], "durationId": "bcb771bf-241c-40ec-a760-e3b61677a7c0"}}, {"head": {"id": "3f309fea-0679-4786-a342-7f041abc18b1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621506969500, "endTime": 11621507672200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9970fab0-7b98-48d7-99e5-1205c146f59f", "logId": "ed2e2bdb-7718-4aa0-844a-544db9d75409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9970fab0-7b98-48d7-99e5-1205c146f59f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621505152400}, "additional": {"logType": "detail", "children": [], "durationId": "3f309fea-0679-4786-a342-7f041abc18b1"}}, {"head": {"id": "1989327e-4541-434a-84ae-91d4069c09e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621505804300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49253358-5b35-4f40-b1c4-788b4566fd24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621505934600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f052e29-cb54-412f-a375-9079d4595619", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621506982200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f795b512-e1f5-4ca2-a4de-4774de8f2217", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621507282900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68b43c3-8c74-4846-b746-38cb36b24f12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621507380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253278e8-1955-4188-aa5f-eac9d79c51d3", "name": "entry : default@PreCheckSyscap cost memory 0.0374298095703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621507482200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b024ec3-de66-41c3-8c9e-75aad431a2aa", "name": "runTaskFromQueue task cost before running: 6 s 900 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621507592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed2e2bdb-7718-4aa0-844a-544db9d75409", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621506969500, "endTime": 11621507672200, "totalTime": 594800}, "additional": {"logType": "info", "children": [], "durationId": "3f309fea-0679-4786-a342-7f041abc18b1"}}, {"head": {"id": "d1e9c3e6-ffca-49b4-bda2-7f35647aee52", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621520090000, "endTime": 11621521587800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "126dbc64-d7e3-42f3-9ed4-d6be9a94cb99", "logId": "07a7cd97-8e05-471f-9316-c36e145c7be2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "126dbc64-d7e3-42f3-9ed4-d6be9a94cb99", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621510492200}, "additional": {"logType": "detail", "children": [], "durationId": "d1e9c3e6-ffca-49b4-bda2-7f35647aee52"}}, {"head": {"id": "849c74f0-bb67-4eb8-bfe3-f1fd5985dc40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621511117700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa722e4b-d5a4-4521-a235-d72c6eee7d26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621511234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "495dc332-2666-49c0-afbb-20e628847986", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621520107300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b0f610-262e-45b8-8d2c-2cf60e821642", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621520394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "786bb3e5-8218-4351-8205-b27b65be4a7d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621521235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fe8254-196c-4a97-850c-1bda1d354da7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06746673583984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621521476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a7cd97-8e05-471f-9316-c36e145c7be2", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621520090000, "endTime": 11621521587800}, "additional": {"logType": "info", "children": [], "durationId": "d1e9c3e6-ffca-49b4-bda2-7f35647aee52"}}, {"head": {"id": "563cbec2-f113-430e-97cc-1724688e43e3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621525602400, "endTime": 11621527105000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "45b2fa55-2ab5-4ae3-b649-4efae43916f6", "logId": "cfa7fe8f-3b64-4dc6-ae30-057406498a41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45b2fa55-2ab5-4ae3-b649-4efae43916f6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621523653500}, "additional": {"logType": "detail", "children": [], "durationId": "563cbec2-f113-430e-97cc-1724688e43e3"}}, {"head": {"id": "6a7b564e-c771-4091-8488-9e1fa75fea5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621524216500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e6d55d-1a9a-4cbe-acce-2b4e194c8350", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621524320400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95878d58-0d1a-487a-84d2-f07ed65e6124", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621525613600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b857ea96-e8af-4c2a-9531-0079f85c9d1c", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621526908900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb58a85-cb7a-467f-a025-20c2dd8a64a8", "name": "entry : default@ProcessProfile cost memory 0.05928802490234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621527025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa7fe8f-3b64-4dc6-ae30-057406498a41", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621525602400, "endTime": 11621527105000}, "additional": {"logType": "info", "children": [], "durationId": "563cbec2-f113-430e-97cc-1724688e43e3"}}, {"head": {"id": "71c7e0db-a4e9-4a12-9db5-44de71754519", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621531400300, "endTime": 11621537776800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "868fa272-c256-4d6d-9e84-960f282b2ce0", "logId": "91bc4bf3-4a93-4668-b289-ac7967e2b8ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "868fa272-c256-4d6d-9e84-960f282b2ce0", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621528845900}, "additional": {"logType": "detail", "children": [], "durationId": "71c7e0db-a4e9-4a12-9db5-44de71754519"}}, {"head": {"id": "602a3d11-0423-4ac8-8d33-c4db388f1a82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621529380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35131de-26ea-4738-9906-2cf18b441980", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621529495400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcacd51-e2e7-408a-8a1c-7f528b996f82", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621531413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e280ed-314b-4916-bfa0-0a223a904f2e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621537573300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9098bc6-406a-419b-b52a-e02d11c95cde", "name": "entry : default@ProcessRouterMap cost memory 0.19832611083984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621537695800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91bc4bf3-4a93-4668-b289-ac7967e2b8ba", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621531400300, "endTime": 11621537776800}, "additional": {"logType": "info", "children": [], "durationId": "71c7e0db-a4e9-4a12-9db5-44de71754519"}}, {"head": {"id": "2187f650-1016-409b-8076-daf397356439", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621544896900, "endTime": 11621547917400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3888796f-b004-4e7d-a042-35ff6777aebf", "logId": "b54ac720-af40-4cc4-ac3b-f06a9fb5bb3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3888796f-b004-4e7d-a042-35ff6777aebf", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621540952800}, "additional": {"logType": "detail", "children": [], "durationId": "2187f650-1016-409b-8076-daf397356439"}}, {"head": {"id": "abccbce2-16e0-483e-bd4f-e6198832ddf4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621541525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381f38d6-be9f-4903-8a1c-e0632aae71ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621541648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819ff985-2f3e-4017-be99-7879fd4c21e5", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621542704200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "188c32e4-70c2-40de-a611-226785460d31", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621546142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2abea95-84bf-4b46-acd4-dc668dfcae96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621546284900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c08b39-0314-4af1-8ad9-2d05e87eb8db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621546354700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4f17ce-43c8-4d2c-acf7-7cef1ed4f228", "name": "entry : default@PreviewProcessResource cost memory 0.0692596435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621546441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6d019e1-daf4-41f3-b116-5470b25ba142", "name": "runTaskFromQueue task cost before running: 6 s 940 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621547814300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54ac720-af40-4cc4-ac3b-f06a9fb5bb3e", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621544896900, "endTime": 11621547917400, "totalTime": 1612800}, "additional": {"logType": "info", "children": [], "durationId": "2187f650-1016-409b-8076-daf397356439"}}, {"head": {"id": "86d018f7-63ae-47b0-a2ac-109f00321a3b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621555765800, "endTime": 11621577493200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "be5c5c44-f6e9-4a63-89a3-a81593d14420", "logId": "85aa97ce-672f-4c3d-aec4-0b576ae7ae67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5c5c44-f6e9-4a63-89a3-a81593d14420", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621551549300}, "additional": {"logType": "detail", "children": [], "durationId": "86d018f7-63ae-47b0-a2ac-109f00321a3b"}}, {"head": {"id": "62da24b5-5049-4a5a-b516-9434be21073b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621552084200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3346bb8a-f421-4fc9-bb6a-979260adb215", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621552199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f22735b-e103-49de-9d11-592f9e210fad", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621555778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "910bca25-585c-4d2e-9f27-f1bfdd7bd14f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621577260900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27de6933-8ddf-4604-bb17-b157964fc710", "name": "entry : default@GenerateLoaderJson cost memory 0.72998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621577415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85aa97ce-672f-4c3d-aec4-0b576ae7ae67", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621555765800, "endTime": 11621577493200}, "additional": {"logType": "info", "children": [], "durationId": "86d018f7-63ae-47b0-a2ac-109f00321a3b"}}, {"head": {"id": "e3f9434c-b990-4ed8-99c2-047d3613f733", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621590543300, "endTime": 11623571839600}, "additional": {"children": ["4f349796-9388-40d0-b31b-7516745636ce", "222b0e6e-29ef-41bb-bfde-8c9b126288cc", "c6c2e509-a1e5-4ed9-a9ff-a832a2f5bd52", "a6987d89-ef1a-4781-91f9-c4ca2ab86b60"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "ce6ba332-835a-40ed-851a-b2dd212393fb", "logId": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce6ba332-835a-40ed-851a-b2dd212393fb", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621586360200}, "additional": {"logType": "detail", "children": [], "durationId": "e3f9434c-b990-4ed8-99c2-047d3613f733"}}, {"head": {"id": "14ecabd0-9826-48f3-aaff-3bd7e0fa324a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621586915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edf336ee-03f1-4459-b066-f5728126258c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621587051100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2b4cbb-0783-45e2-8881-79b8ecbb20a1", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621588100700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695bfa28-4f32-4b07-b028-ef73afcea80b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621590571200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56dcbe0e-d5d8-48ed-9ba2-fea0eda580ac", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621899895900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f780c73f-3473-4d63-8c94-c0a70ac15298", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 309 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621900115700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f349796-9388-40d0-b31b-7516745636ce", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621901948200, "endTime": 11622067906200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e3f9434c-b990-4ed8-99c2-047d3613f733", "logId": "89f548ea-760b-431c-93f3-a07550829928"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe6191de-1010-4a28-b7bc-fac3e98ce961", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621940217200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98092b47-a707-435c-b544-e1d62b809dfe", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621940564800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f548ea-760b-431c-93f3-a07550829928", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621901948200, "endTime": 11622067906200}, "additional": {"logType": "info", "children": [], "durationId": "4f349796-9388-40d0-b31b-7516745636ce", "parent": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24"}}, {"head": {"id": "dc5f460d-2184-4496-a0a4-e7a9aea66e60", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11622068722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222b0e6e-29ef-41bb-bfde-8c9b126288cc", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11622069862600, "endTime": 11623073337000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e3f9434c-b990-4ed8-99c2-047d3613f733", "logId": "c452bf00-f4fc-4c25-805a-1bb6cd9c0feb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c74109a-0695-4fc1-985e-e99e546525c1", "name": "current process  memoryUsage: {\n  rss: 411918336,\n  heapTotal: 119083008,\n  heapUsed: 112003608,\n  external: 3141707,\n  arrayBuffers: 135572\n} os memoryUsage :7.296733856201172", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11622090472700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2db159-2d3b-4f19-acc6-7ed7a9a8a7dd", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623069291400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c452bf00-f4fc-4c25-805a-1bb6cd9c0feb", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11622069862600, "endTime": 11623073337000}, "additional": {"logType": "info", "children": [], "durationId": "222b0e6e-29ef-41bb-bfde-8c9b126288cc", "parent": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24"}}, {"head": {"id": "3b31e5a8-bfb1-4ff6-b757-4c285caf37ae", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623073644300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c2e509-a1e5-4ed9-a9ff-a832a2f5bd52", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623075727500, "endTime": 11623288245800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e3f9434c-b990-4ed8-99c2-047d3613f733", "logId": "7eafba48-833f-4c13-88d1-68477f4be025"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd06c13d-f827-4134-99ce-fe76141e895f", "name": "current process  memoryUsage: {\n  rss: 119103488,\n  heapTotal: 119083008,\n  heapUsed: 112447632,\n  external: 3124780,\n  arrayBuffers: 118660\n} os memoryUsage :7.024925231933594", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623078374200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54450652-ab88-407b-a3c9-a2777bb85ed3", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623286190100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eafba48-833f-4c13-88d1-68477f4be025", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623075727500, "endTime": 11623288245800}, "additional": {"logType": "info", "children": [], "durationId": "c6c2e509-a1e5-4ed9-a9ff-a832a2f5bd52", "parent": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24"}}, {"head": {"id": "540881eb-d4d6-489f-b744-cfbfeeb98158", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623288913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6987d89-ef1a-4781-91f9-c4ca2ab86b60", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623290246700, "endTime": 11623569501700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e3f9434c-b990-4ed8-99c2-047d3613f733", "logId": "e0e464c2-7a4d-4e31-98dc-4a43827ac8b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca93f96d-83fe-4a05-bc98-a459397741d8", "name": "current process  memoryUsage: {\n  rss: 119181312,\n  heapTotal: 119083008,\n  heapUsed: 112723800,\n  external: 3124906,\n  arrayBuffers: 119600\n} os memoryUsage :7.0249786376953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623291374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0b6db3-c2ac-46ab-998d-0cc8b49b037c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623566235500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e464c2-7a4d-4e31-98dc-4a43827ac8b3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623290246700, "endTime": 11623569501700}, "additional": {"logType": "info", "children": [], "durationId": "a6987d89-ef1a-4781-91f9-c4ca2ab86b60", "parent": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24"}}, {"head": {"id": "2899aa3d-563e-496c-9527-07a4c7f53093", "name": "entry : default@PreviewCompileResource cost memory 0.304931640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623571583100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f8180c1-dfef-4944-b359-058f58bb05e7", "name": "runTaskFromQueue task cost before running: 8 s 964 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623571759700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "436b9a41-eb55-46fe-b7d3-b1bb96d86a24", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11621590543300, "endTime": 11623571839600, "totalTime": 1981167400}, "additional": {"logType": "info", "children": ["89f548ea-760b-431c-93f3-a07550829928", "c452bf00-f4fc-4c25-805a-1bb6cd9c0feb", "7eafba48-833f-4c13-88d1-68477f4be025", "e0e464c2-7a4d-4e31-98dc-4a43827ac8b3"], "durationId": "e3f9434c-b990-4ed8-99c2-047d3613f733"}}, {"head": {"id": "174409ac-5f4c-4a51-96ac-edb86d96d43d", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575139700, "endTime": 11623575571500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "758ada87-7257-4ac5-b68e-d4a991e1c790", "logId": "ffba8446-18d6-4c6a-8165-1f1b4f4c778f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "758ada87-7257-4ac5-b68e-d4a991e1c790", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623574414300}, "additional": {"logType": "detail", "children": [], "durationId": "174409ac-5f4c-4a51-96ac-edb86d96d43d"}}, {"head": {"id": "09a58cf8-6083-4c70-948f-8dbcf411d372", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623574935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823868bc-fb1c-4fba-bd80-05351f113b63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d873cf-a1b0-4549-8027-e72bbf36ec8b", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575147200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352e9ce0-43ea-492f-b997-1f79aa60e5c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575259600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970b197d-2dda-473c-86a2-de1ac401c887", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52fef83-534b-45f1-a17e-405112a9a3fe", "name": "entry : default@PreviewHookCompileResource cost memory 0.167449951171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bf39ff-dcd6-4202-a907-6ce03024b212", "name": "runTaskFromQueue task cost before running: 8 s 968 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575506700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffba8446-18d6-4c6a-8165-1f1b4f4c778f", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623575139700, "endTime": 11623575571500, "totalTime": 340800}, "additional": {"logType": "info", "children": [], "durationId": "174409ac-5f4c-4a51-96ac-edb86d96d43d"}}, {"head": {"id": "ce570968-00d7-487e-8f57-31eda88e0f21", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623667582900, "endTime": 11623729874800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "4edd7b5d-3d44-4cd1-b68b-3c6ff4a24719", "logId": "f9982a67-347c-43fe-addc-387c7b2e3ee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4edd7b5d-3d44-4cd1-b68b-3c6ff4a24719", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623663399400}, "additional": {"logType": "detail", "children": [], "durationId": "ce570968-00d7-487e-8f57-31eda88e0f21"}}, {"head": {"id": "0642c17d-288a-43a1-b976-dc1723cafb6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623665112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc40f905-4365-433d-92b6-25b329be0d28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623665426600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688cf694-6369-4a52-8c0a-8aa16194a277", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623667609200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac29b75-29a3-4ffe-8405-c78716339ce7", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623672135200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e64aac-e74c-4114-9df2-4e10265e758d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623672494400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7395295e-4850-404b-ac24-a199907fdbbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623672806800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2046a843-1015-4b18-813a-3f967ee3a2a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623673003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d0427d-7459-4644-b0de-c1650bfd0d79", "name": "entry : default@CopyPreviewProfile cost memory 0.21437835693359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623729419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "319588e2-6812-4ce3-95d3-85875a8fc38c", "name": "runTaskFromQueue task cost before running: 9 s 122 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623729696600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9982a67-347c-43fe-addc-387c7b2e3ee9", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623667582900, "endTime": 11623729874800, "totalTime": 62066600}, "additional": {"logType": "info", "children": [], "durationId": "ce570968-00d7-487e-8f57-31eda88e0f21"}}, {"head": {"id": "189a4e50-6232-4a4a-a8ec-702a4ce15c0f", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735485800, "endTime": 11623736189500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "dc1f2223-fc0d-4b40-89d6-c68c47cec0b1", "logId": "26270d5f-ba65-4c94-ac76-e0f0fd5f65cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc1f2223-fc0d-4b40-89d6-c68c47cec0b1", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623733265300}, "additional": {"logType": "detail", "children": [], "durationId": "189a4e50-6232-4a4a-a8ec-702a4ce15c0f"}}, {"head": {"id": "16308694-0666-404d-9216-752195b723cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623734086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e5d591-460b-45f6-87d5-e76c1bdf8451", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623734246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4adcd1-9bfa-4def-92c8-e27188c305d6", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735501400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b720ac2-e543-4eb7-bcb0-09215c77055a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735697900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "110e1280-359d-4f6a-8cff-615609dfd962", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735797100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5950c024-e683-4474-8fde-91f89b55afca", "name": "entry : default@ReplacePreviewerPage cost memory 0.03859710693359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "186a6e8c-c9f9-4271-928c-484b829c9ba4", "name": "runTaskFromQueue task cost before running: 9 s 129 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623736099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26270d5f-ba65-4c94-ac76-e0f0fd5f65cc", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623735485800, "endTime": 11623736189500, "totalTime": 552600}, "additional": {"logType": "info", "children": [], "durationId": "189a4e50-6232-4a4a-a8ec-702a4ce15c0f"}}, {"head": {"id": "ed4bb958-6d0e-44ff-a82c-674eab69b6da", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739431100, "endTime": 11623739823400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b8864a57-afbf-4a3b-bebb-bb80ffe93b0e", "logId": "baf9a222-aba3-482c-a7a6-e5510f2badae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8864a57-afbf-4a3b-bebb-bb80ffe93b0e", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739354400}, "additional": {"logType": "detail", "children": [], "durationId": "ed4bb958-6d0e-44ff-a82c-674eab69b6da"}}, {"head": {"id": "e46f85e1-1a56-4b37-88a4-ba07eac9d188", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739442400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f806c9-c0c1-46cf-9077-108440ec650c", "name": "entry : buildPreviewerResource cost memory 0.01190948486328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ef9312-f0b5-449d-91b5-a47aaa9a598f", "name": "runTaskFromQueue task cost before running: 9 s 132 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf9a222-aba3-482c-a7a6-e5510f2badae", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623739431100, "endTime": 11623739823400, "totalTime": 280400}, "additional": {"logType": "info", "children": [], "durationId": "ed4bb958-6d0e-44ff-a82c-674eab69b6da"}}, {"head": {"id": "69452fd3-d251-41cf-b025-bf1c062216c4", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623744015800, "endTime": 11623748430800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "14b3791a-0e1f-4589-9e2d-ca9e83a2be7b", "logId": "be40a3c2-9f7d-4dca-b7d0-f04ba3705346"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14b3791a-0e1f-4589-9e2d-ca9e83a2be7b", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623742253300}, "additional": {"logType": "detail", "children": [], "durationId": "69452fd3-d251-41cf-b025-bf1c062216c4"}}, {"head": {"id": "e1611824-3c1c-4368-9ad6-ac94c0fff39d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623742891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b213b1-12c2-47d7-b673-8e9549722098", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623743046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647bb11e-f51e-47ba-9ff5-e057221270c6", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623744029600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce3aa5e-338a-40f2-87fe-c02baa3f1c80", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623746617100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81af9001-0847-43a0-bb4a-d5a3ee312edf", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623746787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2bed631-f835-4cfc-b347-03a8452ab11e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623746933300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de537280-0688-49ca-8be2-40bb871a60fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623747008200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148c9f07-3961-49f3-91aa-58bf0b13158b", "name": "entry : default@PreviewUpdateAssets cost memory 0.13317108154296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623748203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a03780-f5c1-463d-97fd-555bb40b35e2", "name": "runTaskFromQueue task cost before running: 9 s 141 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623748353900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be40a3c2-9f7d-4dca-b7d0-f04ba3705346", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623744015800, "endTime": 11623748430800, "totalTime": 4307900}, "additional": {"logType": "info", "children": [], "durationId": "69452fd3-d251-41cf-b025-bf1c062216c4"}}, {"head": {"id": "a12039d2-7d7c-4d27-9e66-41a1cd2d9fb9", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623760339600, "endTime": 11664415963100}, "additional": {"children": ["78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "4326c21b-7461-4385-b1ca-7a731406eedc", "logId": "ec85afac-1090-412b-be32-54651b1aad01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4326c21b-7461-4385-b1ca-7a731406eedc", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623752048200}, "additional": {"logType": "detail", "children": [], "durationId": "a12039d2-7d7c-4d27-9e66-41a1cd2d9fb9"}}, {"head": {"id": "4230ae64-79bb-4ab4-a107-884a9d03a761", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623752892800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08276534-881a-46dd-b74b-128dccdd6b31", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623753078500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10321b3-6a87-467a-a668-b9ef8ea9ecf4", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623760356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5271dabc-93d3-4b7d-adec-76859c87d998", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623775587500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6366be28-7c84-49d9-acb5-c97f72d3c29e", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623775815200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11623805363700, "endTime": 11664411839200}, "additional": {"children": ["55e575f2-d409-492d-a344-ae1891b31f33", "e1d9432f-9cba-4db3-bedc-db404b66d695", "a3ecb224-989a-495f-a28c-c306d9b16c0c", "eac2c21b-3945-4b4a-979f-664e22ba67a1", "aed85290-125d-4bf3-a459-54d9653eb136", "47dcfb4e-6542-443a-ae24-5b9abdfcd565"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a12039d2-7d7c-4d27-9e66-41a1cd2d9fb9", "logId": "d088298c-801b-4ffc-bd2b-ddbae86df371"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a39512b-c80b-4bab-bf6e-6be909b52e60", "name": "entry : default@PreviewArkTS cost memory 1.011199951171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623808094800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bb8decb-35cb-45cf-9913-b7a361fa85af", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11652178213600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e575f2-d409-492d-a344-ae1891b31f33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11652183692100, "endTime": 11652183802000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "c5762f07-60de-4cc8-b5a6-a67d49c71325"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5762f07-60de-4cc8-b5a6-a67d49c71325", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11652183692100, "endTime": 11652183802000}, "additional": {"logType": "info", "children": [], "durationId": "55e575f2-d409-492d-a344-ae1891b31f33", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "224dcdcd-468c-474b-baef-e10ac5cd41f9", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664410294100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d9432f-9cba-4db3-bedc-db404b66d695", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11664411459200, "endTime": 11664411480600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "7b052ddb-2c84-4954-8f3d-070b7e9904e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b052ddb-2c84-4954-8f3d-070b7e9904e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664411459200, "endTime": 11664411480600}, "additional": {"logType": "info", "children": [], "durationId": "e1d9432f-9cba-4db3-bedc-db404b66d695", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "d088298c-801b-4ffc-bd2b-ddbae86df371", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11623805363700, "endTime": 11664411839200}, "additional": {"logType": "info", "children": ["c5762f07-60de-4cc8-b5a6-a67d49c71325", "7b052ddb-2c84-4954-8f3d-070b7e9904e7", "03f6072c-0008-4f5a-b1b5-0937f0d58b7d", "cb78e61d-7d27-4720-912b-013736ff8d4a", "b9efe78d-b4ca-40ae-b7e0-d15ac1855128", "14a2fe01-e4f5-4fe0-a4ac-cf2eacbac2eb"], "durationId": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "parent": "ec85afac-1090-412b-be32-54651b1aad01"}}, {"head": {"id": "a3ecb224-989a-495f-a28c-c306d9b16c0c", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11646474435700, "endTime": 11652068282600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "03f6072c-0008-4f5a-b1b5-0937f0d58b7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03f6072c-0008-4f5a-b1b5-0937f0d58b7d", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11646474435700, "endTime": 11652068282600}, "additional": {"logType": "info", "children": [], "durationId": "a3ecb224-989a-495f-a28c-c306d9b16c0c", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "eac2c21b-3945-4b4a-979f-664e22ba67a1", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11652068546300, "endTime": 11652073974500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "cb78e61d-7d27-4720-912b-013736ff8d4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb78e61d-7d27-4720-912b-013736ff8d4a", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11652068546300, "endTime": 11652073974500}, "additional": {"logType": "info", "children": [], "durationId": "eac2c21b-3945-4b4a-979f-664e22ba67a1", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "aed85290-125d-4bf3-a459-54d9653eb136", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11652074132400, "endTime": 11652074138500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "b9efe78d-b4ca-40ae-b7e0-d15ac1855128"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9efe78d-b4ca-40ae-b7e0-d15ac1855128", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11652074132400, "endTime": 11652074138500}, "additional": {"logType": "info", "children": [], "durationId": "aed85290-125d-4bf3-a459-54d9653eb136", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "47dcfb4e-6542-443a-ae24-5b9abdfcd565", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker2", "startTime": 11652074232800, "endTime": 11664410382500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "14a2fe01-e4f5-4fe0-a4ac-cf2eacbac2eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14a2fe01-e4f5-4fe0-a4ac-cf2eacbac2eb", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11652074232800, "endTime": 11664410382500}, "additional": {"logType": "info", "children": [], "durationId": "47dcfb4e-6542-443a-ae24-5b9abdfcd565", "parent": "d088298c-801b-4ffc-bd2b-ddbae86df371"}}, {"head": {"id": "ec85afac-1090-412b-be32-54651b1aad01", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11623760339600, "endTime": 11664415963100, "totalTime": 40655609000}, "additional": {"logType": "info", "children": ["d088298c-801b-4ffc-bd2b-ddbae86df371"], "durationId": "a12039d2-7d7c-4d27-9e66-41a1cd2d9fb9"}}, {"head": {"id": "b211f874-7fe5-4305-a8c6-f908dd5b782b", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421221800, "endTime": 11664421599500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c808bae8-109c-47b7-b91f-f0f80d71a3d9", "logId": "a1fa387a-fc36-4cab-bc05-40f08508dc41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c808bae8-109c-47b7-b91f-f0f80d71a3d9", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421168000}, "additional": {"logType": "detail", "children": [], "durationId": "b211f874-7fe5-4305-a8c6-f908dd5b782b"}}, {"head": {"id": "12ee00a9-0525-4732-b1e9-57666d1cb880", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df2bff60-61b6-4f1e-9049-8bd0546906c1", "name": "entry : PreviewBuild cost memory 0.0117340087890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421372900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408bdbe3-c01e-4cfe-86c1-13ac68618639", "name": "runTaskFromQueue task cost before running: 49 s 814 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421490900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1fa387a-fc36-4cab-bc05-40f08508dc41", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664421221800, "endTime": 11664421599500, "totalTime": 244200}, "additional": {"logType": "info", "children": [], "durationId": "b211f874-7fe5-4305-a8c6-f908dd5b782b"}}, {"head": {"id": "63c9865f-d132-4f4c-adee-4d09686c75ee", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428193200, "endTime": 11664428220900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "352b4f35-3350-4e4b-b21c-2ae15e242414", "logId": "44236481-9f0c-4d3a-afa8-f37aa7dbea50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44236481-9f0c-4d3a-afa8-f37aa7dbea50", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428193200, "endTime": 11664428220900}, "additional": {"logType": "info", "children": [], "durationId": "63c9865f-d132-4f4c-adee-4d09686c75ee"}}, {"head": {"id": "8b138320-f0a9-4058-ab2f-eb5ad20bc2d2", "name": "BUILD SUCCESSFUL in 49 s 821 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428270000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "954da755-ddb0-4d05-a534-9d5ad4c5df46", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11614607927100, "endTime": 11664428558100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 4}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "35a55079-adb1-498b-8cb1-6c021a8e653a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428584000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f836e7-daa8-4144-9a7f-5e385650f804", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8932cd0-a455-4f5f-9af9-30c1464e96ea", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad563740-c11f-43f6-a3f1-0490225c2382", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee668cf-6769-4f94-b0de-e12a59e455ba", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428850700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1941e3aa-1ae4-4fc3-8b0f-5973a836d6a0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428904200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224883fd-e8e2-482a-ad0f-8bfc96ac6423", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664428981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6919db9-18bc-44f1-a07a-a8967867b00e", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664429804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daf02150-b9f6-40b9-a047-3b1473ba2167", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664438536600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "551a83e6-9010-4184-9866-c4bdddf90fb1", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664438899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac086310-9a8e-4353-b6ad-10d6253731a9", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664450348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6950893-5661-4adb-82dd-aac3727f9057", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664451153600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d29a06a-e27b-44cd-9e2b-a2a2f5cc3c7f", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664451565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ff6449-dd10-4a61-9300-7acdf4a96c82", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664452448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23713432-d529-4f23-a5f8-4fcc86300b0e", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664453369600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e091ad62-19fb-4cc6-bd85-4d33496553c2", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664453822400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40bdee95-1193-4ad9-904f-daaeae8a1c12", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664454205200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65995e7b-6983-4e42-a7de-202ab12576c2", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664454591300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd916d0a-97af-4a38-a27b-f633a369883e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664457836400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afbdae84-cc7f-484c-8536-de48190507b5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664458782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c49084-e5cd-467b-affe-dd3242bd4d59", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664458871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac29ba6e-3101-4b5e-abf3-f6aa109a379b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664459262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4069b60-c689-4e54-933c-df0315c642cb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664460458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3388ae05-e0d5-45be-962b-a4fb514c9725", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664472295600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a915bc34-e537-4af1-8b8d-ddfd640574f6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664472671700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01d6b03-630d-4c50-83c1-b6be5b6cb1db", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664473018700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70734770-8912-4689-b031-ea2ef7dc13e1", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664473380100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9af1fea-bc0c-49aa-b51d-dfbc3c6791ca", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664473708100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}