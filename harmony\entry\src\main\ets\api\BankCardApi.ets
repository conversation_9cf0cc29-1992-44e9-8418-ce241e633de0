import { httpClient } from '../common/http/HttpClient';
import {
  BankCard,
  BankCardBindRequest,
  BankCardQueryParams
} from '../common/types/index';

/**
 * 银行卡相关API接口
 */
export class BankCardApi {
  
  /**
   * 添加银行卡
   */
  static async addCard(data: BankCard): Promise<void> {
    await httpClient.post<void>('/bank/card', data);
  }

  /**
   * 更新银行卡
   */
  static async updateCard(data: BankCard): Promise<void> {
    await httpClient.put<void>(`/bank/card/${data.cardId}`, data);
  }

  /**
   * 绑定银行卡（使用Spring Boot的参数格式）
   */
  static async bindCard(data: BankCardBindRequest): Promise<void> {
    const formData = {
      userId: data.userId.toString(),
      bankName: data.bankName,
      cardNumber: data.cardNumber,
      cardType: data.cardType,
      balance: data.balance.toString(),
      creditLimit: data.creditLimit?.toString() || '0'
    };
    await httpClient.postForm<void>('/bank/bind', formData);
  }

  /**
   * 解绑银行卡
   */
  static async unbindCard(cardId: number): Promise<void> {
    await httpClient.put<void>(`/bank/unbind/${cardId}`);
  }

  /**
   * 删除银行卡
   */
  static async deleteCard(cardId: number): Promise<void> {
    await httpClient.delete<void>(`/bank/card/${cardId}`);
  }

  /**
   * 查询银行卡列表（根据用户ID）
   */
  static async getCardList(userId: number, bound?: boolean): Promise<BankCard[]> {
    const endpoint = bound ? `/bank/bound-cards/${userId}` : `/bank/cards/${userId}`;
    const response = await httpClient.get<BankCard[]>(endpoint);
    return response.data;
  }

  /**
   * 查看银行卡详情
   */
  static async getCardDetail(cardId: number): Promise<BankCard> {
    const response = await httpClient.get<BankCard>(`/bank/card/${cardId}`);
    return response.data;
  }
}

/**
 * 银行卡列表查询参数类
 */
class BankCardListParams {
  public bound: string;

  constructor(bound: string) {
    this.bound = bound;
  }

  public toRecord(): Record<string, string> {
    const record: Record<string, string> = {};
    record['bound'] = this.bound;
    return record;
  }
}

// 类型定义已移至 common/types/index.ets
