import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { UserLoginRequest, UserInfo, SendSmsCodeRequest, SmsLoginRequest, UserLoginResponse } from '../common/types/index';
import { storageManager, LocalUserInfo } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';
import http from '@ohos.net.http';
import { BusinessError } from '@ohos.base';

// Spring Boot响应接口
interface SpringBootResponse {
  code: number;
  msg: string;
  data: string;
}

@Entry
@Component
struct LoginPage {
  @State username: string = 'admin'; // 默认用户名，方便测试
  @State password: string = '123456'; // 默认密码，方便测试
  @State verificationCode: string = '';
  @State isLoading: boolean = false;
  @State captchaCode: string = '9889'; // 模拟验证码
  @State captcha: string = '1234'; // 验证码输入

  build() {
    Column() {
      // 背景渐变容器
      Column() {
        // Logo和标题区域
        Column({ space: 20 }) {
          // Logo图标 (圆形头像样式)
          Image($r('app.media.app_icon'))
            .width(80)
            .height(80)
            .borderRadius(40)
            .border({ width: 3, color: '#FF6B6B' })
            .margin({ top: 80 })

          // 系统标题
          Text('电子钱包交易系统')
            .fontSize(24)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
        }
        .margin({ bottom: 60 })

        // 登录表单卡片
        Column({ space: 20 }) {

          // 用户名输入框
          Column() {
            Row() {
              Text('* ')
                .fontSize(16)
                .fontColor('#FF4444')
              Text('用户名')
                .fontSize(16)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入用户名称', text: this.username })
              .width('100%')
              .height(48)
              .backgroundColor(Color.White)
              .borderRadius(6)
              .border({ width: 1, color: '#E0E0E0' })
              .padding({ left: 12, right: 12 })
              .fontSize(16)
              .onChange((value: string) => {
                this.username = value;
              })


          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)

          // 密码输入框
          Column() {
            Row() {
              Text('* ')
                .fontSize(16)
                .fontColor('#FF4444')
              Text('密码')
                .fontSize(16)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入密码', text: this.password })
              .width('100%')
              .height(48)
              .backgroundColor(Color.White)
              .borderRadius(6)
              .border({ width: 1, color: '#E0E0E0' })
              .padding({ left: 12, right: 12 })
              .fontSize(16)
              .type(InputType.Password)
              .onChange((value: string) => {
                this.password = value;
              })


          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)

          // 验证码输入框
          Column() {
            Row() {
              Text('* ')
                .fontSize(16)
                .fontColor('#FF4444')
              Text('验证码')
                .fontSize(16)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            Row({ space: 12 }) {
              TextInput({ placeholder: '请输入验证码', text: this.verificationCode })
                .layoutWeight(1)
                .height(48)
                .backgroundColor(Color.White)
                .borderRadius(6)
                .border({ width: 1, color: '#E0E0E0' })
                .padding({ left: 12, right: 12 })
                .fontSize(16)
                .type(InputType.Number)
                .maxLength(4)
                .onChange((value: string) => {
                  this.verificationCode = value;
                })

              Text(`${this.verificationCode.length}/4`)
                .fontSize(14)
                .fontColor('#999999')
                .margin({ right: 12 })

              Button('获取验证码')
                .height(48)
                .backgroundColor('#F0F0F0')
                .fontColor('#666666')
                .fontSize(14)
                .borderRadius(6)
                .border({ width: 1, color: '#E0E0E0' })
                .onClick(() => {
                  this.generateCaptcha();
                })
            }
            .width('100%')


          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)

          // 登录按钮
          Button(this.isLoading ? '登录中...' : '登录')
            .width('100%')
            .height(48)
            .backgroundColor(this.isLoginEnabled() ? '#4A90E2' : '#CCCCCC')
            .borderRadius(6)
            .fontSize(16)
            .fontColor(Color.White)
            .margin({ top: 20 })
            .enabled(this.isLoginEnabled())
            .onClick(() => {
              this.handleLogin();
            })

          // 测试连接按钮
          Button('测试Spring Boot连接')
            .width('100%')
            .height(48)
            .backgroundColor('#4CAF50')
            .borderRadius(6)
            .fontSize(16)
            .fontColor(Color.White)
            .margin({ top: 10 })
            .onClick(() => {
              router.pushUrl({
                url: 'pages/TestConnectionPage'
              });
            })

          // 测试跳转按钮
          Button('直接跳转测试')
            .width('100%')
            .height(48)
            .backgroundColor('#FF6B6B')
            .borderRadius(6)
            .fontSize(16)
            .fontColor(Color.White)
            .margin({ top: 10 })
            .onClick(() => {
              console.log('=== 测试直接跳转 ===');
              router.replaceUrl({
                url: 'pages/MyBankCardPage'
              }).then(() => {
                console.log('测试跳转成功');
              }).catch((error: Error) => {
                console.error('测试跳转失败:', error.message);
                promptAction.showToast({ message: `跳转失败: ${error.message}` });
              });
            })


        }
        .width('100%')
        .padding(24)
        .backgroundColor(Color.White)
        .borderRadius(12)
        .shadow({ radius: 10, color: '#********', offsetX: 0, offsetY: 2 })
      }
      .width('90%')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFE4E1')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 生成验证码
   */
  generateCaptcha() {
    // 生成4位随机数字验证码
    this.captchaCode = Math.floor(1000 + Math.random() * 9000).toString();
    promptAction.showToast({
      message: `新验证码: ${this.captchaCode}`,
      duration: 2000
    });
  }

  /**
   * 判断是否可以登录
   */
  isLoginEnabled(): boolean {
    if (this.isLoading) {
      return false;
    }
    // 简化条件：只需要用户名和密码
    return !!(this.username && this.username.length >= 1 &&
              this.password && this.password.length >= 1);
  }



  /**
   * 处理登录
   */
  async handleLogin() {
    console.log('=== 登录按钮被点击 ===');
    console.log('当前isLoading状态:', this.isLoading);
    console.log('用户名:', this.username);
    console.log('密码:', this.password);
    console.log('isLoginEnabled():', this.isLoginEnabled());

    if (this.isLoading) {
      console.log('正在登录中，忽略重复点击');
      return;
    }

    // 简化表单验证 - 只要有用户名和密码就可以登录
    if (!this.username || this.username.length < 1) {
      console.log('用户名验证失败');
      promptAction.showToast({ message: '请输入用户名' });
      return;
    }

    if (!this.password || this.password.length < 1) {
      console.log('密码验证失败');
      promptAction.showToast({ message: '请输入密码' });
      return;
    }

    console.log('表单验证通过，开始登录流程');
    this.isLoading = true;

    try {
      console.log('连接Spring Boot后端进行登录验证...');

      // 1. 先获取验证码来建立Session
      console.log('获取验证码以建立Session...');
      const captchaResponse = await UserApi.getCaptcha();
      console.log('验证码响应:', captchaResponse);

      // 2. 调用Spring Boot登录API
      const loginData: UserLoginRequest = {
        username: this.username,
        password: this.password,
        captcha: captchaResponse // 使用从服务器获取的验证码
      };

      console.log('发送登录请求:', loginData);
      const response = await UserApi.login(loginData);
      console.log('登录响应:', response);

      // 保存用户信息
      AppStorage.setOrCreate('userToken', response.token);
      AppStorage.setOrCreate('userInfo', response.userInfo);

      console.log('用户信息已保存，准备跳转');

      // 跳转到银行卡页面
      await router.replaceUrl({
        url: 'pages/MyBankCardPage'
      });

      console.log('页面跳转成功！');
      promptAction.showToast({ message: '登录成功' });

    } catch (error) {
      console.error('登录过程出错:', error);
      let errorMessage = '登录失败，请重试';
      if (error instanceof Error) {
        errorMessage = `登录失败: ${error.message}`;
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      console.log('登录流程结束，重置loading状态');
      this.isLoading = false;
    }
  }



  /**
   * 测试Spring Boot后端连接
   */
  async testSpringBootConnection() {
    try {
      const response = await this.callSpringBootTest();

      // 检查业务状态码
      if (response.code === 200) {
        promptAction.showToast({
          message: `✅ 连接成功: ${response.msg}`,
          duration: 3000,
          bottom: 50
        });
      } else {
        promptAction.showToast({
          message: `❌ 业务错误: ${response.msg}`,
          duration: 3000,
          bottom: 50
        });
      }
    } catch (error) {
      promptAction.showToast({
        message: `❌ 连接失败: ${(error as Error).message}`,
        duration: 3000,
        bottom: 50
      });
    }
  }

  /**
   * 调用Spring Boot测试接口
   */
  private callSpringBootTest(): Promise<SpringBootResponse> {
    return new Promise((resolve, reject) => {
      const httpRequest = http.createHttp();

      const options: http.HttpRequestOptions = {
        method: http.RequestMethod.GET,
        header: {
          'Accept': 'application/json'
        },
        readTimeout: 10000,
        connectTimeout: 10000
      };

      httpRequest.request('http://localhost:8080/test', options, (err: BusinessError, data: http.HttpResponse) => {
        if (err) {
          console.error('HTTP请求失败:', err);
          reject(new Error(err.message));
          return;
        }

        try {
          if (data.responseCode !== 200) {
            reject(new Error(`HTTP ${data.responseCode}`));
            return;
          }

          const response = JSON.parse(data.result as string) as SpringBootResponse;
          resolve(response);
        } catch (parseError) {
          console.error('响应解析失败:', parseError);
          reject(new Error('响应解析失败'));
        } finally {
          httpRequest.destroy();
        }
      });
    });
  }
}
