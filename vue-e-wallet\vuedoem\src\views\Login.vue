<template>
  <div class="login-container">
    <div class="login-box">
      <div class="avatar-container">
        <img
          src="@/assets/logo.png"
          alt="系统Logo"
          class="avatar"
          @error="handleImageError"
        >
      </div>
      <h2 class="title">电子钱包交易系统</h2>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入账户名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入账户密码"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="验证码" prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              clearable
              maxlength="4"
              show-word-limit
              style="width: 60%"
              @input="loginForm.captcha = loginForm.captcha.replace(/[^\d]/g,'')"
            />
            <el-button
              @click="refreshCaptcha"
              style="margin-left: 10px"
              :loading="captchaLoading"
            >
              {{ captchaLoading ? '获取中...' : '获取验证码' }}
            </el-button>
          </div>
          <div v-if="showCaptchaHint" class="captcha-hint">
            测试提示：当前验证码 {{ lastCaptcha }}
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleLogin"
            style="width: 100%"
            :loading="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/api/index.js'
import axios from 'axios'

// 配置axios默认设置
axios.defaults.withCredentials = true

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const captchaLoading = ref(false)
const lastCaptcha = ref('')
const showCaptchaHint = import.meta.env.MODE === 'development'

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  captcha: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入账户名称', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入账户密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在6到20个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!/^\d{4}$/.test(value)) {
          callback(new Error('验证码必须为4位数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 表单引用
const loginFormRef = ref()

// 图片错误处理
const handleImageError = (e) => {
  e.target.src = 'data:image/svg+xml;base64,...'
  console.error('加载Logo图片失败')
}

// 获取验证码
const refreshCaptcha = async () => {
  try {
    captchaLoading.value = true

    // 使用API模块获取验证码
    const response = await userApi.getCaptcha()

    console.log('验证码接口响应:', response)

    // API模块已经处理了响应格式，直接使用data
    if (response && response.data) {
      const captchaText = String(response.data).trim()

      // 验证码格式检查
      if (!/^\d{4}$/.test(captchaText)) {
        throw new Error('验证码必须为4位数字')
      }

      lastCaptcha.value = captchaText
      ElMessage.success('验证码获取成功')
    } else {
      throw new Error('验证码获取失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error(`获取验证码失败: ${error.message}`)

    // 降级方案：生成随机验证码
    lastCaptcha.value = Math.floor(1000 + Math.random() * 9000).toString()
    ElMessage.info(`降级方案：使用本地验证码 ${lastCaptcha.value}`)
  } finally {
    captchaLoading.value = false
  }
}

// 登录处理
const handleLogin = async () => {
  try {
    loading.value = true
    
    // 1. 验证表单
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    // 2. 检查验证码是否匹配
    if (loginForm.value.captcha !== lastCaptcha.value) {
      ElMessage.error('验证码错误，请重新获取')
      return refreshCaptcha()
    }

    // 3. 准备请求数据
    const requestPayload = {
      username: loginForm.value.username.trim(),
      password: loginForm.value.password,
      captcha: loginForm.value.captcha
    }

    console.log('提交的登录数据:', {
      username: requestPayload.username,
      password: '******',
      captcha: requestPayload.captcha
    })

    // 4. 发送请求 - 使用API模块
    const formData = new URLSearchParams()
    formData.append('username', requestPayload.username)
    formData.append('password', requestPayload.password)
    formData.append('captcha', requestPayload.captcha)

    const response = await userApi.login(formData)

    // 5. 处理响应 - API模块已经处理了响应格式
    if (response && response.data) {
      ElMessage.success(response.msg || '登录成功')

      // 存储用户信息到store和localStorage
      const userData = {
        username: requestPayload.username,
        token: response.data?.token || 'demo-token',
        ...response.data
      }

      userStore.login(userData)
      localStorage.setItem('token', userData.token)
      localStorage.setItem('user', JSON.stringify(userData))

      router.push('/home')
    } else {
      ElMessage.error('登录失败')
      refreshCaptcha()
    }
  } catch (error) {
    console.error('登录错误详情:', error)
    
    if (error.response) {
      // 提取更详细的错误信息
      const serverMessage = error.response.data?.message || 
                          error.response.data?.msg ||
                          error.response.data?.error ||
                          (typeof error.response.data === 'string' ? error.response.data : '未知错误')
      
      let errorMessage = `登录失败: ${serverMessage}`
      
      // 特定错误处理
      if (error.response.status === 400) {
        if (serverMessage.includes('验证码')) {
          errorMessage = '验证码错误，请重新获取'
          refreshCaptcha()
        }
        if (serverMessage.includes('密码') || serverMessage.includes('用户名')) {
          loginFormRef.value.validateField('password')
        }
      }
      
      ElMessage.error(errorMessage)
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.request) {
      ElMessage.error('服务器无响应，请检查后端服务是否运行')
    } else {
      ElMessage.error(`请求错误: ${error.message}`)
    }
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取初始验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

<style scoped>
.avatar-container {
  text-align: center;
  margin-bottom: 20px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 0%;
  border: 3px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: #f5f7fa;
  object-fit: cover;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #2c3e50, #3498db);
}

.login-box {
  width: 450px;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #2c3e50;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}
</style>

