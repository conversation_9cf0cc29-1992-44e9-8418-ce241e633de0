if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface MyBankCardPage_Params {
    cardList?: BankCard[];
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
class MyBankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardList = new ObservedPropertyObjectPU([], this, "cardList");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: MyBankCardPage_Params) {
        if (params.cardList !== undefined) {
            this.cardList = params.cardList;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: MyBankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardList.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardList.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardList: ObservedPropertyObjectPU<BankCard[]>;
    get cardList() {
        return this.__cardList.get();
    }
    set cardList(newValue: BankCard[]) {
        this.__cardList.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        console.log('MyBankCardPage - aboutToAppear 开始');
        // 直接加载测试数据，确保界面有内容显示
        this.addTestData();
        this.isLoading = false;
        console.log('MyBankCardPage - aboutToAppear 完成，银行卡数量:', this.cardList.length);
        console.log('MyBankCardPage - 银行卡数据:', JSON.stringify(this.cardList));
    }
    onPageShow() {
        console.log('MyBankCardPage onPageShow - 页面显示');
        // 检查是否有银行卡添加事件
        const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
        console.log('MyBankCardPage onPageShow - 检查银行卡添加事件:', cardAdded);
        if (cardAdded) {
            console.log('MyBankCardPage - 检测到银行卡添加，重新加载银行卡列表');
            // 检查是否有新添加的银行卡数据
            const newCard = tempDataManager.getData('NEW_BANK_CARD') as BankCard;
            if (newCard) {
                console.log('MyBankCardPage - 发现新添加的银行卡:', JSON.stringify(newCard));
                // 将新卡添加到当前列表
                this.cardList.push(newCard);
                // 清除临时数据
                tempDataManager.setData('NEW_BANK_CARD', null);
            }
            // 重新加载银行卡列表
            this.loadBankCards();
            // 清除添加事件标志
            tempDataManager.setData('BANK_CARD_ADDED', false);
        }
        // 检查是否有银行卡更新事件
        const cardUpdated = tempDataManager.getData('BANK_CARD_UPDATED');
        console.log('MyBankCardPage onPageShow - 检查银行卡更新事件:', cardUpdated);
        if (cardUpdated) {
            console.log('MyBankCardPage - 检测到银行卡更新，重新加载银行卡列表');
            // 检查是否有更新的银行卡数据
            const updatedCard = tempDataManager.getData('UPDATED_BANK_CARD') as BankCard;
            if (updatedCard) {
                console.log('MyBankCardPage - 发现更新的银行卡:', JSON.stringify(updatedCard));
                // 在当前列表中更新对应的银行卡
                const cardIndex = this.cardList.findIndex(card => card.cardId === updatedCard.cardId);
                if (cardIndex !== -1) {
                    this.cardList[cardIndex] = updatedCard;
                    console.log('MyBankCardPage - 银行卡列表已更新');
                }
                // 清除临时数据
                tempDataManager.setData('UPDATED_BANK_CARD', null);
            }
            // 重新加载银行卡列表
            this.loadBankCards();
            // 清除更新事件标志
            tempDataManager.setData('BANK_CARD_UPDATED', false);
        }
        // 检查是否有银行卡解绑事件
        const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
        console.log('MyBankCardPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
        if (cardUnbound) {
            console.log('MyBankCardPage - 检测到银行卡解绑，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 清除解绑事件标志
            tempDataManager.setData('BANK_CARD_UNBOUND', false);
        }
        // 检查是否有银行卡删除事件
        const cardDeleted = tempDataManager.getData('BANK_CARD_DELETED');
        console.log('MyBankCardPage onPageShow - 检查银行卡删除事件:', cardDeleted);
        if (cardDeleted) {
            console.log('MyBankCardPage - 检测到银行卡删除，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 清除删除事件标志
            tempDataManager.setData('BANK_CARD_DELETED', false);
        }
    }
    async loadBankCards() {
        try {
            this.isLoading = true;
            console.log('开始加载银行卡列表...');
            // 先加载测试数据
            this.addTestData();
            try {
                // 尝试从API获取银行卡
                const userId = 1; // 临时使用固定用户ID
                const apiCards = await BankCardApi.getCardList(userId);
                console.log('API银行卡列表加载成功，数量:', apiCards.length);
                if (apiCards.length > 0) {
                    this.cardList = apiCards;
                }
            }
            catch (apiError) {
                console.log('API调用失败，使用本地数据:', apiError);
            }
            // 加载本地存储的银行卡
            const localCards = (tempDataManager.getData('LOCAL_BANK_CARDS') || []) as BankCard[];
            console.log('本地银行卡数量:', localCards.length);
            if (localCards.length > 0) {
                // 合并本地银行卡到列表中（避免重复）
                localCards.forEach((localCard: BankCard) => {
                    const exists = this.cardList.find(card => card.cardId === localCard.cardId);
                    if (!exists) {
                        this.cardList.push(localCard);
                    }
                });
            }
            console.log('最终银行卡列表数量:', this.cardList.length);
            console.log('银行卡数据:', JSON.stringify(this.cardList));
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
            console.log('使用测试数据');
            this.addTestData();
            promptAction.showToast({ message: '加载银行卡列表失败，显示测试数据' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 添加测试数据
     */
    addTestData() {
        this.cardList = [
            {
                cardId: 1,
                userId: 1,
                cardNo: '****************',
                cardType: BankCardType.DEBIT,
                bankName: '工商银行',
                holderName: '张三',
                isBound: BankCardStatus.BOUND,
                createTime: '2024-01-01 10:00:00',
                updateTime: '2024-01-01 10:00:00'
            },
            {
                cardId: 2,
                userId: 1,
                cardNo: '****************',
                cardType: BankCardType.DEBIT,
                bankName: '中国银行',
                holderName: '张三',
                isBound: BankCardStatus.BOUND,
                createTime: '2024-01-02 10:00:00',
                updateTime: '2024-01-02 10:00:00'
            },
            {
                cardId: 3,
                userId: 1,
                cardNo: '****************',
                cardType: BankCardType.CREDIT,
                bankName: '平安银行',
                holderName: '张三',
                isBound: BankCardStatus.UNBOUND,
                createTime: '2024-01-03 10:00:00',
                updateTime: '2024-01-03 10:00:00'
            },
            {
                cardId: 4,
                userId: 1,
                cardNo: '****************',
                cardType: BankCardType.DEBIT,
                bankName: '工商银行',
                holderName: '张三',
                isBound: BankCardStatus.UNBOUND,
                createTime: '2024-01-04 10:00:00',
                updateTime: '2024-01-04 10:00:00'
            },
            {
                cardId: 5,
                userId: 1,
                cardNo: '****************',
                cardType: BankCardType.CREDIT,
                bankName: '农业银行',
                holderName: '张三',
                isBound: BankCardStatus.BOUND,
                createTime: '2024-01-05 10:00:00',
                updateTime: '2024-01-05 10:00:00'
            }
        ];
        console.log('测试数据添加完成，银行卡数量:', this.cardList.length);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(207:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(209:7)", "entry");
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.width('90%');
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.margin({ top: 20 });
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.borderRadius(16);
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.backgroundColor('#ff3785f5');
            // 修改后的顶部导航栏（淡粉色卡片式）
            Column.shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(210:9)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的银行卡');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(211:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('+');
            Button.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(217:11)", "entry");
            Button.fontSize(28);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#FF4444');
            Button.borderRadius(25);
            Button.width(50);
            Button.height(50);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                console.log('点击添加银行卡按钮');
                router.pushUrl({ url: 'pages/AddBankCardPage' }).then(() => {
                    console.log('跳转到添加银行卡页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到添加银行卡页面失败:', error.message);
                    promptAction.showToast({ message: '跳转失败，请重试' });
                });
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 修改后的顶部导航栏（淡粉色卡片式）
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 主要内容区域
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.CardListView.bind(this)();
                });
            }
        }, If);
        If.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        Column.pop();
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(260:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(261:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
            LoadingProgress.color('#1976D2');
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(266:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.margin({ top: 16 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    CardListView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.cardList.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 空状态提示
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(281:7)", "entry");
                        // 空状态提示
                        Column.width('100%');
                        // 空状态提示
                        Column.layoutWeight(1);
                        // 空状态提示
                        Column.justifyContent(FlexAlign.Center);
                        // 空状态提示
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无银行卡');
                        Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(282:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('点击右上角"+"添加银行卡');
                        Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(287:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#CCCCCC');
                    }, Text);
                    Text.pop();
                    // 空状态提示
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡列表
                        List.create({ space: 0 });
                        List.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(297:7)", "entry");
                        // 银行卡列表
                        List.layoutWeight(1);
                        // 银行卡列表
                        List.scrollBar(BarState.Off);
                        // 银行卡列表
                        List.padding({ left: 16, right: 16, top: 16, bottom: 20 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const card = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.swipeAction({ end: this.SwipeActionBuilder.bind(this, card) });
                                    ListItem.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(299:11)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.BankCardItem.bind(this)(card, index);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.cardList, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    // 银行卡列表
                    List.pop();
                });
            }
        }, If);
        If.pop();
    }
    BankCardItem(card: BankCard, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡主体内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(314:5)", "entry");
            // 银行卡主体内容
            Column.width('100%');
            // 银行卡主体内容
            Column.padding(16);
            // 银行卡主体内容
            Column.linearGradient(this.getCardGradient(index));
            // 银行卡主体内容
            Column.borderRadius(12);
            // 银行卡主体内容
            Column.margin({ bottom: 12 });
            // 银行卡主体内容
            Column.shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetX: 0, offsetY: 2 });
            Gesture.create(GesturePriority.Low);
            // 双击手势 - 查看详情
            TapGesture.create({ count: 2 });
            // 双击手势 - 查看详情
            TapGesture.onAction(() => {
                router.pushUrl({
                    url: 'pages/BankCardDetailPage',
                    params: { cardId: card.cardId }
                });
            });
            // 双击手势 - 查看详情
            TapGesture.pop();
            Gesture.pop();
            Gesture.create(GesturePriority.Low);
            // 单击手势 - 防止与双击冲突
            TapGesture.create({ count: 1 });
            // 单击手势 - 防止与双击冲突
            TapGesture.onAction(() => {
                // 单击暂时不做处理，避免与双击冲突
            });
            // 单击手势 - 防止与双击冲突
            TapGesture.pop();
            Gesture.pop();
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部：银行名称和卡片类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(316:7)", "entry");
            // 顶部：银行名称和卡片类型
            Row.width('100%');
            // 顶部：银行名称和卡片类型
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(317:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡图标
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(324:9)", "entry");
            // 银行卡图标
            Row.width(40);
            // 银行卡图标
            Row.height(24);
            // 银行卡图标
            Row.justifyContent(FlexAlign.Center);
            // 银行卡图标
            Row.backgroundColor('rgba(255, 255, 255, 0.2)');
            // 银行卡图标
            Row.borderRadius(4);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(325:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#FFFFFF');
        }, Image);
        // 银行卡图标
        Row.pop();
        // 顶部：银行名称和卡片类型
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 中间：卡片类型
            Text.create(card.cardType === BankCardType.DEBIT ? '储蓄卡' : '信用卡');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(340:7)", "entry");
            // 中间：卡片类型
            Text.fontSize(12);
            // 中间：卡片类型
            Text.fontColor('rgba(255, 255, 255, 0.8)');
            // 中间：卡片类型
            Text.alignSelf(ItemAlign.Start);
            // 中间：卡片类型
            Text.margin({ bottom: 12 });
        }, Text);
        // 中间：卡片类型
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡号
            Text.create(`**** **** **** ${card.cardNo.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(347:7)", "entry");
            // 银行卡号
            Text.fontSize(18);
            // 银行卡号
            Text.fontColor('#FFFFFF');
            // 银行卡号
            Text.fontWeight(FontWeight.Medium);
            // 银行卡号
            Text.alignSelf(ItemAlign.Start);
            // 银行卡号
            Text.margin({ bottom: 8 });
        }, Text);
        // 银行卡号
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部：持卡人姓名和绑定状态
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(355:7)", "entry");
            // 底部：持卡人姓名和绑定状态
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.holderName);
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(356:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('rgba(255, 255, 255, 0.9)');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 绑定状态
            Text.create(card.isBound === BankCardStatus.BOUND ? '已绑定' : '未绑定');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(362:9)", "entry");
            // 绑定状态
            Text.fontSize(12);
            // 绑定状态
            Text.fontColor('#FFFFFF');
            // 绑定状态
            Text.backgroundColor(card.isBound === BankCardStatus.BOUND ? 'rgba(76, 175, 80, 0.8)' : 'rgba(255, 152, 0, 0.8)');
            // 绑定状态
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            // 绑定状态
            Text.borderRadius(4);
        }, Text);
        // 绑定状态
        Text.pop();
        // 底部：持卡人姓名和绑定状态
        Row.pop();
        // 银行卡主体内容
        Column.pop();
    }
    SwipeActionBuilder(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(398:5)", "entry");
            Row.padding({ left: 16, right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.createWithLabel('修改');
            Button.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(400:7)", "entry");
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.fontSize(14);
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.fontColor('#FFFFFF');
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.backgroundColor('#FF9800');
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.borderRadius(8);
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.width(60);
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.height(40);
            // 修改按钮 - 跳转到银行卡编辑页面
            Button.onClick(() => {
                console.log('点击修改按钮，银行卡ID:', card.cardId);
                router.pushUrl({
                    url: 'pages/EditBankCardPage',
                    params: {
                        cardId: card.cardId,
                        editMode: true,
                        cardData: card
                    }
                }).then(() => {
                    console.log('跳转到编辑页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到编辑页面失败:', error.message);
                    promptAction.showToast({ message: '跳转失败，请重试' });
                });
            });
        }, Button);
        // 修改按钮 - 跳转到银行卡编辑页面
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 删除按钮
            Button.createWithLabel('删除');
            Button.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(425:7)", "entry");
            // 删除按钮
            Button.fontSize(14);
            // 删除按钮
            Button.fontColor('#FFFFFF');
            // 删除按钮
            Button.backgroundColor('#F44336');
            // 删除按钮
            Button.borderRadius(8);
            // 删除按钮
            Button.width(60);
            // 删除按钮
            Button.height(40);
            // 删除按钮
            Button.margin({ left: 8 });
            // 删除按钮
            Button.onClick(() => {
                this.confirmDeleteCard(card);
            });
        }, Button);
        // 删除按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 解绑按钮
            Button.createWithLabel('解绑');
            Button.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(438:7)", "entry");
            // 解绑按钮
            Button.fontSize(14);
            // 解绑按钮
            Button.fontColor('#FFFFFF');
            // 解绑按钮
            Button.backgroundColor('#9E9E9E');
            // 解绑按钮
            Button.borderRadius(8);
            // 解绑按钮
            Button.width(60);
            // 解绑按钮
            Button.height(40);
            // 解绑按钮
            Button.margin({ left: 8 });
            // 解绑按钮
            Button.onClick(() => {
                this.confirmUnbindCard(card);
            });
        }, Button);
        // 解绑按钮
        Button.pop();
        Row.pop();
    }
    /**
     * 获取银行卡渐变色
     */
    getCardGradient(index: number): LinearGradient {
        const gradients: LinearGradient[] = [
            // 蓝色渐变
            {
                angle: 135,
                colors: [['#667eea', 0], ['#764ba2', 1]]
            },
            // 紫色渐变
            {
                angle: 135,
                colors: [['#f093fb', 0], ['#f5576c', 1]]
            },
            // 绿色渐变
            {
                angle: 135,
                colors: [['#4facfe', 0], ['#00f2fe', 1]]
            },
            // 橙色渐变
            {
                angle: 135,
                colors: [['#ff9a9e', 0], ['#fecfef', 1]]
            },
            // 深蓝渐变
            {
                angle: 135,
                colors: [['#a8edea', 0], ['#fed6e3', 1]]
            }
        ];
        return gradients[index % gradients.length];
    }
    /**
     * 确认删除银行卡
     */
    async confirmDeleteCard(card: BankCard) {
        try {
            const result = await promptAction.showDialog({
                title: '确认删除',
                message: `确定要删除银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？\n删除后无法恢复！`,
                buttons: [
                    { text: '确定删除', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.deleteCard(card);
            }
        }
        catch (error) {
            console.error('显示删除确认对话框失败:', error);
        }
    }
    /**
     * 删除银行卡
     */
    async deleteCard(card: BankCard) {
        try {
            console.log('开始删除银行卡:', card.cardId);
            // 从本地列表中删除
            const cardIndex = this.cardList.findIndex(c => c.cardId === card.cardId);
            if (cardIndex !== -1) {
                this.cardList.splice(cardIndex, 1);
                console.log('从本地列表删除成功，剩余银行卡数量:', this.cardList.length);
            }
            // 从本地存储中删除
            const localCards = (tempDataManager.getData('LOCAL_BANK_CARDS') || []) as BankCard[];
            const localIndex = localCards.findIndex(c => c.cardId === card.cardId);
            if (localIndex !== -1) {
                localCards.splice(localIndex, 1);
                tempDataManager.setData('LOCAL_BANK_CARDS', localCards);
                console.log('从本地存储删除成功');
            }
            // 尝试调用API删除（如果API可用）
            try {
                await BankCardApi.deleteCard(card.cardId);
                console.log('API删除成功');
            }
            catch (apiError) {
                console.log('API删除失败，但本地删除已完成:', apiError);
            }
            promptAction.showToast({ message: '删除成功' });
            // 通知其他页面银行卡已删除
            tempDataManager.setData('BANK_CARD_DELETED', true);
        }
        catch (error) {
            console.error('删除银行卡失败:', error);
            promptAction.showToast({ message: '删除失败，请重试' });
        }
    }
    /**
     * 确认解绑银行卡
     */
    async confirmUnbindCard(card: BankCard) {
        try {
            const result = await promptAction.showDialog({
                title: '确认解绑',
                message: `确定要解绑银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
                buttons: [
                    { text: '确定', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.unbindCard(card);
            }
        }
        catch (error) {
            console.error('显示确认对话框失败:', error);
        }
    }
    /**
     * 解绑银行卡
     */
    async unbindCard(card: BankCard) {
        try {
            await BankCardApi.unbindCard(card.cardId);
            promptAction.showToast({ message: '解绑成功' });
            // 通知其他页面银行卡已解绑
            console.log('MyBankCardPage - 设置银行卡解绑事件标志');
            tempDataManager.setData('BANK_CARD_UNBOUND', true);
            // 重新加载银行卡列表
            await this.loadBankCards();
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            promptAction.showToast({ message: '解绑失败，请重试' });
        }
    }
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(596:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({ width: { top: 1 }, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(598:7)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.alignItems(HorizontalAlign.Center);
            // 银行卡
            Column.padding({ top: 8, bottom: 8 });
            // 银行卡
            Column.onClick(() => {
                // 当前页面，不需要跳转
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(599:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(601:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#1976D2');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(614:7)", "entry");
            // 交易
            Column.layoutWeight(1);
            // 交易
            Column.alignItems(HorizontalAlign.Center);
            // 交易
            Column.padding({ top: 8, bottom: 8 });
            // 交易
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionListPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(615:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(617:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(632:7)", "entry");
            // 钱包
            Column.layoutWeight(1);
            // 钱包
            Column.alignItems(HorizontalAlign.Center);
            // 钱包
            Column.padding({ top: 8, bottom: 8 });
            // 钱包
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/WalletPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(633:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(635:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 钱包
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付中心
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(650:7)", "entry");
            // 支付中心
            Column.layoutWeight(1);
            // 支付中心
            Column.alignItems(HorizontalAlign.Center);
            // 支付中心
            Column.padding({ top: 8, bottom: 8 });
            // 支付中心
            Column.onClick(() => {
                console.log('底部导航：点击支付中心按钮');
                promptAction.showToast({
                    message: '正在跳转到支付中心...',
                    duration: 1000
                });
                router.pushUrl({
                    url: 'pages/PaymentCenterPage'
                }).then(() => {
                    console.log('底部导航：跳转支付中心成功');
                }).catch((error: Error) => {
                    console.error('底部导航：跳转支付中心失败:', error.message);
                    promptAction.showToast({
                        message: '跳转失败: ' + error.message,
                        duration: 3000
                    });
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(651:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(653:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 支付中心
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(681:7)", "entry");
            // 我的
            Column.layoutWeight(1);
            // 我的
            Column.alignItems(HorizontalAlign.Center);
            // 我的
            Column.padding({ top: 8, bottom: 8 });
            // 我的
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(682:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/MyBankCardPage.ets(684:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 我的
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "MyBankCardPage";
    }
}
registerNamedRoute(() => new MyBankCardPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/MyBankCardPage", pageFullPath: "entry/src/main/ets/pages/MyBankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
