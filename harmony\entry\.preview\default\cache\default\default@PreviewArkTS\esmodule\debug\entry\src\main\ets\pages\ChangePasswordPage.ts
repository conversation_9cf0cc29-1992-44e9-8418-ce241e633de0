if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ChangePasswordPage_Params {
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
class ChangePasswordPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentPassword = new ObservedPropertySimplePU('', this, "currentPassword");
        this.__newPassword = new ObservedPropertySimplePU('', this, "newPassword");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ChangePasswordPage_Params) {
        if (params.currentPassword !== undefined) {
            this.currentPassword = params.currentPassword;
        }
        if (params.newPassword !== undefined) {
            this.newPassword = params.newPassword;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: ChangePasswordPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__newPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentPassword.aboutToBeDeleted();
        this.__newPassword.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentPassword: ObservedPropertySimplePU<string>;
    get currentPassword() {
        return this.__currentPassword.get();
    }
    set currentPassword(newValue: string) {
        this.__currentPassword.set(newValue);
    }
    private __newPassword: ObservedPropertySimplePU<string>;
    get newPassword() {
        return this.__newPassword.get();
    }
    set newPassword(newValue: string) {
        this.__newPassword.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    async handleSave() {
        if (!this.currentPassword.trim()) {
            promptAction.showToast({ message: '请输入当前密码' });
            return;
        }
        if (!this.newPassword.trim()) {
            promptAction.showToast({ message: '请输入新密码' });
            return;
        }
        if (this.newPassword.length < 6) {
            promptAction.showToast({ message: '新密码长度不能少于6位' });
            return;
        }
        if (!this.confirmPassword.trim()) {
            promptAction.showToast({ message: '请确认新密码' });
            return;
        }
        if (this.newPassword !== this.confirmPassword) {
            promptAction.showToast({ message: '两次输入的密码不一致' });
            return;
        }
        if (this.currentPassword === this.newPassword) {
            promptAction.showToast({ message: '新密码不能与当前密码相同' });
            return;
        }
        this.isLoading = true;
        try {
            // 调用API修改密码
            await UserApi.changePassword({
                currentPassword: this.currentPassword,
                newPassword: this.newPassword
            });
            promptAction.showToast({ message: '密码修改成功' });
            router.back();
        }
        catch (error) {
            console.error('密码修改失败:', error);
            promptAction.showToast({ message: '密码修改失败，请检查当前密码是否正确' });
        }
        finally {
            this.isLoading = false;
        }
    }
    handleCancel() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(67:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(69:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({ width: { bottom: 1 }, color: '#E5E7EB' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(70:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(78:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认修改');
            Button.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(85:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleSave();
            });
        }, Button);
        Button.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(101:7)", "entry");
            // 表单内容
            Column.padding(20);
            // 表单内容
            Column.backgroundColor('#FFFFFF');
            // 表单内容
            Column.margin({ top: 16, left: 16, right: 16 });
            // 表单内容
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 当前密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(103:9)", "entry");
            // 当前密码
            Column.width('100%');
            // 当前密码
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(104:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('当前密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(105:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(100);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入当前密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(110:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.currentPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(124:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 当前密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 新密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(132:9)", "entry");
            // 新密码
            Column.width('100%');
            // 新密码
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(133:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(134:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(100);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入新密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(139:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.newPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(153:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 新密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(161:9)", "entry");
            // 确认密码
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(162:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(163:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(100);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请确认新密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(168:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.confirmPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(182:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 确认密码
        Column.pop();
        // 表单内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 密码要求提示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(194:7)", "entry");
            // 密码要求提示
            Column.width('100%');
            // 密码要求提示
            Column.padding(20);
            // 密码要求提示
            Column.margin({ top: 16, left: 16, right: 16 });
            // 密码要求提示
            Column.backgroundColor('#F8F9FA');
            // 密码要求提示
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码要求：');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(195:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 密码长度不少于6位');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(201:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 建议包含字母、数字和特殊字符');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(207:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 密码要求提示
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(218:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ChangePasswordPage";
    }
}
registerNamedRoute(() => new ChangePasswordPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/ChangePasswordPage", pageFullPath: "entry/src/main/ets/pages/ChangePasswordPage", integratedHsp: "false", moduleType: "followWithHap" });
