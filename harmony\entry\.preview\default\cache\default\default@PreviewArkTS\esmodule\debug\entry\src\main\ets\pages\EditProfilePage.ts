if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface EditProfilePage_Params {
    username?: string;
    email?: string;
    phone?: string;
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
class EditProfilePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__username = new ObservedPropertySimplePU('', this, "username");
        this.__email = new ObservedPropertySimplePU('', this, "email");
        this.__phone = new ObservedPropertySimplePU('', this, "phone");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: EditProfilePage_Params) {
        if (params.username !== undefined) {
            this.username = params.username;
        }
        if (params.email !== undefined) {
            this.email = params.email;
        }
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: EditProfilePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__username.purgeDependencyOnElmtId(rmElmtId);
        this.__email.purgeDependencyOnElmtId(rmElmtId);
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__username.aboutToBeDeleted();
        this.__email.aboutToBeDeleted();
        this.__phone.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __username: ObservedPropertySimplePU<string>;
    get username() {
        return this.__username.get();
    }
    set username(newValue: string) {
        this.__username.set(newValue);
    }
    private __email: ObservedPropertySimplePU<string>;
    get email() {
        return this.__email.get();
    }
    set email(newValue: string) {
        this.__email.set(newValue);
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.loadUserInfo();
    }
    async loadUserInfo() {
        try {
            // 临时使用固定数据
            this.username = '张三';
            this.email = '<EMAIL>';
            this.phone = '13800138000';
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }
    async handleSave() {
        if (!this.username.trim()) {
            promptAction.showToast({ message: '请输入用户名' });
            return;
        }
        if (!this.email.trim()) {
            promptAction.showToast({ message: '请输入邮箱' });
            return;
        }
        if (!this.phone.trim()) {
            promptAction.showToast({ message: '请输入手机号' });
            return;
        }
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(this.email)) {
            promptAction.showToast({ message: '邮箱格式不正确' });
            return;
        }
        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(this.phone)) {
            promptAction.showToast({ message: '手机号格式不正确' });
            return;
        }
        this.isLoading = true;
        try {
            // 调用API保存用户信息
            await UserApi.updateProfile({
                username: this.username,
                email: this.email,
                phone: this.phone
            });
            promptAction.showToast({ message: '保存成功' });
            router.back();
        }
        catch (error) {
            console.error('保存失败:', error);
            promptAction.showToast({ message: '保存失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    handleCancel() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(82:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(84:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({ width: { bottom: 1 }, color: '#E5E7EB' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(85:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改基本资料');
            Text.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(93:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('保存');
            Button.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(100:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleSave();
            });
        }, Button);
        Button.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(116:7)", "entry");
            // 表单内容
            Column.padding(20);
            // 表单内容
            Column.backgroundColor('#FFFFFF');
            // 表单内容
            Column.margin({ top: 16, left: 16, right: 16 });
            // 表单内容
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户名
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(118:9)", "entry");
            // 用户名
            Column.width('100%');
            // 用户名
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(119:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户名');
            Text.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(120:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入用户名', text: this.username });
            TextInput.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(125:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.username = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(138:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 用户名
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 邮箱
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(146:9)", "entry");
            // 邮箱
            Column.width('100%');
            // 邮箱
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(147:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('邮箱');
            Text.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(148:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入邮箱', text: this.email });
            TextInput.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(153:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Email);
            TextInput.onChange((value: string) => {
                this.email = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(167:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 邮箱
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 手机号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(175:9)", "entry");
            // 手机号
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(176:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('手机');
            Text.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(177:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入手机号', text: this.phone });
            TextInput.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(182:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.PhoneNumber);
            TextInput.onChange((value: string) => {
                this.phone = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(196:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 手机号
        Column.pop();
        // 表单内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/EditProfilePage.ets(207:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "EditProfilePage";
    }
}
registerNamedRoute(() => new EditProfilePage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/EditProfilePage", pageFullPath: "entry/src/main/ets/pages/EditProfilePage", integratedHsp: "false", moduleType: "followWithHap" });
