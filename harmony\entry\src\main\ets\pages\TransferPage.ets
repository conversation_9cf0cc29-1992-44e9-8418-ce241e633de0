import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import { PageResult } from '../common/types/PageResult';
import { Transaction, TransactionStatus, TransactionType } from '../common/types/Transaction';

@Entry
@Component
struct TransactionListPage {
  @State transactions: Transaction[] = [];
  @State isLoading: boolean = true;

  aboutToAppear() {
    this.loadTransactions();
  }

  async loadTransactions() {
    try {
      this.isLoading = true;
      const result: PageResult<Transaction> = await TransactionApi.getTransactionList();
      this.transactions = result.records || [];
    } catch (error) {
      console.error('加载交易记录失败:', error);
      promptAction.showToast({ message: '加载交易记录失败' });
      this.transactions = this.getTestData();
    } finally {
      this.isLoading = false;
    }
  }

  getTestData(): Transaction[] {
    return [
      {
        id: 1,
        type: TransactionType.TRANSFER,
        amount: 500.00,
        target: '李四',
        time: '2023-05-15 14:30',
        status: TransactionStatus.COMPLETED
      },
      {
        id: 2,
        type: TransactionType.DEPOSIT,
        amount: 1000.00,
        target: '银行卡(尾号1234)',
        time: '2023-05-14 09:15',
        status: TransactionStatus.COMPLETED
      },
      {
        id: 3,
        type: TransactionType.CONSUME,
        amount: -128.50,
        target: '星巴克咖啡',
        time: '2023-05-12 18:45',
        status: TransactionStatus.COMPLETED
      },
      {
        id: 4,
        type: TransactionType.WITHDRAW,
        amount: -300.00,
        target: '银行卡(尾号5678)',
        time: '2023-05-10 16:20',
        status: TransactionStatus.PROCESSING
      }
    ] as Transaction[];
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('交易记录')
          .fontSize(20)
          .fontColor($r('app.color.white'))
          .fontWeight(FontWeight.Bold)
      }
      .width('100%')
      .height(60)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: [
          [$r('app.color.primary'), 0.0],
          [$r('app.color.primary_dark'), 1.0]
        ]
      })

      if (this.isLoading) {
        this.LoadingView()
      } else if (this.transactions.length === 0) {
        this.EmptyView()
      } else {
        Scroll() {
          Column() {
            Row() {
              Text('全部交易')
                .fontSize(18)
                .fontColor($r('app.color.text_primary'))
                .fontWeight(FontWeight.Medium)

              Text(`共 ${this.transactions.length} 笔`)
                .fontSize(14)
                .fontColor($r('app.color.text_secondary'))
                .margin({ left: 8 })
            }
            .width('100%')
            .margin({ bottom: 16 })

            ForEach(this.transactions, (item: Transaction) => {
              this.TransactionItem(item)
            })
          }
          .width('100%')
          .padding(16)
        }
        .width('100%')
        .layoutWeight(1)
      }

      this.BottomNavigation()
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background'))
  }

  @Builder
  TransactionItem(item: Transaction) {
    Column() {
      Row() {
        Column() {
          Text(this.getTypeIcon(item.type))
            .fontSize(24)
        }
        .width(48)
        .height(48)
        .backgroundColor(this.getTypeColor(item.type) + '20')
        .borderRadius(24)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .margin({ right: 12 })

        Column() {
          Row() {
            Text(this.getTypeName(item.type))
              .fontSize(16)
              .fontColor($r('app.color.text_primary'))
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            Text(item.amount > 0 ? `+¥${item.amount.toFixed(2)}` : `-¥${Math.abs(item.amount).toFixed(2)}`)
              .fontSize(16)
              .fontColor(item.amount > 0 ? $r('app.color.success') : $r('app.color.warning'))
          }

          Text(item.target)
            .fontSize(14)
            .fontColor($r('app.color.text_secondary'))
            .margin({ top: 4 })
            .alignSelf(ItemAlign.Start)

          Row() {
            Text(item.time)
              .fontSize(12)
              .fontColor($r('app.color.text_hint'))

            Text(this.getStatusName(item.status))
              .fontSize(12)
              .fontColor(this.getStatusColor(item.status))
              .margin({ left: 8 })
          }
          .margin({ top: 8 })
        }
        .layoutWeight(1)
      }
      .width('100%')
      .padding(16)
    }
    .width('100%')
    .margin({ bottom: 12 })
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(12)
    .shadow({ radius: 4, color: $r('app.color.card_shadow'), offsetX: 0, offsetY: 2 })
    .onClick(() => {
      router.pushUrl({
        url: 'pages/TransactionDetailPage',
        params: { id: item.id.toString() }
      });
    })
  }

  getTypeIcon(type: TransactionType): string {
    switch(type) {
      case TransactionType.TRANSFER: return '💸';
      case TransactionType.DEPOSIT: return '💰';
      case TransactionType.WITHDRAW: return '💳';
      case TransactionType.CONSUME: return '🛒';
      default: return '📊';
    }
  }

  getTypeName(type: TransactionType): string {
    switch(type) {
      case TransactionType.TRANSFER: return '转账';
      case TransactionType.DEPOSIT: return '充值';
      case TransactionType.WITHDRAW: return '提现';
      case TransactionType.CONSUME: return '消费';
      default: return '交易';
    }
  }

  getTypeColor(type: TransactionType): string {
    switch(type) {
      case TransactionType.TRANSFER: return $r('app.color.primary');
      case TransactionType.DEPOSIT: return $r('app.color.success');
      case TransactionType.WITHDRAW: return $r('app.color.warning');
      case TransactionType.CONSUME: return $r('app.color.primary_dark');
      default: return $r('app.color.text_secondary');
    }
  }

  getStatusName(status: TransactionStatus): string {
    switch(status) {
      case TransactionStatus.COMPLETED: return '已完成';
      case TransactionStatus.PROCESSING: return '处理中';
      case TransactionStatus.FAILED: return '失败';
      case TransactionStatus.CANCELED: return '已取消';
      default: return '未知状态';
    }
  }

  getStatusColor(status: TransactionStatus): string {
    switch(status) {
      case TransactionStatus.COMPLETED: return $r('app.color.success');
      case TransactionStatus.PROCESSING: return $r('app.color.warning');
      case TransactionStatus.FAILED: return $r('app.color.error');
      case TransactionStatus.CANCELED: return $r('app.color.text_hint');
      default: return $r('app.color.text_secondary');
    }
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)

      Text('加载中...')
        .fontSize(14)
        .fontColor($r('app.color.text_secondary'))
        .margin({ top: 16 })
    }
    .width('100%')
    .height(200)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  EmptyView() {
    Column() {
      Image($r('app.media.ic_transaction'))
        .width(120)
        .height(120)
        .margin({ bottom: 16 })

      Text('暂无交易记录')
        .fontSize(16)
        .fontColor($r('app.color.text_hint'))
    }
    .width('100%')
    .height(200)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  BottomNavigation() {
    Row() {
      Column() {
        Text('💳').fontSize(20)
        Text('银行卡').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }))

      Column() {
        Text('📊').fontSize(20)
        Text('交易').fontSize(12).fontColor($r('app.color.primary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })

      Column() {
        Text('👛').fontSize(20)
        Text('钱包').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/WalletPage' }))

      Column() {
        Text('💰').fontSize(20)
        Text('支付中心').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }))

      Column() {
        Text('👤').fontSize(20)
        Text('我的').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/SettingsPage' }))
    }
    .width('100%').height(60).backgroundColor($r('app.color.card_background'))
    .border({ width: { top: 1 }, color: $r('app.color.divider') })
  }
}