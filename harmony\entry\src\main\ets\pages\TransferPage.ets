import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import {
  Transaction,
  TransactionType,
  TransactionStatus,
  TransactionQueryParams,
  PaymentMethod,
  PageResult
} from '../common/types/index';

@Entry
@Component
struct TransferPage {
  @State transactions: Transaction[] = [];
  @State isLoading: boolean = true;

  aboutToAppear() {
    this.loadTransactions();
  }

  async loadTransactions() {
    try {
      this.isLoading = true;
      const userId = 1; // 临时使用固定用户ID
      const transactions = await TransactionApi.getTransactionList(userId);
      this.transactions = transactions;
    } catch (error) {
      console.error('加载交易记录失败:', error);
      promptAction.showToast({ message: '加载交易记录失败' });
      this.transactions = this.getTestData();
    } finally {
      this.isLoading = false;
    }
  }

  getTestData(): Transaction[] {
    return [
      {
        transactionId: 1,
        transactionNo: 'T001',
        fromUserId: 1,
        toUserId: 2,
        amount: 500.00,
        transactionType: TransactionType.TRANSFER,
        paymentMethod: PaymentMethod.BANK_CARD,
        description: '转账给李四',
        status: TransactionStatus.SUCCESS,
        createTime: '2023-05-15 14:30'
      },
      {
        transactionId: 2,
        transactionNo: 'T002',
        fromUserId: 1,
        toUserId: 1,
        amount: 1000.00,
        transactionType: TransactionType.RECHARGE,
        paymentMethod: PaymentMethod.BANK_CARD,
        description: '银行卡充值',
        status: TransactionStatus.SUCCESS,
        createTime: '2023-05-14 09:15'
      },
      {
        transactionId: 3,
        transactionNo: 'T003',
        fromUserId: 1,
        toUserId: 3,
        amount: 128.50,
        transactionType: TransactionType.PAYMENT,
        paymentMethod: PaymentMethod.WALLET,
        description: '星巴克咖啡',
        status: TransactionStatus.SUCCESS,
        createTime: '2023-05-12 18:45'
      },
      {
        transactionId: 4,
        transactionNo: 'T004',
        fromUserId: 1,
        toUserId: 1,
        amount: 300.00,
        transactionType: TransactionType.WITHDRAW,
        paymentMethod: PaymentMethod.BANK_CARD,
        description: '银行卡提现',
        status: TransactionStatus.PENDING,
        createTime: '2023-05-10 16:20'
      }
    ] as Transaction[];
  }

  build() {
    Column() {
      // 顶部导航栏
      Column() {
        Row() {
          Text('交易记录')
            .fontSize(20)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
        }
        .width('100%')
        .height(60)
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
      }
      .width('90%')
      .margin({ top: 20 })
      .borderRadius(16)
      .backgroundColor('#ff3785f5')
      .shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 })
      if (this.isLoading) {
        this.LoadingView()
      } else if (this.transactions.length === 0) {
        this.EmptyView()
      } else {
        Scroll() {
          Column() {
            Row() {
              Text('全部交易')
                .fontSize(18)
                .fontColor($r('app.color.text_primary'))
                .fontWeight(FontWeight.Medium)

              Text(`共 ${this.transactions.length} 笔`)
                .fontSize(14)
                .fontColor($r('app.color.text_secondary'))
                .margin({ left: 8 })
            }
            .width('100%')
            .margin({ bottom: 16 })

            ForEach(this.transactions, (item: Transaction) => {
              this.TransactionItem(item)
            })
          }
          .width('100%')
          .padding(16)
        }
        .width('100%')
        .layoutWeight(1)
      }

      this.BottomNavigation()
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background'))
  }

  @Builder
  TransactionItem(item: Transaction) {
    Column() {
      Row() {
        Column() {
          Text(this.getTypeIcon(item.transactionType))
            .fontSize(24)
        }
        .width(48)
        .height(48)
        .backgroundColor(this.getTypeColor(item.transactionType) + '20')
        .borderRadius(24)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .margin({ right: 12 })

        Column() {
          Row() {
            Text(this.getTypeName(item.transactionType))
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            Text(`¥${item.amount.toFixed(2)}`)
              .fontSize(16)
              .fontColor(this.getAmountColor(item))
          }

          Text(item.description)
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
            .alignSelf(ItemAlign.Start)

          Row() {
            Text(item.createTime)
              .fontSize(12)
              .fontColor('#999999')

            Text(this.getStatusName(item.status))
              .fontSize(12)
              .fontColor(this.getStatusColor(item.status))
              .margin({ left: 8 })
          }
          .margin({ top: 8 })
        }
        .layoutWeight(1)
      }
      .width('100%')
      .padding(16)
    }
    .width('100%')
    .margin({ bottom: 12 })
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(12)
    .shadow({ radius: 4, color: $r('app.color.card_shadow'), offsetX: 0, offsetY: 2 })
    .onClick(() => {
      router.pushUrl({
        url: 'pages/TransactionDetailPage',
        params: { id: item.transactionId.toString() }
      });
    })
  }

  getTypeIcon(type: TransactionType): string {
    switch(type) {
      case TransactionType.TRANSFER: return '💸';
      case TransactionType.RECHARGE: return '💰';
      case TransactionType.WITHDRAW: return '💳';
      case TransactionType.PAYMENT: return '🛒';
      case TransactionType.RECEIVE: return '📥';
      case TransactionType.REFUND: return '↩️';
      default: return '📊';
    }
  }

  getTypeName(type: TransactionType): string {
    return type; // 枚举值本身就是中文
  }

  getTypeColor(type: TransactionType): string {
    switch(type) {
      case TransactionType.TRANSFER: return '#6366F1';
      case TransactionType.RECHARGE: return '#10B981';
      case TransactionType.WITHDRAW: return '#F59E0B';
      case TransactionType.PAYMENT: return '#4338CA';
      case TransactionType.RECEIVE: return '#059669';
      case TransactionType.REFUND: return '#7C3AED';
      default: return '#6B7280';
    }
  }

  getStatusName(status: TransactionStatus): string {
    return status; // 枚举值本身就是中文
  }

  getStatusColor(status: TransactionStatus): string {
    switch(status) {
      case TransactionStatus.SUCCESS: return '#10B981';
      case TransactionStatus.PENDING: return '#F59E0B';
      case TransactionStatus.FAILED: return '#EF4444';
      case TransactionStatus.CANCELLED: return '#9CA3AF';
      default: return '#6B7280';
    }
  }

  getAmountColor(transaction: Transaction): string {
    // 充值和收钱显示绿色，其他显示红色
    if (transaction.transactionType === TransactionType.RECHARGE ||
        transaction.transactionType === TransactionType.RECEIVE) {
      return '#10B981';
    }
    return '#EF4444';
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)

      Text('加载中...')
        .fontSize(14)
        .fontColor($r('app.color.text_secondary'))
        .margin({ top: 16 })
    }
    .width('100%')
    .height(200)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  EmptyView() {
    Column() {
      Image($r('app.media.ic_transaction'))
        .width(120)
        .height(120)
        .margin({ bottom: 16 })

      Text('暂无交易记录')
        .fontSize(16)
        .fontColor($r('app.color.text_hint'))
    }
    .width('100%')
    .height(200)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  BottomNavigation() {
    Row() {
      Column() {
        Text('💳').fontSize(20)
        Text('银行卡').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }))

      Column() {
        Text('📊').fontSize(20)
        Text('交易').fontSize(12).fontColor($r('app.color.primary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })

      Column() {
        Text('👛').fontSize(20)
        Text('钱包').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/WalletPage' }))

      Column() {
        Text('💰').fontSize(20)
        Text('支付中心').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }))

      Column() {
        Text('👤').fontSize(20)
        Text('我的').fontSize(12).fontColor($r('app.color.text_secondary')).margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/SettingsPage' }))
    }
    .width('100%').height(60).backgroundColor($r('app.color.card_background'))
    .border({ width: { top: 1 }, color: $r('app.color.divider') })
  }
}