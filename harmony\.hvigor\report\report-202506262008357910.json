{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "64f32274-0056-4063-b67c-c4c8c562b8bd", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061220833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a97ba22-6506-45fe-a0ce-bc6ad181ef92", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061281026300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cfdf42-2722-4bfa-bdb3-e8ba6bc84c03", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061281264000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b54afe-2063-468a-997a-2a46e5ae2e6e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504473797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7dd081-de98-4ce1-82b4-082289842a14", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504481587100, "endTime": 15504670207300}, "additional": {"children": ["2fa4cf3f-52b7-4930-8ca6-d23f350fda5d", "6b02661e-aa23-45ae-9589-77a5b7328434", "4f397edd-fa3d-49e5-bbd2-ed755803ae2a", "00f44ac7-41ed-4c79-8193-a3c4acc6b3f3", "06a07591-cd3e-4de8-949e-e95a481176b9", "e8832155-81e0-48f9-a527-fbf3f2da7e20", "363fd43b-48f2-48e1-abd5-401cb0eafb0c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fa4cf3f-52b7-4930-8ca6-d23f350fda5d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504481588600, "endTime": 15504498369800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "5ea9af51-006b-4dbc-a305-2ebaeb52e7b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b02661e-aa23-45ae-9589-77a5b7328434", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504498399900, "endTime": 15504668964800}, "additional": {"children": ["04671a2c-5269-4a15-b5b9-d18b7bf3ab71", "f7c58d43-067d-42e0-8fc3-4005ced3821b", "6996efab-ee39-4785-a34e-1f445617ee21", "639ea649-721c-42b4-8fb4-ad5ad4910aa6", "dfa72ea0-88ed-4e35-aa52-98ea55745241", "c0aa3d04-a71a-47f7-8f23-4f631a9e1012", "6771bd38-4603-4321-a70c-a448e288ba8d", "f30a59d8-31bd-41e5-8d52-c454a5e55850", "1ee58713-9367-444d-a9d2-a1002db36fbd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f397edd-fa3d-49e5-bbd2-ed755803ae2a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504668987800, "endTime": 15504670197400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "6fee1193-41b5-4713-af4c-f57a6fb127fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00f44ac7-41ed-4c79-8193-a3c4acc6b3f3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504670202500, "endTime": 15504670203600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "b39831bc-a0a5-4b25-815d-867aeb6d119c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06a07591-cd3e-4de8-949e-e95a481176b9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504487129500, "endTime": 15504487341100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "0025209a-89db-498e-9abd-c8809f02cccc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0025209a-89db-498e-9abd-c8809f02cccc", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504487129500, "endTime": 15504487341100}, "additional": {"logType": "info", "children": [], "durationId": "06a07591-cd3e-4de8-949e-e95a481176b9", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "e8832155-81e0-48f9-a527-fbf3f2da7e20", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504493990300, "endTime": 15504494011900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "9adb56aa-eb77-4cb2-afa5-57520ff699cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9adb56aa-eb77-4cb2-afa5-57520ff699cb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504493990300, "endTime": 15504494011900}, "additional": {"logType": "info", "children": [], "durationId": "e8832155-81e0-48f9-a527-fbf3f2da7e20", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "b649e647-7a99-4849-8969-2afe77c03d69", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504494061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e71b76-2cbc-4a16-a17a-d78a539e7f1a", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504498213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ea9af51-006b-4dbc-a305-2ebaeb52e7b3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504481588600, "endTime": 15504498369800}, "additional": {"logType": "info", "children": [], "durationId": "2fa4cf3f-52b7-4930-8ca6-d23f350fda5d", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "04671a2c-5269-4a15-b5b9-d18b7bf3ab71", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504505659400, "endTime": 15504505668100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "cc515a9a-2fc3-4bfc-a37e-675aa8b5f7d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7c58d43-067d-42e0-8fc3-4005ced3821b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504505682000, "endTime": 15504510163200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "9e6a2f92-b77d-4336-91a1-ec9d8f904b1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6996efab-ee39-4785-a34e-1f445617ee21", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504510181500, "endTime": 15504597118600}, "additional": {"children": ["d6b83f3b-1b8d-422c-afd7-c95f4cbe5481", "6cdb283b-2832-410d-8dd3-cf4775e45a09", "8b77b448-5b62-4ec8-b082-b9e92c15ee4b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "639ea649-721c-42b4-8fb4-ad5ad4910aa6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504597148400, "endTime": 15504621036700}, "additional": {"children": ["42348e5a-6ba6-43aa-a047-bb055dd11a9f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "6c3cebae-3389-4596-9d2f-ab2daf1238be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfa72ea0-88ed-4e35-aa52-98ea55745241", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504621527900, "endTime": 15504646499200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "45e2c66f-d678-4886-b592-22c12b9861fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0aa3d04-a71a-47f7-8f23-4f631a9e1012", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504647865500, "endTime": 15504656358500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "13914232-937b-4648-9563-ac5d90861d8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6771bd38-4603-4321-a70c-a448e288ba8d", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504656382000, "endTime": 15504668795300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "27c1cf72-85b7-4f52-a079-c94854af2595"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f30a59d8-31bd-41e5-8d52-c454a5e55850", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504668826300, "endTime": 15504668953200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "ac160816-cf21-45b7-afa3-1ee6ff2b679c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc515a9a-2fc3-4bfc-a37e-675aa8b5f7d5", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504505659400, "endTime": 15504505668100}, "additional": {"logType": "info", "children": [], "durationId": "04671a2c-5269-4a15-b5b9-d18b7bf3ab71", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "9e6a2f92-b77d-4336-91a1-ec9d8f904b1e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504505682000, "endTime": 15504510163200}, "additional": {"logType": "info", "children": [], "durationId": "f7c58d43-067d-42e0-8fc3-4005ced3821b", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "d6b83f3b-1b8d-422c-afd7-c95f4cbe5481", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504510864000, "endTime": 15504510880900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6996efab-ee39-4785-a34e-1f445617ee21", "logId": "264d6fa0-458c-4362-af36-9438d4333680"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "264d6fa0-458c-4362-af36-9438d4333680", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504510864000, "endTime": 15504510880900}, "additional": {"logType": "info", "children": [], "durationId": "d6b83f3b-1b8d-422c-afd7-c95f4cbe5481", "parent": "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3"}}, {"head": {"id": "6cdb283b-2832-410d-8dd3-cf4775e45a09", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504513887500, "endTime": 15504596182100}, "additional": {"children": ["cc2ba7bb-0536-4a17-b274-152b4760f2b4", "7a43bdb6-b03d-470a-bab1-913678c7ea23"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6996efab-ee39-4785-a34e-1f445617ee21", "logId": "6bc93c60-28a1-48f9-b02d-a33e9aa5943e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc2ba7bb-0536-4a17-b274-152b4760f2b4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504513897900, "endTime": 15504518854400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cdb283b-2832-410d-8dd3-cf4775e45a09", "logId": "d3b841df-f03c-4185-a28d-ed41a02f8f46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a43bdb6-b03d-470a-bab1-913678c7ea23", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504518877800, "endTime": 15504596167400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cdb283b-2832-410d-8dd3-cf4775e45a09", "logId": "7bfe0e42-364c-4aa0-ab0a-caa9035c8d6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1cc38d3-e2af-416a-ab47-698d0a1b13b8", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504513915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8021eb2-4b8b-41bd-84fa-58ae2334f84d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504518682900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b841df-f03c-4185-a28d-ed41a02f8f46", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504513897900, "endTime": 15504518854400}, "additional": {"logType": "info", "children": [], "durationId": "cc2ba7bb-0536-4a17-b274-152b4760f2b4", "parent": "6bc93c60-28a1-48f9-b02d-a33e9aa5943e"}}, {"head": {"id": "ec50da94-e7b7-4cb2-b589-851798441ee2", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504518898800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431eccc8-43d9-4ce4-a8dc-20034e6039e3", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504525673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772bbe7f-a55a-4e11-b8da-c7495c2b17a0", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504525815000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf0e7bd-fc57-4e16-a9b2-f0f1037cf868", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504525955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f4eb47-4e89-4b08-999f-58dde738343f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504526065000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab04b95-4ef1-49fe-8af0-6771815ad466", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504528603300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ef2e8f-1ecf-4df6-8ddb-d7b91db456c0", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504532352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3283be80-a2a3-4cd0-84dc-a23caa39f358", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504542897400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86628f6-da8f-4574-82cf-32cdc7db71f7", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504570998300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1682e43-9680-4b27-9b7c-22a3c49dd354", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504571138800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 8}, "markType": "other"}}, {"head": {"id": "883b9e57-887a-44f6-9ba0-053c3f6d9344", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504571151900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 8}, "markType": "other"}}, {"head": {"id": "82772c93-9167-42d9-9607-84fb08ef3fd5", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504595845800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46f08fbd-950e-4cc7-87e1-daefecfbe62e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504595963200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e6f34bb-f96b-47ff-b591-b6b6ef354cb4", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504596047900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3abac52c-fcf7-4c9e-b605-b88d37d2bbec", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504596110700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bfe0e42-364c-4aa0-ab0a-caa9035c8d6f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504518877800, "endTime": 15504596167400}, "additional": {"logType": "info", "children": [], "durationId": "7a43bdb6-b03d-470a-bab1-913678c7ea23", "parent": "6bc93c60-28a1-48f9-b02d-a33e9aa5943e"}}, {"head": {"id": "6bc93c60-28a1-48f9-b02d-a33e9aa5943e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504513887500, "endTime": 15504596182100}, "additional": {"logType": "info", "children": ["d3b841df-f03c-4185-a28d-ed41a02f8f46", "7bfe0e42-364c-4aa0-ab0a-caa9035c8d6f"], "durationId": "6cdb283b-2832-410d-8dd3-cf4775e45a09", "parent": "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3"}}, {"head": {"id": "8b77b448-5b62-4ec8-b082-b9e92c15ee4b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504596936000, "endTime": 15504596950700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6996efab-ee39-4785-a34e-1f445617ee21", "logId": "1534f60b-bf82-4ea4-9294-5ac7c0896f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1534f60b-bf82-4ea4-9294-5ac7c0896f54", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504596936000, "endTime": 15504596950700}, "additional": {"logType": "info", "children": [], "durationId": "8b77b448-5b62-4ec8-b082-b9e92c15ee4b", "parent": "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3"}}, {"head": {"id": "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504510181500, "endTime": 15504597118600}, "additional": {"logType": "info", "children": ["264d6fa0-458c-4362-af36-9438d4333680", "6bc93c60-28a1-48f9-b02d-a33e9aa5943e", "1534f60b-bf82-4ea4-9294-5ac7c0896f54"], "durationId": "6996efab-ee39-4785-a34e-1f445617ee21", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "42348e5a-6ba6-43aa-a047-bb055dd11a9f", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504597959400, "endTime": 15504621020800}, "additional": {"children": ["fbb85a7a-bf11-49de-a3df-052662f0db0f", "1dac4ec2-740c-4f30-9be3-4ca9b6c21cfa", "d67ec0ee-c4a8-4954-846e-6452a4d15ae8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "639ea649-721c-42b4-8fb4-ad5ad4910aa6", "logId": "618dee3c-1a8b-4334-948c-372a97c27886"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbb85a7a-bf11-49de-a3df-052662f0db0f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504602333100, "endTime": 15504602354600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42348e5a-6ba6-43aa-a047-bb055dd11a9f", "logId": "b36d8fd1-39c8-48eb-b06a-ff4b89fecf50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b36d8fd1-39c8-48eb-b06a-ff4b89fecf50", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504602333100, "endTime": 15504602354600}, "additional": {"logType": "info", "children": [], "durationId": "fbb85a7a-bf11-49de-a3df-052662f0db0f", "parent": "618dee3c-1a8b-4334-948c-372a97c27886"}}, {"head": {"id": "1dac4ec2-740c-4f30-9be3-4ca9b6c21cfa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504605353600, "endTime": 15504619550200}, "additional": {"children": ["e93e5156-1b57-4b09-b4c5-b71d5807358d", "a7f0c435-e2a3-43ac-bd41-f572e9e264cf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42348e5a-6ba6-43aa-a047-bb055dd11a9f", "logId": "dd3f3f0c-213e-481e-856d-d3b9d334667f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e93e5156-1b57-4b09-b4c5-b71d5807358d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504605355100, "endTime": 15504607971200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dac4ec2-740c-4f30-9be3-4ca9b6c21cfa", "logId": "f681a9b0-7158-47d7-947d-1a386d59a5f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7f0c435-e2a3-43ac-bd41-f572e9e264cf", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504607988900, "endTime": 15504619537100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dac4ec2-740c-4f30-9be3-4ca9b6c21cfa", "logId": "ef222a16-5126-418c-8ec2-e7fa60b935c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ebcbe9c-6c2e-4daf-8ecc-43cdca71b9c4", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504605360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cea203d3-8ab8-4c12-9b24-671a4447a2e8", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504607851200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f681a9b0-7158-47d7-947d-1a386d59a5f8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504605355100, "endTime": 15504607971200}, "additional": {"logType": "info", "children": [], "durationId": "e93e5156-1b57-4b09-b4c5-b71d5807358d", "parent": "dd3f3f0c-213e-481e-856d-d3b9d334667f"}}, {"head": {"id": "28b2c910-36f6-4df1-87dd-9ff5082417de", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504608005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33ee9d1-811a-43b9-a593-6ac12d42ff73", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504614890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296d9d5e-29e5-44a8-bfd1-efd1c59f0869", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce5fc558-6ada-4fcc-af3c-6da141531ccc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecffb90-a752-4683-bc58-79b84cbe8838", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a28964ab-2ee7-4aad-9936-3c3b330601e1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ceda16-0088-458e-99eb-348676ba628b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615835400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d34f4e58-ce97-4fd1-b841-ce18eb9fcab5", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504615906600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcf056e-0c7a-4a90-8f1a-9d7f14e69201", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504619210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6209433-901e-4b52-9c7b-501ef1b97126", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504619365000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5dfe6b-f1de-4db4-9aef-5b1487650b4a", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504619435300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4be29ae2-f911-4d87-8f14-ebb695b21370", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504619488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef222a16-5126-418c-8ec2-e7fa60b935c3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504607988900, "endTime": 15504619537100}, "additional": {"logType": "info", "children": [], "durationId": "a7f0c435-e2a3-43ac-bd41-f572e9e264cf", "parent": "dd3f3f0c-213e-481e-856d-d3b9d334667f"}}, {"head": {"id": "dd3f3f0c-213e-481e-856d-d3b9d334667f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504605353600, "endTime": 15504619550200}, "additional": {"logType": "info", "children": ["f681a9b0-7158-47d7-947d-1a386d59a5f8", "ef222a16-5126-418c-8ec2-e7fa60b935c3"], "durationId": "1dac4ec2-740c-4f30-9be3-4ca9b6c21cfa", "parent": "618dee3c-1a8b-4334-948c-372a97c27886"}}, {"head": {"id": "d67ec0ee-c4a8-4954-846e-6452a4d15ae8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504620992200, "endTime": 15504621004300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42348e5a-6ba6-43aa-a047-bb055dd11a9f", "logId": "6e0fd904-bf0f-443b-9dd1-3fa6f37c77df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e0fd904-bf0f-443b-9dd1-3fa6f37c77df", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504620992200, "endTime": 15504621004300}, "additional": {"logType": "info", "children": [], "durationId": "d67ec0ee-c4a8-4954-846e-6452a4d15ae8", "parent": "618dee3c-1a8b-4334-948c-372a97c27886"}}, {"head": {"id": "618dee3c-1a8b-4334-948c-372a97c27886", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504597959400, "endTime": 15504621020800}, "additional": {"logType": "info", "children": ["b36d8fd1-39c8-48eb-b06a-ff4b89fecf50", "dd3f3f0c-213e-481e-856d-d3b9d334667f", "6e0fd904-bf0f-443b-9dd1-3fa6f37c77df"], "durationId": "42348e5a-6ba6-43aa-a047-bb055dd11a9f", "parent": "6c3cebae-3389-4596-9d2f-ab2daf1238be"}}, {"head": {"id": "6c3cebae-3389-4596-9d2f-ab2daf1238be", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504597148400, "endTime": 15504621036700}, "additional": {"logType": "info", "children": ["618dee3c-1a8b-4334-948c-372a97c27886"], "durationId": "639ea649-721c-42b4-8fb4-ad5ad4910aa6", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "0009b013-56f1-481d-a809-8c87a1b9057e", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504645621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb475848-3e37-44a1-83a0-f4d1104dedde", "name": "hvigorfile, resolve hvigorfile dependencies in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504646327700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e2c66f-d678-4886-b592-22c12b9861fb", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504621527900, "endTime": 15504646499200}, "additional": {"logType": "info", "children": [], "durationId": "dfa72ea0-88ed-4e35-aa52-98ea55745241", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "1ee58713-9367-444d-a9d2-a1002db36fbd", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504647609600, "endTime": 15504647832800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b02661e-aa23-45ae-9589-77a5b7328434", "logId": "2b388d04-ed85-4d4f-a694-c2954e61c750"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4c94c90-5d0c-431f-bdb0-fb91c2b48698", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504647637200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b388d04-ed85-4d4f-a694-c2954e61c750", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504647609600, "endTime": 15504647832800}, "additional": {"logType": "info", "children": [], "durationId": "1ee58713-9367-444d-a9d2-a1002db36fbd", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "5ca3c012-5c46-4d28-bf2d-f6fe794efb92", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504649746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002eefe1-233c-4750-a7cc-94425c9e87b5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504655422100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13914232-937b-4648-9563-ac5d90861d8d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504647865500, "endTime": 15504656358500}, "additional": {"logType": "info", "children": [], "durationId": "c0aa3d04-a71a-47f7-8f23-4f631a9e1012", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "f699646e-fcdb-4986-8ce2-800c734366b4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504656393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336d2da0-8810-4deb-a4e7-73ca19ce9369", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504661981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2fbc13a-11f0-42a3-8513-31cbe0741fc2", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504662084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c23eb1e-bc66-495c-aa9d-04ef52dc84c2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504662296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab48184-9a41-4fa1-b1a9-b3a940e51bd7", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504664679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0aceea-8ef7-4c95-b4a2-d911130313ea", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504664797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c1cf72-85b7-4f52-a079-c94854af2595", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504656382000, "endTime": 15504668795300}, "additional": {"logType": "info", "children": [], "durationId": "6771bd38-4603-4321-a70c-a448e288ba8d", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "674b729e-94a7-410e-8341-3c54d8236f25", "name": "Configuration phase cost:164 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504668850400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac160816-cf21-45b7-afa3-1ee6ff2b679c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504668826300, "endTime": 15504668953200}, "additional": {"logType": "info", "children": [], "durationId": "f30a59d8-31bd-41e5-8d52-c454a5e55850", "parent": "04d46af5-ba5a-4004-9516-7fcf8925b47c"}}, {"head": {"id": "04d46af5-ba5a-4004-9516-7fcf8925b47c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504498399900, "endTime": 15504668964800}, "additional": {"logType": "info", "children": ["cc515a9a-2fc3-4bfc-a37e-675aa8b5f7d5", "9e6a2f92-b77d-4336-91a1-ec9d8f904b1e", "4b13d2de-aa7f-454b-8ae1-e87a9d5481c3", "6c3cebae-3389-4596-9d2f-ab2daf1238be", "45e2c66f-d678-4886-b592-22c12b9861fb", "13914232-937b-4648-9563-ac5d90861d8d", "27c1cf72-85b7-4f52-a079-c94854af2595", "ac160816-cf21-45b7-afa3-1ee6ff2b679c", "2b388d04-ed85-4d4f-a694-c2954e61c750"], "durationId": "6b02661e-aa23-45ae-9589-77a5b7328434", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "363fd43b-48f2-48e1-abd5-401cb0eafb0c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504670168300, "endTime": 15504670185800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f7dd081-de98-4ce1-82b4-082289842a14", "logId": "afd0cec2-df13-4e68-921e-f09bf1337eb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afd0cec2-df13-4e68-921e-f09bf1337eb0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504670168300, "endTime": 15504670185800}, "additional": {"logType": "info", "children": [], "durationId": "363fd43b-48f2-48e1-abd5-401cb0eafb0c", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "6fee1193-41b5-4713-af4c-f57a6fb127fb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504668987800, "endTime": 15504670197400}, "additional": {"logType": "info", "children": [], "durationId": "4f397edd-fa3d-49e5-bbd2-ed755803ae2a", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "b39831bc-a0a5-4b25-815d-867aeb6d119c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504670202500, "endTime": 15504670203600}, "additional": {"logType": "info", "children": [], "durationId": "00f44ac7-41ed-4c79-8193-a3c4acc6b3f3", "parent": "156ff979-9638-4bee-91e9-8bfedd5d0df1"}}, {"head": {"id": "156ff979-9638-4bee-91e9-8bfedd5d0df1", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504481587100, "endTime": 15504670207300}, "additional": {"logType": "info", "children": ["5ea9af51-006b-4dbc-a305-2ebaeb52e7b3", "04d46af5-ba5a-4004-9516-7fcf8925b47c", "6fee1193-41b5-4713-af4c-f57a6fb127fb", "b39831bc-a0a5-4b25-815d-867aeb6d119c", "0025209a-89db-498e-9abd-c8809f02cccc", "9adb56aa-eb77-4cb2-afa5-57520ff699cb", "afd0cec2-df13-4e68-921e-f09bf1337eb0"], "durationId": "1f7dd081-de98-4ce1-82b4-082289842a14"}}, {"head": {"id": "f8af2714-a39c-4950-b70a-c62526d1201b", "name": "Configuration task cost before running: 193 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504670331800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a6372c2-69a3-4ad2-b58d-9fa08a3b07cf", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504675753500, "endTime": 15504684809400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "932ff24d-96e5-4860-9db6-c1d9c596c202", "logId": "0e4b96d5-a4dc-4aad-a5d8-5c6636af5e90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932ff24d-96e5-4860-9db6-c1d9c596c202", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504671818400}, "additional": {"logType": "detail", "children": [], "durationId": "2a6372c2-69a3-4ad2-b58d-9fa08a3b07cf"}}, {"head": {"id": "90124194-8e42-4994-af8a-5151e749c4c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504672347700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c887b76-90e8-4556-b525-2a7ad2fe9084", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504672450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe68de6-2cb9-4274-9caf-32bd89ce12a8", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504675770600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ddf4368-4b46-4ad2-9f16-f90059d5bf9b", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504684591400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a66d8976-404e-45d2-9e65-baa322864b23", "name": "entry : default@PreBuild cost memory 0.267822265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504684723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4b96d5-a4dc-4aad-a5d8-5c6636af5e90", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504675753500, "endTime": 15504684809400}, "additional": {"logType": "info", "children": [], "durationId": "2a6372c2-69a3-4ad2-b58d-9fa08a3b07cf"}}, {"head": {"id": "4973d6c2-43c9-4989-88cc-17714ec6427a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504690043800, "endTime": 15504692281900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7146e49b-f650-44b9-b8a5-84fbd0e70df1", "logId": "6fd79f58-916b-42c8-a1db-c91fb5e7c18c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7146e49b-f650-44b9-b8a5-84fbd0e70df1", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504688660200}, "additional": {"logType": "detail", "children": [], "durationId": "4973d6c2-43c9-4989-88cc-17714ec6427a"}}, {"head": {"id": "31abded8-4139-41bc-a00c-383691a7e0b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504689177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44785ddb-2b06-45ee-b246-f3b6ab8d17d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504689282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57143071-ca04-4ad0-8db0-40ad0e108031", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504690055200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d053d9-48de-4304-ab98-6c5c6fd20c2f", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504692087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23401510-b484-482a-b419-c91abe221ae0", "name": "entry : default@MergeProfile cost memory 0.11083221435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504692205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd79f58-916b-42c8-a1db-c91fb5e7c18c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504690043800, "endTime": 15504692281900}, "additional": {"logType": "info", "children": [], "durationId": "4973d6c2-43c9-4989-88cc-17714ec6427a"}}, {"head": {"id": "5ad6286f-c7ea-495c-a7cb-f332b3b6eb81", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504695957000, "endTime": 15504699253500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "af5d0290-4905-4598-8758-a4548d636a9c", "logId": "904b8b97-7003-42bc-b81e-7bb6254d28c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af5d0290-4905-4598-8758-a4548d636a9c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504694160500}, "additional": {"logType": "detail", "children": [], "durationId": "5ad6286f-c7ea-495c-a7cb-f332b3b6eb81"}}, {"head": {"id": "c5cc80a1-a25c-4c41-a0f6-078ad8959451", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504694695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e056fb01-9b11-4d9d-b63e-656e0771ca40", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504694827700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a785c959-0af4-4966-89fc-f5341efd4bc1", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504695969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bea549b-1e22-4bf6-8d00-1835bf98380d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504697180800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f55762-c0a3-4787-878c-e59eb748f346", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504698971700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1adca0f-5cf0-4e4b-815d-b5fc07c11742", "name": "entry : default@CreateBuildProfile cost memory 0.09748077392578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504699142700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "904b8b97-7003-42bc-b81e-7bb6254d28c0", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504695957000, "endTime": 15504699253500}, "additional": {"logType": "info", "children": [], "durationId": "5ad6286f-c7ea-495c-a7cb-f332b3b6eb81"}}, {"head": {"id": "28f6ebbe-98fd-4166-9046-8c1d7b31089e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703609700, "endTime": 15504704181900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6d7dbce7-0851-4dc1-b25a-47fea2cc6df0", "logId": "5bdc3268-740a-49c3-bbc5-240e15bd76dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d7dbce7-0851-4dc1-b25a-47fea2cc6df0", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504701791700}, "additional": {"logType": "detail", "children": [], "durationId": "28f6ebbe-98fd-4166-9046-8c1d7b31089e"}}, {"head": {"id": "ef812f9c-3f6f-4535-afd5-e48646c0e228", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504702431300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25622e35-ba8c-4e96-b87c-288ae6a6022a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504702548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daed32ca-f7d3-48fe-b0e9-b1080dd7cf73", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703621800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f2cd9ea-7867-4e91-b66c-011284b54920", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703751700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ce2221-df1b-4f11-a7bb-29b2a7fcd1f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703880800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3530e03a-b67f-4f84-aa99-727b971f94c5", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3189e4-7c70-4c2e-8742-21c328bb9ffd", "name": "runTaskFromQueue task cost before running: 227 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504704107300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdc3268-740a-49c3-bbc5-240e15bd76dd", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504703609700, "endTime": 15504704181900, "totalTime": 471300}, "additional": {"logType": "info", "children": [], "durationId": "28f6ebbe-98fd-4166-9046-8c1d7b31089e"}}, {"head": {"id": "613082e7-4dc2-4b31-9400-a39527078867", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504716996400, "endTime": 15504718074000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "87b99814-6f5c-4886-bb29-c0f88794bda8", "logId": "a9c405b0-7355-43dc-bf70-2248f58869af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87b99814-6f5c-4886-bb29-c0f88794bda8", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504707217100}, "additional": {"logType": "detail", "children": [], "durationId": "613082e7-4dc2-4b31-9400-a39527078867"}}, {"head": {"id": "6a6b9371-cfbb-43cc-a7b8-1bbd052c8304", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504708004400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58efc819-fa2b-4ca5-b6d8-53727a6596a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504708129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "450c64df-eddd-4714-91c7-ac46bc4b2467", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504717014100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726d880e-b7b5-4b63-85ca-ad6a30d1dac1", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504717228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4525d0c8-b77f-4a96-87d8-6ad81d9f0c18", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504717905300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70005c1f-a506-44c4-914b-7607ab34cfdb", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06615447998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504718000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c405b0-7355-43dc-bf70-2248f58869af", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504716996400, "endTime": 15504718074000}, "additional": {"logType": "info", "children": [], "durationId": "613082e7-4dc2-4b31-9400-a39527078867"}}, {"head": {"id": "af0d04c0-a832-4155-ae4f-fb4ae7d27efe", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504721428100, "endTime": 15504722613000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1ed12ea0-fa82-41b7-a226-0215bfdae169", "logId": "1569398e-4737-4a90-84e6-7c14f49a5597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ed12ea0-fa82-41b7-a226-0215bfdae169", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504719668000}, "additional": {"logType": "detail", "children": [], "durationId": "af0d04c0-a832-4155-ae4f-fb4ae7d27efe"}}, {"head": {"id": "4829fe90-4447-4ce5-8d26-38a7aa382d1d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504720168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a859f7e4-bcb7-4db0-a5de-9a2a46f60df7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504720250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8026bf1f-bac6-4b64-9f8a-8b455acbd899", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504721437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf351b5d-2ba5-42ef-b70f-4d11f908dc64", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504722454200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43fa7996-f068-4409-811e-1a7f7b06797f", "name": "entry : default@ProcessProfile cost memory 0.056365966796875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504722541000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1569398e-4737-4a90-84e6-7c14f49a5597", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504721428100, "endTime": 15504722613000}, "additional": {"logType": "info", "children": [], "durationId": "af0d04c0-a832-4155-ae4f-fb4ae7d27efe"}}, {"head": {"id": "1b002ff9-d714-4069-b3eb-892d55635f2d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504726543000, "endTime": 15504734016600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a86c69af-0314-4395-b4b2-b7a0f340c66b", "logId": "d802c535-698a-431c-ad0e-a4d0ee0ddd48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a86c69af-0314-4395-b4b2-b7a0f340c66b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504724128600}, "additional": {"logType": "detail", "children": [], "durationId": "1b002ff9-d714-4069-b3eb-892d55635f2d"}}, {"head": {"id": "3331ae59-f314-43c6-9eea-2157743ee99d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504724618500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d11de2-8ab6-4ec6-860c-e771b0fc9866", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504724706800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30240625-4547-496a-8731-76e4eea12f57", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504726553100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91147658-f682-41bf-9310-348eac657d77", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504733803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec79723f-f118-498e-a7e0-2c9f979eb095", "name": "entry : default@ProcessRouterMap cost memory 0.18846893310546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504733940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d802c535-698a-431c-ad0e-a4d0ee0ddd48", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504726543000, "endTime": 15504734016600}, "additional": {"logType": "info", "children": [], "durationId": "1b002ff9-d714-4069-b3eb-892d55635f2d"}}, {"head": {"id": "21bdc1c3-4eb2-4b01-a68a-69371605d860", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504742885500, "endTime": 15504746164000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ad91dbe2-02d8-4770-98f3-538326f0572f", "logId": "fcc33773-1b31-44d5-94ff-c49aa8771990"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad91dbe2-02d8-4770-98f3-538326f0572f", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504737144000}, "additional": {"logType": "detail", "children": [], "durationId": "21bdc1c3-4eb2-4b01-a68a-69371605d860"}}, {"head": {"id": "c3a12ae2-c355-4059-a8cf-eaa9c22a4b86", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504737917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d659f1b-1924-4645-bbd1-513bb22f4578", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504738063900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e8c94a-8278-471d-959d-2f67aacb2039", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504739536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d61295-a0ab-4373-affe-f0fd6364e64b", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504744544000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3558d34f-c2f9-4546-8404-076896dd4606", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504744688900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bfb00b-de36-40fd-97a6-f16ad582eb2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504744751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6aeb203-f54a-4cdb-970d-791247b78368", "name": "entry : default@PreviewProcessResource cost memory 0.0685272216796875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504744832600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c5ec5b-2a6f-41c5-94e7-1e11701830ed", "name": "runTaskFromQueue task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504746080800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc33773-1b31-44d5-94ff-c49aa8771990", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504742885500, "endTime": 15504746164000, "totalTime": 2015300}, "additional": {"logType": "info", "children": [], "durationId": "21bdc1c3-4eb2-4b01-a68a-69371605d860"}}, {"head": {"id": "e0916c53-dcbf-4feb-a0fc-c57b54995343", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504753987000, "endTime": 15504776310400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "780c24a9-7f05-4f69-908b-19528333e5bd", "logId": "44e23ba3-aa47-42ec-b219-0f8eaade4de0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "780c24a9-7f05-4f69-908b-19528333e5bd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504749145700}, "additional": {"logType": "detail", "children": [], "durationId": "e0916c53-dcbf-4feb-a0fc-c57b54995343"}}, {"head": {"id": "3552f8da-c3e4-4799-8714-39a8d45ea465", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504750244200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8939caa5-8fe8-4c99-9ea2-29cdbb9fdd1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504750353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22188440-1844-4dc3-b4d8-348a6d64042e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504753999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde53918-52dd-4528-8683-90c08ce1ed5e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504776093000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bfca27-7d19-4d6b-92cc-47abeb5021f2", "name": "entry : default@GenerateLoaderJson cost memory 0.7083663940429688", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504776232400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e23ba3-aa47-42ec-b219-0f8eaade4de0", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504753987000, "endTime": 15504776310400}, "additional": {"logType": "info", "children": [], "durationId": "e0916c53-dcbf-4feb-a0fc-c57b54995343"}}, {"head": {"id": "dd29451b-b2df-4c7b-be91-d64b35bd70c9", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504788696300, "endTime": 15504813542200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7b734e1d-acc7-4eff-9d28-92a393e65db5", "logId": "53fa6ea0-bea2-4348-b693-7887f670db20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b734e1d-acc7-4eff-9d28-92a393e65db5", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504784745600}, "additional": {"logType": "detail", "children": [], "durationId": "dd29451b-b2df-4c7b-be91-d64b35bd70c9"}}, {"head": {"id": "e17d36c0-4cfa-4398-bc7a-f03d27931473", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504785263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86bb04db-7bfb-43a3-9d75-7bc120c11682", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504785358600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfeeddaa-ca79-485d-9ba5-e3b7519d462a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504786320800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f6014f-37cd-4989-856f-6797bdd53a3e", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504788721500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe912cb6-37d3-4683-b05b-ccf2ace84c5f", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504813274400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30091a4b-10d5-4e17-869c-1fea89764480", "name": "entry : default@PreviewCompileResource cost memory -1.046051025390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504813421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53fa6ea0-bea2-4348-b693-7887f670db20", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504788696300, "endTime": 15504813542200}, "additional": {"logType": "info", "children": [], "durationId": "dd29451b-b2df-4c7b-be91-d64b35bd70c9"}}, {"head": {"id": "6251ca3f-dcce-4838-a01d-d95df1cca64c", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816903700, "endTime": 15504817263700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7ed7d3cc-ab28-4827-b9ea-30c875d8386d", "logId": "ae2312a3-d261-49a2-aa54-bfc9f7aa0f75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ed7d3cc-ab28-4827-b9ea-30c875d8386d", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816192200}, "additional": {"logType": "detail", "children": [], "durationId": "6251ca3f-dcce-4838-a01d-d95df1cca64c"}}, {"head": {"id": "9824ee88-d17a-49c7-a4e8-53c548d83e2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816711600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c32ef72-ccee-4823-8079-5b5cad78c099", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78196fe6-8aec-4d07-ac23-c7e6fc930d0d", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816912100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd740e4-dd01-4286-89f6-48eaf76a4c9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a89748-b683-4a10-a066-a793a1094af3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504817049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48da584b-c34a-4182-82c6-3eec3d456ddc", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504817116900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4138757-064c-4201-94e8-48af9421a1ca", "name": "runTaskFromQueue task cost before running: 340 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504817208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2312a3-d261-49a2-aa54-bfc9f7aa0f75", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504816903700, "endTime": 15504817263700, "totalTime": 283900}, "additional": {"logType": "info", "children": [], "durationId": "6251ca3f-dcce-4838-a01d-d95df1cca64c"}}, {"head": {"id": "9ece7711-bfd7-4707-99f0-0e700323786d", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504820079700, "endTime": 15504822627800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "6eba9b7c-ca09-45ff-af83-350d8e788492", "logId": "16c4cbff-7917-4494-b56f-df5fe3c5c5b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eba9b7c-ca09-45ff-af83-350d8e788492", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504818846500}, "additional": {"logType": "detail", "children": [], "durationId": "9ece7711-bfd7-4707-99f0-0e700323786d"}}, {"head": {"id": "09a07a16-4e0c-482d-8b67-f96dd8ddb22d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504819328900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95257e8f-62e6-4a98-a090-dcf5925bf40e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504819416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4eff24-e46b-430b-9bec-d1b9c5307a10", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504820089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a189c5d7-1df0-4c47-921b-9091e66f81d1", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504822420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995846a0-4593-42e2-81ac-d059ca4575dc", "name": "entry : default@CopyPreviewProfile cost memory 0.09494781494140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504822549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c4cbff-7917-4494-b56f-df5fe3c5c5b2", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504820079700, "endTime": 15504822627800}, "additional": {"logType": "info", "children": [], "durationId": "9ece7711-bfd7-4707-99f0-0e700323786d"}}, {"head": {"id": "33c2043c-a211-4302-97db-c3843d230556", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504825942900, "endTime": 15504826506600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "5805e539-81c8-4ddb-ab33-feb7cf15b79f", "logId": "a66c3691-3b75-4154-9b1d-558248a5281f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5805e539-81c8-4ddb-ab33-feb7cf15b79f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504824442200}, "additional": {"logType": "detail", "children": [], "durationId": "33c2043c-a211-4302-97db-c3843d230556"}}, {"head": {"id": "30120c5f-5cd0-44e5-8ec3-7a3180990691", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504824972600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78664eb-0511-4e15-991d-6968933c8666", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504825079600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00aecb8c-8898-4a59-bf0b-0cfbd284eb8c", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504825954500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2d15b7-ab4b-48ea-979d-9d47179b457c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504826072200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "286da9d3-048e-4357-8bd1-8bea0b0c6401", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504826142100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c296ace4-baf5-4572-9238-f0ae5e462e8c", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504826337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33284063-26a9-4d29-9f20-2d372eb79f6e", "name": "runTaskFromQueue task cost before running: 349 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504826443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a66c3691-3b75-4154-9b1d-558248a5281f", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504825942900, "endTime": 15504826506600, "totalTime": 476900}, "additional": {"logType": "info", "children": [], "durationId": "33c2043c-a211-4302-97db-c3843d230556"}}, {"head": {"id": "ec5dee10-be9c-4af0-8165-b70f8ac26f17", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828252800, "endTime": 15504828566100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "245a6141-62bb-46cd-acc6-589a66f414af", "logId": "20d04cf9-0dab-47c8-902e-3640cc42bcfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "245a6141-62bb-46cd-acc6-589a66f414af", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828196500}, "additional": {"logType": "detail", "children": [], "durationId": "ec5dee10-be9c-4af0-8165-b70f8ac26f17"}}, {"head": {"id": "5fcba8f1-2404-4dea-8b55-3b6cea8d00f1", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828260200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d2db2c-8c38-48f7-af71-24decbfe71aa", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828397700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1cbb24d-50f4-4b75-ad39-b5cde6c873a9", "name": "runTaskFromQueue task cost before running: 351 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d04cf9-0dab-47c8-902e-3640cc42bcfb", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504828252800, "endTime": 15504828566100, "totalTime": 228700}, "additional": {"logType": "info", "children": [], "durationId": "ec5dee10-be9c-4af0-8165-b70f8ac26f17"}}, {"head": {"id": "28ea015e-bedc-4ad9-b992-1591af3e8030", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504831956200, "endTime": 15504836309300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "470a3dcc-5226-4ca7-a9bc-3eefaa9c84d5", "logId": "208669c0-595f-4b77-a8a6-92f72213af85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "470a3dcc-5226-4ca7-a9bc-3eefaa9c84d5", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504830374500}, "additional": {"logType": "detail", "children": [], "durationId": "28ea015e-bedc-4ad9-b992-1591af3e8030"}}, {"head": {"id": "8321a956-d550-4227-b8fb-1caf8d44a304", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504830944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0da86a5-765b-46c2-b3e6-49f06d4e0883", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504831042600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e7ab1a-0247-4d83-944c-0e1dfdba6f39", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504831971400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf74905a-bca7-4d5b-a3bc-033545c18c34", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504835957400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6aaef3c-7a4f-48e2-bc7b-86c32132dc8f", "name": "entry : default@PreviewUpdateAssets cost memory 0.10369873046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504836165300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "208669c0-595f-4b77-a8a6-92f72213af85", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504831956200, "endTime": 15504836309300}, "additional": {"logType": "info", "children": [], "durationId": "28ea015e-bedc-4ad9-b992-1591af3e8030"}}, {"head": {"id": "be89cf3f-6609-4dff-9fc5-908b1d42fbc5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15505612882300, "endTime": 15527783369800}, "additional": {"children": ["d694b847-5459-4bea-b4aa-acf1b26f395c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "44797840-387e-4e4d-a209-588f5de2cbf4", "logId": "c38261aa-e5aa-4114-afbd-bd8a57236435"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44797840-387e-4e4d-a209-588f5de2cbf4", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504840501600}, "additional": {"logType": "detail", "children": [], "durationId": "be89cf3f-6609-4dff-9fc5-908b1d42fbc5"}}, {"head": {"id": "d5f746ed-765a-4afb-8b15-1d9de58a4dd6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504841332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e0ba36-42ff-4226-806f-39585164bd23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504841501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0b0ffe-f144-4184-a778-09d2ec912921", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15505612902800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d694b847-5459-4bea-b4aa-acf1b26f395c", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15505635839800, "endTime": 15527780514400}, "additional": {"children": ["e4b473f8-a17c-49e3-b5fc-b2a4df4c318b", "7fa037a7-97ca-48fb-9c74-091e160f9aaf", "82134b16-365f-4896-92e4-f04737a0a581", "4397eed8-23b3-499a-b98b-6a9927eec8d7", "24e4fb36-315b-4185-b8c4-884a5409b3e9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "be89cf3f-6609-4dff-9fc5-908b1d42fbc5", "logId": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "283e0b57-017c-42d5-9730-b22886f31507", "name": "entry : default@PreviewArkTS cost memory 0.9196548461914062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15505638154500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee508f31-2c72-4120-84d1-ebe9d030799b", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15514067804800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b473f8-a17c-49e3-b5fc-b2a4df4c318b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15514068876100, "endTime": 15514068892000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d694b847-5459-4bea-b4aa-acf1b26f395c", "logId": "fd36c08d-1465-426c-9b54-99e182f86828"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd36c08d-1465-426c-9b54-99e182f86828", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15514068876100, "endTime": 15514068892000}, "additional": {"logType": "info", "children": [], "durationId": "e4b473f8-a17c-49e3-b5fc-b2a4df4c318b", "parent": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}}, {"head": {"id": "a7a700b9-4a29-4143-b80f-a0fcfae3d3b1", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527778759800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa037a7-97ca-48fb-9c74-091e160f9aaf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15527779978900, "endTime": 15527780000400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d694b847-5459-4bea-b4aa-acf1b26f395c", "logId": "ae545955-3c68-4dee-8186-cede56c24850"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae545955-3c68-4dee-8186-cede56c24850", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527779978900, "endTime": 15527780000400}, "additional": {"logType": "info", "children": [], "durationId": "7fa037a7-97ca-48fb-9c74-091e160f9aaf", "parent": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}}, {"head": {"id": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15505635839800, "endTime": 15527780514400}, "additional": {"logType": "info", "children": ["fd36c08d-1465-426c-9b54-99e182f86828", "ae545955-3c68-4dee-8186-cede56c24850", "a5238d3e-4b9a-434b-b73e-fbe3a04e4596", "329746d3-b100-4930-b1f7-effc2de2c688", "81c669f1-20ac-4cb6-be92-23eddc2b3430"], "durationId": "d694b847-5459-4bea-b4aa-acf1b26f395c", "parent": "c38261aa-e5aa-4114-afbd-bd8a57236435"}}, {"head": {"id": "82134b16-365f-4896-92e4-f04737a0a581", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15512166487900, "endTime": 15514044985100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d694b847-5459-4bea-b4aa-acf1b26f395c", "logId": "a5238d3e-4b9a-434b-b73e-fbe3a04e4596"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5238d3e-4b9a-434b-b73e-fbe3a04e4596", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15512166487900, "endTime": 15514044985100}, "additional": {"logType": "info", "children": [], "durationId": "82134b16-365f-4896-92e4-f04737a0a581", "parent": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}}, {"head": {"id": "4397eed8-23b3-499a-b98b-6a9927eec8d7", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15514045272300, "endTime": 15514045437500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d694b847-5459-4bea-b4aa-acf1b26f395c", "logId": "329746d3-b100-4930-b1f7-effc2de2c688"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "329746d3-b100-4930-b1f7-effc2de2c688", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15514045272300, "endTime": 15514045437500}, "additional": {"logType": "info", "children": [], "durationId": "4397eed8-23b3-499a-b98b-6a9927eec8d7", "parent": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}}, {"head": {"id": "24e4fb36-315b-4185-b8c4-884a5409b3e9", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker10", "startTime": 15514045589100, "endTime": 15527778881800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d694b847-5459-4bea-b4aa-acf1b26f395c", "logId": "81c669f1-20ac-4cb6-be92-23eddc2b3430"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81c669f1-20ac-4cb6-be92-23eddc2b3430", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15514045589100, "endTime": 15527778881800}, "additional": {"logType": "info", "children": [], "durationId": "24e4fb36-315b-4185-b8c4-884a5409b3e9", "parent": "3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"}}, {"head": {"id": "c38261aa-e5aa-4114-afbd-bd8a57236435", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15505612882300, "endTime": 15527783369800, "totalTime": 22170477500}, "additional": {"logType": "info", "children": ["3b1fbdbb-94f4-4600-9a46-52dfcf5a1088"], "durationId": "be89cf3f-6609-4dff-9fc5-908b1d42fbc5"}}, {"head": {"id": "b75d78ee-29d6-4c63-8d7b-702878f66d18", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788397800, "endTime": 15527788725400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4a1df9fa-8d11-464d-bee2-5ed7c985567d", "logId": "93e147ba-d26d-4ce6-9900-fca19a335252"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a1df9fa-8d11-464d-bee2-5ed7c985567d", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788338300}, "additional": {"logType": "detail", "children": [], "durationId": "b75d78ee-29d6-4c63-8d7b-702878f66d18"}}, {"head": {"id": "c8761946-8fed-4560-8154-57e5ef61f168", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781d6521-2f35-4343-9223-631149f8a0c8", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa95de57-7a4c-42fd-94da-aa93f88d33f9", "name": "runTaskFromQueue task cost before running: 23 s 311 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e147ba-d26d-4ce6-9900-fca19a335252", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527788397800, "endTime": 15527788725400, "totalTime": 235000}, "additional": {"logType": "info", "children": [], "durationId": "b75d78ee-29d6-4c63-8d7b-702878f66d18"}}, {"head": {"id": "7b225508-2d6f-4e7a-a726-38e6c746926b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527795591200, "endTime": 15527795619400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88e95a90-96a5-4a81-b810-c30281347e09", "logId": "df8b35fa-6cee-4801-a81b-250dd99ddc46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df8b35fa-6cee-4801-a81b-250dd99ddc46", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527795591200, "endTime": 15527795619400}, "additional": {"logType": "info", "children": [], "durationId": "7b225508-2d6f-4e7a-a726-38e6c746926b"}}, {"head": {"id": "ab874316-1bb0-438e-ad71-54a94dc6cd62", "name": "BUILD SUCCESSFUL in 23 s 318 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527795660600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "cc160499-e7cc-46c5-8c1c-2e018597c4c9", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15504477890000, "endTime": 15527795911400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 8}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "3fc95750-abc8-428a-94dd-d48ced553361", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796084400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfebe6c6-354e-4078-b51b-c1c14d22a8b7", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e9bd7ce-dbfd-41cd-b9b1-45883ff9661d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796255600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62bcd767-97a5-4943-9733-b5bf563711df", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2158c0-0884-4567-9b98-da557cc200f3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548df384-53b7-4975-abfb-8d7f906f0bb8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc824ff0-cc15-42c9-88d4-ed76e18f30a4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796534300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80683ab-adae-44a7-872a-71d438fec9a1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d779eb34-26b0-48f4-834c-f73062ecf8a2", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796642000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a9d5715-0747-43cc-91eb-48ebfca19264", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527796691200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bf9139-a765-4d0e-b378-cd30780c55b6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527800688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555a3e61-70a0-4ad3-aea5-fa8e305f9517", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527801766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e794f60d-8f37-466a-8301-407d8d138458", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527802168300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819ae777-07c2-4641-970f-dc77bb686bd8", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527802570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5568d9aa-b4a7-4c66-95e5-857f914a6d4a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527803613000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2986ff36-2ff7-462f-9a3c-352816e61479", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527815500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e96e7fc-d634-4f6f-a930-a9bcd5c9de15", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527824438800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99b92cc-0d59-4895-88c1-6e2395dc58ce", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527825331800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e55556-71bf-497a-90b9-44a85b7958ce", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527825906700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712c4b71-ee16-4ff5-9f43-ef305b69174e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:30 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527826353100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}