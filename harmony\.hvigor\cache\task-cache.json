{":harmony:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"82734343919a9219c848a93ce88dfb4d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"c489be8273867a50afbc86c53d938c92\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"23056761dee54835223911e6c9a1888c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.3.135\",\"_valueType\":\"string\",\"_hash\":\"1a0ee02ad70bbe8a0246b9e84bfd4cbd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":harmony:entry:default@PreBuild", "_executionId": ":harmony:entry:default@PreBuild:1750863154705", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c"}], ["F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "e28058952addff9802af04d2667e51ae"}], ["F:\\e-wallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "97b51a6d29cdc809c9b54239e4db53a2"}], ["F:\\e-wallet\\harmony\\entry\\build-profile.json5", {"fileSnapShotHashValue": "9da6945291df8ef3ba4da44e85efec71"}], ["F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "0fa30755b804d6cbee77ee403eaf52e5"}], ["F:\\e-wallet\\harmony\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "b3c28932150c2f93f31ec74602c77fd6"}], ["F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "3c7125ff64a1f5cb04652ed4b904e0a3"}], ["F:\\e-wallet\\harmony\\entry\\oh-package.json5", {"fileSnapShotHashValue": "752cb6a7b1a256aa66ddaaec6cca37cd"}], ["F:\\e-wallet\\harmony\\oh-package.json5", {"fileSnapShotHashValue": "a2b58636854e0cff5afa91c959f75e0c"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":harmony:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"artifactName\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSigned\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"relatedEntryModules\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"remoteHspMetaData\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetDeviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":harmony:entry:default@GenerateMetadata", "_executionId": ":harmony:entry:default@GenerateMetadata:1750210566874", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "7090176ec3048fba8580cdf55d1daaa5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", {"fileSnapShotHashValue": "8f140dc29188172e089e8a7d585068de"}]]}}, ":harmony:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.example.harmony\\\",\\\"vendor\\\":\\\"E-Wallet Team\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\",\\\"description\\\":\\\"$string:app_description\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"d8bf8915204eb34411c5095a6f759681\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\".preview\",\"_valueType\":\"string\",\"_hash\":\"88e9a315669657b02bc470a13f92befe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"44964f8abd318606862a2cee062554fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"e0aa1132f06f8ec686a7fcdcdb02041e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"b2877a8a5e4d85b48b0208a17da3ae75\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":harmony:entry:default@MergeProfile", "_executionId": ":harmony:entry:default@MergeProfile:1750843701314", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\AppScope\\app.json5", {"fileSnapShotHashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c"}], ["F:\\e-wallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "97b51a6d29cdc809c9b54239e4db53a2"}], ["F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "e28058952addff9802af04d2667e51ae"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d8ce18749004845350512f169dc630d2"}]]}}, ":harmony:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":harmony:entry:default@CreateBuildProfile", "_executionId": ":harmony:entry:default@CreateBuildProfile:1750843701368", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\AppScope\\app.json5", {"fileSnapShotHashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c"}], ["F:\\e-wallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "97b51a6d29cdc809c9b54239e4db53a2"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "19fe28f08dbb8d1a45bcd92dc633a693"}]]}}, ":harmony:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\",\"_hash\":\"52638b5b8d5967d85f7d558e6c0897dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\",\"_hash\":\"65eb1ae89d72758b386a93dddd5db61d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\",\"_hash\":\"6fbb0d2287cd1f34051162075393f37a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":harmony:entry:default@GeneratePkgContextInfo", "_executionId": ":harmony:entry:default@GeneratePkgContextInfo:1750748452071", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "dbddfdce2fcf8cdffa2cb3c256479126"}]]}}, ":harmony:entry:default@ProcessIntegratedHsp": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessIntegratedHsp", "_key": ":harmony:entry:default@ProcessIntegratedHsp", "_executionId": ":harmony:entry:default@ProcessIntegratedHsp:1750591011709", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appResOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.example.harmony\\\",\\\"vendor\\\":\\\"E-Wallet Team\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\",\\\"description\\\":\\\"$string:app_description\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:internet_permission_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}}],\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":harmony:entry:default@MakePackInfo", "_executionId": ":harmony:entry:default@MakePackInfo:1750214406798", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\app.json5", {"fileSnapShotHashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "7090176ec3048fba8580cdf55d1daaa5"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "2610b30be322d40e665ac40d75ee654a"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", {"fileSnapShotHashValue": "1ce8b6393e056b44bd83668e22dcc7a8"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\pack.info", {"fileSnapShotHashValue": "ab1932c5c4377e81d8c00ecae774f55a"}]]}}, ":harmony:entry:default@SyscapTransform": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@SyscapTransform", "_key": ":harmony:entry:default@SyscapTransform", "_executionId": ":harmony:entry:default@SyscapTransform:1750591011744", "_inputFiles": {"dataType": "Map", "value": [["D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe", {"fileSnapShotHashValue": "ac8dfe8c3f0133dec4f1fc35ef7cc6d8"}], ["D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define", {"fileSnapShotHashValue": "b39a35ea4b0883a45e39815d85495855"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc", {"fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"713e499a13beffe12f1dfb936f957a2a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"955f8760c6b7289b81ed107c2c4df075\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":harmony:entry:default@ProcessProfile", "_executionId": ":harmony:entry:default@ProcessProfile:1750843701422", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d8ce18749004845350512f169dc630d2"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "aa217ab8c68551b15dfa0795730e3e52"}]]}}, ":harmony:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":harmony:entry:default@ProcessRouterMap", "_executionId": ":harmony:entry:default@ProcessRouterMap:1750843701606", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\oh-package.json5", {"fileSnapShotHashValue": "752cb6a7b1a256aa66ddaaec6cca37cd"}], ["F:\\e-wallet\\harmony\\oh-package.json5", {"fileSnapShotHashValue": "a2b58636854e0cff5afa91c959f75e0c"}], ["F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "e28058952addff9802af04d2667e51ae"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "dbddfdce2fcf8cdffa2cb3c256479126"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "1a6fa0677bc5a7283410c4e5b7c35f2c"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "5c909708794ec0ea7aaa5f369cc62746"}]]}}, ":harmony:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\opt-compression.json\",\"_value\":\"{\\\"context\\\":{\\\"extensionPath\\\":\\\"D:\\\\\\\\Huawei\\\\\\\\DevEco Studio\\\\\\\\sdk\\\\\\\\default\\\\\\\\hms\\\\\\\\toolchains\\\\\\\\lib\\\\\\\\libimage_transcoder_shared.dll\\\"},\\\"compression\\\":{\\\"media\\\":{\\\"enable\\\":false},\\\"filters\\\":[]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"packageName\\\":\\\"com.example.harmony\\\",\\\"output\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"moduleNames\\\":\\\"entry\\\",\\\"ResourceTable\\\":[\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResources\\\":[\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"dependencies\\\":[],\\\"iconCheck\\\":true,\\\"ids\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"definedIds\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\Wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\",\\\"definedSysIds\\\":\\\"D:\\\\\\\\Huawei\\\\\\\\DevEco Studio\\\\\\\\sdk\\\\\\\\default\\\\\\\\hms\\\\\\\\toolchains\\\\\\\\id_defined.json\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resource_str\",\"_value\":false,\"_valueType\":\"boolean\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":harmony:entry:default@ProcessResource", "_executionId": ":harmony:entry:default@ProcessResource:1750214407054", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "56b7e96c5dae23843286e0d05e30b17d"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", {"isDirectory": false, "fileSnapShotHashValue": "********************************"}]]}}, ":harmony:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"F:\\\\e-wallet\\\\harmony\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"c9c5747215cfaa8094d6b614cf0122b0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"4fcd6b9f00d320ada5c4670dd343b8f0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"974757f304b5bfd1c1454ea7a38cd0d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"abd67ea7a4369f2ecaa1d7eefbe62421\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_valueType\":\"undefined\",\"_hash\":\"4620e35a57f3f6f55564cea6f6128e50\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"F:\\\\e-wallet\\\\harmony\",\"_valueType\":\"string\",\"_hash\":\"70cead8f1baac00df262858fecda5e01\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":harmony:entry:default@GenerateLoaderJson", "_executionId": ":harmony:entry:default@GenerateLoaderJson:1750843701671", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "3c7125ff64a1f5cb04652ed4b904e0a3"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "dbddfdce2fcf8cdffa2cb3c256479126"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "1a6fa0677bc5a7283410c4e5b7c35f2c"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "bc7b67707dd5770d834c21831afbc2dd"}]]}}, ":harmony:entry:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessLibs", "_key": ":harmony:entry:default@ProcessLibs", "_executionId": ":harmony:entry:default@ProcessLibs:1750209915176", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\build-profile.json5", {"fileSnapShotHashValue": "2610b30be322d40e665ac40d75ee654a"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build-profile.json5", {"fileSnapShotHashValue": "3f5b9f9d6a98660c37307ee634fe1eb7"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"D:\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe,-l,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":harmony:entry:default@CompileResource", "_executionId": ":harmony:entry:default@CompileResource:1750339246873", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "8d846a6faa2900b9311afd87737e33ed"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources", {"fileSnapShotHashValue": "1fb3d502133f4592d97e99fa98b24b09"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "c33aeeff8408c5f00531dfbfda7f1082"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "56b7e96c5dae23843286e0d05e30b17d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "7623d585cdbb7e1268b06db16dd39808"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "393cd803dd9c52d4aba73fe86ac69ddc"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "5ab46794d8a0b98e0b9f1378cdd68641"}]]}}, ":harmony:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":harmony:entry:default@CompileArkTS", "_executionId": ":harmony:entry:default@CompileArkTS:1750411575563", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "a15891a7280ce43fb61efcd2351f40a1"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "cf4fe6ac71a701e9bba77f4ea7cbbdbc"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "555a7f8a01c9772bf4c385970303b9cf"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "e1a09519f0ea6d6dd19af7d6ee26adca"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "3603cb9eb67c86baff6a2f681cee76e2"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7d1ccda73c6d9f519a5a0712072f7f0e"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "10b0015f894c6dcd11eb9b672be4026b"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "3c7125ff64a1f5cb04652ed4b904e0a3"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": "91c7d7ef10cc9d6c59e4e812e47b4806"}]]}}, ":harmony:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":harmony:entry:default@BuildJS", "_executionId": ":harmony:entry:default@BuildJS:1750591012105", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "a15891a7280ce43fb61efcd2351f40a1"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "cf4fe6ac71a701e9bba77f4ea7cbbdbc"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "555a7f8a01c9772bf4c385970303b9cf"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "e1a09519f0ea6d6dd19af7d6ee26adca"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7d1ccda73c6d9f519a5a0712072f7f0e"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@DoNativeStrip", "_key": ":harmony:entry:default@DoNativeStrip", "_executionId": ":harmony:entry:default@DoNativeStrip:1750209917513", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CacheNativeLibs", "_key": ":harmony:entry:default@CacheNativeLibs", "_executionId": ":harmony:entry:default@CacheNativeLibs:1750209917557", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "8fdedd36f9f20d01ed3e6c8aed9cdc5a"}]]}}, ":harmony:entry:default@GeneratePkgModuleJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GeneratePkgModuleJson", "_key": ":harmony:entry:default@GeneratePkgModuleJson", "_executionId": ":harmony:entry:default@GeneratePkgModuleJson:1750339252749", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "5158e6239cb92ff7efb0db9ae76ddca0"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\package\\default\\module.json", {"fileSnapShotHashValue": "5158e6239cb92ff7efb0db9ae76ddca0"}]]}}, ":harmony:entry:default@PackageHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"java,-Dfile.encoding=GBK,-jar,D:\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar,--mode,hap,--force,true,--lib-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default,--json-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json,--resources-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources,--index-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index,--pack-info-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info,--out-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap,--ets-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets,--pkg-context-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Wallet\\\\harmony\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"bundleType\",\"_value\":\"app\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hotReload\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceMapDir\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PackageHap", "_key": ":harmony:entry:default@PackageHap", "_executionId": ":harmony:entry:default@PackageHap:1750411579222", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": false, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "5158e6239cb92ff7efb0db9ae76ddca0"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources", {"isDirectory": false, "fileSnapShotHashValue": "a9e74228925f88829e27333c4020c70f"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources.index", {"isDirectory": false, "fileSnapShotHashValue": "19ede55d1705bdf56dfa47c111014234"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\pack.info", {"isDirectory": false, "fileSnapShotHashValue": "ab1932c5c4377e81d8c00ecae774f55a"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": false, "fileSnapShotHashValue": "91c7d7ef10cc9d6c59e4e812e47b4806"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "7d1ccda73c6d9f519a5a0712072f7f0e"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"isDirectory": false, "fileSnapShotHashValue": "43facd534529c7742d32faedbba523e3"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "10b0015f894c6dcd11eb9b672be4026b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "5f95c814e52f7be24072b631271fa481"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map", {"fileSnapShotHashValue": "43facd534529c7742d32faedbba523e3"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map", {"fileSnapShotHashValue": "43facd534529c7742d32faedbba523e3"}]]}}, ":harmony:entry:default@SignHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.3.135\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@SignHap", "_key": ":harmony:entry:default@SignHap", "_executionId": ":harmony:entry:default@SignHap:1750591012206", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "5f95c814e52f7be24072b631271fa481"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@CollectDebugSymbol": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CollectDebugSymbol", "_key": ":harmony:entry:default@CollectDebugSymbol", "_executionId": ":harmony:entry:default@CollectDebugSymbol:1750591012220", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"fileSnapShotHashValue": "43facd534529c7742d32faedbba523e3"}], ["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\symbol", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":harmony:entry:default@PreviewCompileResource", "_executionId": ":harmony:entry:default@PreviewCompileResource:1750904598367", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "56a4984cedc2ba528d09dc76e841d08a"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d8ce18749004845350512f169dc630d2"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "29f036533cf6cf109a406defb09448a3"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "ac64a29371ea4b682637b1016313671c"}]]}}, ":harmony:entry:default@CopyPreviewProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CopyPreviewProfile", "_key": ":harmony:entry:default@CopyPreviewProfile", "_executionId": ":harmony:entry:default@CopyPreviewProfile:1750904599086", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile", {"fileSnapShotHashValue": "93886f22bf2b9435cee6bb90f06dbe86"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"fileSnapShotHashValue": "a3627da62a9d5e89a7547fc7544869ce"}]]}}, ":harmony:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewBuildConfigJson\",\"_value\":\"{\\\"deviceType\\\":\\\"phone,tablet,2in1\\\",\\\"buildMode\\\":\\\"debug\\\",\\\"note\\\":\\\"false\\\",\\\"logLevel\\\":\\\"3\\\",\\\"isPreview\\\":\\\"true\\\",\\\"checkEntry\\\":\\\"true\\\",\\\"localPropertiesPath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\local.properties\\\",\\\"Path\\\":\\\"E:\\\\\\\\hongmeng\\\\\\\\HM\\\\\\\\DevEco Studio\\\\\\\\tools\\\\\\\\node\\\\\\\\\\\",\\\"aceProfilePath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\",\\\"hapMode\\\":\\\"false\\\",\\\"img2bin\\\":\\\"true\\\",\\\"projectProfilePath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\build-profile.json5\\\",\\\"watchMode\\\":\\\"true\\\",\\\"appResource\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ResourceTable.txt\\\",\\\"aceBuildJson\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\loader\\\\\\\\default\\\\\\\\loader.json\\\",\\\"aceModuleRoot\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\ets\\\",\\\"aceSoPath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\nativeDependencies.txt\\\",\\\"cachePath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\.default\\\",\\\"aceModuleBuild\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\assets\\\\\\\\default\\\\\\\\ets\\\",\\\"aceModuleJsonPath\\\":\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"stageRouterConfig\\\":{\\\"paths\\\":[\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"F:\\\\\\\\e-wallet\\\\\\\\harmony\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\\\\\\main_pages.json\\\"],\\\"contents\\\":[\\\"{\\\\\\\"module\\\\\\\":{\\\\\\\"pages\\\\\\\":\\\\\\\"$profile:main_pages\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"entry\\\\\\\"}}\\\",\\\"{\\\\\\\"src\\\\\\\":[\\\\\\\"pages/Index\\\\\\\",\\\\\\\"pages/LoginPage\\\\\\\",\\\\\\\"pages/MyBankCardPage\\\\\\\",\\\\\\\"pages/TransferPage\\\\\\\",\\\\\\\"pages/BankCardDetailPage\\\\\\\",\\\\\\\"pages/AddBankCardPage\\\\\\\",\\\\\\\"pages/EditBankCardPage\\\\\\\",\\\\\\\"pages/RechargePage\\\\\\\",\\\\\\\"pages/WithdrawPage\\\\\\\",\\\\\\\"pages/TransactionListPage\\\\\\\",\\\\\\\"pages/SettingsPage\\\\\\\",\\\\\\\"pages/PaymentCenterPage\\\\\\\",\\\\\\\"pages/WalletPage\\\\\\\",\\\\\\\"pages/WalletPaymentPage\\\\\\\",\\\\\\\"pages/BankCardPaymentPage\\\\\\\",\\\\\\\"pages/QRCodePaymentPage\\\\\\\",\\\\\\\"pages/NFCPaymentPage\\\\\\\",\\\\\\\"pages/TestPaymentPage\\\\\\\"]}\\\"]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":harmony:entry:default@PreviewUpdateAssets", "_executionId": ":harmony:entry:default@PreviewUpdateAssets:*************", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "e4d7c7adfcc2da118cf1221d82695242"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "********************************"}]]}}, ":harmony:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":harmony:entry:default@PreviewArkTS", "_executionId": ":harmony:entry:default@PreviewArkTS:1750904599119", "_inputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "50c080ce70d893b0435a009ae981d5ed"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "3c3f603a22f8abb5b7b35f148fd1381d"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "aa217ab8c68551b15dfa0795730e3e52"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "a3627da62a9d5e89a7547fc7544869ce"}], ["F:\\e-wallet\\harmony\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "bf4398e43c61456fb1a735d3aea31e33"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "dbddfdce2fcf8cdffa2cb3c256479126"}], ["F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "19fe28f08dbb8d1a45bcd92dc633a693"}], ["F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "3c7125ff64a1f5cb04652ed4b904e0a3"}]]}, "_outputFiles": {"dataType": "Map", "value": [["F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}