{"version": "2.0", "ppid": 2480, "events": [{"head": {"id": "3ad3dff0-4972-4e4e-99fa-beaf6d2fc7dd", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 351823994200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138a6bf2-479b-4d11-9c7e-99a2ea6c4bdf", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 351968712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3923f602-b5cf-4088-a27c-c17a98e25fd2", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 351969632500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb18eae4-6347-4c93-9832-cb0c3c8e8f3e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706119021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706129815900, "endTime": 706433556300}, "additional": {"children": ["95d76a22-4497-4ac6-b5c3-919bb61f76b3", "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "3550a757-fbda-4c18-9c64-32f59b735bde", "b7aa23f5-5d93-48bb-8dd3-95f84bb8c893", "932732af-8343-4634-b012-a8c523240354", "50bcb7bd-5d1e-43c5-8b1f-437f67429f70", "bfcc0c82-b0cb-48b9-a261-b6ae75ce5fe3"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e61b7597-338e-4962-8eb0-41e586476147"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95d76a22-4497-4ac6-b5c3-919bb61f76b3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706129818200, "endTime": 706228464500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "8406ccd3-9053-4c20-bf68-bf5664b7867d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706228483200, "endTime": 706432299300}, "additional": {"children": ["e85b189a-b932-4ec2-9bc2-3b7f68045015", "85909eff-62af-4848-ac71-c6d6fc52b413", "78ac1c04-aac3-439c-9f81-18c97d3221b1", "d5ad34c3-2ed3-40d1-93ea-7cc21e1093fc", "cd70cb96-ee6a-4bfd-a282-540c7d718a0a", "0a439b08-e960-4c34-840b-08a600faace1", "31e8f42e-bf3e-4f89-b3ac-50b9106f8e49", "00573f99-8f67-4c78-8338-c2419c496308", "642836db-1dfa-4c70-bbb5-d905421b16f6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3550a757-fbda-4c18-9c64-32f59b735bde", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706432324200, "endTime": 706433538100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "5409f1f5-f7e9-4831-a1bb-e54fb83717ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7aa23f5-5d93-48bb-8dd3-95f84bb8c893", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706433544700, "endTime": 706433550900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "1e2afce6-3211-4068-b12d-e447992f728e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932732af-8343-4634-b012-a8c523240354", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706164780400, "endTime": 706164849900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "5e245420-ae59-412d-86e4-e0980a101375"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e245420-ae59-412d-86e4-e0980a101375", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706164780400, "endTime": 706164849900}, "additional": {"logType": "info", "children": [], "durationId": "932732af-8343-4634-b012-a8c523240354", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "50bcb7bd-5d1e-43c5-8b1f-437f67429f70", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706190566100, "endTime": 706190581400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "94761231-e9e6-4be3-93c2-0d9e4f368ef2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94761231-e9e6-4be3-93c2-0d9e4f368ef2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706190566100, "endTime": 706190581400}, "additional": {"logType": "info", "children": [], "durationId": "50bcb7bd-5d1e-43c5-8b1f-437f67429f70", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "a975b3ab-2ae0-4bb1-81d8-6961d119dc72", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706190639400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d27fe5ee-df37-4d2a-bc5e-936871187bc9", "name": "Cache service initialization finished in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706228330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8406ccd3-9053-4c20-bf68-bf5664b7867d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706129818200, "endTime": 706228464500}, "additional": {"logType": "info", "children": [], "durationId": "95d76a22-4497-4ac6-b5c3-919bb61f76b3", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "e85b189a-b932-4ec2-9bc2-3b7f68045015", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706234794400, "endTime": 706234806100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "a31c78d3-e07b-4b4a-b93b-9aa4ed457d5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85909eff-62af-4848-ac71-c6d6fc52b413", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706234822100, "endTime": 706241774800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "250b9122-285a-4e80-bfca-ccac48f8dd7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78ac1c04-aac3-439c-9f81-18c97d3221b1", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706241906300, "endTime": 706345165400}, "additional": {"children": ["e3dd02df-ba0f-418c-8e96-60a928aa268e", "708ec4ab-23fa-477d-8153-528176df713a", "6767fa0d-f8a7-44f6-a701-0fd80c754f74"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5ad34c3-2ed3-40d1-93ea-7cc21e1093fc", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345179800, "endTime": 706372403600}, "additional": {"children": ["35b90eff-4310-4133-8b11-8292e9d89e56"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "621b7ec9-7be1-4fd2-8dd5-7324ac488206"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd70cb96-ee6a-4bfd-a282-540c7d718a0a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706372415700, "endTime": 706408118000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "adeb7f70-f45b-478f-a848-f99a840c2b3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a439b08-e960-4c34-840b-08a600faace1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706409564500, "endTime": 706418440400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "0d28a484-6371-45a0-8f6d-9f0290e5f6da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31e8f42e-bf3e-4f89-b3ac-50b9106f8e49", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706418459600, "endTime": 706432110500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "7ab0851e-4651-4728-b660-5a1d56c56265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00573f99-8f67-4c78-8338-c2419c496308", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706432146800, "endTime": 706432285900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "21c888e7-952c-4cb3-9d50-8a2d92418a59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a31c78d3-e07b-4b4a-b93b-9aa4ed457d5e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706234794400, "endTime": 706234806100}, "additional": {"logType": "info", "children": [], "durationId": "e85b189a-b932-4ec2-9bc2-3b7f68045015", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "250b9122-285a-4e80-bfca-ccac48f8dd7c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706234822100, "endTime": 706241774800}, "additional": {"logType": "info", "children": [], "durationId": "85909eff-62af-4848-ac71-c6d6fc52b413", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "e3dd02df-ba0f-418c-8e96-60a928aa268e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706242731800, "endTime": 706242748900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ac1c04-aac3-439c-9f81-18c97d3221b1", "logId": "5bd7d168-7187-4bc3-8f27-67d7236a6d5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bd7d168-7187-4bc3-8f27-67d7236a6d5a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706242731800, "endTime": 706242748900}, "additional": {"logType": "info", "children": [], "durationId": "e3dd02df-ba0f-418c-8e96-60a928aa268e", "parent": "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e"}}, {"head": {"id": "708ec4ab-23fa-477d-8153-528176df713a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706244962200, "endTime": 706344407400}, "additional": {"children": ["d2b361a8-b21b-4263-8653-3b77208932d5", "9c52280b-7cd6-4e43-8cb9-33820c1db663"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ac1c04-aac3-439c-9f81-18c97d3221b1", "logId": "9a467009-ad4b-45d9-8a03-6c14c46261d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2b361a8-b21b-4263-8653-3b77208932d5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706244963300, "endTime": 706251219200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "708ec4ab-23fa-477d-8153-528176df713a", "logId": "a6402f1e-ba35-4478-8460-75140fc2baec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c52280b-7cd6-4e43-8cb9-33820c1db663", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706251236300, "endTime": 706344394000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "708ec4ab-23fa-477d-8153-528176df713a", "logId": "94f0bf0d-309f-43c7-bca5-e2b64c960503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a63d5fc-2a35-4fe7-8531-2ee249dfb9ab", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706244968700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9497ff93-52d0-42d5-8b71-fb0bbd93702a", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706251075800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6402f1e-ba35-4478-8460-75140fc2baec", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706244963300, "endTime": 706251219200}, "additional": {"logType": "info", "children": [], "durationId": "d2b361a8-b21b-4263-8653-3b77208932d5", "parent": "9a467009-ad4b-45d9-8a03-6c14c46261d8"}}, {"head": {"id": "90baabca-36b6-4718-bd3a-3c02c960084d", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706251252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d2d93e6-887a-4744-af0a-0814d9605975", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706260585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86e036d-7e87-4e15-a8eb-050153742c30", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706260718300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc481ea1-6712-4d8d-b5a9-bf4d1ae3dd02", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706260865600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df281246-bb29-4a0e-ada8-9a38dc002cb3", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706261086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce98ec91-1ce8-4910-a4b8-682db599f1a9", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706262869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d98d78-a30f-404b-80d4-bd9eb19839ec", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706266712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1cef29e-5298-4d82-8185-d0a1426c0c87", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706277163900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d6a004-30c2-4f9b-8190-42cb35a9a3c1", "name": "Sdk init in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706319841700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccad13a4-9850-4c82-8a75-6c49ce01dc66", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706319996000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 16, "minute": 1}, "markType": "other"}}, {"head": {"id": "afbafcb7-8fce-412b-beaf-b34b77e6f93f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706320015600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 16, "minute": 1}, "markType": "other"}}, {"head": {"id": "1a3e8e9f-797f-43f6-ae38-bc5a243c8cf1", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706344078900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b2c55a1-4ca8-4a09-b964-227809559cf5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706344211800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20abb947-63cf-4269-b879-fbc4846979d8", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706344284000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2499b0f-debb-48ce-8867-b9ea1c18c284", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706344342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f0bf0d-309f-43c7-bca5-e2b64c960503", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706251236300, "endTime": 706344394000}, "additional": {"logType": "info", "children": [], "durationId": "9c52280b-7cd6-4e43-8cb9-33820c1db663", "parent": "9a467009-ad4b-45d9-8a03-6c14c46261d8"}}, {"head": {"id": "9a467009-ad4b-45d9-8a03-6c14c46261d8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706244962200, "endTime": 706344407400}, "additional": {"logType": "info", "children": ["a6402f1e-ba35-4478-8460-75140fc2baec", "94f0bf0d-309f-43c7-bca5-e2b64c960503"], "durationId": "708ec4ab-23fa-477d-8153-528176df713a", "parent": "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e"}}, {"head": {"id": "6767fa0d-f8a7-44f6-a701-0fd80c754f74", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345138700, "endTime": 706345151500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ac1c04-aac3-439c-9f81-18c97d3221b1", "logId": "88309a8d-6f08-44f5-bbb2-c4de2f29d2f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88309a8d-6f08-44f5-bbb2-c4de2f29d2f5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345138700, "endTime": 706345151500}, "additional": {"logType": "info", "children": [], "durationId": "6767fa0d-f8a7-44f6-a701-0fd80c754f74", "parent": "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e"}}, {"head": {"id": "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706241906300, "endTime": 706345165400}, "additional": {"logType": "info", "children": ["5bd7d168-7187-4bc3-8f27-67d7236a6d5a", "9a467009-ad4b-45d9-8a03-6c14c46261d8", "88309a8d-6f08-44f5-bbb2-c4de2f29d2f5"], "durationId": "78ac1c04-aac3-439c-9f81-18c97d3221b1", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "35b90eff-4310-4133-8b11-8292e9d89e56", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345902200, "endTime": 706372380400}, "additional": {"children": ["28f9fc03-54a3-4781-82ab-178356b2d312", "a82cfeea-84e1-48b8-8621-1ff7bd983cf1", "1b97bb31-cd91-4fee-83dd-07dd4818ae05"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5ad34c3-2ed3-40d1-93ea-7cc21e1093fc", "logId": "e9364aa1-44b3-4e32-b683-c942a93ad31a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28f9fc03-54a3-4781-82ab-178356b2d312", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706349965100, "endTime": 706349978900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35b90eff-4310-4133-8b11-8292e9d89e56", "logId": "adad22f2-ca9a-4774-851c-fdbfaaca1974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adad22f2-ca9a-4774-851c-fdbfaaca1974", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706349965100, "endTime": 706349978900}, "additional": {"logType": "info", "children": [], "durationId": "28f9fc03-54a3-4781-82ab-178356b2d312", "parent": "e9364aa1-44b3-4e32-b683-c942a93ad31a"}}, {"head": {"id": "a82cfeea-84e1-48b8-8621-1ff7bd983cf1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706352365700, "endTime": 706369667200}, "additional": {"children": ["d6b2d205-d875-4ce8-80fe-fbc0f4ef55d5", "aaaa13d8-07d7-44fe-8642-bce077a5c61a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35b90eff-4310-4133-8b11-8292e9d89e56", "logId": "37fe87a2-e49e-4f58-85b5-9ea987a8047f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6b2d205-d875-4ce8-80fe-fbc0f4ef55d5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706352367300, "endTime": 706357152500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a82cfeea-84e1-48b8-8621-1ff7bd983cf1", "logId": "cec1f41d-5270-441a-ad70-beb101b92b90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaaa13d8-07d7-44fe-8642-bce077a5c61a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706357168000, "endTime": 706369653500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a82cfeea-84e1-48b8-8621-1ff7bd983cf1", "logId": "16049cb6-b9e5-4d35-bae2-51f7d59f3769"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5b22889-1bc8-45e1-a2fd-a189161a4e0a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706352375100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e9f3a0f-66f4-48f1-8f6a-082f2ff4fbee", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706357028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec1f41d-5270-441a-ad70-beb101b92b90", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706352367300, "endTime": 706357152500}, "additional": {"logType": "info", "children": [], "durationId": "d6b2d205-d875-4ce8-80fe-fbc0f4ef55d5", "parent": "37fe87a2-e49e-4f58-85b5-9ea987a8047f"}}, {"head": {"id": "7b096ccb-ec99-4ebd-952c-0e6ef44357be", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706357181600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aed86930-289e-4fc5-95b2-c89fcb2ac09e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706364232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7185066-e202-411e-aebd-6700be519ae4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706364415000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27a316f-d5cb-4649-8e43-646bb520ff6b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706364701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7550e973-2d91-45c8-a433-f29562d208b7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706364872900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5267eca1-dc15-4e03-86af-529905b0facf", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706365007200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa802136-92c7-4598-8b17-e32b29416b41", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706365082800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1288a136-4cf3-402e-9eb6-d38944d30f0d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706365922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e70fc49-7780-4154-a0a9-fe75b9af662b", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706369292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee001e0-ae9e-4326-b562-06119b61b6fa", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706369439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205c6490-2a1e-4047-aa53-630ccdca3d6c", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706369531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad31c352-05b9-4655-90c4-2176cae15da9", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706369593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16049cb6-b9e5-4d35-bae2-51f7d59f3769", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706357168000, "endTime": 706369653500}, "additional": {"logType": "info", "children": [], "durationId": "aaaa13d8-07d7-44fe-8642-bce077a5c61a", "parent": "37fe87a2-e49e-4f58-85b5-9ea987a8047f"}}, {"head": {"id": "37fe87a2-e49e-4f58-85b5-9ea987a8047f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706352365700, "endTime": 706369667200}, "additional": {"logType": "info", "children": ["cec1f41d-5270-441a-ad70-beb101b92b90", "16049cb6-b9e5-4d35-bae2-51f7d59f3769"], "durationId": "a82cfeea-84e1-48b8-8621-1ff7bd983cf1", "parent": "e9364aa1-44b3-4e32-b683-c942a93ad31a"}}, {"head": {"id": "1b97bb31-cd91-4fee-83dd-07dd4818ae05", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706372332000, "endTime": 706372353500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35b90eff-4310-4133-8b11-8292e9d89e56", "logId": "d7b92f21-86ea-4ac3-96cc-98364812c401"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7b92f21-86ea-4ac3-96cc-98364812c401", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706372332000, "endTime": 706372353500}, "additional": {"logType": "info", "children": [], "durationId": "1b97bb31-cd91-4fee-83dd-07dd4818ae05", "parent": "e9364aa1-44b3-4e32-b683-c942a93ad31a"}}, {"head": {"id": "e9364aa1-44b3-4e32-b683-c942a93ad31a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345902200, "endTime": 706372380400}, "additional": {"logType": "info", "children": ["adad22f2-ca9a-4774-851c-fdbfaaca1974", "37fe87a2-e49e-4f58-85b5-9ea987a8047f", "d7b92f21-86ea-4ac3-96cc-98364812c401"], "durationId": "35b90eff-4310-4133-8b11-8292e9d89e56", "parent": "621b7ec9-7be1-4fd2-8dd5-7324ac488206"}}, {"head": {"id": "621b7ec9-7be1-4fd2-8dd5-7324ac488206", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706345179800, "endTime": 706372403600}, "additional": {"logType": "info", "children": ["e9364aa1-44b3-4e32-b683-c942a93ad31a"], "durationId": "d5ad34c3-2ed3-40d1-93ea-7cc21e1093fc", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "e8f151e9-5217-4ed8-b052-bcd5c5c0c09c", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706407416300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f001e0-c16e-4802-85e1-e00978b5929c", "name": "hvigorfile, resolve hvigorfile dependencies in 36 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706407958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adeb7f70-f45b-478f-a848-f99a840c2b3e", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706372415700, "endTime": 706408118000}, "additional": {"logType": "info", "children": [], "durationId": "cd70cb96-ee6a-4bfd-a282-540c7d718a0a", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "642836db-1dfa-4c70-bbb5-d905421b16f6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706409325700, "endTime": 706409535600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "logId": "e3455f26-f25a-4d0c-af7a-d4c3c1fe2e2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92173af4-c015-4d00-8690-3f64a2dab91c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706409354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3455f26-f25a-4d0c-af7a-d4c3c1fe2e2d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706409325700, "endTime": 706409535600}, "additional": {"logType": "info", "children": [], "durationId": "642836db-1dfa-4c70-bbb5-d905421b16f6", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "78919a2b-307f-4f6e-80f8-d0759732f71c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706411380900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fab505c-8c99-498d-b935-ba0cc9c10137", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706417646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d28a484-6371-45a0-8f6d-9f0290e5f6da", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706409564500, "endTime": 706418440400}, "additional": {"logType": "info", "children": [], "durationId": "0a439b08-e960-4c34-840b-08a600faace1", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "4c1dbd3b-0dda-47f9-a870-7942c2e781ac", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706418473800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26252001-6e1e-444d-90d6-bbb5b1a9bb16", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706424655300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc59b65-775d-4eff-8d77-764d9f60b063", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706424789900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f469a1-82c3-44f7-9770-d05fbab75fda", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706425174800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67fd429e-5931-4493-b61a-865093bd64a8", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706427806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba88b2ec-9d6a-4e63-a813-a97a37c9adeb", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706427916000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ab0851e-4651-4728-b660-5a1d56c56265", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706418459600, "endTime": 706432110500}, "additional": {"logType": "info", "children": [], "durationId": "31e8f42e-bf3e-4f89-b3ac-50b9106f8e49", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "df2ba9e1-291d-4f61-a294-fe12187c1f86", "name": "Configuration phase cost:198 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706432171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c888e7-952c-4cb3-9d50-8a2d92418a59", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706432146800, "endTime": 706432285900}, "additional": {"logType": "info", "children": [], "durationId": "00573f99-8f67-4c78-8338-c2419c496308", "parent": "e246f93b-71a6-49c4-87d8-66c7450b5b8a"}}, {"head": {"id": "e246f93b-71a6-49c4-87d8-66c7450b5b8a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706228483200, "endTime": 706432299300}, "additional": {"logType": "info", "children": ["a31c78d3-e07b-4b4a-b93b-9aa4ed457d5e", "250b9122-285a-4e80-bfca-ccac48f8dd7c", "a1f41b7b-d421-4de7-94a3-fcf9841cdb8e", "621b7ec9-7be1-4fd2-8dd5-7324ac488206", "adeb7f70-f45b-478f-a848-f99a840c2b3e", "0d28a484-6371-45a0-8f6d-9f0290e5f6da", "7ab0851e-4651-4728-b660-5a1d56c56265", "21c888e7-952c-4cb3-9d50-8a2d92418a59", "e3455f26-f25a-4d0c-af7a-d4c3c1fe2e2d"], "durationId": "1a0fde4f-ab02-41de-aba2-c373c67e1f9a", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "bfcc0c82-b0cb-48b9-a261-b6ae75ce5fe3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706433514000, "endTime": 706433527300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b", "logId": "ae6f00cb-884c-4b3c-bf8d-c0c74ebf1e5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae6f00cb-884c-4b3c-bf8d-c0c74ebf1e5e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706433514000, "endTime": 706433527300}, "additional": {"logType": "info", "children": [], "durationId": "bfcc0c82-b0cb-48b9-a261-b6ae75ce5fe3", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "5409f1f5-f7e9-4831-a1bb-e54fb83717ee", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706432324200, "endTime": 706433538100}, "additional": {"logType": "info", "children": [], "durationId": "3550a757-fbda-4c18-9c64-32f59b735bde", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "1e2afce6-3211-4068-b12d-e447992f728e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706433544700, "endTime": 706433550900}, "additional": {"logType": "info", "children": [], "durationId": "b7aa23f5-5d93-48bb-8dd3-95f84bb8c893", "parent": "e61b7597-338e-4962-8eb0-41e586476147"}}, {"head": {"id": "e61b7597-338e-4962-8eb0-41e586476147", "name": "init", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706129815900, "endTime": 706433556300}, "additional": {"logType": "info", "children": ["8406ccd3-9053-4c20-bf68-bf5664b7867d", "e246f93b-71a6-49c4-87d8-66c7450b5b8a", "5409f1f5-f7e9-4831-a1bb-e54fb83717ee", "1e2afce6-3211-4068-b12d-e447992f728e", "5e245420-ae59-412d-86e4-e0980a101375", "94761231-e9e6-4be3-93c2-0d9e4f368ef2", "ae6f00cb-884c-4b3c-bf8d-c0c74ebf1e5e"], "durationId": "dbdf40b7-c959-41ab-ae3d-c9fea187b64b"}}, {"head": {"id": "91213dc2-5a68-45ad-9f6d-877e3091c4b0", "name": "Configuration task cost before running: 309 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706433668500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb35ed48-70fb-4fdf-ba49-847e283af785", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706440791100, "endTime": 706450566600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0f4736a4-2f35-42d8-9405-639a9b4e548c", "logId": "874b2887-817d-45eb-a1f3-7482ff25496b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f4736a4-2f35-42d8-9405-639a9b4e548c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706435508000}, "additional": {"logType": "detail", "children": [], "durationId": "bb35ed48-70fb-4fdf-ba49-847e283af785"}}, {"head": {"id": "68d357b4-d27b-4523-8c79-7d0bc0b1c66f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706436120400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b97a5e9-b09d-47a8-9dcb-b32992329a04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706436249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d406eba9-9365-49bc-bad0-547b2e4d31f9", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706440812100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea76bafb-c5d5-42ab-96e1-a419ac381317", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706450293800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44203d43-4f13-4269-8da3-f71ad49e410e", "name": "entry : default@PreBuild cost memory 0.4917449951171875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706450451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874b2887-817d-45eb-a1f3-7482ff25496b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706440791100, "endTime": 706450566600}, "additional": {"logType": "info", "children": [], "durationId": "bb35ed48-70fb-4fdf-ba49-847e283af785"}}, {"head": {"id": "af9cdaaf-87c7-4ad3-abaf-59b405efc840", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706458234000, "endTime": 706460895700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "09415867-68cb-41e1-96a0-01a46f90a2bf", "logId": "a084b2c4-ff29-48cb-a181-89e0b090338c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09415867-68cb-41e1-96a0-01a46f90a2bf", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706456679900}, "additional": {"logType": "detail", "children": [], "durationId": "af9cdaaf-87c7-4ad3-abaf-59b405efc840"}}, {"head": {"id": "bdda3597-5ab2-43a6-919b-934915a2aeaa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706457344700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a410e93-3bde-4089-a871-42fdf812c7b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706457470300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988eb345-1372-4524-b7d1-dbb52d5e8129", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706458245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2c45080-cc6c-4ef3-828d-7d672f60025d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706460634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b728950-5d17-4934-9c13-cdf0e7df60c3", "name": "entry : default@MergeProfile cost memory 0.11553192138671875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706460793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a084b2c4-ff29-48cb-a181-89e0b090338c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706458234000, "endTime": 706460895700}, "additional": {"logType": "info", "children": [], "durationId": "af9cdaaf-87c7-4ad3-abaf-59b405efc840"}}, {"head": {"id": "4467c568-a4a3-4a99-b9b4-81daf7b6fba8", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706465988600, "endTime": 706517498500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "46db6e90-69a3-4973-87ce-26bd6ef13bc6", "logId": "f4bbad77-ec4f-45b0-8de0-b7ec2f369b04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46db6e90-69a3-4973-87ce-26bd6ef13bc6", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706463730900}, "additional": {"logType": "detail", "children": [], "durationId": "4467c568-a4a3-4a99-b9b4-81daf7b6fba8"}}, {"head": {"id": "b7899289-4f65-4919-8d9e-76d1f6887686", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706464505600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "477998c4-a04e-4704-bead-9c5e1e235213", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706464661500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d39014b-b7f3-4176-8cf1-1fafd3d13550", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706466004300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258c5648-1277-45a0-b606-0ae22c0015a2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 49 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706514676800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b22b7e5c-6bca-4ce2-be3b-41ceb5bd7cf5", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706517253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "facefc5a-ab0a-4226-8d13-452a854edd1b", "name": "entry : default@CreateBuildProfile cost memory -1.6492767333984375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706517402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4bbad77-ec4f-45b0-8de0-b7ec2f369b04", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706465988600, "endTime": 706517498500}, "additional": {"logType": "info", "children": [], "durationId": "4467c568-a4a3-4a99-b9b4-81daf7b6fba8"}}, {"head": {"id": "31e51d17-d4c0-433d-905c-ebca4d3b8968", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522059700, "endTime": 706522786000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "68eda5dd-d881-4d84-80f1-319fe8b84b99", "logId": "2463b30e-57fe-4878-91a2-e70d22078966"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68eda5dd-d881-4d84-80f1-319fe8b84b99", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706519522400}, "additional": {"logType": "detail", "children": [], "durationId": "31e51d17-d4c0-433d-905c-ebca4d3b8968"}}, {"head": {"id": "aea2c2f6-f62b-4162-bbbc-474b41f7f755", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706520271900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6d468b-4f63-432f-8efb-0df6e1b53823", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706520399600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0ad1e2-e9f1-497e-8c7f-26ace7098d79", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522080100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71261e5-9d58-44df-bb1b-2e55a28df587", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522291400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8decd181-a035-4236-8a07-9178aec7ece3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522403400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c78420-74c4-43c6-be59-8f1bdb628adb", "name": "entry : default@PreCheckSyscap cost memory 0.03763580322265625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522551700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec7b085-71df-450f-9b6c-b521651ebe78", "name": "runTaskFromQueue task cost before running: 398 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2463b30e-57fe-4878-91a2-e70d22078966", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706522059700, "endTime": 706522786000, "totalTime": 596100}, "additional": {"logType": "info", "children": [], "durationId": "31e51d17-d4c0-433d-905c-ebca4d3b8968"}}, {"head": {"id": "4b82177c-9ff3-4b25-aa8f-927befb3f47e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706537159900, "endTime": 706538906200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1bc40367-0681-4d58-a1fd-9b6e1a9e9a4c", "logId": "6dd38e91-315e-432f-9bc1-312056e8e59c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bc40367-0681-4d58-a1fd-9b6e1a9e9a4c", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706525227000}, "additional": {"logType": "detail", "children": [], "durationId": "4b82177c-9ff3-4b25-aa8f-927befb3f47e"}}, {"head": {"id": "41dda796-42b2-4ecc-8c34-3a4e855e12d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706525777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f678303-8069-4ff7-a7fe-113dc98294b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706525906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2146b739-1559-4ed9-b7a2-cc8194ff825c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706537186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c22e61-fde4-4760-821a-cd1344263f75", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706537646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28914f9c-8526-4a64-876e-9cc62a26cb2d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706538627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235ddd21-e6f2-4743-bf93-7b4b81ecd6be", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0673675537109375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706538793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dd38e91-315e-432f-9bc1-312056e8e59c", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706537159900, "endTime": 706538906200}, "additional": {"logType": "info", "children": [], "durationId": "4b82177c-9ff3-4b25-aa8f-927befb3f47e"}}, {"head": {"id": "ebc21d34-e0d9-4da0-8f9a-b294bf11a38b", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706546060400, "endTime": 706547981700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f3891f6a-ca32-473b-bc7b-89bcada12342", "logId": "8481e380-55d3-4c62-9fe0-ecfafe391371"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3891f6a-ca32-473b-bc7b-89bcada12342", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706542406100}, "additional": {"logType": "detail", "children": [], "durationId": "ebc21d34-e0d9-4da0-8f9a-b294bf11a38b"}}, {"head": {"id": "49fcd655-cb7e-48d7-b7a1-df795cf9455f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706543372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0e4dbc-67a5-440d-88a2-3a48e7162f8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706543590500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a4f225-6df1-4416-b96e-676a73ba965a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706546082900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc73b43-f13a-4291-9c23-ccc452c17225", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706547567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3621c561-f99a-40df-a65b-5fff0d1bcc6d", "name": "entry : default@ProcessProfile cost memory 0.05930328369140625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706547873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8481e380-55d3-4c62-9fe0-ecfafe391371", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706546060400, "endTime": 706547981700}, "additional": {"logType": "info", "children": [], "durationId": "ebc21d34-e0d9-4da0-8f9a-b294bf11a38b"}}, {"head": {"id": "859b3259-7cad-49b6-8be7-269a1f60359c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706556720600, "endTime": 706567139700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1e167544-6b5c-483d-bc9a-cc5a357928cd", "logId": "8cc63344-c7ff-4f38-9e6b-46eaa406d808"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e167544-6b5c-483d-bc9a-cc5a357928cd", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706551012400}, "additional": {"logType": "detail", "children": [], "durationId": "859b3259-7cad-49b6-8be7-269a1f60359c"}}, {"head": {"id": "6809fa73-6903-48c6-8a16-1273bc755c3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706552332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d98ef92-519a-41a3-b49c-6698b4a93744", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706552557300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bd8aae-c799-442d-acf5-72cc8dc1aa43", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706556741800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1f36a8f-5ad8-4e34-b02e-5f647fe8e551", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706566803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c90e36-6f1f-4b14-8db0-207d536d2bc3", "name": "entry : default@ProcessRouterMap cost memory 0.19696044921875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706567011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc63344-c7ff-4f38-9e6b-46eaa406d808", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706556720600, "endTime": 706567139700}, "additional": {"logType": "info", "children": [], "durationId": "859b3259-7cad-49b6-8be7-269a1f60359c"}}, {"head": {"id": "99c05bf3-f86c-4fa4-992a-1441a9fc5e5f", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706578537400, "endTime": 706582114000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "12e40c44-d54c-4d49-aad3-03c4c37b1c4b", "logId": "952c2ca7-df80-483c-b64a-fb98d630234c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12e40c44-d54c-4d49-aad3-03c4c37b1c4b", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706573651500}, "additional": {"logType": "detail", "children": [], "durationId": "99c05bf3-f86c-4fa4-992a-1441a9fc5e5f"}}, {"head": {"id": "c26b75e7-e713-45f6-b145-c1e84fb70717", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706574395200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e24985ff-c563-4968-95c9-45c3e4ce5bc2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706574549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15d6d63-5c46-4c3c-8bb0-a952dc2e3ac7", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706575939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09ac466b-a6a8-49b1-9cfb-0ed3277d6374", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706579797600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b75fbcf-6ae8-456a-8cc1-81ceebf8f743", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706580014900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3553c96a-05ee-40fe-8612-7605c18f309c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706580100300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f52ce37-b9d2-4ee8-bbf8-41a39b3db98d", "name": "entry : default@PreviewProcessResource cost memory 0.06934356689453125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706580208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb9485d-7254-4cb7-b8dc-39f837c75932", "name": "runTaskFromQueue task cost before running: 457 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706581989800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952c2ca7-df80-483c-b64a-fb98d630234c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706578537400, "endTime": 706582114000, "totalTime": 1773300}, "additional": {"logType": "info", "children": [], "durationId": "99c05bf3-f86c-4fa4-992a-1441a9fc5e5f"}}, {"head": {"id": "60d5ed1d-d408-431b-b7a3-0bd3b3a7b11f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706591940600, "endTime": 706616933100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ee125cd-8def-4efe-8c81-26ba648191d0", "logId": "6db51728-a169-4fa2-b76c-2f2cfb93d4b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ee125cd-8def-4efe-8c81-26ba648191d0", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706585836800}, "additional": {"logType": "detail", "children": [], "durationId": "60d5ed1d-d408-431b-b7a3-0bd3b3a7b11f"}}, {"head": {"id": "a8c95dd4-e0c1-4cc3-b316-f21aa5d05be3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706586593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6341cc1f-df76-40d0-8160-709e6a6582dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706586761100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52daf25c-1d10-4e99-9819-38df873d53fe", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706591960100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cd7de7-c505-420a-8989-ed50856db4bb", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706616615300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3450faa0-5ed2-4bbe-8192-db86298eb371", "name": "entry : default@GenerateLoaderJson cost memory 0.7296981811523438", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706616829400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db51728-a169-4fa2-b76c-2f2cfb93d4b1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706591940600, "endTime": 706616933100}, "additional": {"logType": "info", "children": [], "durationId": "60d5ed1d-d408-431b-b7a3-0bd3b3a7b11f"}}, {"head": {"id": "f59a944f-f673-4a25-bab5-3058035a5a05", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706630620100, "endTime": 706655172700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9760b9da-272a-482e-aef7-17f1d26daeaf", "logId": "637c4ef3-4ead-43ee-8aac-d46dad1935da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9760b9da-272a-482e-aef7-17f1d26daeaf", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706625825000}, "additional": {"logType": "detail", "children": [], "durationId": "f59a944f-f673-4a25-bab5-3058035a5a05"}}, {"head": {"id": "4e8de4eb-8c99-4f61-aa0b-a6821540e4b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706626349400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75f96d4-a003-4535-8e7f-c6e93579bd2d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706626461300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0bd7f4-ecc2-47fd-a743-dceabd97d465", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706627507600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b8aa53-dcb7-432a-817a-36f505e93ac2", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706630655500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36604f8-ab60-42b1-adb2-e7e479cdb470", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706654850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0206041d-7389-4fcb-9d7d-dee0df66382a", "name": "entry : default@PreviewCompileResource cost memory 0.7965850830078125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706655028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637c4ef3-4ead-43ee-8aac-d46dad1935da", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706630620100, "endTime": 706655172700}, "additional": {"logType": "info", "children": [], "durationId": "f59a944f-f673-4a25-bab5-3058035a5a05"}}, {"head": {"id": "e96cf990-faee-4db1-b747-a2e39c94fb7d", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660179500, "endTime": 706661114500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "759f9cf9-8475-4d73-80a4-d45bcb309ad3", "logId": "20ef23ba-2828-4d79-a065-15697d0ba15f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "759f9cf9-8475-4d73-80a4-d45bcb309ad3", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706658919900}, "additional": {"logType": "detail", "children": [], "durationId": "e96cf990-faee-4db1-b747-a2e39c94fb7d"}}, {"head": {"id": "38cfa969-3220-45d6-b3ce-86913d2e18fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706659809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2201ad1a-de87-4516-8618-86cab3f9d53b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706659998500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a4c086d-1edc-452f-a4cb-448be8564bab", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c926510f-4fd4-4444-8efd-c5f11dbcc11f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660577800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c5e958-6730-4089-bdfd-2e8badfc145c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660697900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397fdda3-f322-4e38-8c18-4d1a68352dfc", "name": "entry : default@PreviewHookCompileResource cost memory 0.038726806640625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660856300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01310bb-f390-4d5a-9b2a-6f3d9a11d5c6", "name": "runTaskFromQueue task cost before running: 536 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706661006300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ef23ba-2828-4d79-a065-15697d0ba15f", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706660179500, "endTime": 706661114500, "totalTime": 791000}, "additional": {"logType": "info", "children": [], "durationId": "e96cf990-faee-4db1-b747-a2e39c94fb7d"}}, {"head": {"id": "94855aaa-d219-4d2c-8227-1c4bf89b2cae", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706666027700, "endTime": 706669663200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1e3c9d21-8ebf-4e82-89a4-c61a34c6bc34", "logId": "d55d0511-b7a5-4120-9268-f3c9572c813e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e3c9d21-8ebf-4e82-89a4-c61a34c6bc34", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706664241900}, "additional": {"logType": "detail", "children": [], "durationId": "94855aaa-d219-4d2c-8227-1c4bf89b2cae"}}, {"head": {"id": "6ffbfe68-404d-4a96-8f2e-2b44bac4b81d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706664943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16eb76cc-1cdf-4a2e-889e-89ddb92ff93f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706665116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21a1de7-ecdf-4ea2-96d1-a055e8541841", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706666042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c0a961-e1e8-43e2-80b2-ec733c43baf0", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706669380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16826644-2016-4154-9c59-824c1a1f4f11", "name": "entry : default@CopyPreviewProfile cost memory 0.10211181640625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706669568400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55d0511-b7a5-4120-9268-f3c9572c813e", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706666027700, "endTime": 706669663200}, "additional": {"logType": "info", "children": [], "durationId": "94855aaa-d219-4d2c-8227-1c4bf89b2cae"}}, {"head": {"id": "7c622166-144c-4b36-916d-1532d18d9ad4", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675452600, "endTime": 706676153300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "4c7c150c-652d-412d-8122-bfd275a1d6ed", "logId": "106aad20-b6a3-40a7-aac4-c47f9e6552d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c7c150c-652d-412d-8122-bfd275a1d6ed", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706673618200}, "additional": {"logType": "detail", "children": [], "durationId": "7c622166-144c-4b36-916d-1532d18d9ad4"}}, {"head": {"id": "7eed6fb7-3e0d-45a3-b6e4-d107ee983423", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706674275400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d01c179-4484-4230-8046-ff70979cb2f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706674413800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37c6e31-f06a-416e-a424-28edf42bbbf4", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675482100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32693fd-f869-44f9-b22e-bc762ef422c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675681000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301e021b-127b-4a44-8f2c-95951527af0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675767500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "379bd5f4-7887-43ae-9b43-81adfb6195d7", "name": "entry : default@ReplacePreviewerPage cost memory 0.03864288330078125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2feca870-b3f3-497c-aba5-ac333e10bc2f", "name": "runTaskFromQueue task cost before running: 551 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706676069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106aad20-b6a3-40a7-aac4-c47f9e6552d6", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706675452600, "endTime": 706676153300, "totalTime": 592600}, "additional": {"logType": "info", "children": [], "durationId": "7c622166-144c-4b36-916d-1532d18d9ad4"}}, {"head": {"id": "f8fecd95-2f35-43e0-8306-5c227e59d0e2", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706679756000, "endTime": 706680149700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a76eb984-b7af-4e7b-b755-27d5b519fe0d", "logId": "8c8069d4-dbfb-4019-9cd6-07b0d6aabf6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a76eb984-b7af-4e7b-b755-27d5b519fe0d", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706679687600}, "additional": {"logType": "detail", "children": [], "durationId": "f8fecd95-2f35-43e0-8306-5c227e59d0e2"}}, {"head": {"id": "66b0a16b-190c-49bf-80a0-ceda547f4df8", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706679766500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e2a813-ff40-4cea-b938-9617b6530ecf", "name": "entry : buildPreviewerResource cost memory 0.01189422607421875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706679964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f6868f0-628a-4197-ba1f-ef552da44c85", "name": "runTaskFromQueue task cost before running: 555 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706680079300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8069d4-dbfb-4019-9cd6-07b0d6aabf6b", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706679756000, "endTime": 706680149700, "totalTime": 299100}, "additional": {"logType": "info", "children": [], "durationId": "f8fecd95-2f35-43e0-8306-5c227e59d0e2"}}, {"head": {"id": "22d21cf3-d7da-4e9d-8123-38507589528d", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706686220300, "endTime": 706690636500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "485f8f7a-8658-436c-9533-ec49d5dea771", "logId": "eea3d1b6-ca4a-4b46-ac5e-82c66c972f01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "485f8f7a-8658-436c-9533-ec49d5dea771", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706683478100}, "additional": {"logType": "detail", "children": [], "durationId": "22d21cf3-d7da-4e9d-8123-38507589528d"}}, {"head": {"id": "74a92d99-9a0c-43c4-9c7f-1af94dbf1ae2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706684319600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b0914e-ed35-4b0a-826a-b58a92232694", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706684501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "284ea1cf-4621-4f30-8d80-202321681297", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706686233100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdac0f8b-a7e4-4c48-b5af-b0f4de1fd147", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706690358600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d20dc47-1239-46fd-b171-f8e6364dc82c", "name": "entry : default@PreviewUpdateAssets cost memory 0.1064605712890625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706690542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea3d1b6-ca4a-4b46-ac5e-82c66c972f01", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706686220300, "endTime": 706690636500}, "additional": {"logType": "info", "children": [], "durationId": "22d21cf3-d7da-4e9d-8123-38507589528d"}}, {"head": {"id": "bd015734-909d-4d61-9869-e69fb33cb77e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706701441900, "endTime": 737574163300}, "additional": {"children": ["f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "819d301b-81df-4b9b-84ce-3c0ddb06b419", "logId": "503e2ca1-6146-4c4d-a1d5-3a1be500f423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "819d301b-81df-4b9b-84ce-3c0ddb06b419", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706693862000}, "additional": {"logType": "detail", "children": [], "durationId": "bd015734-909d-4d61-9869-e69fb33cb77e"}}, {"head": {"id": "98ebfe97-5c4b-4485-843e-dba7fb50e23b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706694526500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd1b1e3-5137-4de6-85c9-9369dee2ba69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706694660800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85481a50-bbc9-4dcb-962f-f5e40d5281f5", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706701461000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 706753957000, "endTime": 737571182700}, "additional": {"children": ["0e2bdaa0-173d-46bf-ac49-6a5fa62f28f7", "3917a88e-d6b7-4cb7-b303-60c7acf70262", "8c4f1e79-19f5-4b20-a27d-52e2aaca71ca", "373faf40-aed1-4cf6-b83a-da0f8de19d0f", "82f34ed3-ee5f-4a9b-840f-1e177c1bded0"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bd015734-909d-4d61-9869-e69fb33cb77e", "logId": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "224142e2-0cdf-46e1-96ce-52fe8c434c5f", "name": "entry : default@PreviewArkTS cost memory 0.882843017578125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706762266100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43fa2a10-3af3-4f8d-9f08-e6d763bbb13f", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 721027210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2bdaa0-173d-46bf-ac49-6a5fa62f28f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 721028540000, "endTime": 721028562400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "logId": "9293c0cf-b4b0-4a55-b28c-436980c9c697"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9293c0cf-b4b0-4a55-b28c-436980c9c697", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 721028540000, "endTime": 721028562400}, "additional": {"logType": "info", "children": [], "durationId": "0e2bdaa0-173d-46bf-ac49-6a5fa62f28f7", "parent": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}}, {"head": {"id": "4f481a49-7a45-4571-9d29-ca3fee139e3a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737569540800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3917a88e-d6b7-4cb7-b303-60c7acf70262", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 737570653600, "endTime": 737570672000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "logId": "d6c8bc6a-0c96-4804-9389-af0a32ac342c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6c8bc6a-0c96-4804-9389-af0a32ac342c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737570653600, "endTime": 737570672000}, "additional": {"logType": "info", "children": [], "durationId": "3917a88e-d6b7-4cb7-b303-60c7acf70262", "parent": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}}, {"head": {"id": "1e6d61c5-d9bc-4565-b508-53ef3ea81920", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 706753957000, "endTime": 737571182700}, "additional": {"logType": "info", "children": ["9293c0cf-b4b0-4a55-b28c-436980c9c697", "d6c8bc6a-0c96-4804-9389-af0a32ac342c", "2d318dc0-cfb0-4eee-9216-70b488c83433", "3453e956-f5d2-4209-9b9c-31ea70aaf785", "b7210092-9ecc-48be-9879-1bd283645426"], "durationId": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "parent": "503e2ca1-6146-4c4d-a1d5-3a1be500f423"}}, {"head": {"id": "8c4f1e79-19f5-4b20-a27d-52e2aaca71ca", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 716674861400, "endTime": 720974182600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "logId": "2d318dc0-cfb0-4eee-9216-70b488c83433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d318dc0-cfb0-4eee-9216-70b488c83433", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 716674861400, "endTime": 720974182600}, "additional": {"logType": "info", "children": [], "durationId": "8c4f1e79-19f5-4b20-a27d-52e2aaca71ca", "parent": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}}, {"head": {"id": "373faf40-aed1-4cf6-b83a-da0f8de19d0f", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 720974434900, "endTime": 720974635200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "logId": "3453e956-f5d2-4209-9b9c-31ea70aaf785"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3453e956-f5d2-4209-9b9c-31ea70aaf785", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 720974434900, "endTime": 720974635200}, "additional": {"logType": "info", "children": [], "durationId": "373faf40-aed1-4cf6-b83a-da0f8de19d0f", "parent": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}}, {"head": {"id": "82f34ed3-ee5f-4a9b-840f-1e177c1bded0", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker2", "startTime": 720974783300, "endTime": 737569574100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f3d3dc1f-12b8-4aef-92ed-4f7495bdd18e", "logId": "b7210092-9ecc-48be-9879-1bd283645426"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7210092-9ecc-48be-9879-1bd283645426", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 720974783300, "endTime": 737569574100}, "additional": {"logType": "info", "children": [], "durationId": "82f34ed3-ee5f-4a9b-840f-1e177c1bded0", "parent": "1e6d61c5-d9bc-4565-b508-53ef3ea81920"}}, {"head": {"id": "503e2ca1-6146-4c4d-a1d5-3a1be500f423", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706701441900, "endTime": 737574163300, "totalTime": 30872677500}, "additional": {"logType": "info", "children": ["1e6d61c5-d9bc-4565-b508-53ef3ea81920"], "durationId": "bd015734-909d-4d61-9869-e69fb33cb77e"}}, {"head": {"id": "572aa45e-c7fc-427c-91db-3d4d27ac2c44", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579446600, "endTime": 737579761800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "aa176aad-7bff-4bb6-b71f-bf759aed64cc", "logId": "eca6bc05-6af3-4524-a6c6-91bbd9aa6c12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa176aad-7bff-4bb6-b71f-bf759aed64cc", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579392500}, "additional": {"logType": "detail", "children": [], "durationId": "572aa45e-c7fc-427c-91db-3d4d27ac2c44"}}, {"head": {"id": "8b9dbdb5-e5ff-47d3-80dc-da227e96a443", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bc67e2-4c77-4acf-a4f3-0b1902a21bdc", "name": "entry : PreviewBuild cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dfe6fe5-7c21-4f7c-804d-76c87a9ddf30", "name": "runTaskFromQueue task cost before running: 31 s 455 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca6bc05-6af3-4524-a6c6-91bbd9aa6c12", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737579446600, "endTime": 737579761800, "totalTime": 222600}, "additional": {"logType": "info", "children": [], "durationId": "572aa45e-c7fc-427c-91db-3d4d27ac2c44"}}, {"head": {"id": "722fc4ec-76d1-4d96-b2a4-d4ba15116064", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737584859300, "endTime": 737584887300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2aa5b082-b056-4c50-b44b-7511f14c7a4f", "logId": "1889beef-cb61-4eed-89f6-1f0da8657434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1889beef-cb61-4eed-89f6-1f0da8657434", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737584859300, "endTime": 737584887300}, "additional": {"logType": "info", "children": [], "durationId": "722fc4ec-76d1-4d96-b2a4-d4ba15116064"}}, {"head": {"id": "6476f042-c2f0-49a9-8e6b-2da7a930e13d", "name": "BUILD SUCCESSFUL in 31 s 460 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737584979900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "ad7898d7-84bc-4278-9513-965aeb18794a", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 706125109500, "endTime": 737585238200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 16, "minute": 2}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "683f1700-384e-4971-ba2e-e35616cd69a7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585262500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4070d7bc-2ad5-4494-bb98-a7dd27ffc554", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbaa75f5-ad5f-4d30-a9af-734b4540586a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f35eb2-a299-48d8-9eb9-83cf06e182a2", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1634d0-9282-41c2-b4c8-bf180f67c791", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ec70cb-7a30-473e-941c-f8f54b747e71", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585553100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74fe680-dcf9-4fbb-98e3-f49286714005", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585618800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7854228e-b105-448b-a8df-4a23230ce5eb", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585679000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50edd32-579a-48ee-967e-103d1a2a9a6a", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa98ee6b-3fd8-43ce-b7a4-39a450fe6cf6", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737585844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25edc72a-e305-4934-bd86-d24c2ef97e83", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737589182800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912b1413-e04d-411f-b490-01b36c2dcf71", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737590233300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed46f452-10d0-4cd8-beae-99df43bef101", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737590620500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc7f175-6420-4907-88b6-63fb73b189ae", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737590963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b94b837-fd0d-45e2-9f91-c6a12905dd6b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737592086800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b15819-dee0-4ae1-9fac-7a100f9a3f7e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737603545300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b26069-cce9-4b46-a69f-ff7d65cf8713", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737603921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2b75b0-010d-4881-b0ea-812c8dbe8a0f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737604281600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc681252-dcbe-4135-a566-033c1004bc06", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737604655300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12396a2a-eb05-4a9e-8feb-f6ef596fdc21", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737604955300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}