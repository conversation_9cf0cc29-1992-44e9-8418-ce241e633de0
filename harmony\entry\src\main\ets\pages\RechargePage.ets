import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct RechargePage {
  @State rechargeAmount: string = '';
  @State selectedBankCard: string = '';
  @State paymentPassword: string = '';
  @State isLoading: boolean = false;

  async handleRecharge() {
    if (!this.rechargeAmount || !this.selectedBankCard || !this.paymentPassword) {
      promptAction.showToast({
        message: '请填写完整信息',
        duration: 2000
      });
      return;
    }

    if (parseFloat(this.rechargeAmount) <= 0) {
      promptAction.showToast({
        message: '充值金额必须大于0',
        duration: 2000
      });
      return;
    }

    try {
      this.isLoading = true;

      // 模拟API调用
      await new Promise<void>(resolve => setTimeout(resolve, 2000));

      promptAction.showToast({
        message: '充值成功',
        duration: 2000
      });

      // 返回上一页
      router.back();

    } catch (error) {
      console.error('充值失败:', error);
      promptAction.showToast({
        message: '充值失败，请重试',
        duration: 2000
      });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          Text('×')
            .fontSize(24)
            .fontColor('#666666')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('充值')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位
        Text('')
          .width(40)
          .height(40)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#FFFFFF')

      // 表单内容
      Column() {
        // 充值金额
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('充值金额')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入充值金额' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .type(InputType.Number)
            .onChange((value: string) => {
              this.rechargeAmount = value;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 银行卡选择
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('银行卡')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入银行卡号' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .type(InputType.Number)
            .onChange((value: string) => {
              this.selectedBankCard = value;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 支付密码
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('支付密码')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入支付密码' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .type(InputType.Password)
            .onChange((value: string) => {
              this.paymentPassword = value;
            })
        }
        .width('100%')
        .margin({ bottom: 40 })

        // 操作按钮
        Row() {
          Button('取消')
            .width(100)
            .height(44)
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .onClick(() => {
              router.back();
            })

          Button(this.isLoading ? '充值中...' : '确认充值')
            .width(120)
            .height(44)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#1976D2')
            .borderRadius(8)
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleRecharge();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.End)
        .margin({ top: 20 })
      }
      .layoutWeight(1)
      .padding({ left: 20, right: 20, top: 20 })
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}