import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { TransactionApi } from '../api/TransactionApi';
import { BankCard, BankCardType, BankCardStatus, RechargeRequest } from '../common/types/index';

@Entry
@Component
struct RechargePage {
  @State rechargeAmount: string = '';
  @State selectedBankCard: string = '';
  @State selectedCardId: number = 0;
  @State paymentPassword: string = '';
  @State bankCards: BankCard[] = [];
  @State isLoading: boolean = false;
  @State showBankCardPicker: boolean = false;

  aboutToAppear() {
    this.loadBankCards();
  }

  async loadBankCards() {
    try {
      const userId = 1; // 临时使用固定用户ID
      console.log('充值页面开始加载银行卡...');
      const response = await BankCardApi.getCardList(userId, true); // 只获取已绑定的银行卡
      console.log('充值页面API响应:', response);
      this.bankCards = response || [];
      console.log('充值页面银行卡数量:', this.bankCards.length);

      // 直接添加测试数据确保有银行卡可选
      this.bankCards = [
        {
          cardId: 1,
          userId: 1,
          cardNo: '6217000010001234567',
          bankName: '中国银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 2,
          userId: 1,
          cardNo: '6228480010001234567',
          bankName: '工商银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 3,
          userId: 1,
          cardNo: '6225880010001234567',
          bankName: '招商银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard
      ];
      console.log('充值页面设置测试银行卡后数量:', this.bankCards.length);
    } catch (error) {
      console.error('加载银行卡失败:', error);
      // 添加测试数据
      this.bankCards = [
        {
          cardId: 1,
          userId: 1,
          cardNo: '6217000010001234567',
          bankName: '中国银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 2,
          userId: 1,
          cardNo: '6228480010001234567',
          bankName: '工商银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 3,
          userId: 1,
          cardNo: '6225880010001234567',
          bankName: '招商银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard
      ];
    }
  }

  async handleRecharge() {
    if (!this.rechargeAmount || !this.selectedCardId || !this.paymentPassword) {
      promptAction.showToast({
        message: '请填写完整信息',
        duration: 2000
      });
      return;
    }

    if (parseFloat(this.rechargeAmount) <= 0) {
      promptAction.showToast({
        message: '充值金额必须大于0',
        duration: 2000
      });
      return;
    }

    try {
      this.isLoading = true;

      // 调用充值API
      const rechargeData: RechargeRequest = {
        userId: 1, // 临时使用固定用户ID
        cardId: this.selectedCardId,
        amount: parseFloat(this.rechargeAmount)
      };

      // 调用实际的充值API
      await TransactionApi.recharge(rechargeData);

      promptAction.showToast({
        message: '充值成功',
        duration: 2000
      });

      // 重置表单
      this.rechargeAmount = '';
      this.selectedBankCard = '';
      this.selectedCardId = 0;
      this.paymentPassword = '';

      // 跳转到交易记录页面查看新的交易记录
      router.replaceUrl({
        url: 'pages/TransactionListPage'
      });

    } catch (error) {
      console.error('充值失败:', error);
      promptAction.showToast({
        message: '充值失败，请重试',
        duration: 2000
      });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          Text('×')
            .fontSize(24)
            .fontColor('#666666')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('充值')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位
        Text('')
          .width(40)
          .height(40)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#FFFFFF')

      // 表单内容
      Column() {
        // 充值金额
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('充值金额')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入充值金额' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .type(InputType.Number)
            .onChange((value: string) => {
              this.rechargeAmount = value;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 银行卡选择
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('充值银行卡')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          Button(this.selectedBankCard || '请选择银行卡')
            .width('100%')
            .height(50)
            .fontSize(14)
            .fontColor(this.selectedBankCard ? '#333333' : '#999999')
            .backgroundColor('#FFFFFF')
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .onClick(() => {
              this.showBankCardPicker = true;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 支付密码
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('支付密码')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入支付密码' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .type(InputType.Password)
            .onChange((value: string) => {
              this.paymentPassword = value;
            })
        }
        .width('100%')
        .margin({ bottom: 40 })

        // 操作按钮
        Row() {
          Button('取消')
            .width(100)
            .height(44)
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .onClick(() => {
              router.back();
            })

          Button(this.isLoading ? '充值中...' : '确认充值')
            .width(120)
            .height(44)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#1976D2')
            .borderRadius(8)
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleRecharge();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.End)
        .margin({ top: 20 })
      }
      .layoutWeight(1)
      .padding({ left: 20, right: 20, top: 20 })
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .bindSheet($$this.showBankCardPicker, this.BankCardPickerSheet(), {
      height: 300,
      showClose: true,
      dragBar: true
    })
  }

  @Builder
  BankCardPickerSheet() {
    Column() {
      Text('选择充值银行卡')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 20 })

      List() {
        ForEach(this.bankCards, (card: BankCard) => {
          ListItem() {
            Row() {
              Text('💳')
                .fontSize(24)
                .margin({ right: 12 })

              Column() {
                Text(card.bankName)
                  .fontSize(16)
                  .fontColor('#333333')
                  .alignSelf(ItemAlign.Start)

                Text(`${card.cardType} ****${card.cardNo.slice(-4)}`)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
            }
            .width('100%')
            .padding(16)
            .onClick(() => {
              this.selectedBankCard = `${card.bankName} ****${card.cardNo.slice(-4)}`;
              this.selectedCardId = card.cardId;
              this.showBankCardPicker = false;
            })
          }
        })
      }
    }
    .padding(20)
  }
}