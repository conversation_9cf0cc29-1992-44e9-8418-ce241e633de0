import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';

@Entry
@Component
struct ChangePasswordPage {
  @State currentPassword: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State isLoading: boolean = false;

  async handleSave() {
    if (!this.currentPassword.trim()) {
      promptAction.showToast({ message: '请输入当前密码' });
      return;
    }

    if (!this.newPassword.trim()) {
      promptAction.showToast({ message: '请输入新密码' });
      return;
    }

    if (this.newPassword.length < 6) {
      promptAction.showToast({ message: '新密码长度不能少于6位' });
      return;
    }

    if (!this.confirmPassword.trim()) {
      promptAction.showToast({ message: '请确认新密码' });
      return;
    }

    if (this.newPassword !== this.confirmPassword) {
      promptAction.showToast({ message: '两次输入的密码不一致' });
      return;
    }

    if (this.currentPassword === this.newPassword) {
      promptAction.showToast({ message: '新密码不能与当前密码相同' });
      return;
    }

    this.isLoading = true;
    try {
      // 调用API修改密码
      await UserApi.changePassword({
        currentPassword: this.currentPassword,
        newPassword: this.newPassword
      });

      promptAction.showToast({ message: '密码修改成功' });
      router.back();
    } catch (error) {
      console.error('密码修改失败:', error);
      promptAction.showToast({ message: '密码修改失败，请检查当前密码是否正确' });
    } finally {
      this.isLoading = false;
    }
  }

  handleCancel() {
    router.back();
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('修改密码')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button('确认修改')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .enabled(!this.isLoading)
          .onClick(() => {
            this.handleSave();
          })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({ width: { bottom: 1 }, color: '#E5E7EB' })

      // 表单内容
      Column() {
        // 当前密码
        Column() {
          Row() {
            Text('当前密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(100)

            TextInput({ placeholder: '请输入当前密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Password)
              .onChange((value: string) => {
                this.currentPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 新密码
        Column() {
          Row() {
            Text('新密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(100)

            TextInput({ placeholder: '请输入新密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Password)
              .onChange((value: string) => {
                this.newPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 确认密码
        Column() {
          Row() {
            Text('确认密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(100)

            TextInput({ placeholder: '请确认新密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Password)
              .onChange((value: string) => {
                this.confirmPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
      }
      .padding(20)
      .backgroundColor('#FFFFFF')
      .margin({ top: 16, left: 16, right: 16 })
      .borderRadius(12)

      // 密码要求提示
      Column() {
        Text('密码要求：')
          .fontSize(14)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        Text('• 密码长度不少于6位')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text('• 建议包含字母、数字和特殊字符')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
      }
      .width('100%')
      .padding(20)
      .margin({ top: 16, left: 16, right: 16 })
      .backgroundColor('#F8F9FA')
      .borderRadius(12)

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
