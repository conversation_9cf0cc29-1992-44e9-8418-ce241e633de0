if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WalletPaymentPage_Params {
    product?: string;
    amount?: string;
    password?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
class WalletPaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__product = new ObservedPropertySimplePU('商品购买', this, "product");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WalletPaymentPage_Params) {
        if (params.product !== undefined) {
            this.product = params.product;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
    }
    updateStateVars(params: WalletPaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__product.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__product.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __product: ObservedPropertySimplePU<string>;
    get product() {
        return this.__product.get();
    }
    set product(newValue: string) {
        this.__product.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    aboutToAppear() {
        console.log('WalletPaymentPage 页面加载成功');
    }
    // 处理支付
    private async handlePayment() {
        if (!this.product.trim()) {
            promptAction.showToast({
                message: '请输入商品信息',
                duration: 2000
            });
            return;
        }
        if (!this.amount.trim()) {
            promptAction.showToast({
                message: '请输入支付金额',
                duration: 2000
            });
            return;
        }
        if (!this.password.trim()) {
            promptAction.showToast({
                message: '请输入支付密码',
                duration: 2000
            });
            return;
        }
        try {
            // 调用支付API生成交易记录
            const paymentAmount = parseFloat(this.amount);
            await TransactionApi.payment({
                userId: 1,
                amount: paymentAmount,
                description: `钱包支付-${this.product}`,
                paymentMethod: 'WALLET'
            });
            promptAction.showToast({
                message: '钱包支付成功！',
                duration: 2000
            });
            // 延迟跳转到交易记录页面
            setTimeout(() => {
                router.replaceUrl({
                    url: 'pages/TransactionListPage'
                });
            }, 2000);
        }
        catch (error) {
            console.error('支付失败:', error);
            promptAction.showToast({
                message: '支付失败，请重试',
                duration: 2000
            });
        }
    }
    // 取消支付
    private handleCancel() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(78:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(80:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('×');
            Button.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(81:9)", "entry");
            Button.fontSize(24);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包支付');
            Text.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(89:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(96:9)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(109:7)", "entry");
            // 支付表单
            Column.layoutWeight(1);
            // 支付表单
            Column.padding(20);
            // 支付表单
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 商品信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(111:9)", "entry");
            // 商品信息
            Row.width('100%');
            // 商品信息
            Row.alignItems(VerticalAlign.Center);
            // 商品信息
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商品');
            Text.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(112:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '商品购买', text: this.product });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(117:11)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.onChange((value: string) => {
                this.product = value;
            });
        }, TextInput);
        // 商品信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付金额
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(134:9)", "entry");
            // 支付金额
            Row.width('100%');
            // 支付金额
            Row.alignItems(VerticalAlign.Center);
            // 支付金额
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('金额');
            Text.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(135:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付金额' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(140:11)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        // 支付金额
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(158:9)", "entry");
            // 支付密码
            Row.width('100%');
            // 支付密码
            Row.alignItems(VerticalAlign.Center);
            // 支付密码
            Row.margin({ bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码');
            Text.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(159:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(164:11)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.password = value;
            });
        }, TextInput);
        // 支付密码
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(182:9)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.End);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(183:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F8F9FA');
            Button.border({ width: 1, color: '#E5E5E5' });
            Button.borderRadius(8);
            Button.width(100);
            Button.height(44);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认支付');
            Button.debugLine("entry/src/main/ets/pages/WalletPaymentPage.ets(195:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4A90E2');
            Button.borderRadius(8);
            Button.width(120);
            Button.height(44);
            Button.margin({ left: 20 });
            Button.onClick(() => {
                this.handlePayment();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 支付表单
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WalletPaymentPage";
    }
}
registerNamedRoute(() => new WalletPaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/WalletPaymentPage", pageFullPath: "entry/src/main/ets/pages/WalletPaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
