import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import {
  Transaction,
  TransactionQueryParams,
  PageResult,
  TransactionType,
  PaymentMethod,
  TransactionStatus
} from '../common/types/index';

@Entry
@Component
struct TransactionListPage {
  @State transactions: Transaction[] = [];
  @State isLoading: boolean = false;
  @State isRefreshing: boolean = false;
  @State hasMore: boolean = true;
  @State currentPage: number = 1;
  @State showDetailDialog: boolean = false;
  @State selectedTransaction: Transaction | null = null;
  @State pageSize: number = 20;
  @State selectedType: TransactionType | '' = '';
  @State showFilterDialog: boolean = false;

  // 筛选条件
  @State filterStartDate: string = '';
  @State filterEndDate: string = '';

  aboutToAppear() {
    this.loadTransactions(true);
  }

  async loadTransactions(refresh: boolean = false) {
    if (this.isLoading) return;

    this.isLoading = true;
    if (refresh) {
      this.isRefreshing = true;
      this.currentPage = 1;
      this.hasMore = true;
    }

    try {
      const params: TransactionQueryParams = {
        page: this.currentPage.toString(),
        size: this.pageSize.toString(),
        type: this.selectedType || undefined,
        startDate: this.filterStartDate,
        endDate: this.filterEndDate
      };

      const result: PageResult<Transaction> = await TransactionApi.getTransactionList(params);

      if (refresh) {
        this.transactions = result.records;
      } else {
        this.transactions = [...this.transactions, ...result.records];
      }

      this.hasMore = this.currentPage < result.pages;
      this.currentPage++;
    } catch (error) {
      console.error('加载交易记录失败:', error);
      promptAction.showToast({ message: '加载交易记录失败' });
    } finally {
      this.isLoading = false;
      this.isRefreshing = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('交易记录')
          .fontSize(20)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
      }
      .width('100%')
      .height(60)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
      })

      // 交易类型筛选
      Scroll() {
        Row() {
          this.TypeFilterButton('全部', '')
          this.TypeFilterButton('支付', TransactionType.PAYMENT)
          this.TypeFilterButton('充值', TransactionType.RECHARGE)
          this.TypeFilterButton('提现', TransactionType.WITHDRAW)
          this.TypeFilterButton('转账', TransactionType.TRANSFER)
        }
        .padding(12)
      }
      .scrollable(ScrollDirection.Horizontal)
      .scrollBar(BarState.Off)

      // 交易记录列表
      if (this.transactions.length === 0 && !this.isLoading) {
        this.EmptyView()
      } else {
        List() {
          ForEach(this.transactions, (item: Transaction) => {
            ListItem() {
              this.TransactionCard(item)
            }
            .onClick(() => this.viewTransactionDetail(item))
          })

          if (this.hasMore) {
            ListItem() {
              this.LoadMoreView()
            }
          }
        }
        .width('100%')
        .layoutWeight(1)
        .divider({
          strokeWidth: 1,
          color: '#F0F0F0',
          startMargin: 16,
          endMargin: 16
        })
        .onReachEnd(() => {
          if (!this.isLoading && this.hasMore) {
            this.loadTransactions();
          }
        })
      }

      // 底部导航栏
      this.BottomNavigation()

      // 交易详情弹窗
      if (this.showDetailDialog && this.selectedTransaction) {
        this.TransactionDetailDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  TransactionCard(item: Transaction) {
    Column() {
      Row() {
        // 交易类型图标
        Column() {
          Text(this.getTransactionIcon(item.transactionType))
            .fontSize(20)
        }
        .width(40)
        .height(40)
        .backgroundColor(this.getTypeColor(item.transactionType) + '20')
        .borderRadius(20)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .margin({ right: 12 })

        // 交易信息
        Column() {
          Row() {
            Text(item.transactionType)
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            Text(this.formatAmount(item))
              .fontSize(16)
              .fontColor(this.getAmountColor(item))
          }

          Text(item.description)
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Row() {
            Text(item.createTime)
              .fontSize(12)
              .fontColor('#999999')

            Text(item.status)
              .fontSize(12)
              .fontColor(this.getStatusColor(item.status))
              .margin({ left: 8 })
          }
          .margin({ top: 8 })
        }
        .layoutWeight(1)
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#FFFFFF')
  }

  @Builder
  TypeFilterButton(title: string, type: TransactionType | '') {
    Button(title)
      .fontSize(14)
      .fontColor(this.selectedType === type ? '#FFFFFF' : '#6366F1')
      .backgroundColor(this.selectedType === type ? '#6366F1' : '#F5F5F5')
      .borderRadius(16)
      .height(32)
      .padding({ left: 16, right: 16 })
      .margin({ right: 8 })
      .onClick(() => {
        this.selectedType = type;
        this.loadTransactions(true);
      })
  }

  @Builder
  EmptyView() {
    Column() {
      Image($r('app.media.ic_empty'))
        .width(120)
        .height(120)
        .margin({ bottom: 16 })

      Text('暂无交易记录')
        .fontSize(16)
        .fontColor('#999999')
        .margin({ bottom: 16 })

      Button('去充值')
        .type(ButtonType.Capsule)
        .fontColor('#FFFFFF')
        .backgroundColor('#6366F1')
        .onClick(() => router.pushUrl({ url: 'pages/RechargePage' }))
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  LoadMoreView() {
    Row() {
      if (this.isLoading) {
        LoadingProgress()
          .width(20)
          .height(20)
          .margin({ right: 8 })
      }
      Text(this.isLoading ? '加载中...' : '上拉加载更多')
        .fontSize(14)
        .fontColor('#666666')
    }
    .width('100%')
    .height(60)
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      if (!this.isLoading) {
        this.loadTransactions();
      }
    })
  }

  @Builder
  TransactionDetailDialog() {
    Column() {
      Column() {
        Row() {
          Text('交易详情')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          Button($r('app.media.ic_close'))
            .width(24)
            .height(24)
            .backgroundColor(Color.Transparent)
            .onClick(() => this.showDetailDialog = false)
        }
        .margin({ bottom: 20 })

        Column() {
          this.DetailItem('交易编号', this.selectedTransaction!.transactionNo)
          this.DetailItem('交易类型', this.selectedTransaction!.transactionType)
          this.DetailItem('交易金额', `¥${this.selectedTransaction!.amount.toFixed(2)}`)
          this.DetailItem('支付方式', this.selectedTransaction!.paymentMethod)
          this.DetailItem('交易状态', this.selectedTransaction!.status)
          this.DetailItem('交易时间', this.selectedTransaction!.createTime)

          if (this.selectedTransaction!.cardNo) {
            this.DetailItem('银行卡', this.selectedTransaction!.cardNo)
          }

          this.DetailItem('交易描述', this.selectedTransaction!.description)
        }
      }
      .width('90%')
      .padding(20)
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('rgba(0,0,0,0.5)')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => this.showDetailDialog = false)
  }

  @Builder
  DetailItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width('30%')

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
    }
    .height(40)
    .margin({ bottom: 8 })
  }

  // ...其他工具方法保持不变...
}