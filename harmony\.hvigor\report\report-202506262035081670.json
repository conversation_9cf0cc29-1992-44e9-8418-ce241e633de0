{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "8bb85f93-8da0-49d5-9022-11eef3d87316", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 16696119055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3186c90f-ddb6-41b3-bc82-a6ea41245844", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 16696297635500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb23fa3-d90d-4a15-bb81-8cebc2aee281", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 16696298000000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78229342-e194-44cc-8db1-4c6cc856ff72", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096848096900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096861088400, "endTime": 17097581105600}, "additional": {"children": ["089e5090-ed9e-4c03-9bd0-05b2a7188a56", "c790c338-9607-4119-889b-ff431ab59d0d", "79e655fd-117f-4692-8215-a5ef8f967f20", "7079997a-330f-4acf-90e0-482f35a56b92", "d9f46378-1684-490b-a0b6-14c98bb88782", "6be5a076-88b2-443a-8aa3-34dd4c985fd1", "a45d3baa-6718-4d1f-914a-162497dbff76"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f45548b2-81a7-4440-a470-4d6901880b76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "089e5090-ed9e-4c03-9bd0-05b2a7188a56", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096861090900, "endTime": 17096924091300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "31ec2e79-07bb-4c74-ae24-ff912058bd98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c790c338-9607-4119-889b-ff431ab59d0d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096924121100, "endTime": 17097579690000}, "additional": {"children": ["02045493-60a8-4b62-bca7-a73a20698432", "12539efd-f5d7-46c0-ad79-c68d7b2cd29b", "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "c7e0cf74-a788-4d5f-87d6-dbc6057f12f2", "068d45c5-88ff-4b35-af7a-ef16260a5909", "560c1982-ff0a-40d7-bce3-5410acd222ec", "15c153b6-cdba-45b9-b44e-76332b0f91dc", "d12e29e5-23db-4aa2-962c-d92e2e896ebe", "0eb679d2-d4eb-4ad2-b60f-19fbbc556214"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "6abbac13-842b-4a3f-b054-b371a89a618d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79e655fd-117f-4692-8215-a5ef8f967f20", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097579713500, "endTime": 17097581096600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "d1889308-0d6c-44b6-96b0-4ddf06b852cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7079997a-330f-4acf-90e0-482f35a56b92", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097581101300, "endTime": 17097581102400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "60612f85-0cdc-4ac4-b223-c350ce524d24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9f46378-1684-490b-a0b6-14c98bb88782", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096866393500, "endTime": 17096866446800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "3e7cca40-5596-4b88-b80b-02b1b1baa7d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e7cca40-5596-4b88-b80b-02b1b1baa7d2", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096866393500, "endTime": 17096866446800}, "additional": {"logType": "info", "children": [], "durationId": "d9f46378-1684-490b-a0b6-14c98bb88782", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "6be5a076-88b2-443a-8aa3-34dd4c985fd1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096874768800, "endTime": 17096874788400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "2dbdc35b-94cd-469e-8f98-d7d96988befd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dbdc35b-94cd-469e-8f98-d7d96988befd", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096874768800, "endTime": 17096874788400}, "additional": {"logType": "info", "children": [], "durationId": "6be5a076-88b2-443a-8aa3-34dd4c985fd1", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "a061b9f4-cba0-4f1b-a230-f53342d5dde9", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096918641800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0857232f-411e-4d15-bc90-3a45347fec2e", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096923878200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ec2e79-07bb-4c74-ae24-ff912058bd98", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096861090900, "endTime": 17096924091300}, "additional": {"logType": "info", "children": [], "durationId": "089e5090-ed9e-4c03-9bd0-05b2a7188a56", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "02045493-60a8-4b62-bca7-a73a20698432", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096933756100, "endTime": 17096933765100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "db8d6cc3-fc8c-4e28-8d8b-1da8f9a0f546"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12539efd-f5d7-46c0-ad79-c68d7b2cd29b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096933784200, "endTime": 17096938803400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "7eb6aedb-8b70-41cf-ac23-1fa83bccbf66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096938817400, "endTime": 17097424083000}, "additional": {"children": ["9613e39f-2c4d-42ad-97ed-5a3c4949d11a", "a8a4fa4e-bff4-4549-8761-d090ac83099b", "dd954e2f-9580-47e5-9b91-e33423983102"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "255ce618-6356-4d0c-894d-f2fc09f7b585"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7e0cf74-a788-4d5f-87d6-dbc6057f12f2", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424098200, "endTime": 17097498508700}, "additional": {"children": ["2cca8ead-ee95-4c55-a584-f9ff49c72a35"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "a99f4586-46e0-4375-a503-500cef82bbe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "068d45c5-88ff-4b35-af7a-ef16260a5909", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097498543400, "endTime": 17097532155600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "2e3d77d4-5a22-4b1c-9754-5a4869ba54be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "560c1982-ff0a-40d7-bce3-5410acd222ec", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097533200500, "endTime": 17097542298800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "a9d3dba2-da29-44ff-b78c-4a83e5fd3568"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15c153b6-cdba-45b9-b44e-76332b0f91dc", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097542319700, "endTime": 17097579472700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "6f4fd1c7-8786-4d77-9a64-8365fa14b5bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d12e29e5-23db-4aa2-962c-d92e2e896ebe", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097579507000, "endTime": 17097579678000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "6c9a292e-7fef-4e63-9eff-7d946b6c08ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db8d6cc3-fc8c-4e28-8d8b-1da8f9a0f546", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096933756100, "endTime": 17096933765100}, "additional": {"logType": "info", "children": [], "durationId": "02045493-60a8-4b62-bca7-a73a20698432", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "7eb6aedb-8b70-41cf-ac23-1fa83bccbf66", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096933784200, "endTime": 17096938803400}, "additional": {"logType": "info", "children": [], "durationId": "12539efd-f5d7-46c0-ad79-c68d7b2cd29b", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "9613e39f-2c4d-42ad-97ed-5a3c4949d11a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096939540800, "endTime": 17096939559200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "logId": "8c98066c-366d-4b4a-b769-4050940427c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c98066c-366d-4b4a-b769-4050940427c6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096939540800, "endTime": 17096939559200}, "additional": {"logType": "info", "children": [], "durationId": "9613e39f-2c4d-42ad-97ed-5a3c4949d11a", "parent": "255ce618-6356-4d0c-894d-f2fc09f7b585"}}, {"head": {"id": "a8a4fa4e-bff4-4549-8761-d090ac83099b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096942507300, "endTime": 17097422753500}, "additional": {"children": ["ea6c2f53-aad0-4c92-b85b-4739c2f96ba5", "3a31be3f-796e-4437-8559-f517e8e162c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "logId": "a4b863ad-46c7-4794-88c9-4ef308636520"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea6c2f53-aad0-4c92-b85b-4739c2f96ba5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096942508700, "endTime": 17097281318800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8a4fa4e-bff4-4549-8761-d090ac83099b", "logId": "636e8976-a8cd-465b-90d2-adebaba8591b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a31be3f-796e-4437-8559-f517e8e162c5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097281356000, "endTime": 17097422739600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8a4fa4e-bff4-4549-8761-d090ac83099b", "logId": "0e0c0049-81e2-4350-9af1-90ceee25a4d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c2de14f-7b97-49d7-a92e-7a24756a9d49", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096942512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8e97858-6fec-4ce6-a806-3017f001232c", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097281171100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636e8976-a8cd-465b-90d2-adebaba8591b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096942508700, "endTime": 17097281318800}, "additional": {"logType": "info", "children": [], "durationId": "ea6c2f53-aad0-4c92-b85b-4739c2f96ba5", "parent": "a4b863ad-46c7-4794-88c9-4ef308636520"}}, {"head": {"id": "610a6d56-bd21-4f17-8c3d-15f69256a959", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097281372600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006a01d7-051c-4ae8-b040-fe40884b39ad", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097338398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29b1288-ad7b-401c-9b6e-7226a01cd569", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097338523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ac5562-c430-4050-a788-575d9ae1291c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097338657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0296620f-424b-4a2c-8402-8a58833a855f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097338748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fbafee1-41af-4209-9ac6-81693fa1c008", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097341655800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36f877ec-9435-411f-9d8c-b4be20fdc9e4", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097355183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742ed832-5f92-497b-ab71-f03820439ae6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097366290900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa8537b-df66-48d6-a1b6-809228fa6600", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097397994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5faaed94-d1a4-4a7f-b3a3-aaa2fde102b0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097398119500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "markType": "other"}}, {"head": {"id": "4b6df4d0-6492-4679-900a-f7f0e354a790", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097398166400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "markType": "other"}}, {"head": {"id": "378e09f2-aa8a-4ae7-892a-9194e7224480", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097422451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e9af78-295d-4320-a7ad-bbb5b010bce8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097422576200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84292e3-dd0a-4cfc-b184-674f8e3152e9", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097422640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d0ca0b-d7b0-425d-b93d-545071d7d1ab", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097422691500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0c0049-81e2-4350-9af1-90ceee25a4d4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097281356000, "endTime": 17097422739600}, "additional": {"logType": "info", "children": [], "durationId": "3a31be3f-796e-4437-8559-f517e8e162c5", "parent": "a4b863ad-46c7-4794-88c9-4ef308636520"}}, {"head": {"id": "a4b863ad-46c7-4794-88c9-4ef308636520", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096942507300, "endTime": 17097422753500}, "additional": {"logType": "info", "children": ["636e8976-a8cd-465b-90d2-adebaba8591b", "0e0c0049-81e2-4350-9af1-90ceee25a4d4"], "durationId": "a8a4fa4e-bff4-4549-8761-d090ac83099b", "parent": "255ce618-6356-4d0c-894d-f2fc09f7b585"}}, {"head": {"id": "dd954e2f-9580-47e5-9b91-e33423983102", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424052100, "endTime": 17097424067900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "logId": "fa6e4516-cc5e-4753-8dfb-d499ea9b53fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa6e4516-cc5e-4753-8dfb-d499ea9b53fd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424052100, "endTime": 17097424067900}, "additional": {"logType": "info", "children": [], "durationId": "dd954e2f-9580-47e5-9b91-e33423983102", "parent": "255ce618-6356-4d0c-894d-f2fc09f7b585"}}, {"head": {"id": "255ce618-6356-4d0c-894d-f2fc09f7b585", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096938817400, "endTime": 17097424083000}, "additional": {"logType": "info", "children": ["8c98066c-366d-4b4a-b769-4050940427c6", "a4b863ad-46c7-4794-88c9-4ef308636520", "fa6e4516-cc5e-4753-8dfb-d499ea9b53fd"], "durationId": "d653878b-c2b9-4b1f-86cc-6cb2e02dc75a", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "2cca8ead-ee95-4c55-a584-f9ff49c72a35", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424925100, "endTime": 17097498496200}, "additional": {"children": ["11f68edb-5d7a-4bad-9439-f1767732de53", "f63a96d8-48dc-4e2a-bf21-5cf030016eac", "6de07f4b-5580-48bf-b427-0ff347dec2a9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7e0cf74-a788-4d5f-87d6-dbc6057f12f2", "logId": "7ec65575-9fab-4e0a-81e7-f9102219c4f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11f68edb-5d7a-4bad-9439-f1767732de53", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097428471100, "endTime": 17097428486900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2cca8ead-ee95-4c55-a584-f9ff49c72a35", "logId": "f18ab6f8-a75e-4413-a21f-6983413882d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f18ab6f8-a75e-4413-a21f-6983413882d2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097428471100, "endTime": 17097428486900}, "additional": {"logType": "info", "children": [], "durationId": "11f68edb-5d7a-4bad-9439-f1767732de53", "parent": "7ec65575-9fab-4e0a-81e7-f9102219c4f9"}}, {"head": {"id": "f63a96d8-48dc-4e2a-bf21-5cf030016eac", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097430923800, "endTime": 17097496599600}, "additional": {"children": ["d86e10df-52e6-4880-b004-1471657460dd", "e48f028b-73f1-45e4-aece-e51ef21c705f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2cca8ead-ee95-4c55-a584-f9ff49c72a35", "logId": "7cbdaa9d-7aa7-4c71-9f7b-d5eccf8483ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d86e10df-52e6-4880-b004-1471657460dd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097430925100, "endTime": 17097434206900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f63a96d8-48dc-4e2a-bf21-5cf030016eac", "logId": "f8a51976-218d-4012-ae56-741178261409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e48f028b-73f1-45e4-aece-e51ef21c705f", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097434221900, "endTime": 17097496580500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f63a96d8-48dc-4e2a-bf21-5cf030016eac", "logId": "f777a059-6bee-46fe-b639-ab2cf36ff076"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c066763-bc29-4bf6-857a-fd4001fa7810", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097430929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e52916-ca44-4c62-b1a8-6629276e5323", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097434088500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a51976-218d-4012-ae56-741178261409", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097430925100, "endTime": 17097434206900}, "additional": {"logType": "info", "children": [], "durationId": "d86e10df-52e6-4880-b004-1471657460dd", "parent": "7cbdaa9d-7aa7-4c71-9f7b-d5eccf8483ea"}}, {"head": {"id": "d5779564-a727-4d31-9cec-52ad61cac632", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097434235900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c94ffce-b832-4eb0-a67c-de301c707c57", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097488319300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b0e6e9d-2cd8-4bcd-a203-5570a98f45e7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097488863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e86f0dd-0b0d-4832-b924-aaad6ee5a70a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097489383000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90207cc8-6c24-464b-b785-52170726bdec", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097490823700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77144ac0-1d92-45e0-8dd4-ac35b50d3fbb", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097491009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f23ea5-faad-442c-a5b6-4ae8d3939fa3", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097491131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba3f177-0a87-4df7-914a-55fa68b4164c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097491236600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337c3f13-167d-48a1-aba3-2428a68a86e8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097496127700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2bf8040-f262-46bb-923e-5aa4139c962f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097496280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8c2098-be2c-4a7d-b84f-e4f93917da9a", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097496351600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3a22c31-2a4d-4a86-baf9-cdf397a2ea07", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097496480000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f777a059-6bee-46fe-b639-ab2cf36ff076", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097434221900, "endTime": 17097496580500}, "additional": {"logType": "info", "children": [], "durationId": "e48f028b-73f1-45e4-aece-e51ef21c705f", "parent": "7cbdaa9d-7aa7-4c71-9f7b-d5eccf8483ea"}}, {"head": {"id": "7cbdaa9d-7aa7-4c71-9f7b-d5eccf8483ea", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097430923800, "endTime": 17097496599600}, "additional": {"logType": "info", "children": ["f8a51976-218d-4012-ae56-741178261409", "f777a059-6bee-46fe-b639-ab2cf36ff076"], "durationId": "f63a96d8-48dc-4e2a-bf21-5cf030016eac", "parent": "7ec65575-9fab-4e0a-81e7-f9102219c4f9"}}, {"head": {"id": "6de07f4b-5580-48bf-b427-0ff347dec2a9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097498455900, "endTime": 17097498468000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2cca8ead-ee95-4c55-a584-f9ff49c72a35", "logId": "cf6b97b1-8758-459f-bfd0-dc604e122dbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf6b97b1-8758-459f-bfd0-dc604e122dbe", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097498455900, "endTime": 17097498468000}, "additional": {"logType": "info", "children": [], "durationId": "6de07f4b-5580-48bf-b427-0ff347dec2a9", "parent": "7ec65575-9fab-4e0a-81e7-f9102219c4f9"}}, {"head": {"id": "7ec65575-9fab-4e0a-81e7-f9102219c4f9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424925100, "endTime": 17097498496200}, "additional": {"logType": "info", "children": ["f18ab6f8-a75e-4413-a21f-6983413882d2", "7cbdaa9d-7aa7-4c71-9f7b-d5eccf8483ea", "cf6b97b1-8758-459f-bfd0-dc604e122dbe"], "durationId": "2cca8ead-ee95-4c55-a584-f9ff49c72a35", "parent": "a99f4586-46e0-4375-a503-500cef82bbe0"}}, {"head": {"id": "a99f4586-46e0-4375-a503-500cef82bbe0", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097424098200, "endTime": 17097498508700}, "additional": {"logType": "info", "children": ["7ec65575-9fab-4e0a-81e7-f9102219c4f9"], "durationId": "c7e0cf74-a788-4d5f-87d6-dbc6057f12f2", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "6e918ee4-cd1a-44f3-a58d-650cd7295dfd", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097523667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5ee3aea-dee9-4a9c-af64-e1f4599a0872", "name": "hvigorfile, resolve hvigorfile dependencies in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097532037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3d77d4-5a22-4b1c-9754-5a4869ba54be", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097498543400, "endTime": 17097532155600}, "additional": {"logType": "info", "children": [], "durationId": "068d45c5-88ff-4b35-af7a-ef16260a5909", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "0eb679d2-d4eb-4ad2-b60f-19fbbc556214", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097532962400, "endTime": 17097533190300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c790c338-9607-4119-889b-ff431ab59d0d", "logId": "723929ed-dca0-48a9-97e6-c5611a93be19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1346a21e-0296-421c-9d8a-8df94233ceff", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097532979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "723929ed-dca0-48a9-97e6-c5611a93be19", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097532962400, "endTime": 17097533190300}, "additional": {"logType": "info", "children": [], "durationId": "0eb679d2-d4eb-4ad2-b60f-19fbbc556214", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "2cc3a06d-db00-4ba2-89e1-66303f6d0bb0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097534688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ac1c6c-b1e8-4dc6-a7fc-f22bb6751c75", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097541422900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d3dba2-da29-44ff-b78c-4a83e5fd3568", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097533200500, "endTime": 17097542298800}, "additional": {"logType": "info", "children": [], "durationId": "560c1982-ff0a-40d7-bce3-5410acd222ec", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "3792acdd-d1ea-46d1-af46-c26a69f0d9ef", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097542329900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137073a6-7a29-49ff-89c4-31ef6ac7e334", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097561897600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8d1e36-8d2f-4835-bac9-6b98ea48dbd3", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097562026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f0e41ec-efad-4eaa-a02f-a72bd18b0c40", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097562575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab72780f-e78f-4606-b596-05ce8ad74b58", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097565027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5b9cb2-72bc-4f3a-a1ee-a2307801fb99", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097565113700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4fd1c7-8786-4d77-9a64-8365fa14b5bf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097542319700, "endTime": 17097579472700}, "additional": {"logType": "info", "children": [], "durationId": "15c153b6-cdba-45b9-b44e-76332b0f91dc", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "3a646338-32e7-48bf-a2b3-be8ed5fa0717", "name": "Configuration phase cost:646 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097579531400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c9a292e-7fef-4e63-9eff-7d946b6c08ed", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097579507000, "endTime": 17097579678000}, "additional": {"logType": "info", "children": [], "durationId": "d12e29e5-23db-4aa2-962c-d92e2e896ebe", "parent": "6abbac13-842b-4a3f-b054-b371a89a618d"}}, {"head": {"id": "6abbac13-842b-4a3f-b054-b371a89a618d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096924121100, "endTime": 17097579690000}, "additional": {"logType": "info", "children": ["db8d6cc3-fc8c-4e28-8d8b-1da8f9a0f546", "7eb6aedb-8b70-41cf-ac23-1fa83bccbf66", "255ce618-6356-4d0c-894d-f2fc09f7b585", "a99f4586-46e0-4375-a503-500cef82bbe0", "2e3d77d4-5a22-4b1c-9754-5a4869ba54be", "a9d3dba2-da29-44ff-b78c-4a83e5fd3568", "6f4fd1c7-8786-4d77-9a64-8365fa14b5bf", "6c9a292e-7fef-4e63-9eff-7d946b6c08ed", "723929ed-dca0-48a9-97e6-c5611a93be19"], "durationId": "c790c338-9607-4119-889b-ff431ab59d0d", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "a45d3baa-6718-4d1f-914a-162497dbff76", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097581074300, "endTime": 17097581087700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14d43bea-e17b-4afc-94d8-0eb2558e0849", "logId": "e446ff2d-7a90-46af-8811-ee69d7106b71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e446ff2d-7a90-46af-8811-ee69d7106b71", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097581074300, "endTime": 17097581087700}, "additional": {"logType": "info", "children": [], "durationId": "a45d3baa-6718-4d1f-914a-162497dbff76", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "d1889308-0d6c-44b6-96b0-4ddf06b852cf", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097579713500, "endTime": 17097581096600}, "additional": {"logType": "info", "children": [], "durationId": "79e655fd-117f-4692-8215-a5ef8f967f20", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "60612f85-0cdc-4ac4-b223-c350ce524d24", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097581101300, "endTime": 17097581102400}, "additional": {"logType": "info", "children": [], "durationId": "7079997a-330f-4acf-90e0-482f35a56b92", "parent": "f45548b2-81a7-4440-a470-4d6901880b76"}}, {"head": {"id": "f45548b2-81a7-4440-a470-4d6901880b76", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096861088400, "endTime": 17097581105600}, "additional": {"logType": "info", "children": ["31ec2e79-07bb-4c74-ae24-ff912058bd98", "6abbac13-842b-4a3f-b054-b371a89a618d", "d1889308-0d6c-44b6-96b0-4ddf06b852cf", "60612f85-0cdc-4ac4-b223-c350ce524d24", "3e7cca40-5596-4b88-b80b-02b1b1baa7d2", "2dbdc35b-94cd-469e-8f98-d7d96988befd", "e446ff2d-7a90-46af-8811-ee69d7106b71"], "durationId": "14d43bea-e17b-4afc-94d8-0eb2558e0849"}}, {"head": {"id": "688a754f-4c0b-465e-afc7-ffc2bfa1965d", "name": "Configuration task cost before running: 727 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097581441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323fd60b-6224-4a8c-a6b5-6e6c5dd0349d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097587797700, "endTime": 17097597802100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "bea68c60-3210-4726-b542-780d93c3e0ee", "logId": "bb8e1b9a-9c25-4f79-bf57-5e5873ea8879"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bea68c60-3210-4726-b542-780d93c3e0ee", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097583449800}, "additional": {"logType": "detail", "children": [], "durationId": "323fd60b-6224-4a8c-a6b5-6e6c5dd0349d"}}, {"head": {"id": "dbf50950-2a4b-43fd-8b34-ed3c9c97fb9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097584050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b9acfd-76be-4761-9e93-8774fc7363c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097584166000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47bb73a-6726-4839-88db-4d0f645c930e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097587818000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6434fb5-4169-44fa-920a-f5dfb7b5dc83", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097597578500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20b93f3f-a205-440a-9c50-c1b56f42195a", "name": "entry : default@PreBuild cost memory 0.3372802734375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097597724100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb8e1b9a-9c25-4f79-bf57-5e5873ea8879", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097587797700, "endTime": 17097597802100}, "additional": {"logType": "info", "children": [], "durationId": "323fd60b-6224-4a8c-a6b5-6e6c5dd0349d"}}, {"head": {"id": "645a2f8f-ff6e-46af-855b-608d722e5820", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097603014300, "endTime": 17097605266100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "778b882e-2967-4935-bb94-856243f2d74f", "logId": "2a25a393-faa2-44d5-af38-0b24293a7cbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "778b882e-2967-4935-bb94-856243f2d74f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097601741700}, "additional": {"logType": "detail", "children": [], "durationId": "645a2f8f-ff6e-46af-855b-608d722e5820"}}, {"head": {"id": "427b9ee1-78c9-465d-9e61-2c7e37131f70", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097602267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec6f4970-0825-4d50-b9fe-fdec2af15130", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097602347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c1105e-c227-4510-8751-a6bf1c9ed694", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097603023800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a793089f-81fe-429d-b352-643c30091c10", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097605065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9657c1-4af8-48d4-bfc9-a1034474adbe", "name": "entry : default@MergeProfile cost memory 0.11061859130859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097605194700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a25a393-faa2-44d5-af38-0b24293a7cbc", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097603014300, "endTime": 17097605266100}, "additional": {"logType": "info", "children": [], "durationId": "645a2f8f-ff6e-46af-855b-608d722e5820"}}, {"head": {"id": "4f8ed9ee-e8db-481a-a727-b06afde27738", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097609007900, "endTime": 17097612005500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8ebc548b-ea13-49aa-b4f2-f8f8ff49ed90", "logId": "a592afa4-e453-49b8-8d56-870ef45d2cf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ebc548b-ea13-49aa-b4f2-f8f8ff49ed90", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097606939300}, "additional": {"logType": "detail", "children": [], "durationId": "4f8ed9ee-e8db-481a-a727-b06afde27738"}}, {"head": {"id": "974c590e-dff5-4670-80a9-53fe2dd2bcc2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097607475500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251d7036-dd35-4d95-bd90-35bb95a96be0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097607612000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9705219-2f28-4905-85fd-b81e9f07a65e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097609032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a0a720-1cde-4447-a7b5-5cb268edd6fd", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097610379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a893441-0da4-4293-901d-75f0a89e222b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097611808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f617e8-17c0-448b-b169-0690d744d180", "name": "entry : default@CreateBuildProfile cost memory 0.0963134765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097611914800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a592afa4-e453-49b8-8d56-870ef45d2cf3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097609007900, "endTime": 17097612005500}, "additional": {"logType": "info", "children": [], "durationId": "4f8ed9ee-e8db-481a-a727-b06afde27738"}}, {"head": {"id": "193911e2-fcfe-4563-b962-3d5b950760a3", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615249200, "endTime": 17097615681000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "58e7d28f-eaa3-418c-a4e2-ae6b95644c40", "logId": "c7609402-aa2f-4596-adb8-91159611013b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58e7d28f-eaa3-418c-a4e2-ae6b95644c40", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097613671900}, "additional": {"logType": "detail", "children": [], "durationId": "193911e2-fcfe-4563-b962-3d5b950760a3"}}, {"head": {"id": "f4735b2e-07c6-4de2-8850-f5a08b5f156c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097614197800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea15f662-2ebf-4d86-8b5a-efef4fdcf131", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097614294900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09a174c-0795-458d-93ec-70dbfe018e88", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615260800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9a895b-5725-4735-a257-36046fefdc56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615387000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "844a5772-c540-44f5-a49c-3f86e0d5e484", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5e442b-e63f-4cb5-91e2-5e154a639186", "name": "entry : default@PreCheckSyscap cost memory 0.0368194580078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63bb841-7869-4864-8c15-6060b28436b8", "name": "runTaskFromQueue task cost before running: 761 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7609402-aa2f-4596-adb8-91159611013b", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097615249200, "endTime": 17097615681000, "totalTime": 354100}, "additional": {"logType": "info", "children": [], "durationId": "193911e2-fcfe-4563-b962-3d5b950760a3"}}, {"head": {"id": "59c219af-a135-4697-bf71-4915c0f43a1c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097625490000, "endTime": 17097626931300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4c598d0e-6066-45a8-b38c-cfe6dd5ebc59", "logId": "017b5841-b771-469c-9848-5d1124c6b161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c598d0e-6066-45a8-b38c-cfe6dd5ebc59", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097617563000}, "additional": {"logType": "detail", "children": [], "durationId": "59c219af-a135-4697-bf71-4915c0f43a1c"}}, {"head": {"id": "52938db1-007f-48b4-bb2e-7c3d6ced67aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097618118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5fcd87-4522-4b0b-98ad-5dc3fcf0363a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097618197000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e310f9d8-dbcc-4eda-94f6-e18aa3ec9e8e", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097625508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "676c7665-9870-48f8-ba2b-5cf22d79fd3a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097625765800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0de93b-b654-4c59-9a8c-d0a678488828", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097626708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e69828e3-c1c5-48ac-bc54-e1c5616d328e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06491851806640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097626831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017b5841-b771-469c-9848-5d1124c6b161", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097625490000, "endTime": 17097626931300}, "additional": {"logType": "info", "children": [], "durationId": "59c219af-a135-4697-bf71-4915c0f43a1c"}}, {"head": {"id": "b2f0a00c-b213-4f95-9158-af29f7c7ae31", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097631337700, "endTime": 17097632746800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "eb06ce62-dfcd-49cf-bae2-5611332aeed2", "logId": "66d5b2ba-b4d0-413f-9817-5241174db08c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb06ce62-dfcd-49cf-bae2-5611332aeed2", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097629272700}, "additional": {"logType": "detail", "children": [], "durationId": "b2f0a00c-b213-4f95-9158-af29f7c7ae31"}}, {"head": {"id": "5aaf3cc3-4502-4484-b335-f00bff084d81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097629859500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e026a33-a839-4395-b99f-e1ec78af6e68", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097629970100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25961965-6852-49f5-add3-43bb56adaf09", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097631349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acaca929-defa-4063-9dee-9e959aeba60a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097632567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa074182-2a41-4fea-b666-c4aa50fd06f5", "name": "entry : default@ProcessProfile cost memory 0.05646514892578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097632672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d5b2ba-b4d0-413f-9817-5241174db08c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097631337700, "endTime": 17097632746800}, "additional": {"logType": "info", "children": [], "durationId": "b2f0a00c-b213-4f95-9158-af29f7c7ae31"}}, {"head": {"id": "813a3472-628d-48b3-9979-3d48828fe869", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097637310400, "endTime": 17097644663100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "baf63fa8-ecfc-4c38-8f48-65fa084fab3c", "logId": "bdb33ca4-e452-4a28-a47a-6923119a5fc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baf63fa8-ecfc-4c38-8f48-65fa084fab3c", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097634570100}, "additional": {"logType": "detail", "children": [], "durationId": "813a3472-628d-48b3-9979-3d48828fe869"}}, {"head": {"id": "4c91add3-7481-4014-a562-ee7683a35316", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097635183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399fa1db-5e97-4ba4-928a-e492a991b316", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097635273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "168a369f-8f46-4522-b729-e749d03d958e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097637323700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0183e32b-d90c-4d19-831c-1bb7a04f1931", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097644452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dccc64fb-b395-41ff-9dd4-5eb36b06379d", "name": "entry : default@ProcessRouterMap cost memory 0.18697357177734375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097644587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb33ca4-e452-4a28-a47a-6923119a5fc5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097637310400, "endTime": 17097644663100}, "additional": {"logType": "info", "children": [], "durationId": "813a3472-628d-48b3-9979-3d48828fe869"}}, {"head": {"id": "45548d1c-a491-47a8-b1c4-5bf1e8ef5ec1", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097652968000, "endTime": 17097655934100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8638f40b-ccb6-43ed-b731-50610b0cd029", "logId": "3da4472c-30df-4770-8ac1-f583f9b327eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8638f40b-ccb6-43ed-b731-50610b0cd029", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097648849100}, "additional": {"logType": "detail", "children": [], "durationId": "45548d1c-a491-47a8-b1c4-5bf1e8ef5ec1"}}, {"head": {"id": "fed17641-6891-4d11-89c6-1607895fe7a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097649442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e5d2d0-48a7-4f8b-a82a-747dd80ee90b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097649543300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b71487-382d-44f9-b52e-18a976e681b7", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097650703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12296b1c-1586-4797-b2e5-4fe874b5fe7c", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097654246600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d52d86-3ab9-4afb-b94b-66330f00bdcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097654391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df33dc6-de08-481c-aed2-e219accd7efd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097654451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31abc86-2a36-46e5-a40f-cefe7649143f", "name": "entry : default@PreviewProcessResource cost memory 0.06777191162109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097654533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8706c938-d077-4322-afca-39920ab65b7b", "name": "runTaskFromQueue task cost before running: 801 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097655833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da4472c-30df-4770-8ac1-f583f9b327eb", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097652968000, "endTime": 17097655934100, "totalTime": 1631100}, "additional": {"logType": "info", "children": [], "durationId": "45548d1c-a491-47a8-b1c4-5bf1e8ef5ec1"}}, {"head": {"id": "25178f05-b863-4c91-8fc8-cfde13b9f429", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097663913300, "endTime": 17097685811000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "063729e9-5c2e-49c6-b60c-d28162af5c34", "logId": "5a622b17-c3de-40bf-9750-cd385ed227a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "063729e9-5c2e-49c6-b60c-d28162af5c34", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097659450000}, "additional": {"logType": "detail", "children": [], "durationId": "25178f05-b863-4c91-8fc8-cfde13b9f429"}}, {"head": {"id": "0444a10c-b046-44ad-8fe6-253e41b1aea7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097660142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66c9abf-ce8f-4919-9245-574163de4029", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097660257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9dd38fb-98ec-4ede-adee-5aa1f97c188e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097663931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77250141-1974-4e92-adbb-a0b7837fa7c1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097685573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988190eb-e1ec-4290-96b4-5dd3f4e2929b", "name": "entry : default@GenerateLoaderJson cost memory 0.7085952758789062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097685729800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a622b17-c3de-40bf-9750-cd385ed227a3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097663913300, "endTime": 17097685811000}, "additional": {"logType": "info", "children": [], "durationId": "25178f05-b863-4c91-8fc8-cfde13b9f429"}}, {"head": {"id": "b207ba5e-3310-4458-9c35-eb801db2964e", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097699941100, "endTime": 17097722360000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "aa5a1c3c-98d4-47c9-9f04-230391432cba", "logId": "69650333-b798-4252-b394-200da56d4b44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa5a1c3c-98d4-47c9-9f04-230391432cba", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097695327000}, "additional": {"logType": "detail", "children": [], "durationId": "b207ba5e-3310-4458-9c35-eb801db2964e"}}, {"head": {"id": "43950173-2982-47ee-8149-a1dba1e71f98", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097695943800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85398f7e-fd81-46e4-a7bd-ccdcbc42533e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097696045100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c8c5e1d-6a1a-4287-836e-3f959e87b045", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097697049700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a75acbd2-c870-47b0-8533-641e487f0075", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097699964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea065ce3-45f5-412f-8a33-281b80d0ba22", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097722112800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac2ad16-42ef-404c-b633-c69ec6a1c4e1", "name": "entry : default@PreviewCompileResource cost memory 0.70654296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097722252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69650333-b798-4252-b394-200da56d4b44", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097699941100, "endTime": 17097722360000}, "additional": {"logType": "info", "children": [], "durationId": "b207ba5e-3310-4458-9c35-eb801db2964e"}}, {"head": {"id": "e64e8b7d-6b82-4ac3-b181-e77adc4c4888", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725727100, "endTime": 17097726283200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "353a8fb5-cd77-477e-aa7e-81526db3acb6", "logId": "6ac58ec4-d433-404f-99ed-733b0e649d15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "353a8fb5-cd77-477e-aa7e-81526db3acb6", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097724839900}, "additional": {"logType": "detail", "children": [], "durationId": "e64e8b7d-6b82-4ac3-b181-e77adc4c4888"}}, {"head": {"id": "3c775434-6ad5-43f4-a203-7dcb77eec15b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6349ae37-78b0-4646-a26c-08f0e8c7d0df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725593100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "561a82ef-b290-4547-8408-949d839a9350", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725737000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e82bbb-6429-4952-9dcd-fac8d6ad468b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725934400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba6ac81e-91a6-43d2-9551-f2d06dc1fb6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281469ab-caed-49f9-ba84-de9f6a5ddd16", "name": "entry : default@PreviewHookCompileResource cost memory 0.037933349609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097726100400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af2d4b98-c164-4ec5-a8af-d6d645a28ea0", "name": "runTaskFromQueue task cost before running: 871 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097726192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac58ec4-d433-404f-99ed-733b0e649d15", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097725727100, "endTime": 17097726283200, "totalTime": 444700}, "additional": {"logType": "info", "children": [], "durationId": "e64e8b7d-6b82-4ac3-b181-e77adc4c4888"}}, {"head": {"id": "d08cf9f7-a8da-4b2c-94f7-2a571d72f843", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097731448100, "endTime": 17097734767600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c6cf75b8-7a26-48a2-9470-5d91f62fee8f", "logId": "b32f8df2-7867-447e-bacc-b1d995ef3393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6cf75b8-7a26-48a2-9470-5d91f62fee8f", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097729370900}, "additional": {"logType": "detail", "children": [], "durationId": "d08cf9f7-a8da-4b2c-94f7-2a571d72f843"}}, {"head": {"id": "41aaa76b-9651-443c-b4f2-788c67dcdc79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097730193000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a09b08-2483-4e90-b3da-127c38ea9724", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097730337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf518970-1d4e-474e-982e-3a4a652f8af7", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097731491500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ef5fc0-9529-439a-8fcf-da29802c2f3d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097734578800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f299cb07-2eb6-44ae-8cdf-054e6dc1b8e1", "name": "entry : default@CopyPreviewProfile cost memory 0.09520721435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097734692400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32f8df2-7867-447e-bacc-b1d995ef3393", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097731448100, "endTime": 17097734767600}, "additional": {"logType": "info", "children": [], "durationId": "d08cf9f7-a8da-4b2c-94f7-2a571d72f843"}}, {"head": {"id": "fda3adff-d98a-460c-b1f0-c2de4d8ab61a", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738153800, "endTime": 17097738627100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ca621077-de7c-4724-9721-f4ba0219dc9f", "logId": "b5f01a27-0e9e-4958-b2c7-5ac794d961c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca621077-de7c-4724-9721-f4ba0219dc9f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097736589400}, "additional": {"logType": "detail", "children": [], "durationId": "fda3adff-d98a-460c-b1f0-c2de4d8ab61a"}}, {"head": {"id": "2ab02e5d-7d55-482d-945f-e6bd2c06270f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097737200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e774d5-e007-4216-8416-fb2f7dff59f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097737292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5112c027-2b94-470b-9514-e45273c1544b", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738165200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "426a5595-bac0-4b88-a7c2-b28250a030f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2442109-c3e9-43ee-8b81-a7654448f295", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0693ceaf-8759-42e0-8fe7-2d343d2dc789", "name": "entry : default@ReplacePreviewerPage cost memory 0.038726806640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738470900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7c134c-f675-4a05-8c6a-4a0b23148659", "name": "runTaskFromQueue task cost before running: 884 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738564100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f01a27-0e9e-4958-b2c7-5ac794d961c7", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097738153800, "endTime": 17097738627100, "totalTime": 391000}, "additional": {"logType": "info", "children": [], "durationId": "fda3adff-d98a-460c-b1f0-c2de4d8ab61a"}}, {"head": {"id": "38586c79-dca4-4203-8c61-2e86d02faee1", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097741684300, "endTime": 17097742180300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "976752ff-9ddb-4190-84f0-00f98ec5f1a1", "logId": "19496897-3fb5-4b55-8afc-5da3fe46af5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "976752ff-9ddb-4190-84f0-00f98ec5f1a1", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097741595100}, "additional": {"logType": "detail", "children": [], "durationId": "38586c79-dca4-4203-8c61-2e86d02faee1"}}, {"head": {"id": "91583190-fa0c-43f8-8eb8-90ed08ff00c7", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097741696100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "339ecf56-11a6-4edf-95ff-a858aff76f57", "name": "entry : buildPreviewerResource cost memory 0.01165008544921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097741905200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc269c6-e6eb-4c83-abea-20d2c0b9791e", "name": "runTaskFromQueue task cost before running: 887 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097742059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19496897-3fb5-4b55-8afc-5da3fe46af5d", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097741684300, "endTime": 17097742180300, "totalTime": 348100}, "additional": {"logType": "info", "children": [], "durationId": "38586c79-dca4-4203-8c61-2e86d02faee1"}}, {"head": {"id": "2ad6895d-a169-496b-ac08-31d7f227b33b", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097746877400, "endTime": 17097749627700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "07dbbbd4-2bd9-45ba-9cca-e396f50a4477", "logId": "d5d34211-80db-425b-a63d-129b80f78f89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07dbbbd4-2bd9-45ba-9cca-e396f50a4477", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097745390900}, "additional": {"logType": "detail", "children": [], "durationId": "2ad6895d-a169-496b-ac08-31d7f227b33b"}}, {"head": {"id": "4d6b0e96-a0fb-48a5-8e9c-167f655fa6b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097745961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb011d4-7f31-4185-9575-b3e46a4b0a58", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097746063100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1222f01-6e49-4de7-914b-a77102c33c02", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097746887600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ea0c61-1618-4608-892f-c8fb5147134a", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097749433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d30a30-ad28-4896-99cd-d4687a68d2b9", "name": "entry : default@PreviewUpdateAssets cost memory 0.113739013671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097749550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d34211-80db-425b-a63d-129b80f78f89", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097746877400, "endTime": 17097749627700}, "additional": {"logType": "info", "children": [], "durationId": "2ad6895d-a169-496b-ac08-31d7f227b33b"}}, {"head": {"id": "3c73dc6b-8b36-4dee-b789-60e893c67c9e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097758336300}, "additional": {"children": ["c1ff4271-c56d-4ec3-88f4-610821983d31"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "61732f7a-0ae8-4233-a576-b9bc459f1c09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61732f7a-0ae8-4233-a576-b9bc459f1c09", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097752465800}, "additional": {"logType": "detail", "children": [], "durationId": "3c73dc6b-8b36-4dee-b789-60e893c67c9e"}}, {"head": {"id": "30645111-8a60-4c09-9627-1646d3e3d152", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097753000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26610568-68ff-44c6-8a05-990e20353eb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097753100600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ddae11-cd58-4168-ae00-187f8078a070", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097758348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ff4271-c56d-4ec3-88f4-610821983d31", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17097779574900}, "additional": {"children": [], "state": "running", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3c73dc6b-8b36-4dee-b789-60e893c67c9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41577565-2dbe-42ce-bbd3-fd53b2468901", "name": "entry : default@PreviewArkTS cost memory 0.9553146362304688", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097781775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc21d03-f740-4b4e-a4e4-cfd2316e3126", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097783473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b31b45da-592a-420a-a59b-5b3c304db296", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097791627300, "endTime": 17097791655700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8fd61c2-c574-4de2-9ca1-83807f399e89", "logId": "970d3bed-925e-4a44-b676-3008f9522b8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "970d3bed-925e-4a44-b676-3008f9522b8a", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097791627300, "endTime": 17097791655700}, "additional": {"logType": "info", "children": [], "durationId": "b31b45da-592a-420a-a59b-5b3c304db296"}}, {"head": {"id": "2ad15023-c726-408a-9727-ec5f2f2f4eaf", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17096855229500, "endTime": 17097791812800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "2e67cf3e-3459-40e5-9228-3bd47340875a", "name": "BUILD FAILED in 937 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097791839500}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "b0ff895c-1141-49fa-82c5-c10e419b5d52", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13ad3c5-875d-4d8e-be3e-7d539db441cb", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bc407d-6b6a-4b14-bf7f-ffcc162ba4a9", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792211800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6967931d-92f9-4638-9e87-d85be5b461a3", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ddf13e4-6c8f-4975-b054-933347c50185", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792311600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89de9095-0ab2-4fa0-ac8b-881be94efbce", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7a90bf-abd6-4f6f-9e96-43758ade1e52", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "941abcf0-7008-4d3d-bc38-02618c5fa175", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792446200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e93f921-b198-418b-b824-506b089b900d", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792491800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c3944eb-d6d0-49af-8e11-eb738439dc04", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097792537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5d0de7-b139-4881-b926-14e226f02ca6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097795980200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f179286-7c14-446f-843a-2ad5023526a9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097796923900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b4457c-2277-4383-a097-04d1749ec462", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097797309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b91c9c-dc63-46b8-9e51-e52e596f48f6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097797661200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a9a420-dfff-42e5-8c29-33341e4e0a43", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097798695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05e9697-a846-47a6-887f-21b7e45a683b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097815344400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c28894f-6582-41f5-8e74-5c508f5bee37", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097816077700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e69240-0d98-4c34-ac95-008a74eee350", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097816546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c608bef-b0c9-49a5-80cc-7c34f4cbb977", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097817009000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd7839b-f897-4a85-b5b4-96eac690a9ce", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:25 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097817365500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}