-- 简单的数据库修复脚本
USE e_wallet;

-- 1. 直接添加字段（忽略错误）
ALTER TABLE transaction ADD COLUMN status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '交易状态';
ALTER TABLE transaction ADD COLUMN description VARCHAR(200) COMMENT '交易描述';

-- 2. 更新现有记录
UPDATE transaction SET status = 'SUCCESS' WHERE status IS NULL;

-- 3. 清理测试数据
DELETE FROM transaction;

-- 4. 重置余额
UPDATE user SET account = 1000.00 WHERE id = 1;
UPDATE user SET account = 500.00 WHERE id = 2;
UPDATE bank_card SET balance = 5000.00 WHERE user_id = 1 AND card_number = '****************';
UPDATE bank_card SET balance = 3000.00 WHERE user_id = 2;

-- 5. 插入测试数据
INSERT INTO transaction (user_id, transaction_type, amount, payment_method, payment_channel, target_account, card_id, status) VALUES
(1, 'DEPOSIT', 500.00, 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS'),
(1, 'WITHDRAW', 200.00, 'BANK_CARD', 'MERCHANT', '中国银行', 1, 'SUCCESS');

-- 6. 查看结果
SELECT * FROM transaction;
SELECT id, username, account FROM user;
SELECT id, user_id, card_number, bank_name, balance FROM bank_card WHERE bind_status = 1;
