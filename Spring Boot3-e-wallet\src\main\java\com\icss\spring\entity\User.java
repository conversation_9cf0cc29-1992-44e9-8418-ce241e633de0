package com.icss.spring.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class User implements Serializable {
    private Long id;
    private String username;
    private String password;
    private String realName;  // 真实姓名
    private String mobile;
    private String email;     // 邮箱
    private BigDecimal account;
    private Date createTime;
    private BigDecimal balance;
    private String paymentPassword; // 支付密码
    private BigDecimal paymentLimit;

    public User() {}
    public User(Long id, String username, String password, String realName, String mobile, String email,
                BigDecimal account, Date createTime, BigDecimal balance,
                String paymentPassword, BigDecimal paymentLimit) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.mobile = mobile;
        this.email = email;
        this.account = account;
        this.createTime = createTime;
        this.balance = balance;
        this.paymentPassword = paymentPassword;
        this.paymentLimit = paymentLimit;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getAccount() {
        return account;
    }

    public void setAccount(BigDecimal account) {
        this.account = account;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getPaymentPassword() {
        return paymentPassword;
    }

    public void setPaymentPassword(String paymentPassword) {
        this.paymentPassword = paymentPassword;
    }

    public BigDecimal getPaymentLimit() {
        return paymentLimit;
    }

    public void setPaymentLimit(BigDecimal paymentLimit) {
        this.paymentLimit = paymentLimit;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", realName='" + realName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", email='" + email + '\'' +
                ", account=" + account +
                ", createTime=" + createTime +
                ", balance=" + balance +
                ", paymentPassword='" + paymentPassword + '\'' +
                ", paymentLimit=" + paymentLimit +
                '}';
    }
}