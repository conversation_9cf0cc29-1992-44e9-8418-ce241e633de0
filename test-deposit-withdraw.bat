@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 充值提现功能测试脚本
echo ========================================
echo.

echo 📋 测试计划:
echo 1. 重启后端服务
echo 2. 测试充值功能
echo 3. 测试提现功能
echo 4. 验证交易记录
echo 5. 检查余额变化
echo.

REM 1. 重启后端服务
echo 🚀 重启后端服务...
taskkill /f /im java.exe >nul 2>&1
timeout /t 3 >nul

if exist "Spring Boot3-e-wallet\target\spring-e-wallet-1.0-SNAPSHOT.jar" (
    echo ✅ 启动Spring Boot应用...
    cd "Spring Boot3-e-wallet"
    start /min java -jar target\spring-e-wallet-1.0-SNAPSHOT.jar
    cd ..
    
    echo ⏳ 等待服务启动 (20秒)...
    timeout /t 20 >nul
) else (
    echo ❌ 未找到JAR文件，请使用IDE启动
    pause
    exit /b 1
)

REM 2. 测试充值功能
echo.
echo 💰 测试充值功能...
echo ----------------------------------------

echo 测试充值API调用:
curl -s -X POST "http://localhost:8080/bank/deposit" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=1&amount=500.00" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo 查询用户余额:
curl -s "http://localhost:8080/bank/balance/1" -w "\n状态码: %%{http_code}\n"

echo.
echo 查询交易记录:
curl -s "http://localhost:8080/bank/transactions/1" -w "\n状态码: %%{http_code}\n"

REM 3. 测试提现功能
echo.
echo 💸 测试提现功能...
echo ----------------------------------------

echo 测试提现API调用:
curl -s -X POST "http://localhost:8080/bank/withdraw" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=1&amount=200.00" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo 再次查询用户余额:
curl -s "http://localhost:8080/bank/balance/1" -w "\n状态码: %%{http_code}\n"

echo.
echo 再次查询交易记录:
curl -s "http://localhost:8080/bank/transactions/1" -w "\n状态码: %%{http_code}\n"

REM 4. 测试银行卡列表
echo.
echo 🏦 查询银行卡信息...
echo ----------------------------------------

echo 查询用户银行卡:
curl -s "http://localhost:8080/bank/cards/1" -w "\n状态码: %%{http_code}\n"

REM 5. 测试错误情况
echo.
echo ❌ 测试错误情况...
echo ----------------------------------------

echo 测试余额不足的提现:
curl -s -X POST "http://localhost:8080/bank/withdraw" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=1&amount=99999.00" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo 测试无效的银行卡充值:
curl -s -X POST "http://localhost:8080/bank/deposit" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=999&amount=100.00" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo ========================================
echo 🎯 测试完成！
echo ========================================
echo.
echo 💡 如果看到错误，请检查:
echo   1. 后端服务是否正常启动
echo   2. 数据库连接是否正常
echo   3. 数据库表结构是否正确
echo   4. 测试数据是否存在
echo.
echo 🌐 打开前端进行手动测试:
start http://localhost:5173
echo.
pause
