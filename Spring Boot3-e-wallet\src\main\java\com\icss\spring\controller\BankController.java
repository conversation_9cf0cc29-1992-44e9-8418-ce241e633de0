package com.icss.spring.controller;

import com.icss.spring.entity.BankCard;
import com.icss.spring.entity.Transaction;
import com.icss.spring.result.ApiException;
import com.icss.spring.result.Result;
import com.icss.spring.service.BankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
@CrossOrigin
@RestController
@RequestMapping("/bank")
public class BankController {
    @Autowired
    private BankService bankService;

    // 获取所有银行卡
    @GetMapping("/cards/{userId}")
    public Result<List<BankCard>> getAllCards(@PathVariable Long userId) {
        List<BankCard> cards = bankService.getUserCards(userId);
        return Result.OK("查询成功", cards);
    }

    // 获取已绑定银行卡
    @GetMapping("/bound-cards/{userId}")
    public Result<List<BankCard>> getBoundCards(@PathVariable Long userId) {
        List<BankCard> cards = bankService.getBoundCards(userId);
        return Result.OK("查询成功", cards);
    }

    // 获取银行卡详情
    @GetMapping("/card/{cardId}")
    public Result<BankCard> getCardDetail(@PathVariable Long cardId) {
        BankCard card = bankService.getCardDetail(cardId);
        return Result.OK("查询成功", card);
    }

    // 添加银行卡
    @PostMapping("/card")
    public Result addBankCard(@RequestBody BankCard bankCard) {
        try {
            // 设置默认值
            if (bankCard.getBindStatus() == null) {
                bankCard.setBindStatus(1); // 默认绑定
            }
            if (bankCard.getBalance() == null) {
                bankCard.setBalance(BigDecimal.ZERO);
            }
            if (bankCard.getCreditLimit() == null) {
                bankCard.setCreditLimit(BigDecimal.ZERO);
            }

            int count = bankService.addBankCard(bankCard);
            return count > 0 ? Result.OK("添加成功") : Result.error("添加失败");
        } catch (Exception e) {
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    // 绑定银行卡（添加新银行卡）
    @PostMapping("/bind")
    public Result bindCard(@RequestParam Long userId,
                          @RequestParam String bankName,
                          @RequestParam String cardNumber,
                          @RequestParam String cardType,
                          @RequestParam BigDecimal balance,
                          @RequestParam BigDecimal creditLimit) {
        BankCard bankCard = new BankCard();
        bankCard.setUserId(userId);
        bankCard.setBankName(bankName);
        bankCard.setCardNumber(cardNumber);
        bankCard.setCardType(cardType);
        bankCard.setBalance(balance);
        bankCard.setCreditLimit(creditLimit);
        bankCard.setBindStatus(1); // 默认绑定状态

        int count = bankService.addBankCard(bankCard);
        return count > 0 ? Result.OK("添加成功") : Result.error("添加失败");
    }

    // 绑定已存在的银行卡
    @PostMapping("/bind/{cardId}")
    public Result bindExistingCard(@PathVariable Long cardId) {
        int count = bankService.bindCard(cardId);
        return count > 0 ? Result.OK("绑定成功") : Result.error("绑定失败");
    }

    // 解绑银行卡
    @PostMapping("/unbind/{cardId}")
    public Result unbindCard(@PathVariable Long cardId) {
        try {
            int count = bankService.unbindCard(cardId);
            return count > 0 ? Result.OK("解绑成功") : Result.error("解绑失败");
        } catch (Exception e) {
            return Result.error("解绑失败：" + e.getMessage());
        }
    }

    // 修改银行卡信息
    @PutMapping("/card/{cardId}")
    public Result updateBankCard(@PathVariable Long cardId, @RequestBody BankCard bankCard) {
        try {
            bankCard.setId(cardId);
            int count = bankService.updateBankCard(bankCard);
            return count > 0 ? Result.OK("修改成功") : Result.error("修改失败");
        } catch (Exception e) {
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    // 删除银行卡
    @DeleteMapping("/card/{cardId}")
    public Result deleteBankCard(@PathVariable Long cardId) {
        try {
            int count = bankService.deleteBankCard(cardId);
            return count > 0 ? Result.OK("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // 充值
    @PostMapping("/deposit")
    public Result deposit(@RequestParam Long userId,
                          @RequestParam Long cardId,
                          @RequestParam BigDecimal amount) {
        try {
            bankService.deposit(userId, cardId, amount);
            return Result.OK("充值成功");
        } catch (ApiException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("充值失败：" + e.getMessage());
        }
    }

    // 提现
    @PostMapping("/withdraw")
    public Result withdraw(@RequestParam Long userId,
                           @RequestParam Long cardId,
                           @RequestParam BigDecimal amount) {
        try {
            bankService.withdraw(userId, cardId, amount);
            return Result.OK("提现成功");
        } catch (ApiException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("提现失败：" + e.getMessage());
        }
    }

    // 钱包支付
    @PostMapping("/pay/wallet")
    public Result payWithWallet(@RequestParam Long userId,
                                @RequestParam BigDecimal amount,
                                @RequestParam String paymentChannel,
                                @RequestParam String targetAccount) {
        try {
            bankService.payWithWallet(userId, amount, paymentChannel, targetAccount);
            return Result.OK("支付成功");
        } catch (ApiException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("支付失败：" + e.getMessage());
        }
    }

    // 银行卡支付
    @PostMapping("/pay/card")
    public Result payWithCard(@RequestParam Long userId,
                              @RequestParam Long cardId,
                              @RequestParam BigDecimal amount,
                              @RequestParam String paymentChannel,
                              @RequestParam String targetAccount) {
        try {
            bankService.payWithCard(userId, cardId, amount, paymentChannel, targetAccount);
            return Result.OK("支付成功");
        } catch (ApiException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("支付失败：" + e.getMessage());
        }
    }

    // 转账 - 支持银行卡号
    @PostMapping("/transfer")
    public Result transfer(@RequestParam Long fromUserId,
                           @RequestParam String targetAccount,
                           @RequestParam BigDecimal amount,
                           @RequestParam(required = false) String paymentMethod,
                           @RequestParam(required = false) Long cardId) {
        try {
            bankService.transfer(fromUserId, targetAccount, amount, paymentMethod, cardId);
            return Result.OK("转账成功");
        } catch (ApiException e) {
            return Result.error(e.getMessage());
        }
    }

    // 获取所有交易记录
    @GetMapping("/transactions/{userId}")
    public Result<List<Transaction>> getTransactions(@PathVariable Long userId) {
        List<Transaction> transactions = bankService.getTransactions(userId);
        return Result.OK("查询成功", transactions);
    }

    // 按类型获取交易记录
    @GetMapping("/transactions/{userId}/type/{type}")
    public Result<List<Transaction>> getTransactionsByType(@PathVariable Long userId, @PathVariable String type) {
        List<Transaction> transactions = bankService.getTransactionsByType(userId, type);
        return Result.OK("查询成功", transactions);
    }

    // 按支付方式获取交易记录
    @GetMapping("/transactions/{userId}/method/{method}")
    public Result<List<Transaction>> getTransactionsByPaymentMethod(@PathVariable Long userId, @PathVariable String method) {
        List<Transaction> transactions = bankService.getTransactionsByPaymentMethod(userId, method);
        return Result.OK("查询成功", transactions);
    }

    // 删除交易记录
    @DeleteMapping("/transactions/{transactionId}")
    public Result deleteTransaction(@PathVariable Long transactionId) {
        try {
            int count = bankService.deleteTransaction(transactionId);
            return count > 0 ? Result.OK("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // 删除重复交易记录
    @DeleteMapping("/transactions/{userId}/duplicates")
    public Result removeDuplicateTransactions(@PathVariable Long userId) {
        try {
            int count = bankService.removeDuplicateTransactions(userId);
            return Result.OK("删除重复记录成功", count);
        } catch (Exception e) {
            return Result.error("删除重复记录失败：" + e.getMessage());
        }
    }

    // 获取余额
    @GetMapping("/balance/{userId}")
    public Result<BigDecimal> getBalance(@PathVariable Long userId) {
        BigDecimal balance = bankService.getBalance(userId);
        return Result.OK("查询成功", balance);
    }

    // 根据银行卡号查询银行卡信息
    @GetMapping("/card/number/{cardNumber}")
    public Result<BankCard> getCardByNumber(@PathVariable String cardNumber) {
        try {
            BankCard card = bankService.getCardByCardNumber(cardNumber);
            if (card == null) {
                return Result.error("查询不到该银行卡号");
            }
            return Result.OK("查询成功", card);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    // 测试连接端点
    @GetMapping("/test")
    public Result testConnection() {
        return Result.OK("后端连接正常", "银行卡服务运行中");
    }

    // 测试所有HTTP方法的端点
    @RequestMapping(value = "/test-methods", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public Result testMethods() {
        return Result.OK("HTTP方法测试成功", "支持GET, POST, PUT, DELETE");
    }
}