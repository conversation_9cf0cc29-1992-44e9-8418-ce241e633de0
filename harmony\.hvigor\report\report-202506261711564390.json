{"version": "2.0", "ppid": 2480, "events": [{"head": {"id": "05ef7dd3-9a1f-4cee-bfb6-65125ca8d2bb", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 737648980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a8dd34-fae5-49eb-9fa5-7a614b7b5f4d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 2320810221900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59b0fc8-90ee-4161-8130-8b606225e776", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 2322108167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728134ae-ef6b-48d9-851d-6d58fc5b4956", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4902541413600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed14830a-f98e-45ea-9786-e423413085ee", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905005043700, "endTime": 4911302616900}, "additional": {"children": ["c77a9518-5248-4104-abd8-1d1f9c581e85", "8d0abcff-ed63-43ba-80fa-2c680fc10321", "d2348f04-bb10-40ad-acf4-07f9f47321ed", "91a1b9ed-6274-421d-8f73-ae36664369e8", "aaf840fe-4adf-4857-a813-7fdb7cc288f4", "a5cdc5c6-e6ba-4cfa-b27c-813862a80f77", "57526912-7504-4029-b5c9-2d72415b6efa"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c77a9518-5248-4104-abd8-1d1f9c581e85", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905005052800, "endTime": 4905854310300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "8741fae2-828d-4323-841f-899052912081"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905854331600, "endTime": 4911300908700}, "additional": {"children": ["b6bfe92f-612e-4a05-9724-a7aaedc1b58f", "4afa526b-bba9-4285-b059-abfa3073b5e0", "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "9258ea89-ba28-4592-b767-ba661db23980", "6a41454d-c8db-4ee0-b056-fdb7a10e5dd7", "ea1cc8b7-f40d-4398-bc3a-51ffa76bdcb2", "1ed361d8-aed4-411b-8049-958d8c20068a", "e21dd21f-03f4-40ca-b5f5-9e15c4b2aab6", "7cd4b658-45b2-4e3c-b1ab-dc8634742373"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "b5668217-1c90-4a65-bbee-d19056913f92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2348f04-bb10-40ad-acf4-07f9f47321ed", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911300944200, "endTime": 4911302577000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "977726c6-5f29-4944-9952-fd284e089a3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91a1b9ed-6274-421d-8f73-ae36664369e8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911302583700, "endTime": 4911302610000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "607c9c19-38e8-47e6-a1dc-3db579fb76df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaf840fe-4adf-4857-a813-7fdb7cc288f4", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905283769700, "endTime": 4905333060000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "a6b9b601-d12c-4eec-9b17-50607e4e8810"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6b9b601-d12c-4eec-9b17-50607e4e8810", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905283769700, "endTime": 4905333060000}, "additional": {"logType": "info", "children": [], "durationId": "aaf840fe-4adf-4857-a813-7fdb7cc288f4", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "a5cdc5c6-e6ba-4cfa-b27c-813862a80f77", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905471912900, "endTime": 4905471931700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "7be6598b-d055-405a-93ff-38e6c2cdbad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7be6598b-d055-405a-93ff-38e6c2cdbad2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905471912900, "endTime": 4905471931700}, "additional": {"logType": "info", "children": [], "durationId": "a5cdc5c6-e6ba-4cfa-b27c-813862a80f77", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "e2fce994-deab-4d21-bed8-f73fce67dd14", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905522921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0154418-436d-4f36-94d9-5e013bd46003", "name": "Cache service initialization finished in 318 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905854140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8741fae2-828d-4323-841f-899052912081", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905005052800, "endTime": 4905854310300}, "additional": {"logType": "info", "children": [], "durationId": "c77a9518-5248-4104-abd8-1d1f9c581e85", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "b6bfe92f-612e-4a05-9724-a7aaedc1b58f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905883529300, "endTime": 4905883544900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "be5aaf7f-5ad6-46f4-904f-60a66fa8afc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4afa526b-bba9-4285-b059-abfa3073b5e0", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905883572500, "endTime": 4905931042100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "ce4aedfc-ce28-4b22-89d9-202225ca139a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905931061000, "endTime": 4909583594600}, "additional": {"children": ["02c85222-8ff2-48fd-80a4-fe031ec07116", "8310b91d-c6f5-4420-9b2f-22fcfb00299d", "2e92d6ef-1ac2-4720-ab1a-97bb9217d038"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "361e1158-3822-4f5c-aed4-92e2d6a4036d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9258ea89-ba28-4592-b767-ba661db23980", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909583625600, "endTime": 4909883889200}, "additional": {"children": ["6fdfddad-83ba-4004-a494-1ac9c6e8f474"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "8c1e06c1-f5c9-4f57-a04d-236e270517a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a41454d-c8db-4ee0-b056-fdb7a10e5dd7", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909883905200, "endTime": 4910566771800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "2f23b7a1-7bfb-450a-a80e-212b1ebf471d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea1cc8b7-f40d-4398-bc3a-51ffa76bdcb2", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910568119200, "endTime": 4910887899000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "80660dc3-9f5a-4848-a344-115942a253c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ed361d8-aed4-411b-8049-958d8c20068a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910887931700, "endTime": 4911300561100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "8c8a7059-c06e-4aa3-9c7a-68cc0fd46c74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e21dd21f-03f4-40ca-b5f5-9e15c4b2aab6", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911300639600, "endTime": 4911300883300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "4808c2b7-d588-48fa-a9cc-99b4060a5e6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5aaf7f-5ad6-46f4-904f-60a66fa8afc7", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905883529300, "endTime": 4905883544900}, "additional": {"logType": "info", "children": [], "durationId": "b6bfe92f-612e-4a05-9724-a7aaedc1b58f", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "ce4aedfc-ce28-4b22-89d9-202225ca139a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905883572500, "endTime": 4905931042100}, "additional": {"logType": "info", "children": [], "durationId": "4afa526b-bba9-4285-b059-abfa3073b5e0", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "02c85222-8ff2-48fd-80a4-fe031ec07116", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905942857900, "endTime": 4905942881300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "logId": "e486298a-5359-478d-a041-9f37dfec10e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e486298a-5359-478d-a041-9f37dfec10e7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905942857900, "endTime": 4905942881300}, "additional": {"logType": "info", "children": [], "durationId": "02c85222-8ff2-48fd-80a4-fe031ec07116", "parent": "361e1158-3822-4f5c-aed4-92e2d6a4036d"}}, {"head": {"id": "8310b91d-c6f5-4420-9b2f-22fcfb00299d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4906049743000, "endTime": 4909578441500}, "additional": {"children": ["52d5d74a-2f78-4032-8759-8a6fdc07dcb7", "8dfacca2-67ec-48f7-84f5-c3e1ae9321bd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "logId": "9282dde6-f431-46da-b1f6-2e3dbfc84b9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52d5d74a-2f78-4032-8759-8a6fdc07dcb7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4906049745900, "endTime": 4907248412300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8310b91d-c6f5-4420-9b2f-22fcfb00299d", "logId": "f18702db-7d50-4dde-88e0-59f9cb811055"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dfacca2-67ec-48f7-84f5-c3e1ae9321bd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907248450600, "endTime": 4909578411200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8310b91d-c6f5-4420-9b2f-22fcfb00299d", "logId": "782e327f-c3dc-4eff-a23c-bd765e903c43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7167a20a-ae24-4ac2-8821-eb6ce162cb9e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4906049759000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4431a8e8-615f-40be-9d64-2d9c98c18f3b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907248115400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18702db-7d50-4dde-88e0-59f9cb811055", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4906049745900, "endTime": 4907248412300}, "additional": {"logType": "info", "children": [], "durationId": "52d5d74a-2f78-4032-8759-8a6fdc07dcb7", "parent": "9282dde6-f431-46da-b1f6-2e3dbfc84b9a"}}, {"head": {"id": "49ea23c7-f3e7-48e2-a95d-50980568c1f2", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907248497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a85f3ed7-1720-42ff-a085-d1fc3e27402c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907777402300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2bfcf5-e415-4c4b-bc14-93648087cc0b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907778316400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90a40f6-0bad-45fe-80fb-e1c60c34383c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907788859800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca6f28e-9e59-4066-b059-55f9e6fa4f1e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907803616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de14ca1d-229f-4366-bc2b-7af635ebbfa8", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907870043100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1822c1e-78fd-4ab7-b2bf-f3b239eb3ee4", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4908134468900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eacab3e6-ca35-4f55-9985-e0cc7e956792", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4908662805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e37ab9-2ca4-461b-90f6-2a9fd1e61b79", "name": "Sdk init in 727 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4908909960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16064fcd-d29c-49b4-b6ea-80ce5d5809f0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4908932139200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 12}, "markType": "other"}}, {"head": {"id": "d3d614da-c6af-48df-a4f0-2631f7fd28af", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4908940769900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 12}, "markType": "other"}}, {"head": {"id": "f0cb4f35-d353-470a-9932-d945ea87f922", "name": "Project task initialization takes 588 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909548584100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8674c291-e3b0-45e1-b04e-c8ce71d9d1af", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909577819300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea153a53-7306-4428-af23-634fee7d45ce", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909578135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdcc51bf-4526-431d-94e2-d5353e8e7235", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909578281800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "782e327f-c3dc-4eff-a23c-bd765e903c43", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4907248450600, "endTime": 4909578411200}, "additional": {"logType": "info", "children": [], "durationId": "8dfacca2-67ec-48f7-84f5-c3e1ae9321bd", "parent": "9282dde6-f431-46da-b1f6-2e3dbfc84b9a"}}, {"head": {"id": "9282dde6-f431-46da-b1f6-2e3dbfc84b9a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4906049743000, "endTime": 4909578441500}, "additional": {"logType": "info", "children": ["f18702db-7d50-4dde-88e0-59f9cb811055", "782e327f-c3dc-4eff-a23c-bd765e903c43"], "durationId": "8310b91d-c6f5-4420-9b2f-22fcfb00299d", "parent": "361e1158-3822-4f5c-aed4-92e2d6a4036d"}}, {"head": {"id": "2e92d6ef-1ac2-4720-ab1a-97bb9217d038", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909583513800, "endTime": 4909583544500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "logId": "56841f45-7910-45a5-a3a1-d68bf5cfb896"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56841f45-7910-45a5-a3a1-d68bf5cfb896", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909583513800, "endTime": 4909583544500}, "additional": {"logType": "info", "children": [], "durationId": "2e92d6ef-1ac2-4720-ab1a-97bb9217d038", "parent": "361e1158-3822-4f5c-aed4-92e2d6a4036d"}}, {"head": {"id": "361e1158-3822-4f5c-aed4-92e2d6a4036d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905931061000, "endTime": 4909583594600}, "additional": {"logType": "info", "children": ["e486298a-5359-478d-a041-9f37dfec10e7", "9282dde6-f431-46da-b1f6-2e3dbfc84b9a", "56841f45-7910-45a5-a3a1-d68bf5cfb896"], "durationId": "2d4c05a0-ec42-4c39-9fd3-1324ab23c4c6", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "6fdfddad-83ba-4004-a494-1ac9c6e8f474", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909630027900, "endTime": 4909883865400}, "additional": {"children": ["8fe355ee-cc76-4c43-b8c6-a639e1b2145a", "99a0b48a-6bfc-4f42-aaa0-0c734399df8e", "a16658fd-4e93-4e7f-b246-d9bf7863ece5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9258ea89-ba28-4592-b767-ba661db23980", "logId": "9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fe355ee-cc76-4c43-b8c6-a639e1b2145a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909638405300, "endTime": 4909638438600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6fdfddad-83ba-4004-a494-1ac9c6e8f474", "logId": "ffd19fce-844f-49ef-b6fb-86785b1a03f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffd19fce-844f-49ef-b6fb-86785b1a03f8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909638405300, "endTime": 4909638438600}, "additional": {"logType": "info", "children": [], "durationId": "8fe355ee-cc76-4c43-b8c6-a639e1b2145a", "parent": "9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8"}}, {"head": {"id": "99a0b48a-6bfc-4f42-aaa0-0c734399df8e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909643860800, "endTime": 4909869267500}, "additional": {"children": ["452b72df-1af3-40e6-a57e-6a5f2d48b1e0", "ea930e24-75ef-4057-ad38-42da76a698dd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6fdfddad-83ba-4004-a494-1ac9c6e8f474", "logId": "a456149a-bd2f-452f-a575-1410562fde9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "452b72df-1af3-40e6-a57e-6a5f2d48b1e0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909643863800, "endTime": 4909675641400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "99a0b48a-6bfc-4f42-aaa0-0c734399df8e", "logId": "5dd24e8d-506d-4af7-a4f9-32c2c4990552"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea930e24-75ef-4057-ad38-42da76a698dd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909675668600, "endTime": 4909869242200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "99a0b48a-6bfc-4f42-aaa0-0c734399df8e", "logId": "06f92a39-4e39-432c-85ba-ab8698cdd7b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2da863d-a870-41bd-a1d9-883ef6794d65", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909643878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8aabfe4-cf04-4e2b-a0d5-a9d366035e98", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909675389300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd24e8d-506d-4af7-a4f9-32c2c4990552", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909643863800, "endTime": 4909675641400}, "additional": {"logType": "info", "children": [], "durationId": "452b72df-1af3-40e6-a57e-6a5f2d48b1e0", "parent": "a456149a-bd2f-452f-a575-1410562fde9d"}}, {"head": {"id": "66e09ff8-deab-46f8-978e-b410cfc2d0bc", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909675700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52266d7-84cf-4d0b-a1fa-10da2c54e177", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909795261700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf169061-c4f1-4367-9856-4c856f7692d4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909795811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b720ca05-63dc-4045-9321-7b7a3e86de00", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909796489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67823731-cb9b-4e27-a05c-116b326f442e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909796907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef4d4eb-0fc3-477c-b238-e2bdee387f85", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909806779100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c574ea2f-3701-48d2-9819-0455d499cfe3", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909807034200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2866cb00-77e6-4c2f-b58c-0f5676d601d6", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909807221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def89ab1-6f5a-4dbe-8b98-5bdfc2e26af6", "name": "Module entry task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909867723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5d6f75-4da2-424e-806d-e856c3981d65", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909868815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9d05e7-3f89-4d70-bbe6-1644d67303ae", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909868997300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b0a33b-717e-4d99-b6b5-03ee6879f12d", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909869127000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06f92a39-4e39-432c-85ba-ab8698cdd7b7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909675668600, "endTime": 4909869242200}, "additional": {"logType": "info", "children": [], "durationId": "ea930e24-75ef-4057-ad38-42da76a698dd", "parent": "a456149a-bd2f-452f-a575-1410562fde9d"}}, {"head": {"id": "a456149a-bd2f-452f-a575-1410562fde9d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909643860800, "endTime": 4909869267500}, "additional": {"logType": "info", "children": ["5dd24e8d-506d-4af7-a4f9-32c2c4990552", "06f92a39-4e39-432c-85ba-ab8698cdd7b7"], "durationId": "99a0b48a-6bfc-4f42-aaa0-0c734399df8e", "parent": "9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8"}}, {"head": {"id": "a16658fd-4e93-4e7f-b246-d9bf7863ece5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909883819100, "endTime": 4909883837100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6fdfddad-83ba-4004-a494-1ac9c6e8f474", "logId": "a6ada67a-1708-442b-9113-d2189e4d7345"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ada67a-1708-442b-9113-d2189e4d7345", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909883819100, "endTime": 4909883837100}, "additional": {"logType": "info", "children": [], "durationId": "a16658fd-4e93-4e7f-b246-d9bf7863ece5", "parent": "9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8"}}, {"head": {"id": "9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909630027900, "endTime": 4909883865400}, "additional": {"logType": "info", "children": ["ffd19fce-844f-49ef-b6fb-86785b1a03f8", "a456149a-bd2f-452f-a575-1410562fde9d", "a6ada67a-1708-442b-9113-d2189e4d7345"], "durationId": "6fdfddad-83ba-4004-a494-1ac9c6e8f474", "parent": "8c1e06c1-f5c9-4f57-a04d-236e270517a1"}}, {"head": {"id": "8c1e06c1-f5c9-4f57-a04d-236e270517a1", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909583625600, "endTime": 4909883889200}, "additional": {"logType": "info", "children": ["9eb7296e-d5e8-4be7-8f90-2f7d084cd5f8"], "durationId": "9258ea89-ba28-4592-b767-ba661db23980", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "8c4f8b98-43fa-4e86-8da3-3b9f6f64e86d", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910541774200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1cb533-1255-4b2f-a827-ea538bb53d3b", "name": "hvigorfile, resolve hvigorfile dependencies in 683 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910566626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f23b7a1-7bfb-450a-a80e-212b1ebf471d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4909883905200, "endTime": 4910566771800}, "additional": {"logType": "info", "children": [], "durationId": "6a41454d-c8db-4ee0-b056-fdb7a10e5dd7", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "7cd4b658-45b2-4e3c-b1ab-dc8634742373", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910567887600, "endTime": 4910568104900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "logId": "96fd2953-6122-4e92-af8e-72fe4ca9c3b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebb2dcbb-1bc8-47b7-bec0-2659475ce6de", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910567921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96fd2953-6122-4e92-af8e-72fe4ca9c3b3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910567887600, "endTime": 4910568104900}, "additional": {"logType": "info", "children": [], "durationId": "7cd4b658-45b2-4e3c-b1ab-dc8634742373", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "ded4637b-9a01-4df4-bb3d-5d361dca8b57", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910601107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43eae6e1-8ac7-4a3e-a871-5f63506f9ad8", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910879407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80660dc3-9f5a-4848-a344-115942a253c5", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910568119200, "endTime": 4910887899000}, "additional": {"logType": "info", "children": [], "durationId": "ea1cc8b7-f40d-4398-bc3a-51ffa76bdcb2", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "3d146a21-bef4-446d-8fa4-2796047a73fb", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910887956100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef5ef24-a9a0-496b-93a6-12b4150fc790", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911263086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb8b6780-87f6-438e-957e-f9cdd540d26d", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911263284800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d32053-7257-4337-a0f0-344251825f33", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911263765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7474d931-d015-4ad4-b2ec-8d46bb8a8e51", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911266989300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25b361d-06d6-41d9-8d06-1ae6ef6e9290", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911267155900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8a7059-c06e-4aa3-9c7a-68cc0fd46c74", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4910887931700, "endTime": 4911300561100}, "additional": {"logType": "info", "children": [], "durationId": "1ed361d8-aed4-411b-8049-958d8c20068a", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "c5058581-a13f-41b9-98d6-8f4facc853ff", "name": "Configuration phase cost:5 s 418 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911300690100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4808c2b7-d588-48fa-a9cc-99b4060a5e6e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911300639600, "endTime": 4911300883300}, "additional": {"logType": "info", "children": [], "durationId": "e21dd21f-03f4-40ca-b5f5-9e15c4b2aab6", "parent": "b5668217-1c90-4a65-bbee-d19056913f92"}}, {"head": {"id": "b5668217-1c90-4a65-bbee-d19056913f92", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905854331600, "endTime": 4911300908700}, "additional": {"logType": "info", "children": ["be5aaf7f-5ad6-46f4-904f-60a66fa8afc7", "ce4aedfc-ce28-4b22-89d9-202225ca139a", "361e1158-3822-4f5c-aed4-92e2d6a4036d", "8c1e06c1-f5c9-4f57-a04d-236e270517a1", "2f23b7a1-7bfb-450a-a80e-212b1ebf471d", "80660dc3-9f5a-4848-a344-115942a253c5", "8c8a7059-c06e-4aa3-9c7a-68cc0fd46c74", "4808c2b7-d588-48fa-a9cc-99b4060a5e6e", "96fd2953-6122-4e92-af8e-72fe4ca9c3b3"], "durationId": "8d0abcff-ed63-43ba-80fa-2c680fc10321", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "57526912-7504-4029-b5c9-2d72415b6efa", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911302524400, "endTime": 4911302546800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed14830a-f98e-45ea-9786-e423413085ee", "logId": "a5fa42c8-c731-4e14-8e57-a848cc8b636e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5fa42c8-c731-4e14-8e57-a848cc8b636e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911302524400, "endTime": 4911302546800}, "additional": {"logType": "info", "children": [], "durationId": "57526912-7504-4029-b5c9-2d72415b6efa", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "977726c6-5f29-4944-9952-fd284e089a3a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911300944200, "endTime": 4911302577000}, "additional": {"logType": "info", "children": [], "durationId": "d2348f04-bb10-40ad-acf4-07f9f47321ed", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "607c9c19-38e8-47e6-a1dc-3db579fb76df", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911302583700, "endTime": 4911302610000}, "additional": {"logType": "info", "children": [], "durationId": "91a1b9ed-6274-421d-8f73-ae36664369e8", "parent": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e"}}, {"head": {"id": "f6d61572-d0df-44ef-8c3a-d8fc7be7084e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4905005043700, "endTime": 4911302616900}, "additional": {"logType": "info", "children": ["8741fae2-828d-4323-841f-899052912081", "b5668217-1c90-4a65-bbee-d19056913f92", "977726c6-5f29-4944-9952-fd284e089a3a", "607c9c19-38e8-47e6-a1dc-3db579fb76df", "a6b9b601-d12c-4eec-9b17-50607e4e8810", "7be6598b-d055-405a-93ff-38e6c2cdbad2", "a5fa42c8-c731-4e14-8e57-a848cc8b636e"], "durationId": "ed14830a-f98e-45ea-9786-e423413085ee"}}, {"head": {"id": "b5eb6855-557f-4070-b01e-20656abaa245", "name": "Configuration task cost before running: 6 s 324 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911302924900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ade4d1-5759-4890-955b-9075431838e1", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911356834700, "endTime": 4911459374400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "04113f9c-726b-4faa-bb5c-507bac64cbef", "logId": "07698637-ca28-400f-91ab-a86ce4f52d61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04113f9c-726b-4faa-bb5c-507bac64cbef", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911327018500}, "additional": {"logType": "detail", "children": [], "durationId": "d5ade4d1-5759-4890-955b-9075431838e1"}}, {"head": {"id": "a460f565-610c-4c04-b4b8-24d403c8eb2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911328452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610e9bce-d37d-4d38-93bf-5c26d22770ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911328593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f7a837-f735-4c6f-904f-69f33d963535", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911356870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a794b6fd-d63a-4f4a-891e-9258c3c0d98e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911459107800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a860d9-9594-4048-a965-bb36264d1b29", "name": "entry : default@PreBuild cost memory 0.28924560546875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911459274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07698637-ca28-400f-91ab-a86ce4f52d61", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911356834700, "endTime": 4911459374400}, "additional": {"logType": "info", "children": [], "durationId": "d5ade4d1-5759-4890-955b-9075431838e1"}}, {"head": {"id": "e8485a8e-82e1-4fc6-a80c-5001e7a95034", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911474146200, "endTime": 4911507177700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ef26c6e2-b2e7-4954-8e61-fdf1484ec13e", "logId": "13f79e0b-8d6c-46ed-b9b5-845e159b4b7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef26c6e2-b2e7-4954-8e61-fdf1484ec13e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911472726200}, "additional": {"logType": "detail", "children": [], "durationId": "e8485a8e-82e1-4fc6-a80c-5001e7a95034"}}, {"head": {"id": "dbe134d0-9fc6-44b4-ae10-a797fc52f1f1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911473300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c74cea5d-75e3-4e7b-9580-689e1f11bd71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911473410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4764d5-3dd9-4b98-a230-bd3d32d6dc4d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911474157400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6be444-9764-4639-bf22-cc395f12cc53", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911506835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1b21ea8-9650-4a50-a1a8-bada4d2629ec", "name": "entry : default@MergeProfile cost memory 0.11473846435546875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911507077800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f79e0b-8d6c-46ed-b9b5-845e159b4b7f", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911474146200, "endTime": 4911507177700}, "additional": {"logType": "info", "children": [], "durationId": "e8485a8e-82e1-4fc6-a80c-5001e7a95034"}}, {"head": {"id": "9fe93dab-0690-4af1-8ec5-72266458b811", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911512055200, "endTime": 4911560991300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c5a7ae78-89a2-42f5-a0aa-76d4910d3a66", "logId": "77db3db4-8ea2-48a5-8b3e-9b397cce6d4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5a7ae78-89a2-42f5-a0aa-76d4910d3a66", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911509863000}, "additional": {"logType": "detail", "children": [], "durationId": "9fe93dab-0690-4af1-8ec5-72266458b811"}}, {"head": {"id": "c2d5f38b-1e23-47ef-a23b-93f6fb29534b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911510461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "028d98b0-1421-41e2-aa91-0c592c0faa68", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911510595400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f7689a-81fc-4eb6-b18e-5c19b8ba3875", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911512068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c9a8f6-4065-4d7c-ade1-014c796ae521", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 29 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911540785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004a3a51-602a-4c59-b898-41c0b2593caf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911560296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d867ec-5cab-4142-bac1-220fd79bafe3", "name": "entry : default@CreateBuildProfile cost memory 0.10101318359375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911560476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77db3db4-8ea2-48a5-8b3e-9b397cce6d4f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911512055200, "endTime": 4911560991300}, "additional": {"logType": "info", "children": [], "durationId": "9fe93dab-0690-4af1-8ec5-72266458b811"}}, {"head": {"id": "4abff149-c617-4ecf-96ae-9bfd28af270f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583150200, "endTime": 4911583712700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "37d49c76-b5ad-48c3-820b-74bbfdf77866", "logId": "ec9c7390-7dd2-40dd-94f2-68d5d7dc8de2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37d49c76-b5ad-48c3-820b-74bbfdf77866", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911571810000}, "additional": {"logType": "detail", "children": [], "durationId": "4abff149-c617-4ecf-96ae-9bfd28af270f"}}, {"head": {"id": "b54dd2eb-d22b-4dcc-b338-18fcd0029a77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911572331800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76e7089-f162-43f0-9551-f3ec6c83b806", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911581782800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee855f1-f06b-4ca0-bb17-ccccc3e490a0", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06280aa-c0e6-49d5-bef4-94e71f165dd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583358200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6633b928-b5d2-4e63-87c0-bbb2dce5ee29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa24be1d-1f12-4ca2-93ca-b190f1857c6e", "name": "entry : default@PreCheckSyscap cost memory 0.0396728515625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583538300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a55b913-192b-4b47-af47-85b83668a6c6", "name": "runTaskFromQueue task cost before running: 6 s 605 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9c7390-7dd2-40dd-94f2-68d5d7dc8de2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911583150200, "endTime": 4911583712700, "totalTime": 464900}, "additional": {"logType": "info", "children": [], "durationId": "4abff149-c617-4ecf-96ae-9bfd28af270f"}}, {"head": {"id": "371a51e9-b1ee-4529-b7b1-e0803056cb97", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911621428400, "endTime": 4911676629200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "48e8690b-9263-4e0c-9669-444bf843b9b7", "logId": "e02605d5-d964-43b6-897a-a55a94bbe4e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48e8690b-9263-4e0c-9669-444bf843b9b7", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911585664800}, "additional": {"logType": "detail", "children": [], "durationId": "371a51e9-b1ee-4529-b7b1-e0803056cb97"}}, {"head": {"id": "291067e4-29d8-4c63-b336-fe3c5cb059df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911586275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36865e91-fd14-4103-8e5c-e432f4707d80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911586391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973e0fba-8f25-4f1a-9cde-6e5ccc98e3b6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911621449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54989361-afa9-4f2e-aa5f-5d1ec80dd39f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911660305400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a562673-0b8e-4c15-bc25-93e75bdee7cb", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911661248000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba5c2f34-38d9-4cee-9497-a029311fdf97", "name": "entry : default@GeneratePkgContextInfo cost memory -1.5927810668945312", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911676430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02605d5-d964-43b6-897a-a55a94bbe4e6", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911621428400, "endTime": 4911676629200}, "additional": {"logType": "info", "children": [], "durationId": "371a51e9-b1ee-4529-b7b1-e0803056cb97"}}, {"head": {"id": "6e104b32-2e2a-4519-9615-e10cf1a62236", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911681468000, "endTime": 4911715736600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b8c798ff-439d-4404-b867-432fee82d2d7", "logId": "9a9cbe0c-3bdf-4f96-abc0-8e9f9ed60dd9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8c798ff-439d-4404-b867-432fee82d2d7", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911678513900}, "additional": {"logType": "detail", "children": [], "durationId": "6e104b32-2e2a-4519-9615-e10cf1a62236"}}, {"head": {"id": "a7479b88-6fe7-4e78-ba35-e585fe471656", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911679037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb54981-d896-4f01-94ea-b54be240cfa8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911679140400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b96f1ba-c6fb-45e2-b014-c98df33b81d0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911681481400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140bcd7c-0655-4776-a8b8-fcfe90b6d42e", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911715266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a396149-4deb-4635-ad27-7b18101841b5", "name": "entry : default@ProcessProfile cost memory 0.05904388427734375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911715552200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a9cbe0c-3bdf-4f96-abc0-8e9f9ed60dd9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911681468000, "endTime": 4911715736600}, "additional": {"logType": "info", "children": [], "durationId": "6e104b32-2e2a-4519-9615-e10cf1a62236"}}, {"head": {"id": "8dc71617-8467-4ce8-9669-2b44aa251525", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911728683900, "endTime": 4911761008300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5f677290-ebf3-4239-b75a-3c06b9d656ac", "logId": "324936c3-cfe9-4c70-b5c6-025a78508b17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f677290-ebf3-4239-b75a-3c06b9d656ac", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911721327600}, "additional": {"logType": "detail", "children": [], "durationId": "8dc71617-8467-4ce8-9669-2b44aa251525"}}, {"head": {"id": "8b2a16fd-6d42-4493-8f1f-c31775d1a3fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911722166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "797c5e57-6592-48c9-8dde-c41e21f34540", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911724719800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b823cb-e37d-47d0-8cd6-0ac55d189b9e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911728709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8526d100-b251-4321-a663-308d9390707f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911760744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a18312-9e06-4ee7-8113-9b480730b4f0", "name": "entry : default@ProcessRouterMap cost memory 0.19831085205078125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911760918900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "324936c3-cfe9-4c70-b5c6-025a78508b17", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911728683900, "endTime": 4911761008300}, "additional": {"logType": "info", "children": [], "durationId": "8dc71617-8467-4ce8-9669-2b44aa251525"}}, {"head": {"id": "4007ff97-3bd1-42ab-9a45-392b8911cf4a", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911789458200, "endTime": 4911825689800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d1f096be-0e7b-4c1b-a3cd-979edaf040f5", "logId": "289519d5-4844-43d8-b590-b3fa6c08d25b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1f096be-0e7b-4c1b-a3cd-979edaf040f5", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911774777200}, "additional": {"logType": "detail", "children": [], "durationId": "4007ff97-3bd1-42ab-9a45-392b8911cf4a"}}, {"head": {"id": "e20ecdb9-b174-4e17-bdf8-55a1f87d4f97", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911775322900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4445baf5-786e-43d2-9253-76c737f570f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911775440000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68aa3c35-60a8-4470-97bc-89fc9db8a9ba", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911776766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6574604c-270b-417c-9ac1-5f05fc6a4e5b", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911808837500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "160ec41f-2829-4e7f-8a31-38984de66639", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911809205900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38151948-ef9d-4eb7-a488-ec769a573f4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911809351800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faac32c5-3020-4f29-a78f-607b7afbef8d", "name": "entry : default@PreviewProcessResource cost memory 0.071197509765625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911822971900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08984791-4960-48cc-8923-b3857a3ac2be", "name": "runTaskFromQueue task cost before running: 6 s 847 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911825567100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "289519d5-4844-43d8-b590-b3fa6c08d25b", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911789458200, "endTime": 4911825689800, "totalTime": 33731500}, "additional": {"logType": "info", "children": [], "durationId": "4007ff97-3bd1-42ab-9a45-392b8911cf4a"}}, {"head": {"id": "11a9853e-d937-48d7-a8c4-c2102db4f34e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911833491400, "endTime": 4911866851600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eed210ac-f979-415b-8fd9-ef29c1e28048", "logId": "b9007fac-5338-43af-a3b8-0ea2dc71bee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eed210ac-f979-415b-8fd9-ef29c1e28048", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911829271300}, "additional": {"logType": "detail", "children": [], "durationId": "11a9853e-d937-48d7-a8c4-c2102db4f34e"}}, {"head": {"id": "ea40d2bc-a866-4eae-bd91-cf2266d1a301", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911829846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a98c45-6034-4ef2-a4a1-4b2d4d400638", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911829979000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a938c715-af6a-413b-9b66-fb41e4c8a038", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911833506400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc764d4-002e-482c-94c3-2e7f204f55ad", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911866581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e9a26e-ded6-43e5-a042-fb37eb70bca1", "name": "entry : default@GenerateLoaderJson cost memory -0.9808883666992188", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911866758800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9007fac-5338-43af-a3b8-0ea2dc71bee9", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911833491400, "endTime": 4911866851600}, "additional": {"logType": "info", "children": [], "durationId": "11a9853e-d937-48d7-a8c4-c2102db4f34e"}}, {"head": {"id": "259523a9-9c8c-4a43-a8f9-7f1fab287b89", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911881234900, "endTime": 4913203653100}, "additional": {"children": ["9244417e-464e-4da5-b879-074a7abba055", "7881afdd-bf44-4b80-8158-b24fb6594fd8", "4b84a42c-a8da-4f5b-9814-f4ba9da402a6", "5ef35381-315b-40ef-9750-088146c43961"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "4cb0c59d-989e-483a-84c4-4c6748722b7b", "logId": "81149694-9486-4c1f-87cc-b86126cdca92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cb0c59d-989e-483a-84c4-4c6748722b7b", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911874856200}, "additional": {"logType": "detail", "children": [], "durationId": "259523a9-9c8c-4a43-a8f9-7f1fab287b89"}}, {"head": {"id": "437b1c97-1dbb-42ab-b4e5-79fec3a7738d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911875336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0afe7f1-d093-4260-a876-a008dd170c25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911875475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4020d8bc-5596-4d09-9206-4a5c54d42a5e", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911876453400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa1ba86-c9e8-46e5-8452-fba2437ed2c7", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911881269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8d167c0-6369-4ecd-bdb1-e501e5c1521c", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912155292500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "208c063b-93af-4350-b470-d367ad26a3e0", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 285 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912166884300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9244417e-464e-4da5-b879-074a7abba055", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912169136800, "endTime": 4912390829800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "259523a9-9c8c-4a43-a8f9-7f1fab287b89", "logId": "e281c3c8-52e5-4c77-9fb7-aae8e374bf50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e281c3c8-52e5-4c77-9fb7-aae8e374bf50", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912169136800, "endTime": 4912390829800}, "additional": {"logType": "info", "children": [], "durationId": "9244417e-464e-4da5-b879-074a7abba055", "parent": "81149694-9486-4c1f-87cc-b86126cdca92"}}, {"head": {"id": "6c10923b-20e1-4995-9abd-af863fdb4dea", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912391244100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7881afdd-bf44-4b80-8158-b24fb6594fd8", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912392685200, "endTime": 4912842756800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "259523a9-9c8c-4a43-a8f9-7f1fab287b89", "logId": "37ed16a0-4d39-4e8d-a1d4-b8d59168ff2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaac7628-2740-4eff-8fe8-a3bfdca0e1d6", "name": "current process  memoryUsage: {\n  rss: 66625536,\n  heapTotal: 118034432,\n  heapUsed: 105720336,\n  external: 3108429,\n  arrayBuffers: 102292\n} os memoryUsage :6.073402404785156", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912394310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee6e8ab-9ee3-411a-addc-9cc0d8ec45bc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912807977100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37ed16a0-4d39-4e8d-a1d4-b8d59168ff2e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912392685200, "endTime": 4912842756800}, "additional": {"logType": "info", "children": [], "durationId": "7881afdd-bf44-4b80-8158-b24fb6594fd8", "parent": "81149694-9486-4c1f-87cc-b86126cdca92"}}, {"head": {"id": "8dbaa8de-fc57-4049-8a40-47a3f5ce7ea3", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912842903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b84a42c-a8da-4f5b-9814-f4ba9da402a6", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912844089600, "endTime": 4912905543100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "259523a9-9c8c-4a43-a8f9-7f1fab287b89", "logId": "fdba00fa-fe92-452b-8566-9ebcbaa79a25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "318caf1e-ae75-44a1-b511-71138a424e30", "name": "current process  memoryUsage: {\n  rss: 67502080,\n  heapTotal: 118034432,\n  heapUsed: 105986488,\n  external: 3108555,\n  arrayBuffers: 102433\n} os memoryUsage :6.070842742919922", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912844994800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab43a21a-ffdd-41eb-bac8-b0cfa208e895", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912903018000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdba00fa-fe92-452b-8566-9ebcbaa79a25", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912844089600, "endTime": 4912905543100}, "additional": {"logType": "info", "children": [], "durationId": "4b84a42c-a8da-4f5b-9814-f4ba9da402a6", "parent": "81149694-9486-4c1f-87cc-b86126cdca92"}}, {"head": {"id": "466fa968-a8ba-48d2-8187-5dbcb852ae84", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912905933100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef35381-315b-40ef-9750-088146c43961", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912907369300, "endTime": 4913202270100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "259523a9-9c8c-4a43-a8f9-7f1fab287b89", "logId": "de3faec7-4751-4a2b-9867-87d0dbe9919f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f27e81f-c19f-4f1d-94fc-6a2d6314299e", "name": "current process  memoryUsage: {\n  rss: 67575808,\n  heapTotal: 118034432,\n  heapUsed: 106276712,\n  external: 3116873,\n  arrayBuffers: 111565\n} os memoryUsage :6.0702362060546875", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912908496100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42214040-b51d-48f2-9699-ebd16ee5644f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913180428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3faec7-4751-4a2b-9867-87d0dbe9919f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4912907369300, "endTime": 4913202270100}, "additional": {"logType": "info", "children": [], "durationId": "5ef35381-315b-40ef-9750-088146c43961", "parent": "81149694-9486-4c1f-87cc-b86126cdca92"}}, {"head": {"id": "254f512d-f0fa-4124-b64a-06218fa30c9b", "name": "entry : default@PreviewCompileResource cost memory 0.0704345703125", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913203381300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0eea55-8d3e-45d2-b9bb-0ce6ea41aade", "name": "runTaskFromQueue task cost before running: 8 s 225 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913203579300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81149694-9486-4c1f-87cc-b86126cdca92", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4911881234900, "endTime": 4913203653100, "totalTime": 1322292000}, "additional": {"logType": "info", "children": ["e281c3c8-52e5-4c77-9fb7-aae8e374bf50", "37ed16a0-4d39-4e8d-a1d4-b8d59168ff2e", "fdba00fa-fe92-452b-8566-9ebcbaa79a25", "de3faec7-4751-4a2b-9867-87d0dbe9919f"], "durationId": "259523a9-9c8c-4a43-a8f9-7f1fab287b89"}}, {"head": {"id": "7c5502ac-512e-4cdf-b4bd-25f1e89ade9b", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207222400, "endTime": 4913207705200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "30d5c66d-c72c-495a-8516-367892ebbb16", "logId": "619b1c2e-562b-46c1-aee9-1962ac781252"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30d5c66d-c72c-495a-8516-367892ebbb16", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913206505800}, "additional": {"logType": "detail", "children": [], "durationId": "7c5502ac-512e-4cdf-b4bd-25f1e89ade9b"}}, {"head": {"id": "18c156b5-1328-4911-9572-c87f247c47ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207021900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e8efaa-73e4-4bcc-98de-95f4a20f43dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207125500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "513229a9-998b-4879-ab01-a1be63a4781c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab8179f-3136-409f-bf03-83ed61752fae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a62352fc-53f5-4eb2-8d16-b20de87f9456", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207411900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e64335-e6c1-4e87-8bc6-35657d9fee09", "name": "entry : default@PreviewHookCompileResource cost memory 0.04172515869140625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207535800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db3af290-b8ba-46a2-8ace-3b25f72633eb", "name": "runTaskFromQueue task cost before running: 8 s 229 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207627400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "619b1c2e-562b-46c1-aee9-1962ac781252", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913207222400, "endTime": 4913207705200, "totalTime": 377400}, "additional": {"logType": "info", "children": [], "durationId": "7c5502ac-512e-4cdf-b4bd-25f1e89ade9b"}}, {"head": {"id": "0457eff9-0eb5-438c-bd3e-a4bdaf7c4ba8", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913212777800, "endTime": 4913242053500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "0d6aa663-5438-4c71-a1fd-d898e4e78567", "logId": "784cc2ef-0466-4282-bbd0-0bdbf743dbf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d6aa663-5438-4c71-a1fd-d898e4e78567", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913211478000}, "additional": {"logType": "detail", "children": [], "durationId": "0457eff9-0eb5-438c-bd3e-a4bdaf7c4ba8"}}, {"head": {"id": "30a409a0-2ce4-4647-99af-53a768ee02c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913212022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15992109-85cb-492c-83d1-02c4fc13cf42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913212127100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2e7062-4a04-494e-8e43-5b36d6f4bd82", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913212789200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b87467-7622-4a6f-bef6-95ceafa7ae05", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913214187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67b9adf8-d845-4445-9fec-75070aeb7f82", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913214317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d04bda-3f7e-409f-9cf5-49f99ba999e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913214424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cda811e-6939-4562-bf71-ca8bb526f8d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913214487300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85bc0baf-6611-4eb8-b15e-ddfc11a56d6b", "name": "entry : default@CopyPreviewProfile cost memory 0.20894622802734375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913241374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c274e525-07b5-4e7b-ac87-1c63f875857d", "name": "runTaskFromQueue task cost before running: 8 s 263 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913241698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "784cc2ef-0466-4282-bbd0-0bdbf743dbf5", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913212777800, "endTime": 4913242053500, "totalTime": 28857800}, "additional": {"logType": "info", "children": [], "durationId": "0457eff9-0eb5-438c-bd3e-a4bdaf7c4ba8"}}, {"head": {"id": "d2ee5676-2fb0-4a2c-967d-a9eee1375b3c", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277119400, "endTime": 4913278093300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e160d7da-3f49-4c6d-9e9c-247b6ab6a485", "logId": "2e28cc2b-63f9-44dd-8a99-0eeddc03e1bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e160d7da-3f49-4c6d-9e9c-247b6ab6a485", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913266583400}, "additional": {"logType": "detail", "children": [], "durationId": "d2ee5676-2fb0-4a2c-967d-a9eee1375b3c"}}, {"head": {"id": "99d5ef60-308b-4fa4-b317-da87dfabad2b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913267621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67d8365-843d-471e-ab85-bf77f55241bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913267825000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aedca46-3a94-43e7-9ef8-cee3e4f697d9", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277145100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "782f69ce-7a1a-4aff-a0eb-bc57ca76eccf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9832f1f-2813-4cf7-b967-d341342b7d56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277555600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4676cd98-0005-4a2e-9b90-dcda1c96feb2", "name": "entry : default@ReplacePreviewerPage cost memory 0.03835296630859375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d817c6-23f3-4ad9-af18-364f73020c1c", "name": "runTaskFromQueue task cost before running: 8 s 299 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277946200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e28cc2b-63f9-44dd-8a99-0eeddc03e1bd", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913277119400, "endTime": 4913278093300, "totalTime": 780500}, "additional": {"logType": "info", "children": [], "durationId": "d2ee5676-2fb0-4a2c-967d-a9eee1375b3c"}}, {"head": {"id": "5c708bc2-5188-43ac-89b6-0b7eae0e5b13", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913281868300, "endTime": 4913282775200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "962e0152-8f37-495f-921b-b183e25b63ca", "logId": "ba3ebec8-36b8-4970-89e6-3125c1760191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "962e0152-8f37-495f-921b-b183e25b63ca", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913281771600}, "additional": {"logType": "detail", "children": [], "durationId": "5c708bc2-5188-43ac-89b6-0b7eae0e5b13"}}, {"head": {"id": "79dc464d-5845-4e4c-acbd-5dc25e9063b3", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913281885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a4ce29-7279-4c22-b2f1-9f614dcb9a89", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913282394900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6fc18c-b1c2-495e-9e80-4640c9a30c9d", "name": "runTaskFromQueue task cost before running: 8 s 304 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913282627600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba3ebec8-36b8-4970-89e6-3125c1760191", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913281868300, "endTime": 4913282775200, "totalTime": 699900}, "additional": {"logType": "info", "children": [], "durationId": "5c708bc2-5188-43ac-89b6-0b7eae0e5b13"}}, {"head": {"id": "1d846de2-69b9-4196-ad50-18bfeb3af3c8", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913288250700, "endTime": 4913291424700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "20051662-5e14-4f73-84b0-b08052f34a34", "logId": "ae298546-d859-4c1d-b93e-bffdf6d8e23d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20051662-5e14-4f73-84b0-b08052f34a34", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913286142600}, "additional": {"logType": "detail", "children": [], "durationId": "1d846de2-69b9-4196-ad50-18bfeb3af3c8"}}, {"head": {"id": "a346bf34-4fb8-4484-bb1f-028884b3516a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913286980200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e229808c-7fc9-4fa7-be13-9301999d4506", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913287167600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692190b2-b647-4478-9a8c-15cc2a7231f8", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913288260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cacdd75b-3130-476a-8746-46e860a005d3", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913290187900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80468e3e-914f-4f4c-8b7a-1092a825a6db", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913290304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48333515-0f65-444a-be58-764e057bc1d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913290392500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0bd2c9-da69-4254-a36b-c56d6b6dd80d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913290454200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e9a470-5c38-4bbc-93d9-b3d3a233621c", "name": "entry : default@PreviewUpdateAssets cost memory 0.13541412353515625", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913291221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97dcefdb-7f3d-4915-9b43-273a2617b412", "name": "runTaskFromQueue task cost before running: 8 s 312 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913291346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae298546-d859-4c1d-b93e-bffdf6d8e23d", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913288250700, "endTime": 4913291424700, "totalTime": 3071500}, "additional": {"logType": "info", "children": [], "durationId": "1d846de2-69b9-4196-ad50-18bfeb3af3c8"}}, {"head": {"id": "6a3ad8d5-7396-44d9-a617-221c051aefd4", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913309376700, "endTime": 4971282896900}, "additional": {"children": ["08b3fdd7-82ee-4391-8dcc-516da64122ee"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "0551fa04-6fd7-4dd6-a681-c503dad18bfb", "logId": "61c800cb-879e-4afa-9af7-4f9e84b266d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0551fa04-6fd7-4dd6-a681-c503dad18bfb", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913303480200}, "additional": {"logType": "detail", "children": [], "durationId": "6a3ad8d5-7396-44d9-a617-221c051aefd4"}}, {"head": {"id": "c20e95a7-f8de-4c31-a4a4-d9e8b069d3cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913304074000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30ed9f8-52df-44a2-8448-9cba0392b5d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913304177900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0ce0882-f1cc-4b16-9a0f-01018130448e", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913309388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761788ba-d2a2-42d9-bd2f-c5ba99d776bf", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913321776500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761107fd-443f-4699-aabc-6701c388cb22", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913321938400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4913478880400, "endTime": 4971278890600}, "additional": {"children": ["a4c6f2b8-aec5-4a9d-9249-42c2d222ad68", "1ae9dc46-92e6-4ed4-b42e-51de83571dd3", "a647115a-08f2-48e7-9c66-1bc962cd841c", "03a34aff-3e0b-4646-844b-cc6b283fae04", "4d93c79b-52c7-47d9-835d-d866b528acc8", "72e2ae0d-0cca-409f-8aab-e1a76ce0ff65"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "6a3ad8d5-7396-44d9-a617-221c051aefd4", "logId": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "319ca069-671e-4af1-8b55-f98ecabce8f6", "name": "entry : default@PreviewArkTS cost memory -0.5405807495117188", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913481712600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9313ffa6-dc23-4f12-8e8b-c80551e62577", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4953540666100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c6f2b8-aec5-4a9d-9249-42c2d222ad68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4953546122500, "endTime": 4953546207800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "fb8b5d25-72fd-482b-bce3-968b47d4d494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb8b5d25-72fd-482b-bce3-968b47d4d494", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4953546122500, "endTime": 4953546207800}, "additional": {"logType": "info", "children": [], "durationId": "a4c6f2b8-aec5-4a9d-9249-42c2d222ad68", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "ddc555d3-001c-4cb8-84fd-752c703c7e0c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971277412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae9dc46-92e6-4ed4-b42e-51de83571dd3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4971278514800, "endTime": 4971278659400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "68a56332-6020-4437-9484-25df6bc2df54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68a56332-6020-4437-9484-25df6bc2df54", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971278514800, "endTime": 4971278659400}, "additional": {"logType": "info", "children": [], "durationId": "1ae9dc46-92e6-4ed4-b42e-51de83571dd3", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "b40e283c-5dd8-479b-b122-73b3b3ea8180", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4913478880400, "endTime": 4971278890600}, "additional": {"logType": "info", "children": ["fb8b5d25-72fd-482b-bce3-968b47d4d494", "68a56332-6020-4437-9484-25df6bc2df54", "b991116a-ad3d-4027-be32-e08669f72ce0", "a5b2f2c5-73b9-4ceb-85a6-bb8dfb9a70e5", "693a058c-bd60-4bae-bbec-c2cdc69b2762", "99717897-6a66-40ae-90c0-45a26a3178b5"], "durationId": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "parent": "61c800cb-879e-4afa-9af7-4f9e84b266d1"}}, {"head": {"id": "a647115a-08f2-48e7-9c66-1bc962cd841c", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4938839408600, "endTime": 4952981156000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "b991116a-ad3d-4027-be32-e08669f72ce0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b991116a-ad3d-4027-be32-e08669f72ce0", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4938839408600, "endTime": 4952981156000}, "additional": {"logType": "info", "children": [], "durationId": "a647115a-08f2-48e7-9c66-1bc962cd841c", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "03a34aff-3e0b-4646-844b-cc6b283fae04", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4952981367100, "endTime": 4953358161900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "a5b2f2c5-73b9-4ceb-85a6-bb8dfb9a70e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5b2f2c5-73b9-4ceb-85a6-bb8dfb9a70e5", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4952981367100, "endTime": 4953358161900}, "additional": {"logType": "info", "children": [], "durationId": "03a34aff-3e0b-4646-844b-cc6b283fae04", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "4d93c79b-52c7-47d9-835d-d866b528acc8", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4953358874400, "endTime": 4953359572500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "693a058c-bd60-4bae-bbec-c2cdc69b2762"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "693a058c-bd60-4bae-bbec-c2cdc69b2762", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4953358874400, "endTime": 4953359572500}, "additional": {"logType": "info", "children": [], "durationId": "4d93c79b-52c7-47d9-835d-d866b528acc8", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "72e2ae0d-0cca-409f-8aab-e1a76ce0ff65", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Worker3", "startTime": 4953359924100, "endTime": 4971277467500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "08b3fdd7-82ee-4391-8dcc-516da64122ee", "logId": "99717897-6a66-40ae-90c0-45a26a3178b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99717897-6a66-40ae-90c0-45a26a3178b5", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4953359924100, "endTime": 4971277467500}, "additional": {"logType": "info", "children": [], "durationId": "72e2ae0d-0cca-409f-8aab-e1a76ce0ff65", "parent": "b40e283c-5dd8-479b-b122-73b3b3ea8180"}}, {"head": {"id": "61c800cb-879e-4afa-9af7-4f9e84b266d1", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4913309376700, "endTime": 4971282896900, "totalTime": 57973499500}, "additional": {"logType": "info", "children": ["b40e283c-5dd8-479b-b122-73b3b3ea8180"], "durationId": "6a3ad8d5-7396-44d9-a617-221c051aefd4"}}, {"head": {"id": "6812ba20-489c-4afa-98b7-999b657a6627", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288415800, "endTime": 4971288782900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "01cc4f37-bd2e-465e-9bb3-54641e926fc9", "logId": "5568037a-9f2f-4672-b7ac-72524f555530"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01cc4f37-bd2e-465e-9bb3-54641e926fc9", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288365200}, "additional": {"logType": "detail", "children": [], "durationId": "6812ba20-489c-4afa-98b7-999b657a6627"}}, {"head": {"id": "db4d4370-f955-4a3b-b26f-80a760fb385c", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288428700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f187ebd0-e69a-46f4-8a5b-7e4cc6cb4ca5", "name": "entry : PreviewBuild cost memory 0.01174163818359375", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288560700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a03b425-70ca-4663-8953-afdb72a38ce2", "name": "runTaskFromQueue task cost before running: 1 min 6 s 310 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5568037a-9f2f-4672-b7ac-72524f555530", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971288415800, "endTime": 4971288782900, "totalTime": 224200}, "additional": {"logType": "info", "children": [], "durationId": "6812ba20-489c-4afa-98b7-999b657a6627"}}, {"head": {"id": "137fbfb6-baf4-4630-85b7-0af192ab2b29", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971294534800, "endTime": 4971294555600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f82731f-c76e-4adf-a2e7-fc948fc83724", "logId": "f0b07fb4-4fe0-4af3-aeb5-0dca79f20efd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0b07fb4-4fe0-4af3-aeb5-0dca79f20efd", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971294534800, "endTime": 4971294555600}, "additional": {"logType": "info", "children": [], "durationId": "137fbfb6-baf4-4630-85b7-0af192ab2b29"}}, {"head": {"id": "25be8d03-b9f4-44a8-a170-19391896edc0", "name": "BUILD SUCCESSFUL in 1 min 6 s 316 ms ", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971294601800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "01010c42-724c-4965-9db3-21f64f1fd1ce", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4904979535000, "endTime": 4971294849000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 13}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "37488d04-eba6-4e36-bdfc-15e91792b776", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971294877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a44928-8281-4830-a1aa-97b1d04b02ce", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971294954800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc95ac6-5598-41d4-afe9-b4fef420eca6", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971295011700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "875c8661-b0be-4e12-99ae-7de275b07c1b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971295062400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e06e143d-95a6-4e20-bf3c-0c0f399c96e6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971295112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c1e3f2a-4e20-4bac-a957-38ad07b08171", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971295166400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4605a3-33ce-4100-aebb-e3cd4ec788d2", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971295216900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1d69348-e0ea-4587-9ea7-c07ab82114dd", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971296012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70eb04b-f6b9-4205-bcaf-d8613c3b1f2a", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971304581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c3380d-e689-4509-97a1-9bbfa8a01730", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971304977300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b53fd7-3962-4334-9e31-5ee251eab43b", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971316666200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811edf7f-72a9-46b2-95ce-ad0a4b250212", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971317468800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ffea109-5871-439b-b622-fe82ff6c181a", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971317808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec312777-2cd1-4b0f-9e4e-6a7c59a7edcc", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971318743000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d61ff9-606d-4b4c-a8a1-71dab5c79a5b", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971319900100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13310750-53b5-43f4-ac13-33f7f66fee7d", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971320479200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec815571-6c88-4363-879a-67a52e627911", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971320991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6a0bb8-ca6f-4673-a450-5fb40be7c651", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971321572800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7166711-cdac-452e-bc6c-f44fe60b255a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971324856600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18e05026-6e11-478a-b3cf-6fbaa31d0363", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971325846300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22b7ee71-e458-49b5-9194-77381168dcb2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971325964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d41574f2-dc1d-49cc-b7a8-a1f5de49d431", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971326394900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d6b233-ba54-478e-9a56-de82e8ec0014", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971327364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "927a1b0a-49c0-4f1a-8785-42bbd49fd8ba", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971352321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beed883f-343f-4ba7-b360-7b21c8c866a5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971352761500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145daab9-4536-470a-979b-1d32f67d174f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971353117000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1a4d089-c16a-4cf0-b25b-c499c7bda780", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971353657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962d4031-ce1f-47c0-963e-153afa118a7c", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 1652, "tid": "Main Thread", "startTime": 4971354379900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}