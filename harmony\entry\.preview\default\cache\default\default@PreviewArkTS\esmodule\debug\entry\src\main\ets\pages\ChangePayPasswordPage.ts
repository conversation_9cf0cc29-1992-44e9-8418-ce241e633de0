if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ChangePayPasswordPage_Params {
    currentPayPassword?: string;
    newPayPassword?: string;
    confirmPayPassword?: string;
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import type { ChangePayPasswordRequest } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
class ChangePayPasswordPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentPayPassword = new ObservedPropertySimplePU('', this, "currentPayPassword");
        this.__newPayPassword = new ObservedPropertySimplePU('', this, "newPayPassword");
        this.__confirmPayPassword = new ObservedPropertySimplePU('', this, "confirmPayPassword");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ChangePayPasswordPage_Params) {
        if (params.currentPayPassword !== undefined) {
            this.currentPayPassword = params.currentPayPassword;
        }
        if (params.newPayPassword !== undefined) {
            this.newPayPassword = params.newPayPassword;
        }
        if (params.confirmPayPassword !== undefined) {
            this.confirmPayPassword = params.confirmPayPassword;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: ChangePayPasswordPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentPayPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__newPayPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPayPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentPayPassword.aboutToBeDeleted();
        this.__newPayPassword.aboutToBeDeleted();
        this.__confirmPayPassword.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentPayPassword: ObservedPropertySimplePU<string>;
    get currentPayPassword() {
        return this.__currentPayPassword.get();
    }
    set currentPayPassword(newValue: string) {
        this.__currentPayPassword.set(newValue);
    }
    private __newPayPassword: ObservedPropertySimplePU<string>;
    get newPayPassword() {
        return this.__newPayPassword.get();
    }
    set newPayPassword(newValue: string) {
        this.__newPayPassword.set(newValue);
    }
    private __confirmPayPassword: ObservedPropertySimplePU<string>;
    get confirmPayPassword() {
        return this.__confirmPayPassword.get();
    }
    set confirmPayPassword(newValue: string) {
        this.__confirmPayPassword.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    async handleSave() {
        if (!this.currentPayPassword.trim()) {
            promptAction.showToast({ message: '请输入当前支付密码' });
            return;
        }
        if (this.currentPayPassword.length !== 6) {
            promptAction.showToast({ message: '当前支付密码应为6位数字' });
            return;
        }
        if (!this.newPayPassword.trim()) {
            promptAction.showToast({ message: '请输入新支付密码' });
            return;
        }
        if (this.newPayPassword.length !== 6) {
            promptAction.showToast({ message: '新支付密码应为6位数字' });
            return;
        }
        // 验证是否为纯数字
        const numberRegex = /^\d{6}$/;
        if (!numberRegex.test(this.newPayPassword)) {
            promptAction.showToast({ message: '支付密码只能包含数字' });
            return;
        }
        if (!this.confirmPayPassword.trim()) {
            promptAction.showToast({ message: '请确认新支付密码' });
            return;
        }
        if (this.newPayPassword !== this.confirmPayPassword) {
            promptAction.showToast({ message: '两次输入的支付密码不一致' });
            return;
        }
        if (this.currentPayPassword === this.newPayPassword) {
            promptAction.showToast({ message: '新支付密码不能与当前密码相同' });
            return;
        }
        this.isLoading = true;
        try {
            // 调用API修改支付密码
            const changeData: ChangePayPasswordRequest = {
                currentPayPassword: this.currentPayPassword,
                newPayPassword: this.newPayPassword
            };
            await UserApi.changePayPassword(changeData);
            promptAction.showToast({ message: '支付密码修改成功' });
            router.back();
        }
        catch (error) {
            console.error('支付密码修改失败:', error);
            promptAction.showToast({ message: '支付密码修改失败，请检查当前密码是否正确' });
        }
        finally {
            this.isLoading = false;
        }
    }
    handleCancel() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(80:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(82:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({ width: { bottom: 1 }, color: '#E5E7EB' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(83:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(91:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认修改');
            Button.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(98:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#007AFF');
            Button.backgroundColor(Color.Transparent);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleSave();
            });
        }, Button);
        Button.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(114:7)", "entry");
            // 表单内容
            Column.padding(20);
            // 表单内容
            Column.backgroundColor('#FFFFFF');
            // 表单内容
            Column.margin({ top: 16, left: 16, right: 16 });
            // 表单内容
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 当前支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(116:9)", "entry");
            // 当前支付密码
            Column.width('100%');
            // 当前支付密码
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(117:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('当前支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(118:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(120);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入当前支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(123:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Number);
            TextInput.maxLength(6);
            TextInput.onChange((value: string) => {
                this.currentPayPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(138:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 当前支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 新支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(146:9)", "entry");
            // 新支付密码
            Column.width('100%');
            // 新支付密码
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(147:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(148:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(120);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入新支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(153:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Number);
            TextInput.maxLength(6);
            TextInput.onChange((value: string) => {
                this.newPayPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(168:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 新支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(176:9)", "entry");
            // 确认支付密码
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(177:11)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(178:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(120);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请确认新支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(183:13)", "entry");
            TextInput.fontSize(16);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.layoutWeight(1);
            TextInput.type(InputType.Number);
            TextInput.maxLength(6);
            TextInput.onChange((value: string) => {
                this.confirmPayPassword = value;
            });
        }, TextInput);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(198:11)", "entry");
            Divider.color('#E5E7EB');
            Divider.strokeWidth(1);
        }, Divider);
        // 确认支付密码
        Column.pop();
        // 表单内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码要求提示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(210:7)", "entry");
            // 支付密码要求提示
            Column.width('100%');
            // 支付密码要求提示
            Column.padding(20);
            // 支付密码要求提示
            Column.margin({ top: 16, left: 16, right: 16 });
            // 支付密码要求提示
            Column.backgroundColor('#F8F9FA');
            // 支付密码要求提示
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码要求：');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(211:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 支付密码必须为6位数字');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(217:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 请勿使用生日、手机号等易被猜测的密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(223:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 建议定期更换支付密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(229:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 支付密码要求提示
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/ChangePayPasswordPage.ets(240:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ChangePayPasswordPage";
    }
}
registerNamedRoute(() => new ChangePayPasswordPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/ChangePayPasswordPage", pageFullPath: "entry/src/main/ets/pages/ChangePayPasswordPage", integratedHsp: "false", moduleType: "followWithHap" });
