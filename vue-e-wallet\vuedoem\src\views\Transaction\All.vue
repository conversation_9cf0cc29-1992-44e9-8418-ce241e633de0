<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { transactionApi } from '@/api/index.js'
import { TransactionStorage } from '@/utils/transactionStorage.js'

const transactions = ref([])
const loading = ref(false)
const filterType = ref('all')

// 获取当前用户ID
const getCurrentUserId = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.id || 1
}

// 交易类型映射
const typeMap = {
  'PAYMENT': '支付',
  'DEPOSIT': '充值',
  'WITHDRAW': '提现',
  'TRANSFER': '转账',
  'RECEIVE': '收款'
}

// 支付方式映射
const methodMap = {
  'WALLET': '钱包',
  'BANK_CARD': '银行卡'
}

// 支付渠道映射
const channelMap = {
  'MERCHANT': '商户',
  'QR_CODE': '扫码',
  'NFC': 'NFC'
}

// 过滤后的交易记录
const filteredTransactions = computed(() => {
  if (filterType.value === 'all') {
    return transactions.value
  }
  return transactions.value.filter(t => t.transactionType === filterType.value)
})

// 获取交易记录
const fetchTransactions = async () => {
  try {
    loading.value = true
    const userId = getCurrentUserId()
    console.log('正在获取用户ID:', userId, '的交易记录...')

    try {
      const response = await transactionApi.getAllTransactions(userId)
      console.log('API响应:', response)

      // 处理响应数据 - 兼容不同的响应格式
      let transactionData = []

      if (response && response.data && response.data.code === 200 && response.data.data) {
        // Spring Boot返回格式: {code: 200, msg: "查询成功", data: [...]}
        transactionData = Array.isArray(response.data.data) ? response.data.data : []
        console.log('✅ 使用 response.data.data，数据量:', transactionData.length)
      } else if (response && response.data && Array.isArray(response.data)) {
        // 直接返回数组格式
        transactionData = response.data
        console.log('✅ 使用 response.data，数据量:', transactionData.length)
      } else if (Array.isArray(response)) {
        // 如果response本身就是数组
        transactionData = response
        console.log('✅ 使用 response，数据量:', transactionData.length)
      }

      // 去重处理 - 基于交易ID去重
      const uniqueTransactions = removeDuplicateTransactions(transactionData)
      transactions.value = uniqueTransactions
      console.log('交易记录获取成功，数量:', uniqueTransactions.length)
    } catch (apiError) {
      console.warn('API调用失败，使用本地存储数据:', apiError.message)

      // 从本地存储获取交易记录
      const localTransactions = TransactionStorage.getAllTransactions()
      transactions.value = localTransactions
      console.log('使用本地存储交易数据，数量:', localTransactions.length)
    }
  } catch (error) {
    console.error('获取交易记录失败:', error)
    transactions.value = []
  } finally {
    loading.value = false
  }
}

// 去重函数 - 基于交易ID去重
const removeDuplicateTransactions = (transactions) => {
  if (!Array.isArray(transactions)) {
    return []
  }

  const seen = new Set()
  const uniqueTransactions = []

  for (const transaction of transactions) {
    // 使用交易ID作为唯一标识
    const key = transaction.id

    if (!seen.has(key)) {
      seen.add(key)
      uniqueTransactions.push(transaction)
    } else {
      console.log(`🔄 发现重复交易记录，ID: ${key}`)
    }
  }

  return uniqueTransactions
}



// 格式化金额显示
const formatAmount = (type, amount) => {
  const prefix = ['DEPOSIT', 'RECEIVE'].includes(type) ? '+' : '-'
  return `${prefix}¥${Number(amount).toFixed(2)}`
}

// 获取交易类型标签样式
const getTypeTagType = (type) => {
  const typeStyles = {
    'PAYMENT': 'danger',
    'DEPOSIT': 'success',
    'WITHDRAW': 'warning',
    'TRANSFER': 'primary',
    'RECEIVE': 'success'
  }
  return typeStyles[type] || 'info'
}





onMounted(() => {
  fetchTransactions()
})


</script>

<template>
  <div>
    <div style="max-height: 600px; overflow-y: auto;">
      <el-table :data="transactions" v-loading="loading" height="100%">
      <el-table-column prop="id" label="交易ID" width="120" />
      <el-table-column label="类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.transactionType)">
            {{ typeMap[row.transactionType] || row.transactionType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="金额" width="120">
        <template #default="{ row }">
          <span :class="['DEPOSIT', 'RECEIVE'].includes(row.transactionType) ? 'amount-positive' : 'amount-negative'">
            {{ formatAmount(row.transactionType, row.amount) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="targetAccount" label="对象/描述" width="150" />
      <el-table-column label="支付方式" width="100">
        <template #default="{ row }">
          <el-tag size="small" :type="row.paymentMethod === 'WALLET' ? 'success' : 'primary'">
            {{ methodMap[row.paymentMethod] || row.paymentMethod }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付渠道" width="100">
        <template #default="{ row }">
          <span v-if="row.paymentChannel">{{ channelMap[row.paymentChannel] || row.paymentChannel }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="时间" width="160">
        <template #default="{ row }">
          {{ new Date(row.transactionTime).toLocaleString() }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'SUCCESS' ? 'success' : row.status === 'PENDING' ? 'warning' : 'danger'">
            {{ row.status === 'SUCCESS' ? '成功' : row.status === 'PENDING' ? '处理中' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>

      </el-table>
    </div>
  </div>
</template>

<style scoped>
.amount-positive {
  color: #67c23a;
  font-weight: bold;
}

.amount-negative {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-button--danger.is-plain) {
  border-color: #f56c6c;
  color: #f56c6c;
}

:deep(.el-button--danger.is-plain:hover) {
  background-color: #f56c6c;
  color: #fff;
}
</style>
