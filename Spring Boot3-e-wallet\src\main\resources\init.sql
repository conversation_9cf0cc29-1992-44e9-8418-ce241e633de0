-- 创建数据库
CREATE DATABASE IF NOT EXISTS e_wallet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE e_wallet;

-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    mobile VARCHAR(20) COMMENT '手机号',
    account DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    payment_password VARCHAR(100) COMMENT '支付密码',
    payment_limit DECIMAL(10,2) DEFAULT 5000.00 COMMENT '支付限额'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入测试用户数据
INSERT INTO user (username, password, mobile, account, balance, payment_password, payment_limit) VALUES
('1111', '123456', '***********', 10000.00, 10000.00, '123456', 5000.00),
('admin', 'admin123', '***********', 50000.00, 50000.00, '123456', 10000.00),
('test', 'test123', '***********', 1000.00, 1000.00, '123456', 2000.00)
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    mobile = VALUES(mobile),
    account = VALUES(account),
    balance = VALUES(balance),
    payment_password = VALUES(payment_password),
    payment_limit = VALUES(payment_limit);

-- 创建银行卡表
CREATE TABLE IF NOT EXISTS bank_card (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    card_number VARCHAR(20) NOT NULL UNIQUE COMMENT '银行卡号',
    card_type VARCHAR(10) NOT NULL COMMENT '卡片类型：DEBIT储蓄卡/CREDIT信用卡',
    bank_name VARCHAR(50) NOT NULL COMMENT '银行名称',
    balance DECIMAL(12,2) DEFAULT 0.00 COMMENT '卡片余额',
    credit_limit DECIMAL(12,2) DEFAULT 0.00 COMMENT '信用额度（仅信用卡）',
    bind_status INT DEFAULT 1 COMMENT '绑定状态：1绑定/0解绑',
    card_password VARCHAR(100) DEFAULT '123456' COMMENT '银行卡密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行卡表';

-- 创建交易记录表
CREATE TABLE IF NOT EXISTS transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    transaction_type VARCHAR(20) NOT NULL COMMENT '交易类型：PAYMENT支付/DEPOSIT充值/WITHDRAW提现/TRANSFER转账/RECEIVE收款',
    amount DECIMAL(12,2) NOT NULL COMMENT '交易金额',
    transaction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
    payment_method VARCHAR(20) COMMENT '支付方式：WALLET钱包/BANK_CARD银行卡',
    payment_channel VARCHAR(20) COMMENT '支付渠道：MERCHANT商户/QR_CODE扫码/NFC近场',
    target_account VARCHAR(100) COMMENT '目标账户',
    card_id BIGINT COMMENT '关联银行卡ID',
    description VARCHAR(200) COMMENT '交易描述',
    status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '交易状态：SUCCESS成功/PENDING处理中/FAILED失败',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (card_id) REFERENCES bank_card(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易记录表';

-- 插入测试银行卡数据
INSERT INTO bank_card (user_id, card_number, card_type, bank_name, balance, credit_limit, bind_status, card_password) VALUES
(1, '****************', 'DEBIT', '中国银行', 4988.00, 0.00, 1, '123456'),
(1, '****************', 'CREDIT', '建设银行', -100.00, 20000.00, 0, '654321'),
(2, '****************', 'DEBIT', '工商银行', 100.00, 0.00, 1, '111111'),
(1, '****************', 'DEBIT', '中国银行', 5000.00, 0.00, 1, '888888'),
(2, '****************', 'DEBIT', '中国银行', 4.00, 0.00, 1, '999999'),
(2, '******************1', 'CREDIT', '王五', 0.00, 0.00, 1, '555555'),
(2, '0908090807777111111', 'DEBIT', '*********', 0.00, 0.00, 1, '777777'),
(2, '****************', 'CREDIT', '中国', 0.00, 0.00, 1, '222222'),
(3, '****************222', 'CREDIT', '2222', 0.00, 0.00, 1, '333333')
ON DUPLICATE KEY UPDATE
    card_type = VALUES(card_type),
    bank_name = VALUES(bank_name),
    balance = VALUES(balance),
    credit_limit = VALUES(credit_limit),
    bind_status = VALUES(bind_status),
    card_password = VALUES(card_password);

-- 插入测试交易记录
INSERT INTO transaction (user_id, transaction_type, amount, payment_method, payment_channel, target_account, description) VALUES
(1, 'DEPOSIT', 1000.00, 'BANK_CARD', 'MERCHANT', '钱包充值', '从银行卡充值到钱包'),
(1, 'PAYMENT', 150.00, 'WALLET', 'MERCHANT', '星巴克', '商户支付'),
(1, 'TRANSFER', 500.00, 'WALLET', 'MERCHANT', '张三', '转账给朋友'),
(2, 'DEPOSIT', 2000.00, 'BANK_CARD', 'MERCHANT', '钱包充值', '从银行卡充值到钱包'),
(2, 'PAYMENT', 200.00, 'BANK_CARD', 'QR_CODE', '麦当劳', '扫码支付')
ON DUPLICATE KEY UPDATE
    transaction_type = VALUES(transaction_type),
    amount = VALUES(amount),
    payment_method = VALUES(payment_method),
    payment_channel = VALUES(payment_channel),
    target_account = VALUES(target_account),
    description = VALUES(description);
