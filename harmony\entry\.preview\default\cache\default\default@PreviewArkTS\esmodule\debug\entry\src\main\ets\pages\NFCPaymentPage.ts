if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NFCPaymentPage_Params {
    isWaiting?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class NFCPaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isWaiting = new ObservedPropertySimplePU(false, this, "isWaiting");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NFCPaymentPage_Params) {
        if (params.isWaiting !== undefined) {
            this.isWaiting = params.isWaiting;
        }
    }
    updateStateVars(params: NFCPaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isWaiting.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isWaiting.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isWaiting: ObservedPropertySimplePU<boolean>;
    get isWaiting() {
        return this.__isWaiting.get();
    }
    set isWaiting(newValue: boolean) {
        this.__isWaiting.set(newValue);
    }
    aboutToAppear() {
        console.log('NFCPaymentPage 页面加载成功');
    }
    // 处理支付完成
    private handlePaymentComplete() {
        promptAction.showToast({
            message: 'NFC支付成功！',
            duration: 2000
        });
        // 延迟跳转到交易记录页面
        setTimeout(() => {
            router.replaceUrl({
                url: 'pages/TransactionListPage'
            });
        }, 2000);
    }
    // 取消支付
    private handleCancel() {
        router.back();
    }
    // 开始等待NFC设备
    private startWaitingForNFC() {
        this.isWaiting = true;
        // 模拟NFC检测过程
        setTimeout(() => {
            this.isWaiting = false;
            promptAction.showToast({
                message: '检测到NFC设备，正在处理支付...',
                duration: 2000
            });
            // 模拟支付处理
            setTimeout(() => {
                this.handlePaymentComplete();
            }, 2000);
        }, 4000);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(53:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(55:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('×');
            Button.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(56:9)", "entry");
            Button.fontSize(24);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('NFC支付');
            Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(64:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(71:9)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // NFC支付内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(84:7)", "entry");
            // NFC支付内容区域
            Column.layoutWeight(1);
            // NFC支付内容区域
            Column.padding(20);
            // NFC支付内容区域
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // NFC图标和状态
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(86:9)", "entry");
            // NFC图标和状态
            Column.layoutWeight(1);
            // NFC图标和状态
            Column.width('100%');
            // NFC图标和状态
            Column.justifyContent(FlexAlign.Center);
            // NFC图标和状态
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isWaiting) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 等待中状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(89:13)", "entry");
                        // 等待中状态
                        Column.justifyContent(FlexAlign.Center);
                        // 等待中状态
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 动画效果的NFC图标
                        Text.create('📡');
                        Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(91:15)", "entry");
                        // 动画效果的NFC图标
                        Text.fontSize(80);
                        // 动画效果的NFC图标
                        Text.margin({ bottom: 20 });
                    }, Text);
                    // 动画效果的NFC图标
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(95:15)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#4A90E2');
                        LoadingProgress.margin({ bottom: 20 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在等待NFC设备...');
                        Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(101:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#4A90E2');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    // 等待中状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 等待状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(110:13)", "entry");
                        // 等待状态
                        Column.justifyContent(FlexAlign.Center);
                        // 等待状态
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📡');
                        Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(111:15)", "entry");
                        Text.fontSize(80);
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('等待NFC设备');
                        Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(115:15)", "entry");
                        Text.fontSize(20);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.margin({ bottom: 10 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请将支持NFC的设备靠近感应区域');
                        Text.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(121:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 40 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 开始检测按钮
                        Button.createWithLabel('开始NFC支付');
                        Button.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(128:15)", "entry");
                        // 开始检测按钮
                        Button.fontSize(16);
                        // 开始检测按钮
                        Button.fontColor('#FFFFFF');
                        // 开始检测按钮
                        Button.backgroundColor('#4A90E2');
                        // 开始检测按钮
                        Button.borderRadius(8);
                        // 开始检测按钮
                        Button.width(160);
                        // 开始检测按钮
                        Button.height(44);
                        // 开始检测按钮
                        Button.onClick(() => {
                            if (!this.isWaiting) {
                                this.startWaitingForNFC();
                            }
                        });
                    }, Button);
                    // 开始检测按钮
                    Button.pop();
                    // 等待状态
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // NFC图标和状态
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(151:9)", "entry");
            // 底部操作按钮
            Row.width('100%');
            // 底部操作按钮
            Row.justifyContent(FlexAlign.Center);
            // 底部操作按钮
            Row.margin({ bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(152:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F8F9FA');
            Button.border({ width: 1, color: '#E5E5E5' });
            Button.borderRadius(8);
            Button.width(100);
            Button.height(44);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isWaiting) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('停止检测');
                        Button.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(165:13)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#F44336');
                        Button.borderRadius(8);
                        Button.width(120);
                        Button.height(44);
                        Button.margin({ left: 20 });
                        Button.onClick(() => {
                            this.isWaiting = false;
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('确认NFC支付');
                        Button.debugLine("entry/src/main/ets/pages/NFCPaymentPage.ets(177:13)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#4A90E2');
                        Button.borderRadius(8);
                        Button.width(140);
                        Button.height(44);
                        Button.margin({ left: 20 });
                        Button.onClick(() => {
                            this.handlePaymentComplete();
                        });
                    }, Button);
                    Button.pop();
                });
            }
        }, If);
        If.pop();
        // 底部操作按钮
        Row.pop();
        // NFC支付内容区域
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "NFCPaymentPage";
    }
}
registerNamedRoute(() => new NFCPaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/NFCPaymentPage", pageFullPath: "entry/src/main/ets/pages/NFCPaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
