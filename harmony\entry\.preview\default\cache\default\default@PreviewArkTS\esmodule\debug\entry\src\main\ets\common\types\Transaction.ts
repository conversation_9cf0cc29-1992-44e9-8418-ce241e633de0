export enum TransactionStatus {
    COMPLETED = "COMPLETED",
    PROCESSING = "PROCESSING",
    FAILED = "FAILED",
    CANCELED = "CANCELED"
}
export enum TransactionType {
    TRANSFER = "TRANSFER",
    DEPOSIT = "DEPOSIT",
    WITHDRAW = "WITHDRAW",
    CONSUME = "CONSUME"
}
export interface Transaction {
    id: number;
    type: TransactionType;
    amount: number;
    target: string;
    time: string;
    status: TransactionStatus;
}
