package com.icss.spring.mapper;

import com.icss.spring.entity.BankCard;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

public interface BankCardMapper {
    @Select("SELECT * FROM bank_card WHERE user_id = #{userId}")
    List<BankCard> getCardsByUserId(@Param("userId") Long userId);

    @Select("SELECT * FROM bank_card WHERE id = #{id}")
    BankCard getCardById(@Param("id") Long id);

    @Insert("INSERT INTO bank_card(user_id, card_number, card_type, bank_name, balance, credit_limit, bind_status) " +
            "VALUES(#{userId}, #{cardNumber}, #{cardType}, #{bankName}, #{balance}, #{creditLimit}, #{bindStatus})")
    int addBankCard(BankCard bankCard);

    @Update("UPDATE bank_card SET bind_status = #{bindStatus} WHERE id = #{id}")
    int updateBindStatus(@Param("id") Long id, @Param("bindStatus") Integer bindStatus);

    @Update("UPDATE bank_card SET balance = balance + #{amount} WHERE id = #{id}")
    int updateCardBalance(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Select("SELECT * FROM bank_card WHERE user_id = #{userId} AND bind_status = 1")
    List<BankCard> getBoundCardsByUserId(@Param("userId") Long userId);

    @Update("UPDATE bank_card SET bank_name = #{bankName}, card_number = #{cardNumber}, " +
            "card_type = #{cardType}, credit_limit = #{creditLimit} WHERE id = #{id}")
    int updateBankCard(BankCard bankCard);

    @Delete("DELETE FROM bank_card WHERE id = #{cardId}")
    int deleteBankCard(@Param("cardId") Long cardId);

    // 根据银行卡号查询银行卡信息
    @Select("SELECT * FROM bank_card WHERE card_number = #{cardNumber}")
    BankCard getCardByCardNumber(@Param("cardNumber") String cardNumber);

    // 根据银行卡号查询用户ID
    @Select("SELECT user_id FROM bank_card WHERE card_number = #{cardNumber}")
    Long getUserIdByCardNumber(@Param("cardNumber") String cardNumber);


}