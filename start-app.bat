@echo off
echo Starting Spring Boot Application...
cd "Spring Boot3-e-wallet"

echo Checking if <PERSON><PERSON> is available...
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo Maven not found in PATH. Trying to use existing JAR file...
    if exist "target\spring-e-wallet-1.0-SNAPSHOT.jar" (
        echo Found existing JAR file, starting application...
        java -jar "target\spring-e-wallet-1.0-SNAPSHOT.jar"
    ) else (
        echo No JAR file found. Please compile the application first.
        echo You can use your IDE to run the SpringEWalletApplication class.
        pause
    )
) else (
    echo Maven found, starting with Maven...
    mvn spring-boot:run
)
