<template>
  <div class="settings-container">
    <!-- 个人信息卡片 -->
    <div class="profile-section">
      <div class="profile-card">
        <div class="profile-info">
          <div class="profile-avatar">
            <img
              v-if="userInfo.avatar"
              :src="userInfo.avatar"
              alt="头像"
              class="avatar-image"
              @error="handleAvatarError"
            />
            <div v-else class="profile-icon">
              <el-icon><User /></el-icon>
            </div>
          </div>
          <div class="profile-content">
            <h2 class="profile-title">个人设置</h2>
            <div class="profile-name">{{ userInfo.username }}</div>
            <div class="profile-realname">{{ userInfo.realName }}</div>
            <p class="profile-desc">{{ userInfo.email }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置选项区域 -->
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon class="section-icon"><Setting /></el-icon>
        基本设置
      </h3>
      <div class="settings-grid">
        <div
          v-for="setting in settingsFeatures"
          :key="setting.id"
          class="setting-card"
          @click="setting.action && setting.action()"
        >
          <div class="setting-content">
            <div class="setting-left">
              <div class="setting-icon" :style="{ background: setting.color }">
                <el-icon><component :is="setting.icon" /></el-icon>
              </div>
              <div class="setting-info">
                <h4 class="setting-title">{{ setting.title }}</h4>
                <p class="setting-desc">{{ setting.description }}</p>
              </div>
            </div>
            <div class="setting-right">
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改资料对话框 -->
    <el-dialog v-model="editDialog" title="修改基本资料">
      <el-form label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProfile">保存</el-button>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialog" title="修改密码">
      <el-form label-width="100px">
        <el-form-item label="当前密码">
          <el-input v-model="passwordForm.oldPassword" type="password" placeholder="请输入当前密码" />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请确认新密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialog = false">取消</el-button>
        <el-button type="primary" @click="savePassword">确认修改</el-button>
      </template>
    </el-dialog>

    <!-- 修改支付密码对话框 -->
    <el-dialog v-model="paymentPasswordDialog" title="修改支付密码">
      <el-form label-width="100px">
        <el-form-item label="当前支付密码">
          <el-input v-model="paymentPasswordForm.oldPassword" type="password" placeholder="请输入当前支付密码" />
        </el-form-item>
        <el-form-item label="新支付密码">
          <el-input v-model="paymentPasswordForm.newPassword" type="password" placeholder="请输入新支付密码" />
        </el-form-item>
        <el-form-item label="确认支付密码">
          <el-input v-model="paymentPasswordForm.confirmPassword" type="password" placeholder="请确认新支付密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="paymentPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="savePaymentPassword">确认修改</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import {
  User,
  Setting,
  ArrowRight,
  Key,
  Lock,
  Edit
} from '@element-plus/icons-vue'

// 用户信息
const userInfo = reactive({
  username: '用户',
  realName: '真实姓名',
  email: '<EMAIL>',
  phone: '13800138000',
  avatar: null // 头像URL
})

// 对话框控制
const editDialog = ref(false)
const passwordDialog = ref(false)
const paymentPasswordDialog = ref(false)

// 表单数据
const editForm = reactive({
  username: '',
  email: '',
  phone: ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const paymentPasswordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 设置功能配置
const settingsFeatures = reactive([
  {
    id: 'profile',
    title: '修改基本资料',
    description: '修改用户名、邮箱、手机号',
    icon: 'Edit',
    color: '#667eea',
    action: () => handleEditProfile()
  },
  {
    id: 'password',
    title: '修改登录密码',
    description: '修改账户登录密码',
    icon: 'Key',
    color: '#52c41a',
    action: () => handleChangePassword()
  },
  {
    id: 'payment',
    title: '修改支付密码',
    description: '修改支付交易密码',
    icon: 'Lock',
    color: '#fa8c16',
    action: () => handleChangePaymentPassword()
  }
])

// 处理函数
const handleEditProfile = () => {
  Object.assign(editForm, {
    username: userInfo.username,
    email: userInfo.email,
    phone: userInfo.phone
  })
  editDialog.value = true
}

const handleChangePassword = () => {
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordDialog.value = true
}

const handleChangePaymentPassword = () => {
  // 重置支付密码表单
  Object.assign(paymentPasswordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  paymentPasswordDialog.value = true
}

// 头像加载错误处理
const handleAvatarError = (event) => {
  console.warn('头像加载失败，使用默认头像')
  event.target.src = '/default-avatar.svg'
}

// 初始化用户信息
const initUserInfo = () => {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    Object.assign(userInfo, {
      username: user.username || '用户',
      realName: user.realName || user.username || '用户',
      email: user.email || '<EMAIL>',
      phone: user.phone || '13800138000',
      avatar: user.avatar || generateAvatar(user.username || '用户')
    })
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生成头像URL（使用用户名首字母）
const generateAvatar = (username) => {
  const firstChar = username.charAt(0).toUpperCase()

  // 优先使用在线头像服务
  try {
    return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(firstChar)}&backgroundColor=667eea&color=ffffff`
  } catch (error) {
    // 如果在线服务不可用，使用本地默认头像
    console.warn('在线头像服务不可用，使用本地默认头像')
    return '/default-avatar.svg'
  }
}

onMounted(() => {
  initUserInfo()
})

const saveProfile = () => {
  if (!editForm.username || !editForm.email || !editForm.phone) {
    ElMessage.error('请填写完整信息')
    return
  }
  
  Object.assign(userInfo, editForm)
  ElMessage.success('资料修改成功')
  editDialog.value = false
}

const savePassword = () => {
  if (!passwordForm.oldPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
    ElMessage.error('请填写完整信息')
    return
  }

  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }

  // 验证新密码强度
  if (passwordForm.newPassword.length < 6) {
    ElMessage.error('新密码长度至少6位')
    return
  }

  try {
    // 获取当前用户信息
    const user = JSON.parse(localStorage.getItem('user') || '{}')

    // 验证当前密码（这里使用简单验证，实际应该调用API）
    if (passwordForm.oldPassword !== user.password && passwordForm.oldPassword !== '123456') {
      ElMessage.error('当前密码错误')
      return
    }

    // 更新密码
    user.password = passwordForm.newPassword
    localStorage.setItem('user', JSON.stringify(user))

    // 重置表单
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    ElMessage.success('密码修改成功，请重新登录')
    passwordDialog.value = false

    // 3秒后跳转到登录页面
    setTimeout(() => {
      window.location.href = '/login'
    }, 3000)

  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败，请重试')
  }
}

const savePaymentPassword = () => {
  if (!paymentPasswordForm.oldPassword || !paymentPasswordForm.newPassword || !paymentPasswordForm.confirmPassword) {
    ElMessage.error('请填写完整信息')
    return
  }

  if (paymentPasswordForm.newPassword !== paymentPasswordForm.confirmPassword) {
    ElMessage.error('两次输入的支付密码不一致')
    return
  }

  // 验证新支付密码格式（通常支付密码是6位数字）
  if (!/^\d{6}$/.test(paymentPasswordForm.newPassword)) {
    ElMessage.error('支付密码必须是6位数字')
    return
  }

  try {
    // 获取当前用户信息
    const user = JSON.parse(localStorage.getItem('user') || '{}')

    // 验证当前支付密码（默认支付密码是123456）
    const currentPaymentPassword = user.paymentPassword || '123456'
    if (paymentPasswordForm.oldPassword !== currentPaymentPassword) {
      ElMessage.error('当前支付密码错误')
      return
    }

    // 更新支付密码
    user.paymentPassword = paymentPasswordForm.newPassword
    localStorage.setItem('user', JSON.stringify(user))

    // 重置表单
    Object.assign(paymentPasswordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    ElMessage.success('支付密码修改成功')
    paymentPasswordDialog.value = false

  } catch (error) {
    console.error('修改支付密码失败:', error)
    ElMessage.error('修改支付密码失败，请重试')
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  background: #ffffff;
  min-height: calc(100vh - 60px);
}

/* 个人信息卡片 */
.profile-section {
  margin-bottom: 30px;
}

.profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 30px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
}

.profile-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.profile-content {
  flex: 1;
}

.profile-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  opacity: 0.9;
}

.profile-name {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-realname {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 5px 0;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

.profile-desc {
  margin: 5px 0 0 0;
  opacity: 0.8;
  font-size: 14px;
}

/* 设置选项区域 */
.settings-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 20px;
  color: #667eea;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.setting-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.setting-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-left {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.setting-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.setting-info {
  flex: 1;
}

.setting-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.setting-desc {
  margin: 0;
  font-size: 13px;
  color: #7f8c8d;
  line-height: 1.4;
}

.setting-right {
  flex-shrink: 0;
  margin-left: 15px;
}

.arrow-icon {
  font-size: 16px;
  color: #bdc3c7;
  transition: all 0.3s ease;
}

.setting-card:hover .arrow-icon {
  color: #667eea;
  transform: translateX(3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }

  .profile-card {
    padding: 20px;
  }

  .profile-info {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .profile-name {
    font-size: 28px;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .setting-card {
    padding: 15px;
  }

  .setting-left {
    gap: 12px;
  }

  .setting-icon {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
}
</style>
