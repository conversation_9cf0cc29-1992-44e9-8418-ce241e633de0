if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface EditBankCardPage_Params {
    cardId?: number;
    formCardNo?: string;
    formCardType?: string;
    formBankName?: string;
    formHolderName?: string;
    isLoading?: boolean;
    isSubmitting?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
class EditBankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardId = new ObservedPropertySimplePU(0, this, "cardId");
        this.__formCardNo = new ObservedPropertySimplePU('', this, "formCardNo");
        this.__formCardType = new ObservedPropertySimplePU('储蓄卡', this, "formCardType");
        this.__formBankName = new ObservedPropertySimplePU('', this, "formBankName");
        this.__formHolderName = new ObservedPropertySimplePU('', this, "formHolderName");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isSubmitting = new ObservedPropertySimplePU(false, this, "isSubmitting");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: EditBankCardPage_Params) {
        if (params.cardId !== undefined) {
            this.cardId = params.cardId;
        }
        if (params.formCardNo !== undefined) {
            this.formCardNo = params.formCardNo;
        }
        if (params.formCardType !== undefined) {
            this.formCardType = params.formCardType;
        }
        if (params.formBankName !== undefined) {
            this.formBankName = params.formBankName;
        }
        if (params.formHolderName !== undefined) {
            this.formHolderName = params.formHolderName;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isSubmitting !== undefined) {
            this.isSubmitting = params.isSubmitting;
        }
    }
    updateStateVars(params: EditBankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardId.purgeDependencyOnElmtId(rmElmtId);
        this.__formCardNo.purgeDependencyOnElmtId(rmElmtId);
        this.__formCardType.purgeDependencyOnElmtId(rmElmtId);
        this.__formBankName.purgeDependencyOnElmtId(rmElmtId);
        this.__formHolderName.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isSubmitting.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardId.aboutToBeDeleted();
        this.__formCardNo.aboutToBeDeleted();
        this.__formCardType.aboutToBeDeleted();
        this.__formBankName.aboutToBeDeleted();
        this.__formHolderName.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isSubmitting.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardId: ObservedPropertySimplePU<number>;
    get cardId() {
        return this.__cardId.get();
    }
    set cardId(newValue: number) {
        this.__cardId.set(newValue);
    }
    private __formCardNo: ObservedPropertySimplePU<string>;
    get formCardNo() {
        return this.__formCardNo.get();
    }
    set formCardNo(newValue: string) {
        this.__formCardNo.set(newValue);
    }
    private __formCardType: ObservedPropertySimplePU<string>;
    get formCardType() {
        return this.__formCardType.get();
    }
    set formCardType(newValue: string) {
        this.__formCardType.set(newValue);
    }
    private __formBankName: ObservedPropertySimplePU<string>;
    get formBankName() {
        return this.__formBankName.get();
    }
    set formBankName(newValue: string) {
        this.__formBankName.set(newValue);
    }
    private __formHolderName: ObservedPropertySimplePU<string>;
    get formHolderName() {
        return this.__formHolderName.get();
    }
    set formHolderName(newValue: string) {
        this.__formHolderName.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isSubmitting: ObservedPropertySimplePU<boolean>;
    get isSubmitting() {
        return this.__isSubmitting.get();
    }
    set isSubmitting(newValue: boolean) {
        this.__isSubmitting.set(newValue);
    }
    aboutToAppear() {
        console.log('EditBankCardPage aboutToAppear');
        // 获取路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params && params.cardId) {
            this.cardId = params.cardId as number;
            this.loadCardData();
        }
        else {
            promptAction.showToast({ message: '参数错误' });
            router.back();
        }
    }
    /**
     * 加载银行卡数据
     */
    async loadCardData() {
        this.isLoading = true;
        try {
            const cardList = await BankCardApi.getCardList();
            const card = cardList.find(c => c.cardId === this.cardId);
            if (card) {
                this.formCardNo = card.cardNo;
                this.formCardType = card.cardType === BankCardType.DEBIT ? '储蓄卡' : '信用卡';
                this.formBankName = card.bankName;
                this.formHolderName = card.holderName;
            }
            else {
                promptAction.showToast({ message: '银行卡不存在' });
                router.back();
            }
        }
        catch (error) {
            console.error('加载银行卡数据失败:', error);
            promptAction.showToast({ message: '加载失败' });
            router.back();
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(59:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(61:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            // 顶部导航
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(62:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(63:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改银行卡');
            Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(76:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(84:9)", "entry");
            // 占位，保持标题居中
            Row.width(40);
            // 占位，保持标题居中
            Row.height(40);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(94:9)", "entry");
                        // 加载状态
                        Column.width('100%');
                        // 加载状态
                        Column.layoutWeight(1);
                        // 加载状态
                        Column.justifyContent(FlexAlign.Center);
                        // 加载状态
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(95:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(100:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 12 });
                    }, Text);
                    Text.pop();
                    // 加载状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 表单内容
                        Column.create({ space: 20 });
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(111:9)", "entry");
                        // 表单内容
                        Column.width('100%');
                        // 表单内容
                        Column.padding({ left: 20, right: 20, top: 24 });
                        // 表单内容
                        Column.layoutWeight(1);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡号
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(113:11)", "entry");
                        // 银行卡号
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(114:13)", "entry");
                        Row.alignSelf(ItemAlign.Start);
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('* ');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(115:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#FF4444');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('银行卡号');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(118:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入16-19位银行卡号', text: this.formCardNo });
                        TextInput.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(125:13)", "entry");
                        TextInput.width('100%');
                        TextInput.height(48);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.borderRadius(8);
                        TextInput.border({ width: 1, color: '#E0E0E0' });
                        TextInput.padding({ left: 16, right: 16 });
                        TextInput.fontSize(16);
                        TextInput.type(InputType.Number);
                        TextInput.onChange((value: string) => {
                            this.formCardNo = value;
                        });
                    }, TextInput);
                    // 银行卡号
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 持卡人姓名
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(141:11)", "entry");
                        // 持卡人姓名
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(142:13)", "entry");
                        Row.alignSelf(ItemAlign.Start);
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('* ');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(143:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#FF4444');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('持卡人姓名');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(146:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入持卡人姓名', text: this.formHolderName });
                        TextInput.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(153:13)", "entry");
                        TextInput.width('100%');
                        TextInput.height(48);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.borderRadius(8);
                        TextInput.border({ width: 1, color: '#E0E0E0' });
                        TextInput.padding({ left: 16, right: 16 });
                        TextInput.fontSize(16);
                        TextInput.onChange((value: string) => {
                            this.formHolderName = value;
                        });
                    }, TextInput);
                    // 持卡人姓名
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 卡片类型
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(168:11)", "entry");
                        // 卡片类型
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(169:13)", "entry");
                        Row.alignSelf(ItemAlign.Start);
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('* ');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(170:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#FF4444');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('卡片类型');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(173:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(180:13)", "entry");
                        Row.width('100%');
                        Row.height(48);
                        Row.backgroundColor('#F8F9FA');
                        Row.borderRadius(8);
                        Row.border({ width: 1, color: '#E0E0E0' });
                        Row.padding({ left: 16, right: 16 });
                        Row.onClick(() => {
                            this.showCardTypeSelector();
                        });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.formCardType);
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(181:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('▼');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(186:15)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999999');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    // 卡片类型
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行名称
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(203:11)", "entry");
                        // 银行名称
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(204:13)", "entry");
                        Row.alignSelf(ItemAlign.Start);
                        Row.margin({ bottom: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('* ');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(205:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#FF4444');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('银行名称');
                        Text.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(208:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#333333');
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入银行名称', text: this.formBankName });
                        TextInput.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(215:13)", "entry");
                        TextInput.width('100%');
                        TextInput.height(48);
                        TextInput.backgroundColor('#F8F9FA');
                        TextInput.borderRadius(8);
                        TextInput.border({ width: 1, color: '#E0E0E0' });
                        TextInput.padding({ left: 16, right: 16 });
                        TextInput.fontSize(16);
                        TextInput.onChange((value: string) => {
                            this.formBankName = value;
                        });
                    }, TextInput);
                    // 银行名称
                    Column.pop();
                    // 表单内容
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部按钮
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(234:9)", "entry");
                        // 底部按钮
                        Column.width('100%');
                        // 底部按钮
                        Column.padding({ left: 20, right: 20, bottom: 20 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel(this.isSubmitting ? '保存中...' : '保存修改');
                        Button.debugLine("entry/src/main/ets/pages/EditBankCardPage.ets(235:11)", "entry");
                        Button.width('100%');
                        Button.height(48);
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor(this.isSubmitting ? '#CCCCCC' : '#1976D2');
                        Button.borderRadius(8);
                        Button.enabled(!this.isSubmitting);
                        Button.onClick(() => {
                            this.confirmEditBankCard();
                        });
                    }, Button);
                    Button.pop();
                    // 底部按钮
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 显示卡片类型选择器
     */
    async showCardTypeSelector() {
        try {
            const result = await promptAction.showActionMenu({
                title: '选择卡片类型',
                buttons: [
                    { text: '储蓄卡', color: '#1976D2' },
                    { text: '信用卡', color: '#1976D2' }
                ]
            });
            if (result.index === 0) {
                this.formCardType = '储蓄卡';
            }
            else if (result.index === 1) {
                this.formCardType = '信用卡';
            }
        }
        catch (error) {
            console.error('选择卡片类型失败:', error);
        }
    }
    /**
     * 确认修改银行卡
     */
    async confirmEditBankCard() {
        // 表单验证
        if (!this.formCardNo || this.formCardNo.length < 16) {
            promptAction.showToast({ message: '请输入正确的银行卡号' });
            return;
        }
        if (!this.formHolderName) {
            promptAction.showToast({ message: '请输入持卡人姓名' });
            return;
        }
        if (!this.formBankName) {
            promptAction.showToast({ message: '请输入银行名称' });
            return;
        }
        this.isSubmitting = true;
        try {
            // 更新银行卡信息
            const updatedCard: BankCard = {
                cardId: this.cardId,
                userId: 1,
                cardNo: this.formCardNo,
                cardType: this.formCardType === '储蓄卡' ? BankCardType.DEBIT : BankCardType.CREDIT,
                bankName: this.formBankName,
                holderName: this.formHolderName,
                isBound: BankCardStatus.BOUND,
                createTime: '',
                updateTime: new Date().toISOString()
            };
            // 调用API更新银行卡
            await BankCardApi.updateCard(updatedCard);
            promptAction.showToast({ message: '银行卡修改成功' });
            // 返回上一页
            router.back();
        }
        catch (error) {
            console.error('修改银行卡失败:', error);
            promptAction.showToast({ message: '修改失败，请重试' });
        }
        finally {
            this.isSubmitting = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "EditBankCardPage";
    }
}
registerNamedRoute(() => new EditBankCardPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/EditBankCardPage", pageFullPath: "entry/src/main/ets/pages/EditBankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
