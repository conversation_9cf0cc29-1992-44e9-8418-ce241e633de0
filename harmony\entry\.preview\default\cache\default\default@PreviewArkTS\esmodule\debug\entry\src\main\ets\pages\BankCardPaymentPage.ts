if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardPaymentPage_Params {
    product?: string;
    amount?: string;
    selectedBankCard?: string;
    password?: string;
    showBankCardPicker?: boolean;
    bankCardList?: string[];
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class BankCardPaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__product = new ObservedPropertySimplePU('商品购买', this, "product");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__selectedBankCard = new ObservedPropertySimplePU('', this, "selectedBankCard");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__showBankCardPicker = new ObservedPropertySimplePU(false, this, "showBankCardPicker");
        this.bankCardList = [
            '中国银行 ****1234',
            '工商银行 ****5678',
            '建设银行 ****9012'
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardPaymentPage_Params) {
        if (params.product !== undefined) {
            this.product = params.product;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.selectedBankCard !== undefined) {
            this.selectedBankCard = params.selectedBankCard;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.showBankCardPicker !== undefined) {
            this.showBankCardPicker = params.showBankCardPicker;
        }
        if (params.bankCardList !== undefined) {
            this.bankCardList = params.bankCardList;
        }
    }
    updateStateVars(params: BankCardPaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__product.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedBankCard.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__showBankCardPicker.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__product.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__selectedBankCard.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__showBankCardPicker.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __product: ObservedPropertySimplePU<string>;
    get product() {
        return this.__product.get();
    }
    set product(newValue: string) {
        this.__product.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __selectedBankCard: ObservedPropertySimplePU<string>;
    get selectedBankCard() {
        return this.__selectedBankCard.get();
    }
    set selectedBankCard(newValue: string) {
        this.__selectedBankCard.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __showBankCardPicker: ObservedPropertySimplePU<boolean>;
    get showBankCardPicker() {
        return this.__showBankCardPicker.get();
    }
    set showBankCardPicker(newValue: boolean) {
        this.__showBankCardPicker.set(newValue);
    }
    // 模拟银行卡列表
    private bankCardList: string[];
    aboutToAppear() {
        console.log('BankCardPaymentPage 页面加载成功');
    }
    // 处理支付
    private handlePayment() {
        if (!this.product.trim()) {
            promptAction.showToast({
                message: '请输入商品信息',
                duration: 2000
            });
            return;
        }
        if (!this.amount.trim()) {
            promptAction.showToast({
                message: '请输入支付金额',
                duration: 2000
            });
            return;
        }
        if (!this.selectedBankCard) {
            promptAction.showToast({
                message: '请选择银行卡',
                duration: 2000
            });
            return;
        }
        if (!this.password.trim()) {
            promptAction.showToast({
                message: '请输入支付密码',
                duration: 2000
            });
            return;
        }
        // 模拟支付处理
        promptAction.showToast({
            message: '银行卡支付成功！',
            duration: 2000
        });
        // 延迟返回
        setTimeout(() => {
            router.back();
        }, 2000);
    }
    // 取消支付
    private handleCancel() {
        router.back();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(76:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(77:7)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(79:9)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('×');
            Button.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(80:11)", "entry");
            Button.fontSize(24);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡支付');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(88:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(95:11)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(108:9)", "entry");
            // 支付表单
            Column.layoutWeight(1);
            // 支付表单
            Column.padding(20);
            // 支付表单
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 商品信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(110:11)", "entry");
            // 商品信息
            Row.width('100%');
            // 商品信息
            Row.alignItems(VerticalAlign.Center);
            // 商品信息
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商品');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(111:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '商品购买', text: this.product });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(116:13)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.onChange((value: string) => {
                this.product = value;
            });
        }, TextInput);
        // 商品信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付金额
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(133:11)", "entry");
            // 支付金额
            Row.width('100%');
            // 支付金额
            Row.alignItems(VerticalAlign.Center);
            // 支付金额
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('金额');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(134:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付金额' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(139:13)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        // 支付金额
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(157:11)", "entry");
            // 银行卡选择
            Row.width('100%');
            // 银行卡选择
            Row.alignItems(VerticalAlign.Center);
            // 银行卡选择
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(158:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(163:13)", "entry");
            Row.layoutWeight(1);
            Row.height(44);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor('#F8F9FA');
            Row.border({ width: 1, color: '#E5E5E5' });
            Row.borderRadius(8);
            Row.alignItems(VerticalAlign.Center);
            Row.onClick(() => {
                this.showBankCardPicker = true;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.selectedBankCard || '请选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(164:15)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.selectedBankCard ? '#333333' : '#999999');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('▼');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(169:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        Row.pop();
        // 银行卡选择
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(189:11)", "entry");
            // 支付密码
            Row.width('100%');
            // 支付密码
            Row.alignItems(VerticalAlign.Center);
            // 支付密码
            Row.margin({ bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码');
            Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(190:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.width(60);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(195:13)", "entry");
            TextInput.layoutWeight(1);
            TextInput.fontSize(16);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E5E5' });
            TextInput.borderRadius(8);
            TextInput.padding({ left: 12, right: 12 });
            TextInput.height(44);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.password = value;
            });
        }, TextInput);
        // 支付密码
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(213:11)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.End);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(214:13)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F8F9FA');
            Button.border({ width: 1, color: '#E5E5E5' });
            Button.borderRadius(8);
            Button.width(100);
            Button.height(44);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认支付');
            Button.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(226:13)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4A90E2');
            Button.borderRadius(8);
            Button.width(120);
            Button.height(44);
            Button.margin({ left: 20 });
            Button.onClick(() => {
                this.handlePayment();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 支付表单
        Column.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择弹窗
            if (this.showBankCardPicker) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(251:9)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.position({ x: 0, y: 0 });
                        Column.zIndex(1000);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 遮罩层
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(253:11)", "entry");
                        // 遮罩层
                        Column.width('100%');
                        // 遮罩层
                        Column.height('100%');
                        // 遮罩层
                        Column.backgroundColor('#********');
                        // 遮罩层
                        Column.onClick(() => {
                            this.showBankCardPicker = false;
                        });
                    }, Column);
                    // 遮罩层
                    Column.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 弹窗内容
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(269:9)", "entry");
                        // 弹窗内容
                        Column.width(300);
                        // 弹窗内容
                        Column.padding(20);
                        // 弹窗内容
                        Column.borderRadius(16);
                        // 弹窗内容
                        Column.backgroundColor('#FFFFFF');
                        // 弹窗内容
                        Column.shadow({
                            radius: 16,
                            color: '#********',
                            offsetX: 0,
                            offsetY: 8
                        });
                        // 弹窗内容
                        Column.position({ x: '50%', y: '50%' });
                        // 弹窗内容
                        Column.translate({ x: '-50%', y: '-50%' });
                        // 弹窗内容
                        Column.zIndex(1001);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('选择银行卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(270:11)", "entry");
                        Text.fontSize(18);
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor('#333333');
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(card);
                                Text.debugLine("entry/src/main/ets/pages/BankCardPaymentPage.ets(277:13)", "entry");
                                Text.fontSize(16);
                                Text.fontColor('#333333');
                                Text.width('100%');
                                Text.height(44);
                                Text.textAlign(TextAlign.Center);
                                Text.backgroundColor(this.selectedBankCard === card ? '#F0F8FF' : '#FFFFFF');
                                Text.border({
                                    width: { bottom: 1 },
                                    color: '#E5E5E5'
                                });
                                Text.onClick(() => {
                                    this.selectedBankCard = card;
                                    this.showBankCardPicker = false;
                                });
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCardList, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    // 弹窗内容
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardPaymentPage";
    }
}
registerNamedRoute(() => new BankCardPaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardPaymentPage", pageFullPath: "entry/src/main/ets/pages/BankCardPaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
