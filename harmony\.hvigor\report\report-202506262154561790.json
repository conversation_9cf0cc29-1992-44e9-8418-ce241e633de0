{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "1a3e15eb-47df-40e4-a927-f788e1b32abc", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972941889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d579d447-b0ab-4cf5-82dd-bc92470264c7", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21725376139800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cabde94-1781-41f8-ab1e-ff609742673f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21726428953600, "endTime": 21726470727200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "928af6c7-56be-4db2-acde-14ba865be006"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "928af6c7-56be-4db2-acde-14ba865be006", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21726428953600, "endTime": 21726470727200}, "additional": {"logType": "info", "children": [], "durationId": "1cabde94-1781-41f8-ab1e-ff609742673f"}}, {"head": {"id": "172a76fa-4775-4e57-9f34-19ebc5fd8f14", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21726530615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306caff9-d3e3-43a8-a551-969fe81ac9f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21726572623300, "endTime": 21726572709200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "fb4acc05-7982-42a8-9b6e-8ce99b245d12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb4acc05-7982-42a8-9b6e-8ce99b245d12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21726572623300, "endTime": 21726572709200}, "additional": {"logType": "info", "children": [], "durationId": "306caff9-d3e3-43a8-a551-969fe81ac9f2"}}, {"head": {"id": "bd8a2875-3cf7-4d98-84aa-2254e88d7432", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21752730407000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa11fba0-9da6-4e38-9b04-fdb621ee1a00", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21752786875600, "endTime": 21752786921500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "bf9498c6-104d-462f-827d-5226cd1bbe6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf9498c6-104d-462f-827d-5226cd1bbe6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21752786875600, "endTime": 21752786921500}, "additional": {"logType": "info", "children": [], "durationId": "aa11fba0-9da6-4e38-9b04-fdb621ee1a00"}}, {"head": {"id": "cf64f547-3521-41d9-a77a-e9b3aab78094", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21755530280900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e528bb36-14d3-4726-bb3e-958aee44144c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21755531526900, "endTime": 21755531549200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9056971a-8dd2-437d-8032-8648f7b31d8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9056971a-8dd2-437d-8032-8648f7b31d8e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21755531526900, "endTime": 21755531549200}, "additional": {"logType": "info", "children": [], "durationId": "e528bb36-14d3-4726-bb3e-958aee44144c"}}, {"head": {"id": "44350c62-8982-4820-a021-314a453d4aec", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789849184800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee6634a-89f2-4818-bf9e-577dc4a2ad6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789850901200, "endTime": 21789850933600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "121168a5-354f-4091-8c49-192ef32002d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "121168a5-354f-4091-8c49-192ef32002d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789850901200, "endTime": 21789850933600}, "additional": {"logType": "info", "children": [], "durationId": "9ee6634a-89f2-4818-bf9e-577dc4a2ad6c"}}, {"head": {"id": "9cd4544c-0fb7-4f69-ab10-eb78cb188369", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789851069100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12667713-b505-476a-931b-cafdd2414507", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789852735500, "endTime": 21789852766900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "2681042e-500b-4c84-a639-cfeb9a90e7e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2681042e-500b-4c84-a639-cfeb9a90e7e6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21789852735500, "endTime": 21789852766900}, "additional": {"logType": "info", "children": [], "durationId": "12667713-b505-476a-931b-cafdd2414507"}}, {"head": {"id": "5d63129e-4415-4390-a46a-912588e946ed", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791758863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cedbe65-62c0-42cf-81d9-c23b3e4b3451", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791773648000, "endTime": 21791773680700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "58a815ee-0f1b-445d-b62d-2d45b1e2afd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58a815ee-0f1b-445d-b62d-2d45b1e2afd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791773648000, "endTime": 21791773680700}, "additional": {"logType": "info", "children": [], "durationId": "1cedbe65-62c0-42cf-81d9-c23b3e4b3451"}}, {"head": {"id": "90185d01-68d3-4837-a1b7-08fecf467743", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791773834400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c3ac168-99c7-431d-94d6-f708925c3fc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791775264300, "endTime": 21791775298700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "d98f31ef-df89-4b36-8470-c65749dae93b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d98f31ef-df89-4b36-8470-c65749dae93b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791775264300, "endTime": 21791775298700}, "additional": {"logType": "info", "children": [], "durationId": "6c3ac168-99c7-431d-94d6-f708925c3fc4"}}, {"head": {"id": "46a90e33-897b-4926-bf90-ffbef68c4c48", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791879991700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9b0c95-85d4-4126-a1b9-9edef3eaff68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791881501000, "endTime": 21791881530600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "741adc59-c1ab-48ba-85d1-8371024d816c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "741adc59-c1ab-48ba-85d1-8371024d816c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21791881501000, "endTime": 21791881530600}, "additional": {"logType": "info", "children": [], "durationId": "bc9b0c95-85d4-4126-a1b9-9edef3eaff68"}}, {"head": {"id": "3c364aa3-ec20-4083-8d05-ab74c7cdc7e8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792159129800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dacf2fc-bb90-4b3a-81e7-2f0379a7d8c6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792160591500, "endTime": 21792160618000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a54a9f8b-3cb4-4353-8432-ddffbcb56b72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a54a9f8b-3cb4-4353-8432-ddffbcb56b72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792160591500, "endTime": 21792160618000}, "additional": {"logType": "info", "children": [], "durationId": "9dacf2fc-bb90-4b3a-81e7-2f0379a7d8c6"}}, {"head": {"id": "f40c6084-7e15-4901-974c-4b460c589d85", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792620488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d8fc590-86ef-4d85-b7f2-007df8635c60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792625588400, "endTime": 21792625613400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "f6c60fa4-c988-410c-a3c8-adebb867fc82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6c60fa4-c988-410c-a3c8-adebb867fc82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792625588400, "endTime": 21792625613400}, "additional": {"logType": "info", "children": [], "durationId": "2d8fc590-86ef-4d85-b7f2-007df8635c60"}}, {"head": {"id": "7311fa36-fc6c-48e3-987b-5dbeb8b72341", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792625726800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c09e99c-fefb-4632-902d-1bdf9b91d205", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792785642900, "endTime": 21792785694400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "da210456-5f1b-460a-9e75-02b67907aee7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da210456-5f1b-460a-9e75-02b67907aee7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792785642900, "endTime": 21792785694400}, "additional": {"logType": "info", "children": [], "durationId": "4c09e99c-fefb-4632-902d-1bdf9b91d205"}}, {"head": {"id": "0114cab7-64af-4ae6-bf45-8e810ae74c5e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792785915000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eecbf0f-2d71-41b6-99e4-d8f3da525690", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792789208500, "endTime": 21792789265600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "ff9568b1-1329-4de5-b9d9-893010f396e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff9568b1-1329-4de5-b9d9-893010f396e0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21792789208500, "endTime": 21792789265600}, "additional": {"logType": "info", "children": [], "durationId": "4eecbf0f-2d71-41b6-99e4-d8f3da525690"}}, {"head": {"id": "5c29238c-0a55-452e-b48b-3dce2aad25ce", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21798463387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a37395b-e55b-48fa-991e-1819477190f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21798465323600, "endTime": 21798465363100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "08e65ad0-0760-4701-ac3d-38d94cf7ce3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08e65ad0-0760-4701-ac3d-38d94cf7ce3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21798465323600, "endTime": 21798465363100}, "additional": {"logType": "info", "children": [], "durationId": "9a37395b-e55b-48fa-991e-1819477190f8"}}, {"head": {"id": "97a734b7-217c-4e8e-afac-a158374a0856", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21870818071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e9ec13-17c7-4b1a-a5ec-7e00c9625aa2", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21871203697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd80815d-e347-4c41-911c-f6da582c468d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21883983435200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885056591900, "endTime": 21892808893500}, "additional": {"children": ["643ba269-8862-49fa-b6e8-58f31cf87397", "c02ec63a-c431-492b-be43-61a912152372", "6836a5dd-dd1e-44ba-bc7d-38eb5e6dff87", "081f70e9-ea90-4e43-8319-c4f458638ae3", "61afd11f-a230-40dc-b0fd-7e713f8ed1d7", "0c2be503-8735-4139-81d1-5730e9fa6c22", "171857aa-9c2c-4708-b101-00c03b1d6093"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "643ba269-8862-49fa-b6e8-58f31cf87397", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885056607500, "endTime": 21886810185300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "ab1da0c5-f655-4476-b9ed-3fabd3ff9859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c02ec63a-c431-492b-be43-61a912152372", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21886810241400, "endTime": 21892807572900}, "additional": {"children": ["c6f73054-44a7-4d85-9fe8-21f78476b484", "0bc5f447-965c-4184-914c-02514b7252f3", "b2770fb6-5eda-4da7-9d49-265532092b65", "af2a9fc5-9a78-4e94-a8d8-9c7a0a63b866", "e3b5b6e3-0d76-4f2d-be88-94f5704e1f64", "73b96b82-9ee1-41e9-b5b9-97a99a87f9ca", "655a3f02-4254-4b62-a02e-3179b9ab5ff9", "46ae7384-3fc1-4706-99bf-6946edfe3224", "4b30541f-766e-47bf-b88c-b0cd3da24762"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "9b898b29-7d05-440d-9911-01edcf10f924"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6836a5dd-dd1e-44ba-bc7d-38eb5e6dff87", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892807604100, "endTime": 21892808884000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "3cd57be8-02b6-4a20-aa5c-e283edd2da3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "081f70e9-ea90-4e43-8319-c4f458638ae3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892808887900, "endTime": 21892808890200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "e193b0a4-de71-47c2-a52b-a6289046e5b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61afd11f-a230-40dc-b0fd-7e713f8ed1d7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885793040000, "endTime": 21885793081400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "4a5de2e9-1fbb-41fd-8078-3dfcf2dcaaa5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a5de2e9-1fbb-41fd-8078-3dfcf2dcaaa5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885793040000, "endTime": 21885793081400}, "additional": {"logType": "info", "children": [], "durationId": "61afd11f-a230-40dc-b0fd-7e713f8ed1d7", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "0c2be503-8735-4139-81d1-5730e9fa6c22", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885897380800, "endTime": 21885897401600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "ed73921d-ea8e-432b-943a-81e4e8f30a45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed73921d-ea8e-432b-943a-81e4e8f30a45", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885897380800, "endTime": 21885897401600}, "additional": {"logType": "info", "children": [], "durationId": "0c2be503-8735-4139-81d1-5730e9fa6c22", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "41510723-0054-42c8-9c29-cbdc3821d398", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21886026901700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbeb7281-24a8-4490-b402-70e749b80cfa", "name": "Cache service initialization finished in 741 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21886793798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab1da0c5-f655-4476-b9ed-3fabd3ff9859", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885056607500, "endTime": 21886810185300}, "additional": {"logType": "info", "children": [], "durationId": "643ba269-8862-49fa-b6e8-58f31cf87397", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "c6f73054-44a7-4d85-9fe8-21f78476b484", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887107739500, "endTime": 21887107753400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "9470225b-dc26-4d1b-a215-3300bfc8de86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bc5f447-965c-4184-914c-02514b7252f3", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887107783200, "endTime": 21887117630000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "e8b5daea-59b8-466a-a027-20e5b98a513a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2770fb6-5eda-4da7-9d49-265532092b65", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887117995900, "endTime": 21891834856600}, "additional": {"children": ["53c02267-77f6-4137-9216-e963b5fe19b8", "bbd47e3f-1b68-42a8-b634-6f26fcf8da84", "922924b8-6af5-42d6-9b11-ba5c66063655"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "f9631633-5f9f-49c7-abd8-2c1f0bfa0341"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af2a9fc5-9a78-4e94-a8d8-9c7a0a63b866", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891834895800, "endTime": 21892074602100}, "additional": {"children": ["6243e78c-2450-4899-9744-84afc5489a07"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "ea0d9ae8-b612-4ebf-9ecb-03efa25e19f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3b5b6e3-0d76-4f2d-be88-94f5704e1f64", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892074613500, "endTime": 21892714492800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "9ed35ea4-9ffd-43cd-9323-11abba426eac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73b96b82-9ee1-41e9-b5b9-97a99a87f9ca", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892715631800, "endTime": 21892772426300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "dd50fc19-3081-4ceb-84b1-b7085bb39f92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "655a3f02-4254-4b62-a02e-3179b9ab5ff9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892772446100, "endTime": 21892807366400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "5f05b8fa-7260-4591-8e7d-6de1ae585789"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46ae7384-3fc1-4706-99bf-6946edfe3224", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892807391500, "endTime": 21892807542200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "0d418b1b-1791-4f0b-b921-b81e2b7e0cb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9470225b-dc26-4d1b-a215-3300bfc8de86", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887107739500, "endTime": 21887107753400}, "additional": {"logType": "info", "children": [], "durationId": "c6f73054-44a7-4d85-9fe8-21f78476b484", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "e8b5daea-59b8-466a-a027-20e5b98a513a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887107783200, "endTime": 21887117630000}, "additional": {"logType": "info", "children": [], "durationId": "0bc5f447-965c-4184-914c-02514b7252f3", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "53c02267-77f6-4137-9216-e963b5fe19b8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887148120000, "endTime": 21887148145400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2770fb6-5eda-4da7-9d49-265532092b65", "logId": "593bbcb8-3282-4df5-8226-fb522263936a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "593bbcb8-3282-4df5-8226-fb522263936a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887148120000, "endTime": 21887148145400}, "additional": {"logType": "info", "children": [], "durationId": "53c02267-77f6-4137-9216-e963b5fe19b8", "parent": "f9631633-5f9f-49c7-abd8-2c1f0bfa0341"}}, {"head": {"id": "bbd47e3f-1b68-42a8-b634-6f26fcf8da84", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887412077300, "endTime": 21891832823400}, "additional": {"children": ["740993dc-f3c0-4061-a085-3ee815512600", "04fda2f7-99fa-4f73-8ba6-3d212f671bd2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2770fb6-5eda-4da7-9d49-265532092b65", "logId": "e1f49267-ffca-45c0-b84e-48b25fd6b477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "740993dc-f3c0-4061-a085-3ee815512600", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887412079000, "endTime": 21889671412300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbd47e3f-1b68-42a8-b634-6f26fcf8da84", "logId": "efb57b4e-6d69-4cd4-8d4a-7027dc5cfce9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04fda2f7-99fa-4f73-8ba6-3d212f671bd2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21889671486600, "endTime": 21891832787400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbd47e3f-1b68-42a8-b634-6f26fcf8da84", "logId": "ef14a982-29e8-4784-94e9-591bb56f3ba4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6563257-1b91-4a0b-bd3c-ee968da3aef3", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887412084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e9836f-4e14-4589-b35d-3b838dc64c70", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21889670794800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb57b4e-6d69-4cd4-8d4a-7027dc5cfce9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887412079000, "endTime": 21889671412300}, "additional": {"logType": "info", "children": [], "durationId": "740993dc-f3c0-4061-a085-3ee815512600", "parent": "e1f49267-ffca-45c0-b84e-48b25fd6b477"}}, {"head": {"id": "b7099e45-3f51-4a69-931c-76974fc5afef", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21889671586300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80fc4505-8120-4a21-a83b-26d30d8914a4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890556853100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959b7756-e38e-4c6f-aa90-02b1c6165877", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890557185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad810be-2fc1-4720-b08f-9427ec9caa02", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890599150900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0539784-28f5-4e29-99bb-d5d430bc4cf9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890600104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2072fac8-ee8b-45e6-992f-1f134a95dd85", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890711742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115cd207-cf7f-473d-8750-72a812bee2cf", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21890911597500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01092b0f-85b6-449c-86c1-9286c28141e8", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891224537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c8b0b60-ed56-4fce-918e-84f79247652d", "name": "Sdk init in 612 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891529900100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25685965-148b-4cd7-8517-7ca620ef6372", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891555974600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 55}, "markType": "other"}}, {"head": {"id": "5179373d-8992-4b49-a699-1bbef8ea5780", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891561680200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 55}, "markType": "other"}}, {"head": {"id": "d18d897f-bea0-44ac-853d-e58460088bd7", "name": "Project task initialization takes 166 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891775103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bba886f9-1b63-49e0-9d45-0fc08b6d91bf", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891832236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b62b904-53af-425d-af0e-e3a18b28480b", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891832520000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbbb052a-cb14-4d14-8694-7ea9356cd263", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891832660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef14a982-29e8-4784-94e9-591bb56f3ba4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21889671486600, "endTime": 21891832787400}, "additional": {"logType": "info", "children": [], "durationId": "04fda2f7-99fa-4f73-8ba6-3d212f671bd2", "parent": "e1f49267-ffca-45c0-b84e-48b25fd6b477"}}, {"head": {"id": "e1f49267-ffca-45c0-b84e-48b25fd6b477", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887412077300, "endTime": 21891832823400}, "additional": {"logType": "info", "children": ["efb57b4e-6d69-4cd4-8d4a-7027dc5cfce9", "ef14a982-29e8-4784-94e9-591bb56f3ba4"], "durationId": "bbd47e3f-1b68-42a8-b634-6f26fcf8da84", "parent": "f9631633-5f9f-49c7-abd8-2c1f0bfa0341"}}, {"head": {"id": "922924b8-6af5-42d6-9b11-ba5c66063655", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891834797700, "endTime": 21891834828000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2770fb6-5eda-4da7-9d49-265532092b65", "logId": "9e6c46f9-2c17-4eb3-abdb-85d069070ab4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e6c46f9-2c17-4eb3-abdb-85d069070ab4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891834797700, "endTime": 21891834828000}, "additional": {"logType": "info", "children": [], "durationId": "922924b8-6af5-42d6-9b11-ba5c66063655", "parent": "f9631633-5f9f-49c7-abd8-2c1f0bfa0341"}}, {"head": {"id": "f9631633-5f9f-49c7-abd8-2c1f0bfa0341", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21887117995900, "endTime": 21891834856600}, "additional": {"logType": "info", "children": ["593bbcb8-3282-4df5-8226-fb522263936a", "e1f49267-ffca-45c0-b84e-48b25fd6b477", "9e6c46f9-2c17-4eb3-abdb-85d069070ab4"], "durationId": "b2770fb6-5eda-4da7-9d49-265532092b65", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "6243e78c-2450-4899-9744-84afc5489a07", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891836384200, "endTime": 21892074557700}, "additional": {"children": ["d001043a-c503-48f2-8f7e-b65e0c08323a", "1128ea4a-fd3b-443a-abbb-7c8b57f28d80", "8c851c9d-2da3-4e74-a99b-cfe6283c678a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af2a9fc5-9a78-4e94-a8d8-9c7a0a63b866", "logId": "8f8a74bd-7083-4fdc-b491-3f1e6942f123"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d001043a-c503-48f2-8f7e-b65e0c08323a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891844268900, "endTime": 21891844299000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6243e78c-2450-4899-9744-84afc5489a07", "logId": "f2f80560-7556-4ad4-ab4a-8fc6ec2572dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2f80560-7556-4ad4-ab4a-8fc6ec2572dc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891844268900, "endTime": 21891844299000}, "additional": {"logType": "info", "children": [], "durationId": "d001043a-c503-48f2-8f7e-b65e0c08323a", "parent": "8f8a74bd-7083-4fdc-b491-3f1e6942f123"}}, {"head": {"id": "1128ea4a-fd3b-443a-abbb-7c8b57f28d80", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891853304700, "endTime": 21892073041200}, "additional": {"children": ["48ff6bbc-eda8-45a6-8292-6051ab1788a2", "3282e46b-5bfb-4398-90d2-403d504df168"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6243e78c-2450-4899-9744-84afc5489a07", "logId": "0c8ae4c9-c961-40d4-96b7-0770367727d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48ff6bbc-eda8-45a6-8292-6051ab1788a2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891853310900, "endTime": 21891961543400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1128ea4a-fd3b-443a-abbb-7c8b57f28d80", "logId": "8d2d3f3f-a345-4852-9d82-37a36b3e4d43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3282e46b-5bfb-4398-90d2-403d504df168", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891961607400, "endTime": 21892073025600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1128ea4a-fd3b-443a-abbb-7c8b57f28d80", "logId": "4dfac1b9-005d-449e-875f-7a704eec5b97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d514799d-4e7f-42ae-be62-fda47a456c17", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891853326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7db1c3e5-03e6-44a9-953c-564461fe9ceb", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891961044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2d3f3f-a345-4852-9d82-37a36b3e4d43", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891853310900, "endTime": 21891961543400}, "additional": {"logType": "info", "children": [], "durationId": "48ff6bbc-eda8-45a6-8292-6051ab1788a2", "parent": "0c8ae4c9-c961-40d4-96b7-0770367727d1"}}, {"head": {"id": "c9b9ce64-f3bc-44f2-b5b0-22fbec999366", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891961661000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d95faf4-2155-4780-85be-d58a66cd24e1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000062900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de64708f-7d62-4dd0-9d8d-0e665475144a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000177800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "878d7c10-c336-4894-869b-38131f5f9778", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000353600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae0fe22-550a-4a5b-8374-39668844ce36", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000474300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55112dc4-4f70-4515-b9b1-c445e271875b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c29fae1-1746-495e-8e20-83b69af<PERSON><PERSON>", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54048593-126e-4ebe-86ef-a9707496912c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892000632300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76643aa7-9c94-41ca-900a-4bff89594289", "name": "Module entry task initialization takes 42 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892061792700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ee571d-d317-4cb8-8e08-46ee80b0d88e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892072858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0414f2-1e51-48a1-97d1-69f8c8b34b44", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892072939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c37fc97-373e-461a-9274-2e201776dce9", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892072982900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dfac1b9-005d-449e-875f-7a704eec5b97", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891961607400, "endTime": 21892073025600}, "additional": {"logType": "info", "children": [], "durationId": "3282e46b-5bfb-4398-90d2-403d504df168", "parent": "0c8ae4c9-c961-40d4-96b7-0770367727d1"}}, {"head": {"id": "0c8ae4c9-c961-40d4-96b7-0770367727d1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891853304700, "endTime": 21892073041200}, "additional": {"logType": "info", "children": ["8d2d3f3f-a345-4852-9d82-37a36b3e4d43", "4dfac1b9-005d-449e-875f-7a704eec5b97"], "durationId": "1128ea4a-fd3b-443a-abbb-7c8b57f28d80", "parent": "8f8a74bd-7083-4fdc-b491-3f1e6942f123"}}, {"head": {"id": "8c851c9d-2da3-4e74-a99b-cfe6283c678a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892074534200, "endTime": 21892074546000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6243e78c-2450-4899-9744-84afc5489a07", "logId": "e1ee03af-524d-4a5c-85fc-1dea96888b44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1ee03af-524d-4a5c-85fc-1dea96888b44", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892074534200, "endTime": 21892074546000}, "additional": {"logType": "info", "children": [], "durationId": "8c851c9d-2da3-4e74-a99b-cfe6283c678a", "parent": "8f8a74bd-7083-4fdc-b491-3f1e6942f123"}}, {"head": {"id": "8f8a74bd-7083-4fdc-b491-3f1e6942f123", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891836384200, "endTime": 21892074557700}, "additional": {"logType": "info", "children": ["f2f80560-7556-4ad4-ab4a-8fc6ec2572dc", "0c8ae4c9-c961-40d4-96b7-0770367727d1", "e1ee03af-524d-4a5c-85fc-1dea96888b44"], "durationId": "6243e78c-2450-4899-9744-84afc5489a07", "parent": "ea0d9ae8-b612-4ebf-9ecb-03efa25e19f5"}}, {"head": {"id": "ea0d9ae8-b612-4ebf-9ecb-03efa25e19f5", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21891834895800, "endTime": 21892074602100}, "additional": {"logType": "info", "children": ["8f8a74bd-7083-4fdc-b491-3f1e6942f123"], "durationId": "af2a9fc5-9a78-4e94-a8d8-9c7a0a63b866", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "2e979bfd-3fc6-4d0f-9786-4f587e705acf", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892701538900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8041773b-f702-4c2b-9843-be0da7f90312", "name": "hvigorfile, resolve hvigorfile dependencies in 639 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892713602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed35ea4-9ffd-43cd-9323-11abba426eac", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892074613500, "endTime": 21892714492800}, "additional": {"logType": "info", "children": [], "durationId": "e3b5b6e3-0d76-4f2d-be88-94f5704e1f64", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "4b30541f-766e-47bf-b88c-b0cd3da24762", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892715433700, "endTime": 21892715619300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c02ec63a-c431-492b-be43-61a912152372", "logId": "38784202-80ef-4b0a-a053-8cc7bee4360a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "047329fb-c1f0-4873-84fa-51a7a486711f", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892715458900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38784202-80ef-4b0a-a053-8cc7bee4360a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892715433700, "endTime": 21892715619300}, "additional": {"logType": "info", "children": [], "durationId": "4b30541f-766e-47bf-b88c-b0cd3da24762", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "f0857f67-d5ac-4710-abe9-4aa0ceb61fda", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892717128000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d291d9-93c6-4b30-82c9-752d996407c0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892771512200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd50fc19-3081-4ceb-84b1-b7085bb39f92", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892715631800, "endTime": 21892772426300}, "additional": {"logType": "info", "children": [], "durationId": "73b96b82-9ee1-41e9-b5b9-97a99a87f9ca", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "4a366a2b-afb9-4aec-97b9-346d01f5663a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892772461500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b029e271-6342-484b-880b-4780dfc36bca", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892791361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deffacf0-4db0-4727-bbfa-b5f0bd5ca18d", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892791474500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45de8f3c-9141-4206-bcda-cc72810154c6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892791728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe4366a6-230f-42ae-97a7-68855376c5a8", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892793960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1d2268-7249-4eda-930e-87b6f857630e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892794035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f05b8fa-7260-4591-8e7d-6de1ae585789", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892772446100, "endTime": 21892807366400}, "additional": {"logType": "info", "children": [], "durationId": "655a3f02-4254-4b62-a02e-3179b9ab5ff9", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "7e0cddf8-9fb9-46e1-9285-df917d84bdc9", "name": "Configuration phase cost:5 s 700 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892807421200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d418b1b-1791-4f0b-b921-b81e2b7e0cb1", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892807391500, "endTime": 21892807542200}, "additional": {"logType": "info", "children": [], "durationId": "46ae7384-3fc1-4706-99bf-6946edfe3224", "parent": "9b898b29-7d05-440d-9911-01edcf10f924"}}, {"head": {"id": "9b898b29-7d05-440d-9911-01edcf10f924", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21886810241400, "endTime": 21892807572900}, "additional": {"logType": "info", "children": ["9470225b-dc26-4d1b-a215-3300bfc8de86", "e8b5daea-59b8-466a-a027-20e5b98a513a", "f9631633-5f9f-49c7-abd8-2c1f0bfa0341", "ea0d9ae8-b612-4ebf-9ecb-03efa25e19f5", "9ed35ea4-9ffd-43cd-9323-11abba426eac", "dd50fc19-3081-4ceb-84b1-b7085bb39f92", "5f05b8fa-7260-4591-8e7d-6de1ae585789", "0d418b1b-1791-4f0b-b921-b81e2b7e0cb1", "38784202-80ef-4b0a-a053-8cc7bee4360a"], "durationId": "c02ec63a-c431-492b-be43-61a912152372", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "171857aa-9c2c-4708-b101-00c03b1d6093", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892808856500, "endTime": 21892808872900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58", "logId": "c1b7e80c-9064-414d-b5b1-ce77af4f5cb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1b7e80c-9064-414d-b5b1-ce77af4f5cb2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892808856500, "endTime": 21892808872900}, "additional": {"logType": "info", "children": [], "durationId": "171857aa-9c2c-4708-b101-00c03b1d6093", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "3cd57be8-02b6-4a20-aa5c-e283edd2da3c", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892807604100, "endTime": 21892808884000}, "additional": {"logType": "info", "children": [], "durationId": "6836a5dd-dd1e-44ba-bc7d-38eb5e6dff87", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "e193b0a4-de71-47c2-a52b-a6289046e5b7", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892808887900, "endTime": 21892808890200}, "additional": {"logType": "info", "children": [], "durationId": "081f70e9-ea90-4e43-8319-c4f458638ae3", "parent": "83a2311c-6d4f-4404-8cb5-e75de76975f9"}}, {"head": {"id": "83a2311c-6d4f-4404-8cb5-e75de76975f9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21885056591900, "endTime": 21892808893500}, "additional": {"logType": "info", "children": ["ab1da0c5-f655-4476-b9ed-3fabd3ff9859", "9b898b29-7d05-440d-9911-01edcf10f924", "3cd57be8-02b6-4a20-aa5c-e283edd2da3c", "e193b0a4-de71-47c2-a52b-a6289046e5b7", "4a5de2e9-1fbb-41fd-8078-3dfcf2dcaaa5", "ed73921d-ea8e-432b-943a-81e4e8f30a45", "c1b7e80c-9064-414d-b5b1-ce77af4f5cb2"], "durationId": "3c0eb4fd-26ae-45be-a568-c1b86eb99d58"}}, {"head": {"id": "b73eb11c-f427-4db2-b629-7e5cc56b8d32", "name": "Configuration task cost before running: 7 s 854 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892809020900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7865a68f-44ef-47c9-a518-0e304d7cac48", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892815215800, "endTime": 21892848794600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1cac733d-513d-4d18-89a1-481b1919de5a", "logId": "c95a1b85-efe2-40c7-926d-e37af32cd86f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cac733d-513d-4d18-89a1-481b1919de5a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892810951600}, "additional": {"logType": "detail", "children": [], "durationId": "7865a68f-44ef-47c9-a518-0e304d7cac48"}}, {"head": {"id": "f24d8389-3774-4895-bb1c-8c1ac5e2c35a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892811518800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872f180c-c6f6-4cc9-9596-1196e568462a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892811620500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b1ffde-0792-4e4b-ad17-0f5f1547fd31", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892815229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f4e046b-7f43-4b4d-994e-1d5781354de9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892844618300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb5ed24a-d1fb-4a5b-a815-06818cf558e2", "name": "entry : default@PreBuild cost memory 0.2599639892578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892844767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c95a1b85-efe2-40c7-926d-e37af32cd86f", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892815215800, "endTime": 21892848794600}, "additional": {"logType": "info", "children": [], "durationId": "7865a68f-44ef-47c9-a518-0e304d7cac48"}}, {"head": {"id": "46e7bcbf-6313-41f7-8ed8-1b9807df094a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892854215600, "endTime": 21892872805600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "233cc256-fcd7-404b-b579-170c256bb4d9", "logId": "7d320982-d61d-4733-99d2-b79e57336f4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "233cc256-fcd7-404b-b579-170c256bb4d9", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892852883200}, "additional": {"logType": "detail", "children": [], "durationId": "46e7bcbf-6313-41f7-8ed8-1b9807df094a"}}, {"head": {"id": "58d26add-83db-4868-94fb-ba5e939f6bac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892853420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fc01286-dcf7-4cce-a7b6-d6e28cdeaca1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892853517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1086af85-5dd8-4ef9-8e1a-1b7d50d99697", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892854225000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b650b4-d097-4609-8221-396ef92c4745", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892872126300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dda01a2-17af-4626-8ea8-2fb3f1c87eee", "name": "entry : default@MergeProfile cost memory 0.105865478515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892872695700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d320982-d61d-4733-99d2-b79e57336f4a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892854215600, "endTime": 21892872805600}, "additional": {"logType": "info", "children": [], "durationId": "46e7bcbf-6313-41f7-8ed8-1b9807df094a"}}, {"head": {"id": "a5dae7cf-241f-4b9d-ba03-b3337c454d63", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892875972600, "endTime": 21892953404900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "57244a2f-0c06-4fee-8153-4ddad9fe9207", "logId": "05e6340d-700c-4300-beb5-18449eef1e3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57244a2f-0c06-4fee-8153-4ddad9fe9207", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892874558400}, "additional": {"logType": "detail", "children": [], "durationId": "a5dae7cf-241f-4b9d-ba03-b3337c454d63"}}, {"head": {"id": "cba3c14c-216d-415e-800c-5bb766315a3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892875099500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9f5cbe-38ec-4fea-aa8c-3c62ad9e0450", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892875197000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "932191af-bab3-45b4-afe5-403e22f93d3e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892876002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ee45d2-a1ff-4670-a379-fafad93147ac", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 25 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892900414200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8536c6d5-68c1-4c07-9cf3-65742bad509a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 53 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892953069200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757333f8-70e0-47f7-ac42-65cf9a89c434", "name": "entry : default@CreateBuildProfile cost memory 0.094390869140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892953293000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e6340d-700c-4300-beb5-18449eef1e3d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892875972600, "endTime": 21892953404900}, "additional": {"logType": "info", "children": [], "durationId": "a5dae7cf-241f-4b9d-ba03-b3337c454d63"}}, {"head": {"id": "68a303d8-f7da-47b3-b35e-cc1fc46f760b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892956908700, "endTime": 21892957301100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c54cf0e2-3b85-47e5-9bda-16e5362ef7e8", "logId": "27553e17-7616-4516-b11a-3068fa0fabfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c54cf0e2-3b85-47e5-9bda-16e5362ef7e8", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892955488100}, "additional": {"logType": "detail", "children": [], "durationId": "68a303d8-f7da-47b3-b35e-cc1fc46f760b"}}, {"head": {"id": "e898bf0c-37a6-4f3d-839f-9e6a42a04781", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892956016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de458d9-3248-42e9-be5c-25f981430b5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892956096600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e447eb-ef27-4110-af80-05e72883dba2", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892956917900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b70902-b382-4ef7-be03-b663430fe0a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892957018100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a8d309-e5eb-4a91-bbcd-ddfb8dd6fa69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892957070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59207641-f25a-45d4-b404-48a45b2a4122", "name": "entry : default@PreCheckSyscap cost memory 0.03679656982421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892957140600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabfdc62-2b08-454c-9aba-75f18556392c", "name": "runTaskFromQueue task cost before running: 8 s 3 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892957249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27553e17-7616-4516-b11a-3068fa0fabfb", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892956908700, "endTime": 21892957301100, "totalTime": 308700}, "additional": {"logType": "info", "children": [], "durationId": "68a303d8-f7da-47b3-b35e-cc1fc46f760b"}}, {"head": {"id": "0b1aa5a8-ac7f-49af-8f60-a7492be70b91", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892970778000, "endTime": 21892971992900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b923a84a-6349-46e3-80d8-88146e691a53", "logId": "824d8e9d-cf10-42db-a9d6-d054e5a8d995"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b923a84a-6349-46e3-80d8-88146e691a53", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892958908000}, "additional": {"logType": "detail", "children": [], "durationId": "0b1aa5a8-ac7f-49af-8f60-a7492be70b91"}}, {"head": {"id": "bf137c17-a5ee-404d-8302-81c30bba86cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892959350000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bac4359-6d5c-4027-8e65-ac99caeb8ae1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892959421400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7054c1-2efb-4281-a386-ff98fb031f60", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892970790000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "495af1a4-f667-4905-bef9-d2dc6fe6fc35", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892971036800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c070b98-6d46-4b6e-afcb-7bd9100acf68", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892971761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dcd5f94-acec-4f21-ba00-f427f030c013", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06329345703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892971858700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824d8e9d-cf10-42db-a9d6-d054e5a8d995", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892970778000, "endTime": 21892971992900}, "additional": {"logType": "info", "children": [], "durationId": "0b1aa5a8-ac7f-49af-8f60-a7492be70b91"}}, {"head": {"id": "675a4b8b-37ab-498f-ab9c-4f4d37868ccc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893005837900, "endTime": 21893007148900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2262db42-c398-4d78-942e-97916c030516", "logId": "aa508c72-a5b4-4ba7-b830-9b55272ab38b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2262db42-c398-4d78-942e-97916c030516", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892973675300}, "additional": {"logType": "detail", "children": [], "durationId": "675a4b8b-37ab-498f-ab9c-4f4d37868ccc"}}, {"head": {"id": "d95eca72-2029-47d2-8fa0-64ffe3be5281", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892974318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03e2827-b668-4ac4-9b4f-b466bca68b8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21892974420300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "680901e8-b06a-463b-b013-fe9492e7f926", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893005853800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38bb258-742d-41d3-b571-56dc1a961913", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893006992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f14815be-260f-4b8a-bf96-50db44cf6b24", "name": "entry : default@ProcessProfile cost memory 0.0536346435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893007087200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa508c72-a5b4-4ba7-b830-9b55272ab38b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893005837900, "endTime": 21893007148900}, "additional": {"logType": "info", "children": [], "durationId": "675a4b8b-37ab-498f-ab9c-4f4d37868ccc"}}, {"head": {"id": "25effbb9-d9a2-436a-bde1-2fbd24a9699a", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893011113300, "endTime": 21893017037200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2755b1fc-1d9a-4f03-af12-05c902533ed9", "logId": "3faae838-cd59-408f-8143-051350690ea0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2755b1fc-1d9a-4f03-af12-05c902533ed9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893008814100}, "additional": {"logType": "detail", "children": [], "durationId": "25effbb9-d9a2-436a-bde1-2fbd24a9699a"}}, {"head": {"id": "8caa2e18-92f4-4ab4-a16d-60b58990befb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893009317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3368318-1e74-455a-9a06-49f38e381d66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893009406900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83ec667-5d28-4828-9ddd-1ae4325dc429", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893011123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9309ae6a-5827-44c6-9b6e-9052bde7e733", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893016861700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253965dd-71e9-4e8d-8f53-2d6ad21fdceb", "name": "entry : default@ProcessRouterMap cost memory 0.18450164794921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893016970700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3faae838-cd59-408f-8143-051350690ea0", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893011113300, "endTime": 21893017037200}, "additional": {"logType": "info", "children": [], "durationId": "25effbb9-d9a2-436a-bde1-2fbd24a9699a"}}, {"head": {"id": "6e85a594-af34-4f4f-a972-9539565175ee", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893026216900, "endTime": 21893051862400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "34b63cf3-8a2a-4d66-ad5e-f678f0484d88", "logId": "3755ab59-8efb-4af3-8439-8b2f09500d49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34b63cf3-8a2a-4d66-ad5e-f678f0484d88", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893020438500}, "additional": {"logType": "detail", "children": [], "durationId": "6e85a594-af34-4f4f-a972-9539565175ee"}}, {"head": {"id": "e82e8422-fefe-4eb1-9f76-2f979f5fe141", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893021153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed2c273b-f245-480f-807c-1b587289196d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893021285600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cf68316-249a-4208-a2f5-994a8e3ee00a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893022739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d823951d-d5e1-4e8c-90c7-b10b0721aada", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893027976400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d71114a-7d59-4f02-ab4d-840bd75dadd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893028179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f02dac-c51b-4aa3-a1cc-67dfbcb5cedf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893028275400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "531e201f-cf79-47f3-ac54-0f710a430c3a", "name": "entry : default@PreviewProcessResource cost memory 0.067626953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893028417000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc8c0d2-c23e-4990-a21e-ef7efdff14f0", "name": "runTaskFromQueue task cost before running: 8 s 92 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893046266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3755ab59-8efb-4af3-8439-8b2f09500d49", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893026216900, "endTime": 21893051862400, "totalTime": 18704300}, "additional": {"logType": "info", "children": [], "durationId": "6e85a594-af34-4f4f-a972-9539565175ee"}}, {"head": {"id": "453569c5-3f68-4ab4-916c-54e05f0e37fe", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893076433100, "endTime": 21893109393000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fb2a6ce3-6704-4bdf-ac1f-278769e1f193", "logId": "0ee7418f-dbe4-4e21-bea7-c1331546df19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb2a6ce3-6704-4bdf-ac1f-278769e1f193", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893055684600}, "additional": {"logType": "detail", "children": [], "durationId": "453569c5-3f68-4ab4-916c-54e05f0e37fe"}}, {"head": {"id": "cd5aa8fa-d88a-44b5-ba9c-afacdf9e5027", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893056256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558503e1-57dc-4e0c-b0d1-5baeefc8ef91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893056361800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434a9c22-4b54-4469-90f5-c0f2397424f4", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893076451800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ce6b66e-c7dd-4075-a885-f385090d7149", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893109162900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "192cd2c6-27ff-4703-bb82-036ef5073339", "name": "entry : default@GenerateLoaderJson cost memory 0.812957763671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893109317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee7418f-dbe4-4e21-bea7-c1331546df19", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893076433100, "endTime": 21893109393000}, "additional": {"logType": "info", "children": [], "durationId": "453569c5-3f68-4ab4-916c-54e05f0e37fe"}}, {"head": {"id": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893123378400, "endTime": 21896763407400}, "additional": {"children": ["42814a5c-60fe-4659-823c-a77070478feb", "30648f5f-1598-47b9-9c65-9d7729f11228", "10c934a5-0aed-4f76-83ad-9eb4929d41c6", "7d5988bd-2530-449a-bbeb-0a0335924eaf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "9c1d83d3-2c87-41eb-9247-f763f550a666", "logId": "fa327741-8e74-42b6-93f4-3030c2ef8996"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c1d83d3-2c87-41eb-9247-f763f550a666", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893119013200}, "additional": {"logType": "detail", "children": [], "durationId": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0"}}, {"head": {"id": "82c341e3-7440-4044-a673-4bed7a50eaad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893119676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6def6fd-3fd4-480e-9f61-6fcb8e4ab19e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893119795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785c007d-bfad-43dd-9567-54a1f6e7104c", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893120984100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f2a6d7f-97c9-4318-b843-1522e2f685d8", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893123414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca303654-9fd1-439d-8acb-59fa0f70c539", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893245253200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b92f4c2-0e67-4bf6-b8e7-b0c57b55b3de", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 122 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893245384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42814a5c-60fe-4659-823c-a77070478feb", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893260412300, "endTime": 21895760332100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0", "logId": "12639163-a431-4ba6-aff9-fb82f023d424"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fffcc48b-2dae-4cd6-91e0-c6c6ef3919b0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21894702287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae1e4a7-b338-4def-b064-5b49fc7af5d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21894968274200, "endTime": 21894968306400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "8c14b598-7de2-4e1d-afed-f60f683413db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c14b598-7de2-4e1d-afed-f60f683413db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21894968274200, "endTime": 21894968306400}, "additional": {"logType": "info", "children": [], "durationId": "3ae1e4a7-b338-4def-b064-5b49fc7af5d9"}}, {"head": {"id": "12639163-a431-4ba6-aff9-fb82f023d424", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893260412300, "endTime": 21895760332100}, "additional": {"logType": "info", "children": [], "durationId": "42814a5c-60fe-4659-823c-a77070478feb", "parent": "fa327741-8e74-42b6-93f4-3030c2ef8996"}}, {"head": {"id": "2fa2738a-7758-4f88-9e52-4856fcd99673", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895760823200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30648f5f-1598-47b9-9c65-9d7729f11228", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895762288400, "endTime": 21896233934700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0", "logId": "90813137-ad46-47f8-9899-8a0ecb1e2c73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1738813d-fb5e-4fc4-b000-f5891a938884", "name": "current process  memoryUsage: {\n  rss: 495239168,\n  heapTotal: 130375680,\n  heapUsed: 107554952,\n  external: 3075151,\n  arrayBuffers: 69016\n} os memoryUsage :6.512630462646484", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895763721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c470fa-efff-47e2-9533-0bdd1d79ed7f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895850275000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db2b36bc-da98-4834-8c92-c5a9c6261611", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895855794000, "endTime": 21895855879200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "aaf0cc6e-a8a8-4d0c-acf8-86dca1805f49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaf0cc6e-a8a8-4d0c-acf8-86dca1805f49", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895855794000, "endTime": 21895855879200}, "additional": {"logType": "info", "children": [], "durationId": "db2b36bc-da98-4834-8c92-c5a9c6261611"}}, {"head": {"id": "556d6d9a-41e0-4d09-acd5-54c722a0d254", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895856228600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed28881-7dee-4e3d-9140-4231b5a33acb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895862215000, "endTime": 21895862283400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "3b836f8a-25a2-40e2-8b89-2e2c3f3334b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b836f8a-25a2-40e2-8b89-2e2c3f3334b4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895862215000, "endTime": 21895862283400}, "additional": {"logType": "info", "children": [], "durationId": "3ed28881-7dee-4e3d-9140-4231b5a33acb"}}, {"head": {"id": "cfece5fc-a53c-49e1-bb2c-e3c918826f44", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895862749000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d804f9-608f-4ad3-8292-a8c590b20704", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895935225500, "endTime": 21895935272800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f39e5851-a321-4c48-a614-e576c8a6170c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f39e5851-a321-4c48-a614-e576c8a6170c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895935225500, "endTime": 21895935272800}, "additional": {"logType": "info", "children": [], "durationId": "63d804f9-608f-4ad3-8292-a8c590b20704"}}, {"head": {"id": "d946fbf1-3a7e-4d9b-ab4c-c3f4f9e68da6", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896231397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90813137-ad46-47f8-9899-8a0ecb1e2c73", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21895762288400, "endTime": 21896233934700}, "additional": {"logType": "info", "children": [], "durationId": "30648f5f-1598-47b9-9c65-9d7729f11228", "parent": "fa327741-8e74-42b6-93f4-3030c2ef8996"}}, {"head": {"id": "b746869e-997e-47ab-95f6-6227e09f65fa", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896234084700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c934a5-0aed-4f76-83ad-9eb4929d41c6", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896235592800, "endTime": 21896361199200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0", "logId": "9fb43713-c9e0-4233-8c5d-f7c2a81fce7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "515a74a8-2ec0-4668-89f3-ccf7a5a764bb", "name": "current process  memoryUsage: {\n  rss: 500527104,\n  heapTotal: 130375680,\n  heapUsed: 108064296,\n  external: 3083469,\n  arrayBuffers: 77349\n} os memoryUsage :6.5472564697265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896237121600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52a08a9-e734-44f4-a270-bf4505642f1c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896351920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb43713-c9e0-4233-8c5d-f7c2a81fce7a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896235592800, "endTime": 21896361199200}, "additional": {"logType": "info", "children": [], "durationId": "10c934a5-0aed-4f76-83ad-9eb4929d41c6", "parent": "fa327741-8e74-42b6-93f4-3030c2ef8996"}}, {"head": {"id": "3e64e762-8ee4-41fe-b44c-effce84232da", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896361376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5988bd-2530-449a-bbeb-0a0335924eaf", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896362882100, "endTime": 21896738046900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0", "logId": "6681a6f0-8331-4185-8440-3b6a7afb9106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67ff2e03-0382-43e3-b866-9155187ca400", "name": "current process  memoryUsage: {\n  rss: 501694464,\n  heapTotal: 130375680,\n  heapUsed: 108335224,\n  external: 3083595,\n  arrayBuffers: 78289\n} os memoryUsage :6.548248291015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896363952100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91206329-171e-4d4e-9b2b-adca902a2cb9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896420872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68fd7b32-26cf-4b86-a015-fa0ca1d6c8c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896421899800, "endTime": 21896421915800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "89bb4288-b2e8-4a2b-b230-c50a68d72c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89bb4288-b2e8-4a2b-b230-c50a68d72c8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896421899800, "endTime": 21896421915800}, "additional": {"logType": "info", "children": [], "durationId": "68fd7b32-26cf-4b86-a015-fa0ca1d6c8c0"}}, {"head": {"id": "b4be1a8c-081f-47d0-b9a3-dd5e3e7a0622", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896614396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db41775-8cab-4314-8211-a281b40daf50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896616202700, "endTime": 21896616254200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2865ea7b-fe1a-4ee1-8483-2c0cc1a7952a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2865ea7b-fe1a-4ee1-8483-2c0cc1a7952a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896616202700, "endTime": 21896616254200}, "additional": {"logType": "info", "children": [], "durationId": "1db41775-8cab-4314-8211-a281b40daf50"}}, {"head": {"id": "5d986311-3685-4880-afc8-7f51216f6ece", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896734901000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6681a6f0-8331-4185-8440-3b6a7afb9106", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896362882100, "endTime": 21896738046900}, "additional": {"logType": "info", "children": [], "durationId": "7d5988bd-2530-449a-bbeb-0a0335924eaf", "parent": "fa327741-8e74-42b6-93f4-3030c2ef8996"}}, {"head": {"id": "63678bb0-c4e5-4693-b3d6-019bfd3ae060", "name": "entry : default@PreviewCompileResource cost memory -11.586090087890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896763117000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ba01ee-54ca-4cd5-9c20-ab9f38a49e93", "name": "runTaskFromQueue task cost before running: 11 s 809 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896763348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa327741-8e74-42b6-93f4-3030c2ef8996", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21893123378400, "endTime": 21896763407400, "totalTime": 3639917200}, "additional": {"logType": "info", "children": ["12639163-a431-4ba6-aff9-fb82f023d424", "90813137-ad46-47f8-9899-8a0ecb1e2c73", "9fb43713-c9e0-4233-8c5d-f7c2a81fce7a", "6681a6f0-8331-4185-8440-3b6a7afb9106"], "durationId": "0529af2c-ce18-4bea-ba01-7ef349ee1fb0"}}, {"head": {"id": "bf3e3f62-7b65-42b4-adda-f7f3840b673f", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766635000, "endTime": 21896766963800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1f3d7817-18ac-4aff-b0a4-fb18a3cbeb4b", "logId": "db140e50-3701-4c74-bbae-2ccbc8e44121"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f3d7817-18ac-4aff-b0a4-fb18a3cbeb4b", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896765913500}, "additional": {"logType": "detail", "children": [], "durationId": "bf3e3f62-7b65-42b4-adda-f7f3840b673f"}}, {"head": {"id": "8f548bb3-65e2-46c3-aea1-126a2c6183b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9006756e-0568-4eb4-8759-c408949f3329", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766545100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ccdcf7-74e7-4a86-afad-7e7295de86b6", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766643200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6064cd76-a247-4702-99cb-b745e6586359", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766734500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bffe8f-6c15-4413-ab46-64808247eaa0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766783200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f7cb08-7ebd-4612-9add-190ad6444856", "name": "entry : default@PreviewHookCompileResource cost memory 0.03791046142578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff4a65d-34cb-4acd-bd8b-f9aae4ad35f7", "name": "runTaskFromQueue task cost before running: 11 s 812 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db140e50-3701-4c74-bbae-2ccbc8e44121", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896766635000, "endTime": 21896766963800, "totalTime": 266700}, "additional": {"logType": "info", "children": [], "durationId": "bf3e3f62-7b65-42b4-adda-f7f3840b673f"}}, {"head": {"id": "4ac48333-955f-4cfa-afe2-452072c08a54", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896770404600, "endTime": 21896777605100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "942aba42-63e0-4d06-84a3-9423b45c6b86", "logId": "e99064e4-8fc0-4268-b174-0b0444578b97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "942aba42-63e0-4d06-84a3-9423b45c6b86", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896769074600}, "additional": {"logType": "detail", "children": [], "durationId": "4ac48333-955f-4cfa-afe2-452072c08a54"}}, {"head": {"id": "8ea454e3-351e-43c9-b5e3-fd769f9187e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896769632100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24abc891-20bc-4857-a301-a239911ffd2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896769731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a73df4-da9f-451c-973d-03822e93a749", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896770414200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5743ac44-d040-47f0-adc7-c7eed167bb6d", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896771898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f335e0-b02e-4e9d-b0d1-110400fb9a0a", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896771999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc257de-61ba-43a5-a15f-6c9fbaf43df6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896772076400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf92795-ebde-4b83-bbdf-8b7476e86f13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896772124200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ff6ecb-0512-44b3-a490-b84c85785c70", "name": "entry : default@CopyPreviewProfile cost memory 0.21157073974609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896777325000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6adda9b2-90b3-428a-ab0a-fad654f2a57f", "name": "runTaskFromQueue task cost before running: 11 s 823 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896777508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99064e4-8fc0-4268-b174-0b0444578b97", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896770404600, "endTime": 21896777605100, "totalTime": 7072500}, "additional": {"logType": "info", "children": [], "durationId": "4ac48333-955f-4cfa-afe2-452072c08a54"}}, {"head": {"id": "5bc05c84-01f7-44c3-a8fa-052d13dcd99d", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781250800, "endTime": 21896781637000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "04e70a04-cf63-4900-863d-7af6834ed031", "logId": "28772db1-17c3-4a39-93fe-d1bb14b94e4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04e70a04-cf63-4900-863d-7af6834ed031", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896779869200}, "additional": {"logType": "detail", "children": [], "durationId": "5bc05c84-01f7-44c3-a8fa-052d13dcd99d"}}, {"head": {"id": "aa0f04c3-3a0a-4ff1-8c93-808beb5d2ab2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896780396800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5face9cb-0b9b-467f-aa7a-7089b583a7ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896780487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e984f5-a8b4-46da-b0d9-fe1b6c73dbb6", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c58c96-4406-48be-9b4a-7beb0fe60df4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781367500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8dfd1b3-6f0f-4e60-b9a1-2fa863443c37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf33afd-f5e1-47f8-a793-3e5f46646d3e", "name": "entry : default@ReplacePreviewerPage cost memory 0.0378570556640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f701506f-aa12-4cc3-8c8e-0d1660e46038", "name": "runTaskFromQueue task cost before running: 11 s 827 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781586700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28772db1-17c3-4a39-93fe-d1bb14b94e4c", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896781250800, "endTime": 21896781637000, "totalTime": 318700}, "additional": {"logType": "info", "children": [], "durationId": "5bc05c84-01f7-44c3-a8fa-052d13dcd99d"}}, {"head": {"id": "392c8c55-bb34-4495-afd0-5a575f02d44f", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783417600, "endTime": 21896783687900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fbc5621a-b744-4f12-b7e5-e8f7db22697a", "logId": "e2ad2f9d-df75-4f23-8f5c-a61c13c95c87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbc5621a-b744-4f12-b7e5-e8f7db22697a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783352500}, "additional": {"logType": "detail", "children": [], "durationId": "392c8c55-bb34-4495-afd0-5a575f02d44f"}}, {"head": {"id": "dc2a5075-7fa9-4c74-9daa-f4d4f429a595", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783431000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85aa3d11-31a8-4d5b-a07b-2b450c9af4d7", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783550500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604ed049-7341-4fe9-9247-72a493949f60", "name": "runTaskFromQueue task cost before running: 11 s 829 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783631900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ad2f9d-df75-4f23-8f5c-a61c13c95c87", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896783417600, "endTime": 21896783687900, "totalTime": 194200}, "additional": {"logType": "info", "children": [], "durationId": "392c8c55-bb34-4495-afd0-5a575f02d44f"}}, {"head": {"id": "01fe8644-545a-4f2b-b926-ca86084e9afb", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896787334000, "endTime": 21896790530900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "41c26f7c-684a-41d2-9f9f-28512e04f9f6", "logId": "d6a3a906-6500-4e4c-8b3d-3317d3d21f34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41c26f7c-684a-41d2-9f9f-28512e04f9f6", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896785869600}, "additional": {"logType": "detail", "children": [], "durationId": "01fe8644-545a-4f2b-b926-ca86084e9afb"}}, {"head": {"id": "18694a86-8cb4-4885-90b6-823dcf2461b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896786434100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f8440a2-54c8-4e95-bc16-d99e1350e3e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896786532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3b07020-cedb-4b7d-b51d-07c77e7c8004", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896787343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d332bb-54d6-4724-82a5-bee03e8f25af", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896789361900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce2e9f4-f933-4ca7-a7d6-e6316027ccf5", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896789476300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "168aaa9d-8d27-4de0-b9dc-f3a1703cd65a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896789554500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2384a57-1f31-49de-95fe-e9fed7fd380d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896789603700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75228ca6-3c4a-4fc5-848c-fc90d92dbaec", "name": "entry : default@PreviewUpdateAssets cost memory 0.15566253662109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896790370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "345bd9ad-2a23-4b49-9c60-a29908d8db2a", "name": "runTaskFromQueue task cost before running: 11 s 836 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896790472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a3a906-6500-4e4c-8b3d-3317d3d21f34", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896787334000, "endTime": 21896790530900, "totalTime": 3119500}, "additional": {"logType": "info", "children": [], "durationId": "01fe8644-545a-4f2b-b926-ca86084e9afb"}}, {"head": {"id": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896798506500, "endTime": 21942615938000}, "additional": {"children": ["60ecbb9d-f155-4948-81ff-6216ad2f7978"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "40deeb4a-05af-41c1-aba9-d5c205d18682", "logId": "57941bcf-b8a8-43a6-a200-05a0e26422ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40deeb4a-05af-41c1-aba9-d5c205d18682", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896792716600}, "additional": {"logType": "detail", "children": [], "durationId": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e"}}, {"head": {"id": "87e0ca2a-7131-4083-8406-1e122ce46a63", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896793228800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "625e8747-3a60-46b3-aad4-d0a93cc503af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896793322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c267149-4fe5-4dae-946a-2375d8da3f41", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896798520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d63557-fd94-4b56-a60b-ec11836e6f91", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896809911100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29deac53-c768-4154-9618-4330561007c9", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896810047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker20", "startTime": 21896861722200, "endTime": 21942615770300}, "additional": {"children": ["e9d25cf0-0f04-47d8-b60a-9b5f99ef0b8d", "6d25326a-9820-4a40-81a1-c07717bdc528", "c3c430bf-193e-4b1e-88b7-79b693258012", "0ca20b38-7783-4f45-9368-89b8ee939de5", "91e2530e-016c-4bca-a431-6c325c2ccff2"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e", "logId": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c0ef538-df44-47e7-87e0-1c7c57734753", "name": "entry : default@PreviewArkTS cost memory -2.4325103759765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896863832700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f523be-a245-4131-b9e9-61a418989692", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896940466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0beab470-a7a4-4dac-8ac8-625167f3d900", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896941558900, "endTime": 21896941579100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "d90168f8-8cb4-49e2-af7d-0ee47556854b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d90168f8-8cb4-49e2-af7d-0ee47556854b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896941558900, "endTime": 21896941579100}, "additional": {"logType": "info", "children": [], "durationId": "0beab470-a7a4-4dac-8ac8-625167f3d900"}}, {"head": {"id": "4f34df16-42b4-416d-bd30-10921342691f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897179244000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a74ae3d-9029-415f-97f2-8da6c6adcb12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897181854400, "endTime": 21897181895200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "8286c2a9-ab9f-47f7-8a51-ade025dcdb1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8286c2a9-ab9f-47f7-8a51-ade025dcdb1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897181854400, "endTime": 21897181895200}, "additional": {"logType": "info", "children": [], "durationId": "8a74ae3d-9029-415f-97f2-8da6c6adcb12"}}, {"head": {"id": "2290881e-8b2b-46e5-9b8c-ba1e2eca740a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897182087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edcf42c2-c555-4eb4-8a74-db25fee80a25", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897185026600, "endTime": 21897185075300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a0f76c20-c2f8-45c4-8b0c-0d8897148253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0f76c20-c2f8-45c4-8b0c-0d8897148253", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897185026600, "endTime": 21897185075300}, "additional": {"logType": "info", "children": [], "durationId": "edcf42c2-c555-4eb4-8a74-db25fee80a25"}}, {"head": {"id": "b27d2beb-2280-491a-8a28-1602f5c33f80", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897185389300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b546b0f2-2ec7-42d3-929b-b5ef7f4816fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897187352500, "endTime": 21897187388300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "3fdb9776-4604-463e-b86f-c51b46d87f46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fdb9776-4604-463e-b86f-c51b46d87f46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897187352500, "endTime": 21897187388300}, "additional": {"logType": "info", "children": [], "durationId": "b546b0f2-2ec7-42d3-929b-b5ef7f4816fe"}}, {"head": {"id": "93c8d5c9-c993-4157-aa6c-0d9a1c340c9f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897187551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8515eb55-6a1d-4e87-be98-9d345ef7626e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897189467800, "endTime": 21897189500800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a2c73ab8-a724-4372-84aa-cd3991784505"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2c73ab8-a724-4372-84aa-cd3991784505", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897189467800, "endTime": 21897189500800}, "additional": {"logType": "info", "children": [], "durationId": "8515eb55-6a1d-4e87-be98-9d345ef7626e"}}, {"head": {"id": "edef2a03-ce03-4c59-ab2d-c1bed1cfecdd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897189638600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb37f06-1de7-45bb-889b-14322c1e8bd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897191920100, "endTime": 21897191958600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "aebd3346-b97c-42ba-bdcb-67cc5bf6f531"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aebd3346-b97c-42ba-bdcb-67cc5bf6f531", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897191920100, "endTime": 21897191958600}, "additional": {"logType": "info", "children": [], "durationId": "3fb37f06-1de7-45bb-889b-14322c1e8bd8"}}, {"head": {"id": "74d9b568-e404-4ce3-9915-db89b050d005", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897192129400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88c6320-e281-429d-a9d2-fc54198bddfc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897194567200, "endTime": 21897194609700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "1d2321d1-f19e-492f-9212-21c5ec8a7c59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d2321d1-f19e-492f-9212-21c5ec8a7c59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897194567200, "endTime": 21897194609700}, "additional": {"logType": "info", "children": [], "durationId": "f88c6320-e281-429d-a9d2-fc54198bddfc"}}, {"head": {"id": "451e334b-84f6-4962-a148-deac452b5b87", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897194792300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98600111-227c-4c57-8a9f-ec64796f54a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897197301300, "endTime": 21897197344000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f89fb732-ad54-4782-a0b2-2fcdbc4f973c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f89fb732-ad54-4782-a0b2-2fcdbc4f973c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897197301300, "endTime": 21897197344000}, "additional": {"logType": "info", "children": [], "durationId": "98600111-227c-4c57-8a9f-ec64796f54a8"}}, {"head": {"id": "a0a12d7b-5dbc-43d7-94aa-d2d5cab79fb1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897197529400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b5e6dd2-6c67-49f0-ab13-dbf0eeb9c915", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897200088100, "endTime": 21897200158000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ad6769da-56ce-4820-8a06-c8afdbc5ce0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad6769da-56ce-4820-8a06-c8afdbc5ce0f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897200088100, "endTime": 21897200158000}, "additional": {"logType": "info", "children": [], "durationId": "2b5e6dd2-6c67-49f0-ab13-dbf0eeb9c915"}}, {"head": {"id": "3c1ebe20-3f1a-4827-af4e-050f4b431d72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897200370500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d74b514-3db2-421a-9203-5a2824c8d71d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897202571300, "endTime": 21897202608600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9fc053c8-49b8-496b-b1f9-f2a6937980cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fc053c8-49b8-496b-b1f9-f2a6937980cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897202571300, "endTime": 21897202608600}, "additional": {"logType": "info", "children": [], "durationId": "4d74b514-3db2-421a-9203-5a2824c8d71d"}}, {"head": {"id": "aef610f6-46ca-4ac2-bfd2-b8773bd4853c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897202767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48da741b-7c7b-4439-bd3a-7e69051c6edb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897204521600, "endTime": 21897204553700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f8c0ba4c-6a48-448c-a716-69405712a5a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8c0ba4c-6a48-448c-a716-69405712a5a1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897204521600, "endTime": 21897204553700}, "additional": {"logType": "info", "children": [], "durationId": "48da741b-7c7b-4439-bd3a-7e69051c6edb"}}, {"head": {"id": "6291b450-8d4d-4460-afb9-f1361b0d6bc2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897204684100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc4a405-dae7-446d-9304-dc0fefeba81e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897206306900, "endTime": 21897206334200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "bb46b35a-28f4-486c-9bef-b8cb2b92619b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb46b35a-28f4-486c-9bef-b8cb2b92619b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897206306900, "endTime": 21897206334200}, "additional": {"logType": "info", "children": [], "durationId": "bbc4a405-dae7-446d-9304-dc0fefeba81e"}}, {"head": {"id": "d4c17dcb-2ea8-48e8-8ee8-764da7af91b5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897206458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5384ca-9c70-4288-8562-02bec94ef139", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897208078700, "endTime": 21897208110800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e15a7f9f-28f6-4931-9499-07d65267b808"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e15a7f9f-28f6-4931-9499-07d65267b808", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897208078700, "endTime": 21897208110800}, "additional": {"logType": "info", "children": [], "durationId": "1b5384ca-9c70-4288-8562-02bec94ef139"}}, {"head": {"id": "1893e68b-7168-4173-a1af-2c21e1d7bea4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897208234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b44e00-7584-4d7c-9e06-b9eb88ca09dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897209895100, "endTime": 21897209922600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "94908714-cb66-42a1-92ad-3320ea590e6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94908714-cb66-42a1-92ad-3320ea590e6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897209895100, "endTime": 21897209922600}, "additional": {"logType": "info", "children": [], "durationId": "04b44e00-7584-4d7c-9e06-b9eb88ca09dd"}}, {"head": {"id": "c5c14ce5-89f4-4f95-bb08-d7bfd8d7eab4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897210047600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570a1580-3ccf-4c2a-8c70-d0cb866e98a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897211652100, "endTime": 21897211680000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9cb2c64a-45f9-4585-9fd9-24fc71adb75d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb2c64a-45f9-4585-9fd9-24fc71adb75d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897211652100, "endTime": 21897211680000}, "additional": {"logType": "info", "children": [], "durationId": "570a1580-3ccf-4c2a-8c70-d0cb866e98a4"}}, {"head": {"id": "7166b295-a30c-4a85-8213-e664c279b4a3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897211845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9270af-74bc-4364-978f-a0a5c230c51b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897213593700, "endTime": 21897213627500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "42a25969-6cfb-46ff-86fb-0d5936b115ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42a25969-6cfb-46ff-86fb-0d5936b115ea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897213593700, "endTime": 21897213627500}, "additional": {"logType": "info", "children": [], "durationId": "8c9270af-74bc-4364-978f-a0a5c230c51b"}}, {"head": {"id": "9961b641-7071-40d9-a8ee-254c943f8b3e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897428826300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090ec6c8-d3aa-4115-a500-2e7dc1565a5b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897430023700, "endTime": 21897430043200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9cdf340e-447f-4816-9bb7-09800872e1c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cdf340e-447f-4816-9bb7-09800872e1c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897430023700, "endTime": 21897430043200}, "additional": {"logType": "info", "children": [], "durationId": "090ec6c8-d3aa-4115-a500-2e7dc1565a5b"}}, {"head": {"id": "1e4c46a7-5f24-4e5b-a69d-9b84ed4ef475", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897776163800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78577d82-2597-45eb-8611-0fcdbacc99d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897781521500, "endTime": 21897781596000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c3727f0f-0961-4cb3-b825-225709f20c7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3727f0f-0961-4cb3-b825-225709f20c7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897781521500, "endTime": 21897781596000}, "additional": {"logType": "info", "children": [], "durationId": "78577d82-2597-45eb-8611-0fcdbacc99d5"}}, {"head": {"id": "c5b8b773-d321-4509-9533-5e53de0a61c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897781920600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52c4bd8-4314-4636-901e-163c82cb19ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897785983200, "endTime": 21897786017300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "1d758cd7-16ea-4af0-9ac8-02857f480cc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d758cd7-16ea-4af0-9ac8-02857f480cc0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897785983200, "endTime": 21897786017300}, "additional": {"logType": "info", "children": [], "durationId": "e52c4bd8-4314-4636-901e-163c82cb19ca"}}, {"head": {"id": "ca95d206-df79-44c8-b6c8-2103e10d95ca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897786170500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8114d44b-3a56-47b2-b3b9-626affdf5c9e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897788025400, "endTime": 21897788058000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "fe333717-a340-4678-bd9d-fabcb32678c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe333717-a340-4678-bd9d-fabcb32678c1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21897788025400, "endTime": 21897788058000}, "additional": {"logType": "info", "children": [], "durationId": "8114d44b-3a56-47b2-b3b9-626affdf5c9e"}}, {"head": {"id": "578ae943-f14f-4ee5-bc35-a3857cdcd9a8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898420649200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b736d6d-a9fb-4728-8ac5-daa5f54d5639", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898421764400, "endTime": 21898421783800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2c4e3843-b435-4efb-b9e6-298b1c275b72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c4e3843-b435-4efb-b9e6-298b1c275b72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898421764400, "endTime": 21898421783800}, "additional": {"logType": "info", "children": [], "durationId": "7b736d6d-a9fb-4728-8ac5-daa5f54d5639"}}, {"head": {"id": "fae80f31-ca07-4550-88c3-de18df0830f0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898521929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe4b572-3501-4f83-af55-76ed0334e0f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898522989300, "endTime": 21898523009800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7f888bcc-5131-458c-b726-3d6544f3cd8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f888bcc-5131-458c-b726-3d6544f3cd8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898522989300, "endTime": 21898523009800}, "additional": {"logType": "info", "children": [], "durationId": "3fe4b572-3501-4f83-af55-76ed0334e0f3"}}, {"head": {"id": "34f5b11f-0b83-400d-b918-888f09986f22", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898913500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65966d8-852a-4959-aa78-61700566fba9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898914473700, "endTime": 21898914492900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "95993c99-05e7-44e9-9409-43d887f789d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95993c99-05e7-44e9-9409-43d887f789d2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898914473700, "endTime": 21898914492900}, "additional": {"logType": "info", "children": [], "durationId": "e65966d8-852a-4959-aa78-61700566fba9"}}, {"head": {"id": "7a52144d-e106-48e1-a0a1-aa8267e41eb6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898914581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f2a26f-8e4f-4c95-a898-ab42d83bd365", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898915479300, "endTime": 21898915496800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "00d498da-a0d2-4218-804e-fa7386339218"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00d498da-a0d2-4218-804e-fa7386339218", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898915479300, "endTime": 21898915496800}, "additional": {"logType": "info", "children": [], "durationId": "23f2a26f-8e4f-4c95-a898-ab42d83bd365"}}, {"head": {"id": "e426c90d-d086-4102-a729-7209fe28a526", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898915576200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c29be6a-6254-4ac3-adda-081af0eff182", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898916931100, "endTime": 21898916950700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c8b487ba-c8b0-4fe5-9da4-01ad93ad7b82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8b487ba-c8b0-4fe5-9da4-01ad93ad7b82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21898916931100, "endTime": 21898916950700}, "additional": {"logType": "info", "children": [], "durationId": "3c29be6a-6254-4ac3-adda-081af0eff182"}}, {"head": {"id": "b4bc7c56-f091-4b7e-803a-4efe5352b0f2", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21899503441900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7375e4-3323-428d-8ebc-18329372645a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21899504413700, "endTime": 21899504432300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "050df6d0-d0f6-4ba3-934a-ff8c0d468ed5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "050df6d0-d0f6-4ba3-934a-ff8c0d468ed5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21899504413700, "endTime": 21899504432300}, "additional": {"logType": "info", "children": [], "durationId": "0a7375e4-3323-428d-8ebc-18329372645a"}}, {"head": {"id": "f7a442a1-8fb5-4ee2-acc8-d21a8b167a3e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21928068440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d25cf0-0f04-47d8-b60a-9b5f99ef0b8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21928071354300, "endTime": 21928071401700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "logId": "0739b6e9-4298-4741-8f04-80120afbf463"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0739b6e9-4298-4741-8f04-80120afbf463", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21928071354300, "endTime": 21928071401700}, "additional": {"logType": "info", "children": [], "durationId": "e9d25cf0-0f04-47d8-b60a-9b5f99ef0b8d", "parent": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}}, {"head": {"id": "760f18d8-8ba2-4e2a-80e4-afba68ca27c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934777164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d25326a-9820-4a40-81a1-c07717bdc528", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934780028800, "endTime": 21934780092500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "logId": "a9b5dd9c-5d66-4443-92c0-3fcd6cb02047"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9b5dd9c-5d66-4443-92c0-3fcd6cb02047", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934780028800, "endTime": 21934780092500}, "additional": {"logType": "info", "children": [], "durationId": "6d25326a-9820-4a40-81a1-c07717bdc528", "parent": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}}, {"head": {"id": "205d882c-da20-42c6-9215-330a57ca5114", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934780294800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c430bf-193e-4b1e-88b7-79b693258012", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934782617500, "endTime": 21934782660800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "logId": "4f122bd1-190c-4cf1-aba0-7f0cf8c14004"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f122bd1-190c-4cf1-aba0-7f0cf8c14004", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934782617500, "endTime": 21934782660800}, "additional": {"logType": "info", "children": [], "durationId": "c3c430bf-193e-4b1e-88b7-79b693258012", "parent": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}}, {"head": {"id": "958ce7f8-60c4-4144-b6b1-99a6aab579ac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934782855200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca20b38-7783-4f45-9368-89b8ee939de5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934785378300, "endTime": 21934785424000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "logId": "c7100f4b-8c29-4bc8-afe2-dd7dbb0c279a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7100f4b-8c29-4bc8-afe2-dd7dbb0c279a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21934785378300, "endTime": 21934785424000}, "additional": {"logType": "info", "children": [], "durationId": "0ca20b38-7783-4f45-9368-89b8ee939de5", "parent": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}}, {"head": {"id": "8f9cf7c9-61d8-411f-a8d0-f97c293db8df", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942614624500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91e2530e-016c-4bca-a431-6c325c2ccff2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942615666200, "endTime": 21942615686100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "logId": "caaac75c-7ca4-460e-b33b-e4f77f6f4fb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caaac75c-7ca4-460e-b33b-e4f77f6f4fb7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942615666200, "endTime": 21942615686100}, "additional": {"logType": "info", "children": [], "durationId": "91e2530e-016c-4bca-a431-6c325c2ccff2", "parent": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"}}, {"head": {"id": "3c0dc0d1-fb3e-4b38-b3d9-995e5090da44", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker20", "startTime": 21896861722200, "endTime": 21942615770300}, "additional": {"logType": "error", "children": ["0739b6e9-4298-4741-8f04-80120afbf463", "a9b5dd9c-5d66-4443-92c0-3fcd6cb02047", "4f122bd1-190c-4cf1-aba0-7f0cf8c14004", "c7100f4b-8c29-4bc8-afe2-dd7dbb0c279a", "caaac75c-7ca4-460e-b33b-e4f77f6f4fb7"], "durationId": "60ecbb9d-f155-4948-81ff-6216ad2f7978", "parent": "57941bcf-b8a8-43a6-a200-05a0e26422ed"}}, {"head": {"id": "38d4462a-becb-4f84-801d-031c9328c449", "name": "default@PreviewArkTS watch work[20] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942615801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57941bcf-b8a8-43a6-a200-05a0e26422ed", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21896798506500, "endTime": 21942615938000}, "additional": {"logType": "error", "children": ["3c0dc0d1-fb3e-4b38-b3d9-995e5090da44"], "durationId": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e"}}, {"head": {"id": "2275a481-76d0-4618-bdac-de6b3aa22761", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942616055300}, "additional": {"logType": "debug", "children": [], "durationId": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e"}}, {"head": {"id": "32957ef3-84f9-4ad3-9594-267113981000", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:113:24\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:122:24\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942636543800}, "additional": {"logType": "debug", "children": [], "durationId": "17107ffe-ce7e-4149-9582-f2bbaeaf5c6e"}}, {"head": {"id": "bf37d564-e22f-4c6b-9e1a-a74f2a8610fa", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643062900, "endTime": 21942643118900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3049b495-555f-49ab-b5a3-605adbd4d01c", "logId": "7bfa53d7-1ffe-4aff-8529-5365549b41fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bfa53d7-1ffe-4aff-8529-5365549b41fe", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643062900, "endTime": 21942643118900}, "additional": {"logType": "info", "children": [], "durationId": "bf37d564-e22f-4c6b-9e1a-a74f2a8610fa"}}, {"head": {"id": "38b3bec2-92ee-482c-9a14-ba8edc7ca069", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21884955071400, "endTime": 21942643250200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 55}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "6049d374-e188-422d-a2cc-96721a418a4e", "name": "BUILD FAILED in 57 s 689 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643278800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "8385ed90-096e-4d6e-8f4c-dd785fb1ac4a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1ed18d-ebdf-4e93-b3e3-de152b7fa6cf", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643464700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235748f1-3cbc-4286-a49e-f798ba628a53", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ee9919-e5b3-4c9e-a84d-6d6c5e100df3", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643554000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4793426-6334-4616-85bf-2ae42924bce5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643596900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea59909e-b924-4745-a606-ece8cc25096b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643637100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac94537-4e9c-4196-953c-afba0ee48663", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942643678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d08ec18-1602-44e4-9266-816579b3ddf6", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942644363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849929a5-8bcf-4041-84f5-2fc77f7ef9ad", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942667935700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8ae7b5-3adb-4673-8f4e-d8f62fae2159", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942668262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24824798-6e73-4a88-9c31-ee1602e49469", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942678354200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf9773dc-b08b-4e1b-8891-6c1c595bd954", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:36 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942679000700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fb9853-2e6f-45f9-872d-1e1a68e26998", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942679223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b86a24-2424-44e4-a697-19be981d9288", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942680080700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f035f5d1-d40c-4a16-9225-5ccfe2410137", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942680916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4098b025-86d1-41ad-aa39-646a9e1860c7", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942681332900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ad40b1-3d8c-49fe-83e0-816134093e81", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942681621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf4586e-a107-46f4-9c42-7cd17538ebc3", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942681992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42eb894d-36d2-4a04-9733-b60d666d6d72", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942684760500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5bd747-da92-4f27-b568-0eb6dc16b94e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942685632100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3146e705-8b52-44eb-be0f-49aec99fe7a2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942685737800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2883646-2d89-40be-8784-bfb003689c81", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942686008800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea21a6b3-419b-4d86-a03c-3910b4e18f5c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942686847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b2c371-52fc-4475-89d4-f77c9b098d33", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942699686800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50310ced-a162-418d-ad3e-b760f2219595", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942700020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c7f565-a0b8-4f7a-817e-e046457cf6ee", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942700313200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ff43142-1452-48a0-9899-7e4c796060e0", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942700628300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a2e39e6-d042-4167-8602-dea738bb40e6", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942700910800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}