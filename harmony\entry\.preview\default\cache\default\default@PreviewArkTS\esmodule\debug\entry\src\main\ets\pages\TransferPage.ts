if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransferPage_Params {
    transactions?: Transaction[];
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { TransactionType, TransactionStatus, PaymentMethod } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class TransferPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransferPage_Params) {
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: TransferPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.loadTransactions();
    }
    async loadTransactions() {
        try {
            this.isLoading = true;
            const userId = 1; // 临时使用固定用户ID
            const transactions = await TransactionApi.getTransactionList(userId);
            this.transactions = transactions;
        }
        catch (error) {
            console.error('加载交易记录失败:', error);
            promptAction.showToast({ message: '加载交易记录失败' });
            this.transactions = this.getTestData();
        }
        finally {
            this.isLoading = false;
        }
    }
    getTestData(): Transaction[] {
        return [
            {
                transactionId: 1,
                transactionNo: 'T001',
                fromUserId: 1,
                toUserId: 2,
                amount: 500.00,
                transactionType: TransactionType.TRANSFER,
                paymentMethod: PaymentMethod.BANK_CARD,
                description: '转账给李四',
                status: TransactionStatus.SUCCESS,
                createTime: '2023-05-15 14:30'
            },
            {
                transactionId: 2,
                transactionNo: 'T002',
                fromUserId: 1,
                toUserId: 1,
                amount: 1000.00,
                transactionType: TransactionType.RECHARGE,
                paymentMethod: PaymentMethod.BANK_CARD,
                description: '银行卡充值',
                status: TransactionStatus.SUCCESS,
                createTime: '2023-05-14 09:15'
            },
            {
                transactionId: 3,
                transactionNo: 'T003',
                fromUserId: 1,
                toUserId: 3,
                amount: 128.50,
                transactionType: TransactionType.PAYMENT,
                paymentMethod: PaymentMethod.WALLET,
                description: '星巴克咖啡',
                status: TransactionStatus.SUCCESS,
                createTime: '2023-05-12 18:45'
            },
            {
                transactionId: 4,
                transactionNo: 'T004',
                fromUserId: 1,
                toUserId: 1,
                amount: 300.00,
                transactionType: TransactionType.WITHDRAW,
                paymentMethod: PaymentMethod.BANK_CARD,
                description: '银行卡提现',
                status: TransactionStatus.PENDING,
                createTime: '2023-05-10 16:20'
            }
        ] as Transaction[];
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(92:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(94:7)", "entry");
            // 顶部导航栏
            Column.width('90%');
            // 顶部导航栏
            Column.margin({ top: 20 });
            // 顶部导航栏
            Column.borderRadius(16);
            // 顶部导航栏
            Column.backgroundColor('#ff3785f5');
            // 顶部导航栏
            Column.shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(95:9)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(96:11)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部导航栏
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.EmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/TransferPage.ets(116:9)", "entry");
                        Scroll.width('100%');
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(117:11)", "entry");
                        Column.width('100%');
                        Column.padding(16);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(118:13)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('全部交易');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(119:15)", "entry");
                        Text.fontSize(18);
                        Text.fontColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`共 ${this.transactions.length} 笔`);
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(124:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
                        Text.margin({ left: 8 });
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            this.TransactionItem.bind(this)(item);
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
        this.BottomNavigation.bind(this)();
        Column.pop();
    }
    TransactionItem(item: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(152:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 12 });
            Column.backgroundColor({ "id": 16777223, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Column.borderRadius(12);
            Column.shadow({ radius: 4, color: { "id": 16777224, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, offsetX: 0, offsetY: 2 });
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionDetailPage',
                    params: { id: item.transactionId.toString() }
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(153:7)", "entry");
            Row.width('100%');
            Row.padding(16);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(154:9)", "entry");
            Column.width(48);
            Column.height(48);
            Column.backgroundColor(this.getTypeColor(item.transactionType) + '20');
            Column.borderRadius(24);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.margin({ right: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeIcon(item.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(155:11)", "entry");
            Text.fontSize(24);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(166:9)", "entry");
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(167:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeName(item.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(168:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${item.amount.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(174:13)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.getAmountColor(item));
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.description);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(179:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(185:11)", "entry");
            Row.margin({ top: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.createTime);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(186:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getStatusName(item.status));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(190:13)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(item.status));
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        Row.pop();
        Column.pop();
    }
    getTypeIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '💸';
            case TransactionType.RECHARGE: return '💰';
            case TransactionType.WITHDRAW: return '💳';
            case TransactionType.PAYMENT: return '🛒';
            case TransactionType.RECEIVE: return '📥';
            case TransactionType.REFUND: return '↩️';
            default: return '📊';
        }
    }
    getTypeName(type: TransactionType): string {
        return type; // 枚举值本身就是中文
    }
    getTypeColor(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '#6366F1';
            case TransactionType.RECHARGE: return '#10B981';
            case TransactionType.WITHDRAW: return '#F59E0B';
            case TransactionType.PAYMENT: return '#4338CA';
            case TransactionType.RECEIVE: return '#059669';
            case TransactionType.REFUND: return '#7C3AED';
            default: return '#6B7280';
        }
    }
    getStatusName(status: TransactionStatus): string {
        return status; // 枚举值本身就是中文
    }
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS: return '#10B981';
            case TransactionStatus.PENDING: return '#F59E0B';
            case TransactionStatus.FAILED: return '#EF4444';
            case TransactionStatus.CANCELLED: return '#9CA3AF';
            default: return '#6B7280';
        }
    }
    getAmountColor(transaction: Transaction): string {
        // 充值和收钱显示绿色，其他显示红色
        if (transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE) {
            return '#10B981';
        }
        return '#EF4444';
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(268:5)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/TransferPage.ets(269:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(273:7)", "entry");
            Text.fontSize(14);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 16 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(286:5)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransferPage.ets(287:7)", "entry");
            Image.width(120);
            Image.height(120);
            Image.margin({ bottom: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(292:7)", "entry");
            Text.fontSize(16);
            Text.fontColor({ "id": 16777231, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
    }
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(304:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor({ "id": 16777223, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Row.border({ width: { top: 1 }, color: { "id": 16777225, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" } });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(305:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(306:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(307:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(312:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(313:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(314:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": 16777227, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(318:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/WalletPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(319:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(320:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(325:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(326:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(327:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(332:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/SettingsPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(333:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(334:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransferPage";
    }
}
registerNamedRoute(() => new TransferPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransferPage", pageFullPath: "entry/src/main/ets/pages/TransferPage", integratedHsp: "false", moduleType: "followWithHap" });
