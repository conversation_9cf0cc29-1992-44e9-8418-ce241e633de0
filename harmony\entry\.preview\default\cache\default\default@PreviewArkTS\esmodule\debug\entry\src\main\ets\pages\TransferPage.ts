if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionListPage_Params {
    transactions?: Transaction[];
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import type { PageResult } from '../common/types/PageResult';
import { TransactionStatus, TransactionType } from "@normalized:N&&&entry/src/main/ets/common/types/Transaction&";
import type { Transaction } from "@normalized:N&&&entry/src/main/ets/common/types/Transaction&";
class TransactionListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionListPage_Params) {
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: TransactionListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.loadTransactions();
    }
    async loadTransactions() {
        try {
            this.isLoading = true;
            const result: PageResult<Transaction> = await TransactionApi.getTransactionList();
            this.transactions = result.records || [];
        }
        catch (error) {
            console.error('加载交易记录失败:', error);
            promptAction.showToast({ message: '加载交易记录失败' });
            this.transactions = this.getTestData();
        }
        finally {
            this.isLoading = false;
        }
    }
    getTestData(): Transaction[] {
        return [
            {
                id: 1,
                type: TransactionType.TRANSFER,
                amount: 500.00,
                target: '李四',
                time: '2023-05-15 14:30',
                status: TransactionStatus.COMPLETED
            },
            {
                id: 2,
                type: TransactionType.DEPOSIT,
                amount: 1000.00,
                target: '银行卡(尾号1234)',
                time: '2023-05-14 09:15',
                status: TransactionStatus.COMPLETED
            },
            {
                id: 3,
                type: TransactionType.CONSUME,
                amount: -128.50,
                target: '星巴克咖啡',
                time: '2023-05-12 18:45',
                status: TransactionStatus.COMPLETED
            },
            {
                id: 4,
                type: TransactionType.WITHDRAW,
                amount: -300.00,
                target: '银行卡(尾号5678)',
                time: '2023-05-10 16:20',
                status: TransactionStatus.PROCESSING
            }
        ] as Transaction[];
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(69:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 16777244, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(71:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(60);
            // 顶部导航栏
            Row.justifyContent(FlexAlign.Center);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.linearGradient({
                direction: GradientDirection.Right,
                colors: [
                    [{ "id": 16777249, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 0.0],
                    [{ "id": 16777250, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, 1.0]
                ]
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(72:9)", "entry");
            Text.fontSize(20);
            Text.fontColor({ "id": 16777259, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LoadingView.bind(this)();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.EmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/TransferPage.ets(94:9)", "entry");
                        Scroll.width('100%');
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(95:11)", "entry");
                        Column.width('100%');
                        Column.padding(16);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(96:13)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('全部交易');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(97:15)", "entry");
                        Text.fontSize(18);
                        Text.fontColor({ "id": 16777253, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`共 ${this.transactions.length} 笔`);
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(102:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
                        Text.margin({ left: 8 });
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            this.TransactionItem.bind(this)(item);
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
        this.BottomNavigation.bind(this)();
        Column.pop();
    }
    TransactionItem(item: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(130:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 12 });
            Column.backgroundColor({ "id": 16777245, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Column.borderRadius(12);
            Column.shadow({ radius: 4, color: { "id": 16777246, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" }, offsetX: 0, offsetY: 2 });
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionDetailPage',
                    params: { id: item.id.toString() }
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(131:7)", "entry");
            Row.width('100%');
            Row.padding(16);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(132:9)", "entry");
            Column.width(48);
            Column.height(48);
            Column.backgroundColor(this.getTypeColor(item.type) + '20');
            Column.borderRadius(24);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.margin({ right: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeIcon(item.type));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(133:11)", "entry");
            Text.fontSize(24);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(144:9)", "entry");
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(145:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeName(item.type));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(146:13)", "entry");
            Text.fontSize(16);
            Text.fontColor({ "id": 16777253, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.amount > 0 ? `+¥${item.amount.toFixed(2)}` : `-¥${Math.abs(item.amount).toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(152:13)", "entry");
            Text.fontSize(16);
            Text.fontColor(item.amount > 0 ? { "id": 16777251, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" } : { "id": 16777258, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.target);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(157:11)", "entry");
            Text.fontSize(14);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(163:11)", "entry");
            Row.margin({ top: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.time);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(164:13)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": 16777252, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getStatusName(item.status));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(168:13)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(item.status));
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        Row.pop();
        Column.pop();
    }
    getTypeIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '💸';
            case TransactionType.DEPOSIT: return '💰';
            case TransactionType.WITHDRAW: return '💳';
            case TransactionType.CONSUME: return '🛒';
            default: return '📊';
        }
    }
    getTypeName(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '转账';
            case TransactionType.DEPOSIT: return '充值';
            case TransactionType.WITHDRAW: return '提现';
            case TransactionType.CONSUME: return '消费';
            default: return '交易';
        }
    }
    getTypeColor(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return { "id": 16777249, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionType.DEPOSIT: return { "id": 16777251, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionType.WITHDRAW: return { "id": 16777258, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionType.CONSUME: return { "id": 16777250, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            default: return { "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
        }
    }
    getStatusName(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.COMPLETED: return '已完成';
            case TransactionStatus.PROCESSING: return '处理中';
            case TransactionStatus.FAILED: return '失败';
            case TransactionStatus.CANCELED: return '已取消';
            default: return '未知状态';
        }
    }
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.COMPLETED: return { "id": 16777251, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionStatus.PROCESSING: return { "id": 16777258, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionStatus.FAILED: return { "id": 16777248, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            case TransactionStatus.CANCELED: return { "id": 16777252, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
            default: return { "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" };
        }
    }
    LoadingView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(245:5)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.debugLine("entry/src/main/ets/pages/TransferPage.ets(246:7)", "entry");
            LoadingProgress.width(40);
            LoadingProgress.height(40);
        }, LoadingProgress);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('加载中...');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(250:7)", "entry");
            Text.fontSize(14);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 16 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(263:5)", "entry");
            Column.width('100%');
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777228, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransferPage.ets(264:7)", "entry");
            Image.width(120);
            Image.height(120);
            Image.margin({ bottom: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(269:7)", "entry");
            Text.fontSize(16);
            Text.fontColor({ "id": 16777252, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
    }
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(281:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor({ "id": 16777245, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Row.border({ width: { top: 1 }, color: { "id": 16777247, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" } });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(282:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(283:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(284:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(289:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(290:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(291:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": 16777249, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(295:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/WalletPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(296:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(297:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(302:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(303:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(304:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(309:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/SettingsPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(310:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(311:9)", "entry");
            Text.fontSize(12);
            Text.fontColor({ "id": ********, "type": 10001, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionListPage";
    }
}
registerNamedRoute(() => new TransactionListPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransferPage", pageFullPath: "entry/src/main/ets/pages/TransferPage", integratedHsp: "false", moduleType: "followWithHap" });
