{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "49d9a976-ec41-4014-884e-f8b2b464cd7b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057809247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e8955cf-d056-4538-9438-e010c1259f57", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14088288655200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b8ea9a-0c9d-49ff-b504-de132be87542", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14088288892500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b639e877-09d5-43d5-b07f-65a15f183374", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089603279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089623462600, "endTime": 14090113703100}, "additional": {"children": ["166b9ecb-2f4e-4fde-ab91-67fde60a3dce", "de0e4632-81c4-4702-943f-e34c1c085b4f", "221c13de-b106-4283-a02f-9fa4a4ae9266", "95cd9016-6b37-4379-8d0a-39f5cf31efa3", "f19cea10-3771-4713-bd4d-e14bbf5c7a2d", "6f339ae9-1182-4d15-96f4-ab51e68631b2", "c6d02a0b-41f3-4e9f-a5b7-258518c27d80"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "166b9ecb-2f4e-4fde-ab91-67fde60a3dce", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089623474600, "endTime": 14089643677400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "36cf3438-5f1b-4906-aca9-d8f7f6fed11e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de0e4632-81c4-4702-943f-e34c1c085b4f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089643735300, "endTime": 14090112314900}, "additional": {"children": ["f010d461-cdc4-4f70-9ff5-9241cd8b4d69", "f4d91d3e-798d-4e3c-a2e6-47f525f966a5", "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "a782c6ae-2998-4e6d-abc7-20529f67cf85", "799db637-1522-471a-909a-890baafce53a", "8d3206db-ce89-4c97-ace5-88588f306212", "c7ac90f2-76bb-419b-9ee0-4e6df8596266", "31bbcde6-1a5e-4157-8b29-500bbf1f5c83", "a7996ee0-9253-4a42-b295-286cb108b23a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "221c13de-b106-4283-a02f-9fa4a4ae9266", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090112347100, "endTime": 14090113682500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "2e80ecbb-d339-485c-be19-85a357d902ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95cd9016-6b37-4379-8d0a-39f5cf31efa3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090113689200, "endTime": 14090113696800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "c974c0e2-bee5-44e4-8b01-cb6048b02229"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f19cea10-3771-4713-bd4d-e14bbf5c7a2d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089630870800, "endTime": 14089631109900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "d64e0cbf-1acd-4680-816e-61f49a0fccec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d64e0cbf-1acd-4680-816e-61f49a0fccec", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089630870800, "endTime": 14089631109900}, "additional": {"logType": "info", "children": [], "durationId": "f19cea10-3771-4713-bd4d-e14bbf5c7a2d", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "6f339ae9-1182-4d15-96f4-ab51e68631b2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089638991500, "endTime": 14089639011700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "a7b7800b-2f77-4c12-aa8a-1001eead4aec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7b7800b-2f77-4c12-aa8a-1001eead4aec", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089638991500, "endTime": 14089639011700}, "additional": {"logType": "info", "children": [], "durationId": "6f339ae9-1182-4d15-96f4-ab51e68631b2", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "3d15a64e-9449-4079-b4af-470d51cef9d8", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089639089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbac2746-b0fa-45cd-b3a1-59037ec8a22a", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089642969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cf3438-5f1b-4906-aca9-d8f7f6fed11e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089623474600, "endTime": 14089643677400}, "additional": {"logType": "info", "children": [], "durationId": "166b9ecb-2f4e-4fde-ab91-67fde60a3dce", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "f010d461-cdc4-4f70-9ff5-9241cd8b4d69", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089655963200, "endTime": 14089655977400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "5f3ca5cc-d307-4ff2-ba45-73c435c04606"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4d91d3e-798d-4e3c-a2e6-47f525f966a5", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089655995600, "endTime": 14089660715900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "433e7aab-93cf-4f8b-b6a6-09b3cb9f58bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089660757300, "endTime": 14090019623800}, "additional": {"children": ["528b930d-4aa5-48db-893a-a0bf50fda34b", "b46b7b98-eb9d-4786-9003-840bdfee78d3", "1f09e210-22e4-4cda-b4b7-cf558f79c625"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "f675171f-1e5c-41ef-8e9e-64b8ade37ed0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a782c6ae-2998-4e6d-abc7-20529f67cf85", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090019640200, "endTime": 14090066059700}, "additional": {"children": ["39cbd591-b88a-4a51-b960-6c8109d110dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "654266d7-c6aa-45d3-a1a0-7e0bd50053a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "799db637-1522-471a-909a-890baafce53a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090066067900, "endTime": 14090089681100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "173d15f5-776e-4d21-b49f-e2095a2bb084"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d3206db-ce89-4c97-ace5-88588f306212", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090090807700, "endTime": 14090099945200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "e1e195c8-8346-49db-888d-71b146c200df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7ac90f2-76bb-419b-9ee0-4e6df8596266", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090099974700, "endTime": 14090112124000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "497499ef-2afa-4c48-b0d7-a0ac9ade3fcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31bbcde6-1a5e-4157-8b29-500bbf1f5c83", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090112159500, "endTime": 14090112300100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "7d745738-4c77-45e8-980d-edb5e20f4e83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f3ca5cc-d307-4ff2-ba45-73c435c04606", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089655963200, "endTime": 14089655977400}, "additional": {"logType": "info", "children": [], "durationId": "f010d461-cdc4-4f70-9ff5-9241cd8b4d69", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "433e7aab-93cf-4f8b-b6a6-09b3cb9f58bc", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089655995600, "endTime": 14089660715900}, "additional": {"logType": "info", "children": [], "durationId": "f4d91d3e-798d-4e3c-a2e6-47f525f966a5", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "528b930d-4aa5-48db-893a-a0bf50fda34b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089666504500, "endTime": 14089666785700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "logId": "7cb7afe5-60ec-4d45-9cb4-96810c7f8fff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cb7afe5-60ec-4d45-9cb4-96810c7f8fff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089666504500, "endTime": 14089666785700}, "additional": {"logType": "info", "children": [], "durationId": "528b930d-4aa5-48db-893a-a0bf50fda34b", "parent": "f675171f-1e5c-41ef-8e9e-64b8ade37ed0"}}, {"head": {"id": "b46b7b98-eb9d-4786-9003-840bdfee78d3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089669214500, "endTime": 14090018864200}, "additional": {"children": ["12efe15b-7e2f-4295-b141-2fb9e657015c", "7957f20e-d554-4794-9503-3d93ae09b88a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "logId": "f56f00b2-310a-4566-9a81-3dca070675f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12efe15b-7e2f-4295-b141-2fb9e657015c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089669215500, "endTime": 14089672521400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b46b7b98-eb9d-4786-9003-840bdfee78d3", "logId": "e3eff094-82db-45f9-8f67-9187c<PERSON>abfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7957f20e-d554-4794-9503-3d93ae09b88a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089672539400, "endTime": 14090018851000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b46b7b98-eb9d-4786-9003-840bdfee78d3", "logId": "dcdb6799-3b2c-42e6-9dcc-18dfd8c81894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c619d826-1032-4d38-a1b2-4a1b9c251ffd", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089669221400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93fe1eff-88e1-4484-9ee3-f779b54b10cc", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089672375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3eff094-82db-45f9-8f67-9187c<PERSON>abfa", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089669215500, "endTime": 14089672521400}, "additional": {"logType": "info", "children": [], "durationId": "12efe15b-7e2f-4295-b141-2fb9e657015c", "parent": "f56f00b2-310a-4566-9a81-3dca070675f0"}}, {"head": {"id": "71bd3556-d8c8-4af5-ac06-086545a5aab6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089672553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004b8058-478c-4a38-8fa4-cafa0e303843", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089720754300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "770ccee6-5ab8-4230-92b1-5346faa44922", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089720899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37424e14-88fd-4793-b676-07bf69f11a70", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089721056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dacdb6ff-7b5b-4f5b-8b03-3830129dcc27", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089721180700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c63e9557-94f1-4aa6-b706-db1197671cc6", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089723227400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce19429-3f9e-4bfd-be67-9dd06e68e825", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089727282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4188dd35-4047-4bdc-984d-d83d6b5e4204", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089848265800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a9c70eb-c430-442f-9283-073419e398a5", "name": "Sdk init in 263 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089991771900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ead3795-7989-4b57-a9e0-28af6b688a4c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089991920800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 45}, "markType": "other"}}, {"head": {"id": "8589486a-153e-472d-8d85-b2ce42001fc6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089991936000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 45}, "markType": "other"}}, {"head": {"id": "88bc1a06-8ee8-4fdd-9b32-b666691011f7", "name": "Project task initialization takes 26 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090018563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d96fc93-37fd-4f1b-ae03-18d1427a5d9c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090018694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15250c57-708d-4583-a5c3-a19f96bb7876", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090018756800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "285e8e61-f186-4424-9745-217ed5121457", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090018805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdb6799-3b2c-42e6-9dcc-18dfd8c81894", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089672539400, "endTime": 14090018851000}, "additional": {"logType": "info", "children": [], "durationId": "7957f20e-d554-4794-9503-3d93ae09b88a", "parent": "f56f00b2-310a-4566-9a81-3dca070675f0"}}, {"head": {"id": "f56f00b2-310a-4566-9a81-3dca070675f0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089669214500, "endTime": 14090018864200}, "additional": {"logType": "info", "children": ["e3eff094-82db-45f9-8f67-9187c<PERSON>abfa", "dcdb6799-3b2c-42e6-9dcc-18dfd8c81894"], "durationId": "b46b7b98-eb9d-4786-9003-840bdfee78d3", "parent": "f675171f-1e5c-41ef-8e9e-64b8ade37ed0"}}, {"head": {"id": "1f09e210-22e4-4cda-b4b7-cf558f79c625", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090019593900, "endTime": 14090019608200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "logId": "b045e38e-f0d6-4b9e-afc6-e8d7c8e1ff13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b045e38e-f0d6-4b9e-afc6-e8d7c8e1ff13", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090019593900, "endTime": 14090019608200}, "additional": {"logType": "info", "children": [], "durationId": "1f09e210-22e4-4cda-b4b7-cf558f79c625", "parent": "f675171f-1e5c-41ef-8e9e-64b8ade37ed0"}}, {"head": {"id": "f675171f-1e5c-41ef-8e9e-64b8ade37ed0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089660757300, "endTime": 14090019623800}, "additional": {"logType": "info", "children": ["7cb7afe5-60ec-4d45-9cb4-96810c7f8fff", "f56f00b2-310a-4566-9a81-3dca070675f0", "b045e38e-f0d6-4b9e-afc6-e8d7c8e1ff13"], "durationId": "4f57a8e1-a57a-4b8e-a8e9-5edff76b91e9", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "39cbd591-b88a-4a51-b960-6c8109d110dc", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090020300800, "endTime": 14090066044300}, "additional": {"children": ["386a45e1-ac22-436c-9480-c1ca6acf8b9e", "6f0e60ed-c084-41fd-87ac-e50b11197cb3", "362edaff-9e49-411a-ae90-4a86a45d666e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a782c6ae-2998-4e6d-abc7-20529f67cf85", "logId": "21b018e9-8cbe-4778-8940-82c806ba451f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "386a45e1-ac22-436c-9480-c1ca6acf8b9e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090038092700, "endTime": 14090038126000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39cbd591-b88a-4a51-b960-6c8109d110dc", "logId": "7dc97475-0fb6-4691-b513-7a4a3c677e4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dc97475-0fb6-4691-b513-7a4a3c677e4f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090038092700, "endTime": 14090038126000}, "additional": {"logType": "info", "children": [], "durationId": "386a45e1-ac22-436c-9480-c1ca6acf8b9e", "parent": "21b018e9-8cbe-4778-8940-82c806ba451f"}}, {"head": {"id": "6f0e60ed-c084-41fd-87ac-e50b11197cb3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090042284700, "endTime": 14090062660400}, "additional": {"children": ["20768f58-35a9-48cc-8c6c-840fc4c0061a", "9655efab-26a0-4670-813c-e240cc329fad"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39cbd591-b88a-4a51-b960-6c8109d110dc", "logId": "a8defb2f-b469-40b7-8465-4ed91e8ca227"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20768f58-35a9-48cc-8c6c-840fc4c0061a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090042286800, "endTime": 14090046514600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0e60ed-c084-41fd-87ac-e50b11197cb3", "logId": "9b7e58a0-893f-46d8-b2c5-8f8d9b3e2169"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9655efab-26a0-4670-813c-e240cc329fad", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090046541300, "endTime": 14090062645600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0e60ed-c084-41fd-87ac-e50b11197cb3", "logId": "ed74874c-bfed-4061-836e-c2de71c9fdd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd901c33-0578-417d-afc5-88a381161bf9", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090042296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650f4f29-7447-45cb-9036-ef8b8df1507c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090046328900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b7e58a0-893f-46d8-b2c5-8f8d9b3e2169", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090042286800, "endTime": 14090046514600}, "additional": {"logType": "info", "children": [], "durationId": "20768f58-35a9-48cc-8c6c-840fc4c0061a", "parent": "a8defb2f-b469-40b7-8465-4ed91e8ca227"}}, {"head": {"id": "78815bdf-10d4-4ec5-a3a1-feb887a92bae", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090046559000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4173b463-0076-4505-9e59-51397c25fddb", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090055408600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "853a4169-c470-4a23-8127-90ad897e953e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090055557600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5097cdd-1c8f-40de-93e9-7af5236567f6", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090055828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea52093e-dcf3-4728-83b1-45704db2ff8d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090056065600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f309b0-6c51-48f5-9e6f-20bfb9e0002b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090056179900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34877bc-1813-4aa0-a168-5aa3c4f963eb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090056280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979ee94d-ae12-4230-bcbe-fd18af24fcd1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090056514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "913ef13c-be75-4e0c-b2ae-43c1c2ed45e0", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090062237200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8fef4c-1642-4e2a-8865-33d8291d0137", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090062448100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e4c06ef-71c8-499e-aff8-0aa18187d09b", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090062539800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b7ec3e-48f0-483f-99d9-a90ca2e0a2e7", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090062594600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed74874c-bfed-4061-836e-c2de71c9fdd3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090046541300, "endTime": 14090062645600}, "additional": {"logType": "info", "children": [], "durationId": "9655efab-26a0-4670-813c-e240cc329fad", "parent": "a8defb2f-b469-40b7-8465-4ed91e8ca227"}}, {"head": {"id": "a8defb2f-b469-40b7-8465-4ed91e8ca227", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090042284700, "endTime": 14090062660400}, "additional": {"logType": "info", "children": ["9b7e58a0-893f-46d8-b2c5-8f8d9b3e2169", "ed74874c-bfed-4061-836e-c2de71c9fdd3"], "durationId": "6f0e60ed-c084-41fd-87ac-e50b11197cb3", "parent": "21b018e9-8cbe-4778-8940-82c806ba451f"}}, {"head": {"id": "362edaff-9e49-411a-ae90-4a86a45d666e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090066013200, "endTime": 14090066027100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39cbd591-b88a-4a51-b960-6c8109d110dc", "logId": "13d96f53-5a29-4ef8-b779-800231a05cad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13d96f53-5a29-4ef8-b779-800231a05cad", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090066013200, "endTime": 14090066027100}, "additional": {"logType": "info", "children": [], "durationId": "362edaff-9e49-411a-ae90-4a86a45d666e", "parent": "21b018e9-8cbe-4778-8940-82c806ba451f"}}, {"head": {"id": "21b018e9-8cbe-4778-8940-82c806ba451f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090020300800, "endTime": 14090066044300}, "additional": {"logType": "info", "children": ["7dc97475-0fb6-4691-b513-7a4a3c677e4f", "a8defb2f-b469-40b7-8465-4ed91e8ca227", "13d96f53-5a29-4ef8-b779-800231a05cad"], "durationId": "39cbd591-b88a-4a51-b960-6c8109d110dc", "parent": "654266d7-c6aa-45d3-a1a0-7e0bd50053a5"}}, {"head": {"id": "654266d7-c6aa-45d3-a1a0-7e0bd50053a5", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090019640200, "endTime": 14090066059700}, "additional": {"logType": "info", "children": ["21b018e9-8cbe-4778-8940-82c806ba451f"], "durationId": "a782c6ae-2998-4e6d-abc7-20529f67cf85", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "f1828a66-54a2-4f31-814a-9d5f9f6885a3", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090089131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d7cab50-b3c6-473e-b4c3-17c9ceb75628", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090089567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173d15f5-776e-4d21-b49f-e2095a2bb084", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090066067900, "endTime": 14090089681100}, "additional": {"logType": "info", "children": [], "durationId": "799db637-1522-471a-909a-890baafce53a", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "a7996ee0-9253-4a42-b295-286cb108b23a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090090578600, "endTime": 14090090789200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de0e4632-81c4-4702-943f-e34c1c085b4f", "logId": "4815c721-a2ab-4b55-b8d8-94dcd1346b9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f28f33d9-75f3-4968-a997-289e4b89ba76", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090090609800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4815c721-a2ab-4b55-b8d8-94dcd1346b9a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090090578600, "endTime": 14090090789200}, "additional": {"logType": "info", "children": [], "durationId": "a7996ee0-9253-4a42-b295-286cb108b23a", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "f81303e2-1047-41b0-99de-96e22d4781b7", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090092570800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b801caf-bebe-4883-873e-37748c7f0fb6", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090098915300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1e195c8-8346-49db-888d-71b146c200df", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090090807700, "endTime": 14090099945200}, "additional": {"logType": "info", "children": [], "durationId": "8d3206db-ce89-4c97-ace5-88588f306212", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "339ca069-606f-4771-8850-f9615c4ce05e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090099995900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4be4b3ac-0d6c-4db8-bdfb-1c50589611ef", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090106005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c403225-d5e9-4aea-bfbd-4dbab0999a5e", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090106124000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31183a41-4a0f-4b39-8f62-015bdc4717e2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090106355500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8dbf035-5b70-47c3-a8cb-71ba69be2e6a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090108624900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec32c812-638e-42b4-b69e-4ff48130dc62", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090108710800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497499ef-2afa-4c48-b0d7-a0ac9ade3fcd", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090099974700, "endTime": 14090112124000}, "additional": {"logType": "info", "children": [], "durationId": "c7ac90f2-76bb-419b-9ee0-4e6df8596266", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "aa2d9eec-4d2c-4be6-a04a-ba93f73d98bf", "name": "Configuration phase cost:457 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090112191300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d745738-4c77-45e8-980d-edb5e20f4e83", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090112159500, "endTime": 14090112300100}, "additional": {"logType": "info", "children": [], "durationId": "31bbcde6-1a5e-4157-8b29-500bbf1f5c83", "parent": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64"}}, {"head": {"id": "872afa3e-357b-4a7f-a2f4-8e97a20a7f64", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089643735300, "endTime": 14090112314900}, "additional": {"logType": "info", "children": ["5f3ca5cc-d307-4ff2-ba45-73c435c04606", "433e7aab-93cf-4f8b-b6a6-09b3cb9f58bc", "f675171f-1e5c-41ef-8e9e-64b8ade37ed0", "654266d7-c6aa-45d3-a1a0-7e0bd50053a5", "173d15f5-776e-4d21-b49f-e2095a2bb084", "e1e195c8-8346-49db-888d-71b146c200df", "497499ef-2afa-4c48-b0d7-a0ac9ade3fcd", "7d745738-4c77-45e8-980d-edb5e20f4e83", "4815c721-a2ab-4b55-b8d8-94dcd1346b9a"], "durationId": "de0e4632-81c4-4702-943f-e34c1c085b4f", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "c6d02a0b-41f3-4e9f-a5b7-258518c27d80", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090113655700, "endTime": 14090113668700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6e1438-0f9c-4296-8f75-dde716a31fb2", "logId": "765dc08b-015d-41b7-84ca-6e4cfa460909"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "765dc08b-015d-41b7-84ca-6e4cfa460909", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090113655700, "endTime": 14090113668700}, "additional": {"logType": "info", "children": [], "durationId": "c6d02a0b-41f3-4e9f-a5b7-258518c27d80", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "2e80ecbb-d339-485c-be19-85a357d902ef", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090112347100, "endTime": 14090113682500}, "additional": {"logType": "info", "children": [], "durationId": "221c13de-b106-4283-a02f-9fa4a4ae9266", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "c974c0e2-bee5-44e4-8b01-cb6048b02229", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090113689200, "endTime": 14090113696800}, "additional": {"logType": "info", "children": [], "durationId": "95cd9016-6b37-4379-8d0a-39f5cf31efa3", "parent": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d"}}, {"head": {"id": "6a3fcd68-06c0-48fc-995e-ead1d6fea12d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089623462600, "endTime": 14090113703100}, "additional": {"logType": "info", "children": ["36cf3438-5f1b-4906-aca9-d8f7f6fed11e", "872afa3e-357b-4a7f-a2f4-8e97a20a7f64", "2e80ecbb-d339-485c-be19-85a357d902ef", "c974c0e2-bee5-44e4-8b01-cb6048b02229", "d64e0cbf-1acd-4680-816e-61f49a0fccec", "a7b7800b-2f77-4c12-aa8a-1001eead4aec", "765dc08b-015d-41b7-84ca-6e4cfa460909"], "durationId": "8f6e1438-0f9c-4296-8f75-dde716a31fb2"}}, {"head": {"id": "070e4cdf-e5e3-4c56-a51f-c3aa8d6115c2", "name": "Configuration task cost before running: 507 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090113830700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "799a3c78-a335-4608-84e0-2cd50b82fb5c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090118720700, "endTime": 14090138381300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b39f6675-d2c1-4b20-92f5-2f6b17524643", "logId": "054dd4fd-9612-45b5-bc7f-e34fb2495458"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b39f6675-d2c1-4b20-92f5-2f6b17524643", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090115304000}, "additional": {"logType": "detail", "children": [], "durationId": "799a3c78-a335-4608-84e0-2cd50b82fb5c"}}, {"head": {"id": "43bd4c9a-2685-403f-a994-24b6b5193021", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090115786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd42ea7-f54d-425e-8f1d-f688d26fab13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090115875300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dad9f73-d7f3-48f4-bbc2-f53bc59683c7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090118733000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dcb1943-f3f0-4b08-81d1-fcf38d180195", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090126973900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9eec2f-8515-4714-8ac0-f80faa826003", "name": "entry : default@PreBuild cost memory 0.2616119384765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090138269800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054dd4fd-9612-45b5-bc7f-e34fb2495458", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090118720700, "endTime": 14090138381300}, "additional": {"logType": "info", "children": [], "durationId": "799a3c78-a335-4608-84e0-2cd50b82fb5c"}}, {"head": {"id": "0ff7bd21-49ff-46ed-a06b-5c1dbca0f7d2", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090144682500, "endTime": 14090147662500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "990220f8-2c2a-4f26-bb34-3f6434b3d148", "logId": "9e54971f-0956-4308-ada2-a9cabd19a202"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "990220f8-2c2a-4f26-bb34-3f6434b3d148", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090142921600}, "additional": {"logType": "detail", "children": [], "durationId": "0ff7bd21-49ff-46ed-a06b-5c1dbca0f7d2"}}, {"head": {"id": "aea7c3a6-fe90-410a-8e73-053611cb3f55", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090143566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f511a45-9a1f-49d1-b021-970c4e1528c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090143677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3410e56-f133-4015-b49b-cdb58210f1a3", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090144700800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36254e7e-0ca7-4ae3-9b7d-57287175c80a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090147422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3c5ffb-6969-45c0-a2e4-b7eccac0b95d", "name": "entry : default@MergeProfile cost memory 0.10677337646484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090147559500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e54971f-0956-4308-ada2-a9cabd19a202", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090144682500, "endTime": 14090147662500}, "additional": {"logType": "info", "children": [], "durationId": "0ff7bd21-49ff-46ed-a06b-5c1dbca0f7d2"}}, {"head": {"id": "d224883b-d235-4c5f-a4e8-e0ff89e30b4c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090151869700, "endTime": 14090155041900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5a58b1f9-bb41-407b-90b2-63464b5dc2a0", "logId": "babd851e-cee6-4887-ae18-bfdff3990388"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a58b1f9-bb41-407b-90b2-63464b5dc2a0", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090150006800}, "additional": {"logType": "detail", "children": [], "durationId": "d224883b-d235-4c5f-a4e8-e0ff89e30b4c"}}, {"head": {"id": "61a23627-ead5-4f50-92eb-a2addfb94e61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090150665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a49ca20d-478f-406f-81a4-d5335003c2a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090150796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decc9bb3-a1c6-4fc2-9764-0941792e255f", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090151883400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8e4256b-d5bd-4f93-8ca2-ce230b3dee4e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090153112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2caa48d1-4a3d-4ed2-86d6-529fc4cf8259", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090154816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc803318-0682-4602-8e08-32bb79f512cb", "name": "entry : default@CreateBuildProfile cost memory 0.0959625244140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090154940400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "babd851e-cee6-4887-ae18-bfdff3990388", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090151869700, "endTime": 14090155041900}, "additional": {"logType": "info", "children": [], "durationId": "d224883b-d235-4c5f-a4e8-e0ff89e30b4c"}}, {"head": {"id": "ae70b502-1f26-43a9-82d4-bb05547eb16f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090158422100, "endTime": 14090159310100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8be7afb5-7ca6-44b8-baf3-2873730baf36", "logId": "7b99e53a-1268-4089-bd99-5db952f856e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8be7afb5-7ca6-44b8-baf3-2873730baf36", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090157027800}, "additional": {"logType": "detail", "children": [], "durationId": "ae70b502-1f26-43a9-82d4-bb05547eb16f"}}, {"head": {"id": "3dff7344-b6ec-4725-8a77-e675ed35b316", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090157533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf58f22-7feb-4dfc-866d-57b404f551fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090157640600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318118c5-29e1-4150-8062-e81308075c66", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090158432200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cae5faef-a76d-42e0-965b-5f3ff4029239", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090159014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a942d19a-461d-490e-9595-6ad23d4f000d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090159083400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cfdb80-7db9-46f4-b9b6-a8459c284e47", "name": "entry : default@PreCheckSyscap cost memory -1.6539688110351562", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090159166800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f48c575-a9cc-40bd-8b5c-9afcb663fd0c", "name": "runTaskFromQueue task cost before running: 553 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090159253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b99e53a-1268-4089-bd99-5db952f856e9", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090158422100, "endTime": 14090159310100, "totalTime": 810600}, "additional": {"logType": "info", "children": [], "durationId": "ae70b502-1f26-43a9-82d4-bb05547eb16f"}}, {"head": {"id": "50edb538-5f22-40dc-9e18-9c4188e62455", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090168525700, "endTime": 14090169515600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2e7e3ea8-b46a-4b27-87af-8accf2bd841a", "logId": "f27b2395-90c6-4941-85e3-dd40dd0c59e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e7e3ea8-b46a-4b27-87af-8accf2bd841a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090161188600}, "additional": {"logType": "detail", "children": [], "durationId": "50edb538-5f22-40dc-9e18-9c4188e62455"}}, {"head": {"id": "ae77d1a2-6363-4dcc-b087-d93be60a71e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090161821800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5f01aa-f850-487b-b510-e1fef71a958e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090161951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b32140f-3bc4-4b9c-a69e-11efb09885ae", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090168541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d72c09-e33e-4762-858e-02562bed046e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090168730400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc9cc6e-c8df-42dc-93d3-1cff15dc7837", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090169350700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "492b5526-be5c-40d4-998f-dc23f382a3ea", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06520843505859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090169444700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27b2395-90c6-4941-85e3-dd40dd0c59e5", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090168525700, "endTime": 14090169515600}, "additional": {"logType": "info", "children": [], "durationId": "50edb538-5f22-40dc-9e18-9c4188e62455"}}, {"head": {"id": "e6481abb-f12c-4b7c-a3e7-00a76cbe1232", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090174319800, "endTime": 14090175639600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b42ab35e-909c-405f-be7e-24864879457c", "logId": "5512a536-89ad-4226-bb2f-c7deb3ac09c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b42ab35e-909c-405f-be7e-24864879457c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090171416100}, "additional": {"logType": "detail", "children": [], "durationId": "e6481abb-f12c-4b7c-a3e7-00a76cbe1232"}}, {"head": {"id": "adcf007a-72d8-44bb-a4d1-2771cb541b13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090172250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba5f7f9-a785-42c2-b3bc-dd8f86a91e94", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090172480800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0baa647c-1c23-457d-9605-f3e86824ff6a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090174336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5ecf1a-2e6d-49d2-9027-47ae4556f7d3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090175452100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b3d165-36c3-45b8-8601-3fb9fbb61dec", "name": "entry : default@ProcessProfile cost memory 0.054656982421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090175562200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5512a536-89ad-4226-bb2f-c7deb3ac09c6", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090174319800, "endTime": 14090175639600}, "additional": {"logType": "info", "children": [], "durationId": "e6481abb-f12c-4b7c-a3e7-00a76cbe1232"}}, {"head": {"id": "bd760536-9f37-4f45-8f44-daffa795f2cf", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090179986200, "endTime": 14090185898500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c0feac3-90cf-4497-aa59-c739fe96a39e", "logId": "a236853a-cf83-4462-b505-340f4633abe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c0feac3-90cf-4497-aa59-c739fe96a39e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090177435900}, "additional": {"logType": "detail", "children": [], "durationId": "bd760536-9f37-4f45-8f44-daffa795f2cf"}}, {"head": {"id": "7ec59e76-cdba-4f99-b5e0-511ccf2cfd59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090178034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff98974-6cb8-456f-b549-d642cdf1f94e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090178152300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffeb37bb-022b-4e08-9df1-93a0d07303fb", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090179998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423bd81c-46de-4211-b7c4-ee984b2955af", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090185700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64602601-24bf-4488-b574-947b9b58dd43", "name": "entry : default@ProcessRouterMap cost memory 0.18587493896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090185820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a236853a-cf83-4462-b505-340f4633abe5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090179986200, "endTime": 14090185898500}, "additional": {"logType": "info", "children": [], "durationId": "bd760536-9f37-4f45-8f44-daffa795f2cf"}}, {"head": {"id": "fc32ebec-5bff-4534-a69a-b41c91d3e611", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090194336500, "endTime": 14090197790700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dc26a2df-ec2b-40d3-b519-5032af9e10e1", "logId": "fa7144b0-14fc-4e40-af5b-241d4892c4cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc26a2df-ec2b-40d3-b519-5032af9e10e1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090188792600}, "additional": {"logType": "detail", "children": [], "durationId": "fc32ebec-5bff-4534-a69a-b41c91d3e611"}}, {"head": {"id": "687350e2-568d-4888-a8d1-3761772f56d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090189313600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b89797d-bc39-490d-afd9-4e682d78a381", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090189416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dddd77d9-e45c-4228-bc73-eeb25a0d0353", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090190470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527046d5-6600-428f-a7b5-3776aa3bc0e7", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090195993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4de65d7-4d67-47e0-8ec1-7de45525bf3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090196170700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac588eee-f926-4670-bd46-972090e33b04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090196245500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be3a9aa-f1fa-4c38-896a-e738c11e0bad", "name": "entry : default@PreviewProcessResource cost memory 0.0682220458984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090196342500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d255e6b-886e-4183-95bc-e0c42584b6f2", "name": "runTaskFromQueue task cost before running: 591 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090197681800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7144b0-14fc-4e40-af5b-241d4892c4cc", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090194336500, "endTime": 14090197790700, "totalTime": 2090200}, "additional": {"logType": "info", "children": [], "durationId": "fc32ebec-5bff-4534-a69a-b41c91d3e611"}}, {"head": {"id": "3059a034-60ad-4c47-bbca-d24ccbd54053", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090221226700, "endTime": 14090243222800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ded5f5a8-0f58-4c7c-9a5b-f71281843d98", "logId": "48b9e71a-4725-4976-ad54-64dc8dae7cd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ded5f5a8-0f58-4c7c-9a5b-f71281843d98", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090215411800}, "additional": {"logType": "detail", "children": [], "durationId": "3059a034-60ad-4c47-bbca-d24ccbd54053"}}, {"head": {"id": "728e3099-c13c-467e-814a-3fbd472d251d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090215952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712a3635-d606-4349-a0dc-19d8af6d9edb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090216127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac831fd5-4cd5-4b53-b614-49ca7b94a22d", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090221251200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66352518-4e57-4070-9fbc-b1208916966f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090242996600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c62f7e8-a8d8-424c-965a-9dd829c87bf4", "name": "entry : default@GenerateLoaderJson cost memory 0.699188232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090243139500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b9e71a-4725-4976-ad54-64dc8dae7cd6", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090221226700, "endTime": 14090243222800}, "additional": {"logType": "info", "children": [], "durationId": "3059a034-60ad-4c47-bbca-d24ccbd54053"}}, {"head": {"id": "c5696045-7e91-4784-a751-c7e07e287bfb", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090255056900, "endTime": 14090291281500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "180f9199-79bf-4d96-84bd-b70032c8ef68", "logId": "af3a4572-3b3b-4f9d-860c-d40b22585ff8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "180f9199-79bf-4d96-84bd-b70032c8ef68", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090251053300}, "additional": {"logType": "detail", "children": [], "durationId": "c5696045-7e91-4784-a751-c7e07e287bfb"}}, {"head": {"id": "6169b47d-59cb-4f2b-a633-93cb34581cdb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090251572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79564b98-67f9-4d0e-8b5c-4c5ca3c9d146", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090251695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80447d0c-a0da-4c7a-8ea6-12f898a6628a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090252718100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e84e3a1-b76f-4f1f-9380-5c3051d67f24", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090255080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0427fa7-025a-4f8c-8c59-4715d8085362", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090277183700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a1fcfa-1d46-441e-8add-1ed3a15160e6", "name": "entry : default@PreviewCompileResource cost memory -1.0556869506835938", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090277345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3a4572-3b3b-4f9d-860c-d40b22585ff8", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090255056900, "endTime": 14090291281500}, "additional": {"logType": "info", "children": [], "durationId": "c5696045-7e91-4784-a751-c7e07e287bfb"}}, {"head": {"id": "0e3a8f16-d9d0-43b9-be7f-e810d2bc9770", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296133000, "endTime": 14090296681100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4e4cfc75-01b5-435a-b8a6-643c40c446cf", "logId": "4006f1c1-c20b-4571-aaca-2da64cc3b108"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e4cfc75-01b5-435a-b8a6-643c40c446cf", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090295014100}, "additional": {"logType": "detail", "children": [], "durationId": "0e3a8f16-d9d0-43b9-be7f-e810d2bc9770"}}, {"head": {"id": "89a111bd-5350-486b-981f-c4d25a91a1fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090295853000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8338fc0-179c-41c1-97e7-561e5d48460a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296001500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef534d6a-a42c-4de0-aab0-e0d18db4467c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "880b435d-3d3b-4918-b90c-a990fd62eafa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296272900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc6b182-fe6e-4895-a3a4-be6dc93eb4ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac968a44-1091-49d3-bc10-79cb0a9d44c9", "name": "entry : default@PreviewHookCompileResource cost memory 0.03878021240234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296457900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91222600-4aec-4050-937c-ae12d650090b", "name": "runTaskFromQueue task cost before running: 690 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4006f1c1-c20b-4571-aaca-2da64cc3b108", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090296133000, "endTime": 14090296681100, "totalTime": 429600}, "additional": {"logType": "info", "children": [], "durationId": "0e3a8f16-d9d0-43b9-be7f-e810d2bc9770"}}, {"head": {"id": "4564d813-c1ff-448e-af39-7148c7a3926f", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090301039400, "endTime": 14090303734700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "f512424c-2316-4426-8dce-2b9d2e0674a3", "logId": "aadf57df-2844-4625-923f-3e8f4d4d4a01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f512424c-2316-4426-8dce-2b9d2e0674a3", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090299355700}, "additional": {"logType": "detail", "children": [], "durationId": "4564d813-c1ff-448e-af39-7148c7a3926f"}}, {"head": {"id": "56d5f774-32a9-45d9-9b9e-686ac1aea3b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090300104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c5de44-2068-4618-bd48-b083f326a104", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090300248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d368cfed-58c8-491b-93b3-a06779ca8629", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090301050800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "418ed95a-0f82-4037-afb6-a936a653e45a", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090303523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd5fc90-21b9-49ef-ae40-91d6a1387c0e", "name": "entry : default@CopyPreviewProfile cost memory 0.0942535400390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090303649100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aadf57df-2844-4625-923f-3e8f4d4d4a01", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090301039400, "endTime": 14090303734700}, "additional": {"logType": "info", "children": [], "durationId": "4564d813-c1ff-448e-af39-7148c7a3926f"}}, {"head": {"id": "7eff8170-a159-442a-9db3-678ee0ccf1a6", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308188800, "endTime": 14090308612000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7add9677-461c-4948-951d-822f1846b594", "logId": "6204c11e-f0ab-41a5-956d-aa84d9cddfe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7add9677-461c-4948-951d-822f1846b594", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090306635600}, "additional": {"logType": "detail", "children": [], "durationId": "7eff8170-a159-442a-9db3-678ee0ccf1a6"}}, {"head": {"id": "9c560803-56dd-4b47-911d-e6bc84a718df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090307237200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0979634-0dfd-4951-b419-3c6bddb4278b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090307353000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1bf94e-5bb5-450d-93a3-408213d52ee5", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308199900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80d0bea-e666-4def-ae86-2d7df5ebe598", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308308200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e4cccd-2d56-4b1d-b746-a5b1c69b314e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308368400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2b8937-f82c-40cb-acfd-375306fd4874", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f59ff5db-69dc-4d5c-9de3-dfa5107b72ec", "name": "runTaskFromQueue task cost before running: 702 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308550100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6204c11e-f0ab-41a5-956d-aa84d9cddfe8", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090308188800, "endTime": 14090308612000, "totalTime": 341000}, "additional": {"logType": "info", "children": [], "durationId": "7eff8170-a159-442a-9db3-678ee0ccf1a6"}}, {"head": {"id": "df85ec17-3fd4-4e36-b7cf-58552bede747", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310434400, "endTime": 14090310793500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "68fd447d-257f-4f09-9cc3-5d3e1131fdc4", "logId": "cf3c9d72-7484-4fb1-8f10-2681091709e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68fd447d-257f-4f09-9cc3-5d3e1131fdc4", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310352900}, "additional": {"logType": "detail", "children": [], "durationId": "df85ec17-3fd4-4e36-b7cf-58552bede747"}}, {"head": {"id": "5b7f62c0-9e11-4d6d-99d3-9c933ee9d0cf", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310447500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b9cb53-d541-4d80-aea2-806899f44684", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310629700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "389432dc-b9c0-4fff-9b37-30e24a405f34", "name": "runTaskFromQueue task cost before running: 704 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310725400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf3c9d72-7484-4fb1-8f10-2681091709e9", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090310434400, "endTime": 14090310793500, "totalTime": 267100}, "additional": {"logType": "info", "children": [], "durationId": "df85ec17-3fd4-4e36-b7cf-58552bede747"}}, {"head": {"id": "3580d58f-d6bc-428d-ab75-67cf6b7220c8", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090314400400, "endTime": 14090325759900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "33aba1cf-ac96-455c-a289-e06f2ea7d458", "logId": "3f72cbec-c725-4f0f-acc9-0e64c4550e67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33aba1cf-ac96-455c-a289-e06f2ea7d458", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090312995600}, "additional": {"logType": "detail", "children": [], "durationId": "3580d58f-d6bc-428d-ab75-67cf6b7220c8"}}, {"head": {"id": "27c5486b-71bc-4644-bc55-57dc5943ab2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090313545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87fde39-9d06-4fd4-ab18-c60a10f76aa7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090313647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a2ab75f-1e2a-43a8-a76b-4a60fcd97bba", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090314409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4a5413-cb75-4c2a-8d4f-549853d447d6", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090325502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5802e998-13cd-4afe-a29b-0b771e797d3f", "name": "entry : default@PreviewUpdateAssets cost memory 0.10231781005859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090325680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f72cbec-c725-4f0f-acc9-0e64c4550e67", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090314400400, "endTime": 14090325759900}, "additional": {"logType": "info", "children": [], "durationId": "3580d58f-d6bc-428d-ab75-67cf6b7220c8"}}, {"head": {"id": "0d5a53bd-b25c-4a9d-83b5-57b15e99fc2b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090337773500, "endTime": 14109717691400}, "additional": {"children": ["f99dedf6-d756-4f31-9066-4cbee3467df3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "d6409c07-a25f-4dcc-ad86-31ce7be469df", "logId": "5c9ccc9a-3212-49c0-8d34-31d2c54b128c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6409c07-a25f-4dcc-ad86-31ce7be469df", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090329334900}, "additional": {"logType": "detail", "children": [], "durationId": "0d5a53bd-b25c-4a9d-83b5-57b15e99fc2b"}}, {"head": {"id": "57febed5-2585-42fb-90b1-96150ca5329c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090330097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e79a9ad7-a26b-41e5-a281-37bcd3cc3632", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090330294400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e707d9-7d70-401c-a28c-dd5c81d29d67", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090337800700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabfa5e3-54d7-4628-af32-cc441b766af9", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090373617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8559bf8c-d5ea-4aa3-82fd-7c43603e7221", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090373781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99dedf6-d756-4f31-9066-4cbee3467df3", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14090388420800, "endTime": 14109708326000}, "additional": {"children": ["44154789-1a30-4e30-89ce-61e5b2e4f98a", "bc064b70-b9d5-4372-94d6-4d18c22696a6", "15e3aaba-033c-4e12-9bcb-b3c500567449", "c57ccb6c-fcf4-499c-b639-1c4311b742e1", "124f1430-8968-43a9-b275-80af86360b63", "f9db6da8-1ffa-4baf-b2ae-6ebc5e0b6f56"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0d5a53bd-b25c-4a9d-83b5-57b15e99fc2b", "logId": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c85d39f-c12e-4889-9f34-096c6905d269", "name": "entry : default@PreviewArkTS cost memory 1.3818130493164062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090390655500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72644b7a-6ded-4d07-a233-c7e126d4b8e2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14095353442500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44154789-1a30-4e30-89ce-61e5b2e4f98a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14095354660000, "endTime": 14095354679700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "280dce85-15d4-4fcb-97c6-14d0f0709a00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "280dce85-15d4-4fcb-97c6-14d0f0709a00", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14095354660000, "endTime": 14095354679700}, "additional": {"logType": "info", "children": [], "durationId": "44154789-1a30-4e30-89ce-61e5b2e4f98a", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "15cb00b0-0dbd-4e35-b09a-f1e4954f2a3c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109704213600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc064b70-b9d5-4372-94d6-4d18c22696a6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14109706574600, "endTime": 14109706610000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "66ac4c4d-b300-4162-81db-20be5df96644"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ac4c4d-b300-4162-81db-20be5df96644", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109706574600, "endTime": 14109706610000}, "additional": {"logType": "info", "children": [], "durationId": "bc064b70-b9d5-4372-94d6-4d18c22696a6", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "215d5047-da77-4b88-9d83-8b19cd33ce3b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14090388420800, "endTime": 14109708326000}, "additional": {"logType": "info", "children": ["280dce85-15d4-4fcb-97c6-14d0f0709a00", "66ac4c4d-b300-4162-81db-20be5df96644", "792904c9-a82d-40d9-8d02-ef3615277a64", "13a9f657-aef2-40d4-b403-453e58b8d971", "410c2fe6-2b88-4645-84fa-b11e2bc15718", "b8941e59-8ca1-4d16-b4ee-0309dbf5deeb"], "durationId": "f99dedf6-d756-4f31-9066-4cbee3467df3", "parent": "5c9ccc9a-3212-49c0-8d34-31d2c54b128c"}}, {"head": {"id": "15e3aaba-033c-4e12-9bcb-b3c500567449", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14093916599200, "endTime": 14095315945200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "792904c9-a82d-40d9-8d02-ef3615277a64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "792904c9-a82d-40d9-8d02-ef3615277a64", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14093916599200, "endTime": 14095315945200}, "additional": {"logType": "info", "children": [], "durationId": "15e3aaba-033c-4e12-9bcb-b3c500567449", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "c57ccb6c-fcf4-499c-b639-1c4311b742e1", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14095316175000, "endTime": 14095330632200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "13a9f657-aef2-40d4-b403-453e58b8d971"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a9f657-aef2-40d4-b403-453e58b8d971", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14095316175000, "endTime": 14095330632200}, "additional": {"logType": "info", "children": [], "durationId": "c57ccb6c-fcf4-499c-b639-1c4311b742e1", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "124f1430-8968-43a9-b275-80af86360b63", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14095330747100, "endTime": 14095330753100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "410c2fe6-2b88-4645-84fa-b11e2bc15718"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "410c2fe6-2b88-4645-84fa-b11e2bc15718", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14095330747100, "endTime": 14095330753100}, "additional": {"logType": "info", "children": [], "durationId": "124f1430-8968-43a9-b275-80af86360b63", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "f9db6da8-1ffa-4baf-b2ae-6ebc5e0b6f56", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker8", "startTime": 14095330828100, "endTime": 14109704422600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "b8941e59-8ca1-4d16-b4ee-0309dbf5deeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8941e59-8ca1-4d16-b4ee-0309dbf5deeb", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14095330828100, "endTime": 14109704422600}, "additional": {"logType": "info", "children": [], "durationId": "f9db6da8-1ffa-4baf-b2ae-6ebc5e0b6f56", "parent": "215d5047-da77-4b88-9d83-8b19cd33ce3b"}}, {"head": {"id": "5c9ccc9a-3212-49c0-8d34-31d2c54b128c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14090337773500, "endTime": 14109717691400, "totalTime": 19379871800}, "additional": {"logType": "info", "children": ["215d5047-da77-4b88-9d83-8b19cd33ce3b"], "durationId": "0d5a53bd-b25c-4a9d-83b5-57b15e99fc2b"}}, {"head": {"id": "d4657dd1-2415-47c5-905d-2900c59c6aae", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732322000, "endTime": 14109732976000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8f5ec756-4011-435a-b0f1-ff244ac77901", "logId": "4d2d5a3c-1cdb-4f22-becb-bbf35a4142d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f5ec756-4011-435a-b0f1-ff244ac77901", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732215700}, "additional": {"logType": "detail", "children": [], "durationId": "d4657dd1-2415-47c5-905d-2900c59c6aae"}}, {"head": {"id": "6566ad20-718f-4d68-9a80-6b6f9b9df331", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1822fb5a-3136-4526-94f4-66f1df085d61", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec9a8d2-5750-4038-b181-f9c8664d2da6", "name": "runTaskFromQueue task cost before running: 20 s 126 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732829800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2d5a3c-1cdb-4f22-becb-bbf35a4142d1", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109732322000, "endTime": 14109732976000, "totalTime": 462100}, "additional": {"logType": "info", "children": [], "durationId": "d4657dd1-2415-47c5-905d-2900c59c6aae"}}, {"head": {"id": "9d631708-8707-41b6-a86f-b35d325c3dca", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109759035900, "endTime": 14109759079700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6af495-65fd-401c-944e-f7a6795e78b8", "logId": "36e770e4-421b-4504-b342-ba0c58e31e1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36e770e4-421b-4504-b342-ba0c58e31e1d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109759035900, "endTime": 14109759079700}, "additional": {"logType": "info", "children": [], "durationId": "9d631708-8707-41b6-a86f-b35d325c3dca"}}, {"head": {"id": "0900022d-a33e-4404-8def-e060f0e84288", "name": "BUILD SUCCESSFUL in 20 s 152 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109759170900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "87e1b53e-30d7-40a4-aa50-36846e0ed8d3", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14089607219600, "endTime": 14109759713600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 45}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "0b7ba2d6-4c6d-413f-b07b-7042d091932d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109759765500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823057c9-e320-4e4a-8432-e58b45466896", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109759924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d38506-85da-4042-882f-9724d31d609a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bea67fe0-a645-430a-8f69-6e67bcab8e3f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760176700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f5f34d7-f637-4c8b-8cc5-561fa1ca37bc", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760290000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb827866-d373-4a5b-adf5-6de927a0f9f5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760409600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4bb0d54-da68-4f44-a658-abf015a6a54a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760581100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5c24700-837a-4d78-a79e-ec1f295307c5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760751000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a349f5b6-de91-4952-a265-619fdc5b0ce0", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f2fc2b-b916-4683-90d4-4b97d159d4e2", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109760977400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f57f12e-a0f5-4d42-8598-efdb214ca1ee", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109770626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd9891c8-9ccf-4fa8-8130-79ae91b3b0f5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109773632600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d20069d-6fde-4310-81cd-0bfd1e25283c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109774938300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a436c7f-c760-42fa-a4c8-31d1ca2cc4b3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109776118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0c6a6c-22da-4467-834c-8383b4e619d8", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109778653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af64f9ab-8114-4633-93b9-d477308b2345", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109803295500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0180424-2286-4668-bfde-c84e8708afb6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109804044000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3140211-98e5-4678-b5f9-6b6ad5ca431c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109804749200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fcba79-6201-419f-85ff-a37747722a1c", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109805479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e607212-522a-4b38-8a5c-8ec10b47b9ef", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:45 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109806079600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}