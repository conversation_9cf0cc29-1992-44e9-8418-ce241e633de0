import axios from 'axios'
import { ElMessage } from 'element-plus'

// 配置axios默认设置
axios.defaults.baseURL = 'http://localhost:8080'
axios.defaults.withCredentials = true
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === 0 || data.code === 200) {
      return data
    } else {
      ElMessage.error(data.msg || '请求失败')
      return Promise.reject(new Error(data.msg || '请求失败'))
    }
  },
  error => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      let message = '请求失败'
      
      switch (status) {
        case 400:
          message = data?.msg || data?.message || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.msg || data?.message || `请求失败 (${status})`
      }
      
      ElMessage.error(message)
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时')
    } else {
      ElMessage.error('网络错误')
    }
    
    return Promise.reject(error)
  }
)

// 用户相关API
export const userApi = {
  // 登录
  login: (data) => axios.post('/api/user/login', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),
  
  // 获取验证码
  getCaptcha: () => axios.get('/api/user/captcha'),

  // 修改登录密码
  updateLoginPassword: (data) => axios.post('/api/user/updateLoginPassword', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 修改支付密码
  updatePaymentPassword: (data) => axios.post('/api/user/updatePaymentPassword', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 获取用户信息
  getUserInfo: () => axios.get('/api/user/info'),

  // 更新用户信息
  updateUserInfo: (data) => axios.post('/api/user/updateInfo', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 验证支付密码
  verifyPaymentPassword: (data) => axios.post('/api/user/verifyPaymentPassword', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 登出
  logout: () => axios.get('/api/user/logout'),
  
  // 设置支付密码
  setPaymentPassword: (data) => axios.post('/api/user/setPaymentPassword', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),
  
  // 设置支付限额
  setPaymentLimit: (data) => axios.post('/api/user/setPaymentLimit', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 银行卡相关API
export const bankCardApi = {
  // 获取用户所有银行卡
  getAllCards: (userId) => axios.get(`/bank/cards/${userId}`),

  // 获取已绑定银行卡
  getBoundCards: (userId) => axios.get(`/bank/bound-cards/${userId}`),

  // 获取银行卡详情
  getCardDetail: (cardId) => axios.get(`/bank/card/${cardId}`),

  // 添加银行卡
  addCard: (data) => axios.post('/bank/card', data),

  // 绑定银行卡（添加新银行卡）
  bindCard: (data) => axios.post('/bank/bind', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 绑定已存在的银行卡
  bindExistingCard: (cardId) => axios.post(`/bank/bind/${cardId}`),

  // 解绑银行卡
  unbindCard: (cardId) => axios.post(`/bank/unbind/${cardId}`),

  // 修改银行卡
  updateCard: (cardId, data) => axios.put(`/bank/card/${cardId}`, data, {
    headers: { 'Content-Type': 'application/json' }
  }),

  // 删除银行卡
  deleteCard: (cardId) => axios.delete(`/bank/card/${cardId}`)
}

// 银行API (兼容旧版本)
export const bankApi = {
  // 获取银行卡列表
  getCards: (userId) => axios.get(`/bank/cards/${userId}`),

  // 绑定银行卡
  bindCard: (data) => axios.post('/bank/bind', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 解绑银行卡
  unbindCard: (cardId) => axios.post(`/bank/unbind/${cardId}`),

  // 修改银行卡
  updateCard: (cardId, data) => axios.put(`/bank/card/${cardId}`, data),

  // 删除银行卡
  deleteCard: (cardId) => axios.delete(`/bank/card/${cardId}`),

  // 充值
  deposit: (data) => axios.post('/bank/deposit', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 提现
  withdraw: (data) => axios.post('/bank/withdraw', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),

  // 获取余额
  getBalance: (userId) => axios.get(`/bank/balance/${userId}`),

  // 获取交易记录
  getTransactions: (userId) => axios.get(`/bank/transactions/${userId}`),

  // 根据银行卡号查询银行卡信息
  getCardByNumber: (cardNumber) => axios.get(`/bank/card/number/${cardNumber}`)
}

// 交易相关API
export const transactionApi = {
  // 获取所有交易记录
  getAllTransactions: (userId) => axios.get(`/bank/transactions/${userId}`),

  // 按类型获取交易记录
  getTransactionsByType: (userId, type) => axios.get(`/bank/transactions/${userId}/type/${type}`),

  // 按支付方式获取交易记录
  getTransactionsByPaymentMethod: (userId, method) => axios.get(`/bank/transactions/${userId}/method/${method}`),

  // 删除交易记录
  deleteTransaction: (transactionId) => axios.delete(`/bank/transactions/${transactionId}`),

  // 删除重复交易记录
  removeDuplicates: (userId) => axios.delete(`/bank/transactions/${userId}/duplicates`)
}

// 钱包相关API
export const walletApi = {
  // 获取余额
  getBalance: (userId) => axios.get(`/bank/balance/${userId}`),
  
  // 充值
  deposit: (data) => axios.post('/bank/deposit', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),
  
  // 提现
  withdraw: (data) => axios.post('/bank/withdraw', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),
  
  // 转账
  transfer: (data) => axios.post('/bank/transfer', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 支付相关API
export const paymentApi = {
  // 钱包支付
  payWithWallet: (data) => axios.post('/bank/pay/wallet', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }),
  
  // 银行卡支付
  payWithCard: (data) => axios.post('/bank/pay/card', data, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export default axios
