import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { TransactionApi } from '../api/TransactionApi';
import { BankCard } from '../common/types/index';

@Entry
@Component
struct TransferFormPage {
  @State amount: string = '';
  @State targetAccount: string = '';
  @State selectedCardId: number = 0;
  @State password: string = '';
  @State bankCards: BankCard[] = [];
  @State isLoading: boolean = false;
  @State showCardSelector: boolean = false;

  aboutToAppear() {
    this.loadBankCards();
  }

  async loadBankCards() {
    try {
      const userId = 1; // 临时使用固定用户ID
      this.bankCards = await BankCardApi.getCardList(userId, true);
    } catch (error) {
      console.error('加载银行卡失败:', error);
      promptAction.showToast({ message: '加载银行卡失败' });
      // 添加测试数据
      this.bankCards = [
        {
          cardId: 1,
          userId: 1,
          cardNo: '6217000010001234567',
          bankName: '中国银行',
          cardType: 'SAVINGS',
          holderName: '张三',
          isBound: 'BOUND',
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 2,
          userId: 1,
          cardNo: '6228480010001234567',
          bankName: '工商银行',
          cardType: 'SAVINGS',
          holderName: '张三',
          isBound: 'BOUND',
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard
      ];
    }
  }

  async handleTransfer() {
    if (!this.amount || !this.targetAccount || !this.selectedCardId || !this.password) {
      promptAction.showToast({ message: '请填写完整信息' });
      return;
    }

    const transferAmount = parseFloat(this.amount);
    if (transferAmount <= 0) {
      promptAction.showToast({ message: '转账金额必须大于0' });
      return;
    }

    try {
      this.isLoading = true;
      
      // 调用转账API
      const userId = 1;
      await TransactionApi.transfer({
        fromUserId: userId,
        targetAccount: this.targetAccount,
        amount: transferAmount,
        paymentMethod: 'BANK_CARD',
        cardId: this.selectedCardId
      });

      promptAction.showToast({ message: '转账成功' });
      
      // 重置表单
      this.amount = '';
      this.targetAccount = '';
      this.selectedCardId = 0;
      this.password = '';
      
      // 返回上一页
      router.back();
      
    } catch (error) {
      console.error('转账失败:', error);
      promptAction.showToast({ message: '转账失败' });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#667EEA')
          .backgroundColor(Color.Transparent)
          .onClick(() => router.back())

        Text('转账')
          .fontSize(20)
          .fontColor('#1A1A1A')
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60)
      }
      .width('100%')
      .height(60)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      Scroll() {
        Column() {
          // 转账金额
          Column() {
            Text('转账金额')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            TextInput({ placeholder: '请输入转账金额' })
              .fontSize(16)
              .height(50)
              .borderRadius(12)
              .backgroundColor('#F8F9FA')
              .border({ width: 1, color: '#E5E7EB' })
              .onChange((value: string) => {
                this.amount = value;
              })
          }
          .width('100%')
          .margin({ bottom: 24 })

          // 收款账户
          Column() {
            Text('收款账户')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            TextInput({ placeholder: '请输入收款银行卡号' })
              .fontSize(16)
              .height(50)
              .borderRadius(12)
              .backgroundColor('#F8F9FA')
              .border({ width: 1, color: '#E5E7EB' })
              .onChange((value: string) => {
                this.targetAccount = value;
              })
          }
          .width('100%')
          .margin({ bottom: 24 })

          // 付款银行卡选择
          Column() {
            Text('付款银行卡')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            Button(this.getSelectedCardText())
              .fontSize(16)
              .fontColor('#333333')
              .backgroundColor('#F8F9FA')
              .border({ width: 1, color: '#E5E7EB' })
              .borderRadius(12)
              .height(50)
              .width('100%')
              .onClick(() => {
                this.showCardSelector = true;
              })
          }
          .width('100%')
          .margin({ bottom: 24 })

          // 支付密码
          Column() {
            Text('支付密码')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            TextInput({ placeholder: '请输入支付密码' })
              .fontSize(16)
              .height(50)
              .borderRadius(12)
              .backgroundColor('#F8F9FA')
              .border({ width: 1, color: '#E5E7EB' })
              .type(InputType.Password)
              .onChange((value: string) => {
                this.password = value;
              })
          }
          .width('100%')
          .margin({ bottom: 40 })

          // 转账按钮
          Button('确认转账')
            .fontSize(18)
            .fontColor('#FFFFFF')
            .backgroundColor('#667EEA')
            .borderRadius(12)
            .height(50)
            .width('100%')
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleTransfer();
            })
        }
        .padding(20)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .bindSheet($$this.showCardSelector, this.BankCardSelector(), {
      height: 400,
      dragBar: true
    })
  }

  @Builder
  BankCardSelector() {
    Column() {
      Text('选择付款银行卡')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      ForEach(this.bankCards, (card: BankCard) => {
        Row() {
          Column() {
            Text(card.bankName)
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
            
            Text(`****${card.cardNumber.slice(-4)}`)
              .fontSize(14)
              .fontColor('#666666')
              .margin({ top: 4 })
              .alignSelf(ItemAlign.Start)
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)

          if (this.selectedCardId === card.cardId) {
            Text('✓')
              .fontSize(20)
              .fontColor('#667EEA')
          }
        }
        .width('100%')
        .padding(16)
        .backgroundColor(this.selectedCardId === card.cardId ? '#667EEA15' : '#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 8 })
        .onClick(() => {
          this.selectedCardId = card.cardId;
          this.showCardSelector = false;
        })
      })
    }
    .padding(20)
  }

  private getSelectedCardText(): string {
    if (this.selectedCardId === 0) {
      return '请选择付款银行卡';
    }
    
    const selectedCard = this.bankCards.find(card => card.cardId === this.selectedCardId);
    if (selectedCard) {
      return `${selectedCard.bankName} ****${selectedCard.cardNumber.slice(-4)}`;
    }
    
    return '请选择付款银行卡';
  }
}
