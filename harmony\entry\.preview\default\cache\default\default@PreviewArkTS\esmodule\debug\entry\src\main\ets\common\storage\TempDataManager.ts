/**
 * 表单数据接口
 */
export interface FormData {
    cardNo?: string;
    holderName?: string;
    bankName?: string;
    cardType?: string;
}
/**
 * 临时数据类型
 */
export type TempDataValue = string | number | boolean | FormData | object | null | undefined;
/**
 * 数据键接口
 */
export interface DataKeys {
    ADD_BANK_CARD_FORM: string;
    SELECTED_BANK: string;
    SELECTED_CARD_TYPE: string;
}
/**
 * 临时数据管理器
 * 用于在页面间传递临时数据，特别是选择器页面的返回数据
 */
export class TempDataManager {
    private static instance: TempDataManager;
    private tempData: Map<string, TempDataValue> = new Map();
    private constructor() { }
    static getInstance(): TempDataManager {
        if (!TempDataManager.instance) {
            TempDataManager.instance = new TempDataManager();
        }
        return TempDataManager.instance;
    }
    /**
     * 设置临时数据
     */
    setData(key: string, value: TempDataValue): void {
        this.tempData.set(key, value);
    }
    /**
     * 获取临时数据
     */
    getData(key: string): TempDataValue {
        return this.tempData.get(key);
    }
    /**
     * 获取并删除临时数据
     */
    getAndRemoveData(key: string): TempDataValue {
        const data: TempDataValue = this.tempData.get(key);
        this.tempData.delete(key);
        return data;
    }
    /**
     * 删除临时数据
     */
    removeData(key: string): void {
        this.tempData.delete(key);
    }
    /**
     * 清除所有临时数据
     */
    clearAll(): void {
        this.tempData.clear();
    }
}
// 导出单例实例
export const tempDataManager = TempDataManager.getInstance();
// 定义常用的数据键
export const TempDataKeys: DataKeys = {
    ADD_BANK_CARD_FORM: 'add_bank_card_form',
    SELECTED_BANK: 'selected_bank',
    SELECTED_CARD_TYPE: 'selected_card_type'
};
