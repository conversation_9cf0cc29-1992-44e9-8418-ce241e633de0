import { httpClient } from '../common/http/HttpClient';
import {
  Transaction,
  TransactionQueryParams,
  PageResult,
  TransferRequest,
  RechargeRequest,
  WithdrawRequest,
  TransactionResponse
} from '../common/types/index';

/**
 * 交易记录相关API接口
 */
export class TransactionApi {
  
  /**
   * 查询所有交易记录
   */
  static async getTransactionList(userId: number): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}`);
    return response.data;
  }

  /**
   * 按类型查询交易记录
   */
  static async getTransactionsByType(userId: number, type: string): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}/type/${type}`);
    return response.data;
  }

  /**
   * 按支付方式查询交易记录
   */
  static async getTransactionsByPaymentMethod(userId: number, method: string): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}/method/${method}`);
    return response.data;
  }

  /**
   * 转账
   */
  static async transfer(data: TransferRequest): Promise<void> {
    const formData: Record<string, string> = {
      fromUserId: data.fromUserId.toString(),
      targetAccount: data.targetAccount,
      amount: data.amount.toString(),
      paymentMethod: data.paymentMethod || 'WALLET',
      cardId: data.cardId?.toString() || ''
    };
    await httpClient.postForm<void>('/bank/transfer', formData);
  }

  /**
   * 充值（从银行卡充值到钱包）
   */
  static async recharge(data: RechargeRequest): Promise<void> {
    const formData: Record<string, string> = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/deposit', formData);
  }

  /**
   * 提现（从钱包提现到银行卡）
   */
  static async withdraw(data: WithdrawRequest): Promise<void> {
    const formData: Record<string, string> = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/withdraw', formData);
  }

  /**
   * 删除交易记录
   */
  static async deleteTransaction(transactionId: number): Promise<void> {
    await httpClient.delete<void>(`/bank/transactions/${transactionId}`);
  }
}

// 类型定义已移至 common/types/index.ets
