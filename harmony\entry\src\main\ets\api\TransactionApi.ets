import { httpClient } from '../common/http/HttpClient';
import {
  Transaction,
  TransactionQueryParams,
  PageResult,
  TransferRequest,
  RechargeRequest,
  WithdrawRequest,
  TransactionResponse,
  TransferFormData,
  RechargeFormData,
  WithdrawFormData
} from '../common/types/index';

// 支付请求接口
export interface PaymentRequest {
  userId: number;
  amount: number;
  description: string;
  paymentMethod: string;
  cardId?: number;
}

// 支付表单数据接口
export interface PaymentFormData extends Record<string, string> {
  userId: string;
  amount: string;
  description: string;
  paymentMethod: string;
  cardId: string;
}

/**
 * 交易记录相关API接口
 */
export class TransactionApi {
  
  /**
   * 查询所有交易记录
   */
  static async getTransactionList(userId: number): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}`);
    return response.data;
  }

  /**
   * 按类型查询交易记录
   */
  static async getTransactionsByType(userId: number, type: string): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}/type/${type}`);
    return response.data;
  }

  /**
   * 按支付方式查询交易记录
   */
  static async getTransactionsByPaymentMethod(userId: number, method: string): Promise<Transaction[]> {
    const response = await httpClient.get<Transaction[]>(`/bank/transactions/${userId}/method/${method}`);
    return response.data;
  }

  /**
   * 转账
   */
  static async transfer(data: TransferRequest): Promise<void> {
    const formData: TransferFormData = {
      fromUserId: data.fromUserId.toString(),
      targetAccount: data.targetAccount,
      amount: data.amount.toString(),
      paymentMethod: data.paymentMethod || 'WALLET',
      cardId: data.cardId?.toString() || ''
    };
    await httpClient.postForm<void>('/bank/transfer', formData);
  }

  /**
   * 充值（从银行卡充值到钱包）
   */
  static async recharge(data: RechargeRequest): Promise<void> {
    const formData: RechargeFormData = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/deposit', formData);
  }

  /**
   * 提现（从钱包提现到银行卡）
   */
  static async withdraw(data: WithdrawRequest): Promise<void> {
    const formData: WithdrawFormData = {
      userId: data.userId.toString(),
      cardId: data.cardId.toString(),
      amount: data.amount.toString()
    };
    await httpClient.postForm<void>('/bank/withdraw', formData);
  }

  /**
   * 删除交易记录
   */
  static async deleteTransaction(transactionId: number): Promise<void> {
    await httpClient.delete<void>(`/bank/transactions/${transactionId}`);
  }

  /**
   * 支付（创建支付交易记录）
   */
  static async payment(data: PaymentRequest): Promise<void> {
    const formData: PaymentFormData = {
      userId: data.userId.toString(),
      amount: data.amount.toString(),
      description: data.description,
      paymentMethod: data.paymentMethod,
      cardId: data.cardId?.toString() || ''
    };
    await httpClient.postForm<void>('/bank/payment', formData);
  }
}

// 类型定义已移至 common/types/index.ets
