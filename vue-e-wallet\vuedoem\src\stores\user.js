import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {
      id: null,
      username: '',
      realName: '',
      email: '',
      phone: '',
      balance: 0,
      paymentPassword: ''
    },
    isLoggedIn: false,
    token: ''
  }),

  getters: {
    getUserInfo: (state) => state.userInfo,
    getBalance: (state) => state.userInfo.balance,
    username: (state) => state.userInfo.username,
    isAuthenticated: (state) => state.isLoggedIn && !!state.token
  },

  actions: {
    // 登录方法
    login(userData) {
      this.userInfo = { ...this.userInfo, ...userData }
      this.isLoggedIn = true
      this.token = userData.token || ''

      // 保存到本地存储
      if (this.token) {
        localStorage.setItem('token', this.token)
      }
      this.saveToStorage()
    },

    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = { ...this.userInfo, ...userInfo }
    },

    // 设置登录状态
    setLoginStatus(status, token = '') {
      this.isLoggedIn = status
      this.token = token
      if (status) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
    },

    // 更新余额
    updateBalance(balance) {
      this.userInfo.balance = balance
      this.saveToStorage()
    },

    // 登出
    logout() {
      this.userInfo = {
        id: null,
        username: '',
        realName: '',
        email: '',
        phone: '',
        balance: 0,
        paymentPassword: ''
      }
      this.isLoggedIn = false
      this.token = ''
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },

    // 保存到本地存储
    saveToStorage() {
      localStorage.setItem('user', JSON.stringify(this.userInfo))
    },

    // 从本地存储恢复
    restoreFromStorage() {
      try {
        const token = localStorage.getItem('token')
        const userStr = localStorage.getItem('user')
        
        if (token) {
          this.token = token
          this.isLoggedIn = true
        }
        
        if (userStr) {
          const user = JSON.parse(userStr)
          this.userInfo = { ...this.userInfo, ...user }
        }
      } catch (error) {
        console.error('恢复用户信息失败:', error)
      }
    }
  }
})
