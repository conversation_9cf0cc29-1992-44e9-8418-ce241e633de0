import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { TransactionApi } from '../api/TransactionApi';
import { BankCard, TransactionType, PaymentMethod, TransactionStatus, BankCardType, BankCardStatus, WithdrawRequest } from '../common/types/index';

@Entry
@Component
struct WithdrawPage {
  @State withdrawAmount: string = '';
  @State selectedBankCard: string = '';
  @State paymentPassword: string = '';
  @State bankCards: BankCard[] = [];
  @State isLoading: boolean = false;
  @State showBankCardPicker: boolean = false;

  aboutToAppear() {
    this.loadBankCards();
  }

  async loadBankCards() {
    try {
      const response = await BankCardApi.getCardList();
      this.bankCards = response || [];
    } catch (error) {
      console.error('加载银行卡失败:', error);
      // 添加测试数据
      this.bankCards = [
        {
          cardId: 1,
          userId: 1,
          cardNo: '6217000010001234567',
          bankName: '中国银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard,
        {
          cardId: 2,
          userId: 1,
          cardNo: '6228480010001234567',
          bankName: '工商银行',
          cardType: 'SAVINGS' as BankCardType,
          holderName: '张三',
          isBound: BankCardStatus.BOUND,
          createTime: '2024-01-01 00:00:00',
          updateTime: '2024-01-01 00:00:00'
        } as BankCard
      ];
    }
  }

  async handleWithdraw() {
    if (!this.withdrawAmount || !this.selectedBankCard || !this.paymentPassword) {
      promptAction.showToast({
        message: '请填写完整信息',
        duration: 2000
      });
      return;
    }

    if (parseFloat(this.withdrawAmount) <= 0) {
      promptAction.showToast({
        message: '提现金额必须大于0',
        duration: 2000
      });
      return;
    }

    try {
      this.isLoading = true;

      // 调用提现API
      const withdrawData: WithdrawRequest = {
        amount: parseFloat(this.withdrawAmount),
        cardNo: this.selectedBankCard,
        paymentPassword: this.paymentPassword,
        description: '银行卡提现'
      };

      // 调用实际的提现API
      const result = await TransactionApi.withdraw(withdrawData);

      if (result.success) {
        promptAction.showToast({
          message: result.message || '提现成功',
          duration: 2000
        });
      } else {
        throw new Error(result.message || '提现失败');
      }

      // 返回上一页
      router.back();

    } catch (error) {
      console.error('提现失败:', error);
      promptAction.showToast({
        message: '提现失败，请重试',
        duration: 2000
      });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          Text('×')
            .fontSize(24)
            .fontColor('#666666')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('提现')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位
        Text('')
          .width(40)
          .height(40)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#FFFFFF')

      // 表单内容
      Column() {
        // 提现金额
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('提现金额')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入提现金额' })
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .onChange((value: string) => {
              this.withdrawAmount = value;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 到账银行卡
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('到账银行卡')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          Button(this.selectedBankCard || '请选择银行卡')
            .width('100%')
            .height(50)
            .fontSize(14)
            .fontColor(this.selectedBankCard ? '#333333' : '#999999')
            .backgroundColor('#FFFFFF')
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .onClick(() => {
              this.showBankCardPicker = true;
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 支付密码
        Column() {
          Row() {
            Text('*')
              .fontSize(16)
              .fontColor('#FF4444')
              .margin({ right: 4 })
            Text('支付密码')
              .fontSize(16)
              .fontColor('#333333')
          }
          .width('100%')
          .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入支付密码' })
            .type(InputType.Password)
            .width('100%')
            .height(50)
            .fontSize(16)
            .border({ width: 1, color: '#E0E0E0' })
            .borderRadius(8)
            .onChange((value: string) => {
              this.paymentPassword = value;
            })
        }
        .width('100%')
        .margin({ bottom: 40 })

        // 操作按钮
        Row() {
          Button('取消')
            .width(100)
            .height(44)
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .onClick(() => {
              router.back();
            })

          Button(this.isLoading ? '提现中...' : '确认提现')
            .width(120)
            .height(44)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#1976D2')
            .borderRadius(8)
            .enabled(!this.isLoading)
            .onClick(() => {
              this.handleWithdraw();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.End)
        .margin({ top: 20 })
      }
      .layoutWeight(1)
      .padding({ left: 20, right: 20, top: 20 })
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .bindSheet($$this.showBankCardPicker, this.BankCardPickerSheet(), {
      height: 300,
      showClose: true,
      dragBar: true
    })
  }

  @Builder
  BankCardPickerSheet() {
    Column() {
      Text('选择到账银行卡')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 20 })

      List() {
        ForEach(this.bankCards, (card: BankCard) => {
          ListItem() {
            Row() {
              Text('💳')
                .fontSize(24)
                .margin({ right: 12 })

              Column() {
                Text(card.bankName)
                  .fontSize(16)
                  .fontColor('#333333')
                  .alignSelf(ItemAlign.Start)

                Text(`${card.cardType} ****${card.cardNo.slice(-4)}`)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
            }
            .width('100%')
            .padding(16)
            .onClick(() => {
              this.selectedBankCard = `${card.bankName} ****${card.cardNo.slice(-4)}`;
              this.showBankCardPicker = false;
            })
          }
        })
      }
    }
    .padding(20)
  }
}
