<template>
  <div class="transaction-main">
    <h2>全部交易记录</h2>
    
    <!-- 筛选器 -->
    <div class="filter-section">
      <el-radio-group v-model="activeTab" @change="handleTabChange">
        <el-radio-button label="all">全部交易记录</el-radio-button>
        <el-radio-button label="PAYMENT">支付记录</el-radio-button>
        <el-radio-button label="DEPOSIT">充值记录</el-radio-button>
        <el-radio-button label="WITHDRAW">提现记录</el-radio-button>
        <el-radio-button label="TRANSFER">转账记录</el-radio-button>
        <el-radio-button label="RECEIVE">收款记录</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 交易记录表格 -->
    <div class="table-container">
      <el-table 
        :data="filteredTransactions" 
        v-loading="loading" 
        style="width: 100%"
        max-height="500"
      >
        <el-table-column prop="id" label="交易ID" width="120" />
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.transactionType)">
              {{ getTransactionTypeText(row.transactionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="金额" width="120">
          <template #default="{ row }">
            <span :class="getAmountClass(row.transactionType)">
              {{ formatAmount(row.amount, row.transactionType) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="targetAccount" label="对象/商品" width="150" />
        <el-table-column label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="row.paymentMethod === 'WALLET' ? 'success' : 'primary'">
              {{ getPaymentMethodText(row.paymentMethod) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付渠道" width="100">
          <template #default="{ row }">
            <span>{{ getPaymentChannelText(row.paymentChannel) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.transactionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag type="success">成功</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cardInfo" label="银行卡信息" width="150" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { TransactionStorage } from '../utils/transactionStorage.js'

// 响应式数据
const activeTab = ref('all')
const allTransactions = ref([])
const loading = ref(false)

// 过滤后的交易记录
const filteredTransactions = computed(() => {
  if (activeTab.value === 'all') {
    return allTransactions.value
  }
  return allTransactions.value.filter(transaction => 
    transaction.transactionType === activeTab.value
  )
})

// 格式化交易类型
const getTransactionTypeText = (type) => {
  const typeMap = {
    'PAYMENT': '支付',
    'DEPOSIT': '充值', 
    'WITHDRAW': '提现',
    'TRANSFER': '转账',
    'RECEIVE': '收款'
  }
  return typeMap[type] || type
}

// 格式化支付方式
const getPaymentMethodText = (method) => {
  const methodMap = {
    'WALLET': '钱包',
    'BANK_CARD': '银行卡'
  }
  return methodMap[method] || method
}

// 格式化支付渠道
const getPaymentChannelText = (channel) => {
  const channelMap = {
    'MERCHANT': '商户支付',
    'QR_CODE': '扫码支付',
    'NFC': 'NFC支付',
    'TRANSFER': '转账',
    'ATM': 'ATM'
  }
  return channelMap[channel] || channel
}

// 格式化金额
const formatAmount = (amount, type) => {
  const prefix = ['DEPOSIT', 'RECEIVE'].includes(type) ? '+' : '-'
  return `${prefix}¥${Number(amount).toFixed(2)}`
}

// 获取金额样式类
const getAmountClass = (type) => {
  return ['DEPOSIT', 'RECEIVE'].includes(type) ? 'amount-positive' : 'amount-negative'
}

// 获取交易类型标签样式
const getTypeTagType = (type) => {
  const typeStyles = {
    'PAYMENT': 'danger',
    'DEPOSIT': 'success', 
    'WITHDRAW': 'warning',
    'TRANSFER': 'primary',
    'RECEIVE': 'success'
  }
  return typeStyles[type] || 'info'
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  try {
    const date = new Date(timeString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('时间格式化失败:', error)
    return timeString
  }
}

// 获取交易记录
const loadTransactions = () => {
  try {
    loading.value = true
    const transactions = TransactionStorage.getAllTransactions()
    console.log('从存储中获取的交易记录:', transactions)
    
    allTransactions.value = transactions.map(transaction => ({
      id: transaction.id,
      transactionType: transaction.transactionType,
      amount: transaction.amount,
      targetAccount: transaction.targetAccount,
      paymentMethod: transaction.paymentMethod,
      paymentChannel: transaction.paymentChannel,
      transactionTime: transaction.transactionTime,
      cardInfo: transaction.cardInfo || ''
    }))
    
    console.log('交易记录加载完成，总数:', allTransactions.value.length)
  } catch (error) {
    console.error('加载交易记录失败:', error)
    allTransactions.value = []
  } finally {
    loading.value = false
  }
}

// 处理标签页切换
const handleTabChange = (tab) => {
  console.log('切换到标签页:', tab)
}

// 监听交易记录变化
const handleTransactionAdded = (event) => {
  console.log('收到交易添加事件:', event.detail)
  loadTransactions()
}

onMounted(() => {
  // 初始化交易数据
  TransactionStorage.initMockData()
  // 加载交易记录
  loadTransactions()
  
  // 监听自定义交易添加事件
  window.addEventListener('transactionAdded', handleTransactionAdded)
  
  // 定时刷新
  const intervalId = setInterval(() => {
    loadTransactions()
  }, 5000)
  
  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('transactionAdded', handleTransactionAdded)
    clearInterval(intervalId)
  })
})
</script>

<style scoped>
.transaction-main {
  padding: 20px;
}

.filter-section {
  margin: 20px 0;
}

.table-container {
  margin-top: 20px;
}

.amount-positive {
  color: #67c23a;
  font-weight: bold;
}

.amount-negative {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
