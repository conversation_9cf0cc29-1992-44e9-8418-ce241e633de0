{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "8e9ce3b5-6811-4ebd-82e8-222c0a8105d1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556801969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83618232-dcd3-494c-87b4-728ebc4e0966", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323557191729700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ebad53-034e-4005-a524-f2b9a66068b7", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323557192947000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea5edb1-4ec3-472e-97ca-38d0f4329f0a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220286752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220316025900, "endTime": 324220861849900}, "additional": {"children": ["a020564e-f10f-4721-aed3-efb38ad40ff7", "d267294f-6d03-459b-9af1-7fe905ad6f2b", "5e2ce08a-b710-437e-bf1c-ad2829e0fd31", "a62914f2-3b84-4181-a560-085d33504ad4", "03fdf03d-69a1-40e7-a281-528ea5e238b9", "36ad3054-9f42-425b-a680-1000d0a0aa07", "a84e15c5-5d28-4672-9b6c-ebfeb26d69bd"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "815a428f-91f9-4b9d-aecc-7ee200016998"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a020564e-f10f-4721-aed3-efb38ad40ff7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220316029000, "endTime": 324220542604900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "c9c37d91-68ea-4028-9e7d-d18a1e3d05d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220542655700, "endTime": 324220860725900}, "additional": {"children": ["ea802ed3-3295-4a8a-932d-0ce4e8a6c87c", "5f26a8fa-45a8-4bb6-8b93-6b3d19533b1d", "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "d0730d60-c262-409c-b28e-53a027a88e3c", "9ded3f02-e81c-42e9-ac58-583de5bc67cb", "86a3354d-76cf-45b8-b221-a1237e518403", "3518be39-3ae5-4626-abd8-ccee28c0eb6e", "c07aa8a3-a349-4bbf-b255-247475f73fc0", "714fd8e9-f797-427e-ad3c-eeb7b8968ef4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e2ce08a-b710-437e-bf1c-ad2829e0fd31", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220860746200, "endTime": 324220861843200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "04053222-d481-48dd-af7b-df36767cd613"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a62914f2-3b84-4181-a560-085d33504ad4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220861846900, "endTime": 324220861847700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "fc0f0983-f3a0-4cb4-9418-ffed227d387c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03fdf03d-69a1-40e7-a281-528ea5e238b9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220390045100, "endTime": 324220390077800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "0010a3f1-8d3b-4939-bbbe-480d4a28eec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0010a3f1-8d3b-4939-bbbe-480d4a28eec3", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220390045100, "endTime": 324220390077800}, "additional": {"logType": "info", "children": [], "durationId": "03fdf03d-69a1-40e7-a281-528ea5e238b9", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "36ad3054-9f42-425b-a680-1000d0a0aa07", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220395815600, "endTime": 324220395832100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "045f72c7-d079-479f-96d0-a909959a2829"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "045f72c7-d079-479f-96d0-a909959a2829", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220395815600, "endTime": 324220395832100}, "additional": {"logType": "info", "children": [], "durationId": "36ad3054-9f42-425b-a680-1000d0a0aa07", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "832a4001-5532-4104-afda-8e2f6a5e72d8", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220395882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f21b494-eebe-4aff-9c99-52d76db31153", "name": "Cache service initialization finished in 147 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220542173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c37d91-68ea-4028-9e7d-d18a1e3d05d5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220316029000, "endTime": 324220542604900}, "additional": {"logType": "info", "children": [], "durationId": "a020564e-f10f-4721-aed3-efb38ad40ff7", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "ea802ed3-3295-4a8a-932d-0ce4e8a6c87c", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220556062300, "endTime": 324220556071000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "1212bd48-3d06-43e7-a67e-7bc897359592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f26a8fa-45a8-4bb6-8b93-6b3d19533b1d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220556087200, "endTime": 324220565593800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "395c758a-0e88-4e41-b5b6-46b215352e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220565607900, "endTime": 324220791973200}, "additional": {"children": ["5899c438-ebda-4cd6-8779-84ecb8cf9ab6", "d7678ff7-0145-42d2-86a9-7825e93ec7b2", "6d17b7dd-782b-4389-b255-d6925fc7be97"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "84d43310-6f6b-4fe7-b866-0318fa76308a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0730d60-c262-409c-b28e-53a027a88e3c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220791985400, "endTime": 324220814844100}, "additional": {"children": ["b7b510f7-751a-451e-9b8b-f1646bd53310"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "38d29eb6-2e7e-4dc9-8c4c-d106bf8a3737"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ded3f02-e81c-42e9-ac58-583de5bc67cb", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220814854000, "endTime": 324220838864400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "7addcf7f-29d2-4b0d-b8bb-d7b8f35ba903"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86a3354d-76cf-45b8-b221-a1237e518403", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220840097800, "endTime": 324220848209700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "6f464418-8d72-4512-9948-b00cf959ea8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3518be39-3ae5-4626-abd8-ccee28c0eb6e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220848228300, "endTime": 324220860575500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "3715c35c-ba20-4b0d-b0aa-542d956c815a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c07aa8a3-a349-4bbf-b255-247475f73fc0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220860603100, "endTime": 324220860717500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "63604938-6073-42e6-8afe-1f296a15af5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1212bd48-3d06-43e7-a67e-7bc897359592", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220556062300, "endTime": 324220556071000}, "additional": {"logType": "info", "children": [], "durationId": "ea802ed3-3295-4a8a-932d-0ce4e8a6c87c", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "395c758a-0e88-4e41-b5b6-46b215352e62", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220556087200, "endTime": 324220565593800}, "additional": {"logType": "info", "children": [], "durationId": "5f26a8fa-45a8-4bb6-8b93-6b3d19533b1d", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "5899c438-ebda-4cd6-8779-84ecb8cf9ab6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220566468000, "endTime": 324220566486700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "logId": "6f83ce98-786c-406b-9fc9-5a84526a2da3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f83ce98-786c-406b-9fc9-5a84526a2da3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220566468000, "endTime": 324220566486700}, "additional": {"logType": "info", "children": [], "durationId": "5899c438-ebda-4cd6-8779-84ecb8cf9ab6", "parent": "84d43310-6f6b-4fe7-b866-0318fa76308a"}}, {"head": {"id": "d7678ff7-0145-42d2-86a9-7825e93ec7b2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220568795800, "endTime": 324220791098000}, "additional": {"children": ["21a94648-fa76-486b-82b5-9680d8457d93", "c8590b61-0cb2-40bb-ad61-cde79e9f4ed1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "logId": "5891f40e-e1c8-4be6-9e83-a874a9116fb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21a94648-fa76-486b-82b5-9680d8457d93", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220568797400, "endTime": 324220595291900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d7678ff7-0145-42d2-86a9-7825e93ec7b2", "logId": "47dbc724-4c6b-4fe3-8220-a63e355ef107"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8590b61-0cb2-40bb-ad61-cde79e9f4ed1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220595308800, "endTime": 324220791087000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d7678ff7-0145-42d2-86a9-7825e93ec7b2", "logId": "834a6ff5-9d1e-41a7-8189-6068cff4eb86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6869be8-1238-450e-8202-48a7282f3b50", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220568802600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6ab8a8-6afd-4d5a-b40e-f86f0da25dfa", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220595150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47dbc724-4c6b-4fe3-8220-a63e355ef107", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220568797400, "endTime": 324220595291900}, "additional": {"logType": "info", "children": [], "durationId": "21a94648-fa76-486b-82b5-9680d8457d93", "parent": "5891f40e-e1c8-4be6-9e83-a874a9116fb0"}}, {"head": {"id": "b9523f3c-dee7-438e-934a-bfcc060d742f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220595320000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296dcadf-54d5-4b6c-af09-6324af9b6ee3", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220648433200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0f184c-51e0-466a-a2d8-afacebc71f87", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220648548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd9300f-046d-4f9b-a94c-ba8883860995", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220648680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b0c193-08f1-4c86-a317-3fa9555913b2", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220648834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dfb0201-8c23-4388-a953-df7da4a8a333", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220650440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1eff89e-ad48-409d-9f56-2e8575bb3775", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220653786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9aca07-7a49-4efd-b730-1355171437be", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220663595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d0a818-f169-4b6c-b807-a7c69665845d", "name": "Sdk init in 110 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220763949400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1822eaee-d6f8-4000-8147-7b4a0904f190", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220764134100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 50}, "markType": "other"}}, {"head": {"id": "9733f236-4bcf-4e5c-b004-62ff026065ad", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220764151300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 50}, "markType": "other"}}, {"head": {"id": "a3cd7dce-e8a9-419e-869f-faf13d0c4b6a", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220790774600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499838a8-a07d-4456-a3ef-732b76ed2ec3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220790917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "184849a2-6a02-42d1-8f6d-78e8bd8bb3cd", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220790984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce1f0972-2fbf-45b4-b339-9c67158f5cf6", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220791039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834a6ff5-9d1e-41a7-8189-6068cff4eb86", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220595308800, "endTime": 324220791087000}, "additional": {"logType": "info", "children": [], "durationId": "c8590b61-0cb2-40bb-ad61-cde79e9f4ed1", "parent": "5891f40e-e1c8-4be6-9e83-a874a9116fb0"}}, {"head": {"id": "5891f40e-e1c8-4be6-9e83-a874a9116fb0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220568795800, "endTime": 324220791098000}, "additional": {"logType": "info", "children": ["47dbc724-4c6b-4fe3-8220-a63e355ef107", "834a6ff5-9d1e-41a7-8189-6068cff4eb86"], "durationId": "d7678ff7-0145-42d2-86a9-7825e93ec7b2", "parent": "84d43310-6f6b-4fe7-b866-0318fa76308a"}}, {"head": {"id": "6d17b7dd-782b-4389-b255-d6925fc7be97", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220791949700, "endTime": 324220791962400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "logId": "02ead4dd-c4da-4fac-a8a9-f6330950f2b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02ead4dd-c4da-4fac-a8a9-f6330950f2b5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220791949700, "endTime": 324220791962400}, "additional": {"logType": "info", "children": [], "durationId": "6d17b7dd-782b-4389-b255-d6925fc7be97", "parent": "84d43310-6f6b-4fe7-b866-0318fa76308a"}}, {"head": {"id": "84d43310-6f6b-4fe7-b866-0318fa76308a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220565607900, "endTime": 324220791973200}, "additional": {"logType": "info", "children": ["6f83ce98-786c-406b-9fc9-5a84526a2da3", "5891f40e-e1c8-4be6-9e83-a874a9116fb0", "02ead4dd-c4da-4fac-a8a9-f6330950f2b5"], "durationId": "d7a7f7ef-fa69-4592-8f51-15a8ebd02687", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "b7b510f7-751a-451e-9b8b-f1646bd53310", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220792610200, "endTime": 324220814831400}, "additional": {"children": ["4418fa67-6a41-425c-878a-d89e8bf38bf3", "6afef1c8-ba93-48e6-a658-3d1b964ffabf", "06725545-7ac1-4333-b72b-40a581fae523"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d0730d60-c262-409c-b28e-53a027a88e3c", "logId": "51dd6881-f6bc-43d5-ae96-3e31a28f311b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4418fa67-6a41-425c-878a-d89e8bf38bf3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220796071900, "endTime": 324220796081300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7b510f7-751a-451e-9b8b-f1646bd53310", "logId": "065e9529-3e9c-4abc-b12a-0a3c12e25f3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "065e9529-3e9c-4abc-b12a-0a3c12e25f3e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220796071900, "endTime": 324220796081300}, "additional": {"logType": "info", "children": [], "durationId": "4418fa67-6a41-425c-878a-d89e8bf38bf3", "parent": "51dd6881-f6bc-43d5-ae96-3e31a28f311b"}}, {"head": {"id": "6afef1c8-ba93-48e6-a658-3d1b964ffabf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220798093900, "endTime": 324220813083200}, "additional": {"children": ["75874424-2a5a-4c89-9cb5-54471006dc0e", "fff00085-6d3f-4f94-9f25-d2610e3ea6c9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7b510f7-751a-451e-9b8b-f1646bd53310", "logId": "6eddc034-6302-43c0-ba19-4d7d718efb2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75874424-2a5a-4c89-9cb5-54471006dc0e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220798095000, "endTime": 324220800936500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6afef1c8-ba93-48e6-a658-3d1b964ffabf", "logId": "f28d04ee-baa4-4589-a4c2-4b6d6c33a805"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fff00085-6d3f-4f94-9f25-d2610e3ea6c9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220800964100, "endTime": 324220813071300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6afef1c8-ba93-48e6-a658-3d1b964ffabf", "logId": "f169381a-93b7-4928-a8cb-224d7217bc01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1554b05-14b7-4e15-804b-dcf39e073ccf", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220798098200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5eb6c80-d33b-4809-bef6-1ee539a689d7", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220800790000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f28d04ee-baa4-4589-a4c2-4b6d6c33a805", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220798095000, "endTime": 324220800936500}, "additional": {"logType": "info", "children": [], "durationId": "75874424-2a5a-4c89-9cb5-54471006dc0e", "parent": "6eddc034-6302-43c0-ba19-4d7d718efb2e"}}, {"head": {"id": "ac6b2d55-188c-4b3b-b5e4-02f9f116485f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220800978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2679fa-6801-49d5-acb7-e2cb87440662", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220808666300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6138613-a24f-43b5-9f31-e358dd1f1584", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220808781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72fe49a-32bc-47d9-a5c5-b9a92b53e663", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220808962600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ff2b615-6848-46c6-b68c-9c478af633ed", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220809124200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e64bcb4-fdca-4bc4-836f-e9d7a6fe025a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220809227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5384f8-8d78-465e-ae91-7078f433531b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220809291200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93dec06-2d27-427e-96d2-891fbe34fec7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220809356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2760b2ce-15d8-44b7-b037-41a799197f53", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220812130700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c29dba-eef0-4087-9e63-34443272fa2a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220812842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56e6c4cf-c283-45a7-8f04-4f8ba24bb98f", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220812957300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac3c6a2-ed99-43d1-80ad-3a5226dd378b", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220813024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f169381a-93b7-4928-a8cb-224d7217bc01", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220800964100, "endTime": 324220813071300}, "additional": {"logType": "info", "children": [], "durationId": "fff00085-6d3f-4f94-9f25-d2610e3ea6c9", "parent": "6eddc034-6302-43c0-ba19-4d7d718efb2e"}}, {"head": {"id": "6eddc034-6302-43c0-ba19-4d7d718efb2e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220798093900, "endTime": 324220813083200}, "additional": {"logType": "info", "children": ["f28d04ee-baa4-4589-a4c2-4b6d6c33a805", "f169381a-93b7-4928-a8cb-224d7217bc01"], "durationId": "6afef1c8-ba93-48e6-a658-3d1b964ffabf", "parent": "51dd6881-f6bc-43d5-ae96-3e31a28f311b"}}, {"head": {"id": "06725545-7ac1-4333-b72b-40a581fae523", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220814788900, "endTime": 324220814815400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7b510f7-751a-451e-9b8b-f1646bd53310", "logId": "32e9f45c-9993-48ed-b3dd-10ee0239a4bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32e9f45c-9993-48ed-b3dd-10ee0239a4bd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220814788900, "endTime": 324220814815400}, "additional": {"logType": "info", "children": [], "durationId": "06725545-7ac1-4333-b72b-40a581fae523", "parent": "51dd6881-f6bc-43d5-ae96-3e31a28f311b"}}, {"head": {"id": "51dd6881-f6bc-43d5-ae96-3e31a28f311b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220792610200, "endTime": 324220814831400}, "additional": {"logType": "info", "children": ["065e9529-3e9c-4abc-b12a-0a3c12e25f3e", "6eddc034-6302-43c0-ba19-4d7d718efb2e", "32e9f45c-9993-48ed-b3dd-10ee0239a4bd"], "durationId": "b7b510f7-751a-451e-9b8b-f1646bd53310", "parent": "38d29eb6-2e7e-4dc9-8c4c-d106bf8a3737"}}, {"head": {"id": "38d29eb6-2e7e-4dc9-8c4c-d106bf8a3737", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220791985400, "endTime": 324220814844100}, "additional": {"logType": "info", "children": ["51dd6881-f6bc-43d5-ae96-3e31a28f311b"], "durationId": "d0730d60-c262-409c-b28e-53a027a88e3c", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "be314ba6-bbba-4d58-8ef7-ea2bbccf4c7c", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220838359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d483d785-5c2f-4629-be4e-4f0f7f1f04e9", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220838755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7addcf7f-29d2-4b0d-b8bb-d7b8f35ba903", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220814854000, "endTime": 324220838864400}, "additional": {"logType": "info", "children": [], "durationId": "9ded3f02-e81c-42e9-ac58-583de5bc67cb", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "714fd8e9-f797-427e-ad3c-eeb7b8968ef4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220839919800, "endTime": 324220840085400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "logId": "5781bc8e-9e67-47b3-9a81-6709c23e1a4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61de3196-3992-4123-bf3f-986d30a2baf1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220839939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5781bc8e-9e67-47b3-9a81-6709c23e1a4e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220839919800, "endTime": 324220840085400}, "additional": {"logType": "info", "children": [], "durationId": "714fd8e9-f797-427e-ad3c-eeb7b8968ef4", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "c8cf6b42-3f7d-4436-b0b1-ee579ab9c17f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220841622200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad503ca8-793b-450d-a0e9-eae4ed895dcb", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220847413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f464418-8d72-4512-9948-b00cf959ea8f", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220840097800, "endTime": 324220848209700}, "additional": {"logType": "info", "children": [], "durationId": "86a3354d-76cf-45b8-b221-a1237e518403", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "1583375d-d488-4578-97ed-c06b5872ea87", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220848238400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b83262e-c85c-46a8-9f7e-3a4342a1822b", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220854519200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38305b65-d242-4a33-9ae4-5691bf638dfd", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220854630700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d45f0a7-394f-4289-9cfc-791190b1b961", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220854824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f80f78-60ac-4536-a93c-0d1cbba2ae5e", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220857157500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4009e3be-bc4c-4953-bd84-1bcf58a94e98", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220857241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3715c35c-ba20-4b0d-b0aa-542d956c815a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220848228300, "endTime": 324220860575500}, "additional": {"logType": "info", "children": [], "durationId": "3518be39-3ae5-4626-abd8-ccee28c0eb6e", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "a6de0f80-439d-4a4b-ae4d-225a215daf8d", "name": "Configuration phase cost:305 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220860624800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63604938-6073-42e6-8afe-1f296a15af5d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220860603100, "endTime": 324220860717500}, "additional": {"logType": "info", "children": [], "durationId": "c07aa8a3-a349-4bbf-b255-247475f73fc0", "parent": "e6e2f3eb-9b99-4831-9424-6f85780afa03"}}, {"head": {"id": "e6e2f3eb-9b99-4831-9424-6f85780afa03", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220542655700, "endTime": 324220860725900}, "additional": {"logType": "info", "children": ["1212bd48-3d06-43e7-a67e-7bc897359592", "395c758a-0e88-4e41-b5b6-46b215352e62", "84d43310-6f6b-4fe7-b866-0318fa76308a", "38d29eb6-2e7e-4dc9-8c4c-d106bf8a3737", "7addcf7f-29d2-4b0d-b8bb-d7b8f35ba903", "6f464418-8d72-4512-9948-b00cf959ea8f", "3715c35c-ba20-4b0d-b0aa-542d956c815a", "63604938-6073-42e6-8afe-1f296a15af5d", "5781bc8e-9e67-47b3-9a81-6709c23e1a4e"], "durationId": "d267294f-6d03-459b-9af1-7fe905ad6f2b", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "a84e15c5-5d28-4672-9b6c-ebfeb26d69bd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220861825100, "endTime": 324220861836700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9", "logId": "ef2cfe69-105c-48a8-baf7-095f1958cc57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef2cfe69-105c-48a8-baf7-095f1958cc57", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220861825100, "endTime": 324220861836700}, "additional": {"logType": "info", "children": [], "durationId": "a84e15c5-5d28-4672-9b6c-ebfeb26d69bd", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "04053222-d481-48dd-af7b-df36767cd613", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220860746200, "endTime": 324220861843200}, "additional": {"logType": "info", "children": [], "durationId": "5e2ce08a-b710-437e-bf1c-ad2829e0fd31", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "fc0f0983-f3a0-4cb4-9418-ffed227d387c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220861846900, "endTime": 324220861847700}, "additional": {"logType": "info", "children": [], "durationId": "a62914f2-3b84-4181-a560-085d33504ad4", "parent": "815a428f-91f9-4b9d-aecc-7ee200016998"}}, {"head": {"id": "815a428f-91f9-4b9d-aecc-7ee200016998", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220316025900, "endTime": 324220861849900}, "additional": {"logType": "info", "children": ["c9c37d91-68ea-4028-9e7d-d18a1e3d05d5", "e6e2f3eb-9b99-4831-9424-6f85780afa03", "04053222-d481-48dd-af7b-df36767cd613", "fc0f0983-f3a0-4cb4-9418-ffed227d387c", "0010a3f1-8d3b-4939-bbbe-480d4a28eec3", "045f72c7-d079-479f-96d0-a909959a2829", "ef2cfe69-105c-48a8-baf7-095f1958cc57"], "durationId": "37809f7c-d048-4ed3-b31c-ccad9f7cedf9"}}, {"head": {"id": "516eb870-14fb-498c-81b3-a8f3b446350c", "name": "Configuration task cost before running: 559 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220861946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76310885-f426-44ee-9132-77907e5a831c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220868004500, "endTime": 324220879980300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a9f7d927-18fb-46b8-b6ef-fcc8f83af495", "logId": "8d3edf45-2c93-4045-8f3e-fb64939cbc44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f7d927-18fb-46b8-b6ef-fcc8f83af495", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220863841600}, "additional": {"logType": "detail", "children": [], "durationId": "76310885-f426-44ee-9132-77907e5a831c"}}, {"head": {"id": "b17c7bad-db2d-474f-af0c-9832afa53568", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220864516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e1af61-fd1b-4d99-88ae-2f72f32c5816", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220864599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f06f58a-ede9-482e-823e-dc507a8efd70", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220868017700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe990d6-e0ff-4887-a075-e414281bce21", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220879717600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c519ce0-dc05-41f1-81b1-9f021a773b77", "name": "entry : default@PreBuild cost memory 0.266357421875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220879882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3edf45-2c93-4045-8f3e-fb64939cbc44", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220868004500, "endTime": 324220879980300}, "additional": {"logType": "info", "children": [], "durationId": "76310885-f426-44ee-9132-77907e5a831c"}}, {"head": {"id": "c1658cc1-f6c8-4290-a9f5-3c86eaa155f0", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220886591300, "endTime": 324220889398900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2515fb59-090e-4fba-9149-51770031c09a", "logId": "7531ec4e-58a7-4a74-bf5a-a7a26f954d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2515fb59-090e-4fba-9149-51770031c09a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220884985000}, "additional": {"logType": "detail", "children": [], "durationId": "c1658cc1-f6c8-4290-a9f5-3c86eaa155f0"}}, {"head": {"id": "b010eba4-81de-473c-ba0c-e9ffb6bf3897", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220885568100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf15e4d1-d024-4ac3-bf9b-e73d730b71cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220885690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9694eae6-0cc6-4b2f-ba37-3afe16ff818c", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220886598900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdadfea2-08fd-4d99-bb86-6c1f6a762dcd", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220889186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "069d78b6-6428-4f59-a64f-05e30bc226df", "name": "entry : default@MergeProfile cost memory 0.11034393310546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220889309600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7531ec4e-58a7-4a74-bf5a-a7a26f954d2e", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220886591300, "endTime": 324220889398900}, "additional": {"logType": "info", "children": [], "durationId": "c1658cc1-f6c8-4290-a9f5-3c86eaa155f0"}}, {"head": {"id": "6e3737d1-b320-4a34-9c46-a5bf91fd13b2", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220893348100, "endTime": 324220943820100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f4d79719-cc2b-4e43-9a05-512fafe37ab7", "logId": "d164acab-2b7c-4a86-a10b-55864fe66f20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4d79719-cc2b-4e43-9a05-512fafe37ab7", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220891585300}, "additional": {"logType": "detail", "children": [], "durationId": "6e3737d1-b320-4a34-9c46-a5bf91fd13b2"}}, {"head": {"id": "a9419a71-8eb7-4274-b54f-40b0e18277ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220892213100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828bcf1f-2929-47f1-ad8d-77e3533069f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220892332900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c34311-2cb1-4cc7-a0f6-c2375b80c7e8", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220893358300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30536068-39dc-4d55-838e-6e3bc5632cf8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 49 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220941556500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6895a3-b17c-4db9-bad4-cfb4f300f008", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220943619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2168a293-66ac-4577-b1a1-5450276862e0", "name": "entry : default@CreateBuildProfile cost memory 0.09641265869140625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220943749500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d164acab-2b7c-4a86-a10b-55864fe66f20", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220893348100, "endTime": 324220943820100}, "additional": {"logType": "info", "children": [], "durationId": "6e3737d1-b320-4a34-9c46-a5bf91fd13b2"}}, {"head": {"id": "19052305-b734-4011-b219-0b05dd29244a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947350900, "endTime": 324220947867800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8a0b12b8-493c-4cc0-8ef9-2f4fb68bf9bd", "logId": "7369edc2-667d-4c16-920e-28981f56c608"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a0b12b8-493c-4cc0-8ef9-2f4fb68bf9bd", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220945524600}, "additional": {"logType": "detail", "children": [], "durationId": "19052305-b734-4011-b219-0b05dd29244a"}}, {"head": {"id": "030a67c2-a049-4b82-abea-b0d51977ec06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220945978900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9951dd9-4c56-4fe6-8b65-5d6bc40d6e3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220946048400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "654568f1-1f88-4774-a15e-5a0c44d95870", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e6e33b-eaf3-4c14-936e-f9e2afb8812a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22beeb5a-8b74-49c8-bb73-9d5051001113", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947586900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e5db53-23a2-4534-940e-108f8bfd89b7", "name": "entry : default@PreCheckSyscap cost memory 0.036956787109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947684100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c75b26d8-3d26-42ed-928c-89fef0d3b283", "name": "runTaskFromQueue task cost before running: 645 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7369edc2-667d-4c16-920e-28981f56c608", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220947350900, "endTime": 324220947867800, "totalTime": 414400}, "additional": {"logType": "info", "children": [], "durationId": "19052305-b734-4011-b219-0b05dd29244a"}}, {"head": {"id": "33c66eac-01f2-429f-8238-25dd031d4137", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220960520100, "endTime": 324220961761100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c031f857-c6d6-4af2-a9aa-a88c1458aee3", "logId": "e45d0afd-8706-4373-9d64-64d75fed1440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c031f857-c6d6-4af2-a9aa-a88c1458aee3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220950047500}, "additional": {"logType": "detail", "children": [], "durationId": "33c66eac-01f2-429f-8238-25dd031d4137"}}, {"head": {"id": "874fc50e-ae2f-4792-a288-4c4c6586a72a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220950688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8837c68c-69df-48a8-875e-5c3c14c287e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220950790800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea35937-8348-4c54-a0e8-b365d7e604c1", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220960534200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aa1bea1-0f57-4093-ae4d-d56d8f9c9527", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220960772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cc5bf67-09ba-47ad-b0d1-d6b142238a53", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220961567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852f2568-d8be-4e6e-9413-d615942b38d3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06520843505859375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220961674900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45d0afd-8706-4373-9d64-64d75fed1440", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220960520100, "endTime": 324220961761100}, "additional": {"logType": "info", "children": [], "durationId": "33c66eac-01f2-429f-8238-25dd031d4137"}}, {"head": {"id": "953cf638-b7c9-418d-91e6-ef3ea0e01f56", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220965175000, "endTime": 324220966374300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a8d69a34-353f-4972-b446-993b445e690f", "logId": "0317b1eb-b7c1-419c-ab10-a1b4075450b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8d69a34-353f-4972-b446-993b445e690f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220963447000}, "additional": {"logType": "detail", "children": [], "durationId": "953cf638-b7c9-418d-91e6-ef3ea0e01f56"}}, {"head": {"id": "e6678d05-5b31-4c9c-a62c-9c35b45a0b95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220963936800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5979e1e2-ed31-458e-b346-6afc84a3006d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220964012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ed11ef-3c4d-41a6-9c56-8c58e9eda981", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220965183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e48a6d-5f60-41fe-b3a4-78b0d60ca537", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220966225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cbc1336-350a-42a6-943c-f0fedce01222", "name": "entry : default@ProcessProfile cost memory 0.0558319091796875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220966308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0317b1eb-b7c1-419c-ab10-a1b4075450b9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220965175000, "endTime": 324220966374300}, "additional": {"logType": "info", "children": [], "durationId": "953cf638-b7c9-418d-91e6-ef3ea0e01f56"}}, {"head": {"id": "2cd4e13a-9623-4c9a-9a98-e8a50b8f67e7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220970076600, "endTime": 324220976394900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "68946fbb-cfa1-40d9-8513-78acbe2efa0d", "logId": "366cd094-cae5-45b2-889e-6d0d151e9ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68946fbb-cfa1-40d9-8513-78acbe2efa0d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220967814700}, "additional": {"logType": "detail", "children": [], "durationId": "2cd4e13a-9623-4c9a-9a98-e8a50b8f67e7"}}, {"head": {"id": "c2c72b1a-c06c-40e6-9228-cbaec42db363", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220968303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33ae6dd-528a-4651-8db0-284970507f6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220968371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e42e0f5-853b-466a-8918-ecac002ac04c", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220970084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c9acd09-5f87-4019-9b4d-a3d42c044926", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220976175500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7814ccc-d22b-4a8f-8619-fe34dd17fd8c", "name": "entry : default@ProcessRouterMap cost memory 0.186981201171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220976304900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "366cd094-cae5-45b2-889e-6d0d151e9ccf", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220970076600, "endTime": 324220976394900}, "additional": {"logType": "info", "children": [], "durationId": "2cd4e13a-9623-4c9a-9a98-e8a50b8f67e7"}}, {"head": {"id": "5e0ef189-5d88-4299-b57e-7cd33a63b74e", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220983236000, "endTime": 324220986658700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "53ca409f-2621-492d-853f-120446ba8f52", "logId": "79545236-61aa-4d66-a50b-509d1a12e8c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53ca409f-2621-492d-853f-120446ba8f52", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220979709200}, "additional": {"logType": "detail", "children": [], "durationId": "5e0ef189-5d88-4299-b57e-7cd33a63b74e"}}, {"head": {"id": "a405afa1-1993-44a2-ba49-6cd405c9f2a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220980240000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d4455b2-1e18-4e3d-a5dc-05432b50a577", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220980316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e90c8a-944c-483f-a93e-0efa79c71769", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220981258800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216d348b-5312-4bb0-9ae1-b406a11ce2e0", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220984503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a7a66c-f0ad-41d3-9b3b-4d29cfe615e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220984602800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9329807-286b-4698-b3a5-c1b5f5250262", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220984653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70199b10-12ef-4698-afef-f556e661de9b", "name": "entry : default@PreviewProcessResource cost memory 0.06789398193359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220984723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf94128-c4b1-48e5-ac10-4f6d0147e0ae", "name": "runTaskFromQueue task cost before running: 683 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220985856300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79545236-61aa-4d66-a50b-509d1a12e8c1", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220983236000, "endTime": 324220986658700, "totalTime": 1552300}, "additional": {"logType": "info", "children": [], "durationId": "5e0ef189-5d88-4299-b57e-7cd33a63b74e"}}, {"head": {"id": "1b9fe926-3899-462c-bc64-d34dfd84f984", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220994472000, "endTime": 324221024405900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8932624d-1c87-4496-949c-c49c07cd0376", "logId": "a662902a-7ffd-4223-ae9d-a11b85ba6270"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8932624d-1c87-4496-949c-c49c07cd0376", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220990248900}, "additional": {"logType": "detail", "children": [], "durationId": "1b9fe926-3899-462c-bc64-d34dfd84f984"}}, {"head": {"id": "8c15c881-4ec9-4f0c-9edd-184fa959c3b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220990873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f56298-f019-41aa-8b0e-e9a43a3ff97d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220990970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03fffd7f-812e-49d2-ae4f-6b919b0c0872", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220994483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28090fd9-6469-40a5-a349-49ae9e4afefa", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221024146300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67191242-0c57-460c-b730-c10ce160aea5", "name": "entry : default@GenerateLoaderJson cost memory 0.7043991088867188", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221024327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a662902a-7ffd-4223-ae9d-a11b85ba6270", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220994472000, "endTime": 324221024405900}, "additional": {"logType": "info", "children": [], "durationId": "1b9fe926-3899-462c-bc64-d34dfd84f984"}}, {"head": {"id": "ecdb1802-52be-456e-afd4-f2e49af31e33", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221038540200, "endTime": 324221686067000}, "additional": {"children": ["2f7b2f77-c3c6-4020-adc6-f922ba970b19", "5f98a7f6-d52f-4236-908b-e7043a4741e4", "76a4de0d-2d6a-4c3f-86b6-9a586315d34b", "454a291f-e2f3-427f-9e1c-b1001cfc91e4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "ad4e43ea-6024-4578-a90c-fe0dac0924d3", "logId": "5ed32e79-58b0-4552-9260-b784477cd2e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad4e43ea-6024-4578-a90c-fe0dac0924d3", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221032734200}, "additional": {"logType": "detail", "children": [], "durationId": "ecdb1802-52be-456e-afd4-f2e49af31e33"}}, {"head": {"id": "22601af6-f589-4e80-924b-d9db29f0f18f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221033458400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b40160-c49d-49be-be26-f9a98c53b409", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221033595300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70456834-4b5b-4ce6-b221-4c53faa21ccb", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221035014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cbff6af-954e-4c84-a7c6-29440f5e28c8", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221038570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c63a91c0-d2d8-4cc0-b552-46bb57c5f9dc", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221068958700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c290dbf-ea04-4ed7-88f8-c2cb336eed15", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221069176200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7b2f77-c3c6-4020-adc6-f922ba970b19", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221070271300, "endTime": 324221271072400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecdb1802-52be-456e-afd4-f2e49af31e33", "logId": "152f1742-3fd2-48d9-ba9b-476b51ba5f92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "152f1742-3fd2-48d9-ba9b-476b51ba5f92", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221070271300, "endTime": 324221271072400}, "additional": {"logType": "info", "children": [], "durationId": "2f7b2f77-c3c6-4020-adc6-f922ba970b19", "parent": "5ed32e79-58b0-4552-9260-b784477cd2e1"}}, {"head": {"id": "569fb85d-d208-41f3-9e43-e737f20c6c80", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221271550600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f98a7f6-d52f-4236-908b-e7043a4741e4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221272937900, "endTime": 324221482845900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecdb1802-52be-456e-afd4-f2e49af31e33", "logId": "177e4ce7-4e62-4799-a421-b267a74b23a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1202aa1f-6ef3-418f-9d13-f7c4f8187a4b", "name": "current process  memoryUsage: {\n  rss: 97992704,\n  heapTotal: 121724928,\n  heapUsed: 114238336,\n  external: 3141707,\n  arrayBuffers: 135572\n} os memoryUsage :6.918815612792969", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221274305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19a30a2-56e1-4ccf-877c-143d1da8ac99", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221478979300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177e4ce7-4e62-4799-a421-b267a74b23a5", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221272937900, "endTime": 324221482845900}, "additional": {"logType": "info", "children": [], "durationId": "5f98a7f6-d52f-4236-908b-e7043a4741e4", "parent": "5ed32e79-58b0-4552-9260-b784477cd2e1"}}, {"head": {"id": "dd9cce3a-1495-478b-b477-6955358f09a1", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221483053600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a4de0d-2d6a-4c3f-86b6-9a586315d34b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221485192900, "endTime": 324221567463700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecdb1802-52be-456e-afd4-f2e49af31e33", "logId": "274e1f19-dd11-44f7-9791-1e34f81be17d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b55e59b2-a5b8-41d0-a2dc-d59afc39ef2d", "name": "current process  memoryUsage: {\n  rss: 98164736,\n  heapTotal: 121724928,\n  heapUsed: 114486384,\n  external: 3141833,\n  arrayBuffers: 135713\n} os memoryUsage :6.8989410400390625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221486488500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6894af3f-d7e9-4302-9b2e-719d597306e7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221564812800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "274e1f19-dd11-44f7-9791-1e34f81be17d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221485192900, "endTime": 324221567463700}, "additional": {"logType": "info", "children": [], "durationId": "76a4de0d-2d6a-4c3f-86b6-9a586315d34b", "parent": "5ed32e79-58b0-4552-9260-b784477cd2e1"}}, {"head": {"id": "a986d9b3-967e-4fa6-9ed7-180e9d7b4401", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221567649700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454a291f-e2f3-427f-9e1c-b1001cfc91e4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221568886500, "endTime": 324221684787100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecdb1802-52be-456e-afd4-f2e49af31e33", "logId": "df468056-7f7b-4996-99f1-6fd9cbc2789d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b08c1e6-9bee-470c-b911-a6a2c938a433", "name": "current process  memoryUsage: {\n  rss: 98181120,\n  heapTotal: 121724928,\n  heapUsed: 114755936,\n  external: 3141959,\n  arrayBuffers: 136653\n} os memoryUsage :6.897716522216797", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221570011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb11f870-e3cc-46a7-afbb-d01ef6977a21", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221681350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df468056-7f7b-4996-99f1-6fd9cbc2789d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221568886500, "endTime": 324221684787100}, "additional": {"logType": "info", "children": [], "durationId": "454a291f-e2f3-427f-9e1c-b1001cfc91e4", "parent": "5ed32e79-58b0-4552-9260-b784477cd2e1"}}, {"head": {"id": "764715f4-844e-4755-a3f7-4af7ccee16da", "name": "entry : default@PreviewCompileResource cost memory -0.065093994140625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221685764100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51480c39-908e-449a-b7b2-1ac834374042", "name": "runTaskFromQueue task cost before running: 1 s 383 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221685951100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ed32e79-58b0-4552-9260-b784477cd2e1", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221038540200, "endTime": 324221686067000, "totalTime": 647347600}, "additional": {"logType": "info", "children": ["152f1742-3fd2-48d9-ba9b-476b51ba5f92", "177e4ce7-4e62-4799-a421-b267a74b23a5", "274e1f19-dd11-44f7-9791-1e34f81be17d", "df468056-7f7b-4996-99f1-6fd9cbc2789d"], "durationId": "ecdb1802-52be-456e-afd4-f2e49af31e33"}}, {"head": {"id": "9383ef83-582a-45a9-897c-6b87ddc53cac", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689703900, "endTime": 324221690069100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2d2eae16-4d95-4a64-a5a6-a25cef1cfcd0", "logId": "a0e4f6dd-4de6-4d8a-9db9-2b504675dea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d2eae16-4d95-4a64-a5a6-a25cef1cfcd0", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221688921900}, "additional": {"logType": "detail", "children": [], "durationId": "9383ef83-582a-45a9-897c-6b87ddc53cac"}}, {"head": {"id": "4aea8101-9dd5-44b9-9077-03f771fae7db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e7276d-a022-4036-b221-e22d689ab992", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689605200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57ab154e-ddeb-4501-86ff-fe97495bf214", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689711700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9333ab5-674c-45cd-b234-bb57dab306de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689803100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1715e7ae-71a3-442d-a69e-dfb82fa8aea4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689856700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82551b63-0d45-49ed-8e35-1ac21b0bd8d2", "name": "entry : default@PreviewHookCompileResource cost memory 0.0380706787109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05dd8755-e43f-463b-a578-740a7c8a40b8", "name": "runTaskFromQueue task cost before running: 1 s 387 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221690012400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e4f6dd-4de6-4d8a-9db9-2b504675dea8", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221689703900, "endTime": 324221690069100, "totalTime": 292100}, "additional": {"logType": "info", "children": [], "durationId": "9383ef83-582a-45a9-897c-6b87ddc53cac"}}, {"head": {"id": "14fcf99f-f336-4bfb-aeea-9b75995193a7", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221693909600, "endTime": 324221700807800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "69722f73-3862-4741-b1ed-e81933ff06f8", "logId": "9583bd28-e072-4358-95d4-89d73e923848"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69722f73-3862-4741-b1ed-e81933ff06f8", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221692034500}, "additional": {"logType": "detail", "children": [], "durationId": "14fcf99f-f336-4bfb-aeea-9b75995193a7"}}, {"head": {"id": "23e42929-8e96-47af-81bb-d32f839409c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221693114900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460f74dc-b568-4296-aba6-c2f111a6aa25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221693238600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ac593c-0052-46ad-9f30-7e006e4accfe", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221693917300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c96596-8e93-4711-894b-86b4fdecebe7", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221695316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a835fd05-a8a4-4a76-b031-20bae96c18c2", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221695415600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312ccb15-3577-48e3-922f-a9f38392ce89", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221695501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c9f712-c5ac-4d53-97e7-0a115b0a4fbc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221695553200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea504775-8148-4975-a99a-66b22798ba03", "name": "entry : default@CopyPreviewProfile cost memory 0.20758819580078125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221700563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c80479-1f3b-43a1-8373-f451e5c96297", "name": "runTaskFromQueue task cost before running: 1 s 397 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221700731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9583bd28-e072-4358-95d4-89d73e923848", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221693909600, "endTime": 324221700807800, "totalTime": 6790800}, "additional": {"logType": "info", "children": [], "durationId": "14fcf99f-f336-4bfb-aeea-9b75995193a7"}}, {"head": {"id": "9097826e-2d1c-4a9e-bd80-a556fc98d462", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221704762000, "endTime": 324221705141000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "588d66de-c850-4548-8a3b-d721755bba20", "logId": "a7679756-60ef-40e0-8edb-872277091ea4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "588d66de-c850-4548-8a3b-d721755bba20", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221703309000}, "additional": {"logType": "detail", "children": [], "durationId": "9097826e-2d1c-4a9e-bd80-a556fc98d462"}}, {"head": {"id": "f706e14d-4f78-4348-b102-7c99ae6e2f3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221703876000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62298ae8-778b-40d8-ae31-b0c5dabaa937", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221703992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f4da69-547c-40df-96fd-f1493a22bd4a", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221704771100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3f670d-463a-4ba8-b387-9a3741ee2e5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221704872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ae0d31-7975-4ef0-82f5-8f28004c34ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221704938300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c07420a-91f1-4769-9346-399c12db2b0f", "name": "entry : default@ReplacePreviewerPage cost memory 0.03801727294921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221705017600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc8df0d-7f2a-454d-9a6a-029e6118bcfa", "name": "runTaskFromQueue task cost before running: 1 s 402 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221705092100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7679756-60ef-40e0-8edb-872277091ea4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221704762000, "endTime": 324221705141000, "totalTime": 313100}, "additional": {"logType": "info", "children": [], "durationId": "9097826e-2d1c-4a9e-bd80-a556fc98d462"}}, {"head": {"id": "01c88ccb-aa09-4f1b-9640-31f6413447e2", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706645100, "endTime": 324221706865300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c98623a3-c92a-4197-894d-097779867add", "logId": "632f8b77-2095-40a5-bf9c-84504a7095ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c98623a3-c92a-4197-894d-097779867add", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706614300}, "additional": {"logType": "detail", "children": [], "durationId": "01c88ccb-aa09-4f1b-9640-31f6413447e2"}}, {"head": {"id": "f91a6645-63d1-45e5-96a5-b8794fd6616b", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706651600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59d57ecd-28c5-4f8f-9d5b-d73d8443ff54", "name": "entry : buildPreviewerResource cost memory 0.01169586181640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706737300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338f77a2-906a-4899-8224-26f5f7a3eca5", "name": "runTaskFromQueue task cost before running: 1 s 404 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706817000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "632f8b77-2095-40a5-bf9c-84504a7095ea", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221706645100, "endTime": 324221706865300, "totalTime": 156300}, "additional": {"logType": "info", "children": [], "durationId": "01c88ccb-aa09-4f1b-9640-31f6413447e2"}}, {"head": {"id": "a7fdd3a5-3f94-4e55-8a24-9048d8d9620c", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221709856700, "endTime": 324221713709700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "644211d5-e2ba-4238-835f-de9983988101", "logId": "e5001d69-6498-4aea-b89c-e3fc20993013"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "644211d5-e2ba-4238-835f-de9983988101", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221708502000}, "additional": {"logType": "detail", "children": [], "durationId": "a7fdd3a5-3f94-4e55-8a24-9048d8d9620c"}}, {"head": {"id": "ae20ecb2-dc0a-4e35-b6f3-811876ae231c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221709006700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d916fd-2d94-4151-b1c6-e1c1fa1e174a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221709100400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41f7f6a-1935-4c57-b133-6522ad340f18", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221709864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d531b17-c500-43a6-bc58-f4fb00ca41ed", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221711876400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a02d7da8-f6ff-4788-9d85-7dd71450c4f2", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221712071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c41172d-9c49-4aee-a61c-cd12f0e9ea09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221712235000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee25796-33ed-4f77-90fb-5706d71e13c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221712332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4c14560-6ee7-48a8-9dc7-9ce0bf351e81", "name": "entry : default@PreviewUpdateAssets cost memory 0.1286468505859375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221713463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c06bb4de-2836-441f-9482-3e78957ea46c", "name": "runTaskFromQueue task cost before running: 1 s 410 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221713631800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5001d69-6498-4aea-b89c-e3fc20993013", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221709856700, "endTime": 324221713709700, "totalTime": 3743700}, "additional": {"logType": "info", "children": [], "durationId": "a7fdd3a5-3f94-4e55-8a24-9048d8d9620c"}}, {"head": {"id": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221725398100, "endTime": 324255108800300}, "additional": {"children": ["ca2b64f6-b88a-4033-8795-e30437eb4e7e"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "74c18e71-2eba-4786-8aff-d96a1e5d5399", "logId": "0509a0c9-5a5f-4090-be46-9eac86d51295"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74c18e71-2eba-4786-8aff-d96a1e5d5399", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221716659400}, "additional": {"logType": "detail", "children": [], "durationId": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91"}}, {"head": {"id": "047c9735-1dc6-4916-bb1c-e7f89ed54fdf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221717247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3994a514-74f7-4e1f-abae-575a0e4e3dc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221717352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc0a7d8-e2c1-4b5f-bbfb-e13bd803ed92", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221725416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker14", "startTime": 324221751233000, "endTime": 324255108600200}, "additional": {"children": ["e917a839-8a15-4fbf-ad60-3560028bebd1", "e250960c-7107-4512-adad-3101b3a9139e", "14f6cd9a-52fb-4fef-b6e5-01f6dda541ab", "30f4f464-84d1-49f2-b342-1404698c61e3", "55d7410b-4daf-4ea4-8195-279c4b77e6af", "37c64b85-cfcc-4727-91cb-ea791eb85ea5", "027e33aa-e7d4-463e-913c-d5ecff4083e9", "4d349a38-4205-44a8-83cc-6a6210c58779", "af25319e-f1ca-4bfb-b59e-b2e203ed7ced", "05c0ede0-5adb-4b2b-b376-091b72d771d6", "3e3cb8e8-5d90-4a70-86fc-8080d0b5ac93", "591366d8-dfd7-4ab0-a2af-4e875b78ecc7", "bb0bea63-9a29-4306-bf5e-585e88afb6c7", "f1ed198b-0294-49d4-b9c2-82a8cc0c0385", "0c5e0b90-06f7-4936-a25c-a001eb08ddde"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91", "logId": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a833073f-7a29-418c-bb41-213e2baef7ac", "name": "entry : default@PreviewArkTS cost memory -0.7524261474609375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221753533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d793f6e-b2c4-42ee-b579-26804512a16f", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324240001107300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e917a839-8a15-4fbf-ad60-3560028bebd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324240003915400, "endTime": 324240003956300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "557578d8-a9d9-4ebb-b9fd-0d1c8eaced31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "557578d8-a9d9-4ebb-b9fd-0d1c8eaced31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324240003915400, "endTime": 324240003956300}, "additional": {"logType": "info", "children": [], "durationId": "e917a839-8a15-4fbf-ad60-3560028bebd1", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "f00cc8ad-5510-4438-a51d-b23ba1f7edfa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250425745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e250960c-7107-4512-adad-3101b3a9139e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250427084300, "endTime": 324250427107200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "9039c503-4c29-4370-8f09-26650b7e8c89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9039c503-4c29-4370-8f09-26650b7e8c89", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250427084300, "endTime": 324250427107200}, "additional": {"logType": "info", "children": [], "durationId": "e250960c-7107-4512-adad-3101b3a9139e", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "bc05e8b3-319b-4957-99bc-c8d40d0a2640", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250427222500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f6cd9a-52fb-4fef-b6e5-01f6dda541ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250428328800, "endTime": 324250428350200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "89f7623e-2a08-48b0-bb97-ad779149d3da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89f7623e-2a08-48b0-bb97-ad779149d3da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250428328800, "endTime": 324250428350200}, "additional": {"logType": "info", "children": [], "durationId": "14f6cd9a-52fb-4fef-b6e5-01f6dda541ab", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "e7db25dd-a9e6-4476-99d6-3df3311cfdaa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250428465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f4f464-84d1-49f2-b342-1404698c61e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250430044900, "endTime": 324250430074300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "7c3f629c-1e4f-4c95-917a-e94c142209d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c3f629c-1e4f-4c95-917a-e94c142209d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250430044900, "endTime": 324250430074300}, "additional": {"logType": "info", "children": [], "durationId": "30f4f464-84d1-49f2-b342-1404698c61e3", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "3f5f0ad5-f359-4f64-a427-58f180a4325a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250430210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55d7410b-4daf-4ea4-8195-279c4b77e6af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250431684700, "endTime": 324250431714400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "790e8f14-cd47-486c-abd5-b08d4ec00870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "790e8f14-cd47-486c-abd5-b08d4ec00870", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250431684700, "endTime": 324250431714400}, "additional": {"logType": "info", "children": [], "durationId": "55d7410b-4daf-4ea4-8195-279c4b77e6af", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "0ce83957-f40c-473f-9cc7-05520f33c148", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250431938700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37c64b85-cfcc-4727-91cb-ea791eb85ea5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250433219600, "endTime": 324250433240700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "8225221d-4849-45e0-a9d7-9d16e7d448d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8225221d-4849-45e0-a9d7-9d16e7d448d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250433219600, "endTime": 324250433240700}, "additional": {"logType": "info", "children": [], "durationId": "37c64b85-cfcc-4727-91cb-ea791eb85ea5", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "c749a8ac-7871-4a6d-b694-05ee38981d89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250433345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "027e33aa-e7d4-463e-913c-d5ecff4083e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250434564200, "endTime": 324250434588200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "6b8cb391-3865-46b4-aae9-c82a7d3563f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b8cb391-3865-46b4-aae9-c82a7d3563f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250434564200, "endTime": 324250434588200}, "additional": {"logType": "info", "children": [], "durationId": "027e33aa-e7d4-463e-913c-d5ecff4083e9", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "2f4467de-7845-4f96-b01c-4118c880c3c8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250434704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d349a38-4205-44a8-83cc-6a6210c58779", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250435833200, "endTime": 324250435857800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "2fb0606c-c471-444b-a61d-07e5a9244178"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fb0606c-c471-444b-a61d-07e5a9244178", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250435833200, "endTime": 324250435857800}, "additional": {"logType": "info", "children": [], "durationId": "4d349a38-4205-44a8-83cc-6a6210c58779", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "247ed702-4aa2-44ef-ae1c-05c7f8df27b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250435979700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af25319e-f1ca-4bfb-b59e-b2e203ed7ced", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250437033300, "endTime": 324250437056100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "3dcc2e45-0b9b-48eb-9925-b388c6f94af9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dcc2e45-0b9b-48eb-9925-b388c6f94af9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250437033300, "endTime": 324250437056100}, "additional": {"logType": "info", "children": [], "durationId": "af25319e-f1ca-4bfb-b59e-b2e203ed7ced", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "f8f499d0-69a6-42eb-b0e1-5a9f94861772", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250437183600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c0ede0-5adb-4b2b-b376-091b72d771d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250438470900, "endTime": 324250438488100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "d8e14669-fc01-4720-b4be-11bbec5e2dee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8e14669-fc01-4720-b4be-11bbec5e2dee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250438470900, "endTime": 324250438488100}, "additional": {"logType": "info", "children": [], "durationId": "05c0ede0-5adb-4b2b-b376-091b72d771d6", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "efc384f6-c540-4c3e-ad8c-6a20a29fc3a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250438574100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3cb8e8-5d90-4a70-86fc-8080d0b5ac93", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250439441000, "endTime": 324250439456900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "0a9922cd-0616-4b56-8ee0-b538314a848b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a9922cd-0616-4b56-8ee0-b538314a848b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250439441000, "endTime": 324250439456900}, "additional": {"logType": "info", "children": [], "durationId": "3e3cb8e8-5d90-4a70-86fc-8080d0b5ac93", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "d3a68dd6-3eb8-4585-a422-68958a1200cc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250439529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591366d8-dfd7-4ab0-a2af-4e875b78ecc7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250440401400, "endTime": 324250440416000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "d0b1d7f1-b8d2-496c-906e-4cc0819bce0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0b1d7f1-b8d2-496c-906e-4cc0819bce0a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250440401400, "endTime": 324250440416000}, "additional": {"logType": "info", "children": [], "durationId": "591366d8-dfd7-4ab0-a2af-4e875b78ecc7", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "438a39d8-ccbe-476e-af1a-006cb67fbc34", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250440493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0bea63-9a29-4306-bf5e-585e88afb6c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250441405600, "endTime": 324250441418800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "18f8f889-b51d-4c73-987c-eb1a77d89f95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18f8f889-b51d-4c73-987c-eb1a77d89f95", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250441405600, "endTime": 324250441418800}, "additional": {"logType": "info", "children": [], "durationId": "bb0bea63-9a29-4306-bf5e-585e88afb6c7", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "c1d8c0ef-5e6d-4fcd-8696-2d8bb147bf47", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250441488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ed198b-0294-49d4-b9c2-82a8cc0c0385", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250442913600, "endTime": 324250442929400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "d5ec7cc5-bd4a-4eb5-a53e-cdbfd0163eff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5ec7cc5-bd4a-4eb5-a53e-cdbfd0163eff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324250442913600, "endTime": 324250442929400}, "additional": {"logType": "info", "children": [], "durationId": "f1ed198b-0294-49d4-b9c2-82a8cc0c0385", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "0ec0df30-e2ff-4304-8e8c-065128f90d82", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255107272900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c5e0b90-06f7-4936-a25c-a001eb08ddde", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255108518600, "endTime": 324255108534600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "logId": "6ac1390e-a266-4c0b-ae35-240767b75536"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ac1390e-a266-4c0b-ae35-240767b75536", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255108518600, "endTime": 324255108534600}, "additional": {"logType": "info", "children": [], "durationId": "0c5e0b90-06f7-4936-a25c-a001eb08ddde", "parent": "52d6975d-509c-416d-b2ba-bfd8db06cda0"}}, {"head": {"id": "52d6975d-509c-416d-b2ba-bfd8db06cda0", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker14", "startTime": 324221751233000, "endTime": 324255108600200}, "additional": {"logType": "error", "children": ["557578d8-a9d9-4ebb-b9fd-0d1c8eaced31", "9039c503-4c29-4370-8f09-26650b7e8c89", "89f7623e-2a08-48b0-bb97-ad779149d3da", "7c3f629c-1e4f-4c95-917a-e94c142209d1", "790e8f14-cd47-486c-abd5-b08d4ec00870", "8225221d-4849-45e0-a9d7-9d16e7d448d6", "6b8cb391-3865-46b4-aae9-c82a7d3563f8", "2fb0606c-c471-444b-a61d-07e5a9244178", "3dcc2e45-0b9b-48eb-9925-b388c6f94af9", "d8e14669-fc01-4720-b4be-11bbec5e2dee", "0a9922cd-0616-4b56-8ee0-b538314a848b", "d0b1d7f1-b8d2-496c-906e-4cc0819bce0a", "18f8f889-b51d-4c73-987c-eb1a77d89f95", "d5ec7cc5-bd4a-4eb5-a53e-cdbfd0163eff", "6ac1390e-a266-4c0b-ae35-240767b75536"], "durationId": "ca2b64f6-b88a-4033-8795-e30437eb4e7e", "parent": "0509a0c9-5a5f-4090-be46-9eac86d51295"}}, {"head": {"id": "55bb032b-aea3-419f-b077-3f40efebbeb6", "name": "default@PreviewArkTS watch work[14] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255108643500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0509a0c9-5a5f-4090-be46-9eac86d51295", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324221725398100, "endTime": 324255108800300}, "additional": {"logType": "error", "children": ["52d6975d-509c-416d-b2ba-bfd8db06cda0"], "durationId": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91"}}, {"head": {"id": "6fdb4587-4cd5-49cb-a01d-3a07af77109b", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255108915200}, "additional": {"logType": "debug", "children": [], "durationId": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91"}}, {"head": {"id": "e810c92e-cd88-498d-a6bc-5ca6f49eca47", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:192:37\n Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:343:37\n Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:201:15\n Type 'number' is not assignable to type 'string'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:330:73\n Argument of type 'HttpResponse' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'HttpResponse'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:352:15\n Type 'number' is not assignable to type 'string'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/UserApi.ets:27:77\n Argument of type 'UserLoginFormData' is not assignable to parameter of type 'Record<string, string>'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/BankCardApi.ets:40:51\n Argument of type 'BankCardBindFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'BankCardBindFormData'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:55:55\n Argument of type 'TransferFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'TransferFormData'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:67:54\n Argument of type 'RechargeFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'RechargeFormData'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:79:55\n Argument of type 'WithdrawFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'WithdrawFormData'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:63:48\n Property 'pages' does not exist on type 'Transaction[]'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TestConnectionPage.ets:136:84\n Argument of type 'UserLoginFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'UserLoginFormData'.\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255130558200}, "additional": {"logType": "debug", "children": [], "durationId": "4dbe4650-4cc2-4a6b-b9eb-ecd16c6e1b91"}}, {"head": {"id": "acd7bae8-9e16-47c1-ad5f-36d5175755f3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138067800, "endTime": 324255138114800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7e90437-eb10-492e-ac62-5c60a28a7390", "logId": "1dfc0cbf-4f71-4251-b624-454a48c5a1e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dfc0cbf-4f71-4251-b624-454a48c5a1e3", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138067800, "endTime": 324255138114800}, "additional": {"logType": "info", "children": [], "durationId": "acd7bae8-9e16-47c1-ad5f-36d5175755f3"}}, {"head": {"id": "72064246-76c9-4407-b735-0081a83cbfa1", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324220303738300, "endTime": 324255138199500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 51}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "b13aba68-2659-4ac3-81a0-b4d0d3f8b081", "name": "BUILD FAILED in 34 s 835 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138222700}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "22451174-88fa-459a-9d6e-b89a707d846d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72730226-51ab-4105-8830-0f732309a8f9", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138414600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4943cfe-95ad-4b85-a120-48986e27abc4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138457600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be1fcefb-2303-4b52-bb76-488fb6998414", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02966db2-3fa8-4fb1-8799-5b8084fb04ea", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138538100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb0864b-106d-44b0-a645-38c47c89ef7a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ab5c79-b5e5-4a12-ab55-bb4eab45693e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255138639900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40492899-6eb5-4d53-b905-7e81cda75077", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255139493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4f816a-ba71-4f90-b37d-450bf8af8fe3", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255148422700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b4ad61-d34b-4426-abfa-370223b07d65", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255148761400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1d38f1-dec6-4212-8f11-9ba97001d725", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255160041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7bcdea5-bf62-4ec7-a34b-76855034ac8e", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255160880700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e11f68d-54a1-489f-a900-cb4f0e4f6b22", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255161091700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1fbadda-5d79-409f-adda-14434db37798", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255161960200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1133a694-3ce4-4cb8-8ab8-962cb92bb494", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255162841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bea293-725b-4020-83e9-1ddc51d2f5d8", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255163307400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a9d9e3-959a-4fcf-9297-d66b5321f626", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255163658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e6eae2-d5f6-4b76-bf8f-cabe77377455", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255164016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793f5f6c-c48e-45a0-a3c3-3b257de78765", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255167057100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4825026-7e23-45d9-912f-4c2be0a4b51f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255167934900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1dce44-2438-4c7f-8b65-4c37ab6c5cdf", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255168244200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40feb87d-2aae-4921-95bf-06ed6ced70a0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255168549100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82e592d-e133-4a59-9267-7d21e094bb16", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255169434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9588d556-76bd-4c92-8554-f8315d67720d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255182381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b644da82-6026-443d-9e48-d2a86798fc38", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255182801500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef98a4f2-e81a-4794-91ed-2bcf72866077", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255183185300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acfa844-cc0b-412a-9044-f8ea42783c5e", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255183561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9bf4db8-fe61-405f-80e2-8d53c06f8efd", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255183842900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}