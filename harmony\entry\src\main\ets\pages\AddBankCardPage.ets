import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { BankCardBindRequest, BankCardType } from '../common/types/index';
import { tempDataManager, TempDataKeys, FormData } from '../common/storage/TempDataManager';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct AddBankCardPage {
  @State cardNo: string = '';
  @State holderName: string = '';
  @State bankName: string = '';
  @State cardType: string = '储蓄卡';
  @State isLoading: boolean = false;




  @State userInfoLoaded: boolean = false; // 用于强制界面更新

  aboutToAppear() {
    // 获取路由参数（用于初始化）
    const params = router.getParams() as Record<string, string>;
    if (params?.selectedBank) {
      this.bankName = params.selectedBank;
    }
    if (params?.selectedType) {
      this.cardType = params.selectedType;
    }

    // 加载用户信息，设置持卡人姓名
    this.loadUserInfo();
  }

  onPageShow() {
    this.handlePageParams();
  }

  /**
   * 获取持卡人姓名显示文本
   */
  getHolderNameDisplay(): string {
    console.log('getHolderNameDisplay 被调用，holderName:', this.holderName, 'userInfoLoaded:', this.userInfoLoaded);

    if (!this.userInfoLoaded) {
      return '加载中...';
    }

    if (this.holderName && this.holderName.length > 0) {
      return this.holderName;
    }

    return '未知用户';
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const userInfo = await storageManager.getUserInfo();

      if (userInfo && userInfo.realName) {
        this.holderName = userInfo.realName;
        this.userInfoLoaded = true;
        console.log('用户信息加载成功，姓名:', this.holderName);
      } else {
        // 如果没有真实姓名，设置为空字符串，让用户输入
        this.holderName = '';
        this.userInfoLoaded = true;
        console.log('用户信息中没有真实姓名，等待用户输入');
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 加载失败时也设置为空，让用户输入
      this.holderName = '';
      this.userInfoLoaded = true;
    }
  }



  private handlePageParams() {
    // 检查是否有选择器返回的数据
    const selectedBank: string | null = tempDataManager.getAndRemoveData(TempDataKeys.SELECTED_BANK) as string | null;
    const selectedCardType: string | null = tempDataManager.getAndRemoveData(TempDataKeys.SELECTED_CARD_TYPE) as string | null;

    // 恢复表单数据
    const formData: FormData | null = tempDataManager.getData(TempDataKeys.ADD_BANK_CARD_FORM) as FormData | null;

    if (formData) {
      this.cardNo = formData.cardNo || '';
      // 不恢复持卡人姓名，保持用户真实姓名
      // this.holderName = formData.holderName || '';
      // 只有在没有新选择的情况下才恢复原来的值
      if (!selectedBank) {
        this.bankName = formData.bankName || '';
      }
      if (!selectedCardType) {
        this.cardType = formData.cardType || '储蓄卡';
      }
    }

    // 更新选择的银行（优先级最高）
    if (selectedBank) {
      this.bankName = selectedBank;
    }

    // 更新选择的卡片类型（优先级最高）
    if (selectedCardType) {
      this.cardType = selectedCardType;
    }
  }

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('添加银行卡')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Row().width(40).height(40) // 占位
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })

      Scroll() {
        Column() {
          // 银行卡信息表单
          Column() {
            // 银行卡号
            this.InputField('银行卡号', '请输入银行卡号', this.cardNo, (value: string) => {
              this.cardNo = value;
            }, InputType.Number, 19)

            // 持卡人姓名（可输入）
            this.InputField('持卡人姓名', '请输入持卡人姓名', this.holderName, (value: string) => {
              this.holderName = value;
            }, InputType.Normal, 20)

            // 银行名称选择
            Column() {
              Text('开户银行')
                .fontSize(14)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              Row() {
                Text(this.bankName && this.bankName.length > 0 ? this.bankName : '请选择开户银行')
                  .fontSize(16)
                  .fontColor((this.bankName && this.bankName.length > 0 ? this.bankName : '请选择开户银行').includes('请选择') ? '#999999' : '#333333')
                  .layoutWeight(1)

                Image($r('app.media.ic_arrow_right'))
                  .width(16)
                  .height(16)
                  .fillColor('#999999')
              }
              .width('100%')
              .height(48)
              .padding({ left: 12, right: 12 })
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E0E0E0' })
              .onClick(() => {
                // 简单的银行选择逻辑
                if (this.bankName === '中国银行') {
                  this.bankName = '工商银行';
                } else if (this.bankName === '工商银行') {
                  this.bankName = '建设银行';
                } else if (this.bankName === '建设银行') {
                  this.bankName = '农业银行';
                } else {
                  this.bankName = '中国银行';
                }
              })
            }
            .alignItems(HorizontalAlign.Start)
            .margin({ bottom: 20 })

            // 卡片类型选择
            Column() {
              Text('卡片类型')
                .fontSize(14)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              Row() {
                Text(this.cardType)
                  .fontSize(16)
                  .fontColor('#333333')
                  .layoutWeight(1)

                Image($r('app.media.ic_arrow_right'))
                  .width(16)
                  .height(16)
                  .fillColor('#999999')
              }
              .width('100%')
              .height(48)
              .padding({ left: 12, right: 12 })
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E0E0E0' })
              .onClick(() => {
                // 简单的卡片类型切换
                this.cardType = this.cardType === '储蓄卡' ? '信用卡' : '储蓄卡';
              })
            }
            .alignItems(HorizontalAlign.Start)
            .margin({ bottom: 20 })

            // 添加按钮
            Button('添加银行卡')
              .width('100%')
              .height(48)
              .fontSize(16)
              .fontColor(Color.White)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ top: 30 })
              .enabled(!this.isLoading && this.isFormValid())
              .opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5)
              .onClick(() => {
                this.handleAddCard();
              })

            // 安全提示
            Column() {
              Text('安全提示')
                .fontSize(14)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              Text('• 请确保银行卡信息准确无误\n• 仅支持本人实名银行卡\n• 银行卡信息将加密保存')
                .fontSize(12)
                .fontColor('#666666')
                .lineHeight(18)
            }
            .width('100%')
            .padding(16)
            .backgroundColor('#F8F9FA')
            .borderRadius(8)
            .margin({ top: 20 })
            .alignItems(HorizontalAlign.Start)
          }
          .width('100%')
          .padding({ left: 24, right: 24, top: 20, bottom: 40 })
        }
      }
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }

  @Builder
  InputField(
    label: string,
    placeholder: string,
    value: string,
    onChange: (value: string) => void,
    inputType: InputType = InputType.Normal,
    maxLength?: number
  ) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: placeholder, text: value })
        .type(inputType)
        .maxLength(maxLength)
        .fontSize(16)
        .height(48)
        .borderRadius(8)
        .backgroundColor('#F8F9FA')
        .border({ width: 1, color: '#E0E0E0' })
        .onChange(onChange)
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 20 })
  }

  @Builder
  ReadOnlyField(label: string, value: string, hint?: string, forceUpdate?: boolean) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      Row() {
        Text(value)
          .fontSize(16)
          .fontColor('#333333')
          .layoutWeight(1)

        Text('🔒')
          .fontSize(16)
          .fontColor('#999999')
      }
      .width('100%')
      .height(48)
      .padding({ left: 12, right: 12 })
      .backgroundColor('#F5F5F5')
      .borderRadius(8)
      .border({ width: 1, color: '#E0E0E0' })

      if (hint) {
        Text(hint)
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 20 })
  }





  /**
   * 表单验证
   */
  isFormValid(): boolean {
    return this.cardNo.length >= 16 &&
           this.holderName.length >= 2 &&
           this.bankName.length > 0 &&
           this.cardType.length > 0;
  }

  /**
   * 处理添加银行卡
   */
  async handleAddCard() {
    if (this.isLoading) return;

    // 表单验证
    if (!this.validateForm()) return;

    this.isLoading = true;

    try {
      const cardData: BankCardBindRequest = {
        userId: 1, // 临时使用固定用户ID
        cardNumber: this.cardNo,
        cardType: this.cardType,
        bankName: this.bankName,
        balance: 0,
        creditLimit: 0
      };

      await BankCardApi.bindCard(cardData);

      promptAction.showToast({ message: '添加银行卡成功' });

      // 通知银行卡列表页面刷新
      console.log('AddBankCardPage - 设置银行卡添加事件标志');
      tempDataManager.setData('BANK_CARD_ADDED', true);

      // 返回银行卡列表页
      router.back();

    } catch (error) {
      console.error('添加银行卡失败:', error);
      promptAction.showToast({ message: '添加银行卡失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 表单验证
   */
  validateForm(): boolean {
    if (this.cardNo.length < 16) {
      promptAction.showToast({ message: '请输入正确的银行卡号' });
      return false;
    }

    if (this.holderName.length < 2) {
      promptAction.showToast({ message: '请输入持卡人姓名（至少2个字符）' });
      return false;
    }

    if (this.bankName.length === 0) {
      promptAction.showToast({ message: '请选择开户银行' });
      return false;
    }

    return true;
  }
}


