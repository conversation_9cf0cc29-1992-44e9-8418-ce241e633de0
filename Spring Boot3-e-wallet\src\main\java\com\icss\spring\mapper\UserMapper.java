package com.icss.spring.mapper;
import com.icss.spring.entity.User;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

public interface UserMapper {
    // 从指定账户转出指定金额
    @Update("update user set account = account - #{money} where id = #{fromId}")
    int fromAccount(@Param("fromId") Long fromId, @Param("money") BigDecimal money);

    // 转入指定账户指定金额
    @Update("update user set account = account + #{money} where id = #{toId}")
    int toAccount(@Param("toId") Long toId, @Param("money") BigDecimal money);

    // 根据id查询用户
    @Select("select * from user where id = #{id}")
    User findUserById(@Param("id") Long id);

    // 根据用户名查询用户
    @Select("select * from user where username = #{username}")
    User findUserByUsername(@Param("username") String username);

    // 更新登录密码
    @Update("UPDATE user SET password = #{newPassword} WHERE id = #{userId}")
    int updateLoginPassword(@Param("userId") Long userId, @Param("newPassword") String newPassword);

    // 更新支付密码
    @Update("UPDATE user SET payment_password = #{newPassword} WHERE id = #{userId}")
    int updatePaymentPassword(@Param("userId") Long userId, @Param("newPassword") String newPassword);

    // 验证支付密码
    @Select("SELECT COUNT(*) FROM user WHERE id = #{userId} AND payment_password = #{password}")
    int verifyPaymentPassword(@Param("userId") Long userId, @Param("password") String password);

    // 更新用户信息（不包括真实姓名）
    @Update("UPDATE user SET username = #{username}, mobile = #{mobile}, email = #{email} WHERE id = #{id}")
    int updateUserInfo(User user);

    // 更新支付限额
    @Update("UPDATE user SET payment_limit = #{newLimit} WHERE id = #{userId}")
    int updatePaymentLimit(@Param("userId") Long userId, @Param("newLimit") BigDecimal newLimit);
}
