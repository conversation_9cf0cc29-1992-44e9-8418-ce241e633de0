#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的网络连接测试脚本
用于测试Spring Boot服务是否正常运行
"""

import requests
import time
import json

def test_connection():
    """测试Spring Boot连接"""
    base_urls = [
        'http://localhost:8080',
        'http://127.0.0.1:8080',
        'http://0.0.0.0:8080'
    ]
    
    endpoints = [
        '/test',
        '/api/test',
        '/',
        '/actuator/health'
    ]
    
    print("=== Spring Boot 连接测试 ===")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    for base_url in base_urls:
        print(f"测试基础URL: {base_url}")
        
        for endpoint in endpoints:
            url = base_url + endpoint
            try:
                print(f"  测试: {url}")
                response = requests.get(url, timeout=10)
                print(f"    ✅ 状态码: {response.status_code}")
                print(f"    📄 响应: {response.text[:100]}...")
                print()
                
                # 如果成功，尝试测试API
                if response.status_code == 200 and endpoint == '/test':
                    test_api_endpoints(base_url)
                    
            except requests.exceptions.ConnectionError:
                print(f"    ❌ 连接错误: 无法连接到服务器")
            except requests.exceptions.Timeout:
                print(f"    ⏰ 超时错误: 请求超时")
            except Exception as e:
                print(f"    ❌ 其他错误: {str(e)}")
            print()
        print("-" * 50)

def test_api_endpoints(base_url):
    """测试API端点"""
    print(f"  🔍 测试API端点...")
    
    api_tests = [
        {
            'url': f'{base_url}/api/user/login',
            'method': 'POST',
            'data': {
                'username': '1111',
                'password': '123456',
                'captcha': '1234'
            }
        },
        {
            'url': f'{base_url}/api/bank-card/list',
            'method': 'GET'
        }
    ]
    
    for test in api_tests:
        try:
            if test['method'] == 'POST':
                response = requests.post(test['url'], data=test.get('data'), timeout=10)
            else:
                response = requests.get(test['url'], timeout=10)
                
            print(f"    API {test['method']} {test['url']}")
            print(f"    状态码: {response.status_code}")
            print(f"    响应: {response.text[:200]}...")
            
        except Exception as e:
            print(f"    API测试失败: {str(e)}")
        print()

if __name__ == "__main__":
    test_connection()
