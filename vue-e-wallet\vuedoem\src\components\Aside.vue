<script setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
  Money,
  CreditCard,
  Wallet,
  ShoppingCart,
  User
} from '@element-plus/icons-vue'
</script>

<template>
    <div>
        <el-menu
        default-active="/home/<USER>"
        class="el-menu-vertical-demo"
        background-color="#2c3e50"
        text-color="#ecf0f1"
        active-text-color="#3498db"
        :router="true"
      >
        <el-menu-item index="/home/<USER>">
          <el-icon><credit-card /></el-icon>
          <span>银行卡管理</span>
        </el-menu-item>
        
        <el-menu-item index="/home/<USER>">
          <el-icon><money /></el-icon>
          <span>交易记录</span>
        </el-menu-item>
        
        <el-menu-item index="/home/<USER>">
          <el-icon><wallet /></el-icon>
          <span>我的钱包</span>
        </el-menu-item>
        
        <el-menu-item index="/home/<USER>">
          <el-icon><shopping-cart /></el-icon>
          <span>支付中心</span>
        </el-menu-item>
        
        <el-menu-item index="/home/<USER>">
          <el-icon><user /></el-icon>
          <span>个人设置</span>
        </el-menu-item>
      </el-menu>
    </div>
</template>

<style scoped>
.el-menu-vertical-demo {
  height: 100vh;
  border-right: none;
}
</style>