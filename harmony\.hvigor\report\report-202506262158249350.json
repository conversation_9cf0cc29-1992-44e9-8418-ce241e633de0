{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "c2d2b519-e9e6-4908-84e7-81b0671e8377", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965828452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8beeae9-df5f-4c47-baa5-c2b62482175e", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965836577100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d955d1-f948-4364-9348-991aa6745839", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965836801500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d3bda2-9364-4147-8cc0-076a39386116", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21995642425700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d449ac8d-aeee-417c-913c-beaf5d2a8e6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21995644168700, "endTime": 21995644199100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "53585369-4eba-46ad-ba3f-bdd57b6f3172"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53585369-4eba-46ad-ba3f-bdd57b6f3172", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21995644168700, "endTime": 21995644199100}, "additional": {"logType": "info", "children": [], "durationId": "d449ac8d-aeee-417c-913c-beaf5d2a8e6b"}}, {"head": {"id": "3a0adde3-a074-46f2-8201-d4fa312a64d8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997255185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb46a55-f80e-4a63-8af7-1ef02ce097b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997256341200, "endTime": 21997256360600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "59bfc55a-2165-4028-858e-3c7363c8603a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59bfc55a-2165-4028-858e-3c7363c8603a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997256341200, "endTime": 21997256360600}, "additional": {"logType": "info", "children": [], "durationId": "1cb46a55-f80e-4a63-8af7-1ef02ce097b3"}}, {"head": {"id": "ce44bc0a-582c-42ea-a8e3-6d41d9aaa0d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997256447300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b03b5ac-c2fc-43b4-8a4b-6ffda6efd0a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997257306100, "endTime": 21997257321900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e9897203-3687-4f6c-8a0e-bf018579b885"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9897203-3687-4f6c-8a0e-bf018579b885", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997257306100, "endTime": 21997257321900}, "additional": {"logType": "info", "children": [], "durationId": "5b03b5ac-c2fc-43b4-8a4b-6ffda6efd0a8"}}, {"head": {"id": "5b98db0f-226b-4500-8c1b-b2e3cdc610a4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997257387600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad37e6a7-eba4-450a-9aa2-abe9a5212a10", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997258756400, "endTime": 21997258839000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "566d313d-62a1-416e-b7dc-1f0f7eada941"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "566d313d-62a1-416e-b7dc-1f0f7eada941", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997258756400, "endTime": 21997258839000}, "additional": {"logType": "info", "children": [], "durationId": "ad37e6a7-eba4-450a-9aa2-abe9a5212a10"}}, {"head": {"id": "4b9a8535-ba5c-409f-ac24-c4e1b5d617f1", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997493598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312185b0-3855-47b7-b7a2-2587b8eb9a37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997494641000, "endTime": 21997494661300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "b4000e13-312b-405a-94a2-bdaffffd8104"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4000e13-312b-405a-94a2-bdaffffd8104", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21997494641000, "endTime": 21997494661300}, "additional": {"logType": "info", "children": [], "durationId": "312185b0-3855-47b7-b7a2-2587b8eb9a37"}}, {"head": {"id": "2ca33e97-15e2-45a5-b120-4cd6cfebc627", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22015813068800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b85ca69-d84e-40c8-9094-0cd3b3289f67", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22015816086500, "endTime": 22015816120100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c576aae6-7b06-4d89-bd48-2d9864179157"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c576aae6-7b06-4d89-bd48-2d9864179157", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22015816086500, "endTime": 22015816120100}, "additional": {"logType": "info", "children": [], "durationId": "2b85ca69-d84e-40c8-9094-0cd3b3289f67"}}, {"head": {"id": "2e28ff75-558e-492a-939b-ea8e1b27a554", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22017545775400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c32ed8-7161-476b-83e1-0bbdf6c99d15", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22017547003100, "endTime": 22017547023700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a151f1f1-62eb-4f70-b29e-d6c8abd66a8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a151f1f1-62eb-4f70-b29e-d6c8abd66a8a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22017547003100, "endTime": 22017547023700}, "additional": {"logType": "info", "children": [], "durationId": "45c32ed8-7161-476b-83e1-0bbdf6c99d15"}}, {"head": {"id": "88531a10-f201-43fb-9be4-c0e79dd4f4c5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093613781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093633365600, "endTime": 22094125778800}, "additional": {"children": ["78c54127-e8c0-42f6-96a1-f9560b2918db", "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "f0e7218d-68f3-483b-9a34-832409bb4af0", "fe8ccee1-675e-4439-8550-d5a03474e23a", "1e094e95-9181-433f-b850-2cac46d43186", "412b39a0-969a-44ac-ab72-3d8d93508ae2", "da1eb1e1-48d6-4b74-89f5-5e462a675947"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78c54127-e8c0-42f6-96a1-f9560b2918db", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093633368600, "endTime": 22093672237100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "efd03042-474f-47b4-90c0-6f3f54705fe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093672281100, "endTime": 22094117309400}, "additional": {"children": ["383d0f60-3321-443a-97d9-08ee30f91879", "eb908359-e47e-434f-af1c-2f34b0f46185", "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "49b4c661-99bb-4b68-a603-5c6153ac3e5b", "69742570-0707-445a-8412-ca6d1cc45e7d", "5d5843ce-4a9c-4362-846e-6b6e905c2fc9", "b3124bfe-e52d-4559-bfa3-fa4644dde098", "cace3a9c-ee40-4ffc-8398-faffabc5d2a4", "858420c3-92d7-4e98-98d7-55a0e489734b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "3b459733-134a-46ff-9d64-be73ca23bdc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0e7218d-68f3-483b-9a34-832409bb4af0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094117377000, "endTime": 22094125747600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "37687183-13ec-4367-bace-57112ed1e3ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe8ccee1-675e-4439-8550-d5a03474e23a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094125763100, "endTime": 22094125766800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "4b347aa8-3ca4-4315-b98b-4fad3b453615"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e094e95-9181-433f-b850-2cac46d43186", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093642966400, "endTime": 22093643047400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "2bbbe96e-613b-40d2-868f-b4b998cfe996"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bbbe96e-613b-40d2-868f-b4b998cfe996", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093642966400, "endTime": 22093643047400}, "additional": {"logType": "info", "children": [], "durationId": "1e094e95-9181-433f-b850-2cac46d43186", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "412b39a0-969a-44ac-ab72-3d8d93508ae2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093661103300, "endTime": 22093661133600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "d9ae1cf3-aec3-46f9-8e63-03a65d9585da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9ae1cf3-aec3-46f9-8e63-03a65d9585da", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093661103300, "endTime": 22093661133600}, "additional": {"logType": "info", "children": [], "durationId": "412b39a0-969a-44ac-ab72-3d8d93508ae2", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "7721fb30-9e56-43c0-ba44-2f1afffcaa5b", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093661209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2c72a8-1e07-4b37-a860-edb1f41d8551", "name": "Cache service initialization finished in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093672030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efd03042-474f-47b4-90c0-6f3f54705fe0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093633368600, "endTime": 22093672237100}, "additional": {"logType": "info", "children": [], "durationId": "78c54127-e8c0-42f6-96a1-f9560b2918db", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "383d0f60-3321-443a-97d9-08ee30f91879", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093689943800, "endTime": 22093689963000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "3e2dcb79-0069-4238-9b49-a2e540063cb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb908359-e47e-434f-af1c-2f34b0f46185", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093690000200, "endTime": 22093702586800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "cc7f3c08-2994-48be-b885-1ddba400ea62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093702635600, "endTime": 22093947685400}, "additional": {"children": ["aa009a58-cb34-4e6b-b605-a849535968c9", "c19367bc-de7a-4cc0-86f1-95ff5e3d50d2", "36941d89-bc88-4ee4-b250-a47ef6262888"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "38636fb0-1480-4c40-8ca7-3345a82bd4c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49b4c661-99bb-4b68-a603-5c6153ac3e5b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093947714500, "endTime": 22094001433300}, "additional": {"children": ["589928ae-22a2-4d61-b338-7f08951794ae"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "e423c86b-5ef7-4f06-be85-d6b62c8d1bfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69742570-0707-445a-8412-ca6d1cc45e7d", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094001468600, "endTime": 22094059006300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "bb507fca-e430-45aa-b90c-72d016bd8b8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d5843ce-4a9c-4362-846e-6b6e905c2fc9", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094061259400, "endTime": 22094081644000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "5c86c6c9-6639-48be-ac93-4ba2bc402525"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3124bfe-e52d-4559-bfa3-fa4644dde098", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094081685500, "endTime": 22094116953500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "8893f6cf-f56d-476b-bad8-84254c6c822e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cace3a9c-ee40-4ffc-8398-faffabc5d2a4", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094116998400, "endTime": 22094117279400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "68ad3da8-fd1c-46d4-9ea5-21b7ca6e2c55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e2dcb79-0069-4238-9b49-a2e540063cb1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093689943800, "endTime": 22093689963000}, "additional": {"logType": "info", "children": [], "durationId": "383d0f60-3321-443a-97d9-08ee30f91879", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "cc7f3c08-2994-48be-b885-1ddba400ea62", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093690000200, "endTime": 22093702586800}, "additional": {"logType": "info", "children": [], "durationId": "eb908359-e47e-434f-af1c-2f34b0f46185", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "aa009a58-cb34-4e6b-b605-a849535968c9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093704649800, "endTime": 22093704686200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "logId": "d6c5e7d0-a3e0-4d55-87c4-703a7d4d8634"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6c5e7d0-a3e0-4d55-87c4-703a7d4d8634", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093704649800, "endTime": 22093704686200}, "additional": {"logType": "info", "children": [], "durationId": "aa009a58-cb34-4e6b-b605-a849535968c9", "parent": "38636fb0-1480-4c40-8ca7-3345a82bd4c1"}}, {"head": {"id": "c19367bc-de7a-4cc0-86f1-95ff5e3d50d2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093710665000, "endTime": 22093945843200}, "additional": {"children": ["2c76331a-34ff-44cb-8061-3d229919532b", "58e20735-e83c-4391-a914-b3324d7a2651"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "logId": "c83316d8-797a-43f8-a372-47442d86967f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c76331a-34ff-44cb-8061-3d229919532b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093710668000, "endTime": 22093747546100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c19367bc-de7a-4cc0-86f1-95ff5e3d50d2", "logId": "7f2b079e-b047-4223-b5b5-1eca98bfbbb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58e20735-e83c-4391-a914-b3324d7a2651", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093747583100, "endTime": 22093945816800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c19367bc-de7a-4cc0-86f1-95ff5e3d50d2", "logId": "d68ae69a-5144-4621-b8b3-92c2242b67ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f87aa9bc-2960-499b-9a62-c00915d9bf54", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093710676700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79be9983-b2e8-4b55-bc49-149cf2dabd2f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093747251300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2b079e-b047-4223-b5b5-1eca98bfbbb0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093710668000, "endTime": 22093747546100}, "additional": {"logType": "info", "children": [], "durationId": "2c76331a-34ff-44cb-8061-3d229919532b", "parent": "c83316d8-797a-43f8-a372-47442d86967f"}}, {"head": {"id": "4d86de0b-a4ea-48bd-acdf-b709db193e06", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093747617700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e3d5792-a67d-49c5-8894-6740d8db363a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093771440900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19d0884-dde8-4817-9fbd-42d6ff830358", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093771729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d735b5e-b780-4097-b63c-701c4d3fd50d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093772108100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eead9dd2-e932-4731-8f1f-b61a314b0a67", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093772425200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3349f583-74ba-4ba9-b342-afe07dd09944", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093777474200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d314cf15-225f-40bb-8456-01666f328f74", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093790563200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d17ec8b-7eeb-4f2a-af63-5794f0004a6b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093820808600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3986dd8-fd98-44e6-b251-c5246a387e89", "name": "Sdk init in 95 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093888342000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb797240-3acb-46ff-b6b2-2ef9a2dd55f5", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093888643100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 58}, "markType": "other"}}, {"head": {"id": "efe67d15-ff4e-4343-a39d-60bea4f3049f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093888672300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 58}, "markType": "other"}}, {"head": {"id": "70bbc7cc-59d9-4a52-9b41-ebe95250c83d", "name": "Project task initialization takes 54 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093945330400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f65948c-58d2-42a2-a6f3-bc2f0b3d7f87", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093945534500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef713dc-50ed-4729-a6fd-ac36dd2cf042", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093945643200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88af6ba2-176b-48fb-a460-a7682bb474ec", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093945733600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68ae69a-5144-4621-b8b3-92c2242b67ad", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093747583100, "endTime": 22093945816800}, "additional": {"logType": "info", "children": [], "durationId": "58e20735-e83c-4391-a914-b3324d7a2651", "parent": "c83316d8-797a-43f8-a372-47442d86967f"}}, {"head": {"id": "c83316d8-797a-43f8-a372-47442d86967f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093710665000, "endTime": 22093945843200}, "additional": {"logType": "info", "children": ["7f2b079e-b047-4223-b5b5-1eca98bfbbb0", "d68ae69a-5144-4621-b8b3-92c2242b67ad"], "durationId": "c19367bc-de7a-4cc0-86f1-95ff5e3d50d2", "parent": "38636fb0-1480-4c40-8ca7-3345a82bd4c1"}}, {"head": {"id": "36941d89-bc88-4ee4-b250-a47ef6262888", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093947625500, "endTime": 22093947656200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "logId": "0db3a4ef-b591-4d44-a27e-774f5d071126"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0db3a4ef-b591-4d44-a27e-774f5d071126", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093947625500, "endTime": 22093947656200}, "additional": {"logType": "info", "children": [], "durationId": "36941d89-bc88-4ee4-b250-a47ef6262888", "parent": "38636fb0-1480-4c40-8ca7-3345a82bd4c1"}}, {"head": {"id": "38636fb0-1480-4c40-8ca7-3345a82bd4c1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093702635600, "endTime": 22093947685400}, "additional": {"logType": "info", "children": ["d6c5e7d0-a3e0-4d55-87c4-703a7d4d8634", "c83316d8-797a-43f8-a372-47442d86967f", "0db3a4ef-b591-4d44-a27e-774f5d071126"], "durationId": "6ddfbb1d-fdbb-4b81-80b5-2cf4d295150e", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "589928ae-22a2-4d61-b338-7f08951794ae", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093949288100, "endTime": 22094001397000}, "additional": {"children": ["547ae0be-94aa-416d-ac04-d2983a138074", "24eecf40-88a1-4091-a71d-88376dacaf9f", "5978fc3a-4c44-459b-a89e-9243c5f9ab77"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b4c661-99bb-4b68-a603-5c6153ac3e5b", "logId": "b9ccd6c2-efdc-44d5-bfc8-fab1814931cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "547ae0be-94aa-416d-ac04-d2983a138074", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093958201000, "endTime": 22093958238800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "589928ae-22a2-4d61-b338-7f08951794ae", "logId": "99c19d99-f28f-4420-8ac8-9adf8e72d75b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99c19d99-f28f-4420-8ac8-9adf8e72d75b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093958201000, "endTime": 22093958238800}, "additional": {"logType": "info", "children": [], "durationId": "547ae0be-94aa-416d-ac04-d2983a138074", "parent": "b9ccd6c2-efdc-44d5-bfc8-fab1814931cc"}}, {"head": {"id": "24eecf40-88a1-4091-a71d-88376dacaf9f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093962802900, "endTime": 22093997712000}, "additional": {"children": ["ee04b047-6831-40fc-a363-3e1927215c8c", "a0ff534f-0c56-43a5-8c3d-c55aee4863c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "589928ae-22a2-4d61-b338-7f08951794ae", "logId": "2e801aa7-e406-4ecf-a6c1-752359c9b7ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee04b047-6831-40fc-a363-3e1927215c8c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093962805800, "endTime": 22093968765100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24eecf40-88a1-4091-a71d-88376dacaf9f", "logId": "b41ed196-799c-431d-9da0-14158d8c73a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0ff534f-0c56-43a5-8c3d-c55aee4863c4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093968808700, "endTime": 22093997651300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24eecf40-88a1-4091-a71d-88376dacaf9f", "logId": "939fe636-69e7-4885-881b-69310c927f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15806935-ccd8-4d3d-8eaf-15d6ed63fb53", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093962813600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cf7f64e-5e74-4061-a015-89e1fd1d44d5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093968554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41ed196-799c-431d-9da0-14158d8c73a3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093962805800, "endTime": 22093968765100}, "additional": {"logType": "info", "children": [], "durationId": "ee04b047-6831-40fc-a363-3e1927215c8c", "parent": "2e801aa7-e406-4ecf-a6c1-752359c9b7ed"}}, {"head": {"id": "6b8e2bda-82cf-4aeb-8764-e2447d67c760", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093968837000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af76069f-efdc-4608-87f0-746140ca6656", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093983382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade1d4e0-3b4e-4a0e-8b91-07adf5b71877", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093983598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08586276-eda2-4c3e-bbc7-39875c49a699", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093985893500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e231f7c0-ffab-4996-9cc0-f3d70e232806", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093986345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c652bbc-b61e-497f-82e3-f2e371af665b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093986477800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b491c3a6-c703-4960-80b1-df79efa18e53", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093986578700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b69634-403a-4fe0-bd1b-0314df037d9e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093986680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75387c92-09c2-46b0-aa2f-060ac54cf37d", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093996727100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e68efc3b-8162-411b-b8f2-dcde6938f4a3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093997087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e01082-6293-4409-ae67-1be3cfe7bc5f", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093997286400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34f657d-2603-4228-95cd-520ea8934d33", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093997486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939fe636-69e7-4885-881b-69310c927f66", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093968808700, "endTime": 22093997651300}, "additional": {"logType": "info", "children": [], "durationId": "a0ff534f-0c56-43a5-8c3d-c55aee4863c4", "parent": "2e801aa7-e406-4ecf-a6c1-752359c9b7ed"}}, {"head": {"id": "2e801aa7-e406-4ecf-a6c1-752359c9b7ed", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093962802900, "endTime": 22093997712000}, "additional": {"logType": "info", "children": ["b41ed196-799c-431d-9da0-14158d8c73a3", "939fe636-69e7-4885-881b-69310c927f66"], "durationId": "24eecf40-88a1-4091-a71d-88376dacaf9f", "parent": "b9ccd6c2-efdc-44d5-bfc8-fab1814931cc"}}, {"head": {"id": "5978fc3a-4c44-459b-a89e-9243c5f9ab77", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094001318800, "endTime": 22094001356600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "589928ae-22a2-4d61-b338-7f08951794ae", "logId": "d5af9d76-ba7e-4be9-a380-8e2b2e6a9eeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5af9d76-ba7e-4be9-a380-8e2b2e6a9eeb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094001318800, "endTime": 22094001356600}, "additional": {"logType": "info", "children": [], "durationId": "5978fc3a-4c44-459b-a89e-9243c5f9ab77", "parent": "b9ccd6c2-efdc-44d5-bfc8-fab1814931cc"}}, {"head": {"id": "b9ccd6c2-efdc-44d5-bfc8-fab1814931cc", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093949288100, "endTime": 22094001397000}, "additional": {"logType": "info", "children": ["99c19d99-f28f-4420-8ac8-9adf8e72d75b", "2e801aa7-e406-4ecf-a6c1-752359c9b7ed", "d5af9d76-ba7e-4be9-a380-8e2b2e6a9eeb"], "durationId": "589928ae-22a2-4d61-b338-7f08951794ae", "parent": "e423c86b-5ef7-4f06-be85-d6b62c8d1bfa"}}, {"head": {"id": "e423c86b-5ef7-4f06-be85-d6b62c8d1bfa", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093947714500, "endTime": 22094001433300}, "additional": {"logType": "info", "children": ["b9ccd6c2-efdc-44d5-bfc8-fab1814931cc"], "durationId": "49b4c661-99bb-4b68-a603-5c6153ac3e5b", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "5ca6835c-5118-44f2-8958-e084720ead21", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094057710900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a17fdc8-8d31-422d-a628-087a54c80dba", "name": "hvigorfile, resolve hvigorfile dependencies in 57 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094058305600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb507fca-e430-45aa-b90c-72d016bd8b8c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094001468600, "endTime": 22094059006300}, "additional": {"logType": "info", "children": [], "durationId": "69742570-0707-445a-8412-ca6d1cc45e7d", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "858420c3-92d7-4e98-98d7-55a0e489734b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094060824600, "endTime": 22094061226900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "logId": "732d2ef2-48d6-4f72-80c2-47fa49785ec4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c2812db-7ff6-4cb7-a25b-bebe8d82e393", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094060863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732d2ef2-48d6-4f72-80c2-47fa49785ec4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094060824600, "endTime": 22094061226900}, "additional": {"logType": "info", "children": [], "durationId": "858420c3-92d7-4e98-98d7-55a0e489734b", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "b0efe391-1e12-487c-b31d-e895cef61adf", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094064373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ffeb0de-de48-4880-91b6-e3e745ff2045", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094079437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c86c6c9-6639-48be-ac93-4ba2bc402525", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094061259400, "endTime": 22094081644000}, "additional": {"logType": "info", "children": [], "durationId": "5d5843ce-4a9c-4362-846e-6b6e905c2fc9", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "83d9775c-5f3c-4daf-879b-74f60153d160", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094081708400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af583295-5c13-4988-a517-e57d50cd5156", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094100296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "853d2e94-ce5f-4dd0-9f46-2f30458018ee", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094100575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f590052f-d2d9-4ca2-a125-9df6a766e5f3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094101284600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7700dc14-5c87-47af-b8ca-687dd39c5fbd", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094108372000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29893bbb-273d-4bec-9a58-3d0bbee85eb0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094108562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8893f6cf-f56d-476b-bad8-84254c6c822e", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094081685500, "endTime": 22094116953500}, "additional": {"logType": "info", "children": [], "durationId": "b3124bfe-e52d-4559-bfa3-fa4644dde098", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "78b7b392-b92d-486c-b7ab-62c7a95e222f", "name": "Configuration phase cost:428 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094117054800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ad3da8-fd1c-46d4-9ea5-21b7ca6e2c55", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094116998400, "endTime": 22094117279400}, "additional": {"logType": "info", "children": [], "durationId": "cace3a9c-ee40-4ffc-8398-faffabc5d2a4", "parent": "3b459733-134a-46ff-9d64-be73ca23bdc8"}}, {"head": {"id": "3b459733-134a-46ff-9d64-be73ca23bdc8", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093672281100, "endTime": 22094117309400}, "additional": {"logType": "info", "children": ["3e2dcb79-0069-4238-9b49-a2e540063cb1", "cc7f3c08-2994-48be-b885-1ddba400ea62", "38636fb0-1480-4c40-8ca7-3345a82bd4c1", "e423c86b-5ef7-4f06-be85-d6b62c8d1bfa", "bb507fca-e430-45aa-b90c-72d016bd8b8c", "5c86c6c9-6639-48be-ac93-4ba2bc402525", "8893f6cf-f56d-476b-bad8-84254c6c822e", "68ad3da8-fd1c-46d4-9ea5-21b7ca6e2c55", "732d2ef2-48d6-4f72-80c2-47fa49785ec4"], "durationId": "3d5ed9ab-ac24-40a4-b80b-8b06984af287", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "da1eb1e1-48d6-4b74-89f5-5e462a675947", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094125662500, "endTime": 22094125702000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d", "logId": "3923a2bf-fe93-46ac-9c15-607f7a3e0ca3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3923a2bf-fe93-46ac-9c15-607f7a3e0ca3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094125662500, "endTime": 22094125702000}, "additional": {"logType": "info", "children": [], "durationId": "da1eb1e1-48d6-4b74-89f5-5e462a675947", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "37687183-13ec-4367-bace-57112ed1e3ca", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094117377000, "endTime": 22094125747600}, "additional": {"logType": "info", "children": [], "durationId": "f0e7218d-68f3-483b-9a34-832409bb4af0", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "4b347aa8-3ca4-4315-b98b-4fad3b453615", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094125763100, "endTime": 22094125766800}, "additional": {"logType": "info", "children": [], "durationId": "fe8ccee1-675e-4439-8550-d5a03474e23a", "parent": "cb417be0-a57e-487d-b4d4-6b1b7255ec17"}}, {"head": {"id": "cb417be0-a57e-487d-b4d4-6b1b7255ec17", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093633365600, "endTime": 22094125778800}, "additional": {"logType": "info", "children": ["efd03042-474f-47b4-90c0-6f3f54705fe0", "3b459733-134a-46ff-9d64-be73ca23bdc8", "37687183-13ec-4367-bace-57112ed1e3ca", "4b347aa8-3ca4-4315-b98b-4fad3b453615", "2bbbe96e-613b-40d2-868f-b4b998cfe996", "d9ae1cf3-aec3-46f9-8e63-03a65d9585da", "3923a2bf-fe93-46ac-9c15-607f7a3e0ca3"], "durationId": "8ea8763c-5a11-49bd-bdcc-2911f7b5e85d"}}, {"head": {"id": "f6576f1b-88c7-48ea-8310-38c4b871e03c", "name": "Configuration task cost before running: 503 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094126138600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc4ec151-7c9e-4828-bf15-36c28b4e4d01", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094163499700, "endTime": 22094196630600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "95cd6bc9-84a5-46d5-85ef-33d83b99a566", "logId": "38f9097b-24ac-45a9-b46a-1e791b0958cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95cd6bc9-84a5-46d5-85ef-33d83b99a566", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094131283100}, "additional": {"logType": "detail", "children": [], "durationId": "fc4ec151-7c9e-4828-bf15-36c28b4e4d01"}}, {"head": {"id": "24e07a22-9523-411f-8578-0a9828dc13ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094132876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bf4075-512d-4fea-b97c-6c967051781c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094133170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0780480-048d-4bc9-8618-0b699c31fa3e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094163531700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1e7e04e-2b9f-4e91-bf75-6a3f04224b01", "name": "Incremental task entry:default@PreBuild pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094196094400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "359a317b-128f-4827-a0f7-aba1bbc4b79d", "name": "entry : default@PreBuild cost memory 0.2599945068359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094196397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f9097b-24ac-45a9-b46a-1e791b0958cd", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094163499700, "endTime": 22094196630600}, "additional": {"logType": "info", "children": [], "durationId": "fc4ec151-7c9e-4828-bf15-36c28b4e4d01"}}, {"head": {"id": "9ade9e77-38a4-483b-bdc2-3ea45459b635", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094230105500, "endTime": 22094236201900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "71ccf514-89f2-46ea-90e8-5d7bb51a576c", "logId": "3d4cd3f0-a107-4592-a1ca-25054ce0fe1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ccf514-89f2-46ea-90e8-5d7bb51a576c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094209852500}, "additional": {"logType": "detail", "children": [], "durationId": "9ade9e77-38a4-483b-bdc2-3ea45459b635"}}, {"head": {"id": "198e8ead-5334-4dfc-95b2-3ddce6aee7ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094226796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe03214-6aec-4e81-bbea-2452f68d7a79", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094227065100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "398760fe-dec6-4478-bf9c-f8e5d6465ddf", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094230136500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a116a328-c006-4336-9d02-655b187742f8", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094235722600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed971727-8d22-4dd8-b74e-73d57b7a4c4d", "name": "entry : default@MergeProfile cost memory 0.10584259033203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094236002500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d4cd3f0-a107-4592-a1ca-25054ce0fe1b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094230105500, "endTime": 22094236201900}, "additional": {"logType": "info", "children": [], "durationId": "9ade9e77-38a4-483b-bdc2-3ea45459b635"}}, {"head": {"id": "299cf0dc-7e19-4f07-a933-2be34d720462", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094244812000, "endTime": 22094253879000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9bed77d6-c8cb-41d6-b913-9cac8e47548a", "logId": "bf5cb254-66e3-4de4-a01a-1a6ac9e9b4d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bed77d6-c8cb-41d6-b913-9cac8e47548a", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094240861200}, "additional": {"logType": "detail", "children": [], "durationId": "299cf0dc-7e19-4f07-a933-2be34d720462"}}, {"head": {"id": "69610197-b15a-4dca-a578-d771428534b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094242256700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c7839ef-3836-414e-a6b4-eeedf83bc97c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094242458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f60b123-9af0-4267-9ced-7fced1708e63", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094244836600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d198fb87-55ef-44ca-89fa-da733f714d8d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094249073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bc179ba-9b18-4747-a81f-502144e7846a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094253299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1181ab09-e51c-468a-821e-9b466dd16ef9", "name": "entry : default@CreateBuildProfile cost memory -1.651031494140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094253691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf5cb254-66e3-4de4-a01a-1a6ac9e9b4d3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094244812000, "endTime": 22094253879000}, "additional": {"logType": "info", "children": [], "durationId": "299cf0dc-7e19-4f07-a933-2be34d720462"}}, {"head": {"id": "13d16f5e-140c-4efa-b471-83be112a2f0f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094261322800, "endTime": 22094262472100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e6eb0f16-5282-4576-8f49-46e9319d01cd", "logId": "6ca89c44-c7eb-4072-b96b-01102fc82465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6eb0f16-5282-4576-8f49-46e9319d01cd", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094257443600}, "additional": {"logType": "detail", "children": [], "durationId": "13d16f5e-140c-4efa-b471-83be112a2f0f"}}, {"head": {"id": "22d546db-3a01-4837-9866-e3f1feb41131", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094258568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b6c27d7-5334-4b58-ac57-780413de264d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094258806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65cd4687-d693-41f5-aadd-d033fddb2a51", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094261349200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc7bfe3-7e27-4296-8160-0286f91f6ae1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094261650800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd4c93f-99a4-4a85-89ff-d536312c157c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094261827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa261b5-2682-48d5-b9f6-e5d25bab66eb", "name": "entry : default@PreCheckSyscap cost memory 0.03670501708984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094262057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ababce2-6264-4291-b951-ab546d242cb3", "name": "runTaskFromQueue task cost before running: 640 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094262294200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca89c44-c7eb-4072-b96b-01102fc82465", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094261322800, "endTime": 22094262472100, "totalTime": 928500}, "additional": {"logType": "info", "children": [], "durationId": "13d16f5e-140c-4efa-b471-83be112a2f0f"}}, {"head": {"id": "32e7ad2a-60da-4395-95a3-ad07acd71f59", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094288269600, "endTime": 22094290725300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9b1a9483-4049-4af9-95c4-ff96ee91bc36", "logId": "5442960f-2036-4ad8-8605-93ba53a65373"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b1a9483-4049-4af9-95c4-ff96ee91bc36", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094267202900}, "additional": {"logType": "detail", "children": [], "durationId": "32e7ad2a-60da-4395-95a3-ad07acd71f59"}}, {"head": {"id": "91a23d71-f83a-4ed5-908d-05805830f4ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094268779200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1af8030-4796-4d2b-9905-30594f725871", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094268977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d83f522-00d7-43b6-b9bd-1370c4826ad2", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094288292700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e2f0dd-387a-4ce4-8190-6452134fafff", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094288679100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2cf753-3f1a-467c-a652-2d1f05d38579", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094290380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e7c55d-673d-48d2-b86f-e284e33717d9", "name": "entry : default@GeneratePkgContextInfo cost memory 0.074066162109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094290581700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5442960f-2036-4ad8-8605-93ba53a65373", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094288269600, "endTime": 22094290725300}, "additional": {"logType": "info", "children": [], "durationId": "32e7ad2a-60da-4395-95a3-ad07acd71f59"}}, {"head": {"id": "d1427b65-911a-447a-b149-a4b31c4d67f8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094298435700, "endTime": 22094301170900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4f7519dd-53fa-4a3d-a3e9-3a8bf0de82ae", "logId": "7ec148b3-972e-45cb-91f3-afe6b161d3e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f7519dd-53fa-4a3d-a3e9-3a8bf0de82ae", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094294561100}, "additional": {"logType": "detail", "children": [], "durationId": "d1427b65-911a-447a-b149-a4b31c4d67f8"}}, {"head": {"id": "b95345dd-2335-42a0-b3ed-f399d066472b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094295651600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7081efe6-f438-44ce-af8d-0b2c2fa82868", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094295833100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b47d11aa-bf4e-4b48-9113-350cd2db979b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094298452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0117911-3d1e-4942-bb16-e0f0782e4713", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094300674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8a3c82-eab3-482b-b85a-f8d056c43922", "name": "entry : default@ProcessProfile cost memory 0.05455780029296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094300887600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec148b3-972e-45cb-91f3-afe6b161d3e1", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094298435700, "endTime": 22094301170900}, "additional": {"logType": "info", "children": [], "durationId": "d1427b65-911a-447a-b149-a4b31c4d67f8"}}, {"head": {"id": "ec8603f5-0331-4723-9d3b-ba57412341f0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094311178200, "endTime": 22094326113700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2c3584b3-fdae-4073-9b36-b2b99a20e301", "logId": "9e7d6e77-8210-4a84-9218-c468384ebeb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c3584b3-fdae-4073-9b36-b2b99a20e301", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094305422000}, "additional": {"logType": "detail", "children": [], "durationId": "ec8603f5-0331-4723-9d3b-ba57412341f0"}}, {"head": {"id": "bd0e1e47-1ff3-47e4-ad68-8ab651015437", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094306510100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5929abfd-2c23-4ed4-b6cc-2649125e023c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094306695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9b21ae4-8985-44e1-9edf-86c8e8111a20", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094311209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c721d8a1-4ec3-4fa7-8653-ab10ee78887e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094325759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7149b0b-3629-46dd-9b62-a7f000c5cb7c", "name": "entry : default@ProcessRouterMap cost memory 0.1844024658203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094325983600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7d6e77-8210-4a84-9218-c468384ebeb1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094311178200, "endTime": 22094326113700}, "additional": {"logType": "info", "children": [], "durationId": "ec8603f5-0331-4723-9d3b-ba57412341f0"}}, {"head": {"id": "499505e9-d7de-4c1d-a57b-5e35a768fb93", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094341944100, "endTime": 22094347748200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "79392d1f-2cf5-4b91-8f76-d3322f1cbc17", "logId": "dca7f159-cf8e-4d78-a92c-849d9299866b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79392d1f-2cf5-4b91-8f76-d3322f1cbc17", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094332411400}, "additional": {"logType": "detail", "children": [], "durationId": "499505e9-d7de-4c1d-a57b-5e35a768fb93"}}, {"head": {"id": "67561b1d-7018-475a-b684-4cb2cd1df51c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094333469300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d24be9-96d8-43bf-bdac-ba6044340d79", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094333641000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111126da-0450-46d2-8ed7-3468e7d4a5a2", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094337414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a61c6ae1-b16c-4279-b8c4-9e1d835fac32", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094344430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c92a03-795e-4a36-be6f-be10a751434d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094344676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2b784b-eccf-4dec-873b-f903cbd6260c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094344789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5ecc313-1dc2-45ae-9006-0ebd605448fc", "name": "entry : default@PreviewProcessResource cost memory 0.06757354736328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094344932800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbf8860d-b983-4188-a675-5cef27028a75", "name": "runTaskFromQueue task cost before running: 725 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094347580000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca7f159-cf8e-4d78-a92c-849d9299866b", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094341944100, "endTime": 22094347748200, "totalTime": 3104800}, "additional": {"logType": "info", "children": [], "durationId": "499505e9-d7de-4c1d-a57b-5e35a768fb93"}}, {"head": {"id": "64f9b7d4-e2b4-49a9-ba9f-b0acd9010478", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094362377400, "endTime": 22094411196300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "69f62603-5a8e-4177-93ef-6d2f70ad6fc7", "logId": "d08d58fd-65d2-4eee-99b1-95d04fc5094e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69f62603-5a8e-4177-93ef-6d2f70ad6fc7", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094353976700}, "additional": {"logType": "detail", "children": [], "durationId": "64f9b7d4-e2b4-49a9-ba9f-b0acd9010478"}}, {"head": {"id": "243e4648-d80f-4dbc-832e-79be022587ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094355076200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c49cef-22cd-4698-914f-4941bace933f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094355261300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccb2e1e-7c95-452e-8e1a-c88de0884055", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094362405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c00fa022-274b-4faf-be84-90a1cfbf580f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094410776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6f0d15-4fc9-44a5-9748-33fe875371ef", "name": "entry : default@GenerateLoaderJson cost memory 0.7033843994140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094411039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d08d58fd-65d2-4eee-99b1-95d04fc5094e", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094362377400, "endTime": 22094411196300}, "additional": {"logType": "info", "children": [], "durationId": "64f9b7d4-e2b4-49a9-ba9f-b0acd9010478"}}, {"head": {"id": "1c43e3a7-8745-443a-8358-93b7805ee120", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094439730700, "endTime": 22094977737000}, "additional": {"children": ["aa637cf6-2632-4222-ae4c-2a64ef1a0688", "f95861bb-82c0-4b06-aa7d-255dc11bccff", "996d4c52-25d7-47f5-a63e-9d4a54ea39ad", "f08dcbaf-dc2f-4a38-ac15-7f29cf5d51df"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "7846f56c-c3b9-4999-8cf9-e03cabb12a97", "logId": "1cc97da1-d34d-486a-b221-68cda1af259b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7846f56c-c3b9-4999-8cf9-e03cabb12a97", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094430454100}, "additional": {"logType": "detail", "children": [], "durationId": "1c43e3a7-8745-443a-8358-93b7805ee120"}}, {"head": {"id": "d3c29241-ed7d-4eb5-8de3-e638a2f04684", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094431618500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752889e4-ea98-4e94-954b-46d5d83116a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094431793200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3a1cb4-8d39-4303-b6bc-ed490adcc7b8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094433896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2a1079-8b43-42d0-9310-9aff63e4063b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094439783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ef8703-bc62-414a-95e5-160ebac434bd", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094524383300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678a8ba9-7197-4422-9c38-8dfa6f323b40", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 84 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094524629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa637cf6-2632-4222-ae4c-2a64ef1a0688", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094526929700, "endTime": 22094586272200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c43e3a7-8745-443a-8358-93b7805ee120", "logId": "a4c0cf24-3f60-4787-b0d0-b9763cec8697"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4c0cf24-3f60-4787-b0d0-b9763cec8697", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094526929700, "endTime": 22094586272200}, "additional": {"logType": "info", "children": [], "durationId": "aa637cf6-2632-4222-ae4c-2a64ef1a0688", "parent": "1cc97da1-d34d-486a-b221-68cda1af259b"}}, {"head": {"id": "8b4a1ac6-eb89-4e47-a8e2-339a4aedda1a", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094586538800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95861bb-82c0-4b06-aa7d-255dc11bccff", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094588899800, "endTime": 22094652478600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c43e3a7-8745-443a-8358-93b7805ee120", "logId": "fcf47653-3cd1-442c-a97f-3e04d1e338c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1541160-7e94-4779-8e21-db50c49d0557", "name": "current process  memoryUsage: {\n  rss: 469925888,\n  heapTotal: 126705664,\n  heapUsed: 115523440,\n  external: 3149976,\n  arrayBuffers: 143841\n} os memoryUsage :6.760501861572266", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094591125700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602f73d8-3e6d-47e0-b5fb-4b0739f35192", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094646579700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf47653-3cd1-442c-a97f-3e04d1e338c7", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094588899800, "endTime": 22094652478600}, "additional": {"logType": "info", "children": [], "durationId": "f95861bb-82c0-4b06-aa7d-255dc11bccff", "parent": "1cc97da1-d34d-486a-b221-68cda1af259b"}}, {"head": {"id": "fdc4bfd7-2635-4e96-83ca-8a380caca5d1", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094652737500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996d4c52-25d7-47f5-a63e-9d4a54ea39ad", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094655220200, "endTime": 22094764156300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c43e3a7-8745-443a-8358-93b7805ee120", "logId": "b9e09d9a-3a17-4bc1-9d46-4f9e3879719f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86b6ba6d-17d3-4718-9691-0bb890573fc8", "name": "current process  memoryUsage: {\n  rss: 470024192,\n  heapTotal: 126705664,\n  heapUsed: 115779328,\n  external: 3150102,\n  arrayBuffers: 143982\n} os memoryUsage :6.7763671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094657121400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4689b295-3291-4707-9376-c9438706d9cc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094746793200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cebc0e-8297-4b34-ab32-5713cffd6455", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094756227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32589dac-e798-4d44-ae26-0307fd4b4dd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094758835500, "endTime": 22094758875900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7e505fd1-4039-4ab9-8490-0a0b99379f06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e505fd1-4039-4ab9-8490-0a0b99379f06", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094758835500, "endTime": 22094758875900}, "additional": {"logType": "info", "children": [], "durationId": "32589dac-e798-4d44-ae26-0307fd4b4dd1"}}, {"head": {"id": "b9e09d9a-3a17-4bc1-9d46-4f9e3879719f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094655220200, "endTime": 22094764156300}, "additional": {"logType": "info", "children": [], "durationId": "996d4c52-25d7-47f5-a63e-9d4a54ea39ad", "parent": "1cc97da1-d34d-486a-b221-68cda1af259b"}}, {"head": {"id": "f1f1984b-7007-4ca5-ac12-54aa963b2b5e", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094764507800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f08dcbaf-dc2f-4a38-ac15-7f29cf5d51df", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094766824500, "endTime": 22094975287600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1c43e3a7-8745-443a-8358-93b7805ee120", "logId": "3c82c35d-80c7-491e-81d0-e09e78388a0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9229131-b2e7-454d-afc9-c98188ab313d", "name": "current process  memoryUsage: {\n  rss: 470220800,\n  heapTotal: 126705664,\n  heapUsed: 116148648,\n  external: 3150228,\n  arrayBuffers: 144922\n} os memoryUsage :6.750812530517578", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094769316500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48ff84b-cad3-48d7-8448-f2720b040b97", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094966986200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c82c35d-80c7-491e-81d0-e09e78388a0f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094766824500, "endTime": 22094975287600}, "additional": {"logType": "info", "children": [], "durationId": "f08dcbaf-dc2f-4a38-ac15-7f29cf5d51df", "parent": "1cc97da1-d34d-486a-b221-68cda1af259b"}}, {"head": {"id": "170f0325-107c-4589-b9bb-b2b09fe389f5", "name": "entry : default@PreviewCompileResource cost memory 0.0428314208984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094977117400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20edf1a-6e0d-462a-a441-6400a99f5b74", "name": "runTaskFromQueue task cost before running: 1 s 355 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094977522200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc97da1-d34d-486a-b221-68cda1af259b", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094439730700, "endTime": 22094977737000, "totalTime": 537671600}, "additional": {"logType": "info", "children": ["a4c0cf24-3f60-4787-b0d0-b9763cec8697", "fcf47653-3cd1-442c-a97f-3e04d1e338c7", "b9e09d9a-3a17-4bc1-9d46-4f9e3879719f", "3c82c35d-80c7-491e-81d0-e09e78388a0f"], "durationId": "1c43e3a7-8745-443a-8358-93b7805ee120"}}, {"head": {"id": "d23c4795-ee6b-49e1-8aca-3416ebdea0b4", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094987960800, "endTime": 22094988661200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a8dbf6ad-4e2e-451c-b5bd-94957dd2c984", "logId": "757cf3a3-5de8-4c7d-b352-5f8132424b18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8dbf6ad-4e2e-451c-b5bd-94957dd2c984", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094986339400}, "additional": {"logType": "detail", "children": [], "durationId": "d23c4795-ee6b-49e1-8aca-3416ebdea0b4"}}, {"head": {"id": "a7070969-e5de-4251-be69-339b17087203", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094987589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea00af03-04aa-4230-9d5f-f94a85c226e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094987792800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec06071c-fb43-435c-8682-114b717ae427", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094987977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ece64bf-5d82-4652-a33c-4011f3672378", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094988146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68cfc39-f09f-4269-ae38-038f482e3e82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094988242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7808c144-0d73-4222-9687-0fa3ec7d7dee", "name": "entry : default@PreviewHookCompileResource cost memory 0.03781890869140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094988364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a78e42-16d0-45f9-bf03-4fe7868d53d7", "name": "runTaskFromQueue task cost before running: 1 s 366 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094988552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757cf3a3-5de8-4c7d-b352-5f8132424b18", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094987960800, "endTime": 22094988661200, "totalTime": 546200}, "additional": {"logType": "info", "children": [], "durationId": "d23c4795-ee6b-49e1-8aca-3416ebdea0b4"}}, {"head": {"id": "c0225bce-1be6-4d79-9806-7d70d68491ca", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094995554300, "endTime": 22095013760700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "6fafcd72-8461-4b18-885e-b483c247a220", "logId": "466599b7-d922-4cc7-a8c4-1b9ff2a909c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fafcd72-8461-4b18-885e-b483c247a220", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094992521200}, "additional": {"logType": "detail", "children": [], "durationId": "c0225bce-1be6-4d79-9806-7d70d68491ca"}}, {"head": {"id": "85dfea46-8e5d-4ac6-8c7d-8be1f932c46f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094993693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfe04025-f4fa-4767-be04-77222393c0e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094993920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "878232a7-71e2-4c54-81b4-4a4f241561bb", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094995574500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dff58d1-5ef9-4d50-9751-1a69cb0ae5c7", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094998899700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61743b7-66e8-4c27-8d0c-cbc98854d130", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094999111200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a59b4c71-799a-46c7-9bac-c01aefdda80e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094999277600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1809fd71-75ba-412d-a3c8-ac52e2915a25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094999381800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c576aed7-91be-4dbb-8aef-ff8f98b3b1c9", "name": "entry : default@CopyPreviewProfile cost memory 0.2026824951171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095013311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20cde730-806c-4bcc-a1e9-511cfa48dc14", "name": "runTaskFromQueue task cost before running: 1 s 391 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095013614800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "466599b7-d922-4cc7-a8c4-1b9ff2a909c7", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22094995554300, "endTime": 22095013760700, "totalTime": 18005300}, "additional": {"logType": "info", "children": [], "durationId": "c0225bce-1be6-4d79-9806-7d70d68491ca"}}, {"head": {"id": "9ae268fb-86c6-4934-8750-681784d9f61d", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095021575800, "endTime": 22095022754400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "714cb7cf-5937-4ff4-a789-76b001106781", "logId": "b92dc2da-9c3c-4d70-bb2b-aa2a84584168"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "714cb7cf-5937-4ff4-a789-76b001106781", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095018518700}, "additional": {"logType": "detail", "children": [], "durationId": "9ae268fb-86c6-4934-8750-681784d9f61d"}}, {"head": {"id": "6ae88592-0cd9-403a-8726-6056b6a76f86", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095019672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05665073-31b4-4cdd-84c4-83b0a9cdb35a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095019860000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cbb2e40-da82-49da-b3f8-91da3a9996e3", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095021595000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1a6b1f-89ac-46bc-80e9-03a090b3ad5b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095021848300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8982fd3-ec00-434a-aa61-d2225ae3ba46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095022033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f6a78b-104c-4d17-966e-747663ebed09", "name": "entry : default@ReplacePreviewerPage cost memory 0.04986572265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095022327400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307801a4-f381-45cf-838b-247e80d55f3b", "name": "runTaskFromQueue task cost before running: 1 s 400 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095022579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92dc2da-9c3c-4d70-bb2b-aa2a84584168", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095021575800, "endTime": 22095022754400, "totalTime": 951100}, "additional": {"logType": "info", "children": [], "durationId": "9ae268fb-86c6-4934-8750-681784d9f61d"}}, {"head": {"id": "bcf10c56-19e1-4602-8a1d-49bbd112acd5", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095027042100, "endTime": 22095027746300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4e0a26ba-de1f-42fc-9e59-c1289653e731", "logId": "41a96bd6-ed6d-4743-9426-daa9c246a70c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e0a26ba-de1f-42fc-9e59-c1289653e731", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095026964800}, "additional": {"logType": "detail", "children": [], "durationId": "bcf10c56-19e1-4602-8a1d-49bbd112acd5"}}, {"head": {"id": "68eab179-d814-46d8-93bf-bdcde3243e74", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095027059400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acb2674-f924-4517-84fa-370ccb6864d2", "name": "entry : buildPreviewerResource cost memory 0.0116119384765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095027298400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae204fb-8b4c-4071-aa83-47e70631c975", "name": "runTaskFromQueue task cost before running: 1 s 405 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095027556800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a96bd6-ed6d-4743-9426-daa9c246a70c", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095027042100, "endTime": 22095027746300, "totalTime": 455100}, "additional": {"logType": "info", "children": [], "durationId": "bcf10c56-19e1-4602-8a1d-49bbd112acd5"}}, {"head": {"id": "ce553e61-9d10-4fa4-a183-b75a7b3918ed", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095036935300, "endTime": 22095044639700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "e209bc8f-e33c-442a-871c-2736e395ef87", "logId": "5be64e20-c6b9-4a5a-b827-57ae15e51522"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e209bc8f-e33c-442a-871c-2736e395ef87", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095032658500}, "additional": {"logType": "detail", "children": [], "durationId": "ce553e61-9d10-4fa4-a183-b75a7b3918ed"}}, {"head": {"id": "3076e121-8da2-4424-bda9-f3d9c51dad06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095034188500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9494e4d-37f1-4ee9-a336-485e97c90217", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095034457100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37927d01-2919-4bac-8f24-8e5cf640c870", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095036969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e31ed504-ce48-4000-88b0-b33fd1579a67", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095041620100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c296f199-9c5a-47b9-8764-9e2af4fceda7", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095041840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c377a9-9cbc-4728-b7d1-ebd9e20948d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095042080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a48ccb-b2b1-4443-b709-f46c0d38f4ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095042252000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1a4b1a-e489-487d-91ad-d5be0afe18bd", "name": "entry : default@PreviewUpdateAssets cost memory 0.13834381103515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095044154300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f29e69-2acd-46e3-86cb-3490c547c8d9", "name": "runTaskFromQueue task cost before running: 1 s 422 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095044458400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be64e20-c6b9-4a5a-b827-57ae15e51522", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095036935300, "endTime": 22095044639700, "totalTime": 7471700}, "additional": {"logType": "info", "children": [], "durationId": "ce553e61-9d10-4fa4-a183-b75a7b3918ed"}}, {"head": {"id": "86c2a0ce-864c-4018-a92a-3c5044ace318", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095067840800, "endTime": 22119523852700}, "additional": {"children": ["8a176aa2-3c01-4271-a9d0-91558f442aa7"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33e7bd4f-4db4-44f4-875c-a6f1a7bf4c9e", "logId": "c3b8a713-a891-4a28-b265-b3b17d5f7392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33e7bd4f-4db4-44f4-875c-a6f1a7bf4c9e", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095050881800}, "additional": {"logType": "detail", "children": [], "durationId": "86c2a0ce-864c-4018-a92a-3c5044ace318"}}, {"head": {"id": "c85f1fa1-d82b-49f4-861b-b3f0e72e47bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095052516900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90e2326-e079-4ff7-8ff5-219be47f257b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095052716000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88484be5-f47f-49ce-94ec-f12d3e4db8f6", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095067875700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22095137008000, "endTime": 22119520497800}, "additional": {"children": ["57072b08-1e6c-4b25-b5fd-f58598de0728", "d698e1ae-1a7d-4e18-a43b-3cad8c05040f", "efc0f059-e75c-4837-bb59-f2550d30e620", "22645316-06cb-4302-af53-e7f8ebb3bed4", "b4822fef-d361-42ba-8d04-eb3926cdc951"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "86c2a0ce-864c-4018-a92a-3c5044ace318", "logId": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "690a5c0e-b1b0-41c7-8fb7-bdd3d2e597f2", "name": "entry : default@PreviewArkTS cost memory -0.7636566162109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095142014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b73c516-f1d2-4ae8-8de5-e1ed73a70587", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096348574800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91812ad2-d790-4909-acaf-c41ff949fcf3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096353276500, "endTime": 22096353320800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c5bb5e38-f4f5-467b-9182-9680d8eeba61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5bb5e38-f4f5-467b-9182-9680d8eeba61", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096353276500, "endTime": 22096353320800}, "additional": {"logType": "info", "children": [], "durationId": "91812ad2-d790-4909-acaf-c41ff949fcf3"}}, {"head": {"id": "6b944581-6d37-4112-a689-3bab0ac8a0ba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096496551900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7781af4f-90d4-4e99-8442-bb133e1fe8ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096499409900, "endTime": 22096499507600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e54f582b-03ee-424b-891d-23aba0157915"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e54f582b-03ee-424b-891d-23aba0157915", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096499409900, "endTime": 22096499507600}, "additional": {"logType": "info", "children": [], "durationId": "7781af4f-90d4-4e99-8442-bb133e1fe8ab"}}, {"head": {"id": "5420a035-59d9-4cfd-9ca0-e7094a32585b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096803930700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0efc2b85-d53d-4eca-b058-a6c117591c8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096806604600, "endTime": 22096806655000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ef41c31c-5cc1-4527-af35-c7b7fda998b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef41c31c-5cc1-4527-af35-c7b7fda998b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22096806604600, "endTime": 22096806655000}, "additional": {"logType": "info", "children": [], "durationId": "0efc2b85-d53d-4eca-b058-a6c117591c8c"}}, {"head": {"id": "11b1fcce-2667-4f18-b43c-8dfe838e30d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097055776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14f5f47-d9b7-46d8-8f36-de20206b42d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097058946600, "endTime": 22097058993300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f4d96ed3-d042-473c-b32c-d51faa8bd9c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4d96ed3-d042-473c-b32c-d51faa8bd9c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097058946600, "endTime": 22097058993300}, "additional": {"logType": "info", "children": [], "durationId": "c14f5f47-d9b7-46d8-8f36-de20206b42d5"}}, {"head": {"id": "886ef6f1-ef93-483f-a741-92676a8e1bbf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097059194700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c996e537-5e13-4a9b-8e05-cacd368c4424", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097062180500, "endTime": 22097062226100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0970ac30-5d72-49f8-b9f3-98d8e7329f56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0970ac30-5d72-49f8-b9f3-98d8e7329f56", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097062180500, "endTime": 22097062226100}, "additional": {"logType": "info", "children": [], "durationId": "c996e537-5e13-4a9b-8e05-cacd368c4424"}}, {"head": {"id": "93781f5f-98f7-4d94-b670-996c92361142", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097062442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3296de48-aa08-4d81-9f31-e394f75ffaec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097065583500, "endTime": 22097065637500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "62c61d9f-8b1b-4730-bc4f-e6e52f730623"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62c61d9f-8b1b-4730-bc4f-e6e52f730623", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097065583500, "endTime": 22097065637500}, "additional": {"logType": "info", "children": [], "durationId": "3296de48-aa08-4d81-9f31-e394f75ffaec"}}, {"head": {"id": "91b39a7b-7a66-4aac-9405-a86f38b576fd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097065955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95697e2c-a096-4e4a-8137-6294e94da793", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097070511500, "endTime": 22097070560400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "def5ad74-3bf0-4877-9ede-66c8fc421e94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "def5ad74-3bf0-4877-9ede-66c8fc421e94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097070511500, "endTime": 22097070560400}, "additional": {"logType": "info", "children": [], "durationId": "95697e2c-a096-4e4a-8137-6294e94da793"}}, {"head": {"id": "b3cce4c3-8951-4d1d-bd82-2c4932e07a6b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097070783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4174ce58-26b1-4697-a56f-17439153cd19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097073723000, "endTime": 22097073769800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "62eb3bc3-8810-4f98-968a-294e13f74020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62eb3bc3-8810-4f98-968a-294e13f74020", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097073723000, "endTime": 22097073769800}, "additional": {"logType": "info", "children": [], "durationId": "4174ce58-26b1-4697-a56f-17439153cd19"}}, {"head": {"id": "a20452b0-af94-45e6-9e41-a5af51886c63", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097074048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66edbc2-6043-4cc3-a80d-7c13cafa127b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097076925700, "endTime": 22097076972700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "3151bad1-ad42-440b-a8c6-678fea4d9c84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3151bad1-ad42-440b-a8c6-678fea4d9c84", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097076925700, "endTime": 22097076972700}, "additional": {"logType": "info", "children": [], "durationId": "b66edbc2-6043-4cc3-a80d-7c13cafa127b"}}, {"head": {"id": "c1af1cb7-dc53-4e88-973c-0f6c2c4d9a5e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097077169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd7ccaa-b9c2-4ae1-b68f-166e764d3cd6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097079790700, "endTime": 22097079834300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "90791501-f191-4905-adf7-3a4fcb6e86d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90791501-f191-4905-adf7-3a4fcb6e86d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097079790700, "endTime": 22097079834300}, "additional": {"logType": "info", "children": [], "durationId": "2fd7ccaa-b9c2-4ae1-b68f-166e764d3cd6"}}, {"head": {"id": "57172aa7-3e72-467b-b187-ac3e3b5d6747", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097080043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a9c305b-43eb-4af1-8e62-1e3487348b49", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097082948300, "endTime": 22097082995000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f4f88ab2-4b89-41b2-b6c1-736527d19870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4f88ab2-4b89-41b2-b6c1-736527d19870", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097082948300, "endTime": 22097082995000}, "additional": {"logType": "info", "children": [], "durationId": "2a9c305b-43eb-4af1-8e62-1e3487348b49"}}, {"head": {"id": "98b372d4-a920-433b-ba77-e236f85a69d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097083194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6e45db-67f9-4452-ae81-afed18f4b87a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097087968300, "endTime": 22097088014900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7fdccc24-0730-44d3-8ceb-8410c5d16118"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fdccc24-0730-44d3-8ceb-8410c5d16118", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097087968300, "endTime": 22097088014900}, "additional": {"logType": "info", "children": [], "durationId": "2e6e45db-67f9-4452-ae81-afed18f4b87a"}}, {"head": {"id": "0db5ebcf-0c79-410c-ae4a-949c3f44cb72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097088185000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e23bb5d-e859-4cd6-a510-ded61222a1c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097090554900, "endTime": 22097090600000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a414cc5b-1704-4794-87b7-d58354191c90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a414cc5b-1704-4794-87b7-d58354191c90", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097090554900, "endTime": 22097090600000}, "additional": {"logType": "info", "children": [], "durationId": "3e23bb5d-e859-4cd6-a510-ded61222a1c5"}}, {"head": {"id": "8df38b91-4b58-4d95-a845-71587af6d1b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097090803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7309556-159f-4f27-9369-f466917a8bb9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097093504500, "endTime": 22097093550000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a4d61d3f-92c2-48f6-ae3c-8a5c1b90b301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4d61d3f-92c2-48f6-ae3c-8a5c1b90b301", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097093504500, "endTime": 22097093550000}, "additional": {"logType": "info", "children": [], "durationId": "a7309556-159f-4f27-9369-f466917a8bb9"}}, {"head": {"id": "82f4bd7a-05ec-4b53-b7a1-376bf463e1fd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097093744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123993a5-e52d-4fab-86b8-e81dbbc63e8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097096577000, "endTime": 22097096620800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "658158b4-4ee6-47c5-983c-df231e0339c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "658158b4-4ee6-47c5-983c-df231e0339c6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097096577000, "endTime": 22097096620800}, "additional": {"logType": "info", "children": [], "durationId": "123993a5-e52d-4fab-86b8-e81dbbc63e8c"}}, {"head": {"id": "6c0fa527-4a90-4bbe-9cee-199f7836dc73", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097096826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781d575a-9628-40af-9a7c-f2d7037e69ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097099572300, "endTime": 22097099608900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0f027fc8-3f64-49ed-ba18-d6c22436d45a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f027fc8-3f64-49ed-ba18-d6c22436d45a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097099572300, "endTime": 22097099608900}, "additional": {"logType": "info", "children": [], "durationId": "781d575a-9628-40af-9a7c-f2d7037e69ff"}}, {"head": {"id": "5547d6ac-127d-452a-9711-6e3447076725", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097099761500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6752f3e1-e6a1-418f-bde5-91993ab5ddd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097102091900, "endTime": 22097102133000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2c0ae5e1-59a0-4ebc-9ba3-62e902dac078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c0ae5e1-59a0-4ebc-9ba3-62e902dac078", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097102091900, "endTime": 22097102133000}, "additional": {"logType": "info", "children": [], "durationId": "6752f3e1-e6a1-418f-bde5-91993ab5ddd1"}}, {"head": {"id": "37100636-3168-4da7-87b5-2059a6ce5cb4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097102334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9f110c-d5d7-428c-a727-bf00ae5e61e0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097104835200, "endTime": 22097104871900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "cd580cdb-eacd-4e38-92ac-82589a8e7f10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd580cdb-eacd-4e38-92ac-82589a8e7f10", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097104835200, "endTime": 22097104871900}, "additional": {"logType": "info", "children": [], "durationId": "fc9f110c-d5d7-428c-a727-bf00ae5e61e0"}}, {"head": {"id": "6c677b3d-62fd-4f28-9d82-663ceaca4fbf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097254552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7804d8d6-5503-4909-8ae1-fd00c337bb53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097256944600, "endTime": 22097256980700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "257e1af2-1ce8-457b-bc6a-dfa004e46676"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "257e1af2-1ce8-457b-bc6a-dfa004e46676", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097256944600, "endTime": 22097256980700}, "additional": {"logType": "info", "children": [], "durationId": "7804d8d6-5503-4909-8ae1-fd00c337bb53"}}, {"head": {"id": "eb422508-888e-4fe1-934a-36a9ff3e4094", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097257138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5694ad0-c84f-40ea-a952-ba1105b153ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097259791200, "endTime": 22097259836300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2730c7ae-abe4-458f-baba-01a138ff86da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2730c7ae-abe4-458f-baba-01a138ff86da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097259791200, "endTime": 22097259836300}, "additional": {"logType": "info", "children": [], "durationId": "e5694ad0-c84f-40ea-a952-ba1105b153ed"}}, {"head": {"id": "61dc72c6-4c64-42b9-a1ea-b2fe73e0c112", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097260034500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da3883e1-419d-4378-83d6-602e54c98cc9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097262323600, "endTime": 22097262358500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e020bc99-ce21-430f-a2ea-06857ece4f3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e020bc99-ce21-430f-a2ea-06857ece4f3b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097262323600, "endTime": 22097262358500}, "additional": {"logType": "info", "children": [], "durationId": "da3883e1-419d-4378-83d6-602e54c98cc9"}}, {"head": {"id": "39fb02ee-238d-4177-b2f6-e45b31e977db", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097457671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b18cbd4-b872-4b29-a008-6818ec56db2e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097460466100, "endTime": 22097460513900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4f686cd7-8aed-4ec1-9df1-1fed1aff7166"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f686cd7-8aed-4ec1-9df1-1fed1aff7166", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22097460466100, "endTime": 22097460513900}, "additional": {"logType": "info", "children": [], "durationId": "1b18cbd4-b872-4b29-a008-6818ec56db2e"}}, {"head": {"id": "c14f452e-64ba-4071-a7ad-754ceaf02a51", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098298774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0bed843-9889-4464-bb9f-3c2e7f55806f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098313333400, "endTime": 22098313383800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "69c9a36a-7176-42b4-b862-d45882832687"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69c9a36a-7176-42b4-b862-d45882832687", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098313333400, "endTime": 22098313383800}, "additional": {"logType": "info", "children": [], "durationId": "c0bed843-9889-4464-bb9f-3c2e7f55806f"}}, {"head": {"id": "5cabf094-8bee-44a3-b9a1-1dcb5bdf1db4", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098400968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e93456d-bf5a-4d54-b09c-becf25ca0c10", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098403430400, "endTime": 22098403465600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "40ca4548-e630-4a83-874e-f17d43015b94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40ca4548-e630-4a83-874e-f17d43015b94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22098403430400, "endTime": 22098403465600}, "additional": {"logType": "info", "children": [], "durationId": "5e93456d-bf5a-4d54-b09c-becf25ca0c10"}}, {"head": {"id": "66106722-d6d9-4329-b69e-0e2bb642cfa4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22100831662700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17a5e9a-ef0b-4d7c-b5fb-7e567fc9fc52", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22100834566200, "endTime": 22100834604800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "8d2116e2-4760-4f93-ba92-699d13dd2a04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d2116e2-4760-4f93-ba92-699d13dd2a04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22100834566200, "endTime": 22100834604800}, "additional": {"logType": "info", "children": [], "durationId": "e17a5e9a-ef0b-4d7c-b5fb-7e567fc9fc52"}}, {"head": {"id": "f9f05c68-30af-4fca-95eb-6bc1f0bd679e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22104626281400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57072b08-1e6c-4b25-b5fd-f58598de0728", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22104627248700, "endTime": 22104627266700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "5404f8ca-9899-4fe2-97cc-67640e08e2fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5404f8ca-9899-4fe2-97cc-67640e08e2fa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22104627248700, "endTime": 22104627266700}, "additional": {"logType": "info", "children": [], "durationId": "57072b08-1e6c-4b25-b5fd-f58598de0728", "parent": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}}, {"head": {"id": "5e46b55f-ee26-4b4f-a86b-b2594a3dda26", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119518642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d698e1ae-1a7d-4e18-a43b-3cad8c05040f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22119519751300, "endTime": 22119519770100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "3f9e6727-30a0-47a6-9b4c-308a0eb3f35d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f9e6727-30a0-47a6-9b4c-308a0eb3f35d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119519751300, "endTime": 22119519770100}, "additional": {"logType": "info", "children": [], "durationId": "d698e1ae-1a7d-4e18-a43b-3cad8c05040f", "parent": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}}, {"head": {"id": "11c420d6-d70d-49d6-bad6-141d2acc08ee", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22095137008000, "endTime": 22119520497800}, "additional": {"logType": "info", "children": ["5404f8ca-9899-4fe2-97cc-67640e08e2fa", "3f9e6727-30a0-47a6-9b4c-308a0eb3f35d", "a80969f7-36ba-4346-b5e1-381376419d4b", "a5c895ce-1b79-489a-9367-b79f809fdcc8", "2856d4f9-9591-46e2-a302-64ad6b429103"], "durationId": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "parent": "c3b8a713-a891-4a28-b265-b3b17d5f7392"}}, {"head": {"id": "efc0f059-e75c-4837-bb59-f2550d30e620", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22102361004300, "endTime": 22104603267000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "a80969f7-36ba-4346-b5e1-381376419d4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a80969f7-36ba-4346-b5e1-381376419d4b", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22102361004300, "endTime": 22104603267000}, "additional": {"logType": "info", "children": [], "durationId": "efc0f059-e75c-4837-bb59-f2550d30e620", "parent": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}}, {"head": {"id": "22645316-06cb-4302-af53-e7f8ebb3bed4", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22104603511500, "endTime": 22104603667400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "a5c895ce-1b79-489a-9367-b79f809fdcc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5c895ce-1b79-489a-9367-b79f809fdcc8", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22104603511500, "endTime": 22104603667400}, "additional": {"logType": "info", "children": [], "durationId": "22645316-06cb-4302-af53-e7f8ebb3bed4", "parent": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}}, {"head": {"id": "b4822fef-d361-42ba-8d04-eb3926cdc951", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker22", "startTime": 22104603797800, "endTime": 22119518743100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "2856d4f9-9591-46e2-a302-64ad6b429103"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2856d4f9-9591-46e2-a302-64ad6b429103", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22104603797800, "endTime": 22119518743100}, "additional": {"logType": "info", "children": [], "durationId": "b4822fef-d361-42ba-8d04-eb3926cdc951", "parent": "11c420d6-d70d-49d6-bad6-141d2acc08ee"}}, {"head": {"id": "c3b8a713-a891-4a28-b265-b3b17d5f7392", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22095067840800, "endTime": 22119523852700, "totalTime": 24456000700}, "additional": {"logType": "info", "children": ["11c420d6-d70d-49d6-bad6-141d2acc08ee"], "durationId": "86c2a0ce-864c-4018-a92a-3c5044ace318"}}, {"head": {"id": "7345e4f5-7e83-4cd4-9a3e-b7103167e67a", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532613400, "endTime": 22119533034500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "27077c07-4825-4f7c-bc84-66547f683b2f", "logId": "7607ac4d-45f3-458e-a39d-9412c5a6d313"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27077c07-4825-4f7c-bc84-66547f683b2f", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532528300}, "additional": {"logType": "detail", "children": [], "durationId": "7345e4f5-7e83-4cd4-9a3e-b7103167e67a"}}, {"head": {"id": "7bccede7-d3bf-49c1-838a-f8752d41767b", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac5aa2e6-9445-4cbf-9997-9113dcef5754", "name": "entry : PreviewBuild cost memory 0.01148223876953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532827000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db20b8c1-241a-4aad-a036-96ca350001d3", "name": "runTaskFromQueue task cost before running: 25 s 910 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7607ac4d-45f3-458e-a39d-9412c5a6d313", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119532613400, "endTime": 22119533034500, "totalTime": 309700}, "additional": {"logType": "info", "children": [], "durationId": "7345e4f5-7e83-4cd4-9a3e-b7103167e67a"}}, {"head": {"id": "edd262f5-1b85-4a6c-b4ce-5613072d7933", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119557806400, "endTime": 22119557833500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a1e5743-d212-4749-97bf-23862e5e374a", "logId": "d146c12f-d1f4-4bc7-8223-3a2293b2892d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d146c12f-d1f4-4bc7-8223-3a2293b2892d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119557806400, "endTime": 22119557833500}, "additional": {"logType": "info", "children": [], "durationId": "edd262f5-1b85-4a6c-b4ce-5613072d7933"}}, {"head": {"id": "8557cc46-7d25-4c6f-8c7e-7ba6cd721ec5", "name": "BUILD SUCCESSFUL in 25 s 935 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119557887100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d452ca21-0998-43ed-9799-aab2645d3ec3", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22093623104500, "endTime": 22119558178600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 58}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "94c28367-807d-4cd6-9fe8-50c9fcbdb329", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558210100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b51c326-b38b-42e3-a7c4-95d70a41ef96", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558305100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077c7397-56d5-4e45-b20f-57311894e125", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8abd1946-d37b-4982-8a12-009e609706a8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca73f691-8f5f-4a51-96e3-75ea80510bd7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7166b6e4-999c-44c8-b9c6-fc897acb23f2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558577900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95fd6b96-1d17-4a7e-ad52-a32e32bbf5cc", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119558644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e753d319-f968-43a8-b5c7-b3dac3f55405", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119559965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5756ad4e-f3f2-41c9-8bbb-326854a59a41", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119572815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c3e80e5-3303-4a36-88e9-9467cdd96fd8", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119573354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bd052a-9d99-4505-b60d-27d2d19fd2b3", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119603752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038b9493-297d-44b4-b669-97448e977ebc", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:47 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119604943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6770e0f2-7f45-495e-8a14-9feadcd44231", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119605303500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a15bc9-ebda-46d7-bcc7-cbecec12833d", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119606841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65da17a1-46da-4325-98da-ad6cde699e3b", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119608382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579c6c8d-a69d-4163-8ef2-adcbbcbd1686", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119609038600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2093e1-9983-428d-bcc1-7fffdfdeb1d0", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119609686600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb7994b-d90d-4522-baf2-2e0ec06bc9fc", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119610642600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebfe1a36-b020-4f2a-8dc0-58e594f60a21", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119616120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f3a1ca-ea4a-459f-91ff-bbc47c76381c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119617763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "630a4df0-057d-4fef-83e5-05d618fef678", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119618332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6916c90b-bf89-46a0-b3e1-4ec5110a32f6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119618820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "120bb8d7-6f24-4371-8f3e-e77a95062f84", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119620248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d8f056a-25d9-4d6f-b758-32a128af77a9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119640911500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83094fe8-4846-42a2-8a5d-3e3c7b94343d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119641491300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0567a46e-4f3a-4667-ab8b-3129254d7c60", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119641983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e5cb3c5-7431-4c27-a668-d7f1965b0f38", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119642536800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8be32ef-8d98-462b-9567-077a89b9e509", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119643507400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}