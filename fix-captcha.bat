@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 验证码问题修复脚本
echo ========================================
echo.

echo 📋 诊断验证码获取失败问题...
echo.

REM 1. 检查后端服务状态
echo 🔍 步骤1: 检查后端服务...
tasklist /FI "IMAGENAME eq java.exe" 2>nul | find /i "java.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Java进程正在运行
) else (
    echo ❌ Java进程未运行，正在启动...
    goto START_BACKEND
)

REM 2. 检查端口监听
echo 🔍 步骤2: 检查8080端口...
netstat -ano | findstr :8080 >nul
if %errorlevel% equ 0 (
    echo ✅ 8080端口正在监听
) else (
    echo ❌ 8080端口未监听
    goto START_BACKEND
)

REM 3. 直接测试API
echo 🔍 步骤3: 测试验证码API...
curl -s -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/user/captcha
if %errorlevel% equ 0 (
    echo ✅ API响应正常
    goto CHECK_CORS
) else (
    echo ❌ API无响应
    goto START_BACKEND
)

:START_BACKEND
echo.
echo 🚀 重新启动后端服务...
echo ----------------------------------------

REM 强制停止Java进程
echo 🛑 停止现有Java进程...
taskkill /f /im java.exe >nul 2>&1
timeout /t 2 >nul

REM 启动后端
if exist "Spring Boot3-e-wallet\target\spring-e-wallet-1.0-SNAPSHOT.jar" (
    echo ✅ 找到JAR文件，启动中...
    cd "Spring Boot3-e-wallet"
    start /min java -Dserver.port=8080 -jar target\spring-e-wallet-1.0-SNAPSHOT.jar
    cd ..
    
    echo ⏳ 等待服务启动...
    for /l %%i in (1,1,20) do (
        timeout /t 2 >nul
        curl -s http://localhost:8080/api/user/captcha >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ 后端服务启动成功！
            goto CHECK_CORS
        )
        echo 等待中... %%i/20
    )
    echo ❌ 后端启动超时
) else (
    echo ❌ 未找到JAR文件
    echo 💡 请使用IDE启动SpringEWalletApplication类
)

:CHECK_CORS
echo.
echo 🔍 步骤4: 检查CORS配置...
echo ----------------------------------------

REM 测试带Origin头的请求
curl -s -H "Origin: http://localhost:5173" -w "状态码: %%{http_code}\n" http://localhost:8080/api/user/captcha

echo.
echo 🔍 步骤5: 检查前端配置...
echo ----------------------------------------

REM 检查前端是否运行
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /i "node.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Node.js前端服务正在运行
) else (
    echo ❌ 前端服务未运行，正在启动...
    if exist "vue-e-wallet\vuedoem\package.json" (
        cd vue-e-wallet\vuedoem
        start /min cmd /c "npm run dev"
        cd ..\..
        echo ⏳ 等待前端启动...
        timeout /t 10 >nul
    )
)

echo.
echo 🧪 最终测试...
echo ----------------------------------------

REM 完整的API测试
echo 测试验证码API响应:
curl -s -X GET http://localhost:8080/api/user/captcha -H "Content-Type: application/json" -w "\n状态码: %%{http_code}\n"

echo.
echo 🌐 打开浏览器进行手动测试...
start http://localhost:5173

echo.
echo ========================================
echo 🎯 修复完成！请测试以下功能:
echo ========================================
echo.
echo 1. 🌐 访问: http://localhost:5173
echo 2. 🔐 点击"获取验证码"按钮
echo 3. 👀 查看浏览器控制台是否有错误
echo 4. 🔍 检查Network标签页的请求状态
echo.
echo 💡 如果仍有问题，请检查:
echo   - 浏览器控制台错误信息
echo   - 网络请求的详细状态
echo   - 是否有代理或VPN干扰
echo   - 防火墙或杀毒软件拦截
echo.
echo 🔧 手动测试命令:
echo   curl http://localhost:8080/api/user/captcha
echo.
pause
