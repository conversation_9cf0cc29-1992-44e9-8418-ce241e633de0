{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "2f0b07c8-8790-43e7-9355-2027162db02b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159602821000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684e2efa-3307-40e2-bddc-2f4f17c77d06", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316162433523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07345595-ba11-407d-b1af-0960563700fd", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316162434061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3d3a7c-b1b4-4692-b5b5-bdc2fff5a0a4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194291625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d02694ec-598d-4566-81d9-0f210fdee419", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194302373800, "endTime": 316194509432000}, "additional": {"children": ["bb5e4005-42a5-4b47-b6bf-103626a30f18", "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "bf655944-3e96-4959-8335-d2f7336243db", "ffb46a7a-ab23-46d2-9792-cd4f53d8592c", "d8fe659f-b353-4609-a9c1-180fc2e424cd", "3edbaf8f-5e66-46f3-b861-327fd80f26d5", "b08e7004-011d-4ef2-9d48-ad2d998c661c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb5e4005-42a5-4b47-b6bf-103626a30f18", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194302374900, "endTime": 316194316751700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "db474b45-3f69-4f77-ad86-54abc57db945"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194316767500, "endTime": 316194508142900}, "additional": {"children": ["05723fb9-1eda-4c34-a464-c31a50061205", "505e9a89-3b59-4efb-afd8-aee47abc1eaf", "15d14665-04d9-4fd6-8da3-efd385e0eae4", "8db3184b-7859-4869-b241-d53cd61ac9f2", "8fcf381b-978c-4a09-971e-944f106eb1de", "49e69bfb-88b7-477b-af98-cc01d9731f6c", "24622253-3fe2-4443-ab16-c49e0967deb1", "66c8c8e4-2d80-40e5-af89-eba64d65cbd0", "9c5377e0-8fd0-447c-8463-9b83e709025b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "367e0e78-6417-4620-a59e-18567495730a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf655944-3e96-4959-8335-d2f7336243db", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194508166000, "endTime": 316194509422100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "75cfee61-5417-41a1-bfde-0b2761d89fa8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffb46a7a-ab23-46d2-9792-cd4f53d8592c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194509427300, "endTime": 316194509428600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "148ce5be-5820-4e11-b42b-74e19db5cbcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8fe659f-b353-4609-a9c1-180fc2e424cd", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194305952500, "endTime": 316194305993000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "3a4f7e65-5e62-4a74-b618-79bd653154f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a4f7e65-5e62-4a74-b618-79bd653154f5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194305952500, "endTime": 316194305993000}, "additional": {"logType": "info", "children": [], "durationId": "d8fe659f-b353-4609-a9c1-180fc2e424cd", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "3edbaf8f-5e66-46f3-b861-327fd80f26d5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194311823300, "endTime": 316194311845400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "dff05507-a49a-428e-9d27-a0b7f8eef2a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dff05507-a49a-428e-9d27-a0b7f8eef2a3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194311823300, "endTime": 316194311845400}, "additional": {"logType": "info", "children": [], "durationId": "3edbaf8f-5e66-46f3-b861-327fd80f26d5", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "39b2a3ba-ee0f-4e51-87f7-86b0c755fbd3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194311901800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a4e14b-6283-408c-a771-e886a57a535a", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194316628500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db474b45-3f69-4f77-ad86-54abc57db945", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194302374900, "endTime": 316194316751700}, "additional": {"logType": "info", "children": [], "durationId": "bb5e4005-42a5-4b47-b6bf-103626a30f18", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "05723fb9-1eda-4c34-a464-c31a50061205", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194323054300, "endTime": 316194323061900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "12fa335d-aa25-4fc3-b506-b938d78f7d6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "505e9a89-3b59-4efb-afd8-aee47abc1eaf", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194323077300, "endTime": 316194330086300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "68abb50a-62a6-4c74-987f-94098de039dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d14665-04d9-4fd6-8da3-efd385e0eae4", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194330103900, "endTime": 316194429970700}, "additional": {"children": ["2031698a-092a-444d-9969-af3a134f532c", "cb48c4cb-022f-4d7a-8620-f13c6c0754e9", "d45bda67-f6aa-4a49-8130-606e006d7233"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "01ba2401-676b-4b5b-9d05-d9095ae9f492"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8db3184b-7859-4869-b241-d53cd61ac9f2", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194429984200, "endTime": 316194460750300}, "additional": {"children": ["455862c2-4bcc-423b-bf18-50e96539f201"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "cff82ae0-fef2-4d6f-9e35-a64b390e371e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fcf381b-978c-4a09-971e-944f106eb1de", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194460760000, "endTime": 316194485737000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "93b795a2-00b3-4c10-95e0-d35c4158e0b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49e69bfb-88b7-477b-af98-cc01d9731f6c", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194486830300, "endTime": 316194495200500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "22abb95f-f9e8-4d95-82e1-724a981ab07e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24622253-3fe2-4443-ab16-c49e0967deb1", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194495226600, "endTime": 316194507965800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "c6613aaa-4d4b-43f0-a25a-f7d0c1147e1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66c8c8e4-2d80-40e5-af89-eba64d65cbd0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194508003600, "endTime": 316194508131400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "d07a2746-e5d7-49b3-bcea-6cf6ba623a90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12fa335d-aa25-4fc3-b506-b938d78f7d6a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194323054300, "endTime": 316194323061900}, "additional": {"logType": "info", "children": [], "durationId": "05723fb9-1eda-4c34-a464-c31a50061205", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "68abb50a-62a6-4c74-987f-94098de039dc", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194323077300, "endTime": 316194330086300}, "additional": {"logType": "info", "children": [], "durationId": "505e9a89-3b59-4efb-afd8-aee47abc1eaf", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "2031698a-092a-444d-9969-af3a134f532c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194331182600, "endTime": 316194331205900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15d14665-04d9-4fd6-8da3-efd385e0eae4", "logId": "f9367ff2-7239-49e8-9abf-590f09ad6baf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9367ff2-7239-49e8-9abf-590f09ad6baf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194331182600, "endTime": 316194331205900}, "additional": {"logType": "info", "children": [], "durationId": "2031698a-092a-444d-9969-af3a134f532c", "parent": "01ba2401-676b-4b5b-9d05-d9095ae9f492"}}, {"head": {"id": "cb48c4cb-022f-4d7a-8620-f13c6c0754e9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194333956100, "endTime": 316194429112300}, "additional": {"children": ["624a6653-6d33-4ec1-84bd-e1aa4b25e9b5", "e8fcd83f-47c0-451c-9916-69f553d1eb38"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15d14665-04d9-4fd6-8da3-efd385e0eae4", "logId": "6076b3ab-58fb-4a3c-97e4-6d5d204637ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "624a6653-6d33-4ec1-84bd-e1aa4b25e9b5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194333957800, "endTime": 316194340900300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb48c4cb-022f-4d7a-8620-f13c6c0754e9", "logId": "59afe89a-b0a5-448a-9a7b-97e75129a784"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8fcd83f-47c0-451c-9916-69f553d1eb38", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194340926300, "endTime": 316194429097300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb48c4cb-022f-4d7a-8620-f13c6c0754e9", "logId": "ef7356e6-ca23-4cd3-91bf-87332c32fc9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b5743b4-f750-497d-9e50-123ae8d2fac0", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194333965200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2b4133c-aa1c-4e1a-b3eb-1bae1d11f7b2", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194340725800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59afe89a-b0a5-448a-9a7b-97e75129a784", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194333957800, "endTime": 316194340900300}, "additional": {"logType": "info", "children": [], "durationId": "624a6653-6d33-4ec1-84bd-e1aa4b25e9b5", "parent": "6076b3ab-58fb-4a3c-97e4-6d5d204637ce"}}, {"head": {"id": "4c7929fd-d7de-4976-98a3-19c27c2ff7d3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194340945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f97aac-f66b-4048-8401-b43739d809f5", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194349319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c7e6ac-d2fd-403c-8c03-ebedf1f5510d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194349436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ea0aad-0b58-4198-a334-0aa1b49b69e4", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194349597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8396823-78f8-45d3-b9e7-83a8492d3f03", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194349706700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b080398-efa1-4171-a149-00306368088e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194351432200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2562b20f-fd3e-4c0c-b348-6f918ba7d565", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194356871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4ddfd6-92c6-4d45-a30c-be2242fa9e6d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194367289500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73357c76-f206-4ae8-8242-58e73c8a9c87", "name": "Sdk init in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194397574800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01ef504-d839-4b53-a2e7-e64831f6d724", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194397821700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "markType": "other"}}, {"head": {"id": "8c70a8fb-c4a8-4c8a-afe0-df490ddfeed2", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194397849700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "markType": "other"}}, {"head": {"id": "98b67b91-dc39-4444-9be1-99d88deed959", "name": "Project task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194428766400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ad1c89-83df-4dc4-bb88-52061bb251e3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194428904000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b650f34-bf2d-49b9-b6a9-3b6cd8de7e6e", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194428978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff65e3c-0f52-4650-b273-dc3ac766ba8c", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194429039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7356e6-ca23-4cd3-91bf-87332c32fc9d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194340926300, "endTime": 316194429097300}, "additional": {"logType": "info", "children": [], "durationId": "e8fcd83f-47c0-451c-9916-69f553d1eb38", "parent": "6076b3ab-58fb-4a3c-97e4-6d5d204637ce"}}, {"head": {"id": "6076b3ab-58fb-4a3c-97e4-6d5d204637ce", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194333956100, "endTime": 316194429112300}, "additional": {"logType": "info", "children": ["59afe89a-b0a5-448a-9a7b-97e75129a784", "ef7356e6-ca23-4cd3-91bf-87332c32fc9d"], "durationId": "cb48c4cb-022f-4d7a-8620-f13c6c0754e9", "parent": "01ba2401-676b-4b5b-9d05-d9095ae9f492"}}, {"head": {"id": "d45bda67-f6aa-4a49-8130-606e006d7233", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194429943000, "endTime": 316194429956200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15d14665-04d9-4fd6-8da3-efd385e0eae4", "logId": "b89cd939-aa3f-47ae-b7fa-8db21fc2aad7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b89cd939-aa3f-47ae-b7fa-8db21fc2aad7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194429943000, "endTime": 316194429956200}, "additional": {"logType": "info", "children": [], "durationId": "d45bda67-f6aa-4a49-8130-606e006d7233", "parent": "01ba2401-676b-4b5b-9d05-d9095ae9f492"}}, {"head": {"id": "01ba2401-676b-4b5b-9d05-d9095ae9f492", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194330103900, "endTime": 316194429970700}, "additional": {"logType": "info", "children": ["f9367ff2-7239-49e8-9abf-590f09ad6baf", "6076b3ab-58fb-4a3c-97e4-6d5d204637ce", "b89cd939-aa3f-47ae-b7fa-8db21fc2aad7"], "durationId": "15d14665-04d9-4fd6-8da3-efd385e0eae4", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "455862c2-4bcc-423b-bf18-50e96539f201", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194430789000, "endTime": 316194460734800}, "additional": {"children": ["7818b658-f12e-48ea-8ce2-cf3090d8fa7f", "71b69ac7-9067-4044-88e4-119413c686af", "bad63448-2a9d-4fde-b613-6abbd1d14af2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8db3184b-7859-4869-b241-d53cd61ac9f2", "logId": "fd521e8a-d5d7-498e-850f-de714d15cb35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7818b658-f12e-48ea-8ce2-cf3090d8fa7f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194436743900, "endTime": 316194436764500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "455862c2-4bcc-423b-bf18-50e96539f201", "logId": "3cebf6e8-b525-4b10-8f84-96cf5f3be62d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cebf6e8-b525-4b10-8f84-96cf5f3be62d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194436743900, "endTime": 316194436764500}, "additional": {"logType": "info", "children": [], "durationId": "7818b658-f12e-48ea-8ce2-cf3090d8fa7f", "parent": "fd521e8a-d5d7-498e-850f-de714d15cb35"}}, {"head": {"id": "71b69ac7-9067-4044-88e4-119413c686af", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194439963600, "endTime": 316194458775900}, "additional": {"children": ["87a7b6ef-a205-4648-af63-93bd1f4979b4", "0245676c-61bf-45b5-8d59-5929fea9e214"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "455862c2-4bcc-423b-bf18-50e96539f201", "logId": "f4a5e67b-1336-4904-9eee-ba78ed994938"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87a7b6ef-a205-4648-af63-93bd1f4979b4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194439965300, "endTime": 316194444765400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "71b69ac7-9067-4044-88e4-119413c686af", "logId": "bce8270a-5ade-4e0d-827e-2c26b1caa483"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0245676c-61bf-45b5-8d59-5929fea9e214", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194444791300, "endTime": 316194458761000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "71b69ac7-9067-4044-88e4-119413c686af", "logId": "fe44c0b3-54aa-41f9-979b-c2430abd08e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4968c912-8e40-46d3-a0f4-b80533a0d581", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194439972900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740db013-d5c8-468b-95a1-fe567baa1824", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194444566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bce8270a-5ade-4e0d-827e-2c26b1caa483", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194439965300, "endTime": 316194444765400}, "additional": {"logType": "info", "children": [], "durationId": "87a7b6ef-a205-4648-af63-93bd1f4979b4", "parent": "f4a5e67b-1336-4904-9eee-ba78ed994938"}}, {"head": {"id": "c38164be-1cae-4946-81f2-68154371bbe6", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194444810500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14e7cc1-e26a-4f94-952e-0ebac8f163ba", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194453414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0683381e-c30f-4b68-9ca3-8b6d3603296d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194453537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b9ee5e-4405-42ff-ad15-2334abfe920a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194454235200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0778c6ba-b285-4209-b883-6c7ed84348ed", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194454389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dee6353-cf24-4bd7-aa38-f827f1816e6c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194454463300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ce3484-95e0-4345-8bf7-1786e9c7ac0d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194454537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79038d00-589e-4884-a155-85b95106e75c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194454639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92d71fc-fe80-4801-be2d-ae78439dbabd", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194458338800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acaba67d-9351-4032-a5c5-b9b30205374e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194458496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0152201-a4b2-45d9-8c9d-29f680c0a38f", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194458591800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bab4cd31-6d09-4550-a64c-a1092c132ca4", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194458670800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe44c0b3-54aa-41f9-979b-c2430abd08e4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194444791300, "endTime": 316194458761000}, "additional": {"logType": "info", "children": [], "durationId": "0245676c-61bf-45b5-8d59-5929fea9e214", "parent": "f4a5e67b-1336-4904-9eee-ba78ed994938"}}, {"head": {"id": "f4a5e67b-1336-4904-9eee-ba78ed994938", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194439963600, "endTime": 316194458775900}, "additional": {"logType": "info", "children": ["bce8270a-5ade-4e0d-827e-2c26b1caa483", "fe44c0b3-54aa-41f9-979b-c2430abd08e4"], "durationId": "71b69ac7-9067-4044-88e4-119413c686af", "parent": "fd521e8a-d5d7-498e-850f-de714d15cb35"}}, {"head": {"id": "bad63448-2a9d-4fde-b613-6abbd1d14af2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194460690000, "endTime": 316194460710300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "455862c2-4bcc-423b-bf18-50e96539f201", "logId": "144315a4-d331-4666-a5f3-6786caeda425"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "144315a4-d331-4666-a5f3-6786caeda425", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194460690000, "endTime": 316194460710300}, "additional": {"logType": "info", "children": [], "durationId": "bad63448-2a9d-4fde-b613-6abbd1d14af2", "parent": "fd521e8a-d5d7-498e-850f-de714d15cb35"}}, {"head": {"id": "fd521e8a-d5d7-498e-850f-de714d15cb35", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194430789000, "endTime": 316194460734800}, "additional": {"logType": "info", "children": ["3cebf6e8-b525-4b10-8f84-96cf5f3be62d", "f4a5e67b-1336-4904-9eee-ba78ed994938", "144315a4-d331-4666-a5f3-6786caeda425"], "durationId": "455862c2-4bcc-423b-bf18-50e96539f201", "parent": "cff82ae0-fef2-4d6f-9e35-a64b390e371e"}}, {"head": {"id": "cff82ae0-fef2-4d6f-9e35-a64b390e371e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194429984200, "endTime": 316194460750300}, "additional": {"logType": "info", "children": ["fd521e8a-d5d7-498e-850f-de714d15cb35"], "durationId": "8db3184b-7859-4869-b241-d53cd61ac9f2", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "c57165a1-c2fb-445a-b23a-9ba7a66b49c4", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194485301800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93dbe803-cc0e-461b-9b43-ba813bd61527", "name": "hvigorfile, resolve hvigorfile dependencies in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194485653300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b795a2-00b3-4c10-95e0-d35c4158e0b4", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194460760000, "endTime": 316194485737000}, "additional": {"logType": "info", "children": [], "durationId": "8fcf381b-978c-4a09-971e-944f106eb1de", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "9c5377e0-8fd0-447c-8463-9b83e709025b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194486632100, "endTime": 316194486817100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "logId": "4e65ac0e-b79a-4f7e-a779-79136e7be554"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38b380ba-1200-4cc9-9819-c0c31a07a6b0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194486657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e65ac0e-b79a-4f7e-a779-79136e7be554", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194486632100, "endTime": 316194486817100}, "additional": {"logType": "info", "children": [], "durationId": "9c5377e0-8fd0-447c-8463-9b83e709025b", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "df06b6e1-3ed8-47ce-8081-4b76f3c41d46", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194488321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c94493-2aef-4cc5-9dfa-9ba025642c8c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194494260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22abb95f-f9e8-4d95-82e1-724a981ab07e", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194486830300, "endTime": 316194495200500}, "additional": {"logType": "info", "children": [], "durationId": "49e69bfb-88b7-477b-af98-cc01d9731f6c", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "24a49c39-836b-4814-a772-450e736957d7", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194495239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072536ae-b7b6-4625-aa89-0ee5dbb569ce", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194501658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029b8b1d-61f4-4ac0-8296-29079c566004", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194501807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550cf1cf-93e0-4d74-b2f5-422268995bfe", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194502173400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4299de-64db-470e-acfc-1aa5766da589", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194504810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31d93f2a-b75b-4245-9744-4b1115c0fd5a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194504900800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6613aaa-4d4b-43f0-a25a-f7d0c1147e1f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194495226600, "endTime": 316194507965800}, "additional": {"logType": "info", "children": [], "durationId": "24622253-3fe2-4443-ab16-c49e0967deb1", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "59e36fa1-4051-42d2-b3cb-8866ca39920f", "name": "Configuration phase cost:185 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194508026200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d07a2746-e5d7-49b3-bcea-6cf6ba623a90", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194508003600, "endTime": 316194508131400}, "additional": {"logType": "info", "children": [], "durationId": "66c8c8e4-2d80-40e5-af89-eba64d65cbd0", "parent": "367e0e78-6417-4620-a59e-18567495730a"}}, {"head": {"id": "367e0e78-6417-4620-a59e-18567495730a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194316767500, "endTime": 316194508142900}, "additional": {"logType": "info", "children": ["12fa335d-aa25-4fc3-b506-b938d78f7d6a", "68abb50a-62a6-4c74-987f-94098de039dc", "01ba2401-676b-4b5b-9d05-d9095ae9f492", "cff82ae0-fef2-4d6f-9e35-a64b390e371e", "93b795a2-00b3-4c10-95e0-d35c4158e0b4", "22abb95f-f9e8-4d95-82e1-724a981ab07e", "c6613aaa-4d4b-43f0-a25a-f7d0c1147e1f", "d07a2746-e5d7-49b3-bcea-6cf6ba623a90", "4e65ac0e-b79a-4f7e-a779-79136e7be554"], "durationId": "695e5252-1e0e-4c46-bcff-77b2f344a1d1", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "b08e7004-011d-4ef2-9d48-ad2d998c661c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194509395200, "endTime": 316194509409900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d02694ec-598d-4566-81d9-0f210fdee419", "logId": "096dc07e-d292-4553-b887-69b2ab9aef78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "096dc07e-d292-4553-b887-69b2ab9aef78", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194509395200, "endTime": 316194509409900}, "additional": {"logType": "info", "children": [], "durationId": "b08e7004-011d-4ef2-9d48-ad2d998c661c", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "75cfee61-5417-41a1-bfde-0b2761d89fa8", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194508166000, "endTime": 316194509422100}, "additional": {"logType": "info", "children": [], "durationId": "bf655944-3e96-4959-8335-d2f7336243db", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "148ce5be-5820-4e11-b42b-74e19db5cbcc", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194509427300, "endTime": 316194509428600}, "additional": {"logType": "info", "children": [], "durationId": "ffb46a7a-ab23-46d2-9792-cd4f53d8592c", "parent": "c1f22f5c-ab43-48d1-9625-926cf9866aab"}}, {"head": {"id": "c1f22f5c-ab43-48d1-9625-926cf9866aab", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194302373800, "endTime": 316194509432000}, "additional": {"logType": "info", "children": ["db474b45-3f69-4f77-ad86-54abc57db945", "367e0e78-6417-4620-a59e-18567495730a", "75cfee61-5417-41a1-bfde-0b2761d89fa8", "148ce5be-5820-4e11-b42b-74e19db5cbcc", "3a4f7e65-5e62-4a74-b618-79bd653154f5", "dff05507-a49a-428e-9d27-a0b7f8eef2a3", "096dc07e-d292-4553-b887-69b2ab9aef78"], "durationId": "d02694ec-598d-4566-81d9-0f210fdee419"}}, {"head": {"id": "e6f32e5d-9423-4463-9a43-a0fcbd006f3a", "name": "Configuration task cost before running: 213 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194509605900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8494c91e-4efa-4a0b-9a99-e5091fe74624", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194515261200, "endTime": 316194524915100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "dc02c7e3-025c-49b0-9793-5f07bc94706f", "logId": "dcdb4d25-3f6a-4759-9a2a-260e0a4af119"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc02c7e3-025c-49b0-9793-5f07bc94706f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194511641000}, "additional": {"logType": "detail", "children": [], "durationId": "8494c91e-4efa-4a0b-9a99-e5091fe74624"}}, {"head": {"id": "093b534f-b1f6-4450-8105-3f68eb11c649", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194512175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca71557b-8d3e-4ddd-a8fe-dd9913e32873", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194512273200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44573858-719e-453c-89bf-e60e5ca97023", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194515278800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "557f6490-6d84-4725-ad23-8ae7879e6c5d", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194524693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1557049-61de-478b-86b3-3872fd407650", "name": "entry : default@PreBuild cost memory 0.2727203369140625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194524834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdb4d25-3f6a-4759-9a2a-260e0a4af119", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194515261200, "endTime": 316194524915100}, "additional": {"logType": "info", "children": [], "durationId": "8494c91e-4efa-4a0b-9a99-e5091fe74624"}}, {"head": {"id": "c321a92a-2701-4a36-a387-47b21d30eafb", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194531149000, "endTime": 316194533355900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ad10fae9-c82a-4025-8097-fdfe86b74522", "logId": "de9fdd7c-7ddf-44de-94df-7c9ee013b7c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad10fae9-c82a-4025-8097-fdfe86b74522", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194529738700}, "additional": {"logType": "detail", "children": [], "durationId": "c321a92a-2701-4a36-a387-47b21d30eafb"}}, {"head": {"id": "a0d390b6-1f05-4114-90f4-937b310738f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194530318400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "585f4057-7e34-488d-b309-b23316e89f7f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194530422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b210cc-cf45-4b07-9a4d-ec0d3754afdc", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194531159600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1825f1a3-5de2-42ab-ac50-b7d62a3c7001", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194533177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c874f66a-d234-4026-8883-10121223f67a", "name": "entry : default@MergeProfile cost memory 0.11063385009765625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194533283600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9fdd7c-7ddf-44de-94df-7c9ee013b7c1", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194531149000, "endTime": 316194533355900}, "additional": {"logType": "info", "children": [], "durationId": "c321a92a-2701-4a36-a387-47b21d30eafb"}}, {"head": {"id": "9fdd0883-5bd6-4ea6-b507-b8b259489b8d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194537025400, "endTime": 316194539648500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "77a45c76-4aca-469e-825c-391debcdb7a1", "logId": "d8529277-847e-4369-924e-2cc6b70cd767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77a45c76-4aca-469e-825c-391debcdb7a1", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194535312100}, "additional": {"logType": "detail", "children": [], "durationId": "9fdd0883-5bd6-4ea6-b507-b8b259489b8d"}}, {"head": {"id": "6daeeddc-b495-4833-866f-bcd7040e9978", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194535843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1246dd90-abe3-457d-bfcf-ca284989dbb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194535943900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c0b332-c6b0-4304-a36a-b5469c808fb9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194537040500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "442f38e9-e55d-47a9-8080-3cc5df3c1aa2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194538031400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4ec4b6-e941-4fe2-a07e-5ec42e78cb7e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194539453300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4074b7ee-ccee-4ca8-ab3d-4b0a401416c9", "name": "entry : default@CreateBuildProfile cost memory 0.09618377685546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194539569700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8529277-847e-4369-924e-2cc6b70cd767", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194537025400, "endTime": 316194539648500}, "additional": {"logType": "info", "children": [], "durationId": "9fdd0883-5bd6-4ea6-b507-b8b259489b8d"}}, {"head": {"id": "74a9be03-72e4-48fb-b568-9d4e7a053254", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543287100, "endTime": 316194544041800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6dd2c346-cff6-4c44-86fa-c6d4da9c8aa1", "logId": "643c9536-184f-4529-909e-8136af53ec1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dd2c346-cff6-4c44-86fa-c6d4da9c8aa1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194541509900}, "additional": {"logType": "detail", "children": [], "durationId": "74a9be03-72e4-48fb-b568-9d4e7a053254"}}, {"head": {"id": "7867fedd-418c-4009-81d4-4b792e7b9103", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194542130200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ed39ba-6263-4415-bb15-d22461861491", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194542258700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555e15cd-0817-4c9f-a148-6760f8e4a418", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718aaacd-15fc-40ee-8f35-00248df8de6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acb3bc5-a8ea-49a9-ba0e-738b500b2a70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be54ca1-1317-424e-bfa2-85689952ccc5", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543756800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34a4438a-bcae-4679-8dfb-9f1b07e26338", "name": "runTaskFromQueue task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643c9536-184f-4529-909e-8136af53ec1c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194543287100, "endTime": 316194544041800, "totalTime": 565600}, "additional": {"logType": "info", "children": [], "durationId": "74a9be03-72e4-48fb-b568-9d4e7a053254"}}, {"head": {"id": "2ba002a7-6583-4936-b5ca-39234a0bcabb", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194558062800, "endTime": 316194559940900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ee593321-85cd-49b4-99fd-7f327519d506", "logId": "6232e7db-b7e2-4974-99b9-eb735f6bafce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee593321-85cd-49b4-99fd-7f327519d506", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194546319000}, "additional": {"logType": "detail", "children": [], "durationId": "2ba002a7-6583-4936-b5ca-39234a0bcabb"}}, {"head": {"id": "e627cde3-b803-4ef2-88d2-89154146f3b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194547059100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30805db9-0a55-4408-a39e-5798b5fae419", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194547255900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96576fdb-dec6-41e5-bcb5-db2d7a11f210", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194558088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "944626cb-d549-4cfc-9f0a-7706ea8d72a0", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194558410200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1cde2b-6fe3-4d21-85cc-cd61c0a73b23", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194559585900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4512e6-55fe-43b7-9eb5-d9e54086cc70", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06561279296875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194559797200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6232e7db-b7e2-4974-99b9-eb735f6bafce", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194558062800, "endTime": 316194559940900}, "additional": {"logType": "info", "children": [], "durationId": "2ba002a7-6583-4936-b5ca-39234a0bcabb"}}, {"head": {"id": "6c17cb4e-a78b-4c7a-ac70-3a97e638025a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194566057100, "endTime": 316194568325900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6814a517-cbc9-4426-a9cc-c0cfe8554085", "logId": "ca225b7a-21e9-4e2a-917c-a94d1676de7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6814a517-cbc9-4426-a9cc-c0cfe8554085", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194562874400}, "additional": {"logType": "detail", "children": [], "durationId": "6c17cb4e-a78b-4c7a-ac70-3a97e638025a"}}, {"head": {"id": "25332a8b-074a-4772-b51a-ddb873b1d2d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194563754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5699727c-d29a-4d8e-9b6e-b7a37ac5b9b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194563923100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a620089-4f78-458b-a93d-2914082ec464", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194566078000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004c4290-9464-466b-8c26-7f73d6fcb0e3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194567982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faca2a19-8d2c-4a46-b2a3-f9e36db52e7b", "name": "entry : default@ProcessProfile cost memory 0.05654144287109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194568188700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca225b7a-21e9-4e2a-917c-a94d1676de7d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194566057100, "endTime": 316194568325900}, "additional": {"logType": "info", "children": [], "durationId": "6c17cb4e-a78b-4c7a-ac70-3a97e638025a"}}, {"head": {"id": "972928f5-454c-4988-b7b5-6f8482383dfe", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194577056500, "endTime": 316194587328200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "107da8b0-f877-4afe-a1b5-ad5a6a17a8cb", "logId": "ddd026df-4bc2-4190-8fb0-8741a14f7278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "107da8b0-f877-4afe-a1b5-ad5a6a17a8cb", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194571626900}, "additional": {"logType": "detail", "children": [], "durationId": "972928f5-454c-4988-b7b5-6f8482383dfe"}}, {"head": {"id": "05dd0349-99a6-474e-8899-64fc82f8d140", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194572687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f8ec48-0cda-4095-a58a-3324c9fcb47a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194572881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e598edb5-9cb8-40df-8116-df06519031d5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194577086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8602a55-10a6-40a5-b39e-ef214233c674", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194587000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696b03f2-49ad-4c9a-b82e-bec43a391e92", "name": "entry : default@ProcessRouterMap cost memory 0.188079833984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194587197900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddd026df-4bc2-4190-8fb0-8741a14f7278", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194577056500, "endTime": 316194587328200}, "additional": {"logType": "info", "children": [], "durationId": "972928f5-454c-4988-b7b5-6f8482383dfe"}}, {"head": {"id": "a87dae99-6e49-4d4c-b198-73c67ee24a3a", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194598844100, "endTime": 316194603499400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c8391584-eaf6-484b-89d3-6ba2729e488a", "logId": "c03845eb-28d1-43fa-bfe9-d1bb091dd972"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8391584-eaf6-484b-89d3-6ba2729e488a", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194592509300}, "additional": {"logType": "detail", "children": [], "durationId": "a87dae99-6e49-4d4c-b198-73c67ee24a3a"}}, {"head": {"id": "cd66d66c-73ac-4455-9a92-59e49808131b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194593400700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5405e7b0-82c1-4c45-aaa2-ee419e733c06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194593575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ff6806-c139-4ea0-ad34-71b75adcdb92", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194595241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e2fc4b-3003-4496-b4ab-18d0d5aa5cc7", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194600693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd73921-8718-4fa3-a6bc-0c0795efde99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194600950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948f81b0-ecf3-4134-8a7d-38b4c766a4e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194601054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543338cb-3030-4fb4-9e55-fa68fdc8448d", "name": "entry : default@PreviewProcessResource cost memory 0.0684051513671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194601200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcb8a31-abb9-425a-83a2-6847fa34191f", "name": "runTaskFromQueue task cost before running: 306 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194603312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c03845eb-28d1-43fa-bfe9-d1bb091dd972", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194598844100, "endTime": 316194603499400, "totalTime": 2456500}, "additional": {"logType": "info", "children": [], "durationId": "a87dae99-6e49-4d4c-b198-73c67ee24a3a"}}, {"head": {"id": "746102c1-157c-4246-81ba-b4b6bbedefc5", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194612647800, "endTime": 316194636023100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8dc3d445-89d1-4fd8-9391-74bda5943928", "logId": "105d2f50-d967-4b85-84cf-b7048e724d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dc3d445-89d1-4fd8-9391-74bda5943928", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194607814700}, "additional": {"logType": "detail", "children": [], "durationId": "746102c1-157c-4246-81ba-b4b6bbedefc5"}}, {"head": {"id": "c3e96229-0ebb-4467-85b5-f9939a0a8c4b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194608530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5872568f-8710-4609-af53-652ad2dbbb93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194608669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f396c708-3267-4561-9a5b-d8d42f8c1dca", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194612666900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4392090e-6e39-4f42-9972-c50459167aa4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194635808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3df65e4-eeb4-4ea2-8703-1fdd6f5913c9", "name": "entry : default@GenerateLoaderJson cost memory -1.0028839111328125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194635948700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105d2f50-d967-4b85-84cf-b7048e724d60", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194612647800, "endTime": 316194636023100}, "additional": {"logType": "info", "children": [], "durationId": "746102c1-157c-4246-81ba-b4b6bbedefc5"}}, {"head": {"id": "e791a838-4323-40ca-b000-e976a5a26371", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194648737000, "endTime": 316194895619800}, "additional": {"children": ["902dea37-f178-4785-b381-34d899a37e24", "4837b6a8-fab7-437f-82c9-9649031332fd", "2efe980c-2390-4fd4-9d6a-745e71cacedf", "9e9ee3f9-6d31-41f2-8c6a-fe2b8d8d8af5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "28925a36-6fa8-4585-9a0f-5c697c54ac6c", "logId": "97d88808-d87f-47c2-8829-8fa6e48cfb12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28925a36-6fa8-4585-9a0f-5c697c54ac6c", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194644424500}, "additional": {"logType": "detail", "children": [], "durationId": "e791a838-4323-40ca-b000-e976a5a26371"}}, {"head": {"id": "47399ffd-9581-4687-ad7d-410fefbca459", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194644971000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1aa9d8-460b-46ab-8ebd-7a84062c46ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194645071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d036c6-88a0-432b-997a-5f1c3e231d74", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194646067500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e5a3c7-26d8-4f13-b41c-4da04c67fdfb", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194648771700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e808c9ce-c828-45ab-b749-4b4cabccc14a", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194677066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97cd6590-414c-49dd-a196-8c4b3570686d", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194677304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902dea37-f178-4785-b381-34d899a37e24", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194679223700, "endTime": 316194697509600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e791a838-4323-40ca-b000-e976a5a26371", "logId": "13197b2a-5925-4d46-b8fd-dd06e308845c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13197b2a-5925-4d46-b8fd-dd06e308845c", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194679223700, "endTime": 316194697509600}, "additional": {"logType": "info", "children": [], "durationId": "902dea37-f178-4785-b381-34d899a37e24", "parent": "97d88808-d87f-47c2-8829-8fa6e48cfb12"}}, {"head": {"id": "3d97d8ee-1e09-4118-8b81-21bc438240d7", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194697823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4837b6a8-fab7-437f-82c9-9649031332fd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194698719000, "endTime": 316194729141700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e791a838-4323-40ca-b000-e976a5a26371", "logId": "836b8428-0526-4d3a-a1c3-d8e2c41c3db8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2715f8cf-f9ed-4617-adf0-e1806d2ea72b", "name": "current process  memoryUsage: {\n  rss: 110772224,\n  heapTotal: 122249216,\n  heapUsed: 111356336,\n  external: 3108429,\n  arrayBuffers: 102294\n} os memoryUsage :6.328525543212891", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194699677900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f88e85-60fc-4a49-9f34-3d3ddb194022", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194726407700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "836b8428-0526-4d3a-a1c3-d8e2c41c3db8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194698719000, "endTime": 316194729141700}, "additional": {"logType": "info", "children": [], "durationId": "4837b6a8-fab7-437f-82c9-9649031332fd", "parent": "97d88808-d87f-47c2-8829-8fa6e48cfb12"}}, {"head": {"id": "a414c35a-f21f-41d6-87a3-ee16048040d1", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194729277200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2efe980c-2390-4fd4-9d6a-745e71cacedf", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194730636900, "endTime": 316194787811500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e791a838-4323-40ca-b000-e976a5a26371", "logId": "14fcef00-d22d-4058-951d-46fa412441df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c26deaf-331d-46eb-bc30-ec322a8211ce", "name": "current process  memoryUsage: {\n  rss: 110776320,\n  heapTotal: 122249216,\n  heapUsed: 111620096,\n  external: 3108555,\n  arrayBuffers: 102435\n} os memoryUsage :6.338932037353516", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194732042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea69d234-c506-404a-8b7f-cc675925f1ed", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194784517100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14fcef00-d22d-4058-951d-46fa412441df", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194730636900, "endTime": 316194787811500}, "additional": {"logType": "info", "children": [], "durationId": "2efe980c-2390-4fd4-9d6a-745e71cacedf", "parent": "97d88808-d87f-47c2-8829-8fa6e48cfb12"}}, {"head": {"id": "5e20f5a8-f990-4e96-b9e2-358795becefc", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194788065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9ee3f9-6d31-41f2-8c6a-fe2b8d8d8af5", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194789690000, "endTime": 316194894091200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e791a838-4323-40ca-b000-e976a5a26371", "logId": "43c5713e-9344-4da8-bcc0-c0f2dcd61f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd3abd18-8d3c-4c49-a0f8-78af92358e85", "name": "current process  memoryUsage: {\n  rss: 110780416,\n  heapTotal: 122249216,\n  heapUsed: 111908000,\n  external: 3108681,\n  arrayBuffers: 103375\n} os memoryUsage :6.307151794433594", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194791350900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65f37ed4-1180-42a8-be27-d449bbbf1bf7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194890491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c5713e-9344-4da8-bcc0-c0f2dcd61f66", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194789690000, "endTime": 316194894091200}, "additional": {"logType": "info", "children": [], "durationId": "9e9ee3f9-6d31-41f2-8c6a-fe2b8d8d8af5", "parent": "97d88808-d87f-47c2-8829-8fa6e48cfb12"}}, {"head": {"id": "b2cdf18a-3cb2-4c85-9080-a27f767978a3", "name": "entry : default@PreviewCompileResource cost memory -0.0105133056640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194895212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e5aeb65-c6b4-4555-b92c-dfb4963dac7e", "name": "runTaskFromQueue task cost before running: 599 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194895500400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d88808-d87f-47c2-8829-8fa6e48cfb12", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194648737000, "endTime": 316194895619800, "totalTime": 246680500}, "additional": {"logType": "info", "children": ["13197b2a-5925-4d46-b8fd-dd06e308845c", "836b8428-0526-4d3a-a1c3-d8e2c41c3db8", "14fcef00-d22d-4058-951d-46fa412441df", "43c5713e-9344-4da8-bcc0-c0f2dcd61f66"], "durationId": "e791a838-4323-40ca-b000-e976a5a26371"}}, {"head": {"id": "ac499e4f-2e1f-42b3-9d36-0771f76b25bd", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900416700, "endTime": 316194900977700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fb8aa7f3-d824-489d-8da2-b5ff48396111", "logId": "4c104eb7-9883-4d2a-b9c0-dc81111bf867"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb8aa7f3-d824-489d-8da2-b5ff48396111", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194899501000}, "additional": {"logType": "detail", "children": [], "durationId": "ac499e4f-2e1f-42b3-9d36-0771f76b25bd"}}, {"head": {"id": "5aab6d67-ef69-4493-be5e-8154ff5100c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900162200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d56f9c6-37db-4ddb-b10e-04357288c2b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900306000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90880886-a88f-49e3-b9f7-34d0c8ac8c65", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900430800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1904fbc4-8cb3-4e6f-ba44-4a9844c71011", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc4a8ef-4e05-4c15-a604-79690605a074", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900639400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2759e7-1fcd-49ad-9a8c-914b894408bb", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384521484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900755800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7538e590-bd76-49a8-bfcd-5995c8b9116e", "name": "runTaskFromQueue task cost before running: 604 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c104eb7-9883-4d2a-b9c0-dc81111bf867", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194900416700, "endTime": 316194900977700, "totalTime": 435100}, "additional": {"logType": "info", "children": [], "durationId": "ac499e4f-2e1f-42b3-9d36-0771f76b25bd"}}, {"head": {"id": "5f39781d-9667-4556-a135-89e5437f50d8", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194904945100, "endTime": 316194914933300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "8553119d-d999-4a8d-9250-e3a07f69140c", "logId": "30b0c7f8-949d-4920-b385-be4130954111"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8553119d-d999-4a8d-9250-e3a07f69140c", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194903357400}, "additional": {"logType": "detail", "children": [], "durationId": "5f39781d-9667-4556-a135-89e5437f50d8"}}, {"head": {"id": "ca8b5ff9-8a7b-4187-aa03-ef5be0ca2f51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194903973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2f671d-8580-49f1-a152-20e75e8be383", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194904086800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db86da6d-825e-41f9-b86f-74b993a536eb", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194904963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c9637f-a7ff-422d-ba8c-cc83a733caaa", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194906789900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b79763-97e2-458f-b630-da478cd221c8", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194906940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa86b559-ea07-4113-a407-406404286754", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194907055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04330acb-42ed-4b9c-9842-566406f167d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194907119000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057709e9-f64a-4093-9662-78d647bb5ea5", "name": "entry : default@CopyPreviewProfile cost memory 0.20832061767578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194914530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32760f04-1bab-49c2-861b-743ad391a8ab", "name": "runTaskFromQueue task cost before running: 618 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194914819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b0c7f8-949d-4920-b385-be4130954111", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194904945100, "endTime": 316194914933300, "totalTime": 9804100}, "additional": {"logType": "info", "children": [], "durationId": "5f39781d-9667-4556-a135-89e5437f50d8"}}, {"head": {"id": "03b01f27-570e-47d1-bf18-c6ed3b30e22c", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194920977100, "endTime": 316194922454600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7137e32a-d580-4cf3-ad0f-8d8ab652764f", "logId": "34e6147f-ee5f-4cc3-8f26-1203a5ce2add"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7137e32a-d580-4cf3-ad0f-8d8ab652764f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194918511000}, "additional": {"logType": "detail", "children": [], "durationId": "03b01f27-570e-47d1-bf18-c6ed3b30e22c"}}, {"head": {"id": "f50b295a-9263-4492-8822-3adb536dc702", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194919356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e5c6c3-7eec-4850-9b68-7ad31fb2dd1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194919526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33b96d55-2eb9-4fb0-a584-e8077f953bbe", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194920998400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc51570-7340-4861-9760-85e78c17b2cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194921237600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "059d796d-a9fd-45e0-9fd6-2187ba2ede82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194921355400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bf5802-c637-458b-b0f5-75abd48779c3", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194921527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6fd686-5082-4742-9b68-383cad997719", "name": "runTaskFromQueue task cost before running: 626 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194922363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e6147f-ee5f-4cc3-8f26-1203a5ce2add", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194920977100, "endTime": 316194922454600, "totalTime": 1356700}, "additional": {"logType": "info", "children": [], "durationId": "03b01f27-570e-47d1-bf18-c6ed3b30e22c"}}, {"head": {"id": "6f8cf0b2-609d-4889-857c-d8f2318834d9", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194925892100, "endTime": 316194926332400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c1a02e0a-5c5f-4b8d-ae1e-08aa98da7e45", "logId": "4601cd81-b6d4-4ac2-8e3f-9c538cd6c4a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1a02e0a-5c5f-4b8d-ae1e-08aa98da7e45", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194925802900}, "additional": {"logType": "detail", "children": [], "durationId": "6f8cf0b2-609d-4889-857c-d8f2318834d9"}}, {"head": {"id": "e4faa6df-1c6b-42a3-afa0-9289fa90f82c", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194925908700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2449e781-bc2a-4a5b-8289-466dc1857daf", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194926117400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc4a3efc-e0bb-41f0-b37b-04bf28c555c8", "name": "runTaskFromQueue task cost before running: 629 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194926256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4601cd81-b6d4-4ac2-8e3f-9c538cd6c4a3", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194925892100, "endTime": 316194926332400, "totalTime": 330400}, "additional": {"logType": "info", "children": [], "durationId": "6f8cf0b2-609d-4889-857c-d8f2318834d9"}}, {"head": {"id": "0a033cc2-07d7-4522-8af7-223e90923d83", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194932053200, "endTime": 316194938605700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "0dce8a44-cb73-4f12-b294-65c11b3fd012", "logId": "860df3a3-8f8a-4070-86c4-fece9158ee5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dce8a44-cb73-4f12-b294-65c11b3fd012", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194929892700}, "additional": {"logType": "detail", "children": [], "durationId": "0a033cc2-07d7-4522-8af7-223e90923d83"}}, {"head": {"id": "c932edb5-8705-48f0-b9f2-3e4452101b69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194930698200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a5248a-7118-4f40-80e4-efbc49d3f442", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194930838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d409aed8-365f-4abe-ac32-822048a0e98c", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194932070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d9a9f33-67d1-4993-81e4-40dde7318968", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194936028700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a6926f-eb12-4266-95a1-63b3e35d98c8", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194936247300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce158672-794b-423f-a0eb-efd5eda490cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194936420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e04c3b53-978b-46a7-9c23-19461f9d7661", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194936680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb948b7-31da-4774-9f07-e749c624af7e", "name": "entry : default@PreviewUpdateAssets cost memory 0.13263702392578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194938156500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4c5108-b869-472b-b263-a173c66da7ef", "name": "runTaskFromQueue task cost before running: 642 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194938450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "860df3a3-8f8a-4070-86c4-fece9158ee5b", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194932053200, "endTime": 316194938605700, "totalTime": 6343600}, "additional": {"logType": "info", "children": [], "durationId": "0a033cc2-07d7-4522-8af7-223e90923d83"}}, {"head": {"id": "bbea7ef9-9a18-454f-a815-c711ab74644e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194955854600, "endTime": 316213401974000}, "additional": {"children": ["100121b8-dbb0-46c1-af18-66c8eb784ea5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "64fc5919-576f-4428-847f-02ecf85c74ef", "logId": "26a7f4ca-2885-46d3-a754-5868c999bbb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64fc5919-576f-4428-847f-02ecf85c74ef", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194943072800}, "additional": {"logType": "detail", "children": [], "durationId": "bbea7ef9-9a18-454f-a815-c711ab74644e"}}, {"head": {"id": "bcab5f12-9907-4ea1-93b9-567b00cbe2bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194944062400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e11ae0c-e52d-4b19-97f4-3669c1ae47b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194944519400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d7dd30-1b8c-4a29-bdd4-1aa832887e94", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194955881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316194998910900, "endTime": 316213383596500}, "additional": {"children": ["6812986a-bc6a-4ef5-a0ac-032bf5c045ff", "499f55ee-21ac-40c5-bc29-d9e6889d4bce", "fac2d467-e1e8-4c0d-912e-af901ee6667e", "435fc092-c97e-4d0d-a899-f674967a9385", "b20434d8-3bbd-4972-b846-a08ade61532e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bbea7ef9-9a18-454f-a815-c711ab74644e", "logId": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2c25a96-78e3-4b22-81d0-5b43a69c98c3", "name": "entry : default@PreviewArkTS cost memory -0.552825927734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316195002301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98291bab-b435-4f47-a77d-6d1dae7eab24", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316200604428700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6812986a-bc6a-4ef5-a0ac-032bf5c045ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316200606755400, "endTime": 316200606791200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "logId": "2b917f1d-baef-45d3-95da-0566618a6d01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b917f1d-baef-45d3-95da-0566618a6d01", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316200606755400, "endTime": 316200606791200}, "additional": {"logType": "info", "children": [], "durationId": "6812986a-bc6a-4ef5-a0ac-032bf5c045ff", "parent": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}}, {"head": {"id": "d2805ba4-4e07-4691-b276-e90bdb4f2cf7", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213381866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499f55ee-21ac-40c5-bc29-d9e6889d4bce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316213383055200, "endTime": 316213383073700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "logId": "dc5b9775-eb26-4b2f-8175-1aa80ca13e82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc5b9775-eb26-4b2f-8175-1aa80ca13e82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213383055200, "endTime": 316213383073700}, "additional": {"logType": "info", "children": [], "durationId": "499f55ee-21ac-40c5-bc29-d9e6889d4bce", "parent": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}}, {"head": {"id": "e5d5178e-0e15-4bda-ab2c-405f49453c21", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316194998910900, "endTime": 316213383596500}, "additional": {"logType": "info", "children": ["2b917f1d-baef-45d3-95da-0566618a6d01", "dc5b9775-eb26-4b2f-8175-1aa80ca13e82", "b55c3c2c-d730-44ee-a308-1b8d277d9dbf", "6d73b4c2-a676-4ba1-9137-f024d6f2ecc7", "4ccff657-9339-4556-9873-078f9a30806f"], "durationId": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "parent": "26a7f4ca-2885-46d3-a754-5868c999bbb6"}}, {"head": {"id": "fac2d467-e1e8-4c0d-912e-af901ee6667e", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316198561831400, "endTime": 316200562535600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "logId": "b55c3c2c-d730-44ee-a308-1b8d277d9dbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b55c3c2c-d730-44ee-a308-1b8d277d9dbf", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316198561831400, "endTime": 316200562535600}, "additional": {"logType": "info", "children": [], "durationId": "fac2d467-e1e8-4c0d-912e-af901ee6667e", "parent": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}}, {"head": {"id": "435fc092-c97e-4d0d-a899-f674967a9385", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316200562790200, "endTime": 316200562938800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "logId": "6d73b4c2-a676-4ba1-9137-f024d6f2ecc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d73b4c2-a676-4ba1-9137-f024d6f2ecc7", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316200562790200, "endTime": 316200562938800}, "additional": {"logType": "info", "children": [], "durationId": "435fc092-c97e-4d0d-a899-f674967a9385", "parent": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}}, {"head": {"id": "b20434d8-3bbd-4972-b846-a08ade61532e", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker11", "startTime": 316200563069900, "endTime": 316213381923300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "100121b8-dbb0-46c1-af18-66c8eb784ea5", "logId": "4ccff657-9339-4556-9873-078f9a30806f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ccff657-9339-4556-9873-078f9a30806f", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316200563069900, "endTime": 316213381923300}, "additional": {"logType": "info", "children": [], "durationId": "b20434d8-3bbd-4972-b846-a08ade61532e", "parent": "e5d5178e-0e15-4bda-ab2c-405f49453c21"}}, {"head": {"id": "26a7f4ca-2885-46d3-a754-5868c999bbb6", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194955854600, "endTime": 316213401974000, "totalTime": 18446102500}, "additional": {"logType": "info", "children": ["e5d5178e-0e15-4bda-ab2c-405f49453c21"], "durationId": "bbea7ef9-9a18-454f-a815-c711ab74644e"}}, {"head": {"id": "6a3f5665-01c1-4c76-b118-3f508835f9f8", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408364200, "endTime": 316213408709900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9d49e4a6-daa5-437a-a3ee-1ee66cec9dd8", "logId": "16ef9a54-7ef7-4473-88e5-d9ed7f21c069"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d49e4a6-daa5-437a-a3ee-1ee66cec9dd8", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408296800}, "additional": {"logType": "detail", "children": [], "durationId": "6a3f5665-01c1-4c76-b118-3f508835f9f8"}}, {"head": {"id": "ca60d380-6adb-4fb6-9f70-b4ae6e3387f7", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408379700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74947402-7026-4d15-bb02-0661cbc1bb62", "name": "entry : PreviewBuild cost memory 0.0116729736328125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2f05f7-004d-4247-8a26-56ba342c704f", "name": "runTaskFromQueue task cost before running: 19 s 112 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408636400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ef9a54-7ef7-4473-88e5-d9ed7f21c069", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213408364200, "endTime": 316213408709900, "totalTime": 247800}, "additional": {"logType": "info", "children": [], "durationId": "6a3f5665-01c1-4c76-b118-3f508835f9f8"}}, {"head": {"id": "fe4c1905-6de2-43a0-ae2b-e1ac3ca40f0f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416234800, "endTime": 316213416257600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "765a64dd-847d-4b98-83c4-5e08406ef0ba", "logId": "39618ca8-a786-47aa-b7a4-5819b7b2d4fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39618ca8-a786-47aa-b7a4-5819b7b2d4fc", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416234800, "endTime": 316213416257600}, "additional": {"logType": "info", "children": [], "durationId": "fe4c1905-6de2-43a0-ae2b-e1ac3ca40f0f"}}, {"head": {"id": "21473129-e732-49ab-abfe-1719b18f026b", "name": "BUILD SUCCESSFUL in 19 s 119 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416354900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "035fb638-6bfc-43f4-a964-2436b87dba08", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316194297326900, "endTime": 316213416618200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1d455c01-4e74-43bf-bc1b-7840d10a3c7e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416641700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1158fe6c-7170-46fc-b396-c158b6ac4e33", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416715700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "525b4a81-694f-4bdf-a065-0725bcc47318", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416771700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5803b00-4573-4ad1-9514-2385d940f603", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22510a2-b0ce-4964-ad3f-661559209c14", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9254f907-4415-4632-a202-0e5e253c7662", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416938300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c5a875-37cb-436c-8f4b-c73a2136d140", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213416990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d4438a-cf6f-425f-a4ed-974e8b68e834", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213417849800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a4180f2-7aa8-43f1-b71e-ccb388f8c7c2", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213426183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee6ff36-87d6-482b-ae12-91483f4e2edf", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213426635200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "051c8a7f-75cc-4dba-bb33-44f14a8e719f", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213438635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f6e01a-e921-42be-b7ba-648ef30cf8b9", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213439345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16779342-4c7f-474e-abc7-cc60bcccc064", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213439555100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d8da76-2a3e-429f-a7df-b7777c4f33c0", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213440392800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56299065-fa1b-4bfc-b8e0-8c9cbbed5a33", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213441255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63ec54a-3207-49d0-950f-64e4a6bc7ccf", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213441642000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2d5241-7e1c-4a1f-b7fa-927fb046b6f9", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213441973900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4709d83-ff96-4fb0-adb2-55981684aa95", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213442324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fa1b00-f231-4c31-b869-8b5ceb008515", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213445285900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36c3cef-11d4-4d44-8515-3afb40f0881b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213446148900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9511dc5f-4afe-408a-8f90-5bf9d61703a6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213446457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f28e132-5de2-4f4b-9bd4-5df57df5be4b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213446762100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cede3070-a9c4-4583-8249-0ebf385908c9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213447637800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c6bd6e-b1cb-4ede-b167-5478c4032432", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213459271200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c60f5019-c6f6-4e8a-90e0-6a47b104f612", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213459740500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054a5cf4-95c2-4b2a-9b11-d6a815498dc8", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213460112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e156dcb6-b372-41d2-bdb3-b22817023810", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213461326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cdb68d-0b6b-4666-8345-824f23e870ce", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316213461605200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}