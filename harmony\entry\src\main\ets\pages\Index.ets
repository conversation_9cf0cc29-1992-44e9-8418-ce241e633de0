import router from '@ohos.router';
import { storageManager } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';

@Entry
@Component
struct Index {
  @State isLoggedIn: boolean = false;
  @State isLoading: boolean = true;

  aboutToAppear() {
    console.log('=== Index页面启动 ===');
    // 为了测试，直接跳转到MyBankCardPage
    setTimeout(() => {
      console.log('开始测试跳转到MyBankCardPage');
      router.replaceUrl({
        url: 'pages/MyBankCardPage'
      }).then(() => {
        console.log('Index -> MyBankCardPage 跳转成功');
      }).catch((error: Error) => {
        console.error('Index -> MyBankCardPage 跳转失败:', error.message);
        // 如果跳转失败，回退到原来的逻辑
        this.checkLoginStatus();
      });
    }, 1000);
  }

  async checkLoginStatus() {
    try {
      // 检查本地存储的登录状态
      const isLoggedIn = await storageManager.isLoggedIn();

      if (isLoggedIn) {
        // 已登录，获取token并设置到HTTP客户端
        const token = await storageManager.getUserToken();
        if (token) {
          httpClient.setAuthToken(token);
        }

        // 跳转到我的银行卡页面
        router.replaceUrl({
          url: 'pages/MyBankCardPage'
        });
      } else {
        // 未登录，跳转到登录页
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      // 出错时跳转到登录页
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
    }
  }

  build() {
    Column() {
      Image($r('app.media.app_icon'))
        .width(120)
        .height(120)
        .margin({ bottom: 20 })

      Text('E-Wallet')
        .fontSize(32)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1976D2')
        .margin({ bottom: 10 })

      Text('电子钱包')
        .fontSize(16)
        .fontColor('#666666')
        .margin({ bottom: 40 })

      if (this.isLoading) {
        LoadingProgress()
          .width(40)
          .height(40)
          .color('#1976D2')

        Text('正在加载...')
          .fontSize(14)
          .fontColor('#999999')
          .margin({ top: 20 })
      } else {
        Button('进入应用')
          .width(200)
          .height(48)
          .fontSize(16)
          .fontColor(Color.White)
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .onClick(() => {
            router.pushUrl({
              url: 'pages/LoginPage'
            });
          })
      }
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor('#F5F5F5')
  }
}