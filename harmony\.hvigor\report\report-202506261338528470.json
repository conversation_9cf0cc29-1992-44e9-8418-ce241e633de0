{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "560b240c-7470-4bc2-ae9a-637f67ac4b00", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 319787919413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92e3880-2a8e-44ea-b40f-60cf1cba586a", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 319788011371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6444c819-d044-400a-8182-6268f84857ba", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 319788012202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6719291-551e-45fa-a270-c3761cddc6ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499117770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499165984500, "endTime": 323501301159900}, "additional": {"children": ["d0cde7f4-670e-4a0f-ba49-90a31bc136e2", "c312964a-c53a-4846-aaf9-2b57f993818a", "6246bb3c-43a6-4b6e-a6ca-c32171625178", "29b45cde-22e3-4684-a4a1-adb91cc54330", "58be3e98-f1b6-471b-879c-0066370758e3", "97963934-f013-4555-a76c-a3f2bac9c44d", "9aff1768-13bf-473a-aae6-d41419957505"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "597069e5-7669-4f9a-b077-8ec3c687d21f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0cde7f4-670e-4a0f-ba49-90a31bc136e2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499165988400, "endTime": 323499349877900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "d884c291-8549-4aac-9974-323de6c1f8e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c312964a-c53a-4846-aaf9-2b57f993818a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499349910300, "endTime": 323501294896100}, "additional": {"children": ["62f5a055-9376-4540-bd72-fc85d9f3b3aa", "b4411a29-7758-49e2-8aea-100994d257e8", "9f3ea434-e991-4498-8503-64bfe60e7993", "9b4c4faf-f29e-45f6-8a65-6e63b04f15a6", "d6dcc62a-9fbc-4523-badd-f77ebbe542da", "218a1321-1bbe-4cf0-bc95-9d9dd3244c20", "87fcfff3-d799-4f66-a503-9fcc40944b18", "d335b667-0299-4b18-9ce8-bce643bccb47", "1228bdb0-b610-49a5-b0bb-5fdcf72c2757"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6246bb3c-43a6-4b6e-a6ca-c32171625178", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501294987500, "endTime": 323501301098900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "1c93ce01-eedc-4b1a-9b2d-ad361a23a40c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29b45cde-22e3-4684-a4a1-adb91cc54330", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501301132700, "endTime": 323501301139100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "6b0ea6d8-3095-4f2e-af4e-e1e29a31c078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58be3e98-f1b6-471b-879c-0066370758e3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499271308600, "endTime": 323499271483800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "c9d8c73b-90be-448c-885d-8d3dd5e73baa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9d8c73b-90be-448c-885d-8d3dd5e73baa", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499271308600, "endTime": 323499271483800}, "additional": {"logType": "info", "children": [], "durationId": "58be3e98-f1b6-471b-879c-0066370758e3", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "97963934-f013-4555-a76c-a3f2bac9c44d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499293884000, "endTime": 323499293908700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "b4fe600a-4172-437f-bb20-7efcfb8a4cb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4fe600a-4172-437f-bb20-7efcfb8a4cb7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499293884000, "endTime": 323499293908700}, "additional": {"logType": "info", "children": [], "durationId": "97963934-f013-4555-a76c-a3f2bac9c44d", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "d373f6d7-bfc0-4df2-9923-338fd62754b1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499315398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effc66cd-50ff-4a6a-876b-3e70bfced8ca", "name": "Cache service initialization finished in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499349543800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d884c291-8549-4aac-9974-323de6c1f8e4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499165988400, "endTime": 323499349877900}, "additional": {"logType": "info", "children": [], "durationId": "d0cde7f4-670e-4a0f-ba49-90a31bc136e2", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "62f5a055-9376-4540-bd72-fc85d9f3b3aa", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499368029300, "endTime": 323499368046800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "b50f2c11-a922-4a12-a4b8-66f180ebad5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4411a29-7758-49e2-8aea-100994d257e8", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499368085200, "endTime": 323499379876600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "47efb816-0d1b-48a8-838e-f6695eca33f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f3ea434-e991-4498-8503-64bfe60e7993", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499379895600, "endTime": 323500759754900}, "additional": {"children": ["38184a85-7894-4f57-a3a2-336df1996c59", "e6074dfd-c3db-49ff-b5e4-fc97655fd665", "3c2b9822-8b88-4cf0-b699-8ee6e89dfd21"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "f5600e2f-a957-426c-bcce-059868bb5f1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b4c4faf-f29e-45f6-8a65-6e63b04f15a6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500759788400, "endTime": 323500989600200}, "additional": {"children": ["e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "92461824-ad7d-4ee9-86c4-9919df7e20d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6dcc62a-9fbc-4523-badd-f77ebbe542da", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500989607900, "endTime": 323501018933400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "cce5c26a-d3d7-4267-b1d4-7acd55ae30f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "218a1321-1bbe-4cf0-bc95-9d9dd3244c20", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501033564900, "endTime": 323501173782900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "ded19798-b988-4eaa-bb8e-cf3c05e51a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87fcfff3-d799-4f66-a503-9fcc40944b18", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501173858000, "endTime": 323501294147500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "b6daf21c-b317-4847-ac2e-b3b245c0cc40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d335b667-0299-4b18-9ce8-bce643bccb47", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501294280700, "endTime": 323501294851500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "551d02a4-1bf7-4b79-b7b5-7e7c1a37c8fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b50f2c11-a922-4a12-a4b8-66f180ebad5e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499368029300, "endTime": 323499368046800}, "additional": {"logType": "info", "children": [], "durationId": "62f5a055-9376-4540-bd72-fc85d9f3b3aa", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "47efb816-0d1b-48a8-838e-f6695eca33f2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499368085200, "endTime": 323499379876600}, "additional": {"logType": "info", "children": [], "durationId": "b4411a29-7758-49e2-8aea-100994d257e8", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "38184a85-7894-4f57-a3a2-336df1996c59", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499381268300, "endTime": 323499381293900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f3ea434-e991-4498-8503-64bfe60e7993", "logId": "9862e0c7-c402-4d98-98ef-394cab75087a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9862e0c7-c402-4d98-98ef-394cab75087a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499381268300, "endTime": 323499381293900}, "additional": {"logType": "info", "children": [], "durationId": "38184a85-7894-4f57-a3a2-336df1996c59", "parent": "f5600e2f-a957-426c-bcce-059868bb5f1a"}}, {"head": {"id": "e6074dfd-c3db-49ff-b5e4-fc97655fd665", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499386814600, "endTime": 323500756401100}, "additional": {"children": ["6bf24d36-5ee1-43c2-bd5e-5dc3762abf8b", "6e6e1c21-deff-4176-9bc3-31c0e6829463"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f3ea434-e991-4498-8503-64bfe60e7993", "logId": "86e1dca0-1462-433f-a372-c22a2680aa77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bf24d36-5ee1-43c2-bd5e-5dc3762abf8b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499386816800, "endTime": 323500065765800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6074dfd-c3db-49ff-b5e4-fc97655fd665", "logId": "70be8409-b21a-4fe3-86f0-8170be872e91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e6e1c21-deff-4176-9bc3-31c0e6829463", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500065828800, "endTime": 323500756361300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6074dfd-c3db-49ff-b5e4-fc97655fd665", "logId": "0f0a1428-f2fc-444b-95ee-0b2422fc6979"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b30f7d44-f680-44c6-9a83-524e8b7e452b", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499386827000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed823b7-a65c-4fc8-8e6d-a92fb31865cb", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500065052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70be8409-b21a-4fe3-86f0-8170be872e91", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499386816800, "endTime": 323500065765800}, "additional": {"logType": "info", "children": [], "durationId": "6bf24d36-5ee1-43c2-bd5e-5dc3762abf8b", "parent": "86e1dca0-1462-433f-a372-c22a2680aa77"}}, {"head": {"id": "103af2cd-9e5e-4ea5-b75c-fad3c9332a00", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500065917600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db58d3f-cdef-4b6e-a32e-a3fe60bf91af", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500294013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6883cf09-69bf-45c3-8535-897797bb70b7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500294557700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe6ea67-8f10-495a-9ee2-1352cfbf2949", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500295286100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2defdfd-88e0-495e-beae-41b0bc85f638", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500295872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6598f8f5-7401-437e-b2f3-5e3ccc335487", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500304850900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b042dd-a105-43c8-87a8-ecdff2299151", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500360458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "358b7f6d-3c3c-4b0b-b095-fefc2d731d0b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500430429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44db8b26-fc7a-4510-a4a6-6f31299f4e5a", "name": "Sdk init in 293 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500659867900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751af9c9-3deb-4322-a316-aa9d741b4c52", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500660001300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 38}, "markType": "other"}}, {"head": {"id": "b2a6656d-098d-412a-bcf3-72ce8385bf81", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500660052500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 38}, "markType": "other"}}, {"head": {"id": "c3729032-4b9c-405f-b9af-39479eb51d6b", "name": "Project task initialization takes 94 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500754878100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e041375-0975-4265-8f4a-1ee8fde4e3d8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500755519600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbf5c675-efbc-40de-904e-16d37bdd3e28", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500755841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f703e6-e4ec-49ee-ac1e-984150793e56", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500756125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0a1428-f2fc-444b-95ee-0b2422fc6979", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500065828800, "endTime": 323500756361300}, "additional": {"logType": "info", "children": [], "durationId": "6e6e1c21-deff-4176-9bc3-31c0e6829463", "parent": "86e1dca0-1462-433f-a372-c22a2680aa77"}}, {"head": {"id": "86e1dca0-1462-433f-a372-c22a2680aa77", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499386814600, "endTime": 323500756401100}, "additional": {"logType": "info", "children": ["70be8409-b21a-4fe3-86f0-8170be872e91", "0f0a1428-f2fc-444b-95ee-0b2422fc6979"], "durationId": "e6074dfd-c3db-49ff-b5e4-fc97655fd665", "parent": "f5600e2f-a957-426c-bcce-059868bb5f1a"}}, {"head": {"id": "3c2b9822-8b88-4cf0-b699-8ee6e89dfd21", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500759687700, "endTime": 323500759722900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f3ea434-e991-4498-8503-64bfe60e7993", "logId": "ae247f2a-5307-43a7-aad4-268e65040fab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae247f2a-5307-43a7-aad4-268e65040fab", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500759687700, "endTime": 323500759722900}, "additional": {"logType": "info", "children": [], "durationId": "3c2b9822-8b88-4cf0-b699-8ee6e89dfd21", "parent": "f5600e2f-a957-426c-bcce-059868bb5f1a"}}, {"head": {"id": "f5600e2f-a957-426c-bcce-059868bb5f1a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499379895600, "endTime": 323500759754900}, "additional": {"logType": "info", "children": ["9862e0c7-c402-4d98-98ef-394cab75087a", "86e1dca0-1462-433f-a372-c22a2680aa77", "ae247f2a-5307-43a7-aad4-268e65040fab"], "durationId": "9f3ea434-e991-4498-8503-64bfe60e7993", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500763154100, "endTime": 323500989591600}, "additional": {"children": ["dcf9ceaa-8c78-4036-bf66-7d386dcfcd1c", "6273c8d5-36b8-4619-8edc-7dc0e9c9906f", "9e9a998a-3b97-40c3-8a25-1b804566b984"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b4c4faf-f29e-45f6-8a65-6e63b04f15a6", "logId": "890298f4-7324-4a67-b80d-976c9091a9d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcf9ceaa-8c78-4036-bf66-7d386dcfcd1c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500774382000, "endTime": 323500774417600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1", "logId": "20446359-47f4-48d9-bb55-27fce6d85526"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20446359-47f4-48d9-bb55-27fce6d85526", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500774382000, "endTime": 323500774417600}, "additional": {"logType": "info", "children": [], "durationId": "dcf9ceaa-8c78-4036-bf66-7d386dcfcd1c", "parent": "890298f4-7324-4a67-b80d-976c9091a9d3"}}, {"head": {"id": "6273c8d5-36b8-4619-8edc-7dc0e9c9906f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500781582900, "endTime": 323500987954600}, "additional": {"children": ["caeec796-9d8e-4f36-b050-1d02038b4193", "66b36011-640f-41d9-8d9d-30d3d371e5ca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1", "logId": "897b16d8-0ee9-46b9-be4f-4572d0a02e32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caeec796-9d8e-4f36-b050-1d02038b4193", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500781588300, "endTime": 323500790544800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6273c8d5-36b8-4619-8edc-7dc0e9c9906f", "logId": "bd2fbca1-9a23-440e-a86b-370727b27465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66b36011-640f-41d9-8d9d-30d3d371e5ca", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500790578000, "endTime": 323500987939800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6273c8d5-36b8-4619-8edc-7dc0e9c9906f", "logId": "0096f66a-2174-404e-962b-52c0aabd9ba5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc152d32-8bc0-49b1-a5a1-03f4160ba31e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500781602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00032e5-2f0c-42f6-bf8c-e7a4a078ba75", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500790208400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2fbca1-9a23-440e-a86b-370727b27465", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500781588300, "endTime": 323500790544800}, "additional": {"logType": "info", "children": [], "durationId": "caeec796-9d8e-4f36-b050-1d02038b4193", "parent": "897b16d8-0ee9-46b9-be4f-4572d0a02e32"}}, {"head": {"id": "81476f3d-11fd-4d5d-8615-b024ab519322", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500790611000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97f593e-03c3-4ad4-9272-5d26c66350bd", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500912790100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ceb871f-1fee-4c0b-8710-add2ca77061a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500912935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b3b0aa-6c32-4e37-aef4-c741e94636ee", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500913126500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a7bffb-2006-4bda-bace-1b5fb910f642", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500913255500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8e11ee-6787-4a7a-95dd-9e78ed0f7a4d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500913323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b75711-9d3c-4105-a3f0-407c085658e4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500913376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68d3880b-d57c-47bd-b967-a79c91436e38", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500913431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55767847-8e1b-497b-89e7-cb7acbbe777e", "name": "Module entry task initialization takes 26 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500987555300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80fbe5c-3771-4445-ad88-2a0c3b7a29aa", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500987745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc600da-5c09-4920-9f63-89f8461fe7cd", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500987829700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a86a9292-e78a-4f66-ae84-07990d6b1868", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500987889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0096f66a-2174-404e-962b-52c0aabd9ba5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500790578000, "endTime": 323500987939800}, "additional": {"logType": "info", "children": [], "durationId": "66b36011-640f-41d9-8d9d-30d3d371e5ca", "parent": "897b16d8-0ee9-46b9-be4f-4572d0a02e32"}}, {"head": {"id": "897b16d8-0ee9-46b9-be4f-4572d0a02e32", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500781582900, "endTime": 323500987954600}, "additional": {"logType": "info", "children": ["bd2fbca1-9a23-440e-a86b-370727b27465", "0096f66a-2174-404e-962b-52c0aabd9ba5"], "durationId": "6273c8d5-36b8-4619-8edc-7dc0e9c9906f", "parent": "890298f4-7324-4a67-b80d-976c9091a9d3"}}, {"head": {"id": "9e9a998a-3b97-40c3-8a25-1b804566b984", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500989567700, "endTime": 323500989578600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1", "logId": "f702d7c3-aaa2-411a-88d4-a344b9697ae5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f702d7c3-aaa2-411a-88d4-a344b9697ae5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500989567700, "endTime": 323500989578600}, "additional": {"logType": "info", "children": [], "durationId": "9e9a998a-3b97-40c3-8a25-1b804566b984", "parent": "890298f4-7324-4a67-b80d-976c9091a9d3"}}, {"head": {"id": "890298f4-7324-4a67-b80d-976c9091a9d3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500763154100, "endTime": 323500989591600}, "additional": {"logType": "info", "children": ["20446359-47f4-48d9-bb55-27fce6d85526", "897b16d8-0ee9-46b9-be4f-4572d0a02e32", "f702d7c3-aaa2-411a-88d4-a344b9697ae5"], "durationId": "e29b0c79-9e5b-42c7-9a7f-b5cec09b4bf1", "parent": "92461824-ad7d-4ee9-86c4-9919df7e20d7"}}, {"head": {"id": "92461824-ad7d-4ee9-86c4-9919df7e20d7", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500759788400, "endTime": 323500989600200}, "additional": {"logType": "info", "children": ["890298f4-7324-4a67-b80d-976c9091a9d3"], "durationId": "9b4c4faf-f29e-45f6-8a65-6e63b04f15a6", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "c489605c-f104-4c4d-ac50-0c0174b8c41e", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501012581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71445876-bad9-43c0-9869-89475fa66178", "name": "hvigorfile, resolve hvigorfile dependencies in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501018794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce5c26a-d3d7-4267-b1d4-7acd55ae30f2", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323500989607900, "endTime": 323501018933400}, "additional": {"logType": "info", "children": [], "durationId": "d6dcc62a-9fbc-4523-badd-f77ebbe542da", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "1228bdb0-b610-49a5-b0bb-5fdcf72c2757", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501019752000, "endTime": 323501033532800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c312964a-c53a-4846-aaf9-2b57f993818a", "logId": "622342f4-e3f8-4571-a4ad-17ce2327bb07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "966a865e-7109-4552-8888-f3525ba785a6", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501019770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "622342f4-e3f8-4571-a4ad-17ce2327bb07", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501019752000, "endTime": 323501033532800}, "additional": {"logType": "info", "children": [], "durationId": "1228bdb0-b610-49a5-b0bb-5fdcf72c2757", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "9e93401a-c6ca-479c-8d07-e242c1fd9c61", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501035776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdb3677-885a-473a-9e59-cb6d315ddbd1", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501167307600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded19798-b988-4eaa-bb8e-cf3c05e51a24", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501033564900, "endTime": 323501173782900}, "additional": {"logType": "info", "children": [], "durationId": "218a1321-1bbe-4cf0-bc95-9d9dd3244c20", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "1628e1ba-31af-49a3-92b6-4915bc5402c1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501173902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b06789cc-8ce2-407b-a483-58f8e027c881", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501212442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f24b7cc8-7ad6-4485-a09a-a0b5575be9e4", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501212690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31117e16-64c2-4515-9ca2-a28e4efa5b56", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501213934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afb061f-b480-47b2-a05a-71d1926263c7", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501219912400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aea54ec-45ad-4425-8af2-fa11be820496", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501220130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6daf21c-b317-4847-ac2e-b3b245c0cc40", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501173858000, "endTime": 323501294147500}, "additional": {"logType": "info", "children": [], "durationId": "87fcfff3-d799-4f66-a503-9fcc40944b18", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "e21b6015-7068-4aef-b429-1cafb6d81950", "name": "Configuration phase cost:1 s 927 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501294381900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "551d02a4-1bf7-4b79-b7b5-7e7c1a37c8fe", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501294280700, "endTime": 323501294851500}, "additional": {"logType": "info", "children": [], "durationId": "d335b667-0299-4b18-9ce8-bce643bccb47", "parent": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0"}}, {"head": {"id": "3a5be4b6-3e33-42c3-add9-69e391f2b2f0", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499349910300, "endTime": 323501294896100}, "additional": {"logType": "info", "children": ["b50f2c11-a922-4a12-a4b8-66f180ebad5e", "47efb816-0d1b-48a8-838e-f6695eca33f2", "f5600e2f-a957-426c-bcce-059868bb5f1a", "92461824-ad7d-4ee9-86c4-9919df7e20d7", "cce5c26a-d3d7-4267-b1d4-7acd55ae30f2", "ded19798-b988-4eaa-bb8e-cf3c05e51a24", "b6daf21c-b317-4847-ac2e-b3b245c0cc40", "551d02a4-1bf7-4b79-b7b5-7e7c1a37c8fe", "622342f4-e3f8-4571-a4ad-17ce2327bb07"], "durationId": "c312964a-c53a-4846-aaf9-2b57f993818a", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "9aff1768-13bf-473a-aae6-d41419957505", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501300963300, "endTime": 323501301030000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81d0a0e9-08ec-4ed4-b688-3715d998b663", "logId": "e282eec8-9804-48b6-9777-a780c982c6c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e282eec8-9804-48b6-9777-a780c982c6c8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501300963300, "endTime": 323501301030000}, "additional": {"logType": "info", "children": [], "durationId": "9aff1768-13bf-473a-aae6-d41419957505", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "1c93ce01-eedc-4b1a-9b2d-ad361a23a40c", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501294987500, "endTime": 323501301098900}, "additional": {"logType": "info", "children": [], "durationId": "6246bb3c-43a6-4b6e-a6ca-c32171625178", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "6b0ea6d8-3095-4f2e-af4e-e1e29a31c078", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501301132700, "endTime": 323501301139100}, "additional": {"logType": "info", "children": [], "durationId": "29b45cde-22e3-4684-a4a1-adb91cc54330", "parent": "597069e5-7669-4f9a-b077-8ec3c687d21f"}}, {"head": {"id": "597069e5-7669-4f9a-b077-8ec3c687d21f", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499165984500, "endTime": 323501301159900}, "additional": {"logType": "info", "children": ["d884c291-8549-4aac-9974-323de6c1f8e4", "3a5be4b6-3e33-42c3-add9-69e391f2b2f0", "1c93ce01-eedc-4b1a-9b2d-ad361a23a40c", "6b0ea6d8-3095-4f2e-af4e-e1e29a31c078", "c9d8c73b-90be-448c-885d-8d3dd5e73baa", "b4fe600a-4172-437f-bb20-7efcfb8a4cb7", "e282eec8-9804-48b6-9777-a780c982c6c8"], "durationId": "81d0a0e9-08ec-4ed4-b688-3715d998b663"}}, {"head": {"id": "2e262166-b92f-4f2f-8ea3-edccf79fe794", "name": "Configuration task cost before running: 2 s 145 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501302436300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44324748-0e6e-476d-9821-d8ead32b6791", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501328097300, "endTime": 323501355593400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "92b18ea6-2e86-4eea-b5ba-18dfa2d352b0", "logId": "60bc39b5-3487-40f4-b9e0-768c33c2316e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92b18ea6-2e86-4eea-b5ba-18dfa2d352b0", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501309824800}, "additional": {"logType": "detail", "children": [], "durationId": "44324748-0e6e-476d-9821-d8ead32b6791"}}, {"head": {"id": "c5c9bf51-93e8-43cc-b3cc-c83814df21e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501312405900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa9b1923-4eed-4942-bb41-012861cce9e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501312822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4ef621-6379-4fe7-af44-c67fb5b8ac8e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501328149800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b01f92-ab83-4b42-aa03-cfd4604da447", "name": "Incremental task entry:default@PreBuild pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501355070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c29ea2-793b-451c-9ae6-38ef6eddd2b9", "name": "entry : default@PreBuild cost memory -1.5200119018554688", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501355389400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60bc39b5-3487-40f4-b9e0-768c33c2316e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501328097300, "endTime": 323501355593400}, "additional": {"logType": "info", "children": [], "durationId": "44324748-0e6e-476d-9821-d8ead32b6791"}}, {"head": {"id": "f11d9eb3-a45e-4941-8b7d-79403abce808", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501363839100, "endTime": 323501366113400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "61e616bb-d2f4-48a8-a7e6-9802ce1b6ad4", "logId": "a6a03cf5-a4aa-4a71-853a-2f96b81787ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61e616bb-d2f4-48a8-a7e6-9802ce1b6ad4", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501362531000}, "additional": {"logType": "detail", "children": [], "durationId": "f11d9eb3-a45e-4941-8b7d-79403abce808"}}, {"head": {"id": "17dbe568-fee8-44c8-8467-41b52af81e9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501363053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe0f4c87-a0b3-49a2-af19-fe71fa7afc50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501363148400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96fb3b19-23f8-431c-9bc7-b9e4270213c1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501363851600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded41751-3952-4611-9dad-c31850cff27d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501365936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "095999b4-8086-4085-8380-684f13c5ffae", "name": "entry : default@MergeProfile cost memory 0.1104278564453125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501366041200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a03cf5-a4aa-4a71-853a-2f96b81787ab", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501363839100, "endTime": 323501366113400}, "additional": {"logType": "info", "children": [], "durationId": "f11d9eb3-a45e-4941-8b7d-79403abce808"}}, {"head": {"id": "10f0f994-e6d2-4ff8-af9e-179053849950", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501369127000, "endTime": 323501546180600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "baa0a66d-a91f-469f-a8b2-d82dd0bfec80", "logId": "b90dfdcf-45a8-48a8-9bcc-78e94efa58c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa0a66d-a91f-469f-a8b2-d82dd0bfec80", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501367787900}, "additional": {"logType": "detail", "children": [], "durationId": "10f0f994-e6d2-4ff8-af9e-179053849950"}}, {"head": {"id": "f7dc60ce-1bc9-40a1-985a-3d4588d1274b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501368303300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a92562-d6a0-42f1-b788-cfa34cb94bbe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501368386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c249bc-b340-4792-a843-315c6935824e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501369135200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d17c2e-ea75-480b-bbbb-b7ec7e17407b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 173 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501541757600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70bf9b4-a912-4b37-b719-fb4f4a07c1dd", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501545691300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41533b2-686d-4505-9dbe-adaa1eb81bf2", "name": "entry : default@CreateBuildProfile cost memory 0.09661865234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501545960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90dfdcf-45a8-48a8-9bcc-78e94efa58c6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501369127000, "endTime": 323501546180600}, "additional": {"logType": "info", "children": [], "durationId": "10f0f994-e6d2-4ff8-af9e-179053849950"}}, {"head": {"id": "d95a0848-1eaa-4fd5-b9da-be72c85b9ebf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554048000, "endTime": 323501555129300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6328e0ba-7843-467d-8f68-c97be486bd3c", "logId": "5953e5cb-9c52-487a-859f-dad3e468c35e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6328e0ba-7843-467d-8f68-c97be486bd3c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501550361600}, "additional": {"logType": "detail", "children": [], "durationId": "d95a0848-1eaa-4fd5-b9da-be72c85b9ebf"}}, {"head": {"id": "dc5cbcd4-f197-4949-b105-9349e9152c3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501551772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5019f2c7-bdbe-4ec5-825e-f8667c869dc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501552005100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf0a7a8-73de-4eda-9f14-dd746290b0d5", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554068400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb8e65e-7d6f-45ad-a10c-069f196a94c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554366800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89fb2845-d8b3-4edf-8e92-7dca75eef32d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554527000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a68a9a5-271d-4151-9519-f298a33e56f0", "name": "entry : default@PreCheckSyscap cost memory 0.03704833984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc62e1f-d578-4399-95ff-d7bfa216c994", "name": "runTaskFromQueue task cost before running: 2 s 397 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554979800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5953e5cb-9c52-487a-859f-dad3e468c35e", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501554048000, "endTime": 323501555129300, "totalTime": 887800}, "additional": {"logType": "info", "children": [], "durationId": "d95a0848-1eaa-4fd5-b9da-be72c85b9ebf"}}, {"head": {"id": "bad66014-e648-48e1-8acf-f6ff53b7b7ba", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501577037900, "endTime": 323501578815100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "476a29da-88a0-43e5-a93c-f4f7222e6cf2", "logId": "2805735f-862d-41b9-b614-1a712bbe36c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "476a29da-88a0-43e5-a93c-f4f7222e6cf2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501559419700}, "additional": {"logType": "detail", "children": [], "durationId": "bad66014-e648-48e1-8acf-f6ff53b7b7ba"}}, {"head": {"id": "56ccb03d-31d4-42bd-a1c5-3346cc2af89c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501560777500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d7edd9-1165-4f43-a472-b854119ce2db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501560998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce45e51-4a61-4785-a0ce-a7d008eed430", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501577059700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17366ab-b463-4878-a866-428afe46d5ff", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501577395500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77083641-5579-49ba-9219-8253e15f041d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501578532500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cf0e63-2c27-4686-a2bf-878f79b89cc7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06540679931640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501578693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2805735f-862d-41b9-b614-1a712bbe36c5", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501577037900, "endTime": 323501578815100}, "additional": {"logType": "info", "children": [], "durationId": "bad66014-e648-48e1-8acf-f6ff53b7b7ba"}}, {"head": {"id": "34ad6371-7cd9-4a4b-bb74-5d8ae39c0ce3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501585586600, "endTime": 323501587909000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5773365e-9856-4ae9-994e-7be249dac56f", "logId": "ab5b2c02-490e-47f3-b1b0-660cf067b072"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5773365e-9856-4ae9-994e-7be249dac56f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501582127300}, "additional": {"logType": "detail", "children": [], "durationId": "34ad6371-7cd9-4a4b-bb74-5d8ae39c0ce3"}}, {"head": {"id": "8da6ada1-8e77-458e-9d92-0594bb0fd07a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501582933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89849e9a-c02a-473a-a57b-612ac084d601", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501583127900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "668ece1b-2df1-43d3-8715-399bc9c0216e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501585609100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3286f420-5a58-4beb-903f-c6784d5c2232", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501587604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ce7589-2c22-4724-bf9d-351fdffffee2", "name": "entry : default@ProcessProfile cost memory 0.0563812255859375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501587780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5b2c02-490e-47f3-b1b0-660cf067b072", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501585586600, "endTime": 323501587909000}, "additional": {"logType": "info", "children": [], "durationId": "34ad6371-7cd9-4a4b-bb74-5d8ae39c0ce3"}}, {"head": {"id": "fd58626e-27fa-4f33-8e28-f8db36d45373", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501594621600, "endTime": 323501602048300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ac35212d-fa83-4c18-8d30-d1144229fee7", "logId": "efe1c489-e99d-4492-bee1-937777eabca3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac35212d-fa83-4c18-8d30-d1144229fee7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501590703100}, "additional": {"logType": "detail", "children": [], "durationId": "fd58626e-27fa-4f33-8e28-f8db36d45373"}}, {"head": {"id": "368a18df-d5b8-4a8f-a6f0-e7157d309ee1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501591509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411cc953-75e0-4ff9-982f-a2d788c9b09a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501591650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835c9fe5-5867-4603-a8ea-7d8f345e0396", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501594659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2c1398-38e8-4034-9f90-b1ba895ee26d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501601835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106ba10c-e6a7-43b2-9a5d-ece6e88c937e", "name": "entry : default@ProcessRouterMap cost memory 0.18688201904296875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501601969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efe1c489-e99d-4492-bee1-937777eabca3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501594621600, "endTime": 323501602048300}, "additional": {"logType": "info", "children": [], "durationId": "fd58626e-27fa-4f33-8e28-f8db36d45373"}}, {"head": {"id": "f1348e90-4d08-4ad2-aa83-90100faa5bb5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501608667500, "endTime": 323501611750400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2772f858-4428-4f21-8c32-dc87fe6da450", "logId": "6ceab820-73b3-4610-8447-f0e36fa18854"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2772f858-4428-4f21-8c32-dc87fe6da450", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501604916200}, "additional": {"logType": "detail", "children": [], "durationId": "f1348e90-4d08-4ad2-aa83-90100faa5bb5"}}, {"head": {"id": "805a9806-4790-4afc-b399-45d23c1ea442", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501605433700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd264a49-dab3-445d-b338-192d6e66d402", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501605512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f734a80-3b34-46ab-b203-8a87020d7987", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501606572500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ebc15e9-7e70-4461-abb5-e5183553b814", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501609839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1dba91a-1650-4a63-b8dd-3e50be06dc8d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501610005100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d551ab93-2dcc-47d8-9335-1bae1da2f7e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501610067600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5b3b4ba-1a25-4b8b-8b39-065f0a468653", "name": "entry : default@PreviewProcessResource cost memory 0.06798553466796875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501610219900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d2d80f-f7b6-48f8-af5f-822b499f0958", "name": "runTaskFromQueue task cost before running: 2 s 454 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501611644200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ceab820-73b3-4610-8447-f0e36fa18854", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501608667500, "endTime": 323501611750400, "totalTime": 1647500}, "additional": {"logType": "info", "children": [], "durationId": "f1348e90-4d08-4ad2-aa83-90100faa5bb5"}}, {"head": {"id": "47e81506-b504-4483-b393-458b2fa47a53", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501619098300, "endTime": 323501644578000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a59b4406-1bf6-4be2-84dd-e98869ccc66e", "logId": "45317527-f38d-49d6-933b-08e796cb87b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a59b4406-1bf6-4be2-84dd-e98869ccc66e", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501614822000}, "additional": {"logType": "detail", "children": [], "durationId": "47e81506-b504-4483-b393-458b2fa47a53"}}, {"head": {"id": "7e129b64-1353-44a3-a9e9-765d83c1ce37", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501615371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea852c31-1cc2-484f-bd16-206cae6d7583", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501615488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f72d2da3-9506-4837-8ee1-cca365e20643", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501619114700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "613e75c9-41d5-4b44-a7a8-2d47b9629c75", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501644345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d7a212-691c-45ae-ab74-b8f87ba95407", "name": "entry : default@GenerateLoaderJson cost memory -0.9691390991210938", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501644496100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45317527-f38d-49d6-933b-08e796cb87b1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501619098300, "endTime": 323501644578000}, "additional": {"logType": "info", "children": [], "durationId": "47e81506-b504-4483-b393-458b2fa47a53"}}, {"head": {"id": "4c301868-7989-486b-8745-a91735352c91", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501657585300, "endTime": 323501682163900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "83f55615-7b27-4d5c-8e74-d0ec43f97f23", "logId": "9cc74ab0-bf2b-4953-850d-491f023a5020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83f55615-7b27-4d5c-8e74-d0ec43f97f23", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501652630300}, "additional": {"logType": "detail", "children": [], "durationId": "4c301868-7989-486b-8745-a91735352c91"}}, {"head": {"id": "9760fc1f-c51e-4b50-8e2e-0153035d23cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501653205600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9667b0a5-264e-4767-846b-9bf5b8506ca0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501653292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c9c81ad-db35-4563-96e5-10463fce94db", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501654377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec2e374-a1b9-4b75-a4ce-4a260a62a4e5", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501657633400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "334ada42-61c2-4a71-8edd-fe0819e24030", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501681876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95616cdd-d150-47a1-9f8a-761c73035d5c", "name": "entry : default@PreviewCompileResource cost memory 0.7067794799804688", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501682051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc74ab0-bf2b-4953-850d-491f023a5020", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501657585300, "endTime": 323501682163900}, "additional": {"logType": "info", "children": [], "durationId": "4c301868-7989-486b-8745-a91735352c91"}}, {"head": {"id": "32e4149d-a4b8-4849-ac4a-3a6b2d00db92", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685710300, "endTime": 323501686538700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1279d7fd-56a5-4054-b7ab-500c8376e5fd", "logId": "0b27c8f9-151b-42aa-9ff8-2d796c2e8c62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1279d7fd-56a5-4054-b7ab-500c8376e5fd", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501684977600}, "additional": {"logType": "detail", "children": [], "durationId": "32e4149d-a4b8-4849-ac4a-3a6b2d00db92"}}, {"head": {"id": "90a08d07-ef0b-42f7-99eb-db76453a56e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685508100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6c154d-fa36-4bab-8de8-49de8134e010", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685614600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c7dffbf-ea28-477b-b78e-5e85a6ba4bcc", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a5dc3fb-eea7-493f-9b29-e4e9cdbaae39", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685811300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b416007f-c748-48a6-9d0a-e4a5f7600737", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685860200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91882f2f-4338-4577-a082-29c3d6afe92b", "name": "entry : default@PreviewHookCompileResource cost memory 0.0380706787109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685929000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68043c65-8302-49d8-b7e2-f429a02f8b94", "name": "runTaskFromQueue task cost before running: 2 s 529 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501686014000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b27c8f9-151b-42aa-9ff8-2d796c2e8c62", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501685710300, "endTime": 323501686538700, "totalTime": 283200}, "additional": {"logType": "info", "children": [], "durationId": "32e4149d-a4b8-4849-ac4a-3a6b2d00db92"}}, {"head": {"id": "6d0e09d4-c307-49e8-ae87-ffc3222d3d7b", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501690424500, "endTime": 323501693804500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "4d1e8e5c-819f-43cd-81c3-3a988437f726", "logId": "522d9210-9ea5-4f01-b408-3c9f780ce4b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d1e8e5c-819f-43cd-81c3-3a988437f726", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501688772500}, "additional": {"logType": "detail", "children": [], "durationId": "6d0e09d4-c307-49e8-ae87-ffc3222d3d7b"}}, {"head": {"id": "ef8a10e6-d32c-4c26-af41-cb4bd9355cd0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501689440700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3a4d39-c944-4bee-9fd9-7ec28dc0fb6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501689556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300f9317-a94b-4402-ae52-9abd5f6c5975", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501690436300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b1421cd-bf4a-44d5-8375-ba52a7a17b1c", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501693561100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af5ca71-3fc1-4f20-871d-fe3d177e17bd", "name": "entry : default@CopyPreviewProfile cost memory 0.10291290283203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501693704100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "522d9210-9ea5-4f01-b408-3c9f780ce4b0", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501690424500, "endTime": 323501693804500}, "additional": {"logType": "info", "children": [], "durationId": "6d0e09d4-c307-49e8-ae87-ffc3222d3d7b"}}, {"head": {"id": "b17e626c-7326-4f35-bcc1-1416495a121d", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697366600, "endTime": 323501697804300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "2e2ee8a2-4a7b-4b18-bd84-99883f9e388f", "logId": "f0adeef9-bfdf-4d36-9bf5-e0437cdedd44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e2ee8a2-4a7b-4b18-bd84-99883f9e388f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501695914100}, "additional": {"logType": "detail", "children": [], "durationId": "b17e626c-7326-4f35-bcc1-1416495a121d"}}, {"head": {"id": "78e98a05-e998-41ca-92bc-7f8a6c1d8164", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501696459300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879d2833-a509-4a68-bbd4-19b8c202afc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501696563900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedeb8d8-d30c-4200-a3b9-882074ebc895", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697376000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdbdc694-635b-4adb-9f9d-bb4e7a908447", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd2453cf-ee60-41eb-ad5b-9a4c9e62c0c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697544900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad08a55c-7b23-49a9-9843-6b71adbcd699", "name": "entry : default@ReplacePreviewerPage cost memory 0.03801727294921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697635300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c4ebe62-217a-4605-8819-15308fc844bd", "name": "runTaskFromQueue task cost before running: 2 s 540 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697727300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0adeef9-bfdf-4d36-9bf5-e0437cdedd44", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501697366600, "endTime": 323501697804300, "totalTime": 335600}, "additional": {"logType": "info", "children": [], "durationId": "b17e626c-7326-4f35-bcc1-1416495a121d"}}, {"head": {"id": "4ff9b17e-cf67-4208-90d7-2e31aad90632", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699502100, "endTime": 323501699820200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "932db410-d883-456f-803f-60dcdef55ee1", "logId": "7c75b48c-3d16-4950-9e0b-7de41a0386c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932db410-d883-456f-803f-60dcdef55ee1", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699442600}, "additional": {"logType": "detail", "children": [], "durationId": "4ff9b17e-cf67-4208-90d7-2e31aad90632"}}, {"head": {"id": "82f889c3-289f-4f86-8661-78c03051bcfc", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699509600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47535db5-649b-4ce3-8c15-7860155b0ce6", "name": "entry : buildPreviewerResource cost memory 0.01169586181640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cd12937-a5b2-4e7b-82e4-d5fc22b073f0", "name": "runTaskFromQueue task cost before running: 2 s 542 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699760200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c75b48c-3d16-4950-9e0b-7de41a0386c5", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501699502100, "endTime": 323501699820200, "totalTime": 239000}, "additional": {"logType": "info", "children": [], "durationId": "4ff9b17e-cf67-4208-90d7-2e31aad90632"}}, {"head": {"id": "664b7ce0-a5cd-4156-bc67-3378c231a66c", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501703215900, "endTime": 323501706111500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "db59c103-68a5-4d66-af86-a06d0d3ae3c0", "logId": "dbe61f7a-a117-4236-b1fc-75f082a89374"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db59c103-68a5-4d66-af86-a06d0d3ae3c0", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501701765500}, "additional": {"logType": "detail", "children": [], "durationId": "664b7ce0-a5cd-4156-bc67-3378c231a66c"}}, {"head": {"id": "1f8279ba-ce5a-448e-a723-794a18da69fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501702324400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72fcf8d6-b763-4e76-84c9-52f19c81fde5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501702413200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83abe5d7-e7ea-4487-9d41-c8e517746a23", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501703226000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c31ce9-c033-4378-8667-7eb432e40458", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501705759400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c94a81-0a04-46d6-9f12-e23bf546b66e", "name": "entry : default@PreviewUpdateAssets cost memory 0.10564422607421875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501705920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe61f7a-a117-4236-b1fc-75f082a89374", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501703215900, "endTime": 323501706111500}, "additional": {"logType": "info", "children": [], "durationId": "664b7ce0-a5cd-4156-bc67-3378c231a66c"}}, {"head": {"id": "2726466a-3fc3-479f-97e2-a4c7fff21805", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501719461200, "endTime": 323556654017000}, "additional": {"children": ["b26b0a66-b747-4367-8760-e9ba006a16f7"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "34743d24-1776-46cd-a129-8e4664a8ce00", "logId": "0d9a8a87-dd77-49ef-84cd-8f215d74233f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34743d24-1776-46cd-a129-8e4664a8ce00", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501709435800}, "additional": {"logType": "detail", "children": [], "durationId": "2726466a-3fc3-479f-97e2-a4c7fff21805"}}, {"head": {"id": "5e6fdd59-e46e-43d3-8fb4-61c3fcb5e170", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501710133400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d3b090-8c9a-495e-b90d-1cd2558aee07", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501710253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9401c393-6c13-438b-91c9-90f97a37d2d3", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501719478600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26b0a66-b747-4367-8760-e9ba006a16f7", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker13", "startTime": 323501825991600, "endTime": 323556653777700}, "additional": {"children": ["7bbbf2cb-4fc2-4da3-ade1-7af309e76bb3", "c0a877e9-4769-4e53-807f-0edf6e898960", "39e83015-78c4-45be-8967-5e55b619cb95", "353cea0d-030c-4b89-9fc7-2e9669458f32", "b07b4887-bcfd-48bc-9700-472399af372a", "c3b08ce6-a761-4c70-a7d8-a17d652cf3d0", "345ad518-04b9-482d-8634-246e62e2b8ac", "0373ea12-710d-41da-842e-c1743838d66a", "e08fcd40-80d4-410d-987b-2a7c7e3e4f2c", "f41e7e6a-45a6-4cc9-bf6c-2f8191b9f987", "f4761659-8bb0-412f-b044-124262ba6bbc", "77545087-2139-46e8-8b03-7247d3b828ef", "91524ad2-fe9c-4350-a40c-83a141e7e045", "2104c45e-c127-4f09-b5fb-5f06c85ee97c", "230bce31-1d46-4c55-9d92-5228bae61eeb", "268b36af-714a-4326-9ba4-fe1a396a07d5", "4af5f273-625f-4ed1-83bd-dce1adaf0391", "604c78db-6a5e-4387-86ed-0fb563fb6d48", "6bf5e80b-0a25-416a-9b70-a34edc2269e3", "22b08066-a18b-494d-9a51-40be57aadb60", "c5811b6c-b410-4be9-be4e-b04684e230e1", "f7350020-987b-4d64-80c5-3e72be36fccc", "a4713d24-7e8c-4c3a-8a2b-8b78b83ec1e5", "cda55434-cf1f-4b13-834f-21596ee818ec", "775cfa31-3074-48ff-b553-970aa79f6d5e"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2726466a-3fc3-479f-97e2-a4c7fff21805", "logId": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7edd793f-8d67-493a-83c5-7ad2c6b7e926", "name": "entry : default@PreviewArkTS cost memory -0.8032913208007812", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501922465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addefd7c-690a-4a2d-8f38-d3d861a348f9", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323537070614200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bbbf2cb-4fc2-4da3-ade1-7af309e76bb3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323537072620600, "endTime": 323537072647000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "f3259425-d079-498d-9e33-7edb13c13f1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3259425-d079-498d-9e33-7edb13c13f1d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323537072620600, "endTime": 323537072647000}, "additional": {"logType": "info", "children": [], "durationId": "7bbbf2cb-4fc2-4da3-ade1-7af309e76bb3", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "bd22f8b0-a5ca-441b-8f02-12c57c1c6f36", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550663239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a877e9-4769-4e53-807f-0edf6e898960", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550664376300, "endTime": 323550664401100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "1c106d1f-48cf-4af5-b20f-1ce06011de06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c106d1f-48cf-4af5-b20f-1ce06011de06", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550664376300, "endTime": 323550664401100}, "additional": {"logType": "info", "children": [], "durationId": "c0a877e9-4769-4e53-807f-0edf6e898960", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "3004c659-eda4-46e6-8ee0-c96ec4f182ca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550664517200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e83015-78c4-45be-8967-5e55b619cb95", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550665525600, "endTime": 323550665545300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "c6771f16-352d-4b7c-afb5-c783862f7b7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6771f16-352d-4b7c-afb5-c783862f7b7f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550665525600, "endTime": 323550665545300}, "additional": {"logType": "info", "children": [], "durationId": "39e83015-78c4-45be-8967-5e55b619cb95", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "72beb866-31dd-4246-abe5-61541c962bee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550665631200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353cea0d-030c-4b89-9fc7-2e9669458f32", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550666720900, "endTime": 323550666747200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "7ef2b4b0-c361-4a81-9b09-9f0c6180a171"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ef2b4b0-c361-4a81-9b09-9f0c6180a171", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550666720900, "endTime": 323550666747200}, "additional": {"logType": "info", "children": [], "durationId": "353cea0d-030c-4b89-9fc7-2e9669458f32", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "e1d94df0-0396-4fcf-acd7-6d179d7e85ef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550666931600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07b4887-bcfd-48bc-9700-472399af372a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550668357200, "endTime": 323550668379600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "69ea5149-42f6-4431-8008-d3dc4a3cbe41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69ea5149-42f6-4431-8008-d3dc4a3cbe41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550668357200, "endTime": 323550668379600}, "additional": {"logType": "info", "children": [], "durationId": "b07b4887-bcfd-48bc-9700-472399af372a", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "3d2e930f-53e4-4f29-87d7-b8a3319769c4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550668481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b08ce6-a761-4c70-a7d8-a17d652cf3d0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550669693000, "endTime": 323550669714400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "09fd6ceb-acec-420a-a9fa-f3c3b4c439ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09fd6ceb-acec-420a-a9fa-f3c3b4c439ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550669693000, "endTime": 323550669714400}, "additional": {"logType": "info", "children": [], "durationId": "c3b08ce6-a761-4c70-a7d8-a17d652cf3d0", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "5ffb3d9c-dc28-49c6-b629-a616fec8e7ec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550669822900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "345ad518-04b9-482d-8634-246e62e2b8ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550671057500, "endTime": 323550671076300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "d518a7c7-e200-44a7-aed0-22d54ca92388"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d518a7c7-e200-44a7-aed0-22d54ca92388", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550671057500, "endTime": 323550671076300}, "additional": {"logType": "info", "children": [], "durationId": "345ad518-04b9-482d-8634-246e62e2b8ac", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "29c1f8d1-41bb-4296-9821-14900372ab16", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550671175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0373ea12-710d-41da-842e-c1743838d66a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550672383000, "endTime": 323550672399800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "c693f27e-81c0-4d30-ad70-3e7a0bebf146"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c693f27e-81c0-4d30-ad70-3e7a0bebf146", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550672383000, "endTime": 323550672399800}, "additional": {"logType": "info", "children": [], "durationId": "0373ea12-710d-41da-842e-c1743838d66a", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "a200922a-2d08-4301-a8b8-0ddcae933f2d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550672488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08fcd40-80d4-410d-987b-2a7c7e3e4f2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550673501100, "endTime": 323550673515700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "880397a1-4d69-47d8-bcfc-d94218cdcd55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "880397a1-4d69-47d8-bcfc-d94218cdcd55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550673501100, "endTime": 323550673515700}, "additional": {"logType": "info", "children": [], "durationId": "e08fcd40-80d4-410d-987b-2a7c7e3e4f2c", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "5e113d2a-d4da-4737-8a5c-ccc042b1f382", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550673598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41e7e6a-45a6-4cc9-bf6c-2f8191b9f987", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550674434600, "endTime": 323550674450100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "656dfd48-a18b-425f-b72c-76fbad44cf11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "656dfd48-a18b-425f-b72c-76fbad44cf11", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550674434600, "endTime": 323550674450100}, "additional": {"logType": "info", "children": [], "durationId": "f41e7e6a-45a6-4cc9-bf6c-2f8191b9f987", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "e735e717-4769-44bb-ae2c-c902ba2b208f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550674525900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4761659-8bb0-412f-b044-124262ba6bbc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550675504800, "endTime": 323550675526200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "5ead4d2e-92d5-4e2c-ba1d-7b863c187cd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ead4d2e-92d5-4e2c-ba1d-7b863c187cd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550675504800, "endTime": 323550675526200}, "additional": {"logType": "info", "children": [], "durationId": "f4761659-8bb0-412f-b044-124262ba6bbc", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "2e7a9369-f7bb-4336-98f2-38cc63993007", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550675628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77545087-2139-46e8-8b03-7247d3b828ef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550676567600, "endTime": 323550676583600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "539f49d9-cc04-49e4-a61f-ec0b937ea9d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "539f49d9-cc04-49e4-a61f-ec0b937ea9d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550676567600, "endTime": 323550676583600}, "additional": {"logType": "info", "children": [], "durationId": "77545087-2139-46e8-8b03-7247d3b828ef", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "4c4360ef-4dd7-4099-bdb7-51f649a7976e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550676656600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91524ad2-fe9c-4350-a40c-83a141e7e045", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550678204800, "endTime": 323550678247500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "405dc2ec-572e-457f-81d2-8103d646e1db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "405dc2ec-572e-457f-81d2-8103d646e1db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550678204800, "endTime": 323550678247500}, "additional": {"logType": "info", "children": [], "durationId": "91524ad2-fe9c-4350-a40c-83a141e7e045", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "a4c0e379-87ea-4bca-9610-1d05144a638e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550678356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2104c45e-c127-4f09-b5fb-5f06c85ee97c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550679248300, "endTime": 323550679262300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "26f2f73c-c984-4614-b729-6dcfec2d32fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26f2f73c-c984-4614-b729-6dcfec2d32fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550679248300, "endTime": 323550679262300}, "additional": {"logType": "info", "children": [], "durationId": "2104c45e-c127-4f09-b5fb-5f06c85ee97c", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "1f0f9abc-8709-4419-a836-5b78cf3d0f41", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550679331800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "230bce31-1d46-4c55-9d92-5228bae61eeb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550680111100, "endTime": 323550680127800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "26156bb9-7361-4bd8-ac17-2c5a3660bab4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26156bb9-7361-4bd8-ac17-2c5a3660bab4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550680111100, "endTime": 323550680127800}, "additional": {"logType": "info", "children": [], "durationId": "230bce31-1d46-4c55-9d92-5228bae61eeb", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "8278b4d6-d88d-4c65-8d60-26be48f82589", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550680195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "268b36af-714a-4326-9ba4-fe1a396a07d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550680952600, "endTime": 323550680965500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "23d98616-14ad-48cf-ad82-c6ba5f37600e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23d98616-14ad-48cf-ad82-c6ba5f37600e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550680952600, "endTime": 323550680965500}, "additional": {"logType": "info", "children": [], "durationId": "268b36af-714a-4326-9ba4-fe1a396a07d5", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "6c2198f6-fab1-4c09-8232-658e61fd75a4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550681051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af5f273-625f-4ed1-83bd-dce1adaf0391", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550681859200, "endTime": 323550681871700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "92f2564e-ba1d-463b-a7b6-572aa229518a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92f2564e-ba1d-463b-a7b6-572aa229518a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550681859200, "endTime": 323550681871700}, "additional": {"logType": "info", "children": [], "durationId": "4af5f273-625f-4ed1-83bd-dce1adaf0391", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "7b9ca566-43eb-42b2-b73d-14f5b21d2d50", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550681939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604c78db-6a5e-4387-86ed-0fb563fb6d48", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550682722100, "endTime": 323550682735500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "3bf164fc-6cfc-4f5f-804e-5b84a7eb41fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bf164fc-6cfc-4f5f-804e-5b84a7eb41fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550682722100, "endTime": 323550682735500}, "additional": {"logType": "info", "children": [], "durationId": "604c78db-6a5e-4387-86ed-0fb563fb6d48", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "558a8f55-69d8-47ab-90bf-6b95fa37fea9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550682804800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf5e80b-0a25-416a-9b70-a34edc2269e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550683925100, "endTime": 323550683944700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "3d7cb33e-f374-40d8-b5dd-90d227d6cf3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d7cb33e-f374-40d8-b5dd-90d227d6cf3c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550683925100, "endTime": 323550683944700}, "additional": {"logType": "info", "children": [], "durationId": "6bf5e80b-0a25-416a-9b70-a34edc2269e3", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "23ff2b83-e480-4789-9016-9860580b1353", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550684060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22b08066-a18b-494d-9a51-40be57aadb60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550684938400, "endTime": 323550684952200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "8a0f1f6c-8060-4be5-9af5-e2dd3f4db278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a0f1f6c-8060-4be5-9af5-e2dd3f4db278", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550684938400, "endTime": 323550684952200}, "additional": {"logType": "info", "children": [], "durationId": "22b08066-a18b-494d-9a51-40be57aadb60", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "30ab4172-8cae-45ae-ba5b-92948b08f91e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550685023700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5811b6c-b410-4be9-be4e-b04684e230e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550685834900, "endTime": 323550685847600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "6303b6d0-6faf-4494-a537-cccd8e46e8ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6303b6d0-6faf-4494-a537-cccd8e46e8ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550685834900, "endTime": 323550685847600}, "additional": {"logType": "info", "children": [], "durationId": "c5811b6c-b410-4be9-be4e-b04684e230e1", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "fa3bd3e3-6e92-4fd7-b449-34386ca8a8e8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550685911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7350020-987b-4d64-80c5-3e72be36fccc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550687294600, "endTime": 323550687311500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "206e18f1-3902-40cd-8128-cccaf751ca53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "206e18f1-3902-40cd-8128-cccaf751ca53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550687294600, "endTime": 323550687311500}, "additional": {"logType": "info", "children": [], "durationId": "f7350020-987b-4d64-80c5-3e72be36fccc", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "896bae49-9505-454e-9a68-cf9c1f211884", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550687389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4713d24-7e8c-4c3a-8a2b-8b78b83ec1e5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550688203500, "endTime": 323550688218500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "3ca82aaa-0f41-43ec-83c3-b12f94c152e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ca82aaa-0f41-43ec-83c3-b12f94c152e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550688203500, "endTime": 323550688218500}, "additional": {"logType": "info", "children": [], "durationId": "a4713d24-7e8c-4c3a-8a2b-8b78b83ec1e5", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "be8701c1-b34a-47f4-8706-411d2ebb4573", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550688304800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cda55434-cf1f-4b13-834f-21596ee818ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550689173800, "endTime": 323550689189800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "fdc8978b-fb4f-47e6-900d-a5c6534c4239"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdc8978b-fb4f-47e6-900d-a5c6534c4239", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323550689173800, "endTime": 323550689189800}, "additional": {"logType": "info", "children": [], "durationId": "cda55434-cf1f-4b13-834f-21596ee818ec", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "5282fe81-e398-4c9d-b166-f939f7381bc4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556652458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775cfa31-3074-48ff-b553-970aa79f6d5e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556653671800, "endTime": 323556653692600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26b0a66-b747-4367-8760-e9ba006a16f7", "logId": "8fbfae01-f4e5-4e57-b782-a6c2bb793391"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fbfae01-f4e5-4e57-b782-a6c2bb793391", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556653671800, "endTime": 323556653692600}, "additional": {"logType": "info", "children": [], "durationId": "775cfa31-3074-48ff-b553-970aa79f6d5e", "parent": "8369f110-0a7f-4aec-8d61-6728e3e7a059"}}, {"head": {"id": "8369f110-0a7f-4aec-8d61-6728e3e7a059", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker13", "startTime": 323501825991600, "endTime": 323556653777700}, "additional": {"logType": "error", "children": ["f3259425-d079-498d-9e33-7edb13c13f1d", "1c106d1f-48cf-4af5-b20f-1ce06011de06", "c6771f16-352d-4b7c-afb5-c783862f7b7f", "7ef2b4b0-c361-4a81-9b09-9f0c6180a171", "69ea5149-42f6-4431-8008-d3dc4a3cbe41", "09fd6ceb-acec-420a-a9fa-f3c3b4c439ed", "d518a7c7-e200-44a7-aed0-22d54ca92388", "c693f27e-81c0-4d30-ad70-3e7a0bebf146", "880397a1-4d69-47d8-bcfc-d94218cdcd55", "656dfd48-a18b-425f-b72c-76fbad44cf11", "5ead4d2e-92d5-4e2c-ba1d-7b863c187cd1", "539f49d9-cc04-49e4-a61f-ec0b937ea9d6", "405dc2ec-572e-457f-81d2-8103d646e1db", "26f2f73c-c984-4614-b729-6dcfec2d32fc", "26156bb9-7361-4bd8-ac17-2c5a3660bab4", "23d98616-14ad-48cf-ad82-c6ba5f37600e", "92f2564e-ba1d-463b-a7b6-572aa229518a", "3bf164fc-6cfc-4f5f-804e-5b84a7eb41fc", "3d7cb33e-f374-40d8-b5dd-90d227d6cf3c", "8a0f1f6c-8060-4be5-9af5-e2dd3f4db278", "6303b6d0-6faf-4494-a537-cccd8e46e8ee", "206e18f1-3902-40cd-8128-cccaf751ca53", "3ca82aaa-0f41-43ec-83c3-b12f94c152e2", "fdc8978b-fb4f-47e6-900d-a5c6534c4239", "8fbfae01-f4e5-4e57-b782-a6c2bb793391"], "durationId": "b26b0a66-b747-4367-8760-e9ba006a16f7", "parent": "0d9a8a87-dd77-49ef-84cd-8f215d74233f"}}, {"head": {"id": "f84fe0c9-202e-4998-b1eb-c00cb9825abf", "name": "default@PreviewArkTS watch work[13] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556653847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9a8a87-dd77-49ef-84cd-8f215d74233f", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323501719461200, "endTime": 323556654017000}, "additional": {"logType": "error", "children": ["8369f110-0a7f-4aec-8d61-6728e3e7a059"], "durationId": "2726466a-3fc3-479f-97e2-a4c7fff21805"}}, {"head": {"id": "93224d6e-9daa-4baa-bf35-c49b7765376e", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556654147700}, "additional": {"logType": "debug", "children": [], "durationId": "2726466a-3fc3-479f-97e2-a4c7fff21805"}}, {"head": {"id": "7f894fbe-0cd3-46d7-a16d-e1d1a97f5a70", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:192:17\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:342:37\n Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/UserApi.ets:21:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/BankCardApi.ets:31:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:45:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:59:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:71:46\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/AddBankCardPage.ets:404:45\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:76:45\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TestConnectionPage.ets:88:45\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TestConnectionPage.ets:130:49\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:197:19\n Property 'timestamp' is missing in type '{ code: any; message: any; data: any; }' but required in type 'ApiResponse<T>'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:329:73\n Argument of type 'HttpResponse' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'HttpResponse'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:351:15\n Type 'number' is not assignable to type 'string'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/AddBankCardPage.ets:405:9\n Type '{ cardNo: string; cardType: BankCardType; bankName: string; holderName: string; }' is not assignable to type 'BankCardBindRequest'.\n  Object literal may only specify known properties, and 'cardNo' does not exist in type 'BankCardBindRequest'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:23:42\n Expected 1-2 arguments, but got 0.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:78:9\n Type '{ amount: number; cardNo: string; paymentPassword: string; description: string; }' is not assignable to type 'WithdrawRequest'.\n  Object literal may only specify known properties, and 'cardNo' does not exist in type 'WithdrawRequest'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:86:18\n Property 'success' does not exist on type 'void'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:88:27\n Property 'message' does not exist on type 'void'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/WithdrawPage.ets:92:32\n Property 'message' does not exist on type 'void'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:54:13\n Type 'Transaction[]' is missing the following properties from type 'PageResult<Transaction>': records, total, size, current, pages\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:54:87\n Argument of type 'TransactionQueryParams' is not assignable to parameter of type 'number'.\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556715634100}, "additional": {"logType": "debug", "children": [], "durationId": "2726466a-3fc3-479f-97e2-a4c7fff21805"}}, {"head": {"id": "cb4d6ca1-366e-4c40-850c-c7581e169f9e", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556736222400, "endTime": 323556736388800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d7ebcce-3e33-4a20-98fe-93abda744359", "logId": "f60dc944-afec-408b-8568-d3c76893f5b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f60dc944-afec-408b-8568-d3c76893f5b7", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556736222400, "endTime": 323556736388800}, "additional": {"logType": "info", "children": [], "durationId": "cb4d6ca1-366e-4c40-850c-c7581e169f9e"}}, {"head": {"id": "0edb933f-9769-4a74-be50-97002b7bd1dc", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323499157955900, "endTime": 323556736817000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 39}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "08a4ba4b-b28b-4411-abf0-800cc62bae26", "name": "BUILD FAILED in 57 s 579 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556736898900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "b8527398-c455-476a-8576-165537b4fd66", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556737440100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b9dd31-03ee-490d-b302-96fcb55600d2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556737654400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fb37b7-7dee-47ec-9b0e-c05c122db4a4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556737820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda0a69a-2b6b-4c6a-b439-1bfb094520c6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556737977900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dec4595-d38e-4d4a-af53-0d196c34b43e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556738135800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc46c217-ba48-4ae7-9229-649e8c6fa9f9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556738289200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbdc7430-e535-447f-aef3-c317f91130e5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556738436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c738034-f23b-4e9c-bf7a-9e55e125ad14", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556738583500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "238e7c2e-1611-4132-a65d-813fca2a2970", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556738726600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb88014-2334-4eb8-ab80-ae5b2b7b6608", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556740154800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d0f741-bb89-4f9f-a41a-66cd916362a6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556751164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85311f55-3f20-4619-8a0a-2afe99c31796", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556754213200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf5ca732-526b-4188-934b-e2db84f6cba9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556755258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ca3284-9665-4c38-92aa-1992c69ac9dd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556756286400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88780f5d-fc2d-4623-801a-4e01747a09fb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556759108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5e4de9f-c069-4a21-980b-663133989e58", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556783837200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758b0716-f0ac-4765-84c8-79b644b491c3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556784353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d2dd44-ec55-484e-9d7c-372c42cbb78f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556784748000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baec3716-1423-4b73-ad94-b19334bf3707", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556785159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ea310f-299c-4f45-823f-16533971530e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:46 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 323556785527300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}