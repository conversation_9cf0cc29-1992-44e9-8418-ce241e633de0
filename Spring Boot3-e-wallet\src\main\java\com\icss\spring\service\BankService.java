package com.icss.spring.service;

import com.icss.spring.entity.BankCard;
import com.icss.spring.entity.Transaction;
import com.icss.spring.entity.User;
import com.icss.spring.mapper.BankCardMapper;
import com.icss.spring.mapper.TransactionMapper;
import com.icss.spring.mapper.UserMapper;
import com.icss.spring.result.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

@Service
public class BankService {
    @Autowired
    private BankCardMapper bankCardMapper;

    @Autowired
    private TransactionMapper transactionMapper;

    @Autowired
    private UserMapper userMapper;

    // 获取用户所有银行卡
    public List<BankCard> getUserCards(Long userId) {
        return bankCardMapper.getCardsByUserId(userId);
    }

    // 获取用户已绑定的银行卡
    public List<BankCard> getBoundCards(Long userId) {
        return bankCardMapper.getBoundCardsByUserId(userId);
    }

    // 获取银行卡详情
    public BankCard getCardDetail(Long cardId) {
        return bankCardMapper.getCardById(cardId);
    }

    // 添加银行卡
    public int addBankCard(BankCard bankCard) {
        return bankCardMapper.addBankCard(bankCard);
    }

    // 绑定银行卡
    public int bindCard(Long cardId) {
        return bankCardMapper.updateBindStatus(cardId, 1);
    }

    // 解绑银行卡
    public int unbindCard(Long cardId) {
        return bankCardMapper.updateBindStatus(cardId, 0);
    }

    // 充值：从银行卡充值到钱包
    @Transactional
    public void deposit(Long userId, Long cardId, BigDecimal amount) {
        // 参数验证
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("充值金额必须大于0");
        }

        // 检查用户是否存在
        User user = userMapper.findUserById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 检查银行卡是否存在且属于该用户
        BankCard card = bankCardMapper.getCardById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }
        if (!card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不属于当前用户");
        }

        // 检查银行卡余额是否足够
        if (card.getCardType().equals("DEBIT") && card.getBalance().compareTo(amount) < 0) {
            throw new ApiException("银行卡余额不足，当前余额：" + card.getBalance());
        }
        if (card.getCardType().equals("CREDIT")) {
            BigDecimal availableCredit = card.getCreditLimit().add(card.getBalance());
            if (availableCredit.compareTo(amount) < 0) {
                throw new ApiException("信用卡可用额度不足，可用额度：" + availableCredit);
            }
        }

        // 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setTransactionType("DEPOSIT");
        transaction.setAmount(amount);
        transaction.setTransactionTime(new Date());
        transaction.setPaymentMethod("BANK_CARD");
        transaction.setPaymentChannel("MERCHANT");
        transaction.setTargetAccount("钱包充值");
        transaction.setCardId(cardId);
        transaction.setDescription("从" + card.getBankName() + "充值到钱包");

        try {
            // 1. 从银行卡扣款
            bankCardMapper.updateCardBalance(cardId, amount.negate());

            // 2. 向钱包充值
            userMapper.toAccount(userId, amount);

            // 3. 记录成功交易
            transaction.setStatus("SUCCESS");
            transactionMapper.addTransaction(transaction);

        } catch (Exception e) {
            // 记录失败交易
            transaction.setStatus("FAILED");
            transactionMapper.addTransaction(transaction);
            throw new ApiException("充值失败：" + e.getMessage());
        }
    }

    // 提现：从钱包提现到银行卡
    @Transactional
    public void withdraw(Long userId, Long cardId, BigDecimal amount) {
        // 参数验证
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("提现金额必须大于0");
        }

        // 检查用户是否存在
        User user = userMapper.findUserById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 检查钱包余额是否足够
        if (user.getAccount().compareTo(amount) < 0) {
            throw new ApiException("钱包余额不足，当前余额：" + user.getAccount());
        }

        // 检查银行卡是否存在且属于该用户
        BankCard card = bankCardMapper.getCardById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }
        if (!card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不属于当前用户");
        }

        // 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setTransactionType("WITHDRAW");
        transaction.setAmount(amount);
        transaction.setTransactionTime(new Date());
        transaction.setPaymentMethod("BANK_CARD");
        transaction.setPaymentChannel("MERCHANT");
        transaction.setTargetAccount(card.getBankName() + "(" + card.getCardNumber() + ")");
        transaction.setCardId(cardId);
        transaction.setDescription("提现到" + card.getBankName());

        try {
            // 1. 从钱包扣款
            userMapper.fromAccount(userId, amount);

            // 2. 向银行卡充值
            bankCardMapper.updateCardBalance(cardId, amount);

            // 3. 记录成功交易
            transaction.setStatus("SUCCESS");
            transactionMapper.addTransaction(transaction);

        } catch (Exception e) {
            // 记录失败交易
            transaction.setStatus("FAILED");
            transactionMapper.addTransaction(transaction);
            throw new ApiException("提现失败：" + e.getMessage());
        }
    }

    // 钱包支付
    @Transactional
    public void payWithWallet(Long userId, BigDecimal amount, String paymentChannel, String targetAccount) {
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setTransactionType("PAYMENT");
        transaction.setAmount(amount);
        transaction.setTransactionTime(new Date());
        transaction.setPaymentMethod("WALLET");
        transaction.setPaymentChannel(paymentChannel);
        transaction.setTargetAccount(targetAccount);

        try {
            // 检查余额是否足够
            User user = userMapper.findUserById(userId);
            if (user.getAccount().compareTo(amount) < 0) {
                transaction.setStatus("FAILED");
                transactionMapper.addTransaction(transaction);
                throw new ApiException("余额不足");
            }

            // 扣减余额
            userMapper.fromAccount(userId, amount);

            // 设置成功状态
            transaction.setStatus("SUCCESS");
            transactionMapper.addTransaction(transaction);
        } catch (ApiException e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            // 其他异常，记录失败状态
            transaction.setStatus("FAILED");
            transactionMapper.addTransaction(transaction);
            throw new ApiException("支付失败：" + e.getMessage());
        }
    }

    // 银行卡支付
    @Transactional
    public void payWithCard(Long userId, Long cardId, BigDecimal amount, String paymentChannel, String targetAccount) {
        Transaction transaction = new Transaction();
        transaction.setUserId(userId);
        transaction.setTransactionType("PAYMENT");
        transaction.setAmount(amount);
        transaction.setTransactionTime(new Date());
        transaction.setPaymentMethod("BANK_CARD");
        transaction.setPaymentChannel(paymentChannel);
        transaction.setTargetAccount(targetAccount);
        transaction.setCardId(cardId);

        try {
            // 检查银行卡是否存在
            BankCard card = bankCardMapper.getCardById(cardId);
            if (card == null) {
                transaction.setStatus("FAILED");
                transactionMapper.addTransaction(transaction);
                throw new ApiException("银行卡不存在");
            }

            // 检查银行卡余额是否足够
            if (card.getCardType().equals("DEBIT") && card.getBalance().compareTo(amount) < 0) {
                transaction.setStatus("FAILED");
                transactionMapper.addTransaction(transaction);
                throw new ApiException("银行卡余额不足");
            }
            if (card.getCardType().equals("CREDIT") &&
                    card.getBalance().add(card.getCreditLimit()).compareTo(amount) < 0) {
                transaction.setStatus("FAILED");
                transactionMapper.addTransaction(transaction);
                throw new ApiException("信用卡额度不足");
            }

            // 更新银行卡余额
            bankCardMapper.updateCardBalance(cardId, amount.negate());

            // 设置成功状态
            transaction.setStatus("SUCCESS");
            transactionMapper.addTransaction(transaction);
        } catch (ApiException e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            // 其他异常，记录失败状态
            transaction.setStatus("FAILED");
            transactionMapper.addTransaction(transaction);
            throw new ApiException("支付失败：" + e.getMessage());
        }
    }

    // 根据银行卡号查询用户ID
    public Long getUserIdByCardNumber(String cardNumber) {
        return bankCardMapper.getUserIdByCardNumber(cardNumber);
    }

    // 根据银行卡号查询银行卡信息
    public BankCard getCardByCardNumber(String cardNumber) {
        return bankCardMapper.getCardByCardNumber(cardNumber);
    }

    // 转账 - 支持银行卡号作为目标账户
    @Transactional
    public void transfer(Long fromUserId, String targetAccount, BigDecimal amount, String paymentMethod, Long cardId) {
        // 检查转出用户是否存在
        User fromUser = userMapper.findUserById(fromUserId);
        if (fromUser == null) {
            throw new ApiException("转出用户不存在");
        }

        // 解析目标账户 - 可能是用户ID或银行卡号
        Long toUserId = null;
        String displayTargetAccount = targetAccount;

        try {
            // 首先尝试作为用户ID解析
            toUserId = Long.parseLong(targetAccount);
            User toUser = userMapper.findUserById(toUserId);
            if (toUser == null) {
                throw new ApiException("目标用户不存在");
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，则作为银行卡号处理
            toUserId = getUserIdByCardNumber(targetAccount);
            if (toUserId == null) {
                throw new ApiException("查询不到该银行卡号");
            }
            BankCard targetCard = getCardByCardNumber(targetAccount);
            displayTargetAccount = targetCard.getBankName() + "(" + targetAccount + ")";
        }

        // 检查是否转账给自己
        if (fromUserId.equals(toUserId)) {
            throw new ApiException("不能转账给自己");
        }

        // 检查余额是否足够
        if (fromUser.getAccount().compareTo(amount) < 0) {
            throw new ApiException("余额不足");
        }

        // 转出
        userMapper.fromAccount(fromUserId, amount);

        // 转入
        userMapper.toAccount(toUserId, amount);

        // 记录转出交易
        Transaction outTransaction = new Transaction();
        outTransaction.setUserId(fromUserId);
        outTransaction.setTransactionType("TRANSFER");
        outTransaction.setAmount(amount);
        outTransaction.setTransactionTime(new Date());
        outTransaction.setPaymentMethod(paymentMethod != null ? paymentMethod : "WALLET");
        outTransaction.setTargetAccount(displayTargetAccount);
        outTransaction.setStatus("SUCCESS");
        if (cardId != null) {
            outTransaction.setCardId(cardId);
        }
        transactionMapper.addTransaction(outTransaction);

        // 记录转入交易
        Transaction inTransaction = new Transaction();
        inTransaction.setUserId(toUserId);
        inTransaction.setTransactionType("RECEIVE");
        inTransaction.setAmount(amount);
        inTransaction.setTransactionTime(new Date());
        inTransaction.setPaymentMethod("WALLET");
        inTransaction.setTargetAccount("来自用户" + fromUserId);
        inTransaction.setStatus("SUCCESS");
        transactionMapper.addTransaction(inTransaction);
    }

    // 获取交易记录
    public List<Transaction> getTransactions(Long userId) {
        return transactionMapper.getTransactionsByUserId(userId);
    }

    // 按类型获取交易记录
    public List<Transaction> getTransactionsByType(Long userId, String type) {
        return transactionMapper.getTransactionsByType(userId, type);
    }

    // 按支付方式获取交易记录
    public List<Transaction> getTransactionsByPaymentMethod(Long userId, String method) {
        return transactionMapper.getTransactionsByPaymentMethod(userId, method);
    }

    // 获取余额
    public BigDecimal getBalance(Long userId) {
        User user = userMapper.findUserById(userId);
        return user.getAccount();
    }

    // 修改银行卡信息
    public int updateBankCard(BankCard bankCard) {
        return bankCardMapper.updateBankCard(bankCard);
    }

    // 删除银行卡
    public int deleteBankCard(Long cardId) {
        return bankCardMapper.deleteBankCard(cardId);
    }

    // 删除交易记录
    public int deleteTransaction(Long transactionId) {
        return transactionMapper.deleteTransaction(transactionId);
    }

    // 删除重复交易记录
    public int removeDuplicateTransactions(Long userId) {
        // 获取所有交易记录
        List<Transaction> allTransactions = transactionMapper.getAllTransactions(userId);

        // 找出重复的交易记录ID
        Set<Long> duplicateIds = findDuplicateTransactionIds(allTransactions);

        if (duplicateIds.isEmpty()) {
            return 0;
        }

        // 批量删除重复记录
        int deletedCount = 0;
        for (Long duplicateId : duplicateIds) {
            deletedCount += transactionMapper.deleteTransaction(duplicateId);
        }

        return deletedCount;
    }

    // 查找重复的交易记录ID
    private Set<Long> findDuplicateTransactionIds(List<Transaction> transactions) {
        Set<Long> duplicateIds = new HashSet<>();
        Set<String> seen = new HashSet<>();

        for (Transaction transaction : transactions) {
            // 创建唯一标识：用户ID + 交易类型 + 金额 + 时间 + 目标账户
            String key = transaction.getUserId() + "_" +
                        transaction.getTransactionType() + "_" +
                        transaction.getAmount() + "_" +
                        transaction.getTransactionTime() + "_" +
                        (transaction.getTargetAccount() != null ? transaction.getTargetAccount() : "");

            if (seen.contains(key)) {
                // 如果已经存在相同的记录，标记为重复
                duplicateIds.add(transaction.getId());
            } else {
                seen.add(key);
            }
        }

        return duplicateIds;
    }

}