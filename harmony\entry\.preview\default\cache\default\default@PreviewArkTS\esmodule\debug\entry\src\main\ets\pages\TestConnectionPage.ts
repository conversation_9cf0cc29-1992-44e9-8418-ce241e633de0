if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TestConnectionPage_Params {
    testResult?: string;
    isLoading?: boolean;
}
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import promptAction from "@ohos:promptAction";
import type { UserInfo, UserLoginFormData } from '../common/types/index';
import http from "@ohos:net.http";
import type { BusinessError } from "@ohos:base";
class TestConnectionPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__testResult = new ObservedPropertySimplePU('等待测试...', this, "testResult");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TestConnectionPage_Params) {
        if (params.testResult !== undefined) {
            this.testResult = params.testResult;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: TestConnectionPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__testResult.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__testResult.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __testResult: ObservedPropertySimplePU<string>;
    get testResult() {
        return this.__testResult.get();
    }
    set testResult(newValue: string) {
        this.__testResult.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(14:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('Spring Boot 连接测试');
            Text.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(15:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ top: 50 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.testResult);
            Text.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(20:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.textAlign(TextAlign.Center);
            Text.width('90%');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试连接 (10.0.2.2:8080)');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(26:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testConnection();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试本地连接 (127.0.0.1:8080)');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(34:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testLocalConnection();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试实际IP (*************:8080)');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(42:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testRealIPConnection();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试银行卡API');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(50:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testBankAPI();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试HarmonyOS专用API');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(58:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testHarmonyAPI();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('测试登录API');
            Button.debugLine("entry/src/main/ets/pages/TestConnectionPage.ets(66:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.testLoginAPI();
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    async testConnection() {
        this.isLoading = true;
        this.testResult = '正在测试连接...\n目标地址: http://10.0.2.2:8080';
        try {
            // 测试HarmonyOS专用API
            console.log('测试HarmonyOS API连接到: http://10.0.2.2:8080/test');
            this.testResult += '\n正在发送请求...';
            const response = await httpClient.get<string>('/test');
            console.log('HarmonyOS API响应:', response);
            this.testResult = `HarmonyOS API连接成功！\n响应: ${JSON.stringify(response, null, 2)}`;
            promptAction.showToast({ message: 'HarmonyOS API连接测试成功' });
        }
        catch (error) {
            console.error('连接测试失败:', error);
            // 详细的错误分析
            let errorMsg = '连接失败\n';
            if (error.code === 2300028) {
                errorMsg += '错误类型: 连接超时\n';
                errorMsg += '可能原因:\n';
                errorMsg += '1. Spring Boot后端未启动\n';
                errorMsg += '2. 模拟器网络配置问题\n';
                errorMsg += '3. 防火墙阻止连接\n';
                errorMsg += '4. IP地址配置错误\n';
            }
            this.testResult = errorMsg + `详细信息: ${JSON.stringify(error, null, 2)}`;
            promptAction.showToast({ message: '连接测试失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    // 测试本地连接
    async testLocalConnection() {
        this.isLoading = true;
        this.testResult = '正在测试本地连接...\n目标地址: http://127.0.0.1:8080';
        try {
            // 创建临时HTTP客户端测试本地连接
            const httpRequest = http.createHttp();
            const headers: Record<string, string> = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            const options: http.HttpRequestOptions = {
                method: http.RequestMethod.GET,
                header: headers,
                expectDataType: http.HttpDataType.STRING,
                usingCache: false,
                connectTimeout: 10000,
                readTimeout: 10000
            };
            console.log('测试本地连接到: http://127.0.0.1:8080/test');
            httpRequest.request('http://127.0.0.1:8080/test', options, (err: BusinessError, data: http.HttpResponse) => {
                if (err) {
                    console.error('本地连接失败:', err);
                    this.testResult = `本地连接失败\n错误代码: ${err.code}\n错误信息: ${err.message}`;
                }
                else {
                    console.log('本地连接成功:', data);
                    this.testResult = `本地连接成功！\n响应: ${JSON.stringify(data, null, 2)}`;
                }
                this.isLoading = false;
            });
        }
        catch (error) {
            console.error('本地连接测试异常:', error);
            this.testResult = `本地连接测试异常: ${error instanceof Error ? error.message : '未知错误'}`;
            this.isLoading = false;
        }
    }
    // 测试实际IP连接
    async testRealIPConnection() {
        this.isLoading = true;
        this.testResult = '正在测试实际IP连接...\n目标地址: http://*************:8080';
        try {
            // 创建临时HTTP客户端测试实际IP连接
            const httpRequest = http.createHttp();
            const headers: Record<string, string> = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            const options: http.HttpRequestOptions = {
                method: http.RequestMethod.GET,
                header: headers,
                expectDataType: http.HttpDataType.STRING,
                usingCache: false,
                connectTimeout: 10000,
                readTimeout: 10000
            };
            console.log('测试实际IP连接到: http://*************:8080/test');
            httpRequest.request('http://*************:8080/test', options, (err: BusinessError, data: http.HttpResponse) => {
                if (err) {
                    console.error('实际IP连接失败:', err);
                    this.testResult = `实际IP连接失败\n错误代码: ${err.code}\n错误信息: ${err.message}`;
                }
                else {
                    console.log('实际IP连接成功:', data);
                    this.testResult = `实际IP连接成功！\n响应: ${JSON.stringify(data, null, 2)}`;
                }
                this.isLoading = false;
            });
        }
        catch (error) {
            console.error('实际IP连接测试异常:', error);
            this.testResult = `实际IP连接测试异常: ${error instanceof Error ? error.message : '未知错误'}`;
            this.isLoading = false;
        }
    }
    async testBankAPI() {
        this.isLoading = true;
        this.testResult = '正在测试银行卡API...';
        try {
            // 测试银行卡API
            const response = await httpClient.get<string>('/bank/cards/1');
            this.testResult = `银行卡API测试成功！\n响应: ${JSON.stringify(response, null, 2)}`;
            promptAction.showToast({ message: '银行卡API测试成功' });
        }
        catch (error) {
            console.error('银行卡API测试失败:', error);
            this.testResult = `银行卡API测试失败: ${error.message || '未知错误'}`;
            promptAction.showToast({ message: '银行卡API测试失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    async testHarmonyAPI() {
        this.isLoading = true;
        this.testResult = '正在测试HarmonyOS专用API...';
        try {
            // 使用原生HTTP请求测试，避免HttpClient的认证逻辑
            const httpRequest = http.createHttp();
            const headers: Record<string, string> = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            const options: http.HttpRequestOptions = {
                method: http.RequestMethod.GET,
                header: headers,
                expectDataType: http.HttpDataType.STRING,
                usingCache: false,
                connectTimeout: 10000,
                readTimeout: 10000
            };
            console.log('测试HarmonyOS专用API: http://*************:8080/test');
            httpRequest.request('http://*************:8080/test', options, (err: BusinessError, data: http.HttpResponse) => {
                if (err) {
                    console.error('HarmonyOS API测试失败:', err);
                    this.testResult = `HarmonyOS API测试失败\n错误代码: ${err.code}\n错误信息: ${err.message}`;
                }
                else {
                    console.log('HarmonyOS API测试成功:', data);
                    this.testResult = `HarmonyOS API测试成功！\n状态码: ${data.responseCode}\n响应: ${data.result}`;
                    promptAction.showToast({ message: 'HarmonyOS API测试成功' });
                }
                this.isLoading = false;
            });
        }
        catch (error) {
            console.error('HarmonyOS API测试异常:', error);
            this.testResult = `HarmonyOS API测试异常: ${error instanceof Error ? error.message : '未知错误'}`;
            this.isLoading = false;
        }
    }
    async testLoginAPI() {
        this.isLoading = true;
        this.testResult = '正在测试登录API...';
        try {
            // 先获取验证码
            console.log('获取验证码...');
            const captchaResponse = await httpClient.get<string>('/api/user/captcha');
            console.log('验证码响应:', captchaResponse);
            // 测试登录
            console.log('测试登录...');
            const loginData: UserLoginFormData = {
                username: 'admin',
                password: 'admin123',
                captcha: captchaResponse.data
            };
            const loginResponse = await httpClient.postForm<UserInfo>('/api/user/login', loginData);
            console.log('登录响应:', loginResponse);
            this.testResult = `登录API测试成功！\n验证码: ${captchaResponse.data}\n登录响应: ${JSON.stringify(loginResponse, null, 2)}`;
            promptAction.showToast({ message: '登录API测试成功' });
        }
        catch (error) {
            console.error('登录API测试失败:', error);
            this.testResult = `登录API测试失败: ${error.message || '未知错误'}\n详细信息: ${JSON.stringify(error, null, 2)}`;
            promptAction.showToast({ message: '登录API测试失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TestConnectionPage";
    }
}
registerNamedRoute(() => new TestConnectionPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TestConnectionPage", pageFullPath: "entry/src/main/ets/pages/TestConnectionPage", integratedHsp: "false", moduleType: "followWithHap" });
