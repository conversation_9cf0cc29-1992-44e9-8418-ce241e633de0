struct preview{build(){Row() {
        Text('交易记录')
          .fontSize(20)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
      }
      .width('100%')
      .height(60)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
    
      .linearGradient({
        direction: GradientDirection.Right,
        colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
      })}}