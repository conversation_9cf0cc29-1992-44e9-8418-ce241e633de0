if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TestPaymentPage_Params {
}
import router from "@ohos:router";
class TestPaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TestPaymentPage_Params) {
    }
    updateStateVars(params: TestPaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    aboutToAppear() {
        console.log('TestPaymentPage 页面加载成功');
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TestPaymentPage.ets(11:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.backgroundColor('#F0F8FF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('✅ 路由跳转成功！');
            Text.debugLine("entry/src/main/ets/pages/TestPaymentPage.ets(12:7)", "entry");
            Text.fontSize(28);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#4CAF50');
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('这是测试支付页面');
            Text.debugLine("entry/src/main/ets/pages/TestPaymentPage.ets(18:7)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回支付中心');
            Button.debugLine("entry/src/main/ets/pages/TestPaymentPage.ets(23:7)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4A90E2');
            Button.borderRadius(8);
            Button.width(150);
            Button.height(44);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TestPaymentPage";
    }
}
registerNamedRoute(() => new TestPaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TestPaymentPage", pageFullPath: "entry/src/main/ets/pages/TestPaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
