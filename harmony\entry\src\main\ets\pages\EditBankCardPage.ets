import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCard, BankCardType, BankCardStatus } from '../common/types/index';
import { BankCardApi } from '../api/BankCardApi';

@Entry
@Component
struct EditBankCardPage {
  @State cardId: number = 0;
  @State formCardNo: string = '';
  @State formCardType: string = '储蓄卡';
  @State formBankName: string = '';
  @State formHolderName: string = '';
  @State isLoading: boolean = false;
  @State isSubmitting: boolean = false;

  aboutToAppear() {
    console.log('EditBankCardPage aboutToAppear');
    
    // 获取路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params && params.cardId) {
      this.cardId = params.cardId as number;
      this.loadCardData();
    } else {
      promptAction.showToast({ message: '参数错误' });
      router.back();
    }
  }

  /**
   * 加载银行卡数据
   */
  async loadCardData() {
    this.isLoading = true;
    try {
      const userId = 1; // 临时使用固定用户ID
      const cardList = await BankCardApi.getCardList(userId);
      const card = cardList.find(c => c.cardId === this.cardId);
      
      if (card) {
        this.formCardNo = card.cardNo;
        this.formCardType = card.cardType === BankCardType.DEBIT ? '储蓄卡' : '信用卡';
        this.formBankName = card.bankName;
        this.formHolderName = card.holderName;
      } else {
        promptAction.showToast({ message: '银行卡不存在' });
        router.back();
      }
    } catch (error) {
      console.error('加载银行卡数据失败:', error);
      promptAction.showToast({ message: '加载失败' });
      router.back();
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('修改银行卡')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位，保持标题居中
        Row()
          .width(40)
          .height(40)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#FFFFFF')

      if (this.isLoading) {
        // 加载状态
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')
          
          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 12 })
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      } else {
        // 表单内容
        Column({ space: 20 }) {
          // 银行卡号
          Column() {
            Row() {
              Text('* ')
                .fontSize(14)
                .fontColor('#FF4444')
              Text('银行卡号')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入16-19位银行卡号', text: this.formCardNo })
              .width('100%')
              .height(48)
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E0E0E0' })
              .padding({ left: 16, right: 16 })
              .fontSize(16)
              .type(InputType.Number)
              .onChange((value: string) => {
                this.formCardNo = value;
              })
          }
          .alignItems(HorizontalAlign.Start)

          // 持卡人姓名
          Column() {
            Row() {
              Text('* ')
                .fontSize(14)
                .fontColor('#FF4444')
              Text('持卡人姓名')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入持卡人姓名', text: this.formHolderName })
              .width('100%')
              .height(48)
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E0E0E0' })
              .padding({ left: 16, right: 16 })
              .fontSize(16)
              .onChange((value: string) => {
                this.formHolderName = value;
              })
          }
          .alignItems(HorizontalAlign.Start)

          // 卡片类型
          Column() {
            Row() {
              Text('* ')
                .fontSize(14)
                .fontColor('#FF4444')
              Text('卡片类型')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            Row() {
              Text(this.formCardType)
                .fontSize(16)
                .fontColor('#333333')
                .layoutWeight(1)

              Text('▼')
                .fontSize(12)
                .fontColor('#999999')
            }
            .width('100%')
            .height(48)
            .backgroundColor('#F8F9FA')
            .borderRadius(8)
            .border({ width: 1, color: '#E0E0E0' })
            .padding({ left: 16, right: 16 })
            .onClick(() => {
              this.showCardTypeSelector();
            })
          }
          .alignItems(HorizontalAlign.Start)

          // 银行名称
          Column() {
            Row() {
              Text('* ')
                .fontSize(14)
                .fontColor('#FF4444')
              Text('银行名称')
                .fontSize(14)
                .fontColor('#333333')
            }
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入银行名称', text: this.formBankName })
              .width('100%')
              .height(48)
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E0E0E0' })
              .padding({ left: 16, right: 16 })
              .fontSize(16)
              .onChange((value: string) => {
                this.formBankName = value;
              })
          }
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .padding({ left: 20, right: 20, top: 24 })
        .layoutWeight(1)

        // 底部按钮
        Column() {
          Button(this.isSubmitting ? '保存中...' : '保存修改')
            .width('100%')
            .height(48)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor(this.isSubmitting ? '#CCCCCC' : '#1976D2')
            .borderRadius(8)
            .enabled(!this.isSubmitting)
            .onClick(() => {
              this.confirmEditBankCard();
            })
        }
        .width('100%')
        .padding({ left: 20, right: 20, bottom: 20 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  /**
   * 显示卡片类型选择器
   */
  async showCardTypeSelector() {
    try {
      const result = await promptAction.showActionMenu({
        title: '选择卡片类型',
        buttons: [
          { text: '储蓄卡', color: '#1976D2' },
          { text: '信用卡', color: '#1976D2' }
        ]
      });

      if (result.index === 0) {
        this.formCardType = '储蓄卡';
      } else if (result.index === 1) {
        this.formCardType = '信用卡';
      }
    } catch (error) {
      console.error('选择卡片类型失败:', error);
    }
  }

  /**
   * 确认修改银行卡
   */
  async confirmEditBankCard() {
    // 表单验证
    if (!this.formCardNo || this.formCardNo.length < 16) {
      promptAction.showToast({ message: '请输入正确的银行卡号' });
      return;
    }
    if (!this.formHolderName) {
      promptAction.showToast({ message: '请输入持卡人姓名' });
      return;
    }
    if (!this.formBankName) {
      promptAction.showToast({ message: '请输入银行名称' });
      return;
    }

    this.isSubmitting = true;

    try {
      // 更新银行卡信息
      const updatedCard: BankCard = {
        cardId: this.cardId,
        userId: 1,
        cardNo: this.formCardNo,
        cardType: this.formCardType === '储蓄卡' ? BankCardType.DEBIT : BankCardType.CREDIT,
        bankName: this.formBankName,
        holderName: this.formHolderName,
        isBound: BankCardStatus.BOUND,
        createTime: '',
        updateTime: new Date().toISOString()
      };

      // 调用API更新银行卡
      await BankCardApi.updateCard(updatedCard);
      
      promptAction.showToast({ message: '银行卡修改成功' });
      
      // 返回上一页
      router.back();
    } catch (error) {
      console.error('修改银行卡失败:', error);
      promptAction.showToast({ message: '修改失败，请重试' });
    } finally {
      this.isSubmitting = false;
    }
  }
}
