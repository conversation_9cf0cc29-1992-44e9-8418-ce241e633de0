package com.icss.spring.mapper;

import com.icss.spring.entity.Transaction;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface TransactionMapper {
    @Insert("INSERT INTO transaction(user_id, transaction_type, amount, transaction_time, payment_method, payment_channel, target_account, card_id, status, description) " +
            "VALUES(#{userId}, #{transactionType}, #{amount}, #{transactionTime}, #{paymentMethod}, #{paymentChannel}, #{targetAccount}, #{cardId}, #{status}, #{description})")
    int addTransaction(Transaction transaction);

    @Select("SELECT * FROM transaction WHERE user_id = #{userId} ORDER BY transaction_time DESC")
    List<Transaction> getTransactionsByUserId(@Param("userId") Long userId);

    @Select("SELECT * FROM transaction WHERE user_id = #{userId} AND transaction_type = #{type} ORDER BY transaction_time DESC")
    List<Transaction> getTransactionsByType(@Param("userId") Long userId, @Param("type") String type);

    @Select("SELECT * FROM transaction WHERE user_id = #{userId} AND payment_method = #{method} ORDER BY transaction_time DESC")
    List<Transaction> getTransactionsByPaymentMethod(@Param("userId") Long userId, @Param("method") String method);

    @Delete("DELETE FROM transaction WHERE id = #{transactionId}")
    int deleteTransaction(@Param("transactionId") Long transactionId);

    @Select("SELECT * FROM transaction WHERE user_id = #{userId} ORDER BY id ASC")
    List<Transaction> getAllTransactions(@Param("userId") Long userId);
}