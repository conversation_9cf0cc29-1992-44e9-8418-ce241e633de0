package com.icss.spring.controller;

import com.icss.spring.result.Result;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * HarmonyOS应用专用控制器
 * 提供与HarmonyOS应用兼容的API接口
 */
@CrossOrigin
@RestController
public class HarmonyController {

    /**
     * HarmonyOS转账接口
     * 对应HarmonyOS代码中的 /transfer 接口
     */
    @PostMapping("/transfer")
    public Result<String> transfer(@RequestParam Long userId,
                                   @RequestParam String toMobile,
                                   @RequestParam BigDecimal amount,
                                   @RequestParam(required = false) String tradeDesc) {
        try {
            // 验证金额
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("转账金额必须大于0");
            }

            // 验证手机号格式（简单验证）
            if (toMobile == null || toMobile.trim().isEmpty()) {
                return Result.error("收款方手机号不能为空");
            }

            // 模拟转账成功（暂时不调用实际的业务逻辑）
            String message = String.format("转账成功！用户%d向%s转账%.2f元", userId, toMobile, amount);
            if (tradeDesc != null && !tradeDesc.trim().isEmpty()) {
                message += "，说明：" + tradeDesc;
            }

            return Result.OK(message);
        } catch (Exception e) {
            return Result.error("转账失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户余额（模拟）
     */
    @GetMapping("/balance/{userId}")
    public Result<BigDecimal> getBalance(@PathVariable Long userId) {
        try {
            // 模拟返回余额
            BigDecimal balance = new BigDecimal("1000.00");
            return Result.OK("查询成功", balance);
        } catch (Exception e) {
            return Result.error("查询余额失败：" + e.getMessage());
        }
    }

    /**
     * 测试连接
     */
    @GetMapping("/test")
    public Result<String> test() {
        return Result.OK("HarmonyOS API连接正常", "服务运行中");
    }
}
