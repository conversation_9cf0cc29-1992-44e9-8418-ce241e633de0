{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "283f6037-e4f7-4362-9595-be517f6ff54e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255199099400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb35eb4-ede6-478a-936e-7975f647b5ba", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255215178100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7649bb52-aa57-4092-b45a-758eb19f14ec", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324255215500400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663d9c48-2fa1-4816-a01d-4d57637c22e1", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720826141200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720833201000, "endTime": 324721072105400}, "additional": {"children": ["29466d4d-4b17-46e2-bb8d-fc0f9b78e4c4", "ed6b9c39-ae22-4432-9da2-61d75908211b", "901b0376-318f-48ac-a3d1-6a603a91aa86", "628e6e8e-4f5c-4d5c-9b54-d798b8bf79bf", "7b1f3744-b558-4689-bae6-fbc218db4214", "ca4e8112-e1e2-4e65-843d-b5803eb24ca6", "343a463a-3792-4a8d-8dfe-8724a2b082ec"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "44b1278c-9786-4447-919a-23d2c5a90edf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29466d4d-4b17-46e2-bb8d-fc0f9b78e4c4", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720833202700, "endTime": 324720845766700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "b1e7fec0-62a8-4b49-a2c7-bcebd66beb08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed6b9c39-ae22-4432-9da2-61d75908211b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720845780900, "endTime": 324721070866000}, "additional": {"children": ["c49eaca9-16ab-4e2f-93bd-76d4eb7ea710", "2280af6d-3742-4377-a7c1-ea2c783f1716", "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "f64c2f5f-9fc8-4da2-8eb3-1efcb5e58551", "f9e6bdc1-df26-4ba9-9c98-8b0472544efe", "1d7bc608-8f44-4ae7-a576-151d659a8a10", "1dbe9bcc-1ced-433b-8e9e-061c4fef2877", "03254afe-5f2a-4db1-a82f-01b0975a6993", "219ff31c-3456-4626-899e-5692067c2694"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "672b0338-d69d-4e75-95e7-da339689d768"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "901b0376-318f-48ac-a3d1-6a603a91aa86", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721070885700, "endTime": 324721072098400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "da963cbc-5168-43c6-9ac1-524d75a8f84b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "628e6e8e-4f5c-4d5c-9b54-d798b8bf79bf", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721072102000, "endTime": 324721072103000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "1b2581b9-3e2a-445b-8dd6-1baa220b2605"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b1f3744-b558-4689-bae6-fbc218db4214", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720836467200, "endTime": 324720836503000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "d22a1191-8f65-40de-9385-0add0ebdd529"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d22a1191-8f65-40de-9385-0add0ebdd529", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720836467200, "endTime": 324720836503000}, "additional": {"logType": "info", "children": [], "durationId": "7b1f3744-b558-4689-bae6-fbc218db4214", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "ca4e8112-e1e2-4e65-843d-b5803eb24ca6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720842027900, "endTime": 324720842046400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "f44c083a-56a6-4509-a2dd-5b8e7d0fe7e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f44c083a-56a6-4509-a2dd-5b8e7d0fe7e1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720842027900, "endTime": 324720842046400}, "additional": {"logType": "info", "children": [], "durationId": "ca4e8112-e1e2-4e65-843d-b5803eb24ca6", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "f45f513c-b78b-42cd-81d9-78b6b4862eb6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720842088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58dab97b-74ed-4dd7-8dd5-423f230398d2", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720845665400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1e7fec0-62a8-4b49-a2c7-bcebd66beb08", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720833202700, "endTime": 324720845766700}, "additional": {"logType": "info", "children": [], "durationId": "29466d4d-4b17-46e2-bb8d-fc0f9b78e4c4", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "c49eaca9-16ab-4e2f-93bd-76d4eb7ea710", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720851523600, "endTime": 324720851530600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "65ff8a08-be4d-4971-9be7-722433d9cf7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2280af6d-3742-4377-a7c1-ea2c783f1716", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720851542600, "endTime": 324720855511400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "60c61048-c61e-4ec5-90be-ffc058fe6bdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720855522000, "endTime": 324720967676400}, "additional": {"children": ["68412297-0be4-4a7a-bb6d-9f983b8ade38", "14af566f-81fa-4f21-9435-d9ce195e46d1", "2669f247-d93d-46e1-a54b-a2fd5bbf7303"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "2da9de8d-4743-4809-a5c2-687e26642748"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f64c2f5f-9fc8-4da2-8eb3-1efcb5e58551", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720967696700, "endTime": 324720992678900}, "additional": {"children": ["581a5931-714d-4718-b161-1e5826ca9327"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "a9c32c85-338e-47bb-802d-bfe8ea262368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9e6bdc1-df26-4ba9-9c98-8b0472544efe", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720992685500, "endTime": 324721049024000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "6085c245-3f1d-4154-9271-1a26b99019e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d7bc608-8f44-4ae7-a576-151d659a8a10", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721050053200, "endTime": 324721058156500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "11f56ec0-ed4c-4634-9d25-cf75046bc25b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dbe9bcc-1ced-433b-8e9e-061c4fef2877", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721058176400, "endTime": 324721070714700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "58a05000-b259-41f4-9bb7-eca3f864ab23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03254afe-5f2a-4db1-a82f-01b0975a6993", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721070741000, "endTime": 324721070857300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "660798f8-933d-41b5-981f-5212e0d6daf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65ff8a08-be4d-4971-9be7-722433d9cf7a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720851523600, "endTime": 324720851530600}, "additional": {"logType": "info", "children": [], "durationId": "c49eaca9-16ab-4e2f-93bd-76d4eb7ea710", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "60c61048-c61e-4ec5-90be-ffc058fe6bdc", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720851542600, "endTime": 324720855511400}, "additional": {"logType": "info", "children": [], "durationId": "2280af6d-3742-4377-a7c1-ea2c783f1716", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "68412297-0be4-4a7a-bb6d-9f983b8ade38", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720856158600, "endTime": 324720856173500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "logId": "31dfda37-d76a-4d92-9be5-936be73fde47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31dfda37-d76a-4d92-9be5-936be73fde47", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720856158600, "endTime": 324720856173500}, "additional": {"logType": "info", "children": [], "durationId": "68412297-0be4-4a7a-bb6d-9f983b8ade38", "parent": "2da9de8d-4743-4809-a5c2-687e26642748"}}, {"head": {"id": "14af566f-81fa-4f21-9435-d9ce195e46d1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720858223700, "endTime": 324720966281500}, "additional": {"children": ["b5572085-8863-49d8-aa97-7e8f93c5edea", "e7c801e3-17e4-408f-b783-a2612fc4345b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "logId": "e5b94eac-d781-47d2-9b34-981f8d3e4aa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5572085-8863-49d8-aa97-7e8f93c5edea", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720858224900, "endTime": 324720874965500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14af566f-81fa-4f21-9435-d9ce195e46d1", "logId": "c84da52b-25c3-49e3-84a8-70fbaa138640"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7c801e3-17e4-408f-b783-a2612fc4345b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720874987500, "endTime": 324720966263000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14af566f-81fa-4f21-9435-d9ce195e46d1", "logId": "975b2b75-cd50-4674-bf0b-a0b96aae1407"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4200de2d-e8d1-45df-bdc9-e99af61b8bdd", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720858230100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3a41a1-e23a-4a65-8630-45d91b7ba8d4", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720874795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84da52b-25c3-49e3-84a8-70fbaa138640", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720858224900, "endTime": 324720874965500}, "additional": {"logType": "info", "children": [], "durationId": "b5572085-8863-49d8-aa97-7e8f93c5edea", "parent": "e5b94eac-d781-47d2-9b34-981f8d3e4aa9"}}, {"head": {"id": "6c7b93fa-6e19-42bd-b92a-33a944f8418d", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720875005200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb4f0be-d455-4d09-b79b-455328760db8", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720883310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67876e30-dbfe-413c-b5b6-7ad6a68cc552", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720883464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb372ead-984f-4c70-81fe-1d0fa6f8492c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720883629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc57b95-19f3-4a43-9096-69c97005a4bb", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720883734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e64c6ab-480c-4d39-ba44-30bee31f68c5", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720886919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92fc3dc3-de84-4a41-ae0d-9ed8a2f505bb", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720891525900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5966a0a0-d178-42ee-89f1-e84e828c66b7", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720901334100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d856f6-e459-4335-a5c4-e3290da0c8cd", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720929787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9a4860-c3fe-43ac-a8fa-5de049c9de7d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720929917500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 59}, "markType": "other"}}, {"head": {"id": "aa8dadfe-f1b8-454d-b6c5-99497845e807", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720929931800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 59}, "markType": "other"}}, {"head": {"id": "98e22e4d-df51-4def-b35c-f285ffdb1bda", "name": "Project task initialization takes 35 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720965743000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21524214-426a-4013-949a-e67eae947916", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720965932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf046ef1-5903-41a4-996a-8004167ee1ff", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720966048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47742434-7614-432d-b297-52cb99741f6a", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720966181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975b2b75-cd50-4674-bf0b-a0b96aae1407", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720874987500, "endTime": 324720966263000}, "additional": {"logType": "info", "children": [], "durationId": "e7c801e3-17e4-408f-b783-a2612fc4345b", "parent": "e5b94eac-d781-47d2-9b34-981f8d3e4aa9"}}, {"head": {"id": "e5b94eac-d781-47d2-9b34-981f8d3e4aa9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720858223700, "endTime": 324720966281500}, "additional": {"logType": "info", "children": ["c84da52b-25c3-49e3-84a8-70fbaa138640", "975b2b75-cd50-4674-bf0b-a0b96aae1407"], "durationId": "14af566f-81fa-4f21-9435-d9ce195e46d1", "parent": "2da9de8d-4743-4809-a5c2-687e26642748"}}, {"head": {"id": "2669f247-d93d-46e1-a54b-a2fd5bbf7303", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720967626400, "endTime": 324720967652500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "logId": "392d5286-7786-4561-8906-9f583af23c5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "392d5286-7786-4561-8906-9f583af23c5a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720967626400, "endTime": 324720967652500}, "additional": {"logType": "info", "children": [], "durationId": "2669f247-d93d-46e1-a54b-a2fd5bbf7303", "parent": "2da9de8d-4743-4809-a5c2-687e26642748"}}, {"head": {"id": "2da9de8d-4743-4809-a5c2-687e26642748", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720855522000, "endTime": 324720967676400}, "additional": {"logType": "info", "children": ["31dfda37-d76a-4d92-9be5-936be73fde47", "e5b94eac-d781-47d2-9b34-981f8d3e4aa9", "392d5286-7786-4561-8906-9f583af23c5a"], "durationId": "b89bafd6-0fcc-49c4-b05d-ef9f976e0290", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "581a5931-714d-4718-b161-1e5826ca9327", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720969022000, "endTime": 324720992668600}, "additional": {"children": ["a6be693f-8504-4e74-8213-9688490bf576", "84f7f8ea-4067-48f2-a10f-fea90587c058", "dae0517c-6c9f-47eb-8dca-11c6f2c69913"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f64c2f5f-9fc8-4da2-8eb3-1efcb5e58551", "logId": "fae317fb-7ea0-44b2-908b-4f3b9ecacdef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6be693f-8504-4e74-8213-9688490bf576", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720972810500, "endTime": 324720972832300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "581a5931-714d-4718-b161-1e5826ca9327", "logId": "387d2a5d-450c-4481-b827-41b4271ebf57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "387d2a5d-450c-4481-b827-41b4271ebf57", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720972810500, "endTime": 324720972832300}, "additional": {"logType": "info", "children": [], "durationId": "a6be693f-8504-4e74-8213-9688490bf576", "parent": "fae317fb-7ea0-44b2-908b-4f3b9ecacdef"}}, {"head": {"id": "84f7f8ea-4067-48f2-a10f-fea90587c058", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720975619600, "endTime": 324720991058800}, "additional": {"children": ["20e9036c-c139-4b9b-908c-1a935a63834c", "23fcb4b4-9011-48fe-9bf8-09f692452782"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "581a5931-714d-4718-b161-1e5826ca9327", "logId": "e2550a27-0bc1-4007-a01d-251c7ac04fca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20e9036c-c139-4b9b-908c-1a935a63834c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720975621000, "endTime": 324720979575300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84f7f8ea-4067-48f2-a10f-fea90587c058", "logId": "6a8aab64-4a25-4264-a6cf-3b7490a4ca04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23fcb4b4-9011-48fe-9bf8-09f692452782", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720979590500, "endTime": 324720991044800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84f7f8ea-4067-48f2-a10f-fea90587c058", "logId": "0b8c66c6-3f7b-4f1d-8334-e0a6bc4d97b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5635edb3-85eb-4b0c-848d-341800e8b482", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720975626600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38b5be81-cdf9-43ff-a826-2c6bea37dc0e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720979429700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8aab64-4a25-4264-a6cf-3b7490a4ca04", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720975621000, "endTime": 324720979575300}, "additional": {"logType": "info", "children": [], "durationId": "20e9036c-c139-4b9b-908c-1a935a63834c", "parent": "e2550a27-0bc1-4007-a01d-251c7ac04fca"}}, {"head": {"id": "50aa1e72-1518-4ef6-89ea-3c64d0fdf49c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720979601200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ce7caa-7df3-4632-a769-8684a8076336", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720986239900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e8fa0b-f922-49b7-a0f8-2189da782825", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720986384800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c73acd8-1b05-47fc-823a-bb00cef28c62", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720987181300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1370bd6c-933f-4eb5-988c-5d5a40fa486b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720987327800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ce9bc2-22f6-49c1-abf6-11c450e5dac1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720987393700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6334dd1-9326-40f1-befd-c17cfd1bf06b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720987455800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df973cd3-71c5-4a85-ac95-f6a7b786eda5", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720987505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cc9f985-c667-44c3-b6a8-aa7823606094", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720990730100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d08bab-0f10-43ba-8f93-65a9935102e3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720990882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2841cfe4-0ce4-4eb9-9a4d-bf50c794d8d4", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720990949000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b70de3f-e1b7-494c-b077-d8aa3ee4549c", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720990998300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8c66c6-3f7b-4f1d-8334-e0a6bc4d97b1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720979590500, "endTime": 324720991044800}, "additional": {"logType": "info", "children": [], "durationId": "23fcb4b4-9011-48fe-9bf8-09f692452782", "parent": "e2550a27-0bc1-4007-a01d-251c7ac04fca"}}, {"head": {"id": "e2550a27-0bc1-4007-a01d-251c7ac04fca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720975619600, "endTime": 324720991058800}, "additional": {"logType": "info", "children": ["6a8aab64-4a25-4264-a6cf-3b7490a4ca04", "0b8c66c6-3f7b-4f1d-8334-e0a6bc4d97b1"], "durationId": "84f7f8ea-4067-48f2-a10f-fea90587c058", "parent": "fae317fb-7ea0-44b2-908b-4f3b9ecacdef"}}, {"head": {"id": "dae0517c-6c9f-47eb-8dca-11c6f2c69913", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720992644100, "endTime": 324720992655500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "581a5931-714d-4718-b161-1e5826ca9327", "logId": "1436a8c6-9786-40cb-930d-1d54af872440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1436a8c6-9786-40cb-930d-1d54af872440", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720992644100, "endTime": 324720992655500}, "additional": {"logType": "info", "children": [], "durationId": "dae0517c-6c9f-47eb-8dca-11c6f2c69913", "parent": "fae317fb-7ea0-44b2-908b-4f3b9ecacdef"}}, {"head": {"id": "fae317fb-7ea0-44b2-908b-4f3b9ecacdef", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720969022000, "endTime": 324720992668600}, "additional": {"logType": "info", "children": ["387d2a5d-450c-4481-b827-41b4271ebf57", "e2550a27-0bc1-4007-a01d-251c7ac04fca", "1436a8c6-9786-40cb-930d-1d54af872440"], "durationId": "581a5931-714d-4718-b161-1e5826ca9327", "parent": "a9c32c85-338e-47bb-802d-bfe8ea262368"}}, {"head": {"id": "a9c32c85-338e-47bb-802d-bfe8ea262368", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720967696700, "endTime": 324720992678900}, "additional": {"logType": "info", "children": ["fae317fb-7ea0-44b2-908b-4f3b9ecacdef"], "durationId": "f64c2f5f-9fc8-4da2-8eb3-1efcb5e58551", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "0f40a002-19ad-47f3-ad19-bf4ac744c66d", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721048574000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b054e336-9d0d-4d63-a87c-91d560a45bbf", "name": "hvigorfile, resolve hvigorfile dependencies in 57 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721048924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6085c245-3f1d-4154-9271-1a26b99019e9", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720992685500, "endTime": 324721049024000}, "additional": {"logType": "info", "children": [], "durationId": "f9e6bdc1-df26-4ba9-9c98-8b0472544efe", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "219ff31c-3456-4626-899e-5692067c2694", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721049887000, "endTime": 324721050041900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6b9c39-ae22-4432-9da2-61d75908211b", "logId": "f59c508c-e85f-4a88-9fab-79f03c1db2f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a21e8dbc-e175-4e1d-950e-d81590d61dc2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721049905700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f59c508c-e85f-4a88-9fab-79f03c1db2f6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721049887000, "endTime": 324721050041900}, "additional": {"logType": "info", "children": [], "durationId": "219ff31c-3456-4626-899e-5692067c2694", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "d62fd1f8-3f0e-44f5-8087-2526da115ff3", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721051430600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08abf9d-c070-495a-a9ea-485b16a902e5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721057277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f56ec0-ed4c-4634-9d25-cf75046bc25b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721050053200, "endTime": 324721058156500}, "additional": {"logType": "info", "children": [], "durationId": "1d7bc608-8f44-4ae7-a576-151d659a8a10", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "34c03afb-3ca2-4328-a8d1-68cb1287e2da", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721058187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8afd385-0870-4f6a-aac5-0aa6a148e560", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721064984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9cea8d8-df2a-4f63-88b5-46472d61a533", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721065107100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b9c85b-e732-47d1-9fb9-0cb41546a0cf", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721065330200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4fa27a4-a6ec-4a03-8778-ea092daa8971", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721067930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "910f9a86-77e0-4834-a7f9-a50a54149649", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721068013200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a05000-b259-41f4-9bb7-eca3f864ab23", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721058176400, "endTime": 324721070714700}, "additional": {"logType": "info", "children": [], "durationId": "1dbe9bcc-1ced-433b-8e9e-061c4fef2877", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "21764669-8016-4382-a095-e6fb99bb47c8", "name": "Configuration phase cost:220 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721070761400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "660798f8-933d-41b5-981f-5212e0d6daf5", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721070741000, "endTime": 324721070857300}, "additional": {"logType": "info", "children": [], "durationId": "03254afe-5f2a-4db1-a82f-01b0975a6993", "parent": "672b0338-d69d-4e75-95e7-da339689d768"}}, {"head": {"id": "672b0338-d69d-4e75-95e7-da339689d768", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720845780900, "endTime": 324721070866000}, "additional": {"logType": "info", "children": ["65ff8a08-be4d-4971-9be7-722433d9cf7a", "60c61048-c61e-4ec5-90be-ffc058fe6bdc", "2da9de8d-4743-4809-a5c2-687e26642748", "a9c32c85-338e-47bb-802d-bfe8ea262368", "6085c245-3f1d-4154-9271-1a26b99019e9", "11f56ec0-ed4c-4634-9d25-cf75046bc25b", "58a05000-b259-41f4-9bb7-eca3f864ab23", "660798f8-933d-41b5-981f-5212e0d6daf5", "f59c508c-e85f-4a88-9fab-79f03c1db2f6"], "durationId": "ed6b9c39-ae22-4432-9da2-61d75908211b", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "343a463a-3792-4a8d-8dfe-8724a2b082ec", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721072076700, "endTime": 324721072088700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a480bb1-279c-4734-8903-7af5f9b2f1a4", "logId": "cf0139bf-d1a6-4d6b-b917-8078f092f3cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf0139bf-d1a6-4d6b-b917-8078f092f3cf", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721072076700, "endTime": 324721072088700}, "additional": {"logType": "info", "children": [], "durationId": "343a463a-3792-4a8d-8dfe-8724a2b082ec", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "da963cbc-5168-43c6-9ac1-524d75a8f84b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721070885700, "endTime": 324721072098400}, "additional": {"logType": "info", "children": [], "durationId": "901b0376-318f-48ac-a3d1-6a603a91aa86", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "1b2581b9-3e2a-445b-8dd6-1baa220b2605", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721072102000, "endTime": 324721072103000}, "additional": {"logType": "info", "children": [], "durationId": "628e6e8e-4f5c-4d5c-9b54-d798b8bf79bf", "parent": "44b1278c-9786-4447-919a-23d2c5a90edf"}}, {"head": {"id": "44b1278c-9786-4447-919a-23d2c5a90edf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720833201000, "endTime": 324721072105400}, "additional": {"logType": "info", "children": ["b1e7fec0-62a8-4b49-a2c7-bcebd66beb08", "672b0338-d69d-4e75-95e7-da339689d768", "da963cbc-5168-43c6-9ac1-524d75a8f84b", "1b2581b9-3e2a-445b-8dd6-1baa220b2605", "d22a1191-8f65-40de-9385-0add0ebdd529", "f44c083a-56a6-4509-a2dd-5b8e7d0fe7e1", "cf0139bf-d1a6-4d6b-b917-8078f092f3cf"], "durationId": "2a480bb1-279c-4734-8903-7af5f9b2f1a4"}}, {"head": {"id": "7b790180-cda4-4e7d-b72c-fd7379163856", "name": "Configuration task cost before running: 243 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721072684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e6aeee7-8ae6-430b-9ee4-046815132549", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721079476800, "endTime": 324721090326500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9e5914ae-137f-4446-ace0-a17a9c7d1c0d", "logId": "3e0eeeeb-f924-40e7-94e2-9109a5b09c64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e5914ae-137f-4446-ace0-a17a9c7d1c0d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721074660800}, "additional": {"logType": "detail", "children": [], "durationId": "6e6aeee7-8ae6-430b-9ee4-046815132549"}}, {"head": {"id": "e721fae3-9b52-4059-a12d-9d01f4e69c37", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721075221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2dd087-1bd5-4a5d-9e1e-0109a0f7e556", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721075324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50a0ca8-5fb8-4ebc-a600-1be9cb1f5e1e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721079491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afc26b9b-8e74-4f3d-93d2-0b076480fe44", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721090072400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6fb427-d0ea-4470-97e6-ba13cfe6cc63", "name": "entry : default@PreBuild cost memory 0.26714324951171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721090250400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0eeeeb-f924-40e7-94e2-9109a5b09c64", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721079476800, "endTime": 324721090326500}, "additional": {"logType": "info", "children": [], "durationId": "6e6aeee7-8ae6-430b-9ee4-046815132549"}}, {"head": {"id": "94b0eb56-7840-4738-b721-c82acd56b76c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721096799900, "endTime": 324721099190500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "48bec597-c5a7-44ca-b50a-8d985778d64a", "logId": "7f650bd0-082c-478c-9b18-01903cefa985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48bec597-c5a7-44ca-b50a-8d985778d64a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721095336200}, "additional": {"logType": "detail", "children": [], "durationId": "94b0eb56-7840-4738-b721-c82acd56b76c"}}, {"head": {"id": "8af62a87-2d52-4185-8551-7eca29f08359", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721095945600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e56f7a5-c15f-4008-aba4-85a0327b9075", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721096055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd900a4-318b-4003-8c77-ee5bb0fd34b5", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721096808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40219c3d-1b2b-4a75-b3dd-7567c3720179", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721098975900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef4d9118-9bdc-4af5-ab78-a7e631f7d55e", "name": "entry : default@MergeProfile cost memory 0.11020660400390625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721099107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f650bd0-082c-478c-9b18-01903cefa985", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721096799900, "endTime": 324721099190500}, "additional": {"logType": "info", "children": [], "durationId": "94b0eb56-7840-4738-b721-c82acd56b76c"}}, {"head": {"id": "e09149bf-1522-4abe-ba56-572db6124448", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721102585000, "endTime": 324721105972200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6e36201a-8f36-40f5-9133-577745d06b97", "logId": "c592fb21-76fc-466e-8ce2-3a20a9cf5bfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e36201a-8f36-40f5-9133-577745d06b97", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721101001400}, "additional": {"logType": "detail", "children": [], "durationId": "e09149bf-1522-4abe-ba56-572db6124448"}}, {"head": {"id": "52d79c11-0051-447a-b898-4922fbf02c11", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721101589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53ea026b-8f8f-4589-ae5c-46b872bbdc1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721101696500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af222ff-6f2a-420f-95c5-e50be8ba5bfb", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721102595700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe19faa-d2ec-45dd-a36b-1dedbe297f2d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721103646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d9d7a6-af4b-422b-8512-d4afa7647291", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721105565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b0f6ba-9889-4630-b6eb-6eae9c2a8699", "name": "entry : default@CreateBuildProfile cost memory 0.0955810546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721105824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c592fb21-76fc-466e-8ce2-3a20a9cf5bfd", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721102585000, "endTime": 324721105972200}, "additional": {"logType": "info", "children": [], "durationId": "e09149bf-1522-4abe-ba56-572db6124448"}}, {"head": {"id": "4c4e4766-b959-408b-88a1-00ca8e9bf0c5", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109468300, "endTime": 324721109849600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9af866f0-911a-4b45-a739-2326ff2185d8", "logId": "e8297d01-1edf-47d5-ae65-33b4ca319e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9af866f0-911a-4b45-a739-2326ff2185d8", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721108073500}, "additional": {"logType": "detail", "children": [], "durationId": "4c4e4766-b959-408b-88a1-00ca8e9bf0c5"}}, {"head": {"id": "b411cca7-2f07-4f69-98f0-41e5be869fe9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721108588600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51580a15-b82b-4d48-8192-8176aced6d53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721108684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29b51c07-4aad-47bc-93d5-2fd76324cc69", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109478600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb15768-5051-4cc0-ac2d-f7b5443d76ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afdda457-b8f2-4414-8b57-24ca00a65cb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109640000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e387a55-f9a3-4f29-855c-19407b1dd74b", "name": "entry : default@PreCheckSyscap cost memory 0.036956787109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109719000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e766011-16a9-4966-91eb-99d7f8f9e426", "name": "runTaskFromQueue task cost before running: 280 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8297d01-1edf-47d5-ae65-33b4ca319e1c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721109468300, "endTime": 324721109849600, "totalTime": 311700}, "additional": {"logType": "info", "children": [], "durationId": "4c4e4766-b959-408b-88a1-00ca8e9bf0c5"}}, {"head": {"id": "20bfcda5-712a-4a38-ba10-84819a2997c4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721119646300, "endTime": 324721121053800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "707fedcc-4c84-45a5-981e-f781bbd47b8e", "logId": "02c8738c-3fae-4e06-ad8b-42c558264166"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "707fedcc-4c84-45a5-981e-f781bbd47b8e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721111613500}, "additional": {"logType": "detail", "children": [], "durationId": "20bfcda5-712a-4a38-ba10-84819a2997c4"}}, {"head": {"id": "b5b02ee7-34fd-414c-ae01-9ce32a75f364", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721112165900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58301c30-6b0b-457b-97d8-cc05151f72fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721112265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73184e0-a231-4099-9a63-f3b8814211f4", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721119663200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d554b81b-a61b-4fdf-90d8-10c9d7a76f83", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721119919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d3c6bc-f3dd-497d-b630-9c59f148ae13", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721120834700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1742a1-5bea-44eb-b69d-6c798c402387", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06517791748046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721120959400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c8738c-3fae-4e06-ad8b-42c558264166", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721119646300, "endTime": 324721121053800}, "additional": {"logType": "info", "children": [], "durationId": "20bfcda5-712a-4a38-ba10-84819a2997c4"}}, {"head": {"id": "e8f6dabe-62e6-42c1-a336-3ff5f334fb6a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721126596600, "endTime": 324721128949100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0996e600-9655-4e19-80eb-d839d9a58ae5", "logId": "ba4c753c-8ff0-4355-958f-704f1451e2f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0996e600-9655-4e19-80eb-d839d9a58ae5", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721123893700}, "additional": {"logType": "detail", "children": [], "durationId": "e8f6dabe-62e6-42c1-a336-3ff5f334fb6a"}}, {"head": {"id": "e616a570-2249-4661-9596-a073a3b4b831", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721124666200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2964ddb6-b6ab-4839-b40c-8ac723d34bcb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721124797800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b973d2d4-6aba-4096-87a6-9c4936cb66e5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721126618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0258d6ab-ecd8-4e28-bab6-dc4c143ac414", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721128635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd9bba7-e376-488f-a44a-f43f72e783be", "name": "entry : default@ProcessProfile cost memory 0.05649566650390625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721128822300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba4c753c-8ff0-4355-958f-704f1451e2f7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721126596600, "endTime": 324721128949100}, "additional": {"logType": "info", "children": [], "durationId": "e8f6dabe-62e6-42c1-a336-3ff5f334fb6a"}}, {"head": {"id": "ad7369b3-7be3-4e70-8211-09b476dadf87", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721134905100, "endTime": 324721142942200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "99b73d04-0352-4dd7-9c55-3804d2424559", "logId": "8fe72118-3129-47b8-a4a5-dc733ff2d211"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99b73d04-0352-4dd7-9c55-3804d2424559", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721131443600}, "additional": {"logType": "detail", "children": [], "durationId": "ad7369b3-7be3-4e70-8211-09b476dadf87"}}, {"head": {"id": "66fbf9ed-8315-4c79-bc23-60658f52bab2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721132137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1626c5db-f45e-4777-9068-060904fd2a3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721132251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b0741d-2d15-4154-8303-8438acdd0cf7", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721134915700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dd59c52-cfb8-45df-87b5-5a6138a91afe", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721142745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0c3c89-c014-4fb9-82bf-1d6170ab65e6", "name": "entry : default@ProcessRouterMap cost memory 0.1868896484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721142872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe72118-3129-47b8-a4a5-dc733ff2d211", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721134905100, "endTime": 324721142942200}, "additional": {"logType": "info", "children": [], "durationId": "ad7369b3-7be3-4e70-8211-09b476dadf87"}}, {"head": {"id": "ac2df873-721b-4d51-8a92-115c94edcb20", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721149459000, "endTime": 324721152228100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9cdde808-dd2c-470a-9d44-0ba03ffb1eb3", "logId": "896c5ced-8a78-4d7f-a314-1844166fe4cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cdde808-dd2c-470a-9d44-0ba03ffb1eb3", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721145818600}, "additional": {"logType": "detail", "children": [], "durationId": "ac2df873-721b-4d51-8a92-115c94edcb20"}}, {"head": {"id": "337d8195-1b2a-428c-a42f-2d9388e29199", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721146321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5c0649-a6e0-4a1a-89f4-8e9c9c803a7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721146390700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6528bff-6d01-4f86-ab3b-daaf0e320efe", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721147325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5916a8fe-4971-49c5-a7ef-5080af0f4c13", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721150627500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33114e62-5cdb-47dd-99c7-e3bf6c88c930", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721150758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44249229-0b54-4974-ba90-2ecbaab9d647", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721150813700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d438d8ea-46b9-4d9c-b5d4-f86eae940f99", "name": "entry : default@PreviewProcessResource cost memory 0.06789398193359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721150884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75f5b90-abd0-4cb1-a606-be49c1e67371", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721152143800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "896c5ced-8a78-4d7f-a314-1844166fe4cb", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721149459000, "endTime": 324721152228100, "totalTime": 1484600}, "additional": {"logType": "info", "children": [], "durationId": "ac2df873-721b-4d51-8a92-115c94edcb20"}}, {"head": {"id": "fcbf0b4d-d72c-4b21-9280-a0264c9b2157", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721162034700, "endTime": 324721185042700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e11b32b8-c69d-4292-8ed8-6f32835a236e", "logId": "5ef8cd00-ed71-444c-9b15-f91bd73678e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e11b32b8-c69d-4292-8ed8-6f32835a236e", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721155220700}, "additional": {"logType": "detail", "children": [], "durationId": "fcbf0b4d-d72c-4b21-9280-a0264c9b2157"}}, {"head": {"id": "68662bcc-3d2a-4e13-ae4c-30811873be2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721155883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df3bbc6-03f7-4f98-bf7e-6ab5824e7dd0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721156299300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbbf6fbe-2c14-469c-b375-847052feffb3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721162053900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34af42c8-56be-4773-9183-897a48f0dd9c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721184712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8de7b0-680b-4d7c-ad14-544d8c3b671e", "name": "entry : default@GenerateLoaderJson cost memory -0.9962234497070312", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721184910900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef8cd00-ed71-444c-9b15-f91bd73678e7", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721162034700, "endTime": 324721185042700}, "additional": {"logType": "info", "children": [], "durationId": "fcbf0b4d-d72c-4b21-9280-a0264c9b2157"}}, {"head": {"id": "feade572-457f-4eb8-b2d4-4b61cccebce8", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721200283800, "endTime": 324721224138300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dc38eb57-39e2-4a58-8cf3-9a3972b6c827", "logId": "43d57f7e-ed3c-4ea5-b61b-4773096befdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc38eb57-39e2-4a58-8cf3-9a3972b6c827", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721196260400}, "additional": {"logType": "detail", "children": [], "durationId": "feade572-457f-4eb8-b2d4-4b61cccebce8"}}, {"head": {"id": "41d82874-d8c0-44f4-8b9e-09946c7fa3e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721196814300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a2a34c1-c6ac-41bc-a6ac-1be76c1f99fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721196950400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885f07cc-29ea-49d6-84c9-77e15def31b2", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721197972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7451548e-4f4c-44d3-96c3-5e59f296d032", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721200306600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be90b8b-d582-4ac6-b813-c4b41dc559f6", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721223904200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac32f55-430e-4a32-8eb8-7b8cd7aa9cde", "name": "entry : default@PreviewCompileResource cost memory 0.7034072875976562", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721224046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d57f7e-ed3c-4ea5-b61b-4773096befdb", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721200283800, "endTime": 324721224138300}, "additional": {"logType": "info", "children": [], "durationId": "feade572-457f-4eb8-b2d4-4b61cccebce8"}}, {"head": {"id": "3b512eac-7821-4c10-9f5a-7713d61c2eec", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229792200, "endTime": 324721230267800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2ce6a840-affb-44ee-8bdc-c2ff64b1fefb", "logId": "d1095af8-b1b8-4574-8f6a-420d11779165"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ce6a840-affb-44ee-8bdc-c2ff64b1fefb", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721228775300}, "additional": {"logType": "detail", "children": [], "durationId": "3b512eac-7821-4c10-9f5a-7713d61c2eec"}}, {"head": {"id": "75863689-e577-4ad3-8209-27b49e99b8cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e27f483-8348-4638-a0b6-56a2855f8fe2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229646600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9277ab4-81c5-4b92-9636-c501682164f5", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e74bb94d-8473-4806-b427-540a18c392ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229927200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04bbc80-33b1-4e72-97d8-6578148604d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f927ebdc-d140-4509-b7cd-2a6d460835c0", "name": "entry : default@PreviewHookCompileResource cost memory 0.03807830810546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721230081100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e4bd09-eb15-407a-bef9-5ce0fc44ea14", "name": "runTaskFromQueue task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721230204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1095af8-b1b8-4574-8f6a-420d11779165", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721229792200, "endTime": 324721230267800, "totalTime": 395100}, "additional": {"logType": "info", "children": [], "durationId": "3b512eac-7821-4c10-9f5a-7713d61c2eec"}}, {"head": {"id": "73f339ab-148d-419b-a98d-c4f3a40ba8a0", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721233359000, "endTime": 324721236179000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e0190e0c-3f31-45b0-a602-9c6f6d9b8226", "logId": "14b76889-521d-4df0-991a-3d48dd820e4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0190e0c-3f31-45b0-a602-9c6f6d9b8226", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721232101000}, "additional": {"logType": "detail", "children": [], "durationId": "73f339ab-148d-419b-a98d-c4f3a40ba8a0"}}, {"head": {"id": "41a5ee68-2b80-4c58-961a-24a34966def6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721232639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1013607c-6312-4e45-994f-230d8c314e99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721232726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e97ffb8-3251-4e54-91d8-032e4cbc54d8", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721233366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0ebc38-6581-433f-af61-467b16310b73", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721235999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ab26c3-94fb-4100-aed1-57c6a047b6cd", "name": "entry : default@CopyPreviewProfile cost memory 0.09503936767578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721236114300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b76889-521d-4df0-991a-3d48dd820e4a", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721233359000, "endTime": 324721236179000}, "additional": {"logType": "info", "children": [], "durationId": "73f339ab-148d-419b-a98d-c4f3a40ba8a0"}}, {"head": {"id": "cdfa6ae8-c317-4523-9812-4895a7292ab8", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721239996200, "endTime": 324721240442100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "24027c6c-2ade-4434-aa5a-24ca6fdf6dc0", "logId": "fe970057-04d2-4704-9c7b-08c90adb1ef4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24027c6c-2ade-4434-aa5a-24ca6fdf6dc0", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721237917500}, "additional": {"logType": "detail", "children": [], "durationId": "cdfa6ae8-c317-4523-9812-4895a7292ab8"}}, {"head": {"id": "ca59baa7-db84-4027-ae60-5701c0cc6164", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721238637800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb160dd-fbd6-4a6c-bb9d-22fd3ce65b2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721238755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e4c8b5-d31c-4c61-89ae-dbc922543217", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721240008200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf04bbe6-43ff-4c53-baa9-56cf911ef9fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721240148000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e327db51-5939-4041-ba88-64c9d1e86fcd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721240208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "629125f0-240c-47ca-b02c-e6f49c0a1cc6", "name": "entry : default@ReplacePreviewerPage cost memory 0.03801727294921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721240296300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b46fff0-31db-400c-9f40-a14d87068015", "name": "runTaskFromQueue task cost before running: 411 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721240390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe970057-04d2-4704-9c7b-08c90adb1ef4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721239996200, "endTime": 324721240442100, "totalTime": 377500}, "additional": {"logType": "info", "children": [], "durationId": "cdfa6ae8-c317-4523-9812-4895a7292ab8"}}, {"head": {"id": "c35df56e-ce1b-447c-9a9c-e7426d504f9f", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242052500, "endTime": 324721242297300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c86109e0-b875-4c8e-9af4-2da3be475fdd", "logId": "cd0407d5-19fd-48bc-b4b4-b42f3f413c94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c86109e0-b875-4c8e-9af4-2da3be475fdd", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242021200}, "additional": {"logType": "detail", "children": [], "durationId": "c35df56e-ce1b-447c-9a9c-e7426d504f9f"}}, {"head": {"id": "98337e1f-a124-4978-be54-0032a0faec2c", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242059300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9254b0-d168-4129-bf8e-a84da1325057", "name": "entry : buildPreviewerResource cost memory 0.01169586181640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d4fa258-a625-48e8-b8c5-0b9389bf9149", "name": "runTaskFromQueue task cost before running: 413 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242246300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd0407d5-19fd-48bc-b4b4-b42f3f413c94", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721242052500, "endTime": 324721242297300, "totalTime": 179600}, "additional": {"logType": "info", "children": [], "durationId": "c35df56e-ce1b-447c-9a9c-e7426d504f9f"}}, {"head": {"id": "5bae4c29-18f0-4b69-990c-7cfe398003b7", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721245195500, "endTime": 324721247934900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "aa2efeb7-512e-48a3-b784-e24707d5083a", "logId": "99e049ec-7aa2-4ad1-9f86-5947a870e4a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa2efeb7-512e-48a3-b784-e24707d5083a", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721243819000}, "additional": {"logType": "detail", "children": [], "durationId": "5bae4c29-18f0-4b69-990c-7cfe398003b7"}}, {"head": {"id": "9f669fcd-9856-4c1a-9e91-cf51dc359ce3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721244347500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113631c5-e327-44df-a00f-6ed567381a5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721244430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd03e62-df53-46be-afe5-5e1667a7fe16", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721245203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7b8760-97a4-4f75-b896-cb7d14eaa542", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721247658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8553f4-ec80-4ccd-bab2-c0f2a6e6124f", "name": "entry : default@PreviewUpdateAssets cost memory 0.1025848388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721247853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e049ec-7aa2-4ad1-9f86-5947a870e4a8", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721245195500, "endTime": 324721247934900}, "additional": {"logType": "info", "children": [], "durationId": "5bae4c29-18f0-4b69-990c-7cfe398003b7"}}, {"head": {"id": "6b2c1a6f-d0c0-4509-b23d-64e389ad34d6", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721260282200, "endTime": 324740616596700}, "additional": {"children": ["a12cc54b-fe24-47bf-a43b-d4291ab27a96"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5377ed3d-5773-4318-a1b1-2fdd46e5a248", "logId": "01939524-a732-49be-9d55-5c554495dc9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5377ed3d-5773-4318-a1b1-2fdd46e5a248", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721251103000}, "additional": {"logType": "detail", "children": [], "durationId": "6b2c1a6f-d0c0-4509-b23d-64e389ad34d6"}}, {"head": {"id": "b5a38932-2ea4-4809-a397-0899b542ee57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721251835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d0ad63d-9433-46a3-81cb-13b41560471f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721251953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74960d0f-bf9a-421f-9a56-516fd71c8d5b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721260300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324721285015800, "endTime": 324740606710000}, "additional": {"children": ["ae1c46b1-d1f8-4eb9-8fe6-57b4c3d46627", "0342e29e-0c66-498d-ae84-f4441bbcd284", "b7921ded-72ed-415c-a966-7ae2062f9aa1", "4bec1794-db90-4568-b6d9-a788d3df9142", "2d7b5261-114d-4fc6-a53c-2772916d396a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "6b2c1a6f-d0c0-4509-b23d-64e389ad34d6", "logId": "abce3e6b-fc06-4951-831b-5968fcf03307"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b21ce7d1-e2e4-4cc4-b4d2-2bb641c44f4b", "name": "entry : default@PreviewArkTS cost memory -0.8275680541992188", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721287865600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ec7a8d-5b14-4c67-817d-94af3bc0c4d9", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324727683219200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1c46b1-d1f8-4eb9-8fe6-57b4c3d46627", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324727684337100, "endTime": 324727684352400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "logId": "df6b3e53-5f98-4e65-ba34-eb0c02983b07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df6b3e53-5f98-4e65-ba34-eb0c02983b07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324727684337100, "endTime": 324727684352400}, "additional": {"logType": "info", "children": [], "durationId": "ae1c46b1-d1f8-4eb9-8fe6-57b4c3d46627", "parent": "abce3e6b-fc06-4951-831b-5968fcf03307"}}, {"head": {"id": "3862a0ef-0a6d-4925-8473-49e2a2d9e6dc", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740605408800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0342e29e-0c66-498d-ae84-f4441bbcd284", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324740606541400, "endTime": 324740606556600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "logId": "ef85054e-9999-4a1d-bd2b-5f73f39fce64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef85054e-9999-4a1d-bd2b-5f73f39fce64", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740606541400, "endTime": 324740606556600}, "additional": {"logType": "info", "children": [], "durationId": "0342e29e-0c66-498d-ae84-f4441bbcd284", "parent": "abce3e6b-fc06-4951-831b-5968fcf03307"}}, {"head": {"id": "abce3e6b-fc06-4951-831b-5968fcf03307", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324721285015800, "endTime": 324740606710000}, "additional": {"logType": "info", "children": ["df6b3e53-5f98-4e65-ba34-eb0c02983b07", "ef85054e-9999-4a1d-bd2b-5f73f39fce64", "ac3639cd-eac3-4603-ba24-c776278b43a5", "c58ec244-4ace-4541-b8b7-e7d3b9ab3f1d", "5d9dc4c2-e6e6-403a-9696-8865be5a168e"], "durationId": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "parent": "01939524-a732-49be-9d55-5c554495dc9c"}}, {"head": {"id": "b7921ded-72ed-415c-a966-7ae2062f9aa1", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324725894538600, "endTime": 324727623975500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "logId": "ac3639cd-eac3-4603-ba24-c776278b43a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac3639cd-eac3-4603-ba24-c776278b43a5", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324725894538600, "endTime": 324727623975500}, "additional": {"logType": "info", "children": [], "durationId": "b7921ded-72ed-415c-a966-7ae2062f9aa1", "parent": "abce3e6b-fc06-4951-831b-5968fcf03307"}}, {"head": {"id": "4bec1794-db90-4568-b6d9-a788d3df9142", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324727624204100, "endTime": 324727624367600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "logId": "c58ec244-4ace-4541-b8b7-e7d3b9ab3f1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c58ec244-4ace-4541-b8b7-e7d3b9ab3f1d", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324727624204100, "endTime": 324727624367600}, "additional": {"logType": "info", "children": [], "durationId": "4bec1794-db90-4568-b6d9-a788d3df9142", "parent": "abce3e6b-fc06-4951-831b-5968fcf03307"}}, {"head": {"id": "2d7b5261-114d-4fc6-a53c-2772916d396a", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker15", "startTime": 324727624500400, "endTime": 324740605349000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a12cc54b-fe24-47bf-a43b-d4291ab27a96", "logId": "5d9dc4c2-e6e6-403a-9696-8865be5a168e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d9dc4c2-e6e6-403a-9696-8865be5a168e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324727624500400, "endTime": 324740605349000}, "additional": {"logType": "info", "children": [], "durationId": "2d7b5261-114d-4fc6-a53c-2772916d396a", "parent": "abce3e6b-fc06-4951-831b-5968fcf03307"}}, {"head": {"id": "01939524-a732-49be-9d55-5c554495dc9c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324721260282200, "endTime": 324740616596700, "totalTime": 19356288900}, "additional": {"logType": "info", "children": ["abce3e6b-fc06-4951-831b-5968fcf03307"], "durationId": "6b2c1a6f-d0c0-4509-b23d-64e389ad34d6"}}, {"head": {"id": "f1a8b86e-8c8e-4064-8e2d-c04b528456d0", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622222300, "endTime": 324740622516600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ca5e74c6-2d71-467e-98b8-9f237fc3c930", "logId": "0fcf3155-2b7e-455e-bd07-151b8f652ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca5e74c6-2d71-467e-98b8-9f237fc3c930", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622162800}, "additional": {"logType": "detail", "children": [], "durationId": "f1a8b86e-8c8e-4064-8e2d-c04b528456d0"}}, {"head": {"id": "40456b35-a776-4681-8269-0205b6d3d539", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622233700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b773b574-63f7-4e00-84a3-8b7e280e68d8", "name": "entry : PreviewBuild cost memory 0.011566162109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21bbd40-5fcf-44e4-af04-346485be0966", "name": "runTaskFromQueue task cost before running: 19 s 793 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcf3155-2b7e-455e-bd07-151b8f652ccf", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740622222300, "endTime": 324740622516600, "totalTime": 214800}, "additional": {"logType": "info", "children": [], "durationId": "f1a8b86e-8c8e-4064-8e2d-c04b528456d0"}}, {"head": {"id": "1e8c169c-4c52-4812-a93b-6ccab2a94a18", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635424600, "endTime": 324740635443000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "623b47c6-522d-45f0-8602-b84e8fe3a361", "logId": "0079b089-3f2d-4879-b147-29e55ee91883"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0079b089-3f2d-4879-b147-29e55ee91883", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635424600, "endTime": 324740635443000}, "additional": {"logType": "info", "children": [], "durationId": "1e8c169c-4c52-4812-a93b-6ccab2a94a18"}}, {"head": {"id": "77c91ae7-2715-4c46-ac8f-a9e53ae100ec", "name": "BUILD SUCCESSFUL in 19 s 806 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635545100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "c7700256-ab76-420e-b54a-0c4a5b855e97", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324720829974100, "endTime": 324740635829700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 13, "minute": 59}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "fa30baee-bc48-431d-9c9a-89d83e3376a3", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea797df-9820-4c25-82fc-6f1041a1eec1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb8117d-a9ce-44e4-918d-c9fc83a94e92", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740635989800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd8fe2d5-2d3f-420c-b678-4e28ff37b997", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636038200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb1e802-b9f6-4fb0-8b6d-e0a23996a2b9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730137df-fbc8-4c56-b780-4db2fb272b32", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636129100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87e7b885-fe9a-4519-9bb0-3445efcd93ee", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636175500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b047db4b-5b62-4af4-99d9-6a03fa5a9a48", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa20d0ea-715e-4cf0-a2c6-dcb29a1082ae", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636287000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd03a9f-3328-4ac6-b63b-a6327d65a7b3", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740636332000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35a6d2bd-2e25-4517-8cbb-3c35a6514419", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740639711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2bb75c8-775f-4ade-835f-fd35f8ed0ac1", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740640917000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a197964-f096-4868-b84c-b070cd34a1f4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740641305800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf54f91e-8d66-4b45-856f-cd9b950b9ea6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740641641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7aa42a6-130a-4375-a55e-2cc521a506ac", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740642606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8901994b-b15a-424d-979c-9c7305705819", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740653946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddbe56d4-db78-4fe3-8e40-f16574c0b154", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740654301000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142aefa5-68bd-4f40-905c-c9dab0d35d31", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740654609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0d4591-6408-45a8-a093-6c5504f83aa1", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740654981700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad8b1ec-e251-4156-80bd-730e3bfbdce5", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 324740655278900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}