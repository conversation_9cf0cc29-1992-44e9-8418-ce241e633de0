{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "7f5abf54-dd16-4fea-8056-4cc48e3f1d5f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11664499559200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3960c42a-187e-49df-a98b-8d00db4766ce", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11876583038500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0f20e3-8360-4044-980f-876008051ae2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11876659605300, "endTime": 11876659676200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "81c2d28e-053d-4220-9b21-4f9912e72888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81c2d28e-053d-4220-9b21-4f9912e72888", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11876659605300, "endTime": 11876659676200}, "additional": {"logType": "info", "children": [], "durationId": "6a0f20e3-8360-4044-980f-876008051ae2"}}, {"head": {"id": "51d61d2b-c839-42bc-932b-3012ce439fe3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881347896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72b2c3dc-e68d-4959-838e-77f62a4f5aa7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881363868400, "endTime": 11881363909400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "071ae948-1158-4877-929a-2320f7cd2291"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "071ae948-1158-4877-929a-2320f7cd2291", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881363868400, "endTime": 11881363909400}, "additional": {"logType": "info", "children": [], "durationId": "72b2c3dc-e68d-4959-838e-77f62a4f5aa7"}}, {"head": {"id": "216519fe-9fdf-4acb-b9df-affc8bf81a89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881364067100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdd82cb-8064-4499-88ef-88eb9556c82b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881365848000, "endTime": 11881365875200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "629d2e9a-d72b-412d-b74f-f302ce2afd08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "629d2e9a-d72b-412d-b74f-f302ce2afd08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881365848000, "endTime": 11881365875200}, "additional": {"logType": "info", "children": [], "durationId": "ffdd82cb-8064-4499-88ef-88eb9556c82b"}}, {"head": {"id": "27d8970e-ee2d-4dd6-8b87-ea7964087e04", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881366010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3cfcc54-00c3-466b-87c5-1d09a1292576", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881367758100, "endTime": 11881367785200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "4ecdc661-0c5c-4422-9b5b-faf40eeaac90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ecdc661-0c5c-4422-9b5b-faf40eeaac90", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881367758100, "endTime": 11881367785200}, "additional": {"logType": "info", "children": [], "durationId": "c3cfcc54-00c3-466b-87c5-1d09a1292576"}}, {"head": {"id": "a48a4f8c-1082-4d21-98b6-6aab8ecc6177", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881367920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b93b02-d591-4778-b57b-0ef02b95e1f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881369642500, "endTime": 11881369682600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "9aa8f642-ad01-4841-a09a-d4e32faa3566"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aa8f642-ad01-4841-a09a-d4e32faa3566", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11881369642500, "endTime": 11881369682600}, "additional": {"logType": "info", "children": [], "durationId": "78b93b02-d591-4778-b57b-0ef02b95e1f8"}}, {"head": {"id": "38dba550-46a0-46b1-b143-96883608c6b3", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11884038299000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b772759f-8af5-43d1-9ff0-58633ad98e0c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11884039459700, "endTime": 11884039499400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "7947fdfa-6149-44af-9ac9-8b87d15ef17b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7947fdfa-6149-44af-9ac9-8b87d15ef17b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11884039459700, "endTime": 11884039499400}, "additional": {"logType": "info", "children": [], "durationId": "b772759f-8af5-43d1-9ff0-58633ad98e0c"}}, {"head": {"id": "628bbf0a-d800-4938-be04-f3c45914b550", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11901003903500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c4bbcd-91dc-4065-8831-0da28f727bd2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11901005015200, "endTime": 11901005034900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "dc9791b0-75b2-456b-8ccd-f7fe3b7a1e2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc9791b0-75b2-456b-8ccd-f7fe3b7a1e2f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11901005015200, "endTime": 11901005034900}, "additional": {"logType": "info", "children": [], "durationId": "f8c4bbcd-91dc-4065-8831-0da28f727bd2"}}, {"head": {"id": "053af8d6-b4f5-4ee0-b13d-365c7c28ba76", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903011084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd09fb44-4004-4e8a-be32-82f8a4aa7830", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903012503600, "endTime": 11903012537100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "ddfccc26-32a0-4671-bb40-1f70552c8c7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddfccc26-32a0-4671-bb40-1f70552c8c7f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903012503600, "endTime": 11903012537100}, "additional": {"logType": "info", "children": [], "durationId": "dd09fb44-4004-4e8a-be32-82f8a4aa7830"}}, {"head": {"id": "885794eb-35dd-4242-b3b4-9550ec33a9ea", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903012679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73bead9e-8062-433f-b281-6673853bc5c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903014336100, "endTime": 11903014374400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "a8b3e039-2169-4dea-a224-2209c4e8e0b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8b3e039-2169-4dea-a224-2209c4e8e0b0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903014336100, "endTime": 11903014374400}, "additional": {"logType": "info", "children": [], "durationId": "73bead9e-8062-433f-b281-6673853bc5c5"}}, {"head": {"id": "bc8e1283-56de-4157-a527-a567da0dc2cf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903014548400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bd416f-4008-4d89-b20e-5f429681df8a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903016800100, "endTime": 11903017136800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "9639921d-f430-4c93-ae4a-d82ee1dbcec1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9639921d-f430-4c93-ae4a-d82ee1dbcec1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903016800100, "endTime": 11903017136800}, "additional": {"logType": "info", "children": [], "durationId": "b4bd416f-4008-4d89-b20e-5f429681df8a"}}, {"head": {"id": "7dcf8b3e-5c33-49be-91cc-ef5a761224bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903017305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4109e223-3b28-4cc9-b501-833ce00d812d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903020655000, "endTime": 11903020700600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "83f29cdc-2c74-42bc-8a1d-0cb197186bb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83f29cdc-2c74-42bc-8a1d-0cb197186bb6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903020655000, "endTime": 11903020700600}, "additional": {"logType": "info", "children": [], "durationId": "4109e223-3b28-4cc9-b501-833ce00d812d"}}, {"head": {"id": "24cf65b2-fcde-4854-897b-137ef81a32bf", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903944411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3677bda9-0538-4aae-a285-ca9152677226", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903946889500, "endTime": 11903946935200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "2ff27d90-01ee-4190-a020-7b344c92a914"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ff27d90-01ee-4190-a020-7b344c92a914", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11903946889500, "endTime": 11903946935200}, "additional": {"logType": "info", "children": [], "durationId": "3677bda9-0538-4aae-a285-ca9152677226"}}, {"head": {"id": "245940bd-9503-472b-bf66-976489ae5766", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11918261307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7592ca-886c-48dc-8d67-2fc55e34fb37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11918264281400, "endTime": 11918264328400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "7cde3fc2-2e22-4e07-9eef-884f7d4cae7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cde3fc2-2e22-4e07-9eef-884f7d4cae7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11918264281400, "endTime": 11918264328400}, "additional": {"logType": "info", "children": [], "durationId": "fa7592ca-886c-48dc-8d67-2fc55e34fb37"}}, {"head": {"id": "710f7ce9-1ed2-469a-bd58-9f5ae75a8fd5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920176511500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ec09fd9-a6d3-4773-a4b4-f70aaf36e846", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920177857500, "endTime": 11920177879500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "336b7d72-2995-4ea5-9b0b-c8e77e542381"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "336b7d72-2995-4ea5-9b0b-c8e77e542381", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920177857500, "endTime": 11920177879500}, "additional": {"logType": "info", "children": [], "durationId": "2ec09fd9-a6d3-4773-a4b4-f70aaf36e846"}}, {"head": {"id": "0c325752-376a-4a21-8119-cd2767dde29c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920177985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c2b1c29-5118-4901-83ae-8c0de92c2685", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920181684600, "endTime": 11920181711700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "407b6058-c545-42e1-910e-8f1d9631c3a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "407b6058-c545-42e1-910e-8f1d9631c3a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920181684600, "endTime": 11920181711700}, "additional": {"logType": "info", "children": [], "durationId": "4c2b1c29-5118-4901-83ae-8c0de92c2685"}}, {"head": {"id": "c7c89d23-2bd9-4ef4-9602-95b983bd347c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920181909900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e8ee99-fa85-483d-94eb-b830a77ce5c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920216369400, "endTime": 11920216413100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "9fba75fe-5a25-44ac-acf1-45798e20590e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fba75fe-5a25-44ac-acf1-45798e20590e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920216369400, "endTime": 11920216413100}, "additional": {"logType": "info", "children": [], "durationId": "13e8ee99-fa85-483d-94eb-b830a77ce5c7"}}, {"head": {"id": "8e7ce06b-a0a6-45ae-bdf8-3293c8024a7e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920216628000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51dc6c7c-0e62-4d22-8e14-ebeff66d0aea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920218306700, "endTime": 11920218330800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "9d8046fa-5358-485a-9874-7af33c4836ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d8046fa-5358-485a-9874-7af33c4836ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920218306700, "endTime": 11920218330800}, "additional": {"logType": "info", "children": [], "durationId": "51dc6c7c-0e62-4d22-8e14-ebeff66d0aea"}}, {"head": {"id": "504f090a-d622-4ed6-ad6c-d519be0c00b4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920617085500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1466f783-0aae-4089-a20b-f5d839416a1c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920618686900, "endTime": 11920618715700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "6500b849-4f53-4c46-8c46-d1eafa55371a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6500b849-4f53-4c46-8c46-d1eafa55371a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11920618686900, "endTime": 11920618715700}, "additional": {"logType": "info", "children": [], "durationId": "1466f783-0aae-4089-a20b-f5d839416a1c"}}, {"head": {"id": "a76ef009-39d1-4e35-b746-492a06fdc597", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11933078197700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c51717c-0c66-48cc-a478-ac77f76e5414", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11933080977200, "endTime": 11933081031300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "631aa03a-bb8c-44ae-9a83-85ab240b9f33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "631aa03a-bb8c-44ae-9a83-85ab240b9f33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11933080977200, "endTime": 11933081031300}, "additional": {"logType": "info", "children": [], "durationId": "0c51717c-0c66-48cc-a478-ac77f76e5414"}}, {"head": {"id": "5f9a8af5-e513-49d9-9834-b5d5e36ed194", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934837791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ebda43-6940-47a2-bf2d-ae9ba5aeed05", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934858099800, "endTime": 11934858208000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "245af5bc-529d-458d-b9fb-77ac220ac575"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "245af5bc-529d-458d-b9fb-77ac220ac575", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934858099800, "endTime": 11934858208000}, "additional": {"logType": "info", "children": [], "durationId": "97ebda43-6940-47a2-bf2d-ae9ba5aeed05"}}, {"head": {"id": "2eeacbc5-acd2-44bf-886d-5fbe067bfcd9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934860601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4fdefa-d9ee-4c7c-88e1-2de705a27880", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934862628200, "endTime": 11934862657600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "0722ab0a-d4a6-48d7-b6f2-175aeb0572c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0722ab0a-d4a6-48d7-b6f2-175aeb0572c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934862628200, "endTime": 11934862657600}, "additional": {"logType": "info", "children": [], "durationId": "8c4fdefa-d9ee-4c7c-88e1-2de705a27880"}}, {"head": {"id": "ad757a23-f783-4e58-8e36-2bd5964aa384", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934862818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e0ff01-77d0-4257-ab42-dccd1608f355", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934881326900, "endTime": 11934881354400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "ecb0a725-dbae-4d0b-94b5-46c5fe6fe0e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecb0a725-dbae-4d0b-94b5-46c5fe6fe0e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934881326900, "endTime": 11934881354400}, "additional": {"logType": "info", "children": [], "durationId": "63e0ff01-77d0-4257-ab42-dccd1608f355"}}, {"head": {"id": "55e5fc16-f726-4a42-ad75-58bb07a73f2e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934881504700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26fe91b-315c-4321-a5e7-5a36c6696b6f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934882917300, "endTime": 11934882945800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "4e5d8b4d-1476-43c8-b856-e9ac84a5c0a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e5d8b4d-1476-43c8-b856-e9ac84a5c0a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11934882917300, "endTime": 11934882945800}, "additional": {"logType": "info", "children": [], "durationId": "d26fe91b-315c-4321-a5e7-5a36c6696b6f"}}, {"head": {"id": "a4aa0e40-f76d-4e0f-8a8f-d205519c9654", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11935256650200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e90da2-c2fb-4e8d-b628-dcba5c090a3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11935264024700, "endTime": 11935264054800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "d492740f-8afb-44d0-a8b2-e5a1be2179ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d492740f-8afb-44d0-a8b2-e5a1be2179ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11935264024700, "endTime": 11935264054800}, "additional": {"logType": "info", "children": [], "durationId": "92e90da2-c2fb-4e8d-b628-dcba5c090a3a"}}, {"head": {"id": "3b1eda99-8eb3-4f40-9a40-9d2277d9c67e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11949272142200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acb40ee-f7fc-4936-8cf9-c2d19153c25b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11949273954600, "endTime": 11949273984300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "e0f784c2-18e7-4c72-ae61-2ab1d0194df4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0f784c2-18e7-4c72-ae61-2ab1d0194df4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11949273954600, "endTime": 11949273984300}, "additional": {"logType": "info", "children": [], "durationId": "2acb40ee-f7fc-4936-8cf9-c2d19153c25b"}}, {"head": {"id": "90a25539-4d94-4bc2-af78-ff8a730a1d7d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950293248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a02aa005-468a-4397-800c-3cff1459b88d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950294350200, "endTime": 11950294370900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "0e229efb-8474-4ece-b0b1-1b40483f7acc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e229efb-8474-4ece-b0b1-1b40483f7acc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950294350200, "endTime": 11950294370900}, "additional": {"logType": "info", "children": [], "durationId": "a02aa005-468a-4397-800c-3cff1459b88d"}}, {"head": {"id": "45a9492c-2ee4-4de5-8d5c-c7b8ec101e08", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950294479500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3f6b99-54b1-4013-b117-a3655132e375", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950295575400, "endTime": 11950295595100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "5cf00936-6632-4692-984a-3a87949e749e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cf00936-6632-4692-984a-3a87949e749e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950295575400, "endTime": 11950295595100}, "additional": {"logType": "info", "children": [], "durationId": "3d3f6b99-54b1-4013-b117-a3655132e375"}}, {"head": {"id": "18f41866-8920-4a22-a367-e9af396558ad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950295720200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac7b5f65-a95f-47f2-830f-f2869a099d68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950297424600, "endTime": 11950297463800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "aa9f79be-bb48-4f68-af55-2f147e105035"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa9f79be-bb48-4f68-af55-2f147e105035", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950297424600, "endTime": 11950297463800}, "additional": {"logType": "info", "children": [], "durationId": "ac7b5f65-a95f-47f2-830f-f2869a099d68"}}, {"head": {"id": "138b5ded-1716-4934-88a7-1c08af958e3c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950297758100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d767de-2036-4b87-8255-f6a68ed4cdfc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950299561800, "endTime": 11950299601500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "440835df-dcf7-4cc7-9897-413defa7cd37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "440835df-dcf7-4cc7-9897-413defa7cd37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950299561800, "endTime": 11950299601500}, "additional": {"logType": "info", "children": [], "durationId": "f8d767de-2036-4b87-8255-f6a68ed4cdfc"}}, {"head": {"id": "548b9f36-692d-4563-ac58-55f0e957c91f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950751567300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b169d7c-0720-49cd-b641-9c88c6bd4c23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950754077100, "endTime": 11950754121900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78f9e1c6-9e64-4ad5-9afe-c21c2b1dd809", "logId": "996c7390-0a52-42d9-b10b-35b84e3fddb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "996c7390-0a52-42d9-b10b-35b84e3fddb4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 11950754077100, "endTime": 11950754121900}, "additional": {"logType": "info", "children": [], "durationId": "9b169d7c-0720-49cd-b641-9c88c6bd4c23"}}, {"head": {"id": "54be4d5c-959b-4a47-85c3-98ab9db987c1", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12226935334900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad937188-552a-4e12-b62c-160bc8143ea4", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12226935590900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6026c512-671b-462e-8988-d0698c5b2e5a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236701731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ff78735-c592-4b52-a00f-17e00c423686", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236716000300, "endTime": 12237568602500}, "additional": {"children": ["f0eb4c0d-6371-4cce-aa51-0c8692effb53", "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "8b9af3f5-fbb0-4a5d-b41d-4f63abf4222e", "da3a18fb-70f0-4f6b-ae1c-5015f4a125d8", "77f24051-54d5-4c96-8d15-e6ac4c8c8983", "93ef38a4-8db6-435d-ace0-a8776e81ba68", "45f2bc41-28d4-4e36-83f9-09bb00ddf871"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0eb4c0d-6371-4cce-aa51-0c8692effb53", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236716001600, "endTime": 12236799298500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "ecf92e0d-c521-439f-b088-cc49e3b4912e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236799317500, "endTime": 12237547578300}, "additional": {"children": ["3269dd3d-7595-4d0c-8dc5-ed141c1fb176", "a2998dad-233a-4e7a-a04f-c14cd5496616", "a020a88a-46e3-4366-be1e-6d03eece4663", "e571d49e-6989-41c9-96d4-8547d58b71a0", "13aa335c-1db9-484a-9c54-efd2b84ee6cd", "d231e827-df43-4183-9169-6647053b70d4", "cfc42668-e337-4efa-b993-a3f5ec246dab", "1cdfa5d1-3552-404e-a3ca-d8fe022d4d7b", "085d749f-4442-4de9-b577-3306ef75d9cf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b9af3f5-fbb0-4a5d-b41d-4f63abf4222e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237547607400, "endTime": 12237568544700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "f8a2a4cf-89c1-49a9-8453-ddc3021b94f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da3a18fb-70f0-4f6b-ae1c-5015f4a125d8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237568557700, "endTime": 12237568589800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "4e33317d-c164-4cd2-91eb-11279f8db5d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77f24051-54d5-4c96-8d15-e6ac4c8c8983", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236719282200, "endTime": 12236719309500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "6f432506-2bd3-41d2-a7cd-0af3848cb5b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f432506-2bd3-41d2-a7cd-0af3848cb5b5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236719282200, "endTime": 12236719309500}, "additional": {"logType": "info", "children": [], "durationId": "77f24051-54d5-4c96-8d15-e6ac4c8c8983", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "93ef38a4-8db6-435d-ace0-a8776e81ba68", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236724897400, "endTime": 12236724914300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "38b431d9-d3f2-433e-8c79-5d35bd5bc56b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38b431d9-d3f2-433e-8c79-5d35bd5bc56b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236724897400, "endTime": 12236724914300}, "additional": {"logType": "info", "children": [], "durationId": "93ef38a4-8db6-435d-ace0-a8776e81ba68", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "26092219-3b84-445e-916e-902cae8d2046", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236724975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2370e6f6-acd6-49ca-b2f7-b52b9a95d8fa", "name": "Cache service initialization finished in 75 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236799158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf92e0d-c521-439f-b088-cc49e3b4912e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236716001600, "endTime": 12236799298500}, "additional": {"logType": "info", "children": [], "durationId": "f0eb4c0d-6371-4cce-aa51-0c8692effb53", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "3269dd3d-7595-4d0c-8dc5-ed141c1fb176", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236805306800, "endTime": 12236805318100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "e8eede84-bfa2-497d-8740-ca1e37d6cccd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2998dad-233a-4e7a-a04f-c14cd5496616", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236805339800, "endTime": 12236816219100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "c8dd6844-1dd1-4f4c-b38d-d5d93138eeef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a020a88a-46e3-4366-be1e-6d03eece4663", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236816233100, "endTime": 12237178545200}, "additional": {"children": ["60d43833-8a59-49da-95e9-a1ec5a84491a", "e4370d43-61a6-47d1-ba09-fddbdb89e502", "bfeb1e41-76c6-4389-887d-6efe7925ccd3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "0a80fd80-79d4-4977-ab88-38192c71d35d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e571d49e-6989-41c9-96d4-8547d58b71a0", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237178573300, "endTime": 12237257084500}, "additional": {"children": ["ad2506ef-b7b9-4a00-9b18-90602c92df4d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "c328053c-6a69-48a4-8a15-273db7804231"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13aa335c-1db9-484a-9c54-efd2b84ee6cd", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237257093700, "endTime": 12237357140700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "3597ee1c-e75a-4b05-80dd-d3bc6d4d326a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d231e827-df43-4183-9169-6647053b70d4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237411283600, "endTime": 12237463066500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "3e6e74c4-6d54-4c7b-b790-84c3abdeb308"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfc42668-e337-4efa-b993-a3f5ec246dab", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237463094600, "endTime": 12237547005900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "10d53e82-5e47-402a-8dee-07f2ddf9d677"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cdfa5d1-3552-404e-a3ca-d8fe022d4d7b", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237547032800, "endTime": 12237547558300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "f96aa80d-e521-473c-b897-56fa38bf41d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8eede84-bfa2-497d-8740-ca1e37d6cccd", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236805306800, "endTime": 12236805318100}, "additional": {"logType": "info", "children": [], "durationId": "3269dd3d-7595-4d0c-8dc5-ed141c1fb176", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "c8dd6844-1dd1-4f4c-b38d-d5d93138eeef", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236805339800, "endTime": 12236816219100}, "additional": {"logType": "info", "children": [], "durationId": "a2998dad-233a-4e7a-a04f-c14cd5496616", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "60d43833-8a59-49da-95e9-a1ec5a84491a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236816937200, "endTime": 12236816952800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a020a88a-46e3-4366-be1e-6d03eece4663", "logId": "e96549eb-4f41-4162-b48e-09d7602390a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e96549eb-4f41-4162-b48e-09d7602390a7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236816937200, "endTime": 12236816952800}, "additional": {"logType": "info", "children": [], "durationId": "60d43833-8a59-49da-95e9-a1ec5a84491a", "parent": "0a80fd80-79d4-4977-ab88-38192c71d35d"}}, {"head": {"id": "e4370d43-61a6-47d1-ba09-fddbdb89e502", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236820196100, "endTime": 12237177688500}, "additional": {"children": ["7f21b7f2-8b77-4640-9fba-0d3671166276", "b47997c8-54d0-4fad-863a-e3048008ea76"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a020a88a-46e3-4366-be1e-6d03eece4663", "logId": "25f2876e-27aa-49b3-a93b-5be9b3296a64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f21b7f2-8b77-4640-9fba-0d3671166276", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236820198300, "endTime": 12236837085600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4370d43-61a6-47d1-ba09-fddbdb89e502", "logId": "17ec5569-736e-4e59-9a5c-93ac53ee0209"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b47997c8-54d0-4fad-863a-e3048008ea76", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236837104600, "endTime": 12237177675500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4370d43-61a6-47d1-ba09-fddbdb89e502", "logId": "fc33104c-52bb-4cf5-a6ec-af7c241f903f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb207aa1-dfa7-40e4-8b1e-ff1a65258c24", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236820211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f88fdb-1091-41a8-a49b-a98d3569d7a1", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236836935000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ec5569-736e-4e59-9a5c-93ac53ee0209", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236820198300, "endTime": 12236837085600}, "additional": {"logType": "info", "children": [], "durationId": "7f21b7f2-8b77-4640-9fba-0d3671166276", "parent": "25f2876e-27aa-49b3-a93b-5be9b3296a64"}}, {"head": {"id": "68a26ea6-f756-4aac-a25d-6e7aa90fbe0e", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236837116600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "121d3b11-16f6-40ad-8aec-78881907250b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236844507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f19318-b106-4d02-8bd3-cc3e3f4cc47d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236844632300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "790bd6e3-d1f7-472f-a467-bfab4d8baf8f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236844799100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a07e0f2-0ff6-4fde-af5b-963d582bcee2", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236844906900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25cf3c8b-9a48-4175-9416-17dc9fcd0665", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236878085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c0e722-86ca-463c-ba7d-6845712c7d29", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236886045800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d4677f-e9c4-4f43-a787-7ee0257db661", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237011894900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ef04a7-88c8-43c4-968e-de5095d63359", "name": "Sdk init in 219 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237106093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4ccaad-0980-443d-937b-810a675a27d0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237106514500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 14}, "markType": "other"}}, {"head": {"id": "7241cea2-0e8a-4de8-8ce2-cd55c9017610", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237106728600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 14}, "markType": "other"}}, {"head": {"id": "5895b903-ed7b-4e26-be79-231b61f5ca95", "name": "Project task initialization takes 68 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237177401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d56d586-032c-46e4-97bd-4f14257b7da9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237177510800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546c0eb5-c0c4-4bfd-ab29-123d9883bbe9", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237177573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d0455e-dcbe-4170-8437-d956cb4bfe61", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237177626700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc33104c-52bb-4cf5-a6ec-af7c241f903f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236837104600, "endTime": 12237177675500}, "additional": {"logType": "info", "children": [], "durationId": "b47997c8-54d0-4fad-863a-e3048008ea76", "parent": "25f2876e-27aa-49b3-a93b-5be9b3296a64"}}, {"head": {"id": "25f2876e-27aa-49b3-a93b-5be9b3296a64", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236820196100, "endTime": 12237177688500}, "additional": {"logType": "info", "children": ["17ec5569-736e-4e59-9a5c-93ac53ee0209", "fc33104c-52bb-4cf5-a6ec-af7c241f903f"], "durationId": "e4370d43-61a6-47d1-ba09-fddbdb89e502", "parent": "0a80fd80-79d4-4977-ab88-38192c71d35d"}}, {"head": {"id": "bfeb1e41-76c6-4389-887d-6efe7925ccd3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237178514900, "endTime": 12237178528700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a020a88a-46e3-4366-be1e-6d03eece4663", "logId": "dd96a937-c66e-4350-8428-5dfe0cee5972"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd96a937-c66e-4350-8428-5dfe0cee5972", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237178514900, "endTime": 12237178528700}, "additional": {"logType": "info", "children": [], "durationId": "bfeb1e41-76c6-4389-887d-6efe7925ccd3", "parent": "0a80fd80-79d4-4977-ab88-38192c71d35d"}}, {"head": {"id": "0a80fd80-79d4-4977-ab88-38192c71d35d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236816233100, "endTime": 12237178545200}, "additional": {"logType": "info", "children": ["e96549eb-4f41-4162-b48e-09d7602390a7", "25f2876e-27aa-49b3-a93b-5be9b3296a64", "dd96a937-c66e-4350-8428-5dfe0cee5972"], "durationId": "a020a88a-46e3-4366-be1e-6d03eece4663", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "ad2506ef-b7b9-4a00-9b18-90602c92df4d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237179434100, "endTime": 12237257070500}, "additional": {"children": ["d0f88712-a127-4c5f-b7f7-5befb542bc96", "6bfcfc31-ab18-403c-a5a0-f60ba5273994", "37582364-9025-43fd-82bd-9718775b5a9c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e571d49e-6989-41c9-96d4-8547d58b71a0", "logId": "bf90100f-7605-4ca2-a9a7-be2e7a35d66a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0f88712-a127-4c5f-b7f7-5befb542bc96", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237182876000, "endTime": 12237182889400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad2506ef-b7b9-4a00-9b18-90602c92df4d", "logId": "e99dd681-0c09-4357-9a6c-b3c418071bc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e99dd681-0c09-4357-9a6c-b3c418071bc7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237182876000, "endTime": 12237182889400}, "additional": {"logType": "info", "children": [], "durationId": "d0f88712-a127-4c5f-b7f7-5befb542bc96", "parent": "bf90100f-7605-4ca2-a9a7-be2e7a35d66a"}}, {"head": {"id": "6bfcfc31-ab18-403c-a5a0-f60ba5273994", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237184978400, "endTime": 12237255498700}, "additional": {"children": ["914251f6-fdd3-475b-b183-e9ccc2e7413a", "5077d00c-b456-4ecc-aa5e-1c996eaf8e2c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad2506ef-b7b9-4a00-9b18-90602c92df4d", "logId": "1d2de49e-dc7f-474b-b15c-73e5fdbbcacf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "914251f6-fdd3-475b-b183-e9ccc2e7413a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237184979400, "endTime": 12237228110500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6bfcfc31-ab18-403c-a5a0-f60ba5273994", "logId": "7e1720b9-c59c-49ac-baa5-4141aac75fe3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5077d00c-b456-4ecc-aa5e-1c996eaf8e2c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237228127600, "endTime": 12237255484600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6bfcfc31-ab18-403c-a5a0-f60ba5273994", "logId": "92314ecd-bf13-4f80-a279-7be216eeab55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7656e5c-233e-4d95-9533-4025428d4c83", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237184984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81b37a8-e9e0-408f-b226-7a4e2f3e51f9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237227983700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e1720b9-c59c-49ac-baa5-4141aac75fe3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237184979400, "endTime": 12237228110500}, "additional": {"logType": "info", "children": [], "durationId": "914251f6-fdd3-475b-b183-e9ccc2e7413a", "parent": "1d2de49e-dc7f-474b-b15c-73e5fdbbcacf"}}, {"head": {"id": "d55b0af6-23bc-4cfa-bdb5-f25e4ae7fe21", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237228139500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41405bb9-ab9e-4473-8cf4-48c508f45a57", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237250493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b322ed8a-c12c-4fca-ac36-9fd5d9795caf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237250663900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13426815-fd2b-41cf-a59e-bafe45bc501b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237250945000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d92ab1-725f-4d60-b61c-5072a0d7fc6f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237251095500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035bff92-763c-4727-94fd-78c9d4d285b4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237251170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0358d0e4-dad8-4f19-8a4f-bc305ab15639", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237251226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca75b78-456f-4354-9235-215bea9af58c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237251297800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b0dcd3d-e625-4add-9399-e86570a9840b", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237255122500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a96b30-90c3-4b13-8c58-5c5facb9e1cb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237255302800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ec4d4a-6409-4c8f-a52b-fb84cee3a707", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237255373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5c04fc-dba6-462d-b8cb-eb91950d516a", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237255430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92314ecd-bf13-4f80-a279-7be216eeab55", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237228127600, "endTime": 12237255484600}, "additional": {"logType": "info", "children": [], "durationId": "5077d00c-b456-4ecc-aa5e-1c996eaf8e2c", "parent": "1d2de49e-dc7f-474b-b15c-73e5fdbbcacf"}}, {"head": {"id": "1d2de49e-dc7f-474b-b15c-73e5fdbbcacf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237184978400, "endTime": 12237255498700}, "additional": {"logType": "info", "children": ["7e1720b9-c59c-49ac-baa5-4141aac75fe3", "92314ecd-bf13-4f80-a279-7be216eeab55"], "durationId": "6bfcfc31-ab18-403c-a5a0-f60ba5273994", "parent": "bf90100f-7605-4ca2-a9a7-be2e7a35d66a"}}, {"head": {"id": "37582364-9025-43fd-82bd-9718775b5a9c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237257042400, "endTime": 12237257054700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad2506ef-b7b9-4a00-9b18-90602c92df4d", "logId": "1719a06a-f907-43f2-9c31-95a9df5e72ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1719a06a-f907-43f2-9c31-95a9df5e72ac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237257042400, "endTime": 12237257054700}, "additional": {"logType": "info", "children": [], "durationId": "37582364-9025-43fd-82bd-9718775b5a9c", "parent": "bf90100f-7605-4ca2-a9a7-be2e7a35d66a"}}, {"head": {"id": "bf90100f-7605-4ca2-a9a7-be2e7a35d66a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237179434100, "endTime": 12237257070500}, "additional": {"logType": "info", "children": ["e99dd681-0c09-4357-9a6c-b3c418071bc7", "1d2de49e-dc7f-474b-b15c-73e5fdbbcacf", "1719a06a-f907-43f2-9c31-95a9df5e72ac"], "durationId": "ad2506ef-b7b9-4a00-9b18-90602c92df4d", "parent": "c328053c-6a69-48a4-8a15-273db7804231"}}, {"head": {"id": "c328053c-6a69-48a4-8a15-273db7804231", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237178573300, "endTime": 12237257084500}, "additional": {"logType": "info", "children": ["bf90100f-7605-4ca2-a9a7-be2e7a35d66a"], "durationId": "e571d49e-6989-41c9-96d4-8547d58b71a0", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "418ddd2a-c2b0-40b8-ac0e-fc35560c8c60", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237355708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "817afce4-72ae-432f-a6ad-035026b7528a", "name": "hvigorfile, resolve hvigorfile dependencies in 100 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237356985000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3597ee1c-e75a-4b05-80dd-d3bc6d4d326a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237257093700, "endTime": 12237357140700}, "additional": {"logType": "info", "children": [], "durationId": "13aa335c-1db9-484a-9c54-efd2b84ee6cd", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "085d749f-4442-4de9-b577-3306ef75d9cf", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237410999700, "endTime": 12237411265700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "logId": "eb4e7742-8ff3-4407-b4b9-50e854ef6872"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05e36483-a771-47ca-8e8e-46322a3a8219", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237411044100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4e7742-8ff3-4407-b4b9-50e854ef6872", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237410999700, "endTime": 12237411265700}, "additional": {"logType": "info", "children": [], "durationId": "085d749f-4442-4de9-b577-3306ef75d9cf", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "2da8b683-6469-4393-b83d-7b0522826487", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237428634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c42de8b-b000-4d5d-a2ef-4df85e3a195e", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237461865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6e74c4-6d54-4c7b-b790-84c3abdeb308", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237411283600, "endTime": 12237463066500}, "additional": {"logType": "info", "children": [], "durationId": "d231e827-df43-4183-9169-6647053b70d4", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "8762b6b8-7f01-491e-b710-9f6f3cfc5f23", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237463118600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2998218-6bcb-4f2f-8a2e-22e1c4992002", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237540362000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc302f48-2b81-463f-aaf8-f7daf5c29cf4", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237540491300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25b3bf3-8927-4ebe-a3cb-2d1e5c8b2e41", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237540760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4358d58c-965a-4dd0-8f2a-e2e6c9472093", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237543684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45dbc936-7d43-4e9f-8dee-ccf8c11fc385", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237543805100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10d53e82-5e47-402a-8dee-07f2ddf9d677", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237463094600, "endTime": 12237547005900}, "additional": {"logType": "info", "children": [], "durationId": "cfc42668-e337-4efa-b993-a3f5ec246dab", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "1fb2a80a-8876-47b4-87a8-7df54fd90442", "name": "Configuration phase cost:742 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237547064800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f96aa80d-e521-473c-b897-56fa38bf41d0", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237547032800, "endTime": 12237547558300}, "additional": {"logType": "info", "children": [], "durationId": "1cdfa5d1-3552-404e-a3ca-d8fe022d4d7b", "parent": "42d6942b-8c55-43ed-b897-8717ec42aeb3"}}, {"head": {"id": "42d6942b-8c55-43ed-b897-8717ec42aeb3", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236799317500, "endTime": 12237547578300}, "additional": {"logType": "info", "children": ["e8eede84-bfa2-497d-8740-ca1e37d6cccd", "c8dd6844-1dd1-4f4c-b38d-d5d93138eeef", "0a80fd80-79d4-4977-ab88-38192c71d35d", "c328053c-6a69-48a4-8a15-273db7804231", "3597ee1c-e75a-4b05-80dd-d3bc6d4d326a", "3e6e74c4-6d54-4c7b-b790-84c3abdeb308", "10d53e82-5e47-402a-8dee-07f2ddf9d677", "f96aa80d-e521-473c-b897-56fa38bf41d0", "eb4e7742-8ff3-4407-b4b9-50e854ef6872"], "durationId": "a3c9cbfa-1ff8-441a-941f-2d0238444a23", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "45f2bc41-28d4-4e36-83f9-09bb00ddf871", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237568483700, "endTime": 12237568513300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ff78735-c592-4b52-a00f-17e00c423686", "logId": "9e2f7688-af93-46b0-91e4-c50a97fd2f7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e2f7688-af93-46b0-91e4-c50a97fd2f7f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237568483700, "endTime": 12237568513300}, "additional": {"logType": "info", "children": [], "durationId": "45f2bc41-28d4-4e36-83f9-09bb00ddf871", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "f8a2a4cf-89c1-49a9-8453-ddc3021b94f2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237547607400, "endTime": 12237568544700}, "additional": {"logType": "info", "children": [], "durationId": "8b9af3f5-fbb0-4a5d-b41d-4f63abf4222e", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "4e33317d-c164-4cd2-91eb-11279f8db5d9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237568557700, "endTime": 12237568589800}, "additional": {"logType": "info", "children": [], "durationId": "da3a18fb-70f0-4f6b-ae1c-5015f4a125d8", "parent": "4c00414d-1eea-4d66-ac9c-ceea6964500b"}}, {"head": {"id": "4c00414d-1eea-4d66-ac9c-ceea6964500b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236716000300, "endTime": 12237568602500}, "additional": {"logType": "info", "children": ["ecf92e0d-c521-439f-b088-cc49e3b4912e", "42d6942b-8c55-43ed-b897-8717ec42aeb3", "f8a2a4cf-89c1-49a9-8453-ddc3021b94f2", "4e33317d-c164-4cd2-91eb-11279f8db5d9", "6f432506-2bd3-41d2-a7cd-0af3848cb5b5", "38b431d9-d3f2-433e-8c79-5d35bd5bc56b", "9e2f7688-af93-46b0-91e4-c50a97fd2f7f"], "durationId": "3ff78735-c592-4b52-a00f-17e00c423686"}}, {"head": {"id": "2a5e32d4-bf1f-4f8b-860d-ec56e256d67e", "name": "Configuration task cost before running: 857 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237569065200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e62c4ac-e1c4-4f4d-bddf-c96182906482", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237578856200, "endTime": 12237594182300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d3be6699-239a-4fdc-8890-e0b97bd2ed1f", "logId": "feea5be5-f9de-4942-919c-7c3d68ee0465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3be6699-239a-4fdc-8890-e0b97bd2ed1f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237572365200}, "additional": {"logType": "detail", "children": [], "durationId": "7e62c4ac-e1c4-4f4d-bddf-c96182906482"}}, {"head": {"id": "5629b7ea-840c-4faa-a87c-9d1ff3d18c1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237573287200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204fce2e-f842-4210-bf8f-8578e184f683", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237573495300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55adb2e4-3796-43f8-98d9-ee0da01c20ec", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237578885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84cc8835-51c5-4b0f-8cc1-c5e67c79ee47", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237593814000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc95ff46-430f-487b-943d-27162c4cbee1", "name": "entry : default@PreBuild cost memory 0.2857818603515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237594016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feea5be5-f9de-4942-919c-7c3d68ee0465", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237578856200, "endTime": 12237594182300}, "additional": {"logType": "info", "children": [], "durationId": "7e62c4ac-e1c4-4f4d-bddf-c96182906482"}}, {"head": {"id": "b0adeb20-71ba-434f-90e2-7ccf9ed1a4b0", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237602749300, "endTime": 12237606154500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e101b3aa-cf9a-43b6-8553-4fff22b636ce", "logId": "3ce0d41d-8e74-466b-ab54-c902b24c4133"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e101b3aa-cf9a-43b6-8553-4fff22b636ce", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237600627700}, "additional": {"logType": "detail", "children": [], "durationId": "b0adeb20-71ba-434f-90e2-7ccf9ed1a4b0"}}, {"head": {"id": "787bf805-c1b4-48ec-aa54-f018264d79b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237601369500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "956f8e4e-13a8-49c9-b807-ddad53dcf1b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237601565400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec76ba1d-3dcb-4f25-afef-6ccf13925024", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237602770300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d657a590-caec-46b1-9403-3b6ed691a2e5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237605801400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000a29ba-f5a1-41c2-a13b-8a1ad3240f37", "name": "entry : default@MergeProfile cost memory 0.11476898193359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237606001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce0d41d-8e74-466b-ab54-c902b24c4133", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237602749300, "endTime": 12237606154500}, "additional": {"logType": "info", "children": [], "durationId": "b0adeb20-71ba-434f-90e2-7ccf9ed1a4b0"}}, {"head": {"id": "059d84af-d20c-4f6f-8cd3-70aad2ad65d3", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237610905700, "endTime": 12237615816400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0b67d6a9-8ebd-4d1c-85d6-7a5a9bd7b07c", "logId": "fca76566-a72a-44c4-8dc2-69b954f95f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b67d6a9-8ebd-4d1c-85d6-7a5a9bd7b07c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237609100300}, "additional": {"logType": "detail", "children": [], "durationId": "059d84af-d20c-4f6f-8cd3-70aad2ad65d3"}}, {"head": {"id": "e8dc8269-2984-4dc6-afe3-0ee7d6b98133", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237609752200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f64daebd-48ac-4700-bc7e-15cd3050beed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237609884500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c59da0d-8bb1-4d82-a7b4-74315c4f702c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237610920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b2d6433-5721-439e-8430-e945c790ed42", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237614117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce10578-217e-492d-a253-a34fb25a142f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237615623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76bc449b-594a-48a3-a12d-ce6228e9f731", "name": "entry : default@CreateBuildProfile cost memory 0.10491943359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237615735500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca76566-a72a-44c4-8dc2-69b954f95f5f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237610905700, "endTime": 12237615816400}, "additional": {"logType": "info", "children": [], "durationId": "059d84af-d20c-4f6f-8cd3-70aad2ad65d3"}}, {"head": {"id": "5b74f6c4-f33a-46ae-97c7-0eadf20e1b9d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619197100, "endTime": 12237619642700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "037ee151-4f9b-45f4-ba46-f9d67d15884f", "logId": "795ef5bf-df58-4d94-b25b-fbc03b043683"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "037ee151-4f9b-45f4-ba46-f9d67d15884f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237617545100}, "additional": {"logType": "detail", "children": [], "durationId": "5b74f6c4-f33a-46ae-97c7-0eadf20e1b9d"}}, {"head": {"id": "10d2e5d8-f6b2-4c9b-9e0e-77c323b7a7d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237618136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53bdfea-290f-4697-b192-1b6002c1b2c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237618295600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4fb54dc-de5d-4bac-8dde-fe99613d615f", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619208600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64a353b-67f0-4d4b-8e19-fd4cadbddd57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619338900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62459b9d-a9c7-437f-a281-214b2a79e996", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619404400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "169c22da-7df4-416f-ad8a-412a360c70b9", "name": "entry : default@PreCheckSyscap cost memory 0.0377197265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619490800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a63b1e-e2e6-4686-a27d-3f40061551b9", "name": "runTaskFromQueue task cost before running: 908 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795ef5bf-df58-4d94-b25b-fbc03b043683", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237619197100, "endTime": 12237619642700, "totalTime": 360100}, "additional": {"logType": "info", "children": [], "durationId": "5b74f6c4-f33a-46ae-97c7-0eadf20e1b9d"}}, {"head": {"id": "19aa9f9c-bc6b-4862-9a33-ab3ec1aaf9a7", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237633588800, "endTime": 12237635076700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fa2e570d-2f1b-486e-baf5-b30ffd7acf44", "logId": "241ba00e-5fe3-416a-b6a5-55871f301c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa2e570d-2f1b-486e-baf5-b30ffd7acf44", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237622297200}, "additional": {"logType": "detail", "children": [], "durationId": "19aa9f9c-bc6b-4862-9a33-ab3ec1aaf9a7"}}, {"head": {"id": "f6a0727b-401d-4b2f-b954-4c390ac654da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237622958400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84263931-e27b-4e87-acf3-167282065743", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237623088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28402fab-da7d-44d0-b8d1-d96497eeb8ab", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237633615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84fe0d1e-6e78-4d68-985e-5afa4d32496f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237633899300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2714017-954b-4d93-ac59-6d9b33a90cc9", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237634858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7116f035-8866-44da-8073-4ccd743d1038", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0692596435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237634979600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241ba00e-5fe3-416a-b6a5-55871f301c3f", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237633588800, "endTime": 12237635076700}, "additional": {"logType": "info", "children": [], "durationId": "19aa9f9c-bc6b-4862-9a33-ab3ec1aaf9a7"}}, {"head": {"id": "ac58eec1-608f-4acd-91c0-8430fb22fdf1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237639130900, "endTime": 12237640471700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5511c479-a976-4d10-b7f7-144dbb211fa8", "logId": "f9d82a99-5146-460a-9556-a477e0867443"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5511c479-a976-4d10-b7f7-144dbb211fa8", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237636797400}, "additional": {"logType": "detail", "children": [], "durationId": "ac58eec1-608f-4acd-91c0-8430fb22fdf1"}}, {"head": {"id": "6f45d17a-4b03-40e6-a519-ee1c2f7e3f5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237637321600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6764eea0-1f25-4d2c-a7c6-824524c50e52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237637414900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f70cc7-1dd1-47e9-989d-1a560e126baf", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237639148100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b49fd31-1698-4845-80d4-5816a84406f2", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237640295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee229c5d-a021-4bf6-b365-dd9d1a1f4461", "name": "entry : default@ProcessProfile cost memory 0.06230926513671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237640398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d82a99-5146-460a-9556-a477e0867443", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237639130900, "endTime": 12237640471700}, "additional": {"logType": "info", "children": [], "durationId": "ac58eec1-608f-4acd-91c0-8430fb22fdf1"}}, {"head": {"id": "ae037abc-f0e1-42fb-97a7-109232f4925e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237648184600, "endTime": 12237659746900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "53b1a14a-a397-484b-b6fd-d8e9277985ad", "logId": "a5653a88-dffb-43e0-8a02-e8073c5d0bf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53b1a14a-a397-484b-b6fd-d8e9277985ad", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237643608300}, "additional": {"logType": "detail", "children": [], "durationId": "ae037abc-f0e1-42fb-97a7-109232f4925e"}}, {"head": {"id": "1336e00f-a71e-4f10-a8a2-0c46e9dffbfa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237644552600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2a551e-fc85-4da7-8f3c-d81083ee5420", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237644731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7614cd7-c18d-4326-88ed-cdd98409eda7", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237648208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cd5a1e-c641-43c5-a2ab-c7d9be6e6e41", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237659510400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1171473b-541f-4095-9bbe-5e7b81323410", "name": "entry : default@ProcessRouterMap cost memory 0.19809722900390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237659663100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5653a88-dffb-43e0-8a02-e8073c5d0bf6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237648184600, "endTime": 12237659746900}, "additional": {"logType": "info", "children": [], "durationId": "ae037abc-f0e1-42fb-97a7-109232f4925e"}}, {"head": {"id": "0c3d9017-2ce8-4cc4-98be-e596e113bc0f", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237671467700, "endTime": 12237678848500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "808a9654-a0bb-40d9-bc24-699ad050ee72", "logId": "86aa90ec-ed8f-43b5-9dc4-9e53e0953a52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "808a9654-a0bb-40d9-bc24-699ad050ee72", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237664803500}, "additional": {"logType": "detail", "children": [], "durationId": "0c3d9017-2ce8-4cc4-98be-e596e113bc0f"}}, {"head": {"id": "3a024e54-456c-4e46-979a-b7fdd3bcbe61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237665560600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6798dfd0-cfc4-425e-92f7-c67ea5ff6b13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237665722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55c4f52-23ea-436d-b3df-96de579fe607", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237667459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307b439c-aa03-4c34-aaa7-a8b010000e54", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237675015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5aee6e0-1bed-4bda-8203-0cfb1b5a3618", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237675352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4355b28a-31c4-4ed1-91a1-3eedff93aa17", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237675510400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03896266-2171-49c4-8239-f646e0ff6e91", "name": "entry : default@PreviewProcessResource cost memory 0.09395599365234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237675692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cafb82c-11b9-4c1f-815b-2e104732ba8c", "name": "runTaskFromQueue task cost before running: 967 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237678691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86aa90ec-ed8f-43b5-9dc4-9e53e0953a52", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237671467700, "endTime": 12237678848500, "totalTime": 4373400}, "additional": {"logType": "info", "children": [], "durationId": "0c3d9017-2ce8-4cc4-98be-e596e113bc0f"}}, {"head": {"id": "bac9ce0f-2e72-41a6-bbce-ecb7622d57c8", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237702635200, "endTime": 12237745074500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c590e5b7-40fa-446c-9efc-2b3c9d0525e5", "logId": "b72c7202-f903-4f18-b447-76e87951e22d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c590e5b7-40fa-446c-9efc-2b3c9d0525e5", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237683408900}, "additional": {"logType": "detail", "children": [], "durationId": "bac9ce0f-2e72-41a6-bbce-ecb7622d57c8"}}, {"head": {"id": "68742dd6-4d41-4855-9957-2be54c69f47c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237684152600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066f6889-51c3-45b5-b3c7-8f72b730569f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237697540100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e9ecf5-f7f2-4392-81aa-db3d3c8cccb9", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237702712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc162017-df07-413e-9867-5e3127df843b", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237744824100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd4e74f-ea6a-410b-be1d-b07d9377554a", "name": "entry : default@GenerateLoaderJson cost memory 0.722442626953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237744983200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72c7202-f903-4f18-b447-76e87951e22d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237702635200, "endTime": 12237745074500}, "additional": {"logType": "info", "children": [], "durationId": "bac9ce0f-2e72-41a6-bbce-ecb7622d57c8"}}, {"head": {"id": "bb33a3d8-63d2-41d4-85ff-9e10cc0cb591", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237777200500, "endTime": 12237834659100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bc0d5a75-eade-4a6f-ab80-9b603809ddfe", "logId": "b2895b0b-5a0c-44d4-a518-d48cb27a26a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc0d5a75-eade-4a6f-ab80-9b603809ddfe", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237772168600}, "additional": {"logType": "detail", "children": [], "durationId": "bb33a3d8-63d2-41d4-85ff-9e10cc0cb591"}}, {"head": {"id": "97454113-30d8-4036-a970-c2ed93cfdf20", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237772872400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d469b9ff-b279-4ec2-9377-6277e3853e13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237773007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7673f5-af67-4b76-ab76-70601fb878ed", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237774354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3d215a-4126-4103-be1b-185f4bbb5b2c", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237777242900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7acff15d-8746-47bf-b8ff-03b124c887d6", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 56 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237834354700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c505ab-c48b-439c-9bb6-09a32eaa1fdb", "name": "entry : default@PreviewCompileResource cost memory -0.976806640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237834512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2895b0b-5a0c-44d4-a518-d48cb27a26a0", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237777200500, "endTime": 12237834659100}, "additional": {"logType": "info", "children": [], "durationId": "bb33a3d8-63d2-41d4-85ff-9e10cc0cb591"}}, {"head": {"id": "b69566b5-65f2-4f1f-bfe1-3bc23eae2564", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838637500, "endTime": 12237839170000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c0f5a488-3de6-4f7a-b678-b0d7d309d71b", "logId": "6397680d-bc19-4d4f-a07f-550ee14bdcf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0f5a488-3de6-4f7a-b678-b0d7d309d71b", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237837391900}, "additional": {"logType": "detail", "children": [], "durationId": "b69566b5-65f2-4f1f-bfe1-3bc23eae2564"}}, {"head": {"id": "b58f2f3d-4245-41c6-8a51-26da453641f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa33cc84-f64c-40b0-9dbb-93826edfaf7b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838470100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f00bb742-efe3-434b-b71b-48cdbdcb7d62", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30581c47-4297-4bf9-a5e5-0080fc95cda4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838831000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2ed7d86-a5b3-4f62-b009-bdb240c74eec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838903400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4f4322-f078-4a5e-8afc-c558aa35d3a3", "name": "entry : default@PreviewHookCompileResource cost memory 0.038970947265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc9a851-c0d6-444b-8d06-2a32423f296e", "name": "runTaskFromQueue task cost before running: 1 s 127 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237839101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6397680d-bc19-4d4f-a07f-550ee14bdcf1", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237838637500, "endTime": 12237839170000, "totalTime": 437900}, "additional": {"logType": "info", "children": [], "durationId": "b69566b5-65f2-4f1f-bfe1-3bc23eae2564"}}, {"head": {"id": "0569ad45-97f0-4d74-b29d-e1c2899e4697", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237843531900, "endTime": 12237846795000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "fe328d37-bece-49c9-8e18-656dbc87555c", "logId": "70600343-0ad8-4903-b38e-07f94c391ba7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe328d37-bece-49c9-8e18-656dbc87555c", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237841717600}, "additional": {"logType": "detail", "children": [], "durationId": "0569ad45-97f0-4d74-b29d-e1c2899e4697"}}, {"head": {"id": "b7b6fcac-a30b-4d69-b2a8-7d6326345df4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237842498300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7fabb4-7083-4614-b8e7-1efd0c1cabc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237842649800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5150dc22-3116-4920-b46e-9187bfb7dd03", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237843547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2973f906-648e-41ad-ab62-90e675857f73", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237846501600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990ae44d-9011-4622-ade7-125be8a2b60b", "name": "entry : default@CopyPreviewProfile cost memory 0.10764312744140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237846693000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70600343-0ad8-4903-b38e-07f94c391ba7", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237843531900, "endTime": 12237846795000}, "additional": {"logType": "info", "children": [], "durationId": "0569ad45-97f0-4d74-b29d-e1c2899e4697"}}, {"head": {"id": "e841d597-e61a-44e2-af87-1a5a0c1c2d7a", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237851725500, "endTime": 12237852638500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e408a327-81fd-4d2a-9431-447056363a5c", "logId": "e90a22d4-4646-4ff5-89e3-08f5e9b375e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e408a327-81fd-4d2a-9431-447056363a5c", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237849178600}, "additional": {"logType": "detail", "children": [], "durationId": "e841d597-e61a-44e2-af87-1a5a0c1c2d7a"}}, {"head": {"id": "73f9f83e-f1b3-48ff-9563-eb7f37eb38db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237850029100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d5b791-99e8-4e26-8db0-272cb523cfdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237850168600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9a472c-463a-46bf-8a3c-879c6da2442a", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237851749700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c7c4881-02be-44b9-8b66-225a98287740", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237852013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc8ba88-5950-4940-9f0d-1b5271bafda8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237852147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eee8fcf-95a3-45d9-94ac-f26582f9dcbe", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237852347100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62bef48e-dd16-41a8-ab22-dd3568eb61f3", "name": "runTaskFromQueue task cost before running: 1 s 141 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237852517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90a22d4-4646-4ff5-89e3-08f5e9b375e4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237851725500, "endTime": 12237852638500, "totalTime": 752000}, "additional": {"logType": "info", "children": [], "durationId": "e841d597-e61a-44e2-af87-1a5a0c1c2d7a"}}, {"head": {"id": "8fceeb27-10e2-42d1-b2a9-98a9c01746ce", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237855859800, "endTime": 12237856222900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "15d60fd5-4fff-4cb2-b19a-c4c578e37c3d", "logId": "fb154311-f53e-4478-9555-0b629ac88a90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d60fd5-4fff-4cb2-b19a-c4c578e37c3d", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237855797100}, "additional": {"logType": "detail", "children": [], "durationId": "8fceeb27-10e2-42d1-b2a9-98a9c01746ce"}}, {"head": {"id": "e9dfa0ea-28bc-4f7e-ac75-f84f07b3a412", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237855870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5918878-d451-4586-b0eb-1f7508543dd3", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237856032900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae20e5c4-7f40-4a06-ba74-f6c74e7ce86b", "name": "runTaskFromQueue task cost before running: 1 s 144 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237856145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb154311-f53e-4478-9555-0b629ac88a90", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237855859800, "endTime": 12237856222900, "totalTime": 257500}, "additional": {"logType": "info", "children": [], "durationId": "8fceeb27-10e2-42d1-b2a9-98a9c01746ce"}}, {"head": {"id": "a0e3d812-9399-40ee-8ab0-2e26a61290a4", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237859992700, "endTime": 12237862921700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3cb2972c-d125-400c-9487-1475f1fe8f67", "logId": "51ac5249-281a-4289-81c8-5ea0f996db8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb2972c-d125-400c-9487-1475f1fe8f67", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237858267900}, "additional": {"logType": "detail", "children": [], "durationId": "a0e3d812-9399-40ee-8ab0-2e26a61290a4"}}, {"head": {"id": "1c8c8eb0-ff77-4e57-90bb-47e038ec0673", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237858954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96149e45-0e89-434b-8d3c-1c0f7689291c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237859087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4e3171-43af-4711-944d-df50534696bd", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237860003500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c94bf4d-dd08-4ade-85e3-d2bfa50c1f95", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237862694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b66b799-cb68-4ba2-9097-a77f3f23a994", "name": "entry : default@PreviewUpdateAssets cost memory 0.10750579833984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237862841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ac5249-281a-4289-81c8-5ea0f996db8c", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237859992700, "endTime": 12237862921700}, "additional": {"logType": "info", "children": [], "durationId": "a0e3d812-9399-40ee-8ab0-2e26a61290a4"}}, {"head": {"id": "fbe9d0aa-c97a-4b18-9b51-2e6570035506", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237873464800, "endTime": 12277563033600}, "additional": {"children": ["47b65fe9-7347-417d-81aa-c5528c6d2c56"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed."], "detailId": "fc426123-3d1b-43d5-b9d7-93c86c23daa4", "logId": "f6b5e103-797a-4304-97e8-0d7cedf5bb15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc426123-3d1b-43d5-b9d7-93c86c23daa4", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237866203500}, "additional": {"logType": "detail", "children": [], "durationId": "fbe9d0aa-c97a-4b18-9b51-2e6570035506"}}, {"head": {"id": "cf13c4c5-e5a2-4819-aaf3-bb71eb3eec1f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237866871900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e326740-89d2-4b2d-aa9f-d232869ddfa6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237866996800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9533022-1918-4321-8745-e4faef0e5ad2", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237873485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f0b8b4-431f-4ebb-a918-ab9e5e7185d6", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237899441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a161f8f4-48f5-4da3-9391-4bc478e124a8", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237899598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker3", "startTime": 12237958927600, "endTime": 12277562433800}, "additional": {"children": ["8746c7ce-105f-4f14-ad14-b285fda5b6fe", "2c51a3bf-a3ce-4f47-901a-4449de833854", "31dba9f9-4e44-471e-9df9-e18dddf878b0", "8759e00d-5301-4b94-8757-77392e51bb1e", "8a14ab56-7caa-4c0a-9b54-b3f7dbecb16b", "8ac12b1c-c799-451b-99f9-8d1df906e419"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "fbe9d0aa-c97a-4b18-9b51-2e6570035506", "logId": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ea710e8-781a-480e-babd-506139a579d1", "name": "entry : default@PreviewArkTS cost memory 1.4981155395507812", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237962157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a35e6348-6fd8-4ef3-ad77-778672266293", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12259115923700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8746c7ce-105f-4f14-ad14-b285fda5b6fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12259117182700, "endTime": 12259117199700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "29a61e8a-a518-4229-934c-d5b76514e7b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29a61e8a-a518-4229-934c-d5b76514e7b0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12259117182700, "endTime": 12259117199700}, "additional": {"logType": "info", "children": [], "durationId": "8746c7ce-105f-4f14-ad14-b285fda5b6fe", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "460bcd39-6022-4fec-b1c0-2fff515b64a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267258711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c51a3bf-a3ce-4f47-901a-4449de833854", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267262261400, "endTime": 12267262308700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "3f2480ef-9425-43f7-987b-de4ceeb3be8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f2480ef-9425-43f7-987b-de4ceeb3be8b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267262261400, "endTime": 12267262308700}, "additional": {"logType": "info", "children": [], "durationId": "2c51a3bf-a3ce-4f47-901a-4449de833854", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "c70fab89-c8a8-4548-9c2c-ce01074735ef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267262552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31dba9f9-4e44-471e-9df9-e18dddf878b0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267265880100, "endTime": 12267265927900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "9c5d8f5b-39d5-401b-a3d6-1c7c35f032db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c5d8f5b-39d5-401b-a3d6-1c7c35f032db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267265880100, "endTime": 12267265927900}, "additional": {"logType": "info", "children": [], "durationId": "31dba9f9-4e44-471e-9df9-e18dddf878b0", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "0eae3a5c-abba-462f-bd5a-263efe89da3d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267266179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8759e00d-5301-4b94-8757-77392e51bb1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267269250800, "endTime": 12267269307300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "1bafe1aa-0dff-4e09-89c4-580a03d6fb20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bafe1aa-0dff-4e09-89c4-580a03d6fb20", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267269250800, "endTime": 12267269307300}, "additional": {"logType": "info", "children": [], "durationId": "8759e00d-5301-4b94-8757-77392e51bb1e", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "1f2c79fd-08b6-4771-b079-30a363d8e954", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267269564600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a14ab56-7caa-4c0a-9b54-b3f7dbecb16b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267273004900, "endTime": 12267273050700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "5d5a8150-801c-445a-925f-3c46356ac74c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d5a8150-801c-445a-925f-3c46356ac74c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12267273004900, "endTime": 12267273050700}, "additional": {"logType": "info", "children": [], "durationId": "8a14ab56-7caa-4c0a-9b54-b3f7dbecb16b", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "f8237769-4576-4b95-8b90-b89d93eca9c7", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277560193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ac12b1c-c799-451b-99f9-8d1df906e419", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277562260700, "endTime": 12277562291700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "logId": "d55d4340-9b17-4c59-8003-ec3bcf9b5fd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d55d4340-9b17-4c59-8003-ec3bcf9b5fd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277562260700, "endTime": 12277562291700}, "additional": {"logType": "info", "children": [], "durationId": "8ac12b1c-c799-451b-99f9-8d1df906e419", "parent": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"}}, {"head": {"id": "8ce87e5f-1913-4b52-b0ef-0fb81b7de64a", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker3", "startTime": 12237958927600, "endTime": 12277562433800}, "additional": {"logType": "error", "children": ["29a61e8a-a518-4229-934c-d5b76514e7b0", "3f2480ef-9425-43f7-987b-de4ceeb3be8b", "9c5d8f5b-39d5-401b-a3d6-1c7c35f032db", "1bafe1aa-0dff-4e09-89c4-580a03d6fb20", "5d5a8150-801c-445a-925f-3c46356ac74c", "d55d4340-9b17-4c59-8003-ec3bcf9b5fd8"], "durationId": "47b65fe9-7347-417d-81aa-c5528c6d2c56", "parent": "f6b5e103-797a-4304-97e8-0d7cedf5bb15"}}, {"head": {"id": "c66e6500-07d3-4f53-a057-65d7f545ae2f", "name": "default@PreviewArkTS watch work[3] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277562514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b5e103-797a-4304-97e8-0d7cedf5bb15", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12237873464800, "endTime": 12277563033600}, "additional": {"logType": "error", "children": ["8ce87e5f-1913-4b52-b0ef-0fb81b7de64a"], "durationId": "fbe9d0aa-c97a-4b18-9b51-2e6570035506"}}, {"head": {"id": "9699bb31-aa9c-4edf-a2ed-6aa6d3cf756c", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277563587900}, "additional": {"logType": "debug", "children": [], "durationId": "fbe9d0aa-c97a-4b18-9b51-2e6570035506"}}, {"head": {"id": "1a3a9cda-f467-4cd5-80fb-06b663efeac8", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:317:20\n Indexed access is not supported for fields (arkts-no-props-by-index)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:318:13\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/common/http/HttpClient.ets:318:31\n Indexed access is not supported for fields (arkts-no-props-by-index)\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277640026600}, "additional": {"logType": "debug", "children": [], "durationId": "fbe9d0aa-c97a-4b18-9b51-2e6570035506"}}, {"head": {"id": "e1899af4-98b4-4dae-a819-a94600709a6f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277689332100, "endTime": 12277689933300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52daf4fa-0971-4583-bf0f-534ee3d95b64", "logId": "e7bcf4b0-6f14-4e68-a4c1-b2e5dd591373"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7bcf4b0-6f14-4e68-a4c1-b2e5dd591373", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277689332100, "endTime": 12277689933300}, "additional": {"logType": "info", "children": [], "durationId": "e1899af4-98b4-4dae-a819-a94600709a6f"}}, {"head": {"id": "b9dac542-22b0-445f-8d16-0dce8764fc0b", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12236712442200, "endTime": 12277690402700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "648bab1e-fe49-4aa1-8e3f-8dd841ec4255", "name": "BUILD FAILED in 40 s 978 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277690561600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "e28584dc-3b6a-4936-8238-f0ca3560874b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277691364000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd3282b-7fae-4280-9b02-aca8e028fe9e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277691685300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c700626e-bf42-4bbc-aaea-c82a433f44f1", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277692263700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f66eded-94d0-40c0-8523-719b773cda55", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277692548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ece0f12-9600-488f-89d4-8ff6a1644704", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277692795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1888f96c-65d1-4ed9-971f-0570512653e0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277693018800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52ec165-3bb1-4365-bcc7-a5c434e86801", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277693242100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34315e3a-eb09-451b-b365-290d086e0bc9", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277693459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a569ef-07b4-457d-b441-0607da824b1c", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277693676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa91d6b-93fe-484e-bded-88332572d575", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277693892500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c62dff8-7d0d-4880-abe9-11d842b5af6c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277704446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e59176-b1c4-4e94-92fc-7dd32204e4a5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277707892400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66f350a-f2a5-497a-8a96-e66dfc132fd3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277709722100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ec2a24-62d9-4548-ac73-ca029421f2ec", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277710907400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9f1726-ac2e-4378-a864-dda3a2e5c1c4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277715991500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b391c9d-3950-4368-90d0-004147644cad", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277716664700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98d1cc6-77d8-446e-9190-420001fb1106", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277717875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb6bb15-25a7-4025-adba-e3b47a8094cd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277718856500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "837f1361-2121-4a6c-a0c3-c0afce970957", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277719951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ae3650-dafe-4148-93ee-b8bd339533f0", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:27 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12277720679700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}