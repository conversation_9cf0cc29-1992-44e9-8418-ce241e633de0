{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "505c60a5-736b-4e73-b363-72b46f095840", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 18971241888800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09a4e12-7c12-432e-81db-36de72331def", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 18971646506100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80376abc-f755-4eda-8dee-94e248461006", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 18971647205900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42940675-802e-4b7e-976d-d6ed5212a8ac", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19226939934600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3364398-5cc7-471b-923b-9ad5d2a49344", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19226943218800, "endTime": 19226943250700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7dfa676a-4789-4239-9dc0-ee215f681565"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dfa676a-4789-4239-9dc0-ee215f681565", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19226943218800, "endTime": 19226943250700}, "additional": {"logType": "info", "children": [], "durationId": "f3364398-5cc7-471b-923b-9ad5d2a49344"}}, {"head": {"id": "f1668adf-3186-4afb-9efd-7c13ca81475d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228081868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ffb0cf1-18c5-4dcf-be90-9e415f4ce742", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228082880900, "endTime": 19228082900000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a29fef5b-02e0-4798-b60c-5aa386878804"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a29fef5b-02e0-4798-b60c-5aa386878804", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228082880900, "endTime": 19228082900000}, "additional": {"logType": "info", "children": [], "durationId": "5ffb0cf1-18c5-4dcf-be90-9e415f4ce742"}}, {"head": {"id": "423ba49e-4305-4027-ac3f-6d3d81256d5f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228082981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf77704-d581-4adf-8ae8-2069ad78e620", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228083789300, "endTime": 19228083804600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4e4dc9c7-50fb-4fd9-aa43-a1f39a2e285a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e4dc9c7-50fb-4fd9-aa43-a1f39a2e285a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228083789300, "endTime": 19228083804600}, "additional": {"logType": "info", "children": [], "durationId": "6bf77704-d581-4adf-8ae8-2069ad78e620"}}, {"head": {"id": "461cd628-7edc-43b6-9cfa-b6e78602d2df", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228083876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037a7832-7686-44fb-91e2-b13a7e60685b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228084937800, "endTime": 19228084959600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e35a7320-ee4e-4231-9209-c0e86d5a5afa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e35a7320-ee4e-4231-9209-c0e86d5a5afa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228084937800, "endTime": 19228084959600}, "additional": {"logType": "info", "children": [], "durationId": "037a7832-7686-44fb-91e2-b13a7e60685b"}}, {"head": {"id": "862fe0ec-c4c7-47be-883f-5d40daddb5af", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228085067300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc1102b-8a64-48cb-a8e6-256dc71a13dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228087365000, "endTime": 19228087395600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "744f4f59-481b-4cf4-a01a-447d1d1f88c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "744f4f59-481b-4cf4-a01a-447d1d1f88c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228087365000, "endTime": 19228087395600}, "additional": {"logType": "info", "children": [], "durationId": "8dc1102b-8a64-48cb-a8e6-256dc71a13dc"}}, {"head": {"id": "9d2a159f-7dc7-4c91-a613-d708bef5d6d3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228087544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137d524c-cc46-4dd6-a647-448b518dcc5c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228090483800, "endTime": 19228090513700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f57f08f5-0fe3-4980-bcc8-7cd94988ac1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f57f08f5-0fe3-4980-bcc8-7cd94988ac1d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228090483800, "endTime": 19228090513700}, "additional": {"logType": "info", "children": [], "durationId": "137d524c-cc46-4dd6-a647-448b518dcc5c"}}, {"head": {"id": "9e166664-55f6-450b-bf45-d2d67f97f030", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228090652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b3dbad-9871-447f-8c0d-b9473ad69f42", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228092864000, "endTime": 19228092890800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0273b471-ca37-4c76-a4fd-2c3cb9cbada5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0273b471-ca37-4c76-a4fd-2c3cb9cbada5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228092864000, "endTime": 19228092890800}, "additional": {"logType": "info", "children": [], "durationId": "84b3dbad-9871-447f-8c0d-b9473ad69f42"}}, {"head": {"id": "a694dd38-172a-459c-ad0f-4f99b7134bc4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228094211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40a65c66-aca0-4b28-bb21-dd95bf18a994", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228103310200, "endTime": 19228103334400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "66048370-d91f-4217-9388-0c579e826630"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66048370-d91f-4217-9388-0c579e826630", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228103310200, "endTime": 19228103334400}, "additional": {"logType": "info", "children": [], "durationId": "40a65c66-aca0-4b28-bb21-dd95bf18a994"}}, {"head": {"id": "6679d13c-2f9d-4a3b-a6f0-8cfc4c270eee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228103510400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e61960f-8842-42b8-bf56-3247dc0dab2d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228105448300, "endTime": 19228105481200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "469de14c-49d8-47a9-9686-9af4383f600b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "469de14c-49d8-47a9-9686-9af4383f600b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228105448300, "endTime": 19228105481200}, "additional": {"logType": "info", "children": [], "durationId": "9e61960f-8842-42b8-bf56-3247dc0dab2d"}}, {"head": {"id": "fbbb6026-2d3a-48ce-b8b1-7ffb613a0ad5", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228385794500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d212aa-1ee5-4074-bf85-53cab58bd1ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228386852400, "endTime": 19228386872200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5c21776d-da8d-4a9d-8c52-5939c9280192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c21776d-da8d-4a9d-8c52-5939c9280192", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19228386852400, "endTime": 19228386872200}, "additional": {"logType": "info", "children": [], "durationId": "b9d212aa-1ee5-4074-bf85-53cab58bd1ed"}}, {"head": {"id": "3bc6cb20-689b-44e0-a395-7e03cfa83673", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19242193671700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b243e31d-12cc-419b-9b17-4c51fab0289a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19242194865700, "endTime": 19242194889000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "1fc64729-e1ba-47da-bff4-318d7cf3efe3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fc64729-e1ba-47da-bff4-318d7cf3efe3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19242194865700, "endTime": 19242194889000}, "additional": {"logType": "info", "children": [], "durationId": "b243e31d-12cc-419b-9b17-4c51fab0289a"}}, {"head": {"id": "73a2f7d6-940f-4f91-a814-e6a308403cf0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243181718700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dac533b-705e-4442-8204-c11d155d78f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243182757600, "endTime": 19243182790500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "b5ea6374-75c6-43ec-875a-255c91a385e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5ea6374-75c6-43ec-875a-255c91a385e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243182757600, "endTime": 19243182790500}, "additional": {"logType": "info", "children": [], "durationId": "5dac533b-705e-4442-8204-c11d155d78f9"}}, {"head": {"id": "28102b0a-3f46-4e3c-b303-0d58225e9d5f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243182974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb1cdc6-3a62-4249-abb3-6d037cc4d28f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243184814500, "endTime": 19243184840300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f1d62dec-3973-4dc5-821c-92bd00cd91ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1d62dec-3973-4dc5-821c-92bd00cd91ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243184814500, "endTime": 19243184840300}, "additional": {"logType": "info", "children": [], "durationId": "4cb1cdc6-3a62-4249-abb3-6d037cc4d28f"}}, {"head": {"id": "06cf7329-13b9-4465-925c-0368a98029d9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243184979300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb6467cc-4549-4224-927d-9003cf0a0766", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243186444900, "endTime": 19243186466800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "d1f062bd-c234-41b2-9356-a11ae3dd0c8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1f062bd-c234-41b2-9356-a11ae3dd0c8f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243186444900, "endTime": 19243186466800}, "additional": {"logType": "info", "children": [], "durationId": "cb6467cc-4549-4224-927d-9003cf0a0766"}}, {"head": {"id": "b8c58b2a-247f-4671-a9d7-fbbfb3cec416", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243186624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb096a1-14a9-488c-9f7b-0db378f27412", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243188536800, "endTime": 19243188567800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0d91bfc2-baaa-4ba4-a705-0bdcfeb9db30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d91bfc2-baaa-4ba4-a705-0bdcfeb9db30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243188536800, "endTime": 19243188567800}, "additional": {"logType": "info", "children": [], "durationId": "adb096a1-14a9-488c-9f7b-0db378f27412"}}, {"head": {"id": "a19419ad-4d36-404e-81b7-fda72db579bd", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243426465500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa71d2a-1edc-4ca8-b0e3-afff1c2353e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243427500700, "endTime": 19243427521000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "dd68f3d3-5062-4973-8132-dc1486ee1153"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd68f3d3-5062-4973-8132-dc1486ee1153", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19243427500700, "endTime": 19243427521000}, "additional": {"logType": "info", "children": [], "durationId": "2aa71d2a-1edc-4ca8-b0e3-afff1c2353e2"}}, {"head": {"id": "7a665c67-4161-4a69-bd00-7b515f17ca5c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19361910206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f2e6fb-a561-40ac-be13-81c74de9774b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19361911729800, "endTime": 19361911761400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a345e337-287b-4e45-8905-918c12fc487a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a345e337-287b-4e45-8905-918c12fc487a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19361911729800, "endTime": 19361911761400}, "additional": {"logType": "info", "children": [], "durationId": "32f2e6fb-a561-40ac-be13-81c74de9774b"}}, {"head": {"id": "a212d71b-3017-4a81-b1c8-b99cd75e1b6a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363676486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34f3a40-5abe-429a-adf5-01996c0d9d94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363677965900, "endTime": 19363677991800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5d469eb8-afba-411b-af16-8628d65e2ef7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d469eb8-afba-411b-af16-8628d65e2ef7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363677965900, "endTime": 19363677991800}, "additional": {"logType": "info", "children": [], "durationId": "c34f3a40-5abe-429a-adf5-01996c0d9d94"}}, {"head": {"id": "cdf1c1dc-7b78-45d9-8a20-858e2c71754b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363678108500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ddc66b-d34a-4208-b5ae-aea8c4b6f85c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363679561900, "endTime": 19363679587000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "26bedac2-cb63-4a78-af3d-065faf07f2d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26bedac2-cb63-4a78-af3d-065faf07f2d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363679561900, "endTime": 19363679587000}, "additional": {"logType": "info", "children": [], "durationId": "17ddc66b-d34a-4208-b5ae-aea8c4b6f85c"}}, {"head": {"id": "cfd11f1d-6594-41fd-bfee-e4a8ac7f2c35", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363679699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e8d14b-e40e-487d-a7dc-a5d6a3dff872", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363681065400, "endTime": 19363681102900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "90cc37ad-9176-43b5-a3c3-bd3d71f91c79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90cc37ad-9176-43b5-a3c3-bd3d71f91c79", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363681065400, "endTime": 19363681102900}, "additional": {"logType": "info", "children": [], "durationId": "32e8d14b-e40e-487d-a7dc-a5d6a3dff872"}}, {"head": {"id": "979f5a15-ae44-4ce1-9846-0ee52be46bb6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363681226200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1238a3-7e3d-45ee-8795-6cf41a64463f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363682890400, "endTime": 19363682919100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "215d69f4-0516-4c8c-90fe-95caef672213"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "215d69f4-0516-4c8c-90fe-95caef672213", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363682890400, "endTime": 19363682919100}, "additional": {"logType": "info", "children": [], "durationId": "dc1238a3-7e3d-45ee-8795-6cf41a64463f"}}, {"head": {"id": "a92b3025-d52b-4ec5-b74a-8e449ffe4164", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363968949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5153c93a-5a97-47a7-a96c-dad9d8faac5b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363970423100, "endTime": 19363970456200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "cd4f0edc-83dd-414d-b315-5217cb615f41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd4f0edc-83dd-414d-b315-5217cb615f41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19363970423100, "endTime": 19363970456200}, "additional": {"logType": "info", "children": [], "durationId": "5153c93a-5a97-47a7-a96c-dad9d8faac5b"}}, {"head": {"id": "5fbee5f5-ec77-4217-87c1-ea9bb1196477", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19377241102700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87092f65-1640-40eb-b93e-721f5b666037", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19377242154000, "endTime": 19377242172600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "cbeb196a-9920-4ab8-8967-499615a43eb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbeb196a-9920-4ab8-8967-499615a43eb0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19377242154000, "endTime": 19377242172600}, "additional": {"logType": "info", "children": [], "durationId": "87092f65-1640-40eb-b93e-721f5b666037"}}, {"head": {"id": "15f4db63-a199-4798-afdc-f4aac84179e4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378774038100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e522414-b80d-4e6a-9512-d26bfc193038", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378775019400, "endTime": 19378775039200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a0b4ece9-c658-4280-856e-8cff70df97bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0b4ece9-c658-4280-856e-8cff70df97bb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378775019400, "endTime": 19378775039200}, "additional": {"logType": "info", "children": [], "durationId": "8e522414-b80d-4e6a-9512-d26bfc193038"}}, {"head": {"id": "9cc924a9-8cb1-42f6-b84b-8175f270f153", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378775121900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a74e1f-07a6-49f8-9014-838ca16b3592", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378777124800, "endTime": 19378777163900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "6c033603-440c-4016-9446-30aaaa0620a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c033603-440c-4016-9446-30aaaa0620a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19378777124800, "endTime": 19378777163900}, "additional": {"logType": "info", "children": [], "durationId": "11a74e1f-07a6-49f8-9014-838ca16b3592"}}, {"head": {"id": "5f67f45a-d85f-4571-afb3-8b95138c6799", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19379079119500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d8d4f1-3d7f-4551-a050-fed27483392c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19379080270300, "endTime": 19379080295900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4db5ec97-4e2c-420d-b344-1e73011dd79f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4db5ec97-4e2c-420d-b344-1e73011dd79f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19379080270300, "endTime": 19379080295900}, "additional": {"logType": "info", "children": [], "durationId": "34d8d4f1-3d7f-4551-a050-fed27483392c"}}, {"head": {"id": "ef785df6-e135-43cd-8b14-47c8e50adc91", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391170852400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e197a1-bdc6-40c8-a43d-56aa34615311", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391172083100, "endTime": 19391172110500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "64ff6de8-2b4f-4ff8-830a-1f9d7171b854"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64ff6de8-2b4f-4ff8-830a-1f9d7171b854", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391172083100, "endTime": 19391172110500}, "additional": {"logType": "info", "children": [], "durationId": "46e197a1-bdc6-40c8-a43d-56aa34615311"}}, {"head": {"id": "4405b6fc-2e6f-42b8-ba12-e160baa4693e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391979864700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16237292-ec79-49d7-8b10-a857533f9bcf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391980952400, "endTime": 19391980973400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "1ba267e6-47e8-4012-8c7e-7ad63203c4ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ba267e6-47e8-4012-8c7e-7ad63203c4ea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391980952400, "endTime": 19391980973400}, "additional": {"logType": "info", "children": [], "durationId": "16237292-ec79-49d7-8b10-a857533f9bcf"}}, {"head": {"id": "5a060f86-9718-4cb8-884a-10085dd13bc1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391981057200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3fd151e-2cd0-4777-ac51-d30bae7174e0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391981987200, "endTime": 19391982003100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "844670cc-496e-4d1c-b7a2-030cb30280a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "844670cc-496e-4d1c-b7a2-030cb30280a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19391981987200, "endTime": 19391982003100}, "additional": {"logType": "info", "children": [], "durationId": "c3fd151e-2cd0-4777-ac51-d30bae7174e0"}}, {"head": {"id": "880a2c31-059a-48ec-9c23-245e00625444", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19392241199500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942ea42f-ad74-48f6-b72f-035c74fec000", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19392242255900, "endTime": 19392242275300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a02a74c7-4c33-4dd5-b623-917de4527535"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a02a74c7-4c33-4dd5-b623-917de4527535", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19392242255900, "endTime": 19392242275300}, "additional": {"logType": "info", "children": [], "durationId": "942ea42f-ad74-48f6-b72f-035c74fec000"}}, {"head": {"id": "2ff19473-6ff9-4b1e-a544-44f09056aae8", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19406783738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0948e822-04cc-459f-a094-a61385aaa158", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19406784791800, "endTime": 19406784810400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "06087727-e47c-4dd9-a0d8-65d5ce4d6d88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06087727-e47c-4dd9-a0d8-65d5ce4d6d88", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19406784791800, "endTime": 19406784810400}, "additional": {"logType": "info", "children": [], "durationId": "0948e822-04cc-459f-a094-a61385aaa158"}}, {"head": {"id": "806bb4eb-557d-4cd0-9acb-c0dea2877f49", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407655177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3f9a47-6872-42bc-acfd-afd82722076c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407656257300, "endTime": 19407656291000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "753db683-d164-499d-b68e-a31306108ba7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "753db683-d164-499d-b68e-a31306108ba7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407656257300, "endTime": 19407656291000}, "additional": {"logType": "info", "children": [], "durationId": "4c3f9a47-6872-42bc-acfd-afd82722076c"}}, {"head": {"id": "2837abc8-9004-4945-a869-c2e457d42d26", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407656375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47e04e5-9b7b-4ee9-8794-baa6ed763c1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407657316200, "endTime": 19407657333000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5fd3308d-77f5-41cc-9924-b988a28bf404"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fd3308d-77f5-41cc-9924-b988a28bf404", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407657316200, "endTime": 19407657333000}, "additional": {"logType": "info", "children": [], "durationId": "c47e04e5-9b7b-4ee9-8794-baa6ed763c1f"}}, {"head": {"id": "677c4666-2fd6-450e-9888-ca2263eda019", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407940152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d28a81e8-6319-460f-9138-508e83e36431", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407941392500, "endTime": 19407941413400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a36f6177-f858-4e2b-8c0d-c68ba461d291"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a36f6177-f858-4e2b-8c0d-c68ba461d291", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19407941392500, "endTime": 19407941413400}, "additional": {"logType": "info", "children": [], "durationId": "d28a81e8-6319-460f-9138-508e83e36431"}}, {"head": {"id": "875d9ec7-5c6c-4f1d-bb4f-dd6f4b92ec50", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19418943005700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f5be87f-f797-4e12-b085-9cce5c772cba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19418944966900, "endTime": 19418944998900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4b69d4db-b91c-4f5e-86fe-4d9d380d1c68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b69d4db-b91c-4f5e-86fe-4d9d380d1c68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19418944966900, "endTime": 19418944998900}, "additional": {"logType": "info", "children": [], "durationId": "2f5be87f-f797-4e12-b085-9cce5c772cba"}}, {"head": {"id": "3f3fe609-0995-4f23-9696-7ead88713c60", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419755687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c14367d-82a3-42ce-bba8-01e41c7aea4e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419756880200, "endTime": 19419756910000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f057a874-6def-4f78-828e-e9c31c1d6641"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f057a874-6def-4f78-828e-e9c31c1d6641", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419756880200, "endTime": 19419756910000}, "additional": {"logType": "info", "children": [], "durationId": "4c14367d-82a3-42ce-bba8-01e41c7aea4e"}}, {"head": {"id": "4d4e49ca-ca33-4f82-ba9c-6437fb6215d5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419757030300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "782d79f8-ea10-4346-ac88-085c8ef8f874", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419758130300, "endTime": 19419758148400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "b139f149-3f28-4d0d-95a4-8faaa3f403d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b139f149-3f28-4d0d-95a4-8faaa3f403d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19419758130300, "endTime": 19419758148400}, "additional": {"logType": "info", "children": [], "durationId": "782d79f8-ea10-4346-ac88-085c8ef8f874"}}, {"head": {"id": "a1f8a2f6-3cdf-4b9a-9498-83341a063bdd", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19420014955800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb644f00-2c12-4c6b-ac5f-2d530d91f228", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19420016066500, "endTime": 19420016089500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7d44bf5e-2d10-47b4-8232-a8e403010876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d44bf5e-2d10-47b4-8232-a8e403010876", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19420016066500, "endTime": 19420016089500}, "additional": {"logType": "info", "children": [], "durationId": "bb644f00-2c12-4c6b-ac5f-2d530d91f228"}}, {"head": {"id": "b61a5c69-8b66-498d-bead-796fec7dd8bd", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19431735494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f8d382-4f87-4518-9a18-48f9f8d9e3eb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19431737500100, "endTime": 19431737534700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9fa50877-7d4a-45cf-a874-26e90559c35f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fa50877-7d4a-45cf-a874-26e90559c35f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19431737500100, "endTime": 19431737534700}, "additional": {"logType": "info", "children": [], "durationId": "98f8d382-4f87-4518-9a18-48f9f8d9e3eb"}}, {"head": {"id": "07f26ce5-47d8-4b0c-ba0a-e5af9ff57c0d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432549743300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76d203e-134f-4ccc-a867-79ae4c9a4152", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432550757400, "endTime": 19432550777700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "02e7d67b-24df-40f5-8c08-a7cb93d15239"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02e7d67b-24df-40f5-8c08-a7cb93d15239", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432550757400, "endTime": 19432550777700}, "additional": {"logType": "info", "children": [], "durationId": "a76d203e-134f-4ccc-a867-79ae4c9a4152"}}, {"head": {"id": "cf0071b8-2580-4cdc-903b-22ea16b16c74", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432550858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9d993b-2657-4103-b3a6-808f1da70f24", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432551694800, "endTime": 19432551710000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "fe2ff32f-adb9-4e30-8dfa-a077d71940b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe2ff32f-adb9-4e30-8dfa-a077d71940b8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432551694800, "endTime": 19432551710000}, "additional": {"logType": "info", "children": [], "durationId": "6a9d993b-2657-4103-b3a6-808f1da70f24"}}, {"head": {"id": "e99a1cd3-62a5-4104-8121-4dfa846e0978", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432853411700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656714fd-b3f3-49bb-a4d0-8cc14bd81c83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432854609600, "endTime": 19432854631100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c61a4253-f376-4b85-b66c-8885f6a78479"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c61a4253-f376-4b85-b66c-8885f6a78479", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19432854609600, "endTime": 19432854631100}, "additional": {"logType": "info", "children": [], "durationId": "656714fd-b3f3-49bb-a4d0-8cc14bd81c83"}}, {"head": {"id": "de8b5fc8-c650-44ba-ab42-ab56891411e0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713165257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f215a57c-203c-4aa8-a1ec-558aaef68521", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713189119000, "endTime": 19713552309000}, "additional": {"children": ["5a9cf5af-697d-4d45-b12a-99669a510816", "475466cc-84f4-4427-b3c2-906dfffbfcd3", "52b04ec5-645a-4e8b-87f4-f36153d2977a", "0057b0b6-44c3-4d80-89f2-9f0a37f7833b", "b438b1bb-54a8-41ce-8d4e-0bbb8984b904", "c378bc76-ffdb-474a-bfeb-4536c566b955", "b0204f43-e73e-413d-bd36-59f271f30c70"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a9cf5af-697d-4d45-b12a-99669a510816", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713189129300, "endTime": 19713202332900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "480addba-3d2b-4291-9a5b-d9fe1038ff95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713202370400, "endTime": 19713550539600}, "additional": {"children": ["28fb2c80-363d-4153-8e80-77899f4d820e", "827cffb7-6eb3-48f7-987b-39490d8405ba", "9258462c-fd8c-4b94-94b9-3e398f21f203", "6474071b-d47c-4452-9917-4cc41cccdffe", "a1b91981-d97b-4519-9f75-ed0887b95920", "0d7e28ca-b3cf-4458-b59d-0211cf34d706", "12281140-c61a-4034-af16-90d77ed59ed6", "5f99ad62-63ca-4b8c-a055-b82927944f85", "020de849-d2c1-4980-b247-2ddd57fed4c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52b04ec5-645a-4e8b-87f4-f36153d2977a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713550571500, "endTime": 19713552297000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "5961d04f-d12b-478e-bd17-395b2948a46b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0057b0b6-44c3-4d80-89f2-9f0a37f7833b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713552303200, "endTime": 19713552304600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "48911973-5f1d-4d72-bb1d-9e4a616ccf82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b438b1bb-54a8-41ce-8d4e-0bbb8984b904", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713192450400, "endTime": 19713192476200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "5e598dc2-83c4-4606-9b07-980fd3251c0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e598dc2-83c4-4606-9b07-980fd3251c0f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713192450400, "endTime": 19713192476200}, "additional": {"logType": "info", "children": [], "durationId": "b438b1bb-54a8-41ce-8d4e-0bbb8984b904", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "c378bc76-ffdb-474a-bfeb-4536c566b955", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713197577400, "endTime": 19713197595700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "048c6c40-437f-4696-adb5-2a27643eeb84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "048c6c40-437f-4696-adb5-2a27643eeb84", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713197577400, "endTime": 19713197595700}, "additional": {"logType": "info", "children": [], "durationId": "c378bc76-ffdb-474a-bfeb-4536c566b955", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "61dd62eb-76a7-42aa-bec9-19abb6d9e832", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713197641300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e957f01e-6a59-414b-a607-a531c54e66bf", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713201664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480addba-3d2b-4291-9a5b-d9fe1038ff95", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713189129300, "endTime": 19713202332900}, "additional": {"logType": "info", "children": [], "durationId": "5a9cf5af-697d-4d45-b12a-99669a510816", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "28fb2c80-363d-4153-8e80-77899f4d820e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713208304000, "endTime": 19713208312300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "55bd0a42-1d21-444e-9307-50bdc549a065"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "827cffb7-6eb3-48f7-987b-39490d8405ba", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713208324200, "endTime": 19713212131900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "3d14c6b5-4e1c-43d0-8e68-e015857c082e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9258462c-fd8c-4b94-94b9-3e398f21f203", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713212145100, "endTime": 19713454531800}, "additional": {"children": ["81280d68-5762-4d8b-92bb-d261ae1a8af8", "9ee1654c-a092-43eb-96ff-8c9b03349a06", "4b96b225-9bb4-452b-bb24-7054e29431a3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "9c04c38e-bfad-4391-8bea-9922e7fd2069"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6474071b-d47c-4452-9917-4cc41cccdffe", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713454552100, "endTime": 19713482265400}, "additional": {"children": ["a62b011a-91f2-40e2-8f14-592be87352ce"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "b1737684-ed21-4436-9a92-c59859071503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1b91981-d97b-4519-9f75-ed0887b95920", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713482272100, "endTime": 19713526978900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "479b0044-78f9-4e33-a8f0-c6b0e8806a91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d7e28ca-b3cf-4458-b59d-0211cf34d706", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713528057100, "endTime": 19713536169700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "6b268c62-eeed-4f3a-a5a6-dc399585bd63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12281140-c61a-4034-af16-90d77ed59ed6", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713536190500, "endTime": 19713550353800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "c79a8c1a-ad53-4ec5-83b0-a90657dff557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f99ad62-63ca-4b8c-a055-b82927944f85", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713550375500, "endTime": 19713550501000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "02f28769-c836-4d32-8e06-0203c657b36c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55bd0a42-1d21-444e-9307-50bdc549a065", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713208304000, "endTime": 19713208312300}, "additional": {"logType": "info", "children": [], "durationId": "28fb2c80-363d-4153-8e80-77899f4d820e", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "3d14c6b5-4e1c-43d0-8e68-e015857c082e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713208324200, "endTime": 19713212131900}, "additional": {"logType": "info", "children": [], "durationId": "827cffb7-6eb3-48f7-987b-39490d8405ba", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "81280d68-5762-4d8b-92bb-d261ae1a8af8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713213337300, "endTime": 19713213363200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9258462c-fd8c-4b94-94b9-3e398f21f203", "logId": "55717c6e-2f94-44a8-9361-7883757c0ee0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55717c6e-2f94-44a8-9361-7883757c0ee0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713213337300, "endTime": 19713213363200}, "additional": {"logType": "info", "children": [], "durationId": "81280d68-5762-4d8b-92bb-d261ae1a8af8", "parent": "9c04c38e-bfad-4391-8bea-9922e7fd2069"}}, {"head": {"id": "9ee1654c-a092-43eb-96ff-8c9b03349a06", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713215546500, "endTime": 19713453116700}, "additional": {"children": ["9107f948-773e-4d3c-884c-790dbaf47e1d", "817b9046-a054-462b-a98c-385a24b04bc0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9258462c-fd8c-4b94-94b9-3e398f21f203", "logId": "4f1c8c67-6415-49c5-9c19-ba43e29b2cb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9107f948-773e-4d3c-884c-790dbaf47e1d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713215548100, "endTime": 19713268470900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ee1654c-a092-43eb-96ff-8c9b03349a06", "logId": "e5cc8c8b-2b6a-4ba4-9779-47e14effaaa8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "817b9046-a054-462b-a98c-385a24b04bc0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713268491300, "endTime": 19713453100600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ee1654c-a092-43eb-96ff-8c9b03349a06", "logId": "bd0a17b0-177c-408b-9ac8-058f1205d93c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "785f0447-1a88-4dcc-a430-967366ce1cfd", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713215552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e07a1b0-086d-43d4-80bd-16558f8703d7", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713268314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5cc8c8b-2b6a-4ba4-9779-47e14effaaa8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713215548100, "endTime": 19713268470900}, "additional": {"logType": "info", "children": [], "durationId": "9107f948-773e-4d3c-884c-790dbaf47e1d", "parent": "4f1c8c67-6415-49c5-9c19-ba43e29b2cb8"}}, {"head": {"id": "6a50bc8c-b5ba-45ee-8c75-fbc3d36e5b34", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713268505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e196488-43d3-4eaf-94c1-75ad503ca556", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713277585300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91692b90-98a8-499e-b6db-404dbecf7c3b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713277703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1fbf4d-bb1c-450b-9be1-4582942ce947", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713277841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6ddbee-a91b-4064-a8c9-59bb7225740c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713277935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8633bad-3f68-4bc1-90fb-ce9386e64df5", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713280143800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c61ff2-952f-4ee3-aa8a-055a4ca18728", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713284000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75021a8-667b-455b-9639-599370f9fc49", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713295204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45576fbd-85b3-476a-9b4e-0471c786c838", "name": "Sdk init in 131 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713415581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1939c594-3722-427a-8169-38bc465b052e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713415706200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 18}, "markType": "other"}}, {"head": {"id": "4396aa67-7f65-4415-9b3e-255dcab7bb98", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713415718700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 18}, "markType": "other"}}, {"head": {"id": "ce56e55a-fa01-4a2d-b20b-f57afbb7b613", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713452808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9588f2-fdd0-4ca2-a308-925e2a83f976", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713452938400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26059dbc-8191-4573-ab49-de5fb2ed4953", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713453001200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d8d1af6-79a1-4f98-89a3-c719be680d02", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713453055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd0a17b0-177c-408b-9ac8-058f1205d93c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713268491300, "endTime": 19713453100600}, "additional": {"logType": "info", "children": [], "durationId": "817b9046-a054-462b-a98c-385a24b04bc0", "parent": "4f1c8c67-6415-49c5-9c19-ba43e29b2cb8"}}, {"head": {"id": "4f1c8c67-6415-49c5-9c19-ba43e29b2cb8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713215546500, "endTime": 19713453116700}, "additional": {"logType": "info", "children": ["e5cc8c8b-2b6a-4ba4-9779-47e14effaaa8", "bd0a17b0-177c-408b-9ac8-058f1205d93c"], "durationId": "9ee1654c-a092-43eb-96ff-8c9b03349a06", "parent": "9c04c38e-bfad-4391-8bea-9922e7fd2069"}}, {"head": {"id": "4b96b225-9bb4-452b-bb24-7054e29431a3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713454481100, "endTime": 19713454507400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9258462c-fd8c-4b94-94b9-3e398f21f203", "logId": "0ffb98a8-99ff-4869-b373-9cbe44f121a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ffb98a8-99ff-4869-b373-9cbe44f121a4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713454481100, "endTime": 19713454507400}, "additional": {"logType": "info", "children": [], "durationId": "4b96b225-9bb4-452b-bb24-7054e29431a3", "parent": "9c04c38e-bfad-4391-8bea-9922e7fd2069"}}, {"head": {"id": "9c04c38e-bfad-4391-8bea-9922e7fd2069", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713212145100, "endTime": 19713454531800}, "additional": {"logType": "info", "children": ["55717c6e-2f94-44a8-9361-7883757c0ee0", "4f1c8c67-6415-49c5-9c19-ba43e29b2cb8", "0ffb98a8-99ff-4869-b373-9cbe44f121a4"], "durationId": "9258462c-fd8c-4b94-94b9-3e398f21f203", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "a62b011a-91f2-40e2-8f14-592be87352ce", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713455684300, "endTime": 19713482253500}, "additional": {"children": ["42266f9e-608c-4f58-b45c-d7a940cc8115", "955f13c7-e621-4d2f-8add-b162f84af850", "d22e65df-7f81-4374-8536-7d36671d3e39"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6474071b-d47c-4452-9917-4cc41cccdffe", "logId": "dd6e037b-99ec-4aa0-aecb-522c3269df41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42266f9e-608c-4f58-b45c-d7a940cc8115", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713460908900, "endTime": 19713460931700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a62b011a-91f2-40e2-8f14-592be87352ce", "logId": "920a7ae7-8ddd-4005-823f-82d1f4df1486"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "920a7ae7-8ddd-4005-823f-82d1f4df1486", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713460908900, "endTime": 19713460931700}, "additional": {"logType": "info", "children": [], "durationId": "42266f9e-608c-4f58-b45c-d7a940cc8115", "parent": "dd6e037b-99ec-4aa0-aecb-522c3269df41"}}, {"head": {"id": "955f13c7-e621-4d2f-8add-b162f84af850", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713463292800, "endTime": 19713480178100}, "additional": {"children": ["cda19924-60ee-4581-bbe6-bcf0748b5bdc", "f5836368-a61e-4250-b098-f5a4df3a6ba2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a62b011a-91f2-40e2-8f14-592be87352ce", "logId": "a29759d3-4a9b-4c4f-8d54-57dbb04f83bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cda19924-60ee-4581-bbe6-bcf0748b5bdc", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713463295400, "endTime": 19713467058800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "955f13c7-e621-4d2f-8add-b162f84af850", "logId": "fc10bf8f-39e7-42ee-90ec-486323a61422"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5836368-a61e-4250-b098-f5a4df3a6ba2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713467075300, "endTime": 19713480165800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "955f13c7-e621-4d2f-8add-b162f84af850", "logId": "e79b13d0-f144-42bd-adad-b78c0b7c9c3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61166c4d-d5b0-4f54-82ca-1e65cc5dcd10", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713463302200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "285ee926-4df3-400c-af78-5167941b98a9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713466934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc10bf8f-39e7-42ee-90ec-486323a61422", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713463295400, "endTime": 19713467058800}, "additional": {"logType": "info", "children": [], "durationId": "cda19924-60ee-4581-bbe6-bcf0748b5bdc", "parent": "a29759d3-4a9b-4c4f-8d54-57dbb04f83bb"}}, {"head": {"id": "5ea56173-c159-41b5-bf2b-4f64bdee408e", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713467085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd3f459-91ca-4200-bdd4-0a08deb6bd34", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713476317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a95d2e9-c96a-40c5-b61c-0dde55a2454a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713476450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee945a13-397f-46c8-a997-99336f13e977", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713476648100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0f26f6-56fa-4171-91bf-a2903ea056ab", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713476832200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9dfa589-eaea-4c72-b9d7-c5dabb9b6398", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713476948700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4920e5df-3629-4c21-8ea9-584f8b2f91e8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713477014300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60e7236b-28a0-4a7c-8e11-8a5cabd17dca", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713477064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c196299-caaa-43bb-983c-981b0d36c415", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713479902000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b49a62-a0ce-4058-bdc1-f14a9b8b9b21", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713480019400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe7a51e-0454-4cd7-b296-e4a7263aa18b", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713480079700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2092f350-b553-4456-bfec-d17760a85dc7", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713480124300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e79b13d0-f144-42bd-adad-b78c0b7c9c3b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713467075300, "endTime": 19713480165800}, "additional": {"logType": "info", "children": [], "durationId": "f5836368-a61e-4250-b098-f5a4df3a6ba2", "parent": "a29759d3-4a9b-4c4f-8d54-57dbb04f83bb"}}, {"head": {"id": "a29759d3-4a9b-4c4f-8d54-57dbb04f83bb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713463292800, "endTime": 19713480178100}, "additional": {"logType": "info", "children": ["fc10bf8f-39e7-42ee-90ec-486323a61422", "e79b13d0-f144-42bd-adad-b78c0b7c9c3b"], "durationId": "955f13c7-e621-4d2f-8add-b162f84af850", "parent": "dd6e037b-99ec-4aa0-aecb-522c3269df41"}}, {"head": {"id": "d22e65df-7f81-4374-8536-7d36671d3e39", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713482226500, "endTime": 19713482239200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a62b011a-91f2-40e2-8f14-592be87352ce", "logId": "1f8ecf16-9a33-489c-b085-570064b7be73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f8ecf16-9a33-489c-b085-570064b7be73", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713482226500, "endTime": 19713482239200}, "additional": {"logType": "info", "children": [], "durationId": "d22e65df-7f81-4374-8536-7d36671d3e39", "parent": "dd6e037b-99ec-4aa0-aecb-522c3269df41"}}, {"head": {"id": "dd6e037b-99ec-4aa0-aecb-522c3269df41", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713455684300, "endTime": 19713482253500}, "additional": {"logType": "info", "children": ["920a7ae7-8ddd-4005-823f-82d1f4df1486", "a29759d3-4a9b-4c4f-8d54-57dbb04f83bb", "1f8ecf16-9a33-489c-b085-570064b7be73"], "durationId": "a62b011a-91f2-40e2-8f14-592be87352ce", "parent": "b1737684-ed21-4436-9a92-c59859071503"}}, {"head": {"id": "b1737684-ed21-4436-9a92-c59859071503", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713454552100, "endTime": 19713482265400}, "additional": {"logType": "info", "children": ["dd6e037b-99ec-4aa0-aecb-522c3269df41"], "durationId": "6474071b-d47c-4452-9917-4cc41cccdffe", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "22c6cc1b-d96e-45dd-9c4c-1560e7934cf4", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713513231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b26cb64-8d26-4f17-a98b-fc45c7df58bd", "name": "hvigorfile, resolve hvigorfile dependencies in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713513559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479b0044-78f9-4e33-a8f0-c6b0e8806a91", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713482272100, "endTime": 19713526978900}, "additional": {"logType": "info", "children": [], "durationId": "a1b91981-d97b-4519-9f75-ed0887b95920", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "020de849-d2c1-4980-b247-2ddd57fed4c4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713527879400, "endTime": 19713528047700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "logId": "fe45c92d-22d7-4f3e-9d46-650158b92b8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca5136c9-d49a-42f2-a5f6-010363cd49a9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713527899200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe45c92d-22d7-4f3e-9d46-650158b92b8a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713527879400, "endTime": 19713528047700}, "additional": {"logType": "info", "children": [], "durationId": "020de849-d2c1-4980-b247-2ddd57fed4c4", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "a13d7e8a-57d0-4f53-aca3-bd6b54a45fac", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713529392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36870ea4-6d83-4ffc-aa49-6f970c22685c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713535324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b268c62-eeed-4f3a-a5a6-dc399585bd63", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713528057100, "endTime": 19713536169700}, "additional": {"logType": "info", "children": [], "durationId": "0d7e28ca-b3cf-4458-b59d-0211cf34d706", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "ae4e058f-cf09-4375-8782-08ef60b0b835", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713536201000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e03b01d-9917-4605-ab0a-c654d2070c02", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713542669500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5366a9f5-0087-4904-abac-341351086d92", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713542807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f2dc70-f9ca-4af7-ba29-6205669cc87f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713543053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4fb9fb-9a82-426b-808e-dd76f2334bd8", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713546351100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4968e1-1608-4d3b-a9a1-fce32a99974e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713546477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c79a8c1a-ad53-4ec5-83b0-a90657dff557", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713536190500, "endTime": 19713550353800}, "additional": {"logType": "info", "children": [], "durationId": "12281140-c61a-4034-af16-90d77ed59ed6", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "5a3fdb4b-e3fe-4f42-811c-1c690fc79493", "name": "Configuration phase cost:343 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713550402100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02f28769-c836-4d32-8e06-0203c657b36c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713550375500, "endTime": 19713550501000}, "additional": {"logType": "info", "children": [], "durationId": "5f99ad62-63ca-4b8c-a055-b82927944f85", "parent": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1"}}, {"head": {"id": "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713202370400, "endTime": 19713550539600}, "additional": {"logType": "info", "children": ["55bd0a42-1d21-444e-9307-50bdc549a065", "3d14c6b5-4e1c-43d0-8e68-e015857c082e", "9c04c38e-bfad-4391-8bea-9922e7fd2069", "b1737684-ed21-4436-9a92-c59859071503", "479b0044-78f9-4e33-a8f0-c6b0e8806a91", "6b268c62-eeed-4f3a-a5a6-dc399585bd63", "c79a8c1a-ad53-4ec5-83b0-a90657dff557", "02f28769-c836-4d32-8e06-0203c657b36c", "fe45c92d-22d7-4f3e-9d46-650158b92b8a"], "durationId": "475466cc-84f4-4427-b3c2-906dfffbfcd3", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "b0204f43-e73e-413d-bd36-59f271f30c70", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713552269500, "endTime": 19713552285700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f215a57c-203c-4aa8-a1ec-558aaef68521", "logId": "e24fbc22-3b91-4b50-a265-10bce5a5cf78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e24fbc22-3b91-4b50-a265-10bce5a5cf78", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713552269500, "endTime": 19713552285700}, "additional": {"logType": "info", "children": [], "durationId": "b0204f43-e73e-413d-bd36-59f271f30c70", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "5961d04f-d12b-478e-bd17-395b2948a46b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713550571500, "endTime": 19713552297000}, "additional": {"logType": "info", "children": [], "durationId": "52b04ec5-645a-4e8b-87f4-f36153d2977a", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "48911973-5f1d-4d72-bb1d-9e4a616ccf82", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713552303200, "endTime": 19713552304600}, "additional": {"logType": "info", "children": [], "durationId": "0057b0b6-44c3-4d80-89f2-9f0a37f7833b", "parent": "d3a8fb22-5a29-4717-a899-b9a5bd48e451"}}, {"head": {"id": "d3a8fb22-5a29-4717-a899-b9a5bd48e451", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713189119000, "endTime": 19713552309000}, "additional": {"logType": "info", "children": ["480addba-3d2b-4291-9a5b-d9fe1038ff95", "1e5a2ca6-18f3-4984-8ad3-acdd1d78bbc1", "5961d04f-d12b-478e-bd17-395b2948a46b", "48911973-5f1d-4d72-bb1d-9e4a616ccf82", "5e598dc2-83c4-4606-9b07-980fd3251c0f", "048c6c40-437f-4696-adb5-2a27643eeb84", "e24fbc22-3b91-4b50-a265-10bce5a5cf78"], "durationId": "f215a57c-203c-4aa8-a1ec-558aaef68521"}}, {"head": {"id": "4df5be46-e6c8-4473-b432-577043<PERSON>ca25", "name": "Configuration task cost before running: 384 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713552454800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f22ae48-1b37-4b3f-9a02-cb747316a82e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713559656700, "endTime": 19713575181600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a1d9d98a-ac7e-41be-8cbf-91dab1ed54c5", "logId": "f615f91a-12b6-426f-b872-1ee7d039ed9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1d9d98a-ac7e-41be-8cbf-91dab1ed54c5", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713554519200}, "additional": {"logType": "detail", "children": [], "durationId": "3f22ae48-1b37-4b3f-9a02-cb747316a82e"}}, {"head": {"id": "9b40b04f-5b74-4a2f-bd37-d33b9dbdbad5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713555130000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae846c5f-b116-4628-9206-9ba6944915bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713555259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e233e81-6bf0-4048-b156-3d1fd78f198e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713559673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1a269b0-507d-4641-a6aa-13a4d9cf69e5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713568863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded4f9f3-8102-4cc6-803c-9601e3ecf0f8", "name": "entry : default@PreBuild cost memory 0.38545989990234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713574983400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f615f91a-12b6-426f-b872-1ee7d039ed9b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713559656700, "endTime": 19713575181600}, "additional": {"logType": "info", "children": [], "durationId": "3f22ae48-1b37-4b3f-9a02-cb747316a82e"}}, {"head": {"id": "aaecca1a-9552-48ba-afc2-aea910606963", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713581480000, "endTime": 19713586233600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c9df4040-20e4-4563-97fe-3987a1a8cea7", "logId": "0465cca1-811f-46c8-b6ad-b00dfbb797e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9df4040-20e4-4563-97fe-3987a1a8cea7", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713579575200}, "additional": {"logType": "detail", "children": [], "durationId": "aaecca1a-9552-48ba-afc2-aea910606963"}}, {"head": {"id": "3c61e8f0-fd9e-4a80-9026-373b03dc4ad9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713580343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5d3ed8-565e-491f-92dd-86543114de14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713580465600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a8e94b-d678-4c18-90fc-191941f8f592", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713581492400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c1be4d-276c-4223-b4a7-a0617c0ce6e1", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713584304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4625fdf8-d7f7-408f-9ed1-8b1815150d68", "name": "entry : default@MergeProfile cost memory 0.1100006103515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713586110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0465cca1-811f-46c8-b6ad-b00dfbb797e2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713581480000, "endTime": 19713586233600}, "additional": {"logType": "info", "children": [], "durationId": "aaecca1a-9552-48ba-afc2-aea910606963"}}, {"head": {"id": "bcd83d6e-cbdc-4a07-a8dd-b409864e0bfc", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713589372700, "endTime": 19713591908800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7af37f2d-dc5b-4ed2-a764-5112dc37181d", "logId": "649215d1-ce68-4fbe-b30a-c9eed1a38696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7af37f2d-dc5b-4ed2-a764-5112dc37181d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713588072200}, "additional": {"logType": "detail", "children": [], "durationId": "bcd83d6e-cbdc-4a07-a8dd-b409864e0bfc"}}, {"head": {"id": "f7686738-fd31-4090-bad0-b70595844c60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713588568900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79871cc7-6cd2-4cbf-966e-37cbcc864508", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713588659400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6ae95c-f927-4d5c-a6bb-3a47b4a53e11", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713589382400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189cd6a1-e049-4297-9314-7434d9ab772d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713590261500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf70b9d0-1c0c-4efd-83e1-79bb19f2e281", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713591706700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122168e4-244d-4f02-b55d-19f11f1be6fc", "name": "entry : default@CreateBuildProfile cost memory 0.095062255859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713591830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649215d1-ce68-4fbe-b30a-c9eed1a38696", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713589372700, "endTime": 19713591908800}, "additional": {"logType": "info", "children": [], "durationId": "bcd83d6e-cbdc-4a07-a8dd-b409864e0bfc"}}, {"head": {"id": "280a2876-685b-49ad-8407-75b1be4dbad8", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595004000, "endTime": 19713595450200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "20429644-be14-4278-8004-b523a9ec2d40", "logId": "8b16004a-85f2-431b-8be0-e771765b3ee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20429644-be14-4278-8004-b523a9ec2d40", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713593522000}, "additional": {"logType": "detail", "children": [], "durationId": "280a2876-685b-49ad-8407-75b1be4dbad8"}}, {"head": {"id": "b34f9049-41ec-4a3e-a4e1-bf41a1c806a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713594003700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80770eb4-12c0-4d77-acb6-e6757bbb4b44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713594089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "577692a8-298e-4b06-a7e3-d6cab6153e9f", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da23d30b-234b-434c-b89c-a7af448756c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595145000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c107c140-7423-4c3f-a415-1b492bdbd2c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595220000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c591dfe4-c6f1-4007-8787-7eed3aa0318a", "name": "entry : default@PreCheckSyscap cost memory 0.0366363525390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595305900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f970e2a-b2da-4f03-af11-d22183421eee", "name": "runTaskFromQueue task cost before running: 427 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b16004a-85f2-431b-8be0-e771765b3ee9", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713595004000, "endTime": 19713595450200, "totalTime": 369400}, "additional": {"logType": "info", "children": [], "durationId": "280a2876-685b-49ad-8407-75b1be4dbad8"}}, {"head": {"id": "bd3ee127-a877-4264-9635-ac7d8eb872fa", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713605485900, "endTime": 19713606491900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7f88605b-51b6-443c-a8dc-96c29ea70516", "logId": "79d64c55-0ac3-49e1-8ecb-866067862d74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f88605b-51b6-443c-a8dc-96c29ea70516", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713597404700}, "additional": {"logType": "detail", "children": [], "durationId": "bd3ee127-a877-4264-9635-ac7d8eb872fa"}}, {"head": {"id": "73ad6162-95fb-4c2e-9ee9-6ae0903ef816", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713597959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f17d2a6-ce28-47ad-a99d-a9ddcacd476d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713598073100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b597ca-03cc-4145-a5e3-5d9bc87292de", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713605500200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17517d87-f6cb-4c78-8fc1-f66d728b3766", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713605698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e49ab7c-6ea5-4232-91bc-8c566c482b7d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713606339400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84858488-82cc-4a09-8a42-23736409c7f5", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06481170654296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713606427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d64c55-0ac3-49e1-8ecb-866067862d74", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713605485900, "endTime": 19713606491900}, "additional": {"logType": "info", "children": [], "durationId": "bd3ee127-a877-4264-9635-ac7d8eb872fa"}}, {"head": {"id": "8dde914b-ed8a-40c2-a48e-9218a9d89dd9", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713610297700, "endTime": 19713611483200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "50472226-66c8-4612-9452-d5d824929dde", "logId": "38b98867-3fc1-4d94-a796-3a207892fa74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50472226-66c8-4612-9452-d5d824929dde", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713608437800}, "additional": {"logType": "detail", "children": [], "durationId": "8dde914b-ed8a-40c2-a48e-9218a9d89dd9"}}, {"head": {"id": "3051774d-4542-4cce-a645-3fa5a5795ba8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713609008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b68ddb2-8060-4606-8ff9-9b1e3366b609", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713609119400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449a8a4e-4452-4cb5-b5d7-5a1e18efb031", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713610307900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f182d7e1-d52d-40d6-afc5-b5c653becf15", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713611335900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7427c643-60c6-4fea-b1f5-072e3b7a41dc", "name": "entry : default@ProcessProfile cost memory 0.0554656982421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713611421000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38b98867-3fc1-4d94-a796-3a207892fa74", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713610297700, "endTime": 19713611483200}, "additional": {"logType": "info", "children": [], "durationId": "8dde914b-ed8a-40c2-a48e-9218a9d89dd9"}}, {"head": {"id": "609f4630-1521-4948-8ce9-e952cb918833", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713616611300, "endTime": 19713622674000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1420bb19-24ca-44cd-a957-686a6172aeac", "logId": "6b5631ff-703a-4681-8fbb-2ab760c90ca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1420bb19-24ca-44cd-a957-686a6172aeac", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713613734100}, "additional": {"logType": "detail", "children": [], "durationId": "609f4630-1521-4948-8ce9-e952cb918833"}}, {"head": {"id": "e224b72a-79ff-4d14-ba1b-22bc25b371bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713614340700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b11d42-9ed6-4868-a23f-a8fc146772ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713614472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd4f482-f0da-486e-81ad-d2a387f77074", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713616623800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3846c149-7819-4eb4-b7ed-2d6b9158df52", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713622480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "773a9c00-3670-40c0-a364-039308386fe7", "name": "entry : default@ProcessRouterMap cost memory 0.18602752685546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713622602300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5631ff-703a-4681-8fbb-2ab760c90ca2", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713616611300, "endTime": 19713622674000}, "additional": {"logType": "info", "children": [], "durationId": "609f4630-1521-4948-8ce9-e952cb918833"}}, {"head": {"id": "a38b121c-83e8-4d32-9b83-0202a6f4de50", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713630428800, "endTime": 19713633972900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "eaa96a03-8ca5-4b94-9b27-9502afa4cb98", "logId": "08808201-40e4-4356-abeb-3235476286fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaa96a03-8ca5-4b94-9b27-9502afa4cb98", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713625765800}, "additional": {"logType": "detail", "children": [], "durationId": "a38b121c-83e8-4d32-9b83-0202a6f4de50"}}, {"head": {"id": "73f3e12c-be69-45ab-81be-4d01a455b4d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713626398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e653dd3-f7b0-4221-8032-d62442030f7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713626524000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad9bd4f-4917-4f8a-921f-6349754bc714", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713627739500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65cd2aa-69f4-432a-8dc2-e1412a7f12ca", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713631863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d76e73-ceb7-448c-91f5-960b5b56d070", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713632034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f55cf4ba-3014-460b-8180-37ae720555f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713632114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd5f2446-6d96-439d-be23-8f67960d675f", "name": "entry : default@PreviewProcessResource cost memory 0.06746673583984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713632219500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222721c3-8675-4bbf-bef6-173b147378df", "name": "runTaskFromQueue task cost before running: 466 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713633856200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08808201-40e4-4356-abeb-3235476286fe", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713630428800, "endTime": 19713633972900, "totalTime": 1871900}, "additional": {"logType": "info", "children": [], "durationId": "a38b121c-83e8-4d32-9b83-0202a6f4de50"}}, {"head": {"id": "0ebb80ef-b4ab-4c18-88b6-2fa657f3fb11", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713644254300, "endTime": 19713669766400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3d95b45b-6685-40c6-9c96-4d7abf317816", "logId": "bda88b7b-bd3a-4514-afcb-2430e00655c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d95b45b-6685-40c6-9c96-4d7abf317816", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713637720400}, "additional": {"logType": "detail", "children": [], "durationId": "0ebb80ef-b4ab-4c18-88b6-2fa657f3fb11"}}, {"head": {"id": "b80f4d14-60f1-474b-bb59-47cf69cbbcb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713638418800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af31e20-cf2f-4ff7-9cb2-27b4bafbd882", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713638552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f9774c2-9784-4ec6-be8a-a89931e22a81", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713644269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f4fe96-a853-425c-ac39-21d612e8b1f0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713669570500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf9687c-880d-4309-8b4e-f0bbcbf62f49", "name": "entry : default@GenerateLoaderJson cost memory -1.025299072265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713669694800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda88b7b-bd3a-4514-afcb-2430e00655c0", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713644254300, "endTime": 19713669766400}, "additional": {"logType": "info", "children": [], "durationId": "0ebb80ef-b4ab-4c18-88b6-2fa657f3fb11"}}, {"head": {"id": "aef5a161-5503-4000-8262-327093bdb21e", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713684295900, "endTime": 19713710959500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6a66ff03-d0bc-456a-9a55-8cdd13c07fe2", "logId": "61f3167a-9661-43ed-b3f6-c69d0dfe0847"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a66ff03-d0bc-456a-9a55-8cdd13c07fe2", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713678963700}, "additional": {"logType": "detail", "children": [], "durationId": "aef5a161-5503-4000-8262-327093bdb21e"}}, {"head": {"id": "bd2d1efb-146d-45cd-8b96-9752407d17ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713679613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9f94e8-a825-4505-9eb7-bea786ea2ac4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713679726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4414ad79-ec12-4b43-8e8c-bcdb99102285", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713681049000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2306d0a-4c90-43c7-9e4e-ce79ef3f2a22", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713684327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7af87bc-6424-4754-8619-d264b1b9dc03", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713710623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed092c7-e831-496a-a4a5-91e554e1385b", "name": "entry : default@PreviewCompileResource cost memory 0.70001220703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713710830300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f3167a-9661-43ed-b3f6-c69d0dfe0847", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713684295900, "endTime": 19713710959500}, "additional": {"logType": "info", "children": [], "durationId": "aef5a161-5503-4000-8262-327093bdb21e"}}, {"head": {"id": "be674e68-c107-4629-ae6a-3f9ab87516a7", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715270600, "endTime": 19713715709200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "83373ef9-dda5-420e-8dd2-31380f1f5433", "logId": "a0bd3be7-632f-46d0-90d0-7fca286ddc24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83373ef9-dda5-420e-8dd2-31380f1f5433", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713714379800}, "additional": {"logType": "detail", "children": [], "durationId": "be674e68-c107-4629-ae6a-3f9ab87516a7"}}, {"head": {"id": "4b6ed38c-8c6d-4b0b-9b49-e4dcc659f86c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715040000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78474359-7d30-4b4c-ab14-2c536d468179", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715161200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd685215-6eab-4419-b8cc-7150c42c52fd", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715281400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982051e6-04c5-4160-9106-a0f982c45c6a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715393600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19ecee1a-3ca2-4a17-8b8b-44a4e7fd590a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db3a19e-9f02-4877-aabd-5d939580ea52", "name": "entry : default@PreviewHookCompileResource cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66746939-3b3d-4255-9160-bbbed4b6e6b4", "name": "runTaskFromQueue task cost before running: 547 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0bd3be7-632f-46d0-90d0-7fca286ddc24", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713715270600, "endTime": 19713715709200, "totalTime": 349500}, "additional": {"logType": "info", "children": [], "durationId": "be674e68-c107-4629-ae6a-3f9ab87516a7"}}, {"head": {"id": "7c98edfe-f8e4-4eb2-b504-367b0e0e6ec7", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713719788900, "endTime": 19713723442000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "10d7d633-32c6-4172-ac16-a8e6f421c701", "logId": "9d367dce-28a2-4f52-9268-4d8ffa35e5e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10d7d633-32c6-4172-ac16-a8e6f421c701", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713717991700}, "additional": {"logType": "detail", "children": [], "durationId": "7c98edfe-f8e4-4eb2-b504-367b0e0e6ec7"}}, {"head": {"id": "d9893f9a-12fe-4b29-9c6c-ebc98caa0e68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713718693200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f512a3d-033c-42e4-9df9-d3cad6b5ab82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713718824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfdbc838-da50-4403-9a04-7cb176d81eec", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713719806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0baf794c-7f57-441e-98a7-5d918f561f60", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713723175300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5dfd1f-480a-443b-92d6-7e38901d09ab", "name": "entry : default@CopyPreviewProfile cost memory 0.09454345703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713723333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d367dce-28a2-4f52-9268-4d8ffa35e5e6", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713719788900, "endTime": 19713723442000}, "additional": {"logType": "info", "children": [], "durationId": "7c98edfe-f8e4-4eb2-b504-367b0e0e6ec7"}}, {"head": {"id": "2ef2fe70-cdc8-4573-8e24-491034461ffa", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713728800400, "endTime": 19713729173000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "dc3887c6-c585-4fac-913d-9991d4b9889e", "logId": "66b69ca0-cd8f-42a6-a754-669eb7e614b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc3887c6-c585-4fac-913d-9991d4b9889e", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713727214600}, "additional": {"logType": "detail", "children": [], "durationId": "2ef2fe70-cdc8-4573-8e24-491034461ffa"}}, {"head": {"id": "e9fd1132-0035-4ba3-bb40-0ace7923d07a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713727863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49b9645-95a0-4cf6-84aa-7ea09e361f47", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713727977800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e65b1d-bbff-4b1d-8a7d-30b38c257873", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713728808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacec3f3-b0ac-4233-8412-e91d7506fed5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713728907400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b4f7221-97a3-42b9-8b03-5bbde3a61479", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713728960000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e68a42-20c4-4b78-a1b3-9f033986ed2f", "name": "entry : default@ReplacePreviewerPage cost memory 0.03769683837890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713729045800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc95c78-3971-4554-85a0-8b4b3c891e62", "name": "runTaskFromQueue task cost before running: 561 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713729121600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b69ca0-cd8f-42a6-a754-669eb7e614b3", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713728800400, "endTime": 19713729173000, "totalTime": 305100}, "additional": {"logType": "info", "children": [], "durationId": "2ef2fe70-cdc8-4573-8e24-491034461ffa"}}, {"head": {"id": "06aa61f4-637b-4dfc-be93-39d4f568155c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713730917200, "endTime": 19713731191300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "faf06cf9-e0ec-4288-a901-92fa6920dfca", "logId": "dfbb7219-b9f9-4461-aa58-54c996ce30fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "faf06cf9-e0ec-4288-a901-92fa6920dfca", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713730869800}, "additional": {"logType": "detail", "children": [], "durationId": "06aa61f4-637b-4dfc-be93-39d4f568155c"}}, {"head": {"id": "1f9a44e1-2df9-4e5e-ade0-98f8ae1a9745", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713730925400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb6a60e-e85b-4308-abd4-5772002c7fdd", "name": "entry : buildPreviewerResource cost memory 0.01158905029296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713731048400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420b3535-fd52-4743-9376-7b95b09aea13", "name": "runTaskFromQueue task cost before running: 563 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713731134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfbb7219-b9f9-4461-aa58-54c996ce30fd", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713730917200, "endTime": 19713731191300, "totalTime": 199100}, "additional": {"logType": "info", "children": [], "durationId": "06aa61f4-637b-4dfc-be93-39d4f568155c"}}, {"head": {"id": "b3dac9af-57ba-426c-a2b1-b93e9e63162e", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713734214200, "endTime": 19713736790200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a50935b9-a45d-4e60-a012-b4cbd80d297e", "logId": "29d891e1-7492-4618-bbc8-521477350a66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a50935b9-a45d-4e60-a012-b4cbd80d297e", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713732851800}, "additional": {"logType": "detail", "children": [], "durationId": "b3dac9af-57ba-426c-a2b1-b93e9e63162e"}}, {"head": {"id": "77bf8d3d-6b7a-4282-b711-21c17d6a6353", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713733380800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d34f7c4-5f81-423a-9d55-664ba5c939ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713733472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a7b8b55-23cf-4d1d-b77d-c099548e61e0", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713734222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b5d51a-7c25-42fe-9cd7-a116d6468deb", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713736610700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d9f17a-d042-4637-accd-cfb99419e3fe", "name": "entry : default@PreviewUpdateAssets cost memory 0.11058807373046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713736723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d891e1-7492-4618-bbc8-521477350a66", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713734214200, "endTime": 19713736790200}, "additional": {"logType": "info", "children": [], "durationId": "b3dac9af-57ba-426c-a2b1-b93e9e63162e"}}, {"head": {"id": "f8862762-c3aa-4a9d-98b5-1b285861ac59", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713745078700, "endTime": 19731973925200}, "additional": {"children": ["8f4f2976-d42c-4dd9-9d51-ce681bf6ac90"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1e6ac4cf-9492-4b5c-9a31-edfe6a7673c1", "logId": "737fc9c0-a790-4469-af53-8733c7d15822"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e6ac4cf-9492-4b5c-9a31-edfe6a7673c1", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713739060800}, "additional": {"logType": "detail", "children": [], "durationId": "f8862762-c3aa-4a9d-98b5-1b285861ac59"}}, {"head": {"id": "713a9cd3-9ddc-450e-9e79-2d2d532bd787", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713739553000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fd49bd-92af-44db-b594-63b114e79cd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713739645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7cdbe2-4487-4c42-a09c-d8b84009f03a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713745091900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker17", "startTime": 19713764867000, "endTime": 19731973712900}, "additional": {"children": ["a349ba8e-e5d0-48f1-b23e-18df79ca75fd", "8d8843de-32be-4c9d-9774-1e7e97863917", "0d6eb6d9-8dd3-4ef6-b330-33603317d735", "1a7012df-c75f-4302-bbe0-5b053abe0049"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f8862762-c3aa-4a9d-98b5-1b285861ac59", "logId": "f9e053d8-4a12-48aa-8d95-06d13b26af6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7678bc5-eed4-4de8-b7a6-0e87a534a13c", "name": "entry : default@PreviewArkTS cost memory -0.6504592895507812", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713767119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42a111e-9e2a-4306-acc7-0c83bc3c3b1f", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19718845873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a349ba8e-e5d0-48f1-b23e-18df79ca75fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19718847143200, "endTime": 19718847177200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "logId": "7cb8adc0-2501-4c73-b166-5b4e3cc92454"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cb8adc0-2501-4c73-b166-5b4e3cc92454", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19718847143200, "endTime": 19718847177200}, "additional": {"logType": "info", "children": [], "durationId": "a349ba8e-e5d0-48f1-b23e-18df79ca75fd", "parent": "f9e053d8-4a12-48aa-8d95-06d13b26af6d"}}, {"head": {"id": "e3c0b41b-5b9c-45e4-bdbd-9194f9538f0b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727258762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d8843de-32be-4c9d-9774-1e7e97863917", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727261206500, "endTime": 19727261243600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "logId": "5759418e-db80-4f5d-bc1b-e96429ea50af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5759418e-db80-4f5d-bc1b-e96429ea50af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727261206500, "endTime": 19727261243600}, "additional": {"logType": "info", "children": [], "durationId": "8d8843de-32be-4c9d-9774-1e7e97863917", "parent": "f9e053d8-4a12-48aa-8d95-06d13b26af6d"}}, {"head": {"id": "f1c66fd4-c655-4f0e-9099-a9abb78f10ad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727261455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6eb6d9-8dd3-4ef6-b330-33603317d735", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727264073600, "endTime": 19727264115900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "logId": "8a82b3d0-4d68-48c7-9cd0-60a04285f411"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a82b3d0-4d68-48c7-9cd0-60a04285f411", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19727264073600, "endTime": 19727264115900}, "additional": {"logType": "info", "children": [], "durationId": "0d6eb6d9-8dd3-4ef6-b330-33603317d735", "parent": "f9e053d8-4a12-48aa-8d95-06d13b26af6d"}}, {"head": {"id": "caa10b43-aad3-4656-9c1a-5568e8026e9d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731972511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a7012df-c75f-4302-bbe0-5b053abe0049", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731973608400, "endTime": 19731973626300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "logId": "cc4b6c29-9559-47d5-920a-825064654835"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc4b6c29-9559-47d5-920a-825064654835", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731973608400, "endTime": 19731973626300}, "additional": {"logType": "info", "children": [], "durationId": "1a7012df-c75f-4302-bbe0-5b053abe0049", "parent": "f9e053d8-4a12-48aa-8d95-06d13b26af6d"}}, {"head": {"id": "f9e053d8-4a12-48aa-8d95-06d13b26af6d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker17", "startTime": 19713764867000, "endTime": 19731973712900}, "additional": {"logType": "error", "children": ["7cb8adc0-2501-4c73-b166-5b4e3cc92454", "5759418e-db80-4f5d-bc1b-e96429ea50af", "8a82b3d0-4d68-48c7-9cd0-60a04285f411", "cc4b6c29-9559-47d5-920a-825064654835"], "durationId": "8f4f2976-d42c-4dd9-9d51-ce681bf6ac90", "parent": "737fc9c0-a790-4469-af53-8733c7d15822"}}, {"head": {"id": "8ddec451-8ce8-40e5-8a97-f189e07b3012", "name": "default@PreviewArkTS watch work[17] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731973754500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "737fc9c0-a790-4469-af53-8733c7d15822", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713745078700, "endTime": 19731973925200}, "additional": {"logType": "error", "children": ["f9e053d8-4a12-48aa-8d95-06d13b26af6d"], "durationId": "f8862762-c3aa-4a9d-98b5-1b285861ac59"}}, {"head": {"id": "5998702c-8973-4692-a20c-d922ac5953a8", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731974042000}, "additional": {"logType": "debug", "children": [], "durationId": "f8862762-c3aa-4a9d-98b5-1b285861ac59"}}, {"head": {"id": "4f132e4f-1477-4cdf-acac-9c00f9a3958e", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:102:22\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731974724400}, "additional": {"logType": "debug", "children": [], "durationId": "f8862762-c3aa-4a9d-98b5-1b285861ac59"}}, {"head": {"id": "6b62a124-af32-4f1c-b37d-d5b38df4f070", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981070100, "endTime": 19731981128000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92e8f19e-0a33-4c37-a497-d14dc4a1897d", "logId": "e60a508f-5186-4bb4-9b6a-e1bf9490f83e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60a508f-5186-4bb4-9b6a-e1bf9490f83e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981070100, "endTime": 19731981128000}, "additional": {"logType": "info", "children": [], "durationId": "6b62a124-af32-4f1c-b37d-d5b38df4f070"}}, {"head": {"id": "30083370-76e9-41ea-b329-c319bb0ad3a9", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19713168723500, "endTime": 19731981276700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 19}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "a853dac6-d86d-4e0c-be6a-092f59dfa38c", "name": "BUILD FAILED in 18 s 813 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981301600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "0423eaee-fd3a-4ffd-9469-457a90c332cc", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981475100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "660cc624-10b1-4d53-a48c-4b9093e6c766", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981534800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6501e49d-799d-4e74-ad55-2c6532ae5735", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981584600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c946c1d-4e76-4dfc-9b29-a91093ae53fc", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d80d60b-831f-4173-bc54-c1f32f887c4a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0041e5-0fdc-48c4-b6ea-28ce86259a6a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981729700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29532219-95e2-4a36-8604-967b81395373", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fcfbcd-b8a3-4b6e-bda2-16b204652717", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4700aa05-0a76-4af0-9d2f-0c9e10ddd4a6", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981878600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bea4671-1318-46d3-a961-f340e54eb1df", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731981931800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7638159-8fa5-4fd6-ba8c-7b6b54daabbc", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731985246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384d370c-07cb-4c9e-8793-ef1b76d44c14", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731986314200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ee89aa-56e9-44f9-b923-0b215b1268ec", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731986675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539e37ed-f923-4903-bf46-1852d70e593f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731986973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef887b4e-5c2e-4259-8a3b-70f672456b08", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731987788900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0eb788-1ab6-47e7-b0f7-70e1288ef621", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731999238300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f7952f-53e3-4f7d-b28d-43b92b8586df", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731999569900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0809a909-d657-4bdc-8d0e-f76ba7333c64", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19731999852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626ba6ea-a656-4d07-a83e-8f591b6ca51c", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19732000156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f997107-3f17-4a85-9b2e-f8218f818398", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19732000458100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}