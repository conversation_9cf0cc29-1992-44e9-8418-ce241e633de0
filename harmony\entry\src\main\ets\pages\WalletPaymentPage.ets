import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct WalletPaymentPage {
  @State product: string = '商品购买';
  @State amount: string = '';
  @State password: string = '';

  aboutToAppear() {
    console.log('WalletPaymentPage 页面加载成功');
  }

  // 处理支付
  private handlePayment() {
    if (!this.product.trim()) {
      promptAction.showToast({
        message: '请输入商品信息',
        duration: 2000
      });
      return;
    }

    if (!this.amount.trim()) {
      promptAction.showToast({
        message: '请输入支付金额',
        duration: 2000
      });
      return;
    }

    if (!this.password.trim()) {
      promptAction.showToast({
        message: '请输入支付密码',
        duration: 2000
      });
      return;
    }

    // 模拟支付处理
    promptAction.showToast({
      message: '钱包支付成功！',
      duration: 2000
    });

    // 延迟跳转到交易记录页面
    setTimeout(() => {
      router.replaceUrl({
        url: 'pages/TransactionListPage'
      });
    }, 2000);
  }

  // 取消支付
  private handleCancel() {
    router.back();
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('×')
          .fontSize(24)
          .fontColor('#666666')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('钱包支付')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({
        width: { bottom: 1 },
        color: '#E5E5E5'
      })

      // 支付表单
      Column() {
        // 商品信息
        Row() {
          Text('商品')
            .fontSize(16)
            .fontColor('#333333')
            .width(60)

          TextInput({ placeholder: '商品购买', text: this.product })
            .layoutWeight(1)
            .fontSize(16)
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .padding({ left: 12, right: 12 })
            .height(44)
            .onChange((value: string) => {
              this.product = value;
            })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 20 })

        // 支付金额
        Row() {
          Text('金额')
            .fontSize(16)
            .fontColor('#333333')
            .width(60)

          TextInput({ placeholder: '请输入支付金额' })
            .layoutWeight(1)
            .fontSize(16)
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .padding({ left: 12, right: 12 })
            .height(44)
            .type(InputType.Number)
            .onChange((value: string) => {
              this.amount = value;
            })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 20 })

        // 支付密码
        Row() {
          Text('密码')
            .fontSize(16)
            .fontColor('#333333')
            .width(60)

          TextInput({ placeholder: '请输入支付密码' })
            .layoutWeight(1)
            .fontSize(16)
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .padding({ left: 12, right: 12 })
            .height(44)
            .type(InputType.Password)
            .onChange((value: string) => {
              this.password = value;
            })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 40 })

        // 操作按钮
        Row() {
          Button('取消')
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .width(100)
            .height(44)
            .onClick(() => {
              this.handleCancel();
            })

          Button('确认支付')
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#4A90E2')
            .borderRadius(8)
            .width(120)
            .height(44)
            .margin({ left: 20 })
            .onClick(() => {
              this.handlePayment();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.End)
      }
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }
}
