import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { storageManager, LocalUserInfo } from '../common/storage/StorageManager';
import { UpdatePayPasswordRequest, UpdatePayLimitRequest, UserInfo } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';

@Entry
@Component
struct SettingsPage {
  @State userInfo: LocalUserInfo | null = null;
  @State showLogoutDialog: boolean = false;

  aboutToAppear() {
    this.loadUserInfo();
  }

  onPageShow() {
    console.log('设置页面显示，重新加载用户信息');
    this.loadUserInfo();
  }

  async loadUserInfo(): Promise<void> {
    try {
      const cachedUserInfo = await storageManager.getUserInfo();
      if (cachedUserInfo) {
        this.userInfo = cachedUserInfo;
      } else {
        this.userInfo = {
          userId: 1,
          phone: '<EMAIL>',
          realName: '未设置',
          idCard: '',
          walletNo: '',
          balance: 0,
          payLimit: 1000,
          status: 1,
          createTime: '',
          updateTime: ''
        };
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      this.userInfo = {
        userId: 1,
        phone: '<EMAIL>',
        realName: '未设置',
        idCard: '',
        walletNo: '',
        balance: 0,
        payLimit: 1000,
        status: 1,
        createTime: '',
        updateTime: ''
      };
    }
  }

  build() {
    Stack() {
      Column() {
        // 顶部导航栏
        Column() {
          Row() {
            Text('个人设置')
              .fontSize(20)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Medium)
              .width('100%')
              .textAlign(TextAlign.Center)
          }
          .width('100%')
          .height(56)
          .padding(16)
        }
        .width('90%')
        .margin({ top: 20 })
        .borderRadius(16)
        .backgroundColor('#ff3785f5')
        .shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 })

        Scroll() {
          Column() {
            // 个人信息卡片
            Column() {
              // 头像和信息行
              Row() {
                // 头像
                Column() {
                  Text(this.userInfo?.realName?.charAt(0) || 'L')
                    .fontSize(40)
                    .fontColor('#FFFFFF')
                    .fontWeight(FontWeight.Bold)
                }
                .width(80)
                .height(80)
                .borderRadius(40)
                .backgroundColor('rgba(255, 255, 255, 0.2)')
                .justifyContent(FlexAlign.Center)
                .alignItems(HorizontalAlign.Center)
                .margin({ right: 20 })

                // 用户信息
                Column() {
                  Text(this.userInfo?.realName || '未设置')
                    .fontSize(24)
                    .fontColor('#FFFFFF')
                    .fontWeight(FontWeight.Bold)
                    .alignSelf(ItemAlign.Start)
                    .margin({ bottom: 8 })

                  Text(this.userInfo?.phone || '<EMAIL>')
                    .fontSize(14)
                    .fontColor('rgba(255, 255, 255, 0.8)')
                    .alignSelf(ItemAlign.Start)
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)
              }
              .width('100%')
              .alignItems(VerticalAlign.Center)
              .margin({ bottom: 8 })
            }
            .width('90%')
            .padding(24)
            .margin({ top: 16, bottom: 24 })
            .borderRadius(16)
            .linearGradient({
              direction: GradientDirection.Right,
              colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
            })

            // 基本设置卡片
            Column() {
              Row() {
                Text('⚙️')
                  .fontSize(18)
                  .margin({ right: 8 })
                Text('基本设置')
                  .fontSize(18)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Medium)
              }
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 20 })

              // 修改基本资料
              this.SettingCardItem(
                '📝',
                '#667eea',
                '修改基本资料',
                '修改用户名、邮箱、手机号',
                () => {
                  router.pushUrl({ url: 'pages/EditProfilePage' });
                }
              )

              // 修改登录密码
              this.SettingCardItem(
                '🔑',
                '#52c41a',
                '修改登录密码',
                '修改账户登录密码',
                () => {
                  router.pushUrl({ url: 'pages/ChangePasswordPage' });
                }
              )

              // 修改支付密码
              this.SettingCardItem(
                '🔒',
                '#fa8c16',
                '修改支付密码',
                '修改支付交易密码',
                () => {
                  router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
                }
              )

              // 退出登录
              this.SettingCardItem(
                '🚪',
                '#f5222d',
                '退出登录',
                '退出当前账户',
                () => {
                  this.showLogoutDialog = true;
                }
              )
            }
            .width('100%')
            .padding(20)
            .margin({ left: 16, right: 16 })
            .borderRadius(16)
            .backgroundColor('#FFFFFF')
            .shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.06)', offsetX: 0, offsetY: 2 })
          }
          .padding({ bottom: 20 })
        }
        .layoutWeight(1)
        .backgroundColor(Color.Transparent)

        // 底部导航栏
        this.BottomNavigation()
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')

      // 退出登录确认弹窗
      if (this.showLogoutDialog) {
        Column() {
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#80000000')
            .onClick(() => {
              this.showLogoutDialog = false;
            })

          Column() {
            Text('确认退出')
              .fontSize(18)
              .fontWeight(FontWeight.Medium)
              .fontColor('#333333')
              .margin({ bottom: 16 })

            Text('确定要退出登录吗？')
              .fontSize(14)
              .fontColor('#666666')
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })

            Row() {
              Button('取消')
                .fontSize(16)
                .fontColor('#666666')
                .backgroundColor('#F8F9FA')
                .borderRadius(8)
                .layoutWeight(1)
                .height(44)
                .onClick(() => {
                  this.showLogoutDialog = false;
                })

              Button('确认退出')
                .fontSize(16)
                .fontColor('#FFFFFF')
                .backgroundColor('#F44336')
                .borderRadius(8)
                .layoutWeight(1)
                .height(44)
                .margin({ left: 12 })
                .onClick(() => {
                  this.showLogoutDialog = false;
                  this.handleLogout();
                })
            }
            .width('100%')
          }
          .width('280vp')
          .padding(24)
          .borderRadius(16)
          .backgroundColor('#FFFFFF')
          .shadow({
            radius: 16,
            color: '#40000000',
            offsetX: 0,
            offsetY: 8
          })
          .position({ x: '50%', y: '50%' })
          .translate({ x: '-50%', y: '-50%' })
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
        .zIndex(1000)
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  SettingCardItem(icon: string, color: string, title: string, subtitle: string, onTap: () => void) {
    Row() {
      Column() {
        Text(icon)
          .fontSize(20)
          .fontColor('#FFFFFF')
      }
      .width(44)
      .height(44)
      .borderRadius(12)
      .backgroundColor(color)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .margin({ right: 16 })

      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(subtitle)
          .fontSize(13)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      Text('>')
        .fontSize(18)
        .fontColor('#CCCCCC')
    }
    .width('100%')
    .height(70)
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .margin({ bottom: 8 })
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .shadow({ radius: 4, color: 'rgba(0, 0, 0, 0.08)', offsetX: 0, offsetY: 2 })
    .onClick(onTap)
  }

  async handleLogout(): Promise<void> {
    try {
      await storageManager.clearUserData();
      httpClient.clearAuthToken();
      promptAction.showToast({ message: '已退出登录' });
      router.replaceUrl({ url: 'pages/LoginPage' });
    } catch (error) {
      console.error('退出登录失败:', error);
      promptAction.showToast({ message: '退出登录失败' });
    }
  }

  @Builder
  BottomNavigation() {
    Row() {
      Column() {
        Text('💳').fontSize(20)
        Text('银行卡').fontSize(12).fontColor('#666666').margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }))

      Column() {
        Text('📊').fontSize(20)
        Text('交易').fontSize(12).fontColor('#666666').margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/TransactionListPage' }))

      Column() {
        Text('👛').fontSize(20)
        Text('钱包').fontSize(12).fontColor('#666666').margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/WalletPage' }))

      Column() {
        Text('💰').fontSize(20)
        Text('支付中心').fontSize(12).fontColor('#666666').margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
      .onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }))

      Column() {
        Text('👤').fontSize(20)
        Text('我的').fontSize(12).fontColor('#6366F1').margin({ top: 4 })
      }
      .layoutWeight(1).alignItems(HorizontalAlign.Center).padding({ top: 8, bottom: 8 })
    }
    .width('100%').height(60).backgroundColor('#FFFFFF')
    .border({ width: { top: 1 }, color: '#E5E5E5' })
  }
}