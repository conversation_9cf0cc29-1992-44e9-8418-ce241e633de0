package com.icss.spring.entity;

import java.math.BigDecimal;
import java.util.Date;

public class Transaction {
    private Long id;
    private Long userId;
    private String transactionType; // PAYMENT/DEPOSIT/WITHDRAW/TRANSFER/RECEIVE
    private BigDecimal amount;
    private Date transactionTime;
    private String paymentMethod; // WALLET/BANK_CARD
    private String paymentChannel; // MERCHANT/QR_CODE/NFC
    private String targetAccount;
    private Long cardId;
    private String status; // SUCCESS/FAILED/PENDING
    private String description; // 交易描述

    // 构造方法
    public Transaction() {}

    public Transaction(Long id, Long userId, String transactionType, BigDecimal amount,
                       Date transactionTime, String paymentMethod, String paymentChannel,
                       String targetAccount, Long cardId, String status) {
        this.id = id;
        this.userId = userId;
        this.transactionType = transactionType;
        this.amount = amount;
        this.transactionTime = transactionTime;
        this.paymentMethod = paymentMethod;
        this.paymentChannel = paymentChannel;
        this.targetAccount = targetAccount;
        this.cardId = cardId;
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(Date transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(String paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public String getTargetAccount() {
        return targetAccount;
    }

    public void setTargetAccount(String targetAccount) {
        this.targetAccount = targetAccount;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}