{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "290c37d5-b375-435a-9ce4-9a11c3552566", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263636442800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e5dfbf-ea77-4691-a4f0-673cbbe19132", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13947503166500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1163911e-20be-45ad-809a-efa7c005d905", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950005117500, "endTime": 13957429892200}, "additional": {"children": ["4037aa03-d654-4f25-851d-8a3c61193fef", "604bf61c-88b5-40d1-a400-36e54fc9422a", "81a42910-156d-44ae-92d1-a05b0eb18e49", "c5e787de-9ebe-4638-a81b-db45074242d4", "8bef841f-4b01-47ae-99f2-18e7fb6e74c6", "e3a35328-4b06-4ded-be05-7269ed66a674", "36499102-7a31-4f84-841b-a3fe243cf238"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bf939028-a49a-46cf-9714-48c252634756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4037aa03-d654-4f25-851d-8a3c61193fef", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950005120400, "endTime": 13950958350000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "a8c43983-de99-4a49-aea9-5e421d4ea636"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "604bf61c-88b5-40d1-a400-36e54fc9422a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950958403700, "endTime": 13957428480500}, "additional": {"children": ["b89b2d11-33ed-4e81-a65e-993ed55bec80", "4eee2b49-1156-4dc9-9a09-1c7de3b6b6c8", "639a2487-3d3a-4367-819c-c1aa6192fd13", "f641b216-5791-45af-913a-69a9952f8ab9", "f7a9e0d8-0c2f-4028-8fb5-1b9fe6f35d72", "de3b8c9a-a05f-47eb-bb37-c18fc8ea0cf7", "88ae7b1d-66b4-4130-a05d-644f150d96d2", "c69eb261-969d-4020-bc61-0af2d3258f7c", "a7e30f59-e573-46e9-832c-96748779ebcb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81a42910-156d-44ae-92d1-a05b0eb18e49", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957428507400, "endTime": 13957429873900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "dd832751-69df-4144-af8b-372413102add"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5e787de-9ebe-4638-a81b-db45074242d4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957429880400, "endTime": 13957429887800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "af03db88-1630-4fc8-bebc-eae4b40023a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bef841f-4b01-47ae-99f2-18e7fb6e74c6", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950287136500, "endTime": 13950302806800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "70240bf3-d2e1-4373-84b1-de9697e47527"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70240bf3-d2e1-4373-84b1-de9697e47527", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950287136500, "endTime": 13950302806800}, "additional": {"logType": "info", "children": [], "durationId": "8bef841f-4b01-47ae-99f2-18e7fb6e74c6", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "e3a35328-4b06-4ded-be05-7269ed66a674", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950436649800, "endTime": 13950436724100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "6fa914a7-a8ae-4e43-9e59-69fe782804e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fa914a7-a8ae-4e43-9e59-69fe782804e1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950436649800, "endTime": 13950436724100}, "additional": {"logType": "info", "children": [], "durationId": "e3a35328-4b06-4ded-be05-7269ed66a674", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "84f8dba4-cbd0-4841-9ee7-bfac2170bb13", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950468368400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f8d2c0-a046-400b-a812-568d7f47b94f", "name": "Cache service initialization finished in 458 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950957462200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8c43983-de99-4a49-aea9-5e421d4ea636", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950005120400, "endTime": 13950958350000}, "additional": {"logType": "info", "children": [], "durationId": "4037aa03-d654-4f25-851d-8a3c61193fef", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "b89b2d11-33ed-4e81-a65e-993ed55bec80", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951053585800, "endTime": 13951053596700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "4a2d7053-7b69-4f7f-a14c-be58e8210feb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4eee2b49-1156-4dc9-9a09-1c7de3b6b6c8", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951053614000, "endTime": 13951162384100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "94203fcd-8027-4efd-8eb6-55f90ec86a52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "639a2487-3d3a-4367-819c-c1aa6192fd13", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951162413600, "endTime": 13955773182000}, "additional": {"children": ["63307f85-4d74-432c-9b44-3d29757cfe7f", "3f2121ab-97f7-4c6c-8fcc-3f2f1518b488", "9b7dc718-a160-4644-91d5-606f1c37c02d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "75124dd6-32b9-4dd8-8a8f-360ca62ce812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f641b216-5791-45af-913a-69a9952f8ab9", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773200500, "endTime": 13956174275300}, "additional": {"children": ["71d2d4da-5f88-4b42-a8be-638eca29c67c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "36c4c658-56e5-499f-8c92-49f0eb3403b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7a9e0d8-0c2f-4028-8fb5-1b9fe6f35d72", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956174284900, "endTime": 13957046604900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "4fc8fbdc-ab35-4d44-835c-300f34d12752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de3b8c9a-a05f-47eb-bb37-c18fc8ea0cf7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957048848900, "endTime": 13957102367300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "ef5941a5-b775-421f-b5b9-e552b67aaa78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88ae7b1d-66b4-4130-a05d-644f150d96d2", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957102433800, "endTime": 13957428237000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "9d81993a-30ed-4be0-abec-f10f18d48cee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c69eb261-969d-4020-bc61-0af2d3258f7c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957428267500, "endTime": 13957428427100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "d087ac3c-0885-4dea-9c8c-48903cda9ec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a2d7053-7b69-4f7f-a14c-be58e8210feb", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951053585800, "endTime": 13951053596700}, "additional": {"logType": "info", "children": [], "durationId": "b89b2d11-33ed-4e81-a65e-993ed55bec80", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "94203fcd-8027-4efd-8eb6-55f90ec86a52", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951053614000, "endTime": 13951162384100}, "additional": {"logType": "info", "children": [], "durationId": "4eee2b49-1156-4dc9-9a09-1c7de3b6b6c8", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "63307f85-4d74-432c-9b44-3d29757cfe7f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951165382400, "endTime": 13951165422400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "639a2487-3d3a-4367-819c-c1aa6192fd13", "logId": "22464c88-7137-4038-96c0-3f2ce6f7a64c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22464c88-7137-4038-96c0-3f2ce6f7a64c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951165382400, "endTime": 13951165422400}, "additional": {"logType": "info", "children": [], "durationId": "63307f85-4d74-432c-9b44-3d29757cfe7f", "parent": "75124dd6-32b9-4dd8-8a8f-360ca62ce812"}}, {"head": {"id": "3f2121ab-97f7-4c6c-8fcc-3f2f1518b488", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951214516800, "endTime": 13955772159000}, "additional": {"children": ["7348cbd9-bd26-4943-9d28-ac2046a14cb2", "ad0eddaf-2030-4cd2-86ca-bdebdd30b38d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "639a2487-3d3a-4367-819c-c1aa6192fd13", "logId": "f3c35558-a6c6-4551-a793-c27cebc75c0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7348cbd9-bd26-4943-9d28-ac2046a14cb2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951214522200, "endTime": 13953089310900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f2121ab-97f7-4c6c-8fcc-3f2f1518b488", "logId": "32a11941-c3a5-4e4d-b552-bc33118a6cbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad0eddaf-2030-4cd2-86ca-bdebdd30b38d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13953089345400, "endTime": 13955772129600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f2121ab-97f7-4c6c-8fcc-3f2f1518b488", "logId": "c4cc5998-6bc4-401f-935a-e9e3533c8c4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d796311-1882-4ae4-a4e9-9f1e16ee7de7", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951214538200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e11f02-615e-4619-b8fa-2ccffa7b0a96", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13953089009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32a11941-c3a5-4e4d-b552-bc33118a6cbc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951214522200, "endTime": 13953089310900}, "additional": {"logType": "info", "children": [], "durationId": "7348cbd9-bd26-4943-9d28-ac2046a14cb2", "parent": "f3c35558-a6c6-4551-a793-c27cebc75c0e"}}, {"head": {"id": "e624c158-f7d8-40c2-9136-c831d6a0e16a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13953089379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f105ab58-6541-44b7-afd7-33db4ae168e7", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954319601100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67549d45-bd5f-4e48-819b-6fe6a1205041", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954319830700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d9d99b-b696-4695-95ab-7062bdeac912", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954320111000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b28154-598f-4a4e-9a92-67313830ba7b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954365887800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f77415e-86f2-487b-a0e7-288ee9d9071b", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954491445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f4de14-2ee7-4931-a2f9-c032e0cb730e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13954853886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d0fded9-af57-44de-91d3-adeed009e4c5", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955015602300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e65cc8-bf0c-41a0-bb13-2203949c126f", "name": "Sdk init in 527 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955471852200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8065a197-3c43-4c41-9b40-94f241cf21a7", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955473320700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 42}, "markType": "other"}}, {"head": {"id": "8837a223-2828-46ab-8779-792457ce21e9", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955473352800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 42}, "markType": "other"}}, {"head": {"id": "401d2fee-fc80-4bb6-a4ec-c4f7f0dde22a", "name": "Project task initialization takes 263 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955749352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd519af-a3fc-4ab9-969f-54816002cb3d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955749505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b79fbf-37e0-4c4f-9308-eacd4086b4c9", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955749585900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a899e697-0040-4abf-be9c-360cf288fd4d", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955749654300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4cc5998-6bc4-401f-935a-e9e3533c8c4b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13953089345400, "endTime": 13955772129600}, "additional": {"logType": "info", "children": [], "durationId": "ad0eddaf-2030-4cd2-86ca-bdebdd30b38d", "parent": "f3c35558-a6c6-4551-a793-c27cebc75c0e"}}, {"head": {"id": "f3c35558-a6c6-4551-a793-c27cebc75c0e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951214516800, "endTime": 13955772159000}, "additional": {"logType": "info", "children": ["32a11941-c3a5-4e4d-b552-bc33118a6cbc", "c4cc5998-6bc4-401f-935a-e9e3533c8c4b"], "durationId": "3f2121ab-97f7-4c6c-8fcc-3f2f1518b488", "parent": "75124dd6-32b9-4dd8-8a8f-360ca62ce812"}}, {"head": {"id": "9b7dc718-a160-4644-91d5-606f1c37c02d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773139700, "endTime": 13955773162100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "639a2487-3d3a-4367-819c-c1aa6192fd13", "logId": "310a01cd-24e1-41b2-ac97-89b3a166a0e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "310a01cd-24e1-41b2-ac97-89b3a166a0e0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773139700, "endTime": 13955773162100}, "additional": {"logType": "info", "children": [], "durationId": "9b7dc718-a160-4644-91d5-606f1c37c02d", "parent": "75124dd6-32b9-4dd8-8a8f-360ca62ce812"}}, {"head": {"id": "75124dd6-32b9-4dd8-8a8f-360ca62ce812", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13951162413600, "endTime": 13955773182000}, "additional": {"logType": "info", "children": ["22464c88-7137-4038-96c0-3f2ce6f7a64c", "f3c35558-a6c6-4551-a793-c27cebc75c0e", "310a01cd-24e1-41b2-ac97-89b3a166a0e0"], "durationId": "639a2487-3d3a-4367-819c-c1aa6192fd13", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "71d2d4da-5f88-4b42-a8be-638eca29c67c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773951000, "endTime": 13956174251600}, "additional": {"children": ["f87667d8-c95e-44f4-b9aa-896314a7b795", "936be755-bbbf-41ee-9f45-bfd7a92056a1", "e08f4bc7-d952-47a6-8c55-93b82695d3e5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f641b216-5791-45af-913a-69a9952f8ab9", "logId": "11d82782-427d-4dd9-8c03-4466b3876f32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f87667d8-c95e-44f4-b9aa-896314a7b795", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955777087300, "endTime": 13955777116300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "71d2d4da-5f88-4b42-a8be-638eca29c67c", "logId": "1e97c4b7-0aae-4ed2-9efd-317cdc11d503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e97c4b7-0aae-4ed2-9efd-317cdc11d503", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955777087300, "endTime": 13955777116300}, "additional": {"logType": "info", "children": [], "durationId": "f87667d8-c95e-44f4-b9aa-896314a7b795", "parent": "11d82782-427d-4dd9-8c03-4466b3876f32"}}, {"head": {"id": "936be755-bbbf-41ee-9f45-bfd7a92056a1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955779602800, "endTime": 13956053468700}, "additional": {"children": ["ee2ca568-77e0-4b72-94bb-fee6ddb50f76", "0a44cf02-370e-4b88-aeca-8c76242ef9f7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "71d2d4da-5f88-4b42-a8be-638eca29c67c", "logId": "8d50fdcf-d001-4ba7-bf97-f772ae23f945"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee2ca568-77e0-4b72-94bb-fee6ddb50f76", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955779603700, "endTime": 13955784423900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "936be755-bbbf-41ee-9f45-bfd7a92056a1", "logId": "da808672-cbf8-4bdc-a168-92b9efa3061d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a44cf02-370e-4b88-aeca-8c76242ef9f7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955784441200, "endTime": 13956053445700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "936be755-bbbf-41ee-9f45-bfd7a92056a1", "logId": "32e72616-9925-47a9-8322-b72ee5322fc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5eb4e85-d6c8-4616-b42f-a23abc81336b", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955779608000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89669d4-7aac-4043-9560-69e0139d4cfe", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955784281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da808672-cbf8-4bdc-a168-92b9efa3061d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955779603700, "endTime": 13955784423900}, "additional": {"logType": "info", "children": [], "durationId": "ee2ca568-77e0-4b72-94bb-fee6ddb50f76", "parent": "8d50fdcf-d001-4ba7-bf97-f772ae23f945"}}, {"head": {"id": "0ee0e3b2-c562-4f2b-af0a-26ddacb8ab62", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955784453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3befb9de-773a-438f-a398-5853403f611f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955965094900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97249e04-2957-4a56-9ed6-aad8b93cc534", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955965224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba15612-bf3d-4c3b-ab0f-09e3a548d78b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956020569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277ef2aa-8e1c-4ce5-aa1c-96bd56384b59", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956020911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a2ab98-2d6e-4c16-a18a-b65ad8f56684", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956021110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4d6379-cdc3-4bea-bcc4-7f0434dd358c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956021260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d610d3-e961-40ca-834c-8244fc53b7a2", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956021374700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a64b5e-2794-4160-9ee4-d6446fb6616a", "name": "Module entry task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956044576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c91c99-73bd-4dcc-9838-a024298dfc50", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956053149900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a145a7-2ef9-41c1-921e-c80549ef724d", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956053266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ea9799-c8a0-4b3e-92a9-c9e5012c42f2", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956053357300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e72616-9925-47a9-8322-b72ee5322fc8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955784441200, "endTime": 13956053445700}, "additional": {"logType": "info", "children": [], "durationId": "0a44cf02-370e-4b88-aeca-8c76242ef9f7", "parent": "8d50fdcf-d001-4ba7-bf97-f772ae23f945"}}, {"head": {"id": "8d50fdcf-d001-4ba7-bf97-f772ae23f945", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955779602800, "endTime": 13956053468700}, "additional": {"logType": "info", "children": ["da808672-cbf8-4bdc-a168-92b9efa3061d", "32e72616-9925-47a9-8322-b72ee5322fc8"], "durationId": "936be755-bbbf-41ee-9f45-bfd7a92056a1", "parent": "11d82782-427d-4dd9-8c03-4466b3876f32"}}, {"head": {"id": "e08f4bc7-d952-47a6-8c55-93b82695d3e5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956174076700, "endTime": 13956174098500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "71d2d4da-5f88-4b42-a8be-638eca29c67c", "logId": "e9e4bf9e-6ad0-413f-924d-02a2d6b760c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9e4bf9e-6ad0-413f-924d-02a2d6b760c4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956174076700, "endTime": 13956174098500}, "additional": {"logType": "info", "children": [], "durationId": "e08f4bc7-d952-47a6-8c55-93b82695d3e5", "parent": "11d82782-427d-4dd9-8c03-4466b3876f32"}}, {"head": {"id": "11d82782-427d-4dd9-8c03-4466b3876f32", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773951000, "endTime": 13956174251600}, "additional": {"logType": "info", "children": ["1e97c4b7-0aae-4ed2-9efd-317cdc11d503", "8d50fdcf-d001-4ba7-bf97-f772ae23f945", "e9e4bf9e-6ad0-413f-924d-02a2d6b760c4"], "durationId": "71d2d4da-5f88-4b42-a8be-638eca29c67c", "parent": "36c4c658-56e5-499f-8c92-49f0eb3403b2"}}, {"head": {"id": "36c4c658-56e5-499f-8c92-49f0eb3403b2", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13955773200500, "endTime": 13956174275300}, "additional": {"logType": "info", "children": ["11d82782-427d-4dd9-8c03-4466b3876f32"], "durationId": "f641b216-5791-45af-913a-69a9952f8ab9", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "bc2c669c-b607-46f0-8ca6-af7b11608062", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957044554000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98287d6e-88b4-4e2e-bd1d-d5257b606b58", "name": "hvigorfile, resolve hvigorfile dependencies in 873 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957046373300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc8fbdc-ab35-4d44-835c-300f34d12752", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13956174284900, "endTime": 13957046604900}, "additional": {"logType": "info", "children": [], "durationId": "f7a9e0d8-0c2f-4028-8fb5-1b9fe6f35d72", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "a7e30f59-e573-46e9-832c-96748779ebcb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957048437900, "endTime": 13957048819600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "604bf61c-88b5-40d1-a400-36e54fc9422a", "logId": "8d0e6c8d-7b87-40f4-852d-c3d5f177d6fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eda73c2-5d60-4a91-99ab-79978f921a60", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957048494400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0e6c8d-7b87-40f4-852d-c3d5f177d6fd", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957048437900, "endTime": 13957048819600}, "additional": {"logType": "info", "children": [], "durationId": "a7e30f59-e573-46e9-832c-96748779ebcb", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "ebccc6b4-6c39-4d1b-a515-78abea4c5104", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957052232600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a029af95-3f56-4592-b5e9-f593dd2c285b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957075775400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef5941a5-b775-421f-b5b9-e552b67aaa78", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957048848900, "endTime": 13957102367300}, "additional": {"logType": "info", "children": [], "durationId": "de3b8c9a-a05f-47eb-bb37-c18fc8ea0cf7", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "6eccd183-b680-4a86-b87d-e573415984a5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957127990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c043084b-045f-435c-ba2d-d9400b673580", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957402075000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf478731-3911-494c-acb1-be43fac9d566", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957402303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ad260c-8a49-4f82-b241-9591acaaf048", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957410068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b669aa3-8f7b-4390-a9f2-f4a1589477b2", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957413286800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22efb31e-5497-46f9-9e62-499662587b91", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957413419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d81993a-30ed-4be0-abec-f10f18d48cee", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957102433800, "endTime": 13957428237000}, "additional": {"logType": "info", "children": [], "durationId": "88ae7b1d-66b4-4130-a05d-644f150d96d2", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "0de602de-2b4b-4a0e-93a4-48f56934efc9", "name": "Configuration phase cost:6 s 375 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957428300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d087ac3c-0885-4dea-9c8c-48903cda9ec3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957428267500, "endTime": 13957428427100}, "additional": {"logType": "info", "children": [], "durationId": "c69eb261-969d-4020-bc61-0af2d3258f7c", "parent": "9fc2d93b-8277-44c6-80f9-77a5b85d6347"}}, {"head": {"id": "9fc2d93b-8277-44c6-80f9-77a5b85d6347", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950958403700, "endTime": 13957428480500}, "additional": {"logType": "info", "children": ["4a2d7053-7b69-4f7f-a14c-be58e8210feb", "94203fcd-8027-4efd-8eb6-55f90ec86a52", "75124dd6-32b9-4dd8-8a8f-360ca62ce812", "36c4c658-56e5-499f-8c92-49f0eb3403b2", "4fc8fbdc-ab35-4d44-835c-300f34d12752", "ef5941a5-b775-421f-b5b9-e552b67aaa78", "9d81993a-30ed-4be0-abec-f10f18d48cee", "d087ac3c-0885-4dea-9c8c-48903cda9ec3", "8d0e6c8d-7b87-40f4-852d-c3d5f177d6fd"], "durationId": "604bf61c-88b5-40d1-a400-36e54fc9422a", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "36499102-7a31-4f84-841b-a3fe243cf238", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957429840300, "endTime": 13957429860200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1163911e-20be-45ad-809a-efa7c005d905", "logId": "429f2f51-8b3d-4392-a9c9-5ea6101725f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "429f2f51-8b3d-4392-a9c9-5ea6101725f6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957429840300, "endTime": 13957429860200}, "additional": {"logType": "info", "children": [], "durationId": "36499102-7a31-4f84-841b-a3fe243cf238", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "dd832751-69df-4144-af8b-372413102add", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957428507400, "endTime": 13957429873900}, "additional": {"logType": "info", "children": [], "durationId": "81a42910-156d-44ae-92d1-a05b0eb18e49", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "af03db88-1630-4fc8-bebc-eae4b40023a9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957429880400, "endTime": 13957429887800}, "additional": {"logType": "info", "children": [], "durationId": "c5e787de-9ebe-4638-a81b-db45074242d4", "parent": "bf939028-a49a-46cf-9714-48c252634756"}}, {"head": {"id": "bf939028-a49a-46cf-9714-48c252634756", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13950005117500, "endTime": 13957429892200}, "additional": {"logType": "info", "children": ["a8c43983-de99-4a49-aea9-5e421d4ea636", "9fc2d93b-8277-44c6-80f9-77a5b85d6347", "dd832751-69df-4144-af8b-372413102add", "af03db88-1630-4fc8-bebc-eae4b40023a9", "70240bf3-d2e1-4373-84b1-de9697e47527", "6fa914a7-a8ae-4e43-9e59-69fe782804e1", "429f2f51-8b3d-4392-a9c9-5ea6101725f6"], "durationId": "1163911e-20be-45ad-809a-efa7c005d905"}}, {"head": {"id": "6a9a788a-6a19-4141-8468-427147dacf9e", "name": "Configuration task cost before running: 7 s 554 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957430036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf69d13-1bbf-4cd4-bca6-8defb0109758", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957476745700, "endTime": 13957520517200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c7ac16fa-fe61-4633-9666-4f979771c28b", "logId": "1f9d1fa3-2df1-4e71-acd4-f0395d54deec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7ac16fa-fe61-4633-9666-4f979771c28b", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957432006400}, "additional": {"logType": "detail", "children": [], "durationId": "dbf69d13-1bbf-4cd4-bca6-8defb0109758"}}, {"head": {"id": "1247348e-ca08-4867-ad24-a6dae1e214e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957432592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f060acaa-4477-41b9-95f5-bdaef65cd23c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957432710800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62368dd2-f33f-4a01-8e90-d06262ece99f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957476761000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4541d711-f3eb-4eba-be07-8ade906779a2", "name": "Incremental task entry:default@PreBuild pre-execution cost: 41 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957520180900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6923a40-3c35-4fc1-9732-934b32097c38", "name": "entry : default@PreBuild cost memory 0.26766204833984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957520376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9d1fa3-2df1-4e71-acd4-f0395d54deec", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957476745700, "endTime": 13957520517200}, "additional": {"logType": "info", "children": [], "durationId": "dbf69d13-1bbf-4cd4-bca6-8defb0109758"}}, {"head": {"id": "f3ab2aaf-9d26-4ce3-9434-351ea9e4268f", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957527498000, "endTime": 13957579851300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c40a9292-2dce-4929-9cdb-3f28b5477ee9", "logId": "5db4fd01-2329-4e12-b41c-658811e3920a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c40a9292-2dce-4929-9cdb-3f28b5477ee9", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957526137000}, "additional": {"logType": "detail", "children": [], "durationId": "f3ab2aaf-9d26-4ce3-9434-351ea9e4268f"}}, {"head": {"id": "bbf96dfa-f326-433a-958c-3b56f3ccc9d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957526674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24919355-67fd-4482-8f4d-ca6fb73bc52f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957526783400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4627d809-809e-4f28-bc52-4968d4668f22", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957527508100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0751236-a578-4a6d-922f-310eeb662375", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 52 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957579609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2fa1b4-d9a9-447f-9e4b-5503f26cdfb7", "name": "entry : default@MergeProfile cost memory 0.11077117919921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957579775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db4fd01-2329-4e12-b41c-658811e3920a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957527498000, "endTime": 13957579851300}, "additional": {"logType": "info", "children": [], "durationId": "f3ab2aaf-9d26-4ce3-9434-351ea9e4268f"}}, {"head": {"id": "ba013272-ee82-4dad-b628-7c9ee41939e1", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957582873000, "endTime": 13957585297400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "67ab0b5e-8dc4-4d72-8cf2-0b6b8caa7748", "logId": "ae0b5096-6196-486e-bf92-a9972b2c6974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67ab0b5e-8dc4-4d72-8cf2-0b6b8caa7748", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957581633900}, "additional": {"logType": "detail", "children": [], "durationId": "ba013272-ee82-4dad-b628-7c9ee41939e1"}}, {"head": {"id": "37cf0b4e-5c1d-496d-8abb-f129bda57c92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957582091600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9977bd-ce71-4bd9-9a68-9653696aac62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957582168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6689cc2-2b1b-4adb-8c00-db2ab550fe66", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957582880700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b06781-36ad-46f1-8da7-71f5e97aa629", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957583770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d30412-c418-4cbb-825d-19c42afe2c3a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957585000000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7bd07ac-34c1-49f0-90c8-3384831d44a1", "name": "entry : default@CreateBuildProfile cost memory 0.09674835205078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957585118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0b5096-6196-486e-bf92-a9972b2c6974", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957582873000, "endTime": 13957585297400}, "additional": {"logType": "info", "children": [], "durationId": "ba013272-ee82-4dad-b628-7c9ee41939e1"}}, {"head": {"id": "65cf912e-1f79-452b-a475-51779bfb52e4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588170900, "endTime": 13957599267300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f595e2ba-215c-4172-b438-9cacf131a705", "logId": "89e1e10e-c28d-4362-ac51-d34200e4e58f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f595e2ba-215c-4172-b438-9cacf131a705", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957586889700}, "additional": {"logType": "detail", "children": [], "durationId": "65cf912e-1f79-452b-a475-51779bfb52e4"}}, {"head": {"id": "dfeab808-62ae-43df-a207-d4bc7f9d13f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957587360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec84b94c-b2bf-44ac-974f-9959d27f3828", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957587435400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e5937c8-d3e5-4409-aae7-969111be1f35", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473d0b37-2b6d-48e3-b29d-5d616a89fe55", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f0a3f67-a040-4fd9-9702-88128b95895c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588328500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82bf4da1-a205-45af-bb1a-cc2bb36708f8", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588395100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bdc82e-6c04-4e26-9857-c0f4ed4c1f79", "name": "runTaskFromQueue task cost before running: 7 s 713 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957599112900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e1e10e-c28d-4362-ac51-d34200e4e58f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957588170900, "endTime": 13957599267300, "totalTime": 280200}, "additional": {"logType": "info", "children": [], "durationId": "65cf912e-1f79-452b-a475-51779bfb52e4"}}, {"head": {"id": "cef19ff2-4e8e-467e-85e8-d0cc1a59a5ee", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957627279300, "endTime": 13957628504400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "24e41c1c-c0b0-486a-b499-fbfaf7a37846", "logId": "e5651864-9d6e-418f-8cf5-7641405af3eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24e41c1c-c0b0-486a-b499-fbfaf7a37846", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957601779300}, "additional": {"logType": "detail", "children": [], "durationId": "cef19ff2-4e8e-467e-85e8-d0cc1a59a5ee"}}, {"head": {"id": "4e2b16ed-cc61-4d60-8626-986fc12517ae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957602282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59426bc4-8e3d-40cc-a489-f73cebe2dd03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957602363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a609936-44e5-491e-9d47-cf7edf4f35c6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957627300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ab46d2-e102-4771-8f2e-09d37380e414", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957627568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01874078-5d0b-4a8b-ae45-dc0da3bb0d7a", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957628351900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b77cc4b2-399a-40b9-958b-f1da5fabfec1", "name": "entry : default@GeneratePkgContextInfo cost memory 0.066162109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957628435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5651864-9d6e-418f-8cf5-7641405af3eb", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957627279300, "endTime": 13957628504400}, "additional": {"logType": "info", "children": [], "durationId": "cef19ff2-4e8e-467e-85e8-d0cc1a59a5ee"}}, {"head": {"id": "0a06a1a3-df9e-45b3-952f-852a268640ba", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957632525900, "endTime": 13957634176400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1a90eab4-a19b-491b-8b60-8cbb2747f7fb", "logId": "f4ced8ca-39b6-4b0f-a141-58717b3a0c17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a90eab4-a19b-491b-8b60-8cbb2747f7fb", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957630531800}, "additional": {"logType": "detail", "children": [], "durationId": "0a06a1a3-df9e-45b3-952f-852a268640ba"}}, {"head": {"id": "53de7181-d3eb-40b5-9396-c84443503dee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957631214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25dfb885-a668-40ec-9a81-21d75efc33e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957631313500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9be53df6-dd7c-4350-89d9-c01bdfb88398", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957632535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "720229b7-374d-498b-b95e-8ac926675644", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957633544100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e60ea9-6c5a-476e-85aa-7bd33bb04f5f", "name": "entry : default@ProcessProfile cost memory 0.0583343505859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957634085100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4ced8ca-39b6-4b0f-a141-58717b3a0c17", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957632525900, "endTime": 13957634176400}, "additional": {"logType": "info", "children": [], "durationId": "0a06a1a3-df9e-45b3-952f-852a268640ba"}}, {"head": {"id": "ee91768c-b04c-4f0d-81b9-0696f5045142", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957637890400, "endTime": 13957682508900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0c7af868-9889-48ba-991c-40bc6b5ed735", "logId": "b4647f5f-042f-4aa9-a29c-93d89f261559"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c7af868-9889-48ba-991c-40bc6b5ed735", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957635619400}, "additional": {"logType": "detail", "children": [], "durationId": "ee91768c-b04c-4f0d-81b9-0696f5045142"}}, {"head": {"id": "9c8dbd45-67f6-4f6d-ae7a-822101172e44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957636108900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7dff7d-6bd6-4e24-9397-c46a9e21fd00", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957636180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee887766-ec30-4b9f-96d3-a5407e5d1339", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957637899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f642d67a-841e-42e7-8a70-1be0ff0fe11a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957682268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b3d01dc-b7b3-43c5-977a-cf7198a55202", "name": "entry : default@ProcessRouterMap cost memory 0.18775177001953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957682424200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4647f5f-042f-4aa9-a29c-93d89f261559", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957637890400, "endTime": 13957682508900}, "additional": {"logType": "info", "children": [], "durationId": "ee91768c-b04c-4f0d-81b9-0696f5045142"}}, {"head": {"id": "fb9ba733-ce3a-444a-95ca-b98e43b5c495", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957709304900, "endTime": 13957741565300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ffdcf496-9a68-4d88-bf26-2952640b2fa2", "logId": "e0a6ee37-f759-4978-9edd-6059b9bc0a67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffdcf496-9a68-4d88-bf26-2952640b2fa2", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957685535100}, "additional": {"logType": "detail", "children": [], "durationId": "fb9ba733-ce3a-444a-95ca-b98e43b5c495"}}, {"head": {"id": "4f7c031a-cb5e-406c-a5d0-9864a3e753c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957686098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0275e6a8-83c0-499d-b222-902f07c13955", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957686191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c44f00e-222a-402e-94bd-bed90de78100", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957706659000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcd09c5-975b-4987-bf27-a4a1d85891fc", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957710557200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c53496-bbaa-457d-afc2-75dfa8801c1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957710700500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "523dedac-c470-44d7-9cb4-c52d38dc510c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957710766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e249065-72bb-49c3-a7c9-4bcff170356e", "name": "entry : default@PreviewProcessResource cost memory 0.06829833984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957710852800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e402bb23-aa9e-4a87-bcb6-aaadbeff10b7", "name": "runTaskFromQueue task cost before running: 7 s 836 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957712127000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a6ee37-f759-4978-9edd-6059b9bc0a67", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957709304900, "endTime": 13957741565300, "totalTime": 1624700}, "additional": {"logType": "info", "children": [], "durationId": "fb9ba733-ce3a-444a-95ca-b98e43b5c495"}}, {"head": {"id": "3312c1d7-60b4-481d-aef5-89d38de3b372", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957806993700, "endTime": 13957835392200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "852429c9-1768-4f02-b0b3-9d55177c296f", "logId": "af25091a-8edc-4232-9590-1d8953e75670"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "852429c9-1768-4f02-b0b3-9d55177c296f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957799060800}, "additional": {"logType": "detail", "children": [], "durationId": "3312c1d7-60b4-481d-aef5-89d38de3b372"}}, {"head": {"id": "12d820eb-964c-4758-a43f-9aaef5b278c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957800032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a372a1-4b51-44bd-9e10-2b0e234f5d26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957800231800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ba7dd7-3122-464e-88d9-d90a04119b25", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957807017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628ce98b-8711-459a-8d75-6e3516b50566", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957835163500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf4e17f-fe07-43e6-9d5f-521b7f6b6122", "name": "entry : default@GenerateLoaderJson cost memory 0.7134628295898438", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957835308900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af25091a-8edc-4232-9590-1d8953e75670", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957806993700, "endTime": 13957835392200}, "additional": {"logType": "info", "children": [], "durationId": "3312c1d7-60b4-481d-aef5-89d38de3b372"}}, {"head": {"id": "92163168-22fb-4349-bce0-78716ee0cedb", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957847109400, "endTime": 13957901982400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4642bca2-4f59-4215-b1be-aa4e9e164be5", "logId": "fef27db4-a0ab-4f9b-87dd-11d2c8388f85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4642bca2-4f59-4215-b1be-aa4e9e164be5", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957842948700}, "additional": {"logType": "detail", "children": [], "durationId": "92163168-22fb-4349-bce0-78716ee0cedb"}}, {"head": {"id": "9179c680-5135-41c5-a4be-3c6412113b60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957843398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f49f6d-cccf-4f73-a6a2-bf8af53e3b4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957843472700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5d5612-cccf-4b73-8b28-34f0b4c914a3", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957844365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faba1b70-204c-4f90-8741-043f6abfb38d", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957847136900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef15f510-b064-4ccd-9a14-5e9796cd6a66", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 54 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957901681000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d302d64-b7aa-491a-935a-55788a29f975", "name": "entry : default@PreviewCompileResource cost memory -1.025390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957901856700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fef27db4-a0ab-4f9b-87dd-11d2c8388f85", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957847109400, "endTime": 13957901982400}, "additional": {"logType": "info", "children": [], "durationId": "92163168-22fb-4349-bce0-78716ee0cedb"}}, {"head": {"id": "1563d741-03a9-4114-9ed5-8a7f48922fd6", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905065900, "endTime": 13957905466200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "bc5e202a-0bab-4fac-b72e-525d5320b5cb", "logId": "7671c559-850e-42c9-8756-e1713748a229"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc5e202a-0bab-4fac-b72e-525d5320b5cb", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957904390100}, "additional": {"logType": "detail", "children": [], "durationId": "1563d741-03a9-4114-9ed5-8a7f48922fd6"}}, {"head": {"id": "a2af5b95-2fa4-47eb-ad7c-1ad036acc883", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957904879300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d3b55f-cd18-419e-af9f-5668556e7b2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957904980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e10db560-59ba-493b-94aa-924e609ef58e", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905073000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a545da-aaf2-480d-8059-cb8bbc4c9f53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905156400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d8bf19e-341a-493e-aaa0-2661f80c909f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905231300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28082ca3-97b8-4a64-a18b-2154671ede68", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384521484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905302500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70e51f0-38d2-425b-a11a-67a1f29b6d2d", "name": "runTaskFromQueue task cost before running: 8 s 30 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7671c559-850e-42c9-8756-e1713748a229", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957905065900, "endTime": 13957905466200, "totalTime": 311300}, "additional": {"logType": "info", "children": [], "durationId": "1563d741-03a9-4114-9ed5-8a7f48922fd6"}}, {"head": {"id": "10a1f9e9-4aa1-4f1c-b75e-73d4f2894abf", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957909105900, "endTime": 13957911598700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "345d32cd-3dc9-4c37-8f7d-8c98a319ffdb", "logId": "e3c191ff-d23c-4345-8b3f-a909c3635002"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "345d32cd-3dc9-4c37-8f7d-8c98a319ffdb", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957907552800}, "additional": {"logType": "detail", "children": [], "durationId": "10a1f9e9-4aa1-4f1c-b75e-73d4f2894abf"}}, {"head": {"id": "ea31a588-e591-433c-aed2-6a3ea9f5837c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957908396600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff82a7a7-598e-4841-8e89-a1a4c05f93fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957908481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3a24101-83aa-4826-bb83-9ae5f6b9f4bc", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957909116100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ea1f5e-e9dc-40e8-ba3f-6325c634e66f", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957911384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f50d2f6-9afb-4ad4-99a8-dc9fc307213e", "name": "entry : default@CopyPreviewProfile cost memory 0.09540557861328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957911507600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c191ff-d23c-4345-8b3f-a909c3635002", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957909105900, "endTime": 13957911598700}, "additional": {"logType": "info", "children": [], "durationId": "10a1f9e9-4aa1-4f1c-b75e-73d4f2894abf"}}, {"head": {"id": "b9e6763f-37cc-4241-9d77-c595c2dc0217", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914563000, "endTime": 13957915014100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "46695819-82e5-4e46-957a-3dbd580fc195", "logId": "1e0be8ec-9d06-41bb-bcfc-916160647d00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46695819-82e5-4e46-957a-3dbd580fc195", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957913306900}, "additional": {"logType": "detail", "children": [], "durationId": "b9e6763f-37cc-4241-9d77-c595c2dc0217"}}, {"head": {"id": "5f137821-05d5-4e8e-b7c0-b12613ebd2f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957913751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441adb72-f487-4f66-9094-09a69d0bd524", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957913855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0660f215-b308-411c-96e4-5ce5a7a8c200", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fd9114-90c8-47c9-85b7-cc70dad7adb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2431f18e-2697-4fe8-8f8e-51d7ba5da9db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707b6181-d0dd-4962-b887-5d4bd6a812c3", "name": "entry : default@ReplacePreviewerPage cost memory 0.04044342041015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914860100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c91247-e7fc-4295-a6e1-0897ca8ead56", "name": "runTaskFromQueue task cost before running: 8 s 39 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0be8ec-9d06-41bb-bcfc-916160647d00", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957914563000, "endTime": 13957915014100, "totalTime": 372800}, "additional": {"logType": "info", "children": [], "durationId": "b9e6763f-37cc-4241-9d77-c595c2dc0217"}}, {"head": {"id": "da79913a-8080-467d-9aff-c7117cc742d5", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916586700, "endTime": 13957916859900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "52cdeba4-8e5c-4849-8c89-1da683373e16", "logId": "367939c8-da99-46f5-a28d-9c68d1edf628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52cdeba4-8e5c-4849-8c89-1da683373e16", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916537800}, "additional": {"logType": "detail", "children": [], "durationId": "da79913a-8080-467d-9aff-c7117cc742d5"}}, {"head": {"id": "6e8b0bf7-31dd-45b0-81cb-05c6044e1061", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916593000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f15921e-abfc-4a54-8d68-b7758164e2bd", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f9f8f6-ed0a-4f80-a262-24922a830dcc", "name": "runTaskFromQueue task cost before running: 8 s 41 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916774600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367939c8-da99-46f5-a28d-9c68d1edf628", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957916586700, "endTime": 13957916859900, "totalTime": 167500}, "additional": {"logType": "info", "children": [], "durationId": "da79913a-8080-467d-9aff-c7117cc742d5"}}, {"head": {"id": "78ed7f44-9cda-4bcb-aa46-a890f4e3cd4e", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957919647500, "endTime": 13957974046600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1ba17a5b-79d0-42fd-b779-db208d4dcc4c", "logId": "46d4d988-4aaa-4a1e-b99a-031b90aa12a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ba17a5b-79d0-42fd-b779-db208d4dcc4c", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957918347600}, "additional": {"logType": "detail", "children": [], "durationId": "78ed7f44-9cda-4bcb-aa46-a890f4e3cd4e"}}, {"head": {"id": "e22fbdf7-0d8e-4d63-b063-74afbd45e7db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957918824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca62e134-5dd5-44fb-a542-3babeb46ebbf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957918905900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "022054ba-f7dd-49ce-8172-5e7ad8eb6631", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957919654700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a077a9d-11a4-42c9-bc56-eb8700853c09", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957973833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c228905a-847e-46df-9aef-4cd2cf6f469e", "name": "entry : default@PreviewUpdateAssets cost memory 0.10372161865234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957973966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46d4d988-4aaa-4a1e-b99a-031b90aa12a5", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13957919647500, "endTime": 13957974046600}, "additional": {"logType": "info", "children": [], "durationId": "78ed7f44-9cda-4bcb-aa46-a890f4e3cd4e"}}, {"head": {"id": "55e75195-7795-4ae8-bbd4-9b597af69dfe", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958063350300, "endTime": 13999734873700}, "additional": {"children": ["ebe37b80-d695-403c-b3bb-a413a87dcc9e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "097fdbc3-3b2e-4c7b-99b3-234ba1f13aa4", "logId": "2fca469d-a38c-4883-9820-4e70768b3902"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "097fdbc3-3b2e-4c7b-99b3-234ba1f13aa4", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958053986000}, "additional": {"logType": "detail", "children": [], "durationId": "55e75195-7795-4ae8-bbd4-9b597af69dfe"}}, {"head": {"id": "ca76c36c-af28-48c0-b63c-15cde5f41cef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958054765700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643efa48-3903-4716-94d0-06e1e0630531", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958054939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad144f86-224c-45db-a649-54a771314def", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958063377100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7746f0d4-290a-48a2-8070-fbb99eedc0ac", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958088021900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9b26b5-4b2c-4e3e-871b-8ed8ea8de4d7", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958088298500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13958211157200, "endTime": 13999731564800}, "additional": {"children": ["d232ec27-3a06-4560-aeca-c43a9f8db14b", "3667c06d-bdbd-4974-b920-b5bb291a148c", "35760dfa-9c8c-45f4-b0bd-9580d5b3d73e", "e6671b72-7ae5-4b39-a7e6-13d6bc3e4a1d", "7586d4e8-f5a2-4c81-a7b9-2089941a340a", "ac14b649-fa93-4814-afea-3a3d335ea0a7"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "55e75195-7795-4ae8-bbd4-9b597af69dfe", "logId": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76478a01-fdec-4cbf-ac87-3e5e219ca98d", "name": "entry : default@PreviewArkTS cost memory 1.3922195434570312", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958250709200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35da1e85-32ad-40b4-adf0-6bc733d4bd76", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13968332496200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb21fe4-a2d2-49f3-b4ea-f0afe7c388cf", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13968332723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454177b9-d996-409c-bb34-449949420beb", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13985643166400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d232ec27-3a06-4560-aeca-c43a9f8db14b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13985644291800, "endTime": 13985644310300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "403f14b5-df88-4828-82f7-18006eb5b98b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "403f14b5-df88-4828-82f7-18006eb5b98b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13985644291800, "endTime": 13985644310300}, "additional": {"logType": "info", "children": [], "durationId": "d232ec27-3a06-4560-aeca-c43a9f8db14b", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "3e886563-9431-4116-bd63-ae9ce1915706", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999730156800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3667c06d-bdbd-4974-b920-b5bb291a148c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13999731189600, "endTime": 13999731207300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "aa219407-d2d7-49e6-a024-c0505b32ed15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa219407-d2d7-49e6-a024-c0505b32ed15", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999731189600, "endTime": 13999731207300}, "additional": {"logType": "info", "children": [], "durationId": "3667c06d-bdbd-4974-b920-b5bb291a148c", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "ba9eec4c-18d5-41f7-9840-390bc06d6b04", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13958211157200, "endTime": 13999731564800}, "additional": {"logType": "info", "children": ["403f14b5-df88-4828-82f7-18006eb5b98b", "aa219407-d2d7-49e6-a024-c0505b32ed15", "736dc376-03b8-453b-ade8-f6597cecc384", "f24650e5-a800-4ab3-bbd2-8925e1bc56ae", "06b37ef5-6e88-4138-b4f1-e1f3d53f1f47", "4efae61f-bcce-4ec2-b9fd-34e802509b75"], "durationId": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "parent": "2fca469d-a38c-4883-9820-4e70768b3902"}}, {"head": {"id": "35760dfa-9c8c-45f4-b0bd-9580d5b3d73e", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13979274838400, "endTime": 13985582418700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "736dc376-03b8-453b-ade8-f6597cecc384"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "736dc376-03b8-453b-ade8-f6597cecc384", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13979274838400, "endTime": 13985582418700}, "additional": {"logType": "info", "children": [], "durationId": "35760dfa-9c8c-45f4-b0bd-9580d5b3d73e", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "e6671b72-7ae5-4b39-a7e6-13d6bc3e4a1d", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13985582668700, "endTime": 13985587733100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "f24650e5-a800-4ab3-bbd2-8925e1bc56ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f24650e5-a800-4ab3-bbd2-8925e1bc56ae", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13985582668700, "endTime": 13985587733100}, "additional": {"logType": "info", "children": [], "durationId": "e6671b72-7ae5-4b39-a7e6-13d6bc3e4a1d", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "7586d4e8-f5a2-4c81-a7b9-2089941a340a", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13985587831100, "endTime": 13985587836500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "06b37ef5-6e88-4138-b4f1-e1f3d53f1f47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06b37ef5-6e88-4138-b4f1-e1f3d53f1f47", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13985587831100, "endTime": 13985587836500}, "additional": {"logType": "info", "children": [], "durationId": "7586d4e8-f5a2-4c81-a7b9-2089941a340a", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "ac14b649-fa93-4814-afea-3a3d335ea0a7", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker6", "startTime": 13985587896100, "endTime": 13999730243800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ebe37b80-d695-403c-b3bb-a413a87dcc9e", "logId": "4efae61f-bcce-4ec2-b9fd-34e802509b75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4efae61f-bcce-4ec2-b9fd-34e802509b75", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13985587896100, "endTime": 13999730243800}, "additional": {"logType": "info", "children": [], "durationId": "ac14b649-fa93-4814-afea-3a3d335ea0a7", "parent": "ba9eec4c-18d5-41f7-9840-390bc06d6b04"}}, {"head": {"id": "2fca469d-a38c-4883-9820-4e70768b3902", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13958063350300, "endTime": 13999734873700, "totalTime": 41671512600}, "additional": {"logType": "info", "children": ["ba9eec4c-18d5-41f7-9840-390bc06d6b04"], "durationId": "55e75195-7795-4ae8-bbd4-9b597af69dfe"}}, {"head": {"id": "8d1ff819-029f-40dc-9263-21d18fd0f866", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740164400, "endTime": 13999740492200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "bb7ce407-a42a-4a5b-b834-080c494afc5d", "logId": "be26206c-9c44-47d0-b543-9f05f0842d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb7ce407-a42a-4a5b-b834-080c494afc5d", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740114300}, "additional": {"logType": "detail", "children": [], "durationId": "8d1ff819-029f-40dc-9263-21d18fd0f866"}}, {"head": {"id": "c93bc529-c467-4b11-a7f1-0df907598257", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eaf420b-cebd-4292-a531-ff4c43aa8610", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62adb48-dd25-46f5-9bc0-da4b58588bdf", "name": "runTaskFromQueue task cost before running: 49 s 865 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be26206c-9c44-47d0-b543-9f05f0842d60", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999740164400, "endTime": 13999740492200, "totalTime": 213500}, "additional": {"logType": "info", "children": [], "durationId": "8d1ff819-029f-40dc-9263-21d18fd0f866"}}, {"head": {"id": "fe54559f-e3d9-4aa3-80ae-af7964e99f11", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765309000, "endTime": 13999765333400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d07bb74-46f3-4f50-bf0f-c0e35ec383b8", "logId": "2b8181af-ff47-453f-8e63-1e31480e8694"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b8181af-ff47-453f-8e63-1e31480e8694", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765309000, "endTime": 13999765333400}, "additional": {"logType": "info", "children": [], "durationId": "fe54559f-e3d9-4aa3-80ae-af7964e99f11"}}, {"head": {"id": "410fc9a4-d2db-490b-9073-5dd20d7e8d87", "name": "BUILD SUCCESSFUL in 49 s 890 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765381200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "107dd3f7-c882-4ae1-9158-154ce44ed9f1", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13949876166600, "endTime": 13999765620300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 43}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "eb897beb-9a3a-4e79-aa9d-20771ece5c72", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765643900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3288c2b-c241-4718-a03f-373cac618496", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f54cb4-1fb8-4e44-9997-44ee134e6308", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b76bcb-38a4-4577-bdda-27a00faaa18e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765808400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56dfea53-50db-4e81-bb13-850751deece9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8190337-a771-4d11-8019-4472567b5bfd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765898800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0efffb40-1c63-41a1-999c-94321db64366", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90dad6d1-07eb-413f-b8c3-b02c8b7d659e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999765994000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e876870-613a-4246-bf90-4554066372f3", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999781100800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ab965a-d5cf-419b-afd9-dd09a2c4194c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999781251900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c15e03-c436-4f3a-bb6f-34676c74d8c7", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999784486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c067d8b5-19fe-4e25-939b-d10a01096e37", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999785394000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d7dd3a-5215-4976-8b0a-56dc7e2046ae", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999785718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e06aaf3-7816-4a65-82f3-6b2504970844", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999786014300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5850e962-0619-4994-8c2a-326e0d08a88c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999786939600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd6a811-69b9-498b-a8f1-fa7a8a664520", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999797547000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "844fea72-63a2-46fb-a7a8-e14f91ec1b2a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999797869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3187be-be80-4f31-bd61-399afbc981a4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999798202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f45bba0-aa64-4677-a2f6-9a9f76511050", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999798723600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85e5237b-74b6-4f19-b23d-cb0adc4265b7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999798998100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}