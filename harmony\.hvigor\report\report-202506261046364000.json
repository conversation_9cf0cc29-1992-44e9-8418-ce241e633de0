{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "5981eec4-5c08-41fb-9a32-bd65524fe389", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313126348353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c8ce925-28a8-4cda-aa42-034f7174abb0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313126352904700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "797acd8f-7bf6-478c-86c3-2fd48ea87221", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313126353316000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8af0d1d-634d-4b0c-91d9-5d7f4c867e76", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313126354931900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13fbea5f-7d3c-4002-bbb5-5fc6ef25b8c8", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313126360253400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c45c8e1-3aac-4d67-95bd-f8081c02f9ec", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162696758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162725847200, "endTime": 313164376703800}, "additional": {"children": ["248294ab-c5a7-4b9d-906b-d83bf26acef5", "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "cc4b84da-709d-4369-a289-9fe631e88fff", "1b3ad431-0f75-48a0-aea1-5291ffd0c79b", "9b4e9087-3260-4202-bd7f-51d25d4faa71", "dd96edf5-b2c0-4680-8b72-26e3111ee715", "93f840c2-ecb2-4c21-97ad-3e8b4beb6b05"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "248294ab-c5a7-4b9d-906b-d83bf26acef5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162725869300, "endTime": 313162851598400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "79a65648-834f-4c21-8bfb-20ef7bdec3e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162851636700, "endTime": 313164371369300}, "additional": {"children": ["5b1f23bd-9180-4006-825e-87b49b436996", "8b1f5ee1-d96f-4fbc-b2e2-bdfc6db8e999", "759ea640-318e-4b75-a2c8-964f31fe6796", "8ada5cc5-2a7a-44b5-9ae5-15dad6008673", "d6972be7-2c22-4048-89dd-aaa74519a3ed", "b9b66758-28eb-4cd3-9dcd-822c303e2d00", "47687d53-9d2d-430a-8fec-fdbd8792de4e", "5c0b0342-d643-44c9-bdde-bbd58220bf85", "77e6614f-152e-447f-aabe-1acb2153e3b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc4b84da-709d-4369-a289-9fe631e88fff", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164371456500, "endTime": 313164376600700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "097fb023-0378-4480-9bf7-edd74d320123"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b3ad431-0f75-48a0-aea1-5291ffd0c79b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164376626300, "endTime": 313164376681700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "128192c7-460f-4d1d-ab07-e4fe95d5d4d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b4e9087-3260-4202-bd7f-51d25d4faa71", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162739207200, "endTime": 313162739312000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "e7a9771b-0f8d-430e-b7b7-874d4940a8f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7a9771b-0f8d-430e-b7b7-874d4940a8f0", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162739207200, "endTime": 313162739312000}, "additional": {"logType": "info", "children": [], "durationId": "9b4e9087-3260-4202-bd7f-51d25d4faa71", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "dd96edf5-b2c0-4680-8b72-26e3111ee715", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162802636900, "endTime": 313162802681900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "33aa6405-e090-4d2a-846e-08e433047c52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33aa6405-e090-4d2a-846e-08e433047c52", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162802636900, "endTime": 313162802681900}, "additional": {"logType": "info", "children": [], "durationId": "dd96edf5-b2c0-4680-8b72-26e3111ee715", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "3d307e5b-ee91-4153-992e-97ea43e0a74d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162835918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cafca037-1aa0-40dc-8c1e-c9611b5a0561", "name": "Cache service initialization finished in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162851248200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a65648-834f-4c21-8bfb-20ef7bdec3e9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162725869300, "endTime": 313162851598400}, "additional": {"logType": "info", "children": [], "durationId": "248294ab-c5a7-4b9d-906b-d83bf26acef5", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "5b1f23bd-9180-4006-825e-87b49b436996", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162902444600, "endTime": 313162902484700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "a383f1c2-567f-4864-902e-415aac4d653e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b1f5ee1-d96f-4fbc-b2e2-bdfc6db8e999", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162902543600, "endTime": 313162926398900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "02952e39-39c6-4f62-87a3-bfcfb538cef8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "759ea640-318e-4b75-a2c8-964f31fe6796", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162926438100, "endTime": 313163498739500}, "additional": {"children": ["3580dea0-2fa0-49f3-8f7b-1742567c7905", "5338826d-928b-46c7-984a-ba8f0e390e13", "2621e268-701b-4654-b8e4-c77dedf6c4e1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "93779537-66e4-4e3a-9763-99cdf0e9e902"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ada5cc5-2a7a-44b5-9ae5-15dad6008673", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163498806100, "endTime": 313163786782400}, "additional": {"children": ["9a4a3f0a-8420-48bb-956d-e935350f4efb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "97c0d878-a435-45fa-a72d-8b61ec76dce3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6972be7-2c22-4048-89dd-aaa74519a3ed", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163786819700, "endTime": 313164158582500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "3cf2d5bf-7ec6-44cc-b495-991dac0a9268"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9b66758-28eb-4cd3-9dcd-822c303e2d00", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164162016000, "endTime": 313164292648400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "7990dbe4-f409-4475-b044-04418c812ced"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47687d53-9d2d-430a-8fec-fdbd8792de4e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164292708700, "endTime": 313164370306700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "893314fe-281d-44a5-88e7-3367adb51050"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c0b0342-d643-44c9-bdde-bbd58220bf85", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164370361900, "endTime": 313164371326000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "4554fb8c-ad50-4326-814e-7af4c35584e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a383f1c2-567f-4864-902e-415aac4d653e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162902444600, "endTime": 313162902484700}, "additional": {"logType": "info", "children": [], "durationId": "5b1f23bd-9180-4006-825e-87b49b436996", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "02952e39-39c6-4f62-87a3-bfcfb538cef8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162902543600, "endTime": 313162926398900}, "additional": {"logType": "info", "children": [], "durationId": "8b1f5ee1-d96f-4fbc-b2e2-bdfc6db8e999", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "3580dea0-2fa0-49f3-8f7b-1742567c7905", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162942874100, "endTime": 313162942927600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "759ea640-318e-4b75-a2c8-964f31fe6796", "logId": "3899ec2d-5dbf-468a-8728-19ca1af4cd43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3899ec2d-5dbf-468a-8728-19ca1af4cd43", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162942874100, "endTime": 313162942927600}, "additional": {"logType": "info", "children": [], "durationId": "3580dea0-2fa0-49f3-8f7b-1742567c7905", "parent": "93779537-66e4-4e3a-9763-99cdf0e9e902"}}, {"head": {"id": "5338826d-928b-46c7-984a-ba8f0e390e13", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162952392000, "endTime": 313163491163500}, "additional": {"children": ["3b5f5968-c2aa-448d-9c37-6ee5c3ff0719", "c55be669-6aba-45fd-b1c5-9e81ac458b1b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "759ea640-318e-4b75-a2c8-964f31fe6796", "logId": "4a59b96d-7cd2-48f0-9dcb-6d2e09593e9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b5f5968-c2aa-448d-9c37-6ee5c3ff0719", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162952397200, "endTime": 313163011090900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5338826d-928b-46c7-984a-ba8f0e390e13", "logId": "fa918b30-a125-4381-9682-3b01e0247b49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c55be669-6aba-45fd-b1c5-9e81ac458b1b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163011190700, "endTime": 313163491120800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5338826d-928b-46c7-984a-ba8f0e390e13", "logId": "f14c9294-5239-4c88-bbf5-a8f1c1702616"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18fa22eb-5748-4112-aaaf-fe159eaac65e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162952463100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4b9aea-700d-49ae-8aef-03bf7e11268d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163009381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa918b30-a125-4381-9682-3b01e0247b49", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162952397200, "endTime": 313163011090900}, "additional": {"logType": "info", "children": [], "durationId": "3b5f5968-c2aa-448d-9c37-6ee5c3ff0719", "parent": "4a59b96d-7cd2-48f0-9dcb-6d2e09593e9a"}}, {"head": {"id": "7e1e4350-4ccf-4c52-9ebd-1278accde3c9", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163011402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "954607a7-3fed-46c1-ad1d-c77e553200fb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163128272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1df5fdb-33c4-4798-962a-7da8a18e1d38", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163128653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff63692-43cc-4d2c-8eee-9f36f2454986", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163130686800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1069d8-6c38-4e22-b21e-948cd1ec8fd6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163135997300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137f3ff4-0999-4bfe-990f-09d854cd5256", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163151536800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "188057ca-80d3-4775-b1c6-e9c6ccde4b12", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163172292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f60a50b-3796-4ac9-a7ce-44bb261f188d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163217697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8819f4-ac63-4c2e-9472-985467f989c6", "name": "Sdk init in 194 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163370232200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6bf3b7f-60dd-494c-bf1f-3404645dfc13", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163370657100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 46}, "markType": "other"}}, {"head": {"id": "63c60cb2-f0ed-477d-9ccd-d38af4fd8533", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163370789500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 46}, "markType": "other"}}, {"head": {"id": "0201acf5-1c50-478b-a29d-c3c08c38e915", "name": "Project task initialization takes 115 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163489990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13bc634b-cd2c-4f9c-9a4c-ae592e63947d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163490360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efbd439d-cbbb-4ce7-9931-6746e8c1a03c", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163490687200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e28d3ca9-5fc8-42fe-a58a-5f9c97729b30", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163490917800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f14c9294-5239-4c88-bbf5-a8f1c1702616", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163011190700, "endTime": 313163491120800}, "additional": {"logType": "info", "children": [], "durationId": "c55be669-6aba-45fd-b1c5-9e81ac458b1b", "parent": "4a59b96d-7cd2-48f0-9dcb-6d2e09593e9a"}}, {"head": {"id": "4a59b96d-7cd2-48f0-9dcb-6d2e09593e9a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162952392000, "endTime": 313163491163500}, "additional": {"logType": "info", "children": ["fa918b30-a125-4381-9682-3b01e0247b49", "f14c9294-5239-4c88-bbf5-a8f1c1702616"], "durationId": "5338826d-928b-46c7-984a-ba8f0e390e13", "parent": "93779537-66e4-4e3a-9763-99cdf0e9e902"}}, {"head": {"id": "2621e268-701b-4654-b8e4-c77dedf6c4e1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163496156400, "endTime": 313163498454100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "759ea640-318e-4b75-a2c8-964f31fe6796", "logId": "de6e88f1-c110-42df-9dd0-96620ff03e70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de6e88f1-c110-42df-9dd0-96620ff03e70", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163496156400, "endTime": 313163498454100}, "additional": {"logType": "info", "children": [], "durationId": "2621e268-701b-4654-b8e4-c77dedf6c4e1", "parent": "93779537-66e4-4e3a-9763-99cdf0e9e902"}}, {"head": {"id": "93779537-66e4-4e3a-9763-99cdf0e9e902", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162926438100, "endTime": 313163498739500}, "additional": {"logType": "info", "children": ["3899ec2d-5dbf-468a-8728-19ca1af4cd43", "4a59b96d-7cd2-48f0-9dcb-6d2e09593e9a", "de6e88f1-c110-42df-9dd0-96620ff03e70"], "durationId": "759ea640-318e-4b75-a2c8-964f31fe6796", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "9a4a3f0a-8420-48bb-956d-e935350f4efb", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163545259300, "endTime": 313163786728900}, "additional": {"children": ["812c3783-cc78-471b-9395-b4673235a3a5", "bae068e6-74c1-4cd5-8925-4e0ca0d75a65", "3ff9b1dc-009a-43e8-860d-1ba3cbc95dbe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ada5cc5-2a7a-44b5-9ae5-15dad6008673", "logId": "c6d814fc-a9a6-40d6-b096-660bbb143e5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "812c3783-cc78-471b-9395-b4673235a3a5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163562169300, "endTime": 313163562232100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a4a3f0a-8420-48bb-956d-e935350f4efb", "logId": "724d3c1a-e27a-42d7-be37-6a0c13bcad86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "724d3c1a-e27a-42d7-be37-6a0c13bcad86", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163562169300, "endTime": 313163562232100}, "additional": {"logType": "info", "children": [], "durationId": "812c3783-cc78-471b-9395-b4673235a3a5", "parent": "c6d814fc-a9a6-40d6-b096-660bbb143e5f"}}, {"head": {"id": "bae068e6-74c1-4cd5-8925-4e0ca0d75a65", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163572818300, "endTime": 313163758414500}, "additional": {"children": ["ea12d277-1726-4714-8d6d-689d385f8eb0", "06a30013-a7dc-437e-8ed5-1c306a348bfe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a4a3f0a-8420-48bb-956d-e935350f4efb", "logId": "6d9f3fbb-54e9-44a5-809f-fe267d95a6b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea12d277-1726-4714-8d6d-689d385f8eb0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163572823900, "endTime": 313163596760700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bae068e6-74c1-4cd5-8925-4e0ca0d75a65", "logId": "a2fa2f94-1192-4402-b10b-fff4b71c7010"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06a30013-a7dc-437e-8ed5-1c306a348bfe", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163596847700, "endTime": 313163758370700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bae068e6-74c1-4cd5-8925-4e0ca0d75a65", "logId": "23ad9a3c-6508-4c91-8a50-963590af1393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b73c28a5-18e6-4646-961d-885fe092cb72", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163572929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b1d95f-961e-47e3-ba69-d0dd48f8fa89", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163593214800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2fa2f94-1192-4402-b10b-fff4b71c7010", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163572823900, "endTime": 313163596760700}, "additional": {"logType": "info", "children": [], "durationId": "ea12d277-1726-4714-8d6d-689d385f8eb0", "parent": "6d9f3fbb-54e9-44a5-809f-fe267d95a6b9"}}, {"head": {"id": "48325c51-35db-43dd-b63a-a4c3eca89012", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163596933800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0af4374-5e8e-47ac-a8cd-a5c4aab1934c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163727416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94e0bfa9-2ad2-4b77-a09a-cb9c22078b97", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163729674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f09072-2de3-41c3-815c-396184a73516", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163732063900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1c8dff-d32d-450a-bc9c-b579c7361460", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163733561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a67ccb-81c1-4aa7-adfe-2d0db86f40ab", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163734433400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0e100e-b2a6-47d0-91b8-44e51dbc3b85", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163734932900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a316733-adb7-4002-979d-dec203069beb", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163737375700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5582b87e-f83f-4482-a581-6487ce442302", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163756730900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d481247a-e074-4091-ba3d-71e7a2088717", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163757503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7607c5-8b74-4152-bcc2-08d681461ab8", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163757800000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367dabd4-962e-410e-86a5-eacb23691821", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163758116300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ad9a3c-6508-4c91-8a50-963590af1393", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163596847700, "endTime": 313163758370700}, "additional": {"logType": "info", "children": [], "durationId": "06a30013-a7dc-437e-8ed5-1c306a348bfe", "parent": "6d9f3fbb-54e9-44a5-809f-fe267d95a6b9"}}, {"head": {"id": "6d9f3fbb-54e9-44a5-809f-fe267d95a6b9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163572818300, "endTime": 313163758414500}, "additional": {"logType": "info", "children": ["a2fa2f94-1192-4402-b10b-fff4b71c7010", "23ad9a3c-6508-4c91-8a50-963590af1393"], "durationId": "bae068e6-74c1-4cd5-8925-4e0ca0d75a65", "parent": "c6d814fc-a9a6-40d6-b096-660bbb143e5f"}}, {"head": {"id": "3ff9b1dc-009a-43e8-860d-1ba3cbc95dbe", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163786598200, "endTime": 313163786666000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a4a3f0a-8420-48bb-956d-e935350f4efb", "logId": "75369801-c965-45da-b28c-ab88c40743d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75369801-c965-45da-b28c-ab88c40743d5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163786598200, "endTime": 313163786666000}, "additional": {"logType": "info", "children": [], "durationId": "3ff9b1dc-009a-43e8-860d-1ba3cbc95dbe", "parent": "c6d814fc-a9a6-40d6-b096-660bbb143e5f"}}, {"head": {"id": "c6d814fc-a9a6-40d6-b096-660bbb143e5f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163545259300, "endTime": 313163786728900}, "additional": {"logType": "info", "children": ["724d3c1a-e27a-42d7-be37-6a0c13bcad86", "6d9f3fbb-54e9-44a5-809f-fe267d95a6b9", "75369801-c965-45da-b28c-ab88c40743d5"], "durationId": "9a4a3f0a-8420-48bb-956d-e935350f4efb", "parent": "97c0d878-a435-45fa-a72d-8b61ec76dce3"}}, {"head": {"id": "97c0d878-a435-45fa-a72d-8b61ec76dce3", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163498806100, "endTime": 313163786782400}, "additional": {"logType": "info", "children": ["c6d814fc-a9a6-40d6-b096-660bbb143e5f"], "durationId": "8ada5cc5-2a7a-44b5-9ae5-15dad6008673", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "d89255df-f44f-463d-a7cb-72cefb9baf7f", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164156278800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64d6a0a-f0bc-4463-b25a-fe4144819be9", "name": "hvigorfile, resolve hvigorfile dependencies in 372 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164158160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf2d5bf-7ec6-44cc-b495-991dac0a9268", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313163786819700, "endTime": 313164158582500}, "additional": {"logType": "info", "children": [], "durationId": "d6972be7-2c22-4048-89dd-aaa74519a3ed", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "77e6614f-152e-447f-aabe-1acb2153e3b7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164161377300, "endTime": 313164161971300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "logId": "30305578-3476-4239-851c-297b98fb3acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf992e40-b905-4ca9-a731-2f116066b506", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164161471100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30305578-3476-4239-851c-297b98fb3acf", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164161377300, "endTime": 313164161971300}, "additional": {"logType": "info", "children": [], "durationId": "77e6614f-152e-447f-aabe-1acb2153e3b7", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "1362dff9-91c8-49c7-a2bf-a4112b3545e7", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164170146400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7999fd69-4e1d-497e-a0a4-f92284c5d680", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164289655000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7990dbe4-f409-4475-b044-04418c812ced", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164162016000, "endTime": 313164292648400}, "additional": {"logType": "info", "children": [], "durationId": "b9b66758-28eb-4cd3-9dcd-822c303e2d00", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "f700d5e8-9a50-412e-a384-0684a88bbba6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164292749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aa979f8-50f0-44d5-b960-7916f1e35ff4", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164344689600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8b3691a-663f-4ce1-945a-5fbafb72290e", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164345029100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61084b6f-3261-4bda-b2e1-3b37d9000cf8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164345727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "767eb2ee-7cc0-4757-b880-2402a497112d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164359159400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ecb295b-fe49-4b44-a244-8f0914b1b305", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164359548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893314fe-281d-44a5-88e7-3367adb51050", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164292708700, "endTime": 313164370306700}, "additional": {"logType": "info", "children": [], "durationId": "47687d53-9d2d-430a-8fec-fdbd8792de4e", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "bdf2ad0f-18d5-4aca-b922-9590e32007f9", "name": "Configuration phase cost:1 s 468 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164371007600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4554fb8c-ad50-4326-814e-7af4c35584e2", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164370361900, "endTime": 313164371326000}, "additional": {"logType": "info", "children": [], "durationId": "5c0b0342-d643-44c9-bdde-bbd58220bf85", "parent": "a94300a1-6fc1-42bd-9844-f4a67d25d323"}}, {"head": {"id": "a94300a1-6fc1-42bd-9844-f4a67d25d323", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162851636700, "endTime": 313164371369300}, "additional": {"logType": "info", "children": ["a383f1c2-567f-4864-902e-415aac4d653e", "02952e39-39c6-4f62-87a3-bfcfb538cef8", "93779537-66e4-4e3a-9763-99cdf0e9e902", "97c0d878-a435-45fa-a72d-8b61ec76dce3", "3cf2d5bf-7ec6-44cc-b495-991dac0a9268", "7990dbe4-f409-4475-b044-04418c812ced", "893314fe-281d-44a5-88e7-3367adb51050", "4554fb8c-ad50-4326-814e-7af4c35584e2", "30305578-3476-4239-851c-297b98fb3acf"], "durationId": "ed6f3944-3568-4cdd-97ef-6a90f27bfc1d", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "93f840c2-ecb2-4c21-97ad-3e8b4beb6b05", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164376509500, "endTime": 313164376558900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84", "logId": "d6dd4cee-f4ea-4522-8dd1-184de3dbf084"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6dd4cee-f4ea-4522-8dd1-184de3dbf084", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164376509500, "endTime": 313164376558900}, "additional": {"logType": "info", "children": [], "durationId": "93f840c2-ecb2-4c21-97ad-3e8b4beb6b05", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "097fb023-0378-4480-9bf7-edd74d320123", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164371456500, "endTime": 313164376600700}, "additional": {"logType": "info", "children": [], "durationId": "cc4b84da-709d-4369-a289-9fe631e88fff", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "128192c7-460f-4d1d-ab07-e4fe95d5d4d2", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164376626300, "endTime": 313164376681700}, "additional": {"logType": "info", "children": [], "durationId": "1b3ad431-0f75-48a0-aea1-5291ffd0c79b", "parent": "5417b8d2-cb33-41fd-b591-fa9bf725f135"}}, {"head": {"id": "5417b8d2-cb33-41fd-b591-fa9bf725f135", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162725847200, "endTime": 313164376703800}, "additional": {"logType": "info", "children": ["79a65648-834f-4c21-8bfb-20ef7bdec3e9", "a94300a1-6fc1-42bd-9844-f4a67d25d323", "097fb023-0378-4480-9bf7-edd74d320123", "128192c7-460f-4d1d-ab07-e4fe95d5d4d2", "e7a9771b-0f8d-430e-b7b7-874d4940a8f0", "33aa6405-e090-4d2a-846e-08e433047c52", "d6dd4cee-f4ea-4522-8dd1-184de3dbf084"], "durationId": "9a3ab831-4952-4bf1-b0e8-cf91aa35cc84"}}, {"head": {"id": "123e8244-d42e-458a-9efd-4dbad3e964a2", "name": "Configuration task cost before running: 1 s 666 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164377767200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63830734-79e6-4773-8b27-31a4f12c46b9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164410255700, "endTime": 313164488747400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "66c8a7c4-b3d5-4a59-acb6-16ccf81db06f", "logId": "8c5065a5-cc4a-47cd-967e-f4a09c20be10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66c8a7c4-b3d5-4a59-acb6-16ccf81db06f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164386468900}, "additional": {"logType": "detail", "children": [], "durationId": "63830734-79e6-4773-8b27-31a4f12c46b9"}}, {"head": {"id": "5f6c78c2-74fa-42de-a98b-bb11a0eb6402", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164390262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37e0d69-f6d7-4e65-be93-053a60b01514", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164390682900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca0f4ff-96f5-4198-8984-d8caecdeee30", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164410304600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dd07b2f-48ec-4eec-a3e2-9ab18cd9b69f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 49 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164487858700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4c8b2d-8741-4151-a2e6-883d4ecc0c44", "name": "entry : default@PreBuild cost memory 0.29994964599609375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164488297200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5065a5-cc4a-47cd-967e-f4a09c20be10", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164410255700, "endTime": 313164488747400}, "additional": {"logType": "info", "children": [], "durationId": "63830734-79e6-4773-8b27-31a4f12c46b9"}}, {"head": {"id": "6ece25fd-da82-4376-a480-4ea8270a53b9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164515748900, "endTime": 313164526998400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "848b3295-db09-4843-b93f-c23d538b5a56", "logId": "01c03d8f-c009-4c81-953e-f858a86cf501"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "848b3295-db09-4843-b93f-c23d538b5a56", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164507851900}, "additional": {"logType": "detail", "children": [], "durationId": "6ece25fd-da82-4376-a480-4ea8270a53b9"}}, {"head": {"id": "5432d11a-0527-4564-8e91-5d29fdc5f78c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164511157100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010be31e-46b9-497c-a31a-d56edebe044a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164511558800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27416966-ad8e-4154-a2ee-0ec3412831d9", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164515798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46e36a0-5aec-4611-b37e-bf7f9e81a5c5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164526257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5c27f29-766b-4fdd-8463-1d928a8a13cc", "name": "entry : default@MergeProfile cost memory 0.11504364013671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164526688900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c03d8f-c009-4c81-953e-f858a86cf501", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164515748900, "endTime": 313164526998400}, "additional": {"logType": "info", "children": [], "durationId": "6ece25fd-da82-4376-a480-4ea8270a53b9"}}, {"head": {"id": "53951f7c-5eb3-45c1-84e6-fff0914087ea", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164539722300, "endTime": 313164551807600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "47e06841-44be-42d9-ad87-3b1f6679617f", "logId": "3cba7423-b6f9-40b1-b49f-0fbee08c2560"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47e06841-44be-42d9-ad87-3b1f6679617f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164533366800}, "additional": {"logType": "detail", "children": [], "durationId": "53951f7c-5eb3-45c1-84e6-fff0914087ea"}}, {"head": {"id": "0374b603-1701-4492-ad2d-4eb95912110b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164535132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5e0801-84ab-4278-9120-b343e4f2c852", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164535430800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d9dd124-2dcd-4233-abb9-b6f07b313df4", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164539782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d8cf96-6961-452d-a19c-8cf13fed757a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164544925400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "236c49b3-505f-40aa-a010-3367931a2c2f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164551000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "511cebd7-9a9a-4279-9336-0d1e34841b7d", "name": "entry : default@CreateBuildProfile cost memory 0.10170745849609375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164551466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cba7423-b6f9-40b1-b49f-0fbee08c2560", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164539722300, "endTime": 313164551807600}, "additional": {"logType": "info", "children": [], "durationId": "53951f7c-5eb3-45c1-84e6-fff0914087ea"}}, {"head": {"id": "e1988053-5ab4-4aa2-b43a-5c4e4b57d8b6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164574276900, "endTime": 313164576911900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "266e04ae-c8a9-4b32-8c3f-3bc593c857d7", "logId": "7a9750dc-a278-4791-86c9-876bff501bf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "266e04ae-c8a9-4b32-8c3f-3bc593c857d7", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164558479600}, "additional": {"logType": "detail", "children": [], "durationId": "e1988053-5ab4-4aa2-b43a-5c4e4b57d8b6"}}, {"head": {"id": "5c42cf28-dd3e-4bc4-9c04-5bbe87fcb004", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164560330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4acbb85c-c428-45ae-af04-1af8d8ccd18d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164562053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b060492a-7540-4607-bbfb-f7b5973fe160", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164574337800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7380e3da-55b6-4ca1-8689-f10bef9cee64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164575148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f2f71d-1f68-4d4e-8948-e61c89b41808", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164575502200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c61a5ba-6969-4eb8-899c-fe6ed3070417", "name": "entry : default@PreCheckSyscap cost memory 0.0377655029296875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164576271900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2951cb-061c-4a69-a894-175ede824da5", "name": "runTaskFromQueue task cost before running: 1 s 865 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164576659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9750dc-a278-4791-86c9-876bff501bf1", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164574276900, "endTime": 313164576911900, "totalTime": 2308700}, "additional": {"logType": "info", "children": [], "durationId": "e1988053-5ab4-4aa2-b43a-5c4e4b57d8b6"}}, {"head": {"id": "dc0de87c-3afa-4fe8-99c1-cffc4e08e475", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164620425100, "endTime": 313164630352300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8b67965c-e22e-4613-8335-684fda793fb0", "logId": "fbc49b2f-f6e8-4fdc-b000-04809536acbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b67965c-e22e-4613-8335-684fda793fb0", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164584534800}, "additional": {"logType": "detail", "children": [], "durationId": "dc0de87c-3afa-4fe8-99c1-cffc4e08e475"}}, {"head": {"id": "9b60e5f9-3e9d-42c9-b967-0ee7d649906e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164586417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4e13bd-458c-47c0-b8c9-2f692163ff95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164586774800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf6e9ce-9d3c-47b9-9604-6c5d4e9cac55", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164620477200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f45db02-622e-46ec-84da-b8c5043ed4f3", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164621503900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b386e0-c648-4d95-a739-2afe185311c7", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164626536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9551378e-7a21-437b-a54e-59966157c6ea", "name": "entry : default@GeneratePkgContextInfo cost memory 0.067962646484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164627809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc49b2f-f6e8-4fdc-b000-04809536acbb", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164620425100, "endTime": 313164630352300}, "additional": {"logType": "info", "children": [], "durationId": "dc0de87c-3afa-4fe8-99c1-cffc4e08e475"}}, {"head": {"id": "f0423c3e-51bd-4aec-8ddc-a75a3c85aef2", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164685557800, "endTime": 313164702753800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "702439d4-f804-4987-b62a-93594a177610", "logId": "fcfb1c23-6067-45e0-a4a1-9469b4a4e1f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "702439d4-f804-4987-b62a-93594a177610", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164671655200}, "additional": {"logType": "detail", "children": [], "durationId": "f0423c3e-51bd-4aec-8ddc-a75a3c85aef2"}}, {"head": {"id": "ac8f3972-b3ed-468e-8c6c-4bc8aa13b7df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164673640300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d03a49-0935-4e18-8139-77061375149f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164673988000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e78d13-9066-496f-b348-23eb5a5c123e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164685615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc74e92-8624-4813-a4d9-37583736f859", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164701901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e22c29e7-5b80-4bfa-a802-533bf237f4a9", "name": "entry : default@ProcessProfile cost memory 0.05918121337890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164702446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcfb1c23-6067-45e0-a4a1-9469b4a4e1f9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164685557800, "endTime": 313164702753800}, "additional": {"logType": "info", "children": [], "durationId": "f0423c3e-51bd-4aec-8ddc-a75a3c85aef2"}}, {"head": {"id": "db1e9013-f469-407e-8174-2d01779b6305", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164734561500, "endTime": 313164903803100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cd3105fb-2dce-4714-9ec8-13200d19231a", "logId": "50ac1254-9e31-4260-bcb3-b79e9c81f1bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd3105fb-2dce-4714-9ec8-13200d19231a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164710769400}, "additional": {"logType": "detail", "children": [], "durationId": "db1e9013-f469-407e-8174-2d01779b6305"}}, {"head": {"id": "6456b833-db21-46cd-b91b-8e8de41aa31f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164712744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c9aa1e-c507-481d-b7db-94a655dff270", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164713096100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d3617e-f621-4f09-9c63-e971b188cfdc", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164734615800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d87490-5c22-407e-817c-596321b51f60", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 154 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164903057000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cee1ab8-c320-4f26-934e-72fda4b1af47", "name": "entry : default@ProcessRouterMap cost memory 0.19823455810546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164903491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ac1254-9e31-4260-bcb3-b79e9c81f1bb", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164734561500, "endTime": 313164903803100}, "additional": {"logType": "info", "children": [], "durationId": "db1e9013-f469-407e-8174-2d01779b6305"}}, {"head": {"id": "c5875965-1565-4f0d-a791-5484fed46a40", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165015695700, "endTime": 313165100221600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8b09f345-f83a-4586-af53-c681c003b8a2", "logId": "b56c6d06-29b6-4390-bbcd-48379f09a3a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b09f345-f83a-4586-af53-c681c003b8a2", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164918297100}, "additional": {"logType": "detail", "children": [], "durationId": "c5875965-1565-4f0d-a791-5484fed46a40"}}, {"head": {"id": "80bb8b6d-b03b-47f4-bc29-41a650ad9819", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164920353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7185079f-c7a9-4110-8283-bc24a8cb75eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164921493100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b8bd6d1-de59-4bbb-9ee4-50a0cc6602cb", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313164928465300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347ac5d7-b07d-49e9-bafc-bb8de2fb1a12", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165084167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a165916-d7b5-4af7-9c13-9f79f9d087a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165087525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "470809c2-a061-4789-a0c8-b378837d4998", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165088811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84be1a95-81c0-427c-96d5-1d1b34498e14", "name": "entry : default@PreviewProcessResource cost memory 0.07668304443359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165090764100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "966a008f-3f37-46d7-910e-dc81f02d7330", "name": "runTaskFromQueue task cost before running: 2 s 388 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165099896100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b56c6d06-29b6-4390-bbcd-48379f09a3a5", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165015695700, "endTime": 313165100221600, "totalTime": 77076900}, "additional": {"logType": "info", "children": [], "durationId": "c5875965-1565-4f0d-a791-5484fed46a40"}}, {"head": {"id": "dc20cbc0-097d-4005-ab80-f3b8f563ae3d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165156365800, "endTime": 313166309799700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "14b31e9b-9919-403c-a02e-d6680813b247", "logId": "d508d9e4-96a4-4a16-8a23-19d70ef1688b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14b31e9b-9919-403c-a02e-d6680813b247", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165128558900}, "additional": {"logType": "detail", "children": [], "durationId": "dc20cbc0-097d-4005-ab80-f3b8f563ae3d"}}, {"head": {"id": "47e9da1a-8f72-4512-b30a-091f1d69cfdc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165133711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29abeae4-76bf-4f13-9bd5-ad8cac99d092", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165134174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b244c0-3604-422a-93c2-79dd467b86f2", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165156423800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46ca951c-1d88-499b-be35-1745a0bd5675", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165290493100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823cac28-6e2a-4657-a95e-173b55138551", "name": "entry : default@GenerateLoaderJson cost memory 0.7371597290039062", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166308597900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d508d9e4-96a4-4a16-8a23-19d70ef1688b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313165156365800, "endTime": 313166309799700}, "additional": {"logType": "info", "children": [], "durationId": "dc20cbc0-097d-4005-ab80-f3b8f563ae3d"}}, {"head": {"id": "ac8f3e26-2a61-4d8f-8ab2-6c4296b62b8c", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166377321100, "endTime": 313177813770400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9914ccd0-c43b-4359-95dd-436f030f300a", "logId": "f4e9d12a-6022-4050-8546-cef43a93e1d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9914ccd0-c43b-4359-95dd-436f030f300a", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166347755400}, "additional": {"logType": "detail", "children": [], "durationId": "ac8f3e26-2a61-4d8f-8ab2-6c4296b62b8c"}}, {"head": {"id": "8b9c5bc8-c4e9-49cb-b751-6ed82804ef65", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166350011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40cd2408-c1a5-4f4d-9385-f28ac636c5f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166350381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2319e72e-4c30-44fe-9339-6900dbf548b9", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166366367600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a19010b-2e68-4870-a078-eafb65c1ab6c", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166377660400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5480ec-02b1-43ca-9893-f74865476946", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 11 s 428 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177813295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae12a80-ff15-44ba-b4f2-af4932c36937", "name": "entry : default@PreviewCompileResource cost memory -0.9838409423828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177813580500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e9d12a-6022-4050-8546-cef43a93e1d5", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313166377321100, "endTime": 313177813770400}, "additional": {"logType": "info", "children": [], "durationId": "ac8f3e26-2a61-4d8f-8ab2-6c4296b62b8c"}}, {"head": {"id": "b1b195db-001d-4863-9e38-3c362c71c527", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177819808400, "endTime": 313177820845300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4900af9d-9e05-4aa2-824c-ef42a0356893", "logId": "56427000-42da-4574-9fe7-d0e7b8881f59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4900af9d-9e05-4aa2-824c-ef42a0356893", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177818565200}, "additional": {"logType": "detail", "children": [], "durationId": "b1b195db-001d-4863-9e38-3c362c71c527"}}, {"head": {"id": "efc68912-dff3-4029-98cc-8954e6817c25", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177819443000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c433d787-5741-4dd5-885e-8ce0f4e12cb5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177819597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780ab6fc-d950-45a4-ad8d-6f89467bcd87", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177819835700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc89850b-4e92-4f80-aeb2-8b822ef44fde", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177820070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd17ea6f-30c8-44bd-a6b7-616816174dbf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177820226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fadf76a-c81c-4562-b52f-c43f4aae8dd0", "name": "entry : default@PreviewHookCompileResource cost memory 0.038848876953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177820475900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e99d7da-dc4c-41d2-8adb-1dc10dfb640f", "name": "runTaskFromQueue task cost before running: 15 s 109 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177820698300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56427000-42da-4574-9fe7-d0e7b8881f59", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177819808400, "endTime": 313177820845300, "totalTime": 841000}, "additional": {"logType": "info", "children": [], "durationId": "b1b195db-001d-4863-9e38-3c362c71c527"}}, {"head": {"id": "4d592d1a-382f-445d-bb4e-64539c945517", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177827703900, "endTime": 313177832471900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "36e5a504-4f6b-476b-be18-227b69d04080", "logId": "09077390-a7bf-4d1c-b157-be9fe61e0230"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36e5a504-4f6b-476b-be18-227b69d04080", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177824689900}, "additional": {"logType": "detail", "children": [], "durationId": "4d592d1a-382f-445d-bb4e-64539c945517"}}, {"head": {"id": "303d7c53-4c4f-4425-9b45-c408b4f01ef4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177825929700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9332c11-7f54-498f-bf8d-a8093bcd4115", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177826227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2643cccf-1a83-4697-b698-c439017590ed", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177827729300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "249a5a52-82ce-49e5-aef7-358f41b83ca8", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177832051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d528304c-9f89-4653-91f2-98449cfcae60", "name": "entry : default@CopyPreviewProfile cost memory 0.1021881103515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177832324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09077390-a7bf-4d1c-b157-be9fe61e0230", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177827703900, "endTime": 313177832471900}, "additional": {"logType": "info", "children": [], "durationId": "4d592d1a-382f-445d-bb4e-64539c945517"}}, {"head": {"id": "c2742055-d0fb-46f4-b4f3-68124e9d8509", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177838364900, "endTime": 313177839695100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "f1537103-c418-42d3-a72d-9a52be8c6a2c", "logId": "0cb705af-ca3a-4719-b5da-b215097a7182"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1537103-c418-42d3-a72d-9a52be8c6a2c", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177836077400}, "additional": {"logType": "detail", "children": [], "durationId": "c2742055-d0fb-46f4-b4f3-68124e9d8509"}}, {"head": {"id": "f59b6eea-57bc-431b-8791-1669438ab8cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177836907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2644b13-317d-434b-ab75-565b36706ce4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177837090100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef64faab-e947-4e60-8b73-4418e60406fd", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177838383900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ada949-5e4b-4c6e-8a48-fb4d0b5544e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177838591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "683ebffc-65d7-45ad-9a32-ec057a5598a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177838733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e05ab7e-0f87-423f-b5e4-fe0ba6bc40b4", "name": "entry : default@ReplacePreviewerPage cost memory 0.03903961181640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177839320700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "385f7d59-7af8-4820-9283-8bc3f24c18a8", "name": "runTaskFromQueue task cost before running: 15 s 128 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177839550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb705af-ca3a-4719-b5da-b215097a7182", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177838364900, "endTime": 313177839695100, "totalTime": 1135300}, "additional": {"logType": "info", "children": [], "durationId": "c2742055-d0fb-46f4-b4f3-68124e9d8509"}}, {"head": {"id": "de47e8d2-9c40-43a5-8321-e7cfcd76cf1e", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844211300, "endTime": 313177845025400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ace19b7a-3bc2-4e6b-a4b4-900c00a251c3", "logId": "e8f4fab5-23b1-4db7-ae61-98570b678acd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ace19b7a-3bc2-4e6b-a4b4-900c00a251c3", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844061000}, "additional": {"logType": "detail", "children": [], "durationId": "de47e8d2-9c40-43a5-8321-e7cfcd76cf1e"}}, {"head": {"id": "7468168e-08ba-42ec-9534-a981b2e386d7", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844234100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84c49db9-3188-40b8-af8a-8ee2672b0318", "name": "entry : buildPreviewerResource cost memory 0.01215362548828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3a734d-ad10-4ef3-ad94-5fddb283bc2f", "name": "runTaskFromQueue task cost before running: 15 s 133 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844888100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f4fab5-23b1-4db7-ae61-98570b678acd", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177844211300, "endTime": 313177845025400, "totalTime": 568400}, "additional": {"logType": "info", "children": [], "durationId": "de47e8d2-9c40-43a5-8321-e7cfcd76cf1e"}}, {"head": {"id": "5d961477-6d16-4975-8c79-c25407fa5240", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177851557500, "endTime": 313177856255000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "de5d8c32-e4bc-480b-a314-62cc35a9a2de", "logId": "2342af66-3563-49b9-951d-e0752fdbbe3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de5d8c32-e4bc-480b-a314-62cc35a9a2de", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177848703600}, "additional": {"logType": "detail", "children": [], "durationId": "5d961477-6d16-4975-8c79-c25407fa5240"}}, {"head": {"id": "b61be97d-3c5e-4d0f-ac94-952efc40e788", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177849736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c727258-620b-4e6f-8ea7-bccb7110270a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177849948200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69633779-56b8-4e88-8a9a-d9b85021a6c2", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177851587400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140216f1-4fa5-44e6-90d4-884c6768f853", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177855941800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693d2ef8-3123-4bea-8d67-9570364553ea", "name": "entry : default@PreviewUpdateAssets cost memory 0.1095733642578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177856149400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2342af66-3563-49b9-951d-e0752fdbbe3e", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177851557500, "endTime": 313177856255000}, "additional": {"logType": "info", "children": [], "durationId": "5d961477-6d16-4975-8c79-c25407fa5240"}}, {"head": {"id": "9aad71ea-e9c3-40cb-b583-03d4362dbeb2", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177870308800, "endTime": 313248434040000}, "additional": {"children": ["6f78cc58-4ec0-41e1-b67c-35356914e042"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "6d4dbc27-21de-44d8-ac8c-2fba7ad9efbd", "logId": "8744f6a2-e19c-4bff-903e-b8332710337b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d4dbc27-21de-44d8-ac8c-2fba7ad9efbd", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177860987500}, "additional": {"logType": "detail", "children": [], "durationId": "9aad71ea-e9c3-40cb-b583-03d4362dbeb2"}}, {"head": {"id": "f7b6768a-73d1-4920-b710-def85fa65c26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177861678500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef20d1a8-eea2-42b1-831a-a702265e8412", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177861878600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e7035b-084b-48bd-ab46-7e213ed7e501", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177870326200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9acf8d5-cfb4-4d2a-80e8-94d885d74b58", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177912539900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a3edc93-6c38-4bc1-8d05-fd6688102196", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177913030100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f78cc58-4ec0-41e1-b67c-35356914e042", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313178572292900, "endTime": 313248404113600}, "additional": {"children": ["ac9df765-71ca-41f5-aa86-74369e52fb26", "948bbb5c-7b2f-4eaa-bfc3-8e42489e0770", "cf25f6ed-66cb-4ebf-9d61-21ebb5bbc010", "07b1c3c6-e458-4fa2-9673-ce8dbacb8419", "874cdf1b-cae4-40d6-a0e9-496656c5813e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9aad71ea-e9c3-40cb-b583-03d4362dbeb2", "logId": "65ebc35f-b021-4346-9bd5-abc682960892"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "845c88f1-f37d-4989-8e0a-8d65e3a01046", "name": "entry : default@PreviewArkTS cost memory 0.03536224365234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313178607848800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5e1028-e0bd-43e1-ae37-f80ef1bea648", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313225125309100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac9df765-71ca-41f5-aa86-74369e52fb26", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313225131783500, "endTime": 313225132100600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "7c00a75f-f75a-4a35-9988-6263cf32121e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c00a75f-f75a-4a35-9988-6263cf32121e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313225131783500, "endTime": 313225132100600}, "additional": {"logType": "info", "children": [], "durationId": "ac9df765-71ca-41f5-aa86-74369e52fb26", "parent": "65ebc35f-b021-4346-9bd5-abc682960892"}}, {"head": {"id": "87c6c057-a140-4c21-8910-35f2909d1d13", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248150161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948bbb5c-7b2f-4eaa-bfc3-8e42489e0770", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313248176744000, "endTime": 313248176913200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "1a5f237e-6f4a-440f-899c-6b22781fb6a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a5f237e-6f4a-440f-899c-6b22781fb6a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248176744000, "endTime": 313248176913200}, "additional": {"logType": "info", "children": [], "durationId": "948bbb5c-7b2f-4eaa-bfc3-8e42489e0770", "parent": "65ebc35f-b021-4346-9bd5-abc682960892"}}, {"head": {"id": "65ebc35f-b021-4346-9bd5-abc682960892", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313178572292900, "endTime": 313248404113600}, "additional": {"logType": "info", "children": ["7c00a75f-f75a-4a35-9988-6263cf32121e", "1a5f237e-6f4a-440f-899c-6b22781fb6a0", "6e410dba-7c19-4fc9-8b31-94ededd1f8f8", "82ae7daf-6317-477b-9959-4edff19e5528", "f99208c3-afc1-4a4b-bba3-a8791f8729db"], "durationId": "6f78cc58-4ec0-41e1-b67c-35356914e042", "parent": "8744f6a2-e19c-4bff-903e-b8332710337b"}}, {"head": {"id": "cf25f6ed-66cb-4ebf-9d61-21ebb5bbc010", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313212650858200, "endTime": 313224932647600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "6e410dba-7c19-4fc9-8b31-94ededd1f8f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e410dba-7c19-4fc9-8b31-94ededd1f8f8", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313212650858200, "endTime": 313224932647600}, "additional": {"logType": "info", "children": [], "durationId": "cf25f6ed-66cb-4ebf-9d61-21ebb5bbc010", "parent": "65ebc35f-b021-4346-9bd5-abc682960892"}}, {"head": {"id": "07b1c3c6-e458-4fa2-9673-ce8dbacb8419", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313224933227600, "endTime": 313224933608900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "82ae7daf-6317-477b-9959-4edff19e5528"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82ae7daf-6317-477b-9959-4edff19e5528", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313224933227600, "endTime": 313224933608900}, "additional": {"logType": "info", "children": [], "durationId": "07b1c3c6-e458-4fa2-9673-ce8dbacb8419", "parent": "65ebc35f-b021-4346-9bd5-abc682960892"}}, {"head": {"id": "874cdf1b-cae4-40d6-a0e9-496656c5813e", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker1", "startTime": 313224933944800, "endTime": 313248150296900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "f99208c3-afc1-4a4b-bba3-a8791f8729db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f99208c3-afc1-4a4b-bba3-a8791f8729db", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313224933944800, "endTime": 313248150296900}, "additional": {"logType": "info", "children": [], "durationId": "874cdf1b-cae4-40d6-a0e9-496656c5813e", "parent": "65ebc35f-b021-4346-9bd5-abc682960892"}}, {"head": {"id": "8744f6a2-e19c-4bff-903e-b8332710337b", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313177870308800, "endTime": 313248434040000, "totalTime": 70563456000}, "additional": {"logType": "info", "children": ["65ebc35f-b021-4346-9bd5-abc682960892"], "durationId": "9aad71ea-e9c3-40cb-b583-03d4362dbeb2"}}, {"head": {"id": "63a24ce7-1bc8-4c4b-91e0-2d498cdd64fc", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248463985400, "endTime": 313248464982300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9bffbc72-fc3f-453e-b7dc-2bbe193fcf68", "logId": "4eb1b231-383d-46d3-9b59-c0336d664726"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bffbc72-fc3f-453e-b7dc-2bbe193fcf68", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248463814100}, "additional": {"logType": "detail", "children": [], "durationId": "63a24ce7-1bc8-4c4b-91e0-2d498cdd64fc"}}, {"head": {"id": "b7b8d497-7d98-438f-9bf0-c4f88791bf4b", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248464017500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992269e8-2d79-4a52-838a-84a65721e5f3", "name": "entry : PreviewBuild cost memory 0.011810302734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248464418800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd8a4d9-e2e6-4192-beb5-03211adee3e9", "name": "runTaskFromQueue task cost before running: 1 min 25 s 753 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248464747900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eb1b231-383d-46d3-9b59-c0336d664726", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248463985400, "endTime": 313248464982300, "totalTime": 671800}, "additional": {"logType": "info", "children": [], "durationId": "63a24ce7-1bc8-4c4b-91e0-2d498cdd64fc"}}, {"head": {"id": "b94b096e-08aa-48ae-90d0-b010c596cae4", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248586633300, "endTime": 313248586655800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "381d2617-8ec5-4cbb-8855-cedfce045c40", "logId": "7a8fa699-d120-46e6-b1e2-3bec861c5e33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a8fa699-d120-46e6-b1e2-3bec861c5e33", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248586633300, "endTime": 313248586655800}, "additional": {"logType": "info", "children": [], "durationId": "b94b096e-08aa-48ae-90d0-b010c596cae4"}}, {"head": {"id": "aea14ba8-21ac-4cf5-9c12-2808232990c5", "name": "BUILD SUCCESSFUL in 1 min 25 s 875 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248586753700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "307ad0dd-a7a7-47a7-8e4b-2446c54d897d", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313162711889800, "endTime": 313248587301400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 48}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d4a2073d-7010-4d8b-9594-2ef3dc52e77c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8588a59-d1d0-4814-bfc0-ce6ed17b7151", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587599700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea65e2e-785a-4094-b76a-f6215d8dd402", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587671900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "136379b5-da14-4dca-8fb8-0125e727bfcf", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fcc1cf0-e237-47f9-a830-69d6f6d65071", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587798400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d066625-b3fe-42d9-a2b0-af63f39ae873", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587856800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b99476c-9ccb-404d-8442-b9a024ea5370", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e022404e-2fea-43fd-be8b-3068bf379c0b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248587973500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3b4ee5-8b80-41ef-9f50-085b461bf0c4", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248588037700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a9a347-2ea8-4785-af65-ec3661a77a32", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248588111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d29a4f59-d734-43fd-8993-c529ddd30bbb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248638217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ca611df-476d-4064-9904-40a93336b1cc", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248642066600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2685cb3d-4cda-422e-8cd9-ffc2f68cd138", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248643260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a92a7675-f360-495b-8395-e101108fb6c7", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248645748100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed41bab3-1f2d-44fd-a79b-30bd8382dcfb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248648885200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dfdf241-a376-4c7e-a590-9161e53e0161", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248718674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a3f4ae-f745-4288-8a44-19131323db58", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248719307000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa67185a-697a-478a-a469-2622b9e7181f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248719911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5e18d1-e2e3-4100-bd88-9b5b3fae2a13", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248720602500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5354721c-3b45-4fde-9c6e-88f827a4917f", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:134 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248721393400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}