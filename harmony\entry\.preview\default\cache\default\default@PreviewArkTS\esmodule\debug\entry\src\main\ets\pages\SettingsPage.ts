if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    userInfo?: LocalUserInfo | null;
    showLogoutDialog?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__showLogoutDialog = new ObservedPropertySimplePU(false, this, "showLogoutDialog");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.showLogoutDialog !== undefined) {
            this.showLogoutDialog = params.showLogoutDialog;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__showLogoutDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__showLogoutDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __showLogoutDialog: ObservedPropertySimplePU<boolean>;
    get showLogoutDialog() {
        return this.__showLogoutDialog.get();
    }
    set showLogoutDialog(newValue: boolean) {
        this.__showLogoutDialog.set(newValue);
    }
    aboutToAppear() {
        this.loadUserInfo();
    }
    onPageShow() {
        console.log('设置页面显示，重新加载用户信息');
        this.loadUserInfo();
    }
    async loadUserInfo(): Promise<void> {
        try {
            const cachedUserInfo = await storageManager.getUserInfo();
            if (cachedUserInfo) {
                this.userInfo = cachedUserInfo;
            }
            else {
                this.userInfo = {
                    userId: 1,
                    phone: '<EMAIL>',
                    realName: '未设置',
                    idCard: '',
                    walletNo: '',
                    balance: 0,
                    payLimit: 1000,
                    status: 1,
                    createTime: '',
                    updateTime: ''
                };
            }
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
            this.userInfo = {
                userId: 1,
                phone: '<EMAIL>',
                realName: '未设置',
                idCard: '',
                walletNo: '',
                balance: 0,
                payLimit: 1000,
                status: 1,
                createTime: '',
                updateTime: ''
            };
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/SettingsPage.ets(60:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(61:7)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(63:9)", "entry");
            // 顶部导航栏
            Column.width('90%');
            // 顶部导航栏
            Column.margin({ top: 20 });
            // 顶部导航栏
            Column.borderRadius(16);
            // 顶部导航栏
            Column.backgroundColor('#ff3785f5');
            // 顶部导航栏
            Column.shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(64:11)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding(16);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('个人设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(65:13)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
            Text.width('100%');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部导航栏
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/SettingsPage.ets(82:9)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor(Color.Transparent);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(83:11)", "entry");
            Column.padding({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 个人信息卡片
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(85:13)", "entry");
            // 个人信息卡片
            Column.width('90%');
            // 个人信息卡片
            Column.padding(24);
            // 个人信息卡片
            Column.margin({ top: 16, bottom: 24 });
            // 个人信息卡片
            Column.borderRadius(16);
            // 个人信息卡片
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 头像和信息行
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(87:15)", "entry");
            // 头像和信息行
            Row.width('100%');
            // 头像和信息行
            Row.alignItems(VerticalAlign.Center);
            // 头像和信息行
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 头像
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(89:17)", "entry");
            // 头像
            Column.width(80);
            // 头像
            Column.height(80);
            // 头像
            Column.borderRadius(40);
            // 头像
            Column.backgroundColor('rgba(255, 255, 255, 0.2)');
            // 头像
            Column.justifyContent(FlexAlign.Center);
            // 头像
            Column.alignItems(HorizontalAlign.Center);
            // 头像
            Column.margin({ right: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo?.realName?.charAt(0) || 'L');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(90:19)", "entry");
            Text.fontSize(40);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 头像
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(104:17)", "entry");
            // 用户信息
            Column.layoutWeight(1);
            // 用户信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo?.realName || '未设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(105:19)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo?.phone || '<EMAIL>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(112:19)", "entry");
            Text.fontSize(14);
            Text.fontColor('rgba(255, 255, 255, 0.8)');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 用户信息
        Column.pop();
        // 头像和信息行
        Row.pop();
        // 个人信息卡片
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 基本设置卡片
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(134:13)", "entry");
            // 基本设置卡片
            Column.width('100%');
            // 基本设置卡片
            Column.padding(20);
            // 基本设置卡片
            Column.margin({ left: 16, right: 16 });
            // 基本设置卡片
            Column.borderRadius(16);
            // 基本设置卡片
            Column.backgroundColor('#FFFFFF');
            // 基本设置卡片
            Column.shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.06)', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(135:15)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚙️');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(136:17)", "entry");
            Text.fontSize(18);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('基本设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(139:17)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
        // 修改基本资料
        this.SettingCardItem.bind(this)('📝', '#667eea', '修改基本资料', '修改用户名、邮箱、手机号', () => {
            router.pushUrl({ url: 'pages/EditProfilePage' });
        });
        // 修改登录密码
        this.SettingCardItem.bind(this)('🔑', '#52c41a', '修改登录密码', '修改账户登录密码', () => {
            router.pushUrl({ url: 'pages/ChangePasswordPage' });
        });
        // 修改支付密码
        this.SettingCardItem.bind(this)('🔒', '#fa8c16', '修改支付密码', '修改支付交易密码', () => {
            router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
        });
        // 退出登录
        this.SettingCardItem.bind(this)('🚪', '#f5222d', '退出登录', '退出当前账户', () => {
            this.showLogoutDialog = true;
        });
        // 基本设置卡片
        Column.pop();
        Column.pop();
        Scroll.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 退出登录确认弹窗
            if (this.showLogoutDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(212:9)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.position({ x: 0, y: 0 });
                        Column.zIndex(1000);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(213:11)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.backgroundColor('#80000000');
                        Column.onClick(() => {
                            this.showLogoutDialog = false;
                        });
                    }, Column);
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(221:11)", "entry");
                        Column.width('280vp');
                        Column.padding(24);
                        Column.borderRadius(16);
                        Column.backgroundColor('#FFFFFF');
                        Column.shadow({
                            radius: 16,
                            color: '#40000000',
                            offsetX: 0,
                            offsetY: 8
                        });
                        Column.position({ x: '50%', y: '50%' });
                        Column.translate({ x: '-50%', y: '-50%' });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('确认退出');
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(222:13)", "entry");
                        Text.fontSize(18);
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor('#333333');
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('确定要退出登录吗？');
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(228:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 24 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(234:13)", "entry");
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('取消');
                        Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(235:15)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#666666');
                        Button.backgroundColor('#F8F9FA');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.height(44);
                        Button.onClick(() => {
                            this.showLogoutDialog = false;
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('确认退出');
                        Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(246:15)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#F44336');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.height(44);
                        Button.margin({ left: 12 });
                        Button.onClick(() => {
                            this.showLogoutDialog = false;
                            this.handleLogout();
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    SettingCardItem(icon: string, color: string, title: string, subtitle: string, onTap: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(286:5)", "entry");
            Row.width('100%');
            Row.height(70);
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.margin({ bottom: 8 });
            Row.borderRadius(12);
            Row.backgroundColor('#FFFFFF');
            Row.shadow({ radius: 4, color: 'rgba(0, 0, 0, 0.08)', offsetX: 0, offsetY: 2 });
            Row.onClick(onTap);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(287:7)", "entry");
            Column.width(44);
            Column.height(44);
            Column.borderRadius(12);
            Column.backgroundColor(color);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.margin({ right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(icon);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(288:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(300:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(301:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(subtitle);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(308:9)", "entry");
            Text.fontSize(13);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(316:7)", "entry");
            Text.fontSize(18);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
    }
    async handleLogout(): Promise<void> {
        try {
            await storageManager.clearUserData();
            httpClient.clearAuthToken();
            promptAction.showToast({ message: '已退出登录' });
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
        catch (error) {
            console.error('退出登录失败:', error);
            promptAction.showToast({ message: '退出登录失败' });
        }
    }
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(344:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({ width: { top: 1 }, color: '#E5E5E5' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(345:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/MyBankCardPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(346:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(347:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(352:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/TransactionListPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(353:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(354:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(359:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/WalletPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(360:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(361:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(366:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
            Column.onClick(() => router.pushUrl({ url: 'pages/PaymentCenterPage' }));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(367:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(368:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(373:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Center);
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(374:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(375:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#6366F1');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
