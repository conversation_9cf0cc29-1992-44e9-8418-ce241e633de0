import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login,
    },
    {
      path: '/home',
      name: 'home',
      component: Home,
      children: [
        {
          path: '',
          redirect: '/home/<USER>'
        },
        {
          path: 'cards',
          name: 'cards',
          component: () => import('../views/CardManagement.vue')
        },
        {
          path: 'transactions',
          name: 'transactions',
          component: () => import('../views/TransactionMain.vue')
        },
        {
          path: 'wallet',
          name: 'wallet',
          component: () => import('../views/Wallet.vue')
        },
        {
          path: 'payment',
          name: 'payment',
          component: () => import('../views/Payment.vue')
        },
        {
          path: 'settings',
          name: 'settings',
          component: () => import('../views/SimpleSettings.vue')
        },
        {
          path: 'loan',
          name: 'loan',
          component: () => import('../views/loan.vue')
        }
      ]
    },
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/login'
    }
  ]
})

// 路由守卫示例：检查登录状态
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('token')
  if (to.name !== 'login' && !isAuthenticated) {
    next({ name: 'login' })
  } else {
    next()
  }
})

export default router
