-- 🔧 充值提现功能修复脚本
-- 解决数据库字段问题和功能测试

USE e_wallet;

-- 1. 确保transaction表有所有必需的字段
-- 检查并添加status字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'transaction'
     AND table_schema = 'e_wallet'
     AND column_name = 'status') > 0,
    'SELECT "status字段已存在" as result',
    'ALTER TABLE transaction ADD COLUMN status VARCHAR(20) DEFAULT "SUCCESS" COMMENT "交易状态：SUCCESS成功/PENDING处理中/FAILED失败"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加description字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'transaction'
     AND table_schema = 'e_wallet'
     AND column_name = 'description') > 0,
    'SELECT "description字段已存在" as result',
    'ALTER TABLE transaction ADD COLUMN description VARCHAR(200) COMMENT "交易描述"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 更新现有记录的status字段
UPDATE transaction 
SET status = 'SUCCESS' 
WHERE status IS NULL OR status = '';

-- 3. 清理并重置测试数据
-- 删除现有交易记录
DELETE FROM transaction WHERE id > 0;

-- 重置用户钱包余额
UPDATE user SET account = 1000.00 WHERE id = 1;
UPDATE user SET account = 500.00 WHERE id = 2;
UPDATE user SET account = 300.00 WHERE id = 3;

-- 重置银行卡余额
UPDATE bank_card SET balance = 5000.00 WHERE user_id = 1 AND card_number = '****************';
UPDATE bank_card SET balance = 3000.00 WHERE user_id = 2 AND card_number = '****************';
UPDATE bank_card SET balance = 2000.00 WHERE user_id = 3;

-- 4. 插入测试交易记录（包含所有字段）
INSERT INTO transaction (
    user_id, 
    transaction_type, 
    amount, 
    transaction_time,
    payment_method, 
    payment_channel, 
    target_account, 
    card_id, 
    status,
    description
) VALUES
-- 用户1的测试数据
(1, 'DEPOSIT', 500.00, NOW(), 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
(1, 'WITHDRAW', 200.00, NOW(), 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 1, 'SUCCESS', '提现到中国银行'),
(1, 'PAYMENT', 150.00, NOW(), 'WALLET', 'MERCHANT', '星巴克', NULL, 'SUCCESS', '商户支付'),

-- 用户2的测试数据  
(2, 'DEPOSIT', 300.00, NOW(), 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
(2, 'TRANSFER', 100.00, NOW(), 'WALLET', 'MERCHANT', '用户1', NULL, 'SUCCESS', '转账给朋友');

-- 5. 验证数据完整性
SELECT '=== 验证transaction表结构 ===' as info;
DESCRIBE transaction;

SELECT '=== 当前用户余额 ===' as info;
SELECT id, username, real_name, account as wallet_balance FROM user ORDER BY id;

SELECT '=== 当前银行卡余额 ===' as info;
SELECT id, user_id, card_number, bank_name, balance FROM bank_card WHERE bind_status = 1 ORDER BY user_id;

SELECT '=== 交易记录 ===' as info;
SELECT 
    id,
    user_id,
    transaction_type,
    amount,
    payment_method,
    target_account,
    status,
    DATE_FORMAT(transaction_time, '%Y-%m-%d %H:%i:%s') as time
FROM transaction 
ORDER BY transaction_time DESC;

-- 6. 测试充值提现的SQL语句
SELECT '=== 测试SQL语句 ===' as info;

-- 模拟充值：从银行卡到钱包
SELECT '充值前状态:' as step;
SELECT u.id, u.username, u.account as wallet_balance, bc.balance as card_balance 
FROM user u 
JOIN bank_card bc ON u.id = bc.user_id 
WHERE u.id = 1 AND bc.id = 1;

-- 模拟提现：从钱包到银行卡  
SELECT '提现前状态:' as step;
SELECT u.id, u.username, u.account as wallet_balance, bc.balance as card_balance 
FROM user u 
JOIN bank_card bc ON u.id = bc.user_id 
WHERE u.id = 1 AND bc.id = 1;
