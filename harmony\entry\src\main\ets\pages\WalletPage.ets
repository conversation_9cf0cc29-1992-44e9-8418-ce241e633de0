import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct WalletPage {
  @State balance: number = 1200.00;

  aboutToAppear() {
    console.log('WalletPage 页面加载');
  }

  // 快速操作点击处理
  private handleQuickAction(action: string) {
    promptAction.showToast({
      message: `点击了${action}`,
      duration: 2000
    });

    // 根据不同操作跳转到对应页面
    switch (action) {
      case '转账':
        router.pushUrl({
          url: 'pages/TransferPage'
        });
        break;
      case '充值':
        router.pushUrl({
          url: 'pages/RechargePage'
        });
        break;
      case '提现':
        router.pushUrl({
          url: 'pages/WithdrawPage'
        });
        break;
      case '交易记录':
        router.pushUrl({
          url: 'pages/TransactionListPage'
        });
        break;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Column() {
        Row() {
          Text('钱包管理')
            .fontSize(18)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      }
      .width('90%')
      .margin({ top: 20 })
      .borderRadius(16)
      .backgroundColor('#ff3785f5')
      .shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 })
      // 余额卡片
      Column() {
        Row() {
          Text('钱包余额')
            .fontSize(16)
            .fontColor('#FFFFFF')
            .width('100%')
            .textAlign(TextAlign.Center)
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 20 })

        Text(`¥ ${this.balance.toFixed(2)}`)
          .fontSize(32)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 8 })

        Text('可用余额')
          .fontSize(14)
          .fontColor('rgba(255, 255, 255, 0.8)')
      }
      .width('90%')
      .padding(24)
      .margin({ top: 20, bottom: 30 })
      .borderRadius(16)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
      })

      // 快速操作标题
      Row() {
        Text('⚡')
          .fontSize(16)
          .margin({ right: 8 })
        
        Text('快速操作')
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
      }
      .width('90%')
      .justifyContent(FlexAlign.Start)
      .margin({ bottom: 20 })

      // 快速操作列表
      Column() {
        // 转账
        Row() {
          Row() {
            Text('💸')
              .fontSize(24)
              .margin({ right: 12 })
            
            Column() {
              Text('转账')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
              
              Text('向其他账户转账')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 2 })
                .alignSelf(ItemAlign.Start)
            }
            .alignItems(HorizontalAlign.Start)
          }
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .onClick(() => {
          this.handleQuickAction('转账');
        })

        // 充值
        Row() {
          Row() {
            Text('💰')
              .fontSize(24)
              .margin({ right: 12 })
            
            Column() {
              Text('充值')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
              
              Text('从银行卡充值到钱包')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 2 })
                .alignSelf(ItemAlign.Start)
            }
            .alignItems(HorizontalAlign.Start)
          }
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .onClick(() => {
          this.handleQuickAction('充值');
        })

        // 提现
        Row() {
          Row() {
            Text('💳')
              .fontSize(24)
              .margin({ right: 12 })
            
            Column() {
              Text('提现')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
              
              Text('从钱包提现到银行卡')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 2 })
                .alignSelf(ItemAlign.Start)
            }
            .alignItems(HorizontalAlign.Start)
          }
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .onClick(() => {
          this.handleQuickAction('提现');
        })

        // 交易记录
        Row() {
          Row() {
            Text('📊')
              .fontSize(24)
              .margin({ right: 12 })
            
            Column() {
              Text('交易记录')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
              
              Text('查看交易明细')
                .fontSize(12)
                .fontColor('#666666')
                .margin({ top: 2 })
                .alignSelf(ItemAlign.Start)
            }
            .alignItems(HorizontalAlign.Start)
          }
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .onClick(() => {
          this.handleQuickAction('交易记录');
        })
      }
      .width('90%')
      .layoutWeight(1)

      // 底部导航栏
      this.BottomNavigation()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }

  @Builder
  BottomNavigation() {
    Row() {
      // 银行卡
      Column() {
        Text('💳')
          .fontSize(20)
        Text('银行卡')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/MyBankCardPage'
        });
      })

      // 交易
      Column() {
        Text('📊')
          .fontSize(20)
        Text('交易')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/TransactionListPage'
        });
      })

      // 钱包 (当前页面)
      Column() {
        Text('👛')
          .fontSize(20)
        Text('钱包')
          .fontSize(12)
          .fontColor('#6366F1')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })

      // 支付中心
      Column() {
        Text('💰')
          .fontSize(20)
        Text('支付中心')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/PaymentCenterPage'
        });
      })

      // 我的
      Column() {
        Text('👤')
          .fontSize(20)
        Text('我的')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/SettingsPage'
        });
      })
    }
    .width('100%')
    .height(60)
    .backgroundColor('#FFFFFF')
    .border({
      width: { top: 1 },
      color: '#E5E5E5'
    })
  }
}
