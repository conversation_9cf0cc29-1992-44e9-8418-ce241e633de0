if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface PaymentCenterPage_Params {
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class PaymentCenterPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: PaymentCenterPage_Params) {
    }
    updateStateVars(params: PaymentCenterPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    aboutToAppear() {
        console.log('PaymentCenterPage 页面加载');
    }
    // 支付方式点击处理（保持不变）
    private handlePaymentMethod(method: string) {
        console.log(`选择支付方式: ${method}`);
        promptAction.showToast({
            message: `正在跳转到${method}...`,
            duration: 1000
        });
        switch (method) {
            case '钱包支付':
                router.pushUrl({
                    url: 'pages/WalletPaymentPage'
                }).then(() => {
                    console.log('跳转到钱包支付页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到钱包支付页面失败:', error.message);
                    promptAction.showToast({
                        message: '跳转失败: ' + error.message,
                        duration: 3000
                    });
                });
                break;
            case '银行卡支付':
                router.pushUrl({
                    url: 'pages/BankCardPaymentPage'
                }).then(() => {
                    console.log('跳转到银行卡支付页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到银行卡支付页面失败:', error.message);
                    promptAction.showToast({
                        message: '跳转失败: ' + error.message,
                        duration: 3000
                    });
                });
                break;
            case '扫码支付':
                router.pushUrl({
                    url: 'pages/QRCodePaymentPage'
                }).then(() => {
                    console.log('跳转到扫码支付页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到扫码支付页面失败:', error.message);
                    promptAction.showToast({
                        message: '跳转失败: ' + error.message,
                        duration: 3000
                    });
                });
                break;
            case 'NFC支付':
                router.pushUrl({
                    url: 'pages/NFCPaymentPage'
                }).then(() => {
                    console.log('跳转到NFC支付页面成功');
                }).catch((error: Error) => {
                    console.error('跳转到NFC支付页面失败:', error.message);
                    promptAction.showToast({
                        message: '跳转失败: ' + error.message,
                        duration: 3000
                    });
                });
                break;
            default:
                promptAction.showToast({
                    message: `选择了${method}`,
                    duration: 2000
                });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(82:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(84:7)", "entry");
            // 顶部标题栏
            Column.width('90%');
            // 顶部标题栏
            Column.margin({ top: 20 });
            // 顶部标题栏
            Column.borderRadius(16);
            // 顶部标题栏
            Column.backgroundColor('#ff3785f5');
            // 顶部标题栏
            Column.shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(85:9)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(86:11)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部标题栏
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(103:7)", "entry");
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.padding({ left: 16, right: 16, bottom: 10 });
            // 内容区域
            Column.alignItems(HorizontalAlign.Start);
            // 内容区域
            Column.justifyContent(FlexAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 选择支付方式标题
            Text.create('选择支付方式');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(105:9)", "entry");
            // 选择支付方式标题
            Text.fontSize(18);
            // 选择支付方式标题
            Text.fontColor('#333333');
            // 选择支付方式标题
            Text.fontWeight(FontWeight.Medium);
            // 选择支付方式标题
            Text.margin({ top: 20, bottom: 20 });
            // 选择支付方式标题
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 选择支付方式标题
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包支付
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(113:9)", "entry");
            // 钱包支付
            Row.width('100%');
            // 钱包支付
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 钱包支付
            Row.backgroundColor('#FFFFFF');
            // 钱包支付
            Row.borderRadius(12);
            // 钱包支付
            Row.margin({ bottom: 12 });
            // 钱包支付
            Row.alignItems(VerticalAlign.Center);
            // 钱包支付
            Row.onClick(() => {
                this.handlePaymentMethod('钱包支付');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(114:11)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(118:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(119:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('使用钱包余额支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(126:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(134:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 钱包支付
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡支付
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(149:9)", "entry");
            // 银行卡支付
            Row.width('100%');
            // 银行卡支付
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 银行卡支付
            Row.backgroundColor('#FFFFFF');
            // 银行卡支付
            Row.borderRadius(12);
            // 银行卡支付
            Row.margin({ bottom: 12 });
            // 银行卡支付
            Row.alignItems(VerticalAlign.Center);
            // 银行卡支付
            Row.onClick(() => {
                this.handlePaymentMethod('银行卡支付');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🏦');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(150:11)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(154:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(155:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('使用银行卡支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(162:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(170:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 银行卡支付
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 扫码支付
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(185:9)", "entry");
            // 扫码支付
            Row.width('100%');
            // 扫码支付
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 扫码支付
            Row.backgroundColor('#FFFFFF');
            // 扫码支付
            Row.borderRadius(12);
            // 扫码支付
            Row.margin({ bottom: 12 });
            // 扫码支付
            Row.alignItems(VerticalAlign.Center);
            // 扫码支付
            Row.onClick(() => {
                this.handlePaymentMethod('扫码支付');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📱');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(186:11)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(190:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('扫码支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(191:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('扫描二维码支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(198:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(206:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 扫码支付
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // NFC支付
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(221:9)", "entry");
            // NFC支付
            Row.width('100%');
            // NFC支付
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // NFC支付
            Row.backgroundColor('#FFFFFF');
            // NFC支付
            Row.borderRadius(12);
            // NFC支付
            Row.margin({ bottom: 12 });
            // NFC支付
            Row.alignItems(VerticalAlign.Center);
            // NFC支付
            Row.onClick(() => {
                this.handlePaymentMethod('NFC支付');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📡');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(222:11)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(226:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('NFC支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(227:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('近场通信支付');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(234:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(242:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // NFC支付
        Row.pop();
        // 内容区域
        Column.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        Column.pop();
    }
    // 底部导航栏
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(272:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({
                width: { top: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(274:7)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.alignItems(HorizontalAlign.Center);
            // 银行卡
            Column.padding({ top: 8, bottom: 8 });
            // 银行卡
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/MyBankCardPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(275:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(277:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(292:7)", "entry");
            // 交易
            Column.layoutWeight(1);
            // 交易
            Column.alignItems(HorizontalAlign.Center);
            // 交易
            Column.padding({ top: 8, bottom: 8 });
            // 交易
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionListPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(293:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(295:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(310:7)", "entry");
            // 钱包
            Column.layoutWeight(1);
            // 钱包
            Column.alignItems(HorizontalAlign.Center);
            // 钱包
            Column.padding({ top: 8, bottom: 8 });
            // 钱包
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/WalletPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(311:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(313:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 钱包
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付中心 (当前页面)
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(328:7)", "entry");
            // 支付中心 (当前页面)
            Column.layoutWeight(1);
            // 支付中心 (当前页面)
            Column.alignItems(HorizontalAlign.Center);
            // 支付中心 (当前页面)
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(329:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(331:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#6366F1');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 支付中心 (当前页面)
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(341:7)", "entry");
            // 我的
            Column.layoutWeight(1);
            // 我的
            Column.alignItems(HorizontalAlign.Center);
            // 我的
            Column.padding({ top: 8, bottom: 8 });
            // 我的
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(342:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/PaymentCenterPage.ets(344:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 我的
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "PaymentCenterPage";
    }
}
registerNamedRoute(() => new PaymentCenterPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/PaymentCenterPage", pageFullPath: "entry/src/main/ets/pages/PaymentCenterPage", integratedHsp: "false", moduleType: "followWithHap" });
