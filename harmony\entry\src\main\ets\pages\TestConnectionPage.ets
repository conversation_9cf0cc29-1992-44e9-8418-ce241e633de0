import { httpClient } from '../common/http/HttpClient';
import promptAction from '@ohos.promptAction';
import { UserInfo, UserLoginFormData } from '../common/types/index';

@Entry
@Component
struct TestConnectionPage {
  @State testResult: string = '等待测试...';
  @State isLoading: boolean = false;

  build() {
    Column({ space: 20 }) {
      Text('Spring Boot 连接测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 })

      Text(this.testResult)
        .fontSize(16)
        .fontColor('#666666')
        .textAlign(TextAlign.Center)
        .width('90%')

      Button('测试连接 (10.0.2.2:8080)')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testConnection();
        })

      Button('测试本地连接 (127.0.0.1:8080)')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testLocalConnection();
        })

      Button('测试银行卡API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testBankAPI();
        })

      Button('测试HarmonyOS专用API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testHarmonyAPI();
        })

      Button('测试登录API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testLoginAPI();
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .backgroundColor('#F5F5F5')
  }

  async testConnection() {
    this.isLoading = true;
    this.testResult = '正在测试连接...\n目标地址: http://10.0.2.2:8080';

    try {
      // 测试HarmonyOS专用API
      console.log('测试HarmonyOS API连接到: http://10.0.2.2:8080/test');
      this.testResult += '\n正在发送请求...';

      const response = await httpClient.get<string>('/test');
      console.log('HarmonyOS API响应:', response);
      this.testResult = `HarmonyOS API连接成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: 'HarmonyOS API连接测试成功' });
    } catch (error) {
      console.error('连接测试失败:', error);

      // 详细的错误分析
      let errorMsg = '连接失败\n';
      if (error.code === 2300028) {
        errorMsg += '错误类型: 连接超时\n';
        errorMsg += '可能原因:\n';
        errorMsg += '1. Spring Boot后端未启动\n';
        errorMsg += '2. 模拟器网络配置问题\n';
        errorMsg += '3. 防火墙阻止连接\n';
        errorMsg += '4. IP地址配置错误\n';
      }

      this.testResult = errorMsg + `详细信息: ${JSON.stringify(error, null, 2)}`;
      promptAction.showToast({ message: '连接测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  // 测试本地连接
  async testLocalConnection() {
    this.isLoading = true;
    this.testResult = '正在测试本地连接...\n目标地址: http://127.0.0.1:8080';

    try {
      // 创建临时HTTP客户端测试本地连接
      const httpRequest = http.createHttp();

      const options: http.HttpRequestOptions = {
        method: http.RequestMethod.GET,
        header: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        expectDataType: http.HttpDataType.STRING,
        usingCache: false,
        connectTimeout: 10000,
        readTimeout: 10000
      };

      console.log('测试本地连接到: http://127.0.0.1:8080/test');

      httpRequest.request('http://127.0.0.1:8080/test', options, (err, data) => {
        if (err) {
          console.error('本地连接失败:', err);
          this.testResult = `本地连接失败\n错误代码: ${err.code}\n错误信息: ${err.message}`;
        } else {
          console.log('本地连接成功:', data);
          this.testResult = `本地连接成功！\n响应: ${JSON.stringify(data, null, 2)}`;
        }
        this.isLoading = false;
      });
    } catch (error) {
      console.error('本地连接测试异常:', error);
      this.testResult = `本地连接测试异常: ${JSON.stringify(error, null, 2)}`;
      this.isLoading = false;
    }
  }

  async testBankAPI() {
    this.isLoading = true;
    this.testResult = '正在测试银行卡API...';

    try {
      // 测试银行卡API
      const response = await httpClient.get<string>('/bank/cards/1');
      this.testResult = `银行卡API测试成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: '银行卡API测试成功' });
    } catch (error) {
      console.error('银行卡API测试失败:', error);
      this.testResult = `银行卡API测试失败: ${error.message || '未知错误'}`;
      promptAction.showToast({ message: '银行卡API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  async testHarmonyAPI() {
    this.isLoading = true;
    this.testResult = '正在测试HarmonyOS专用API...';

    try {
      // 测试HarmonyOS专用API
      const response = await httpClient.get<string>('/test');
      this.testResult = `HarmonyOS API测试成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: 'HarmonyOS API测试成功' });
    } catch (error) {
      console.error('HarmonyOS API测试失败:', error);
      this.testResult = `HarmonyOS API测试失败: ${error.message || '未知错误'}`;
      promptAction.showToast({ message: 'HarmonyOS API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  async testLoginAPI() {
    this.isLoading = true;
    this.testResult = '正在测试登录API...';

    try {
      // 先获取验证码
      console.log('获取验证码...');
      const captchaResponse = await httpClient.get<string>('/api/user/captcha');
      console.log('验证码响应:', captchaResponse);

      // 测试登录
      console.log('测试登录...');
      const loginData: UserLoginFormData = {
        username: 'admin',
        password: 'admin123',
        captcha: captchaResponse.data
      };

      const loginResponse = await httpClient.postForm<UserInfo>('/api/user/login', loginData);
      console.log('登录响应:', loginResponse);

      this.testResult = `登录API测试成功！\n验证码: ${captchaResponse.data}\n登录响应: ${JSON.stringify(loginResponse, null, 2)}`;
      promptAction.showToast({ message: '登录API测试成功' });
    } catch (error) {
      console.error('登录API测试失败:', error);
      this.testResult = `登录API测试失败: ${error.message || '未知错误'}\n详细信息: ${JSON.stringify(error, null, 2)}`;
      promptAction.showToast({ message: '登录API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }
}
