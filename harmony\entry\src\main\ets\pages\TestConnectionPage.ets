import { httpClient } from '../common/http/HttpClient';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct TestConnectionPage {
  @State testResult: string = '等待测试...';
  @State isLoading: boolean = false;

  build() {
    Column({ space: 20 }) {
      Text('Spring Boot 连接测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 })

      Text(this.testResult)
        .fontSize(16)
        .fontColor('#666666')
        .textAlign(TextAlign.Center)
        .width('90%')

      Button('测试连接')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testConnection();
        })

      Button('测试银行卡API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testBankAPI();
        })

      But<PERSON>('测试HarmonyOS专用API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testHarmonyAPI();
        })

      Button('测试登录API')
        .width('80%')
        .height(50)
        .enabled(!this.isLoading)
        .onClick(() => {
          this.testLoginAPI();
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .backgroundColor('#F5F5F5')
  }

  async testConnection() {
    this.isLoading = true;
    this.testResult = '正在测试连接...';

    try {
      // 测试HarmonyOS专用API
      console.log('测试HarmonyOS API连接...');
      const response = await httpClient.get<string>('/test');
      console.log('HarmonyOS API响应:', response);
      this.testResult = `HarmonyOS API连接成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: 'HarmonyOS API连接测试成功' });
    } catch (error) {
      console.error('连接测试失败:', error);
      this.testResult = `连接失败: ${error.message || '未知错误'}\n详细信息: ${JSON.stringify(error, null, 2)}`;
      promptAction.showToast({ message: '连接测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  async testBankAPI() {
    this.isLoading = true;
    this.testResult = '正在测试银行卡API...';

    try {
      // 测试银行卡API
      const response = await httpClient.get<any>('/bank/cards/1');
      this.testResult = `银行卡API测试成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: '银行卡API测试成功' });
    } catch (error) {
      console.error('银行卡API测试失败:', error);
      this.testResult = `银行卡API测试失败: ${error.message || '未知错误'}`;
      promptAction.showToast({ message: '银行卡API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  async testHarmonyAPI() {
    this.isLoading = true;
    this.testResult = '正在测试HarmonyOS专用API...';

    try {
      // 测试HarmonyOS专用API
      const response = await httpClient.get<string>('/test');
      this.testResult = `HarmonyOS API测试成功！\n响应: ${JSON.stringify(response, null, 2)}`;
      promptAction.showToast({ message: 'HarmonyOS API测试成功' });
    } catch (error) {
      console.error('HarmonyOS API测试失败:', error);
      this.testResult = `HarmonyOS API测试失败: ${error.message || '未知错误'}`;
      promptAction.showToast({ message: 'HarmonyOS API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }

  async testLoginAPI() {
    this.isLoading = true;
    this.testResult = '正在测试登录API...';

    try {
      // 先获取验证码
      console.log('获取验证码...');
      const captchaResponse = await httpClient.get<string>('/api/user/captcha');
      console.log('验证码响应:', captchaResponse);

      // 测试登录
      console.log('测试登录...');
      const loginData = {
        username: 'admin',
        password: 'admin123',
        captcha: captchaResponse.data
      };

      const loginResponse = await httpClient.postForm<any>('/api/user/login', loginData);
      console.log('登录响应:', loginResponse);

      this.testResult = `登录API测试成功！\n验证码: ${captchaResponse.data}\n登录响应: ${JSON.stringify(loginResponse, null, 2)}`;
      promptAction.showToast({ message: '登录API测试成功' });
    } catch (error) {
      console.error('登录API测试失败:', error);
      this.testResult = `登录API测试失败: ${error.message || '未知错误'}\n详细信息: ${JSON.stringify(error, null, 2)}`;
      promptAction.showToast({ message: '登录API测试失败' });
    } finally {
      this.isLoading = false;
    }
  }
}
