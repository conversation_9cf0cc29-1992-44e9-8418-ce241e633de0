if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WithdrawPage_Params {
    withdrawAmount?: string;
    selectedBankCard?: string;
    selectedCardId?: number;
    paymentPassword?: string;
    bankCards?: BankCard[];
    isLoading?: boolean;
    showBankCardPicker?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard, BankCardType, WithdrawRequest } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class WithdrawPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__withdrawAmount = new ObservedPropertySimplePU('', this, "withdrawAmount");
        this.__selectedBankCard = new ObservedPropertySimplePU('', this, "selectedBankCard");
        this.__selectedCardId = new ObservedPropertySimplePU(0, this, "selectedCardId");
        this.__paymentPassword = new ObservedPropertySimplePU('', this, "paymentPassword");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showBankCardPicker = new ObservedPropertySimplePU(false, this, "showBankCardPicker");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WithdrawPage_Params) {
        if (params.withdrawAmount !== undefined) {
            this.withdrawAmount = params.withdrawAmount;
        }
        if (params.selectedBankCard !== undefined) {
            this.selectedBankCard = params.selectedBankCard;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.paymentPassword !== undefined) {
            this.paymentPassword = params.paymentPassword;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showBankCardPicker !== undefined) {
            this.showBankCardPicker = params.showBankCardPicker;
        }
    }
    updateStateVars(params: WithdrawPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__withdrawAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedBankCard.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showBankCardPicker.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__withdrawAmount.aboutToBeDeleted();
        this.__selectedBankCard.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__paymentPassword.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showBankCardPicker.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __withdrawAmount: ObservedPropertySimplePU<string>;
    get withdrawAmount() {
        return this.__withdrawAmount.get();
    }
    set withdrawAmount(newValue: string) {
        this.__withdrawAmount.set(newValue);
    }
    private __selectedBankCard: ObservedPropertySimplePU<string>;
    get selectedBankCard() {
        return this.__selectedBankCard.get();
    }
    set selectedBankCard(newValue: string) {
        this.__selectedBankCard.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __paymentPassword: ObservedPropertySimplePU<string>;
    get paymentPassword() {
        return this.__paymentPassword.get();
    }
    set paymentPassword(newValue: string) {
        this.__paymentPassword.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showBankCardPicker: ObservedPropertySimplePU<boolean>;
    get showBankCardPicker() {
        return this.__showBankCardPicker.get();
    }
    set showBankCardPicker(newValue: boolean) {
        this.__showBankCardPicker.set(newValue);
    }
    aboutToAppear() {
        this.loadBankCards();
    }
    async loadBankCards() {
        try {
            const userId = 1; // 临时使用固定用户ID
            console.log('开始加载银行卡...');
            const response = await BankCardApi.getCardList(userId, true); // 只获取已绑定的银行卡
            console.log('API响应:', response);
            this.bankCards = response || [];
            console.log('银行卡数量:', this.bankCards.length);
            // 直接添加测试数据确保有银行卡可选
            this.bankCards = [
                {
                    cardId: 1,
                    userId: 1,
                    cardNo: '6217000010001234567',
                    bankName: '中国银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 2,
                    userId: 1,
                    cardNo: '6228480010001234567',
                    bankName: '工商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 3,
                    userId: 1,
                    cardNo: '6225880010001234567',
                    bankName: '招商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard
            ];
            console.log('设置测试银行卡后数量:', this.bankCards.length);
        }
        catch (error) {
            console.error('加载银行卡失败:', error);
            // 添加测试数据
            this.bankCards = [
                {
                    cardId: 1,
                    userId: 1,
                    cardNo: '6217000010001234567',
                    bankName: '中国银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 2,
                    userId: 1,
                    cardNo: '6228480010001234567',
                    bankName: '工商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 3,
                    userId: 1,
                    cardNo: '6225880010001234567',
                    bankName: '招商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard
            ];
        }
    }
    async handleWithdraw() {
        if (!this.withdrawAmount || !this.selectedCardId || !this.paymentPassword) {
            promptAction.showToast({
                message: '请填写完整信息',
                duration: 2000
            });
            return;
        }
        if (parseFloat(this.withdrawAmount) <= 0) {
            promptAction.showToast({
                message: '提现金额必须大于0',
                duration: 2000
            });
            return;
        }
        try {
            this.isLoading = true;
            // 调用提现API
            const withdrawData: WithdrawRequest = {
                userId: 1,
                cardId: this.selectedCardId,
                amount: parseFloat(this.withdrawAmount)
            };
            // 调用实际的提现API
            await TransactionApi.withdraw(withdrawData);
            promptAction.showToast({
                message: '提现成功',
                duration: 2000
            });
            // 跳转到交易记录页面查看新的交易记录
            router.replaceUrl({
                url: 'pages/TransactionListPage'
            });
        }
        catch (error) {
            console.error('提现失败:', error);
            promptAction.showToast({
                message: '提现失败，请重试',
                duration: 2000
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(161:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
            Column.bindSheet({ value: this.showBankCardPicker, changeEvent: newValue => { this.showBankCardPicker = newValue; } }, { builder: () => {
                    this.BankCardPickerSheet.call(this);
                } }, {
                height: 300,
                showClose: true,
                dragBar: true
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(163:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(164:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('×');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(165:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(177:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(185:9)", "entry");
            // 占位
            Text.width(40);
            // 占位
            Text.height(40);
        }, Text);
        // 占位
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(194:7)", "entry");
            // 表单内容
            Column.layoutWeight(1);
            // 表单内容
            Column.padding({ left: 20, right: 20, top: 20 });
            // 表单内容
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(196:9)", "entry");
            // 提现金额
            Column.width('100%');
            // 提现金额
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(197:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(198:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现金额');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(202:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入提现金额' });
            TextInput.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(209:11)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.fontSize(16);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.withdrawAmount = value;
            });
        }, TextInput);
        // 提现金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 到账银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(223:9)", "entry");
            // 到账银行卡
            Column.width('100%');
            // 到账银行卡
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(224:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(225:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('到账银行卡');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(229:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.selectedBankCard || '请选择银行卡');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(236:11)", "entry");
            Button.width('100%');
            Button.height(50);
            Button.fontSize(14);
            Button.fontColor(this.selectedBankCard ? '#333333' : '#999999');
            Button.backgroundColor('#FFFFFF');
            Button.border({ width: 1, color: '#E0E0E0' });
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showBankCardPicker = true;
            });
        }, Button);
        Button.pop();
        // 到账银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(252:9)", "entry");
            // 支付密码
            Column.width('100%');
            // 支付密码
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(253:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(254:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(258:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(265:11)", "entry");
            TextInput.type(InputType.Password);
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.fontSize(16);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.borderRadius(8);
            TextInput.onChange((value: string) => {
                this.paymentPassword = value;
            });
        }, TextInput);
        // 支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(280:9)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.End);
            // 操作按钮
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(281:11)", "entry");
            Button.width(100);
            Button.height(44);
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(8);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '提现中...' : '确认提现');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(292:11)", "entry");
            Button.width(120);
            Button.height(44);
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleWithdraw();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 表单内容
        Column.pop();
        Column.pop();
    }
    BankCardPickerSheet(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(324:5)", "entry");
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择到账银行卡');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(325:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create();
            List.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(330:7)", "entry");
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const card = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(332:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Row.create();
                            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(333:13)", "entry");
                            Row.width('100%');
                            Row.padding(16);
                            Row.onClick(() => {
                                this.selectedBankCard = `${card.bankName} ****${card.cardNo.slice(-4)}`;
                                this.selectedCardId = card.cardId;
                                this.showBankCardPicker = false;
                            });
                        }, Row);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create('💳');
                            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(334:15)", "entry");
                            Text.fontSize(24);
                            Text.margin({ right: 12 });
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(338:15)", "entry");
                            Column.layoutWeight(1);
                            Column.alignItems(HorizontalAlign.Start);
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(card.bankName);
                            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(339:17)", "entry");
                            Text.fontSize(16);
                            Text.fontColor('#333333');
                            Text.alignSelf(ItemAlign.Start);
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(`${card.cardType} ****${card.cardNo.slice(-4)}`);
                            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(344:17)", "entry");
                            Text.fontSize(14);
                            Text.fontColor('#666666');
                            Text.alignSelf(ItemAlign.Start);
                            Text.margin({ top: 4 });
                        }, Text);
                        Text.pop();
                        Column.pop();
                        Row.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        List.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WithdrawPage";
    }
}
registerNamedRoute(() => new WithdrawPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/WithdrawPage", pageFullPath: "entry/src/main/ets/pages/WithdrawPage", integratedHsp: "false", moduleType: "followWithHap" });
