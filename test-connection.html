<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Boot 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Spring Boot 连接测试</h1>
        
        <div class="test-section">
            <h3>基础连接测试</h3>
            <button onclick="testBasicConnection()">测试 /test 端点</button>
            <button onclick="testBankConnection()">测试 /bank/test 端点</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>API接口测试</h3>
            <button onclick="testCaptcha()">测试验证码接口</button>
            <button onclick="testLogin()">测试登录接口</button>
            <div id="apiResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>网络诊断</h3>
            <button onclick="testNetworkDiagnostic()">网络诊断</button>
            <div id="diagnosticResult" class="result"></div>
        </div>
    </div>

    <script>
        // 基础连接测试
        async function testBasicConnection() {
            const resultDiv = document.getElementById('basicResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试连接...';

            try {
                const response = await fetch('http://localhost:8080/test', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 连接成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 连接失败\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        // 银行卡测试端点
        async function testBankConnection() {
            const resultDiv = document.getElementById('basicResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试银行卡端点...';

            try {
                const response = await fetch('http://localhost:8080/bank/test', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 银行卡端点连接成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 银行卡端点连接失败\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        // 测试验证码接口
        async function testCaptcha() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试验证码接口...';

            try {
                const response = await fetch('http://localhost:8080/api/user/captcha', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 验证码接口成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 验证码接口失败\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        // 测试登录接口
        async function testLogin() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试登录接口...';

            try {
                // 使用FormData模拟表单提交
                const formData = new FormData();
                formData.append('username', '1111');
                formData.append('password', '123456');
                formData.append('captcha', '1234');

                const response = await fetch('http://localhost:8080/api/user/login', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录接口成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                } else {
                    const errorText = await response.text();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录接口失败\n状态码: ${response.status}\n错误: ${errorText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        // 网络诊断
        async function testNetworkDiagnostic() {
            const resultDiv = document.getElementById('diagnosticResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在进行网络诊断...';

            const tests = [
                { name: 'localhost:8080', url: 'http://localhost:8080/test' },
                { name: '127.0.0.1:8080', url: 'http://127.0.0.1:8080/test' },
                { name: '********:8080 (模拟器)', url: 'http://********:8080/test' }
            ];

            let results = '网络诊断结果:\n\n';

            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    if (response.ok) {
                        results += `✅ ${test.name}: 成功 (${duration}ms)\n`;
                    } else {
                        results += `❌ ${test.name}: HTTP ${response.status}\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: ${error.message}\n`;
                }
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = results;
        }

        // 页面加载时自动测试基础连接
        window.onload = function() {
            testBasicConnection();
        };
    </script>
</body>
</html>
