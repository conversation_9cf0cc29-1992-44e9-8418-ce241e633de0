<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-demo"
    mode="horizontal"
    :ellipsis="false"
    background-color="#2c3e50"
    text-color="#ecf0f1"
    active-text-color="#3498db"
    @select="handleSelect"
  >
    <el-menu-item index="0">
      <h1>电子钱包交易系统</h1>
    </el-menu-item>
    <div class="flex-grow" />
    <el-sub-menu index="1">
      <template #title>您好！{{ username }}</template>
      <el-menu-item index="user-info" @click="showUserInfo">用户信息</el-menu-item>
      <el-menu-item index="change-password" @click="showChangePassword">修改密码</el-menu-item>
      <el-menu-item index="logout">退出系统</el-menu-item>
    </el-sub-menu>
  </el-menu>

  <!-- 用户信息对话框 -->
  <el-dialog v-model="userInfoDialog" title="用户信息" width="500px" :close-on-click-modal="false">
    <div class="user-info-content">
      <div class="info-item">
        <label>账户名：</label>
        <span>{{ userInfo.username || '未设置' }}</span>
      </div>
      <div class="info-item">
        <label>真实姓名：</label>
        <span>{{ userInfo.realName || '未设置' }}</span>
      </div>
      <div class="info-item">
        <label>手机号：</label>
        <span>{{ userInfo.phone || '未设置' }}</span>
      </div>
      <div class="info-item">
        <label>邮箱：</label>
        <span>{{ userInfo.email || '未设置' }}</span>
      </div>
    </div>
    <template #footer>
      <el-button @click="userInfoDialog = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 修改密码对话框 -->
  <el-dialog v-model="changePasswordDialog" title="修改登录密码" width="500px" :close-on-click-modal="false">
    <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
      <el-form-item label="当前密码" prop="currentPassword" required>
        <el-input
          v-model="passwordForm.currentPassword"
          type="password"
          placeholder="请输入当前密码"
          show-password
          :disabled="passwordLoading"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword" required>
        <el-input
          v-model="passwordForm.newPassword"
          type="password"
          placeholder="请输入新密码（至少6位）"
          show-password
          :disabled="passwordLoading"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword" required>
        <el-input
          v-model="passwordForm.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
          :disabled="passwordLoading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="changePasswordDialog = false" :disabled="passwordLoading">取消</el-button>
      <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">确认修改</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api'

const router = useRouter()
const userStore = useUserStore()

// 对话框控制
const userInfoDialog = ref(false)
const changePasswordDialog = ref(false)
const passwordLoading = ref(false)

// 使用computed确保响应式更新
const username = computed(() => {
  return userStore.username ||
         JSON.parse(localStorage.getItem('user') || '{}').username ||
         '用户'
})

// 用户信息
const userInfo = computed(() => {
  const savedUser = JSON.parse(localStorage.getItem('user') || '{}')
  return {
    username: userStore.username || savedUser.username || '',
    realName: userStore.userInfo?.realName || savedUser.realName || '',
    phone: userStore.userInfo?.phone || savedUser.phone || '',
    email: userStore.userInfo?.email || savedUser.email || ''
  }
})

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const passwordFormRef = ref()
const activeIndex = ref('1')

// 显示用户信息
const showUserInfo = async () => {
  try {
    // 从后端获取最新用户信息
    const response = await userApi.getUserInfo()
    if (response.data && response.data.code === 200) {
      const userData = response.data.data
      // 更新用户信息显示
      Object.assign(userInfo.value, {
        username: userData.username || '',
        realName: userData.realName || '未设置',
        phone: userData.mobile || '未设置',
        email: userData.email || '未设置'
      })
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果获取失败，使用本地存储的信息
  }
  userInfoDialog.value = true
}

// 显示修改密码对话框
const showChangePassword = () => {
  changePasswordDialog.value = true
  // 重置表单
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

// 处理密码修改
const handleChangePassword = async () => {
  try {
    // 表单验证
    const valid = await passwordFormRef.value.validate()
    if (!valid) return

    passwordLoading.value = true

    // 获取当前用户ID
    const currentUser = JSON.parse(localStorage.getItem('user') || '{}')
    if (!currentUser.id) {
      ElMessage.error('用户信息获取失败，请重新登录')
      return
    }

    // 调用API修改登录密码
    const formData = new URLSearchParams()
    formData.append('userId', currentUser.id.toString())
    formData.append('oldPassword', passwordForm.currentPassword)
    formData.append('newPassword', passwordForm.newPassword)

    const response = await userApi.updateLoginPassword(formData)

    if (response.data && response.data.code === 200) {
      ElMessage.success('登录密码修改成功')
      changePasswordDialog.value = false

      // 重置表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    } else {
      throw new Error(response.data?.msg || '密码修改失败')
    }

  } catch (error) {
    console.error('修改密码失败:', error)
    const errorMessage = error.response?.data?.msg || error.message || '修改密码失败，请重试'
    ElMessage.error(errorMessage)
  } finally {
    passwordLoading.value = false
  }
}

const handleSelect = (key) => {
  if(key === 'logout'){
    userStore.logout()
    router.push('/login')
  }
}

// 组件挂载时从localStorage恢复用户信息
onMounted(() => {
  const savedUser = localStorage.getItem('user')
  if (savedUser && !userStore.username) {
    try {
      const userData = JSON.parse(savedUser)
      userStore.login(userData)
    } catch (error) {
      console.error('恢复用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
.flex-grow {
  flex-grow: 1;
}
.el-menu--horizontal > .el-menu-item:nth-child(1) {
  margin-right: auto;
}
h1 {
  color: #ecf0f1;
  font-size: 1.5rem;
  margin: 0;
}

/* 用户信息对话框样式 */
.user-info-content {
  padding: 20px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item label {
  font-weight: 600;
  color: #2c3e50;
  width: 100px;
  flex-shrink: 0;
}

.info-item span {
  color: #666;
  flex: 1;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  text-align: right;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
  border-color: #667eea;
}

/* 按钮样式 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

:deep(.el-button--primary.is-loading) {
  transform: none;
}
</style>