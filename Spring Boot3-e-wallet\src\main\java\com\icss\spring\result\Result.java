package com.icss.spring.result;

public class Result<T> {
    private int code; // 响应码: 200表示成功，其他表示失败
    private String msg; // 响应信息: 成功或失败的描述信息
    private T data; // 响应数据

    private Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    // 返回成功的消息和数据
    public static <T> Result<T> OK(String msg, T data) {
        return new Result<T>(200, msg, data);
    }

    // 返回成功的消息
    public static <T> Result<T> OK(String msg) {
        return new Result<T>(200, msg, null);
    }

    // 返回错误的消息
    public static <T> Result<T> error(String msg) {
        return new Result<T>(500, msg, null);
    }

    // 返回错误的消息和数据
    public static <T> Result<T> error(String msg, T data) {
        return new Result<T>(500, msg, data);
    }
}

