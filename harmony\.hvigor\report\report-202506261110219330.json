{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "efd171f6-b307-42bb-8fb1-8c0b3ee2c3df", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350806627000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e29a3d4-7ca9-493c-beaa-884d8284d723", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350824827200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d762c5f4-7f59-4bef-8941-5324219cadb3", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350825281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd5ff06-449f-4dad-8f18-b40ba01b1619", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588238815800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49de3c33-ac9b-4d27-a7e8-465121870b04", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588247719400, "endTime": 314588497284400}, "additional": {"children": ["8477dbe7-c2d0-48c0-8ada-dca0809ea2da", "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "b6f159a0-dbdf-42a4-b5c2-9566f1557cb5", "5ddd5a22-b00f-4fe0-b98a-9feff2055b00", "4cef2679-b07c-4acb-9323-c10b3eff983b", "8c0aff8c-8604-44f1-a30b-20a46fb9686c", "23db21b9-bb9f-4cf2-9f65-c6442150fe01"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8477dbe7-c2d0-48c0-8ada-dca0809ea2da", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588247721500, "endTime": 314588266684000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "cc1e1ff1-511f-4c4c-a19b-4eeba0295b96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588266702000, "endTime": 314588495796500}, "additional": {"children": ["f410ec0c-28d7-4af8-8498-b7a320ad292b", "9c27654c-19a1-4216-9834-b708f54918b6", "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "4a081aa5-aa63-4b00-94ed-42abb14e58ef", "3ecbf023-2b5e-4087-9a66-05b3234f86ca", "cc5f7689-cba5-4294-aef6-c7ca29ec4b90", "78b71e68-b4f2-41ec-b06f-4136a8937a92", "52822d53-f665-4c6c-bef6-973e37640022", "41c09cc9-bb0f-411a-8c6e-bbef85a87a53"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "fe2009d8-370a-4796-9b46-7e9872b1b276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6f159a0-dbdf-42a4-b5c2-9566f1557cb5", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588495820500, "endTime": 314588497265200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "53ab2321-caa2-4b2e-82b4-6de3af19759f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ddd5a22-b00f-4fe0-b98a-9feff2055b00", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588497272000, "endTime": 314588497279500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "5b564dd9-f00a-440d-8daa-bfbaa458601a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cef2679-b07c-4acb-9323-c10b3eff983b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588252193100, "endTime": 314588252231100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "719b5294-0dbf-4b43-be55-76cfd96d07ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "719b5294-0dbf-4b43-be55-76cfd96d07ab", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588252193100, "endTime": 314588252231100}, "additional": {"logType": "info", "children": [], "durationId": "4cef2679-b07c-4acb-9323-c10b3eff983b", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "8c0aff8c-8604-44f1-a30b-20a46fb9686c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588259124400, "endTime": 314588259145200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "e80f1828-0e88-47e0-9bb8-1b160e49ed60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e80f1828-0e88-47e0-9bb8-1b160e49ed60", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588259124400, "endTime": 314588259145200}, "additional": {"logType": "info", "children": [], "durationId": "8c0aff8c-8604-44f1-a30b-20a46fb9686c", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "2cf61cdc-4fda-4ad3-9c05-cc698e328bc3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588259215800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a859ff1-59ef-408c-850e-b7d9cc3333cd", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588266534900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc1e1ff1-511f-4c4c-a19b-4eeba0295b96", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588247721500, "endTime": 314588266684000}, "additional": {"logType": "info", "children": [], "durationId": "8477dbe7-c2d0-48c0-8ada-dca0809ea2da", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "f410ec0c-28d7-4af8-8498-b7a320ad292b", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588274927300, "endTime": 314588274941000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "a42f246f-c41c-4bd9-9c49-95807494e2cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c27654c-19a1-4216-9834-b708f54918b6", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588274965800, "endTime": 314588282531100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "154e8794-5141-40ac-b890-9dcffcbd957b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588282547700, "endTime": 314588416199300}, "additional": {"children": ["b7c15dca-29e7-427c-89ac-f9aac25f3f98", "61e6b021-fdc6-4eb1-9538-ef5c8c23b0d7", "af982f24-abed-4521-9065-8b6c6b44bcb0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "c903db45-1a04-4a40-9ac4-1f432279f27d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a081aa5-aa63-4b00-94ed-42abb14e58ef", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588416217100, "endTime": 314588442274600}, "additional": {"children": ["82d575c2-1400-47fe-b855-01c37bb095f8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "dccdaecc-ae2f-43c5-9d25-ed01c44ead27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ecbf023-2b5e-4087-9a66-05b3234f86ca", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588442284000, "endTime": 314588466251500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "cb4362ab-d736-4fc2-a528-118eb7ee7452"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc5f7689-cba5-4294-aef6-c7ca29ec4b90", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588467759200, "endTime": 314588476728800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "40356808-edcf-41c1-8873-067580abf25a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78b71e68-b4f2-41ec-b06f-4136a8937a92", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588476779900, "endTime": 314588495594300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "0fdd64c2-3473-426d-b912-e23b13cbce6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52822d53-f665-4c6c-bef6-973e37640022", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588495645900, "endTime": 314588495782700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "c21721c1-34d8-4477-897f-09376a562694"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a42f246f-c41c-4bd9-9c49-95807494e2cb", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588274927300, "endTime": 314588274941000}, "additional": {"logType": "info", "children": [], "durationId": "f410ec0c-28d7-4af8-8498-b7a320ad292b", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "154e8794-5141-40ac-b890-9dcffcbd957b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588274965800, "endTime": 314588282531100}, "additional": {"logType": "info", "children": [], "durationId": "9c27654c-19a1-4216-9834-b708f54918b6", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "b7c15dca-29e7-427c-89ac-f9aac25f3f98", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588283498900, "endTime": 314588283521000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "logId": "ca330f7b-ff9d-4c5f-b5f9-7a840450fe45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca330f7b-ff9d-4c5f-b5f9-7a840450fe45", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588283498900, "endTime": 314588283521000}, "additional": {"logType": "info", "children": [], "durationId": "b7c15dca-29e7-427c-89ac-f9aac25f3f98", "parent": "c903db45-1a04-4a40-9ac4-1f432279f27d"}}, {"head": {"id": "61e6b021-fdc6-4eb1-9538-ef5c8c23b0d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588286279700, "endTime": 314588415299100}, "additional": {"children": ["fc26a910-2eff-4397-9946-0c114c01912e", "5fa4b615-e23f-44f3-bfc7-aee39d127263"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "logId": "0b9f644f-eaec-4402-a5f2-5492ea464e0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc26a910-2eff-4397-9946-0c114c01912e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588286281300, "endTime": 314588327492200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61e6b021-fdc6-4eb1-9538-ef5c8c23b0d7", "logId": "0fd61345-9c3e-4211-abaf-c2ac5fdfddb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fa4b615-e23f-44f3-bfc7-aee39d127263", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588327511900, "endTime": 314588415286500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61e6b021-fdc6-4eb1-9538-ef5c8c23b0d7", "logId": "d2e58585-278f-4813-9a77-ebe2b6e72f9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cc5334c-8efe-4016-9e88-9e9b01f9616f", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588286290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37eefdc-1ae6-48b2-aceb-5796f412aade", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588327356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fd61345-9c3e-4211-abaf-c2ac5fdfddb5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588286281300, "endTime": 314588327492200}, "additional": {"logType": "info", "children": [], "durationId": "fc26a910-2eff-4397-9946-0c114c01912e", "parent": "0b9f644f-eaec-4402-a5f2-5492ea464e0b"}}, {"head": {"id": "e5f3710e-1d53-47dd-904c-b167f0d7d5d5", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588327526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b909382f-8fc4-4fbf-9ca3-c4449fd99ddf", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588335317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e6fa36-0e33-410d-894e-ef5abbf74097", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588335446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da27d9d-b99e-434c-970c-c4a8bf2fa5e0", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588335597000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074c001d-8f9c-45f1-89f4-0a2d0e01b54c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588335705000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9702b1e1-1f7c-40b2-a06b-5a9a2e96828f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588337619100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa9c3c0-f3a3-4c6e-bbd6-ced4fdc5a45e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588341325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca9036f-4129-4f80-ad39-95ec86da6ee2", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588353077700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212b9cf9-b25e-454b-9367-54b82f8774d9", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588383704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "159c9826-67ef-4558-8788-87fb59d7022c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588383874200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 10}, "markType": "other"}}, {"head": {"id": "85519ed4-f6be-4317-9047-e52614df04e7", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588383901700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 10}, "markType": "other"}}, {"head": {"id": "d76d13f5-09e3-44fa-b838-8e4193b5b538", "name": "Project task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588414935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f207db-b764-4128-bb16-c42ccb4c9ff2", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588415089200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0149b22f-249a-4f9a-866a-494fb9785fb7", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588415172000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11fdbc8-6cc2-4636-b2f0-e18203d623f6", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588415231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e58585-278f-4813-9a77-ebe2b6e72f9d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588327511900, "endTime": 314588415286500}, "additional": {"logType": "info", "children": [], "durationId": "5fa4b615-e23f-44f3-bfc7-aee39d127263", "parent": "0b9f644f-eaec-4402-a5f2-5492ea464e0b"}}, {"head": {"id": "0b9f644f-eaec-4402-a5f2-5492ea464e0b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588286279700, "endTime": 314588415299100}, "additional": {"logType": "info", "children": ["0fd61345-9c3e-4211-abaf-c2ac5fdfddb5", "d2e58585-278f-4813-9a77-ebe2b6e72f9d"], "durationId": "61e6b021-fdc6-4eb1-9538-ef5c8c23b0d7", "parent": "c903db45-1a04-4a40-9ac4-1f432279f27d"}}, {"head": {"id": "af982f24-abed-4521-9065-8b6c6b44bcb0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588416165200, "endTime": 314588416182300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "logId": "8ad3ea7e-b1a5-40de-9f94-1096478ae668"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ad3ea7e-b1a5-40de-9f94-1096478ae668", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588416165200, "endTime": 314588416182300}, "additional": {"logType": "info", "children": [], "durationId": "af982f24-abed-4521-9065-8b6c6b44bcb0", "parent": "c903db45-1a04-4a40-9ac4-1f432279f27d"}}, {"head": {"id": "c903db45-1a04-4a40-9ac4-1f432279f27d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588282547700, "endTime": 314588416199300}, "additional": {"logType": "info", "children": ["ca330f7b-ff9d-4c5f-b5f9-7a840450fe45", "0b9f644f-eaec-4402-a5f2-5492ea464e0b", "8ad3ea7e-b1a5-40de-9f94-1096478ae668"], "durationId": "2443e392-ea52-4a1a-aa7f-c7240448cb7d", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "82d575c2-1400-47fe-b855-01c37bb095f8", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588417428100, "endTime": 314588442259300}, "additional": {"children": ["92d1840f-04c8-4217-8ab1-dca1bdec58b5", "b4ccdeed-b7d7-4ffd-8f1c-8680be8e0a68", "151ba2cc-c507-485e-ad16-197f64753d3f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a081aa5-aa63-4b00-94ed-42abb14e58ef", "logId": "dda7484d-b63e-40b9-94b8-071316452b18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92d1840f-04c8-4217-8ab1-dca1bdec58b5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588421539100, "endTime": 314588421557200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "82d575c2-1400-47fe-b855-01c37bb095f8", "logId": "47fba301-8c8d-4ad1-a752-5bde926576a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47fba301-8c8d-4ad1-a752-5bde926576a2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588421539100, "endTime": 314588421557200}, "additional": {"logType": "info", "children": [], "durationId": "92d1840f-04c8-4217-8ab1-dca1bdec58b5", "parent": "dda7484d-b63e-40b9-94b8-071316452b18"}}, {"head": {"id": "b4ccdeed-b7d7-4ffd-8f1c-8680be8e0a68", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588423664200, "endTime": 314588440782400}, "additional": {"children": ["839082b7-e6e7-4917-abe6-939d19bbc13b", "718b5b0d-5a12-4392-a56f-0fd592cf0acc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "82d575c2-1400-47fe-b855-01c37bb095f8", "logId": "476bd8cd-2864-4394-8c1e-58ce054a13e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "839082b7-e6e7-4917-abe6-939d19bbc13b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588423665300, "endTime": 314588428943600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4ccdeed-b7d7-4ffd-8f1c-8680be8e0a68", "logId": "148e09ab-3d44-4d18-b134-f9a75ad7cb7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "718b5b0d-5a12-4392-a56f-0fd592cf0acc", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588428960400, "endTime": 314588440770700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4ccdeed-b7d7-4ffd-8f1c-8680be8e0a68", "logId": "cf0786eb-1a8d-4358-a03a-8ed912f4e042"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78ee247c-01a7-4bd2-b7fb-bb51c05be6a3", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588423670800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037b6ebe-d619-4905-8323-330c59cd<PERSON>e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588428817000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148e09ab-3d44-4d18-b134-f9a75ad7cb7f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588423665300, "endTime": 314588428943600}, "additional": {"logType": "info", "children": [], "durationId": "839082b7-e6e7-4917-abe6-939d19bbc13b", "parent": "476bd8cd-2864-4394-8c1e-58ce054a13e2"}}, {"head": {"id": "2db1f4e8-dd9e-4321-ae4a-be1a14e40142", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588428973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074b2c5e-0eeb-41c7-ba88-959e518c1953", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588435772800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b4a5701-632c-4412-b09a-d6174a060130", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588435923300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1524c0e-0176-4277-91bf-e5a82686de35", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588436279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152cf75e-469c-4df0-941e-da815dccf200", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588436463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeacec24-5ae5-446c-9fab-92fbf316e82a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588436544200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852f8d4c-0465-4c05-a14d-2e4b41a83307", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588436606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adcb415f-5cf2-4250-9f68-46611126832c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588436692600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413d16ad-74e4-42d3-8327-b5cb7d83d26e", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588440445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c7cc43-2778-4a01-bb96-060e3da070f0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588440594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4eb1e3-f08d-49b7-a6b3-1ccbcd14d6b4", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588440661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb05cd45-539e-4163-8366-a2e6965c550e", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588440716900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf0786eb-1a8d-4358-a03a-8ed912f4e042", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588428960400, "endTime": 314588440770700}, "additional": {"logType": "info", "children": [], "durationId": "718b5b0d-5a12-4392-a56f-0fd592cf0acc", "parent": "476bd8cd-2864-4394-8c1e-58ce054a13e2"}}, {"head": {"id": "476bd8cd-2864-4394-8c1e-58ce054a13e2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588423664200, "endTime": 314588440782400}, "additional": {"logType": "info", "children": ["148e09ab-3d44-4d18-b134-f9a75ad7cb7f", "cf0786eb-1a8d-4358-a03a-8ed912f4e042"], "durationId": "b4ccdeed-b7d7-4ffd-8f1c-8680be8e0a68", "parent": "dda7484d-b63e-40b9-94b8-071316452b18"}}, {"head": {"id": "151ba2cc-c507-485e-ad16-197f64753d3f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588442232100, "endTime": 314588442245100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "82d575c2-1400-47fe-b855-01c37bb095f8", "logId": "659f3b48-21a5-419c-b471-c89136a5473b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "659f3b48-21a5-419c-b471-c89136a5473b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588442232100, "endTime": 314588442245100}, "additional": {"logType": "info", "children": [], "durationId": "151ba2cc-c507-485e-ad16-197f64753d3f", "parent": "dda7484d-b63e-40b9-94b8-071316452b18"}}, {"head": {"id": "dda7484d-b63e-40b9-94b8-071316452b18", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588417428100, "endTime": 314588442259300}, "additional": {"logType": "info", "children": ["47fba301-8c8d-4ad1-a752-5bde926576a2", "476bd8cd-2864-4394-8c1e-58ce054a13e2", "659f3b48-21a5-419c-b471-c89136a5473b"], "durationId": "82d575c2-1400-47fe-b855-01c37bb095f8", "parent": "dccdaecc-ae2f-43c5-9d25-ed01c44ead27"}}, {"head": {"id": "dccdaecc-ae2f-43c5-9d25-ed01c44ead27", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588416217100, "endTime": 314588442274600}, "additional": {"logType": "info", "children": ["dda7484d-b63e-40b9-94b8-071316452b18"], "durationId": "4a081aa5-aa63-4b00-94ed-42abb14e58ef", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "a5ff0a26-b804-41e2-878b-c27c9e4697fe", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588465745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d286e50e-7513-4e23-a406-b3fbaedb5c6e", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588466123400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4362ab-d736-4fc2-a528-118eb7ee7452", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588442284000, "endTime": 314588466251500}, "additional": {"logType": "info", "children": [], "durationId": "3ecbf023-2b5e-4087-9a66-05b3234f86ca", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "41c09cc9-bb0f-411a-8c6e-bbef85a87a53", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588467400700, "endTime": 314588467728400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "logId": "7d6ea65f-bc9a-40dd-a230-9fbd1dcc7cf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0585865-af1f-453b-8e95-a5d64b20381f", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588467439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6ea65f-bc9a-40dd-a230-9fbd1dcc7cf7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588467400700, "endTime": 314588467728400}, "additional": {"logType": "info", "children": [], "durationId": "41c09cc9-bb0f-411a-8c6e-bbef85a87a53", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "40a3d209-8fc1-4f9d-8383-d8de7f2f2aa2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588469640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd0ecd9-c732-466c-815a-6e62a96ed708", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588475436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40356808-edcf-41c1-8873-067580abf25a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588467759200, "endTime": 314588476728800}, "additional": {"logType": "info", "children": [], "durationId": "cc5f7689-cba5-4294-aef6-c7ca29ec4b90", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "f3a6a605-b697-4e4f-98ea-1717c20be0d3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588476817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490111e1-64d5-4ad6-9961-7c705d571d33", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588487636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5be926-6b5e-43a8-ab9f-9695bafae9b6", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588487803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72850af0-c331-46cf-a75c-bfc1b3ea7b64", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588488246300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e47995-07a9-493e-8e0f-c13b4188c10f", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588491496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2237c1-a50a-47aa-9c9a-872543ba03b1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588491708400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fdd64c2-3473-426d-b912-e23b13cbce6d", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588476779900, "endTime": 314588495594300}, "additional": {"logType": "info", "children": [], "durationId": "78b71e68-b4f2-41ec-b06f-4136a8937a92", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "75a9fb68-e0ab-4611-8109-385748280dd8", "name": "Configuration phase cost:221 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588495673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21721c1-34d8-4477-897f-09376a562694", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588495645900, "endTime": 314588495782700}, "additional": {"logType": "info", "children": [], "durationId": "52822d53-f665-4c6c-bef6-973e37640022", "parent": "fe2009d8-370a-4796-9b46-7e9872b1b276"}}, {"head": {"id": "fe2009d8-370a-4796-9b46-7e9872b1b276", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588266702000, "endTime": 314588495796500}, "additional": {"logType": "info", "children": ["a42f246f-c41c-4bd9-9c49-95807494e2cb", "154e8794-5141-40ac-b890-9dcffcbd957b", "c903db45-1a04-4a40-9ac4-1f432279f27d", "dccdaecc-ae2f-43c5-9d25-ed01c44ead27", "cb4362ab-d736-4fc2-a528-118eb7ee7452", "40356808-edcf-41c1-8873-067580abf25a", "0fdd64c2-3473-426d-b912-e23b13cbce6d", "c21721c1-34d8-4477-897f-09376a562694", "7d6ea65f-bc9a-40dd-a230-9fbd1dcc7cf7"], "durationId": "0b569d9b-71ff-45d6-96cc-dc20045dfd59", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "23db21b9-bb9f-4cf2-9f65-c6442150fe01", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588497228100, "endTime": 314588497246900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49de3c33-ac9b-4d27-a7e8-465121870b04", "logId": "bcb533c9-0fe4-49aa-a981-89ff5bbb7b41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bcb533c9-0fe4-49aa-a981-89ff5bbb7b41", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588497228100, "endTime": 314588497246900}, "additional": {"logType": "info", "children": [], "durationId": "23db21b9-bb9f-4cf2-9f65-c6442150fe01", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "53ab2321-caa2-4b2e-82b4-6de3af19759f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588495820500, "endTime": 314588497265200}, "additional": {"logType": "info", "children": [], "durationId": "b6f159a0-dbdf-42a4-b5c2-9566f1557cb5", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "5b564dd9-f00a-440d-8daa-bfbaa458601a", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588497272000, "endTime": 314588497279500}, "additional": {"logType": "info", "children": [], "durationId": "5ddd5a22-b00f-4fe0-b98a-9feff2055b00", "parent": "374f8044-37d6-4875-ad9a-eee2d02c8baf"}}, {"head": {"id": "374f8044-37d6-4875-ad9a-eee2d02c8baf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588247719400, "endTime": 314588497284400}, "additional": {"logType": "info", "children": ["cc1e1ff1-511f-4c4c-a19b-4eeba0295b96", "fe2009d8-370a-4796-9b46-7e9872b1b276", "53ab2321-caa2-4b2e-82b4-6de3af19759f", "5b564dd9-f00a-440d-8daa-bfbaa458601a", "719b5294-0dbf-4b43-be55-76cfd96d07ab", "e80f1828-0e88-47e0-9bb8-1b160e49ed60", "bcb533c9-0fe4-49aa-a981-89ff5bbb7b41"], "durationId": "49de3c33-ac9b-4d27-a7e8-465121870b04"}}, {"head": {"id": "43dbd1e1-a535-4be9-a06b-97bcb2f12151", "name": "Configuration task cost before running: 255 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588497412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabde965-9793-4ad4-aa81-1645fdf98c72", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588504845400, "endTime": 314588514645000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "41475779-11e2-4242-970b-5d7c29e7bd47", "logId": "29eca70a-391d-4fa5-9fd2-9989b9d94dd9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41475779-11e2-4242-970b-5d7c29e7bd47", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588499226400}, "additional": {"logType": "detail", "children": [], "durationId": "dabde965-9793-4ad4-aa81-1645fdf98c72"}}, {"head": {"id": "ce52c542-0708-4eeb-a1ea-6a1c768a7370", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588499865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb3bffa8-9869-407f-b306-6f0dd402a29b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588500078300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c3ac34-ad81-4d35-80f4-a7a5c8bba28d", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588504862200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d9c64f-8be6-4817-ad38-1a2abc05b542", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588514343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10533e44-06c7-4188-ae18-2979a4b422d6", "name": "entry : default@PreBuild cost memory 0.28469085693359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588514488900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29eca70a-391d-4fa5-9fd2-9989b9d94dd9", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588504845400, "endTime": 314588514645000}, "additional": {"logType": "info", "children": [], "durationId": "dabde965-9793-4ad4-aa81-1645fdf98c72"}}, {"head": {"id": "d7b30e74-8948-43cc-afe5-1197d273ec65", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588521415900, "endTime": 314588523708100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "adc02483-994d-4cb6-963a-852bcc635ac2", "logId": "19fe277b-6003-4e15-beb2-6db848b6e264"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adc02483-994d-4cb6-963a-852bcc635ac2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588519776900}, "additional": {"logType": "detail", "children": [], "durationId": "d7b30e74-8948-43cc-afe5-1197d273ec65"}}, {"head": {"id": "272f055e-e1c2-452a-a121-90b24900bde6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588520405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e6dfc9-2fb8-45ac-a9e1-1c7f738e21f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588520526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe88251-f7a0-422d-b44e-d8b84c26eaf7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588521423300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30ad8ab-8560-4bf5-804d-c1827ffb29e4", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588523496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da0fb06-947a-48bd-b2aa-fd49996d6e7d", "name": "entry : default@MergeProfile cost memory 0.11469268798828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588523633100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fe277b-6003-4e15-beb2-6db848b6e264", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588521415900, "endTime": 314588523708100}, "additional": {"logType": "info", "children": [], "durationId": "d7b30e74-8948-43cc-afe5-1197d273ec65"}}, {"head": {"id": "ed172eff-d52f-4558-8fd9-564fb35428c3", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588527588200, "endTime": 314588530554700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e686e86c-3c38-4866-9e4b-99c4a2d42863", "logId": "1b4ba752-94d3-4391-ad94-9e705372976f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e686e86c-3c38-4866-9e4b-99c4a2d42863", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588525973400}, "additional": {"logType": "detail", "children": [], "durationId": "ed172eff-d52f-4558-8fd9-564fb35428c3"}}, {"head": {"id": "b7610861-3fad-49de-91ea-81daa00e28d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588526557300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e38755e-d10c-4453-a30a-b40689d2343b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588526677400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8b8337c-ecda-4784-ad13-29762d228d56", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588527598600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89eaed9b-2355-4fd9-abe4-04be92b139dc", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588528729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ed644f-2da4-430d-afd0-fc9510d74138", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588530313000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1791c90f-7bea-4790-9c47-0a6ecfedbd5e", "name": "entry : default@CreateBuildProfile cost memory 0.10340118408203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588530445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4ba752-94d3-4391-ad94-9e705372976f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588527588200, "endTime": 314588530554700}, "additional": {"logType": "info", "children": [], "durationId": "ed172eff-d52f-4558-8fd9-564fb35428c3"}}, {"head": {"id": "3e3cce35-4bc2-4dd7-904b-c7feb2f3dc98", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588535798800, "endTime": 314588537068700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "70c3940a-a49b-47a6-bc97-d75c5337e433", "logId": "93083fbb-f6d3-4c85-bf93-3f8bce612d8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70c3940a-a49b-47a6-bc97-d75c5337e433", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588532954300}, "additional": {"logType": "detail", "children": [], "durationId": "3e3cce35-4bc2-4dd7-904b-c7feb2f3dc98"}}, {"head": {"id": "36389b62-9402-4871-a255-2f8dd375316d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588533916200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0b190b-1b39-43e6-8106-c2f5aa10ea16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588534150000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2b0f253-9003-4ce9-ad1a-4d884ec608bf", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588535813900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de87cfbd-59f1-4bce-83f7-6e995c95d3e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588536023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1e8073-ca32-4e7b-a8eb-1645f4717765", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588536259600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "309dae4e-21e1-45cf-beb5-8cb422c94b2c", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588536586600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f48cff-a6bd-49fe-9b4c-1a787116aff3", "name": "runTaskFromQueue task cost before running: 294 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588536825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93083fbb-f6d3-4c85-bf93-3f8bce612d8b", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588535798800, "endTime": 314588537068700, "totalTime": 967600}, "additional": {"logType": "info", "children": [], "durationId": "3e3cce35-4bc2-4dd7-904b-c7feb2f3dc98"}}, {"head": {"id": "f3842f54-f4a6-4dac-9b4e-3c75f1035afe", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588547526100, "endTime": 314588549276300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "15a985bf-e319-4768-9976-ca0cf9c412ac", "logId": "ca6e1ba6-0a89-4055-a42f-60aa2ca1e223"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15a985bf-e319-4768-9976-ca0cf9c412ac", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588539608900}, "additional": {"logType": "detail", "children": [], "durationId": "f3842f54-f4a6-4dac-9b4e-3c75f1035afe"}}, {"head": {"id": "28b9c961-b7cf-40ad-b10a-e324118297e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588540191400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e397730-ab5d-440e-988b-acb377af0774", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588540299900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5d22a0-0b86-4d89-8941-a3dcdb07a9cf", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588547542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4eb4b4-1d8b-4f88-831e-33454cb70313", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588547758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "888c9e69-8492-4671-ba16-983c2a5a0094", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588548444000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa4f083-8a1e-446b-998d-8c2d788a7712", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06685638427734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588548547100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6e1ba6-0a89-4055-a42f-60aa2ca1e223", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588547526100, "endTime": 314588549276300}, "additional": {"logType": "info", "children": [], "durationId": "f3842f54-f4a6-4dac-9b4e-3c75f1035afe"}}, {"head": {"id": "22b31fd7-40ac-4eb0-9727-b6a92ddcba24", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588555275900, "endTime": 314588556819900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d68d7499-8b7e-4a56-9466-80bc36514fa1", "logId": "8c9e325c-72e1-414f-ab68-5b12a852241f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d68d7499-8b7e-4a56-9466-80bc36514fa1", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588552421700}, "additional": {"logType": "detail", "children": [], "durationId": "22b31fd7-40ac-4eb0-9727-b6a92ddcba24"}}, {"head": {"id": "d2482970-4857-4588-a873-224854652d16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588553214100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f798ea3-4ec8-4497-b2a5-05e25578c012", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588553376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15da2b93-7561-4b32-ba71-1308d8444ed7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588555288300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a752d3-e8be-4b50-952a-e9afa8c5bacb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588556616200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b66c72-bde5-44aa-824b-46a99959b695", "name": "entry : default@ProcessProfile cost memory 0.058990478515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588556740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9e325c-72e1-414f-ab68-5b12a852241f", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588555275900, "endTime": 314588556819900}, "additional": {"logType": "info", "children": [], "durationId": "22b31fd7-40ac-4eb0-9727-b6a92ddcba24"}}, {"head": {"id": "6860958f-da11-4f7a-b191-7c2fbc311183", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588561242300, "endTime": 314588567735900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d8c570b7-1264-4445-b5c0-da7f2c66b35a", "logId": "af480714-7d98-48ad-b707-176fe9c1b878"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8c570b7-1264-4445-b5c0-da7f2c66b35a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588558750100}, "additional": {"logType": "detail", "children": [], "durationId": "6860958f-da11-4f7a-b191-7c2fbc311183"}}, {"head": {"id": "93cdb0ef-f965-46c1-b55f-a82f41ba753d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588559267500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddc9d61-6655-4c49-a289-cdc8565523ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588559392300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "786da037-9443-46cf-a2bf-e0f16106ad88", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588561252500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e434ab-d8ba-43ad-90c5-8995352bf7c4", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588567484500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d07e05e0-9244-4b19-aece-20e40bc024f9", "name": "entry : default@ProcessRouterMap cost memory 0.195220947265625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588567645300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af480714-7d98-48ad-b707-176fe9c1b878", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588561242300, "endTime": 314588567735900}, "additional": {"logType": "info", "children": [], "durationId": "6860958f-da11-4f7a-b191-7c2fbc311183"}}, {"head": {"id": "a444cb4d-74fb-45ff-a9af-e79134bafa4d", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588578240900, "endTime": 314588581375300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "167fd8e9-9742-46d6-87b0-5f62a643ef7f", "logId": "1bcbd867-6242-4599-a144-fb71b348f996"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "167fd8e9-9742-46d6-87b0-5f62a643ef7f", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588572475900}, "additional": {"logType": "detail", "children": [], "durationId": "a444cb4d-74fb-45ff-a9af-e79134bafa4d"}}, {"head": {"id": "74d0327e-2662-4061-9402-fb5d4e584550", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588573248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "967985e6-cf35-4a67-900e-8b54beae2b00", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588573430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2e89d4-9844-47eb-8a41-5f1f6e90b9a8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588574882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d88a7107-a59a-40b9-96e8-471edfc521b7", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588579659500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "500718ed-7632-4596-96bc-1bb74671d3a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588579807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc48cca-9c5c-414b-9060-3fd1c90379bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588579877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf915f74-fbe6-4ef9-9981-d94e052cfc8d", "name": "entry : default@PreviewProcessResource cost memory 0.06829833984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588579962600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6acca55-3452-47ad-826e-87586d8af7da", "name": "runTaskFromQueue task cost before running: 339 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588581241000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bcbd867-6242-4599-a144-fb71b348f996", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588578240900, "endTime": 314588581375300, "totalTime": 1787300}, "additional": {"logType": "info", "children": [], "durationId": "a444cb4d-74fb-45ff-a9af-e79134bafa4d"}}, {"head": {"id": "8dcbc62a-d08c-4ab6-922c-428520a9d27a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588591220200, "endTime": 314588637344100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "091b46a3-fddf-4732-a138-91ebab519c24", "logId": "f7001362-da4e-48b0-bb97-3e1628cab9b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "091b46a3-fddf-4732-a138-91ebab519c24", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588585204300}, "additional": {"logType": "detail", "children": [], "durationId": "8dcbc62a-d08c-4ab6-922c-428520a9d27a"}}, {"head": {"id": "63e09115-0ab3-4dab-9693-1bcd0360c971", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588585911000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33039db7-46c1-40ca-b50d-59aecbe9dbc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588586056300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ea4f95-23f2-46b0-812e-f79f27b910a2", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588591239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b66b7ae-4afb-4740-8ef2-6e389fbb0140", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588636817000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fca6396-7eed-4726-b329-b25a35e295b5", "name": "entry : default@GenerateLoaderJson cost memory -0.9835662841796875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588637128200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7001362-da4e-48b0-bb97-3e1628cab9b6", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588591220200, "endTime": 314588637344100}, "additional": {"logType": "info", "children": [], "durationId": "8dcbc62a-d08c-4ab6-922c-428520a9d27a"}}, {"head": {"id": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588650421000, "endTime": 314589124383400}, "additional": {"children": ["da0d80b1-56ab-45ba-9ac4-561f131bab6f", "f46ba6c0-5cbe-4d5c-8741-5aee71555dfd", "303f76d4-89da-4574-a16e-dcd03d0f1d21", "91343a09-6bf2-44af-aaba-75e035771422"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\resources' has been changed."], "detailId": "a73cf982-60b7-4389-bfaf-3848e2cab9ed", "logId": "064d144f-0ef8-4b62-b6db-1bec10037253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a73cf982-60b7-4389-bfaf-3848e2cab9ed", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588646150100}, "additional": {"logType": "detail", "children": [], "durationId": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc"}}, {"head": {"id": "49c8cc4b-abe6-4de6-b37b-d9b818b28441", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588646672500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789e8295-d595-41a2-a0ea-e40263d4d1b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588646776000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c5ae21-21b9-44a2-bb17-9e8b4a9904fc", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588647816100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c87711-8623-4937-97bf-f288aed89d7d", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588650464600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9473f7-b0d6-42c3-8a3d-7bcb0daa5099", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588665159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be05088c-d91a-46c3-9c57-9a3ba7b0c597", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588665363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0d80b1-56ab-45ba-9ac4-561f131bab6f", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588666955000, "endTime": 314588928776500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc", "logId": "542bdb49-61d5-4d8e-9ed4-7f2337bed970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "542bdb49-61d5-4d8e-9ed4-7f2337bed970", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588666955000, "endTime": 314588928776500}, "additional": {"logType": "info", "children": [], "durationId": "da0d80b1-56ab-45ba-9ac4-561f131bab6f", "parent": "064d144f-0ef8-4b62-b6db-1bec10037253"}}, {"head": {"id": "139d43ea-4417-45e4-b599-1849fa61632b", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588928900900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46ba6c0-5cbe-4d5c-8741-5aee71555dfd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588930489300, "endTime": 314588964579700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc", "logId": "f3766370-d514-4b66-805c-ab020aafee07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c804b1bc-a119-4f04-921f-ff5ceab226bb", "name": "current process  memoryUsage: {\n  rss: 105938944,\n  heapTotal: 121143296,\n  heapUsed: 112066840,\n  external: 3174507,\n  arrayBuffers: 168372\n} os memoryUsage :5.398372650146484", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588932069300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa94322-e083-4d72-a0da-d86b99d51d6d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588961537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3766370-d514-4b66-805c-ab020aafee07", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588930489300, "endTime": 314588964579700}, "additional": {"logType": "info", "children": [], "durationId": "f46ba6c0-5cbe-4d5c-8741-5aee71555dfd", "parent": "064d144f-0ef8-4b62-b6db-1bec10037253"}}, {"head": {"id": "1a59468d-aaf1-46be-b314-da321c4a3f9f", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588964767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303f76d4-89da-4574-a16e-dcd03d0f1d21", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588966424200, "endTime": 314589018143900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc", "logId": "a5aed3ec-1d57-400a-9ef0-b4b9e1615780"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18c16559-54a7-4eca-82cf-cd289d390eb6", "name": "current process  memoryUsage: {\n  rss: 105959424,\n  heapTotal: 121143296,\n  heapUsed: 110463232,\n  external: 3174633,\n  arrayBuffers: 118648\n} os memoryUsage :5.406185150146484", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588968125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4db27fa-e6d7-4b07-a255-ece95eafbdcc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589015290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5aed3ec-1d57-400a-9ef0-b4b9e1615780", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588966424200, "endTime": 314589018143900}, "additional": {"logType": "info", "children": [], "durationId": "303f76d4-89da-4574-a16e-dcd03d0f1d21", "parent": "064d144f-0ef8-4b62-b6db-1bec10037253"}}, {"head": {"id": "ede1c215-a22c-49c0-837c-33327eb57967", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589018575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91343a09-6bf2-44af-aaba-75e035771422", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589020223600, "endTime": 314589122745200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc", "logId": "a34bebec-6dfc-4ac3-b14a-955aa180414d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d0e4c9c-7e8a-4599-913e-a18d6d2540f0", "name": "current process  memoryUsage: {\n  rss: 105984000,\n  heapTotal: 121143296,\n  heapUsed: 110746880,\n  external: 3133086,\n  arrayBuffers: 127780\n} os memoryUsage :5.410514831542969", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589021430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1df2a52-00f5-437a-ae96-d760b33abec1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589116622700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34bebec-6dfc-4ac3-b14a-955aa180414d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589020223600, "endTime": 314589122745200}, "additional": {"logType": "info", "children": [], "durationId": "91343a09-6bf2-44af-aaba-75e035771422", "parent": "064d144f-0ef8-4b62-b6db-1bec10037253"}}, {"head": {"id": "7ad14d9d-7b8b-4d56-8256-4b163c63664e", "name": "entry : default@PreviewCompileResource cost memory -0.3271484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589123939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d21b689-f395-4363-988b-ee884265ec74", "name": "runTaskFromQueue task cost before running: 882 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589124226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064d144f-0ef8-4b62-b6db-1bec10037253", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588650421000, "endTime": 314589124383400, "totalTime": 473735000}, "additional": {"logType": "info", "children": ["542bdb49-61d5-4d8e-9ed4-7f2337bed970", "f3766370-d514-4b66-805c-ab020aafee07", "a5aed3ec-1d57-400a-9ef0-b4b9e1615780", "a34bebec-6dfc-4ac3-b14a-955aa180414d"], "durationId": "1ac90907-af41-4fe3-8e8c-f4f92d3caffc"}}, {"head": {"id": "b7d2c313-5934-4ece-928f-f60de2c432f8", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130062800, "endTime": 314589130601400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e5163871-8a34-4dcb-b7e3-394b4a8723ec", "logId": "0d177e01-0066-485b-beb5-311daa8edce8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5163871-8a34-4dcb-b7e3-394b4a8723ec", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589129007200}, "additional": {"logType": "detail", "children": [], "durationId": "b7d2c313-5934-4ece-928f-f60de2c432f8"}}, {"head": {"id": "15404cdf-726c-464d-b462-25838c24d1a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589129804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d771c02e-91d9-406c-9331-52e84388d8e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589129940400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addb2453-6ced-4aa7-9294-e2bb15c48ea5", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130074600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0df4dc64-5bdf-44fc-aba8-8277e3f2a00a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130191400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88436b5-4a89-463f-bd19-63cb9bfb313e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130256300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c57268a-b134-4f4e-8aad-6845b4156190", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384979248046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130343300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849cac28-e240-4628-bca2-d0d532acb97d", "name": "runTaskFromQueue task cost before running: 888 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d177e01-0066-485b-beb5-311daa8edce8", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589130062800, "endTime": 314589130601400, "totalTime": 391100}, "additional": {"logType": "info", "children": [], "durationId": "b7d2c313-5934-4ece-928f-f60de2c432f8"}}, {"head": {"id": "d271e4eb-ce9d-4a9e-8f9a-75dc9beb5f62", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589136492200, "endTime": 314589146892900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "778c8155-adc3-4a22-a56a-5b1e04759fa6", "logId": "e9229568-d8fa-42eb-8b0b-0c3c096b3860"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "778c8155-adc3-4a22-a56a-5b1e04759fa6", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589133862400}, "additional": {"logType": "detail", "children": [], "durationId": "d271e4eb-ce9d-4a9e-8f9a-75dc9beb5f62"}}, {"head": {"id": "435d7623-a57e-4280-89c0-0951ea9a1e2d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589134971200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d365f6-2ddb-4a9c-9df1-70a0c6de3849", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589135167000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f3720a-9fec-40b4-9027-872cbdb7e37f", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589136512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b934cc-c5e6-4a83-949d-078b85ae153b", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589138868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36225af1-f717-4bee-9587-884a939280d2", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589139109500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80096ed6-af9e-41ba-b63c-e09e66f495f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589139267000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d3a874-2bc4-4fec-8f75-f44f053cb1ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589139394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ac3917-3300-4810-a6a4-8403b2e80148", "name": "entry : default@CopyPreviewProfile cost memory 0.20914459228515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589146559400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad10b4a-9918-4362-abe7-2460243178a0", "name": "runTaskFromQueue task cost before running: 904 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589146771100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9229568-d8fa-42eb-8b0b-0c3c096b3860", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589136492200, "endTime": 314589146892900, "totalTime": 10247900}, "additional": {"logType": "info", "children": [], "durationId": "d271e4eb-ce9d-4a9e-8f9a-75dc9beb5f62"}}, {"head": {"id": "b99c24a7-ef63-4e3b-933f-446c1469fd1e", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589151682500, "endTime": 314589152214800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e65795f7-e6ef-4657-af74-4d7556aa552b", "logId": "03d67c32-796f-4df0-b914-780b01a7b9d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e65795f7-e6ef-4657-af74-4d7556aa552b", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589149905600}, "additional": {"logType": "detail", "children": [], "durationId": "b99c24a7-ef63-4e3b-933f-446c1469fd1e"}}, {"head": {"id": "56502c48-53d4-4405-8858-b04ba5512ca6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589150543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d19d5408-168a-4c30-be72-ac84562b0b6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589150683000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55668332-a793-4ab2-89b0-738c6fb2d380", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589151699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e894de6-41e9-4f08-aae4-ee8d0b137d95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589151843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29a6e2c-91f7-465b-a5f6-873298cbc512", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589151918400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3a5bde-ddb4-4155-a978-6d1c6c48e218", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589152029600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d52e3a5-5380-46f0-bd10-d506d2ab73e2", "name": "runTaskFromQueue task cost before running: 909 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589152136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d67c32-796f-4df0-b914-780b01a7b9d4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589151682500, "endTime": 314589152214800, "totalTime": 424500}, "additional": {"logType": "info", "children": [], "durationId": "b99c24a7-ef63-4e3b-933f-446c1469fd1e"}}, {"head": {"id": "76fb63f4-9d6d-42bf-b1cb-165f0c3a82c3", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154497600, "endTime": 314589154816200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8f70011e-cdba-464a-b518-bb5e71b9d4f5", "logId": "1566bb0c-a024-4943-8bd3-d5aa01a17c4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f70011e-cdba-464a-b518-bb5e71b9d4f5", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154436600}, "additional": {"logType": "detail", "children": [], "durationId": "76fb63f4-9d6d-42bf-b1cb-165f0c3a82c3"}}, {"head": {"id": "4088a83c-a842-4d39-bb76-97ce1dbfab1e", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154505200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe15688-ac1a-467b-b420-5ea72303d293", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165145b1-ef49-4f3b-8e05-870dd113aa83", "name": "runTaskFromQueue task cost before running: 912 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1566bb0c-a024-4943-8bd3-d5aa01a17c4d", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589154497600, "endTime": 314589154816200, "totalTime": 229600}, "additional": {"logType": "info", "children": [], "durationId": "76fb63f4-9d6d-42bf-b1cb-165f0c3a82c3"}}, {"head": {"id": "fd9093c0-03d6-4ce4-b65c-4dae60bd94e5", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589160059900, "endTime": 314589164831900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "14b0c21a-a24b-4489-85b5-c979f13112c3", "logId": "ba25f7f9-0228-4b20-9a70-d68080ba65fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14b0c21a-a24b-4489-85b5-c979f13112c3", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589157289900}, "additional": {"logType": "detail", "children": [], "durationId": "fd9093c0-03d6-4ce4-b65c-4dae60bd94e5"}}, {"head": {"id": "a5e1e49a-1268-4c4f-90a5-489e324b3f1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589158007600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee77c8e8-9742-40e5-8308-4581ce29cb1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589158138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f280172-1f73-4bd5-8e5f-76702d553319", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589160074300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881316fe-3ce6-4771-8351-feffca2a971e", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589162911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24fcdf84-7959-4bf5-bbfe-e3e729b9dca4", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589163107700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c73ad5a-5eeb-4200-aebe-b8883804bbcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589163226100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7259708-c6f6-4ff6-8e88-012f830a6247", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589163351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb80b76d-79b2-45d3-9ba4-ac7d1b0f7a57", "name": "entry : default@PreviewUpdateAssets cost memory 0.132049560546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589164449300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2757ddc6-1f46-4add-9efa-d8f6b6ec76c4", "name": "runTaskFromQueue task cost before running: 922 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589164675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba25f7f9-0228-4b20-9a70-d68080ba65fb", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589160059900, "endTime": 314589164831900, "totalTime": 4571200}, "additional": {"logType": "info", "children": [], "durationId": "fd9093c0-03d6-4ce4-b65c-4dae60bd94e5"}}, {"head": {"id": "e5b7677d-82c5-4f13-b04d-8567926e422f", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589179078100, "endTime": 314606206437200}, "additional": {"children": ["df5a6c2f-f2c2-4651-a12b-80321cf0972f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2089e5a-16c6-40d7-8fe0-0b264346709f", "logId": "50167a4e-6e98-4ee1-acce-3c8b07cf2a53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2089e5a-16c6-40d7-8fe0-0b264346709f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589169513500}, "additional": {"logType": "detail", "children": [], "durationId": "e5b7677d-82c5-4f13-b04d-8567926e422f"}}, {"head": {"id": "60a65d38-2ca0-4532-92af-b10f03eb4a84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589170405100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44442b1-59e0-4251-b8be-31d78be19ace", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589170597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4996669b-905f-4e68-890b-3f0a84dd501b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589179096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5a6c2f-f2c2-4651-a12b-80321cf0972f", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker4", "startTime": 314589211604800, "endTime": 314606206223800}, "additional": {"children": ["524275b5-a156-44cc-b09e-a893b1587299", "80c499a7-e1e1-45c1-99c2-7381274535cd", "ecf04eed-0d90-4a7d-b3a0-6602b0f877fc"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e5b7677d-82c5-4f13-b04d-8567926e422f", "logId": "b595ee36-7c50-4707-b964-9f5f3c0653c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e604eb2c-4ea4-42a1-b6f4-8ad8e237be21", "name": "entry : default@PreviewArkTS cost memory 0.8950271606445312", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589215934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faefb40c-b08c-4c55-a485-9e312454582a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314594409907900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "524275b5-a156-44cc-b09e-a893b1587299", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314594410938800, "endTime": 314594410953800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5a6c2f-f2c2-4651-a12b-80321cf0972f", "logId": "433fe69e-9397-4bd5-9c12-9bf3e7951deb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "433fe69e-9397-4bd5-9c12-9bf3e7951deb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314594410938800, "endTime": 314594410953800}, "additional": {"logType": "info", "children": [], "durationId": "524275b5-a156-44cc-b09e-a893b1587299", "parent": "b595ee36-7c50-4707-b964-9f5f3c0653c7"}}, {"head": {"id": "e8f31cbc-a0dc-4a96-a273-3db937a90f35", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314600449905700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c499a7-e1e1-45c1-99c2-7381274535cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314600451626000, "endTime": 314600451651200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5a6c2f-f2c2-4651-a12b-80321cf0972f", "logId": "95b80599-ef25-4be3-bb48-17f327bbc2ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95b80599-ef25-4be3-bb48-17f327bbc2ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314600451626000, "endTime": 314600451651200}, "additional": {"logType": "info", "children": [], "durationId": "80c499a7-e1e1-45c1-99c2-7381274535cd", "parent": "b595ee36-7c50-4707-b964-9f5f3c0653c7"}}, {"head": {"id": "2e9f4177-f257-4374-8cf7-89d225a91db0", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606205062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf04eed-0d90-4a7d-b3a0-6602b0f877fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606206128400, "endTime": 314606206145800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5a6c2f-f2c2-4651-a12b-80321cf0972f", "logId": "3ca859af-8575-4aad-b5c3-2b9ad8174599"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ca859af-8575-4aad-b5c3-2b9ad8174599", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606206128400, "endTime": 314606206145800}, "additional": {"logType": "info", "children": [], "durationId": "ecf04eed-0d90-4a7d-b3a0-6602b0f877fc", "parent": "b595ee36-7c50-4707-b964-9f5f3c0653c7"}}, {"head": {"id": "b595ee36-7c50-4707-b964-9f5f3c0653c7", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker4", "startTime": 314589211604800, "endTime": 314606206223800}, "additional": {"logType": "error", "children": ["433fe69e-9397-4bd5-9c12-9bf3e7951deb", "95b80599-ef25-4be3-bb48-17f327bbc2ff", "3ca859af-8575-4aad-b5c3-2b9ad8174599"], "durationId": "df5a6c2f-f2c2-4651-a12b-80321cf0972f", "parent": "50167a4e-6e98-4ee1-acce-3c8b07cf2a53"}}, {"head": {"id": "98b0e614-2821-4ffc-9c5d-aef0637057fa", "name": "default@PreviewArkTS watch work[4] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606206268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50167a4e-6e98-4ee1-acce-3c8b07cf2a53", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314589179078100, "endTime": 314606206437200}, "additional": {"logType": "error", "children": ["b595ee36-7c50-4707-b964-9f5f3c0653c7"], "durationId": "e5b7677d-82c5-4f13-b04d-8567926e422f"}}, {"head": {"id": "4c01efbd-f3bd-4746-9e49-5944be3b93f1", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606206566400}, "additional": {"logType": "debug", "children": [], "durationId": "e5b7677d-82c5-4f13-b04d-8567926e422f"}}, {"head": {"id": "3d68bd93-8168-4f99-aa5e-757ff9225add", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/LoginPage.ets:33:20\n The input parameter is not supported.\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606207223400}, "additional": {"logType": "debug", "children": [], "durationId": "e5b7677d-82c5-4f13-b04d-8567926e422f"}}, {"head": {"id": "042474fa-fb66-46dc-8598-9c233e01e631", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214443100, "endTime": 314606214486100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6cbca59-baf9-47e4-85f6-acb6187462b0", "logId": "bf9e876e-781e-4101-8cec-42243b77dad9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf9e876e-781e-4101-8cec-42243b77dad9", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214443100, "endTime": 314606214486100}, "additional": {"logType": "info", "children": [], "durationId": "042474fa-fb66-46dc-8598-9c233e01e631"}}, {"head": {"id": "6ecd2563-bb3b-4203-b54d-1bb6113627aa", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314588243113800, "endTime": 314606214578900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 10}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "19cb7dd7-9b1a-4770-bf43-0b32431d8824", "name": "BUILD FAILED in 17 s 972 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214604600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "bc2b04a9-20bf-42f4-88d9-d66dc9ef9d79", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214758900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2150594a-ec72-4166-b113-b9968607e782", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc067174-4e58-49b8-b5de-27bb3b84435b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214872300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca86e7f-032f-4395-bad4-d3e2480879db", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec0a192-26d4-4f20-9695-022c0c24531d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606214968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6567a5a3-5093-4447-bdb7-0e9f9b58043e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606215012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e635b5fe-655b-4d7d-8eb2-74040cb4664c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606215060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86b721f-0f66-492c-b34d-65b327044667", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606215919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf9709f-7309-488d-8cd8-82802d63aa36", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606216044600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdb8907-acce-42ff-8ac1-1e2b1323deb3", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606216497700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1929bff-8aaa-494d-adc3-6bdd7bc6625b", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606228993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87b5628-8726-475e-9612-e8f5bf40fbc7", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606229769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5698f485-bc31-4294-a33b-4d230e4713ae", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606229992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a152813f-697a-460f-88b7-4c00226a5edf", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606230853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61303e9c-a967-4502-9ff0-ed7d57ce33b8", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606231736800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a128a199-518f-43b4-990a-724a39e440d9", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606232129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ff3fa9-b44c-4018-97ed-466392c0706c", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606232448700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b71af02-781b-421a-abf4-8c86d2fe48db", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606232914700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff583597-a0ee-4eac-9837-d4cde032a98f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606235939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98d7163-12a8-432b-883e-9a38ef6598a3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606236801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e6311b-33c8-48b9-8dd6-e4b06d447dd4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606237123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ac30e9-4263-4664-b137-a0e104a4f168", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606237425000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb79aad5-3c4f-44cc-adf0-1e6b9a518589", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606238271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc927fa7-44ae-4644-9c02-7daf8154ac06", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606249466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e13c4b0-95fd-4179-a89e-08bb6d4b939a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606249783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b55c80-d541-4b35-8dec-3d330de82096", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606250173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710517df-afc6-4c08-98f5-6533389a6c52", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606250581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c958c4f-0d04-4f43-b0e2-eb35323853a7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606250956000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}