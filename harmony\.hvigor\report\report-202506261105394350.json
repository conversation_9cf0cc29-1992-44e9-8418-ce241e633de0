{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "10e31017-5d5d-4714-adbe-d1d96c15f84d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314304049882800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7510f2-8389-483b-a4d1-af0497a70754", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305738801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305753397400, "endTime": 314306040287700}, "additional": {"children": ["e34cafc1-c7bb-46bf-91ed-f5c2a7c8a61f", "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "cba97223-c454-418c-92f6-b63c8f0d14d0", "9c3048db-f590-4091-a1af-44bace901744", "d2b5cb1f-a884-40f5-80dd-f6f8bf66b2ba", "6ca032c7-ac8c-433d-b694-60c81dbe26f8", "8fbff2de-ad69-4a0f-9338-49c255cdea94"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "12964837-0d53-47eb-b749-a756cce3dc91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e34cafc1-c7bb-46bf-91ed-f5c2a7c8a61f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305753400000, "endTime": 314305778401500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "4629ab74-d12a-44b2-aa4f-6643d9573251"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305778433500, "endTime": 314306038914000}, "additional": {"children": ["4b39b8a4-b05e-4f6c-beb9-783a48ea7871", "544696e9-5339-403e-a5ed-e6157f477a46", "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "1da55320-49c9-401a-86c6-1b4f93d48a82", "7121be60-eaab-4c1b-aec5-57edd75833d9", "fddbfb32-8fb7-4df4-9163-4e1e1f58fe73", "034301ba-2042-45e3-8fa9-5e440c22f140", "1898c6f7-90aa-4226-aeeb-e69d04a05a99", "f6f9398d-d209-4bca-bb7e-2d193fa13fca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "fe64d47b-188e-4050-afbf-51be05ff2db0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cba97223-c454-418c-92f6-b63c8f0d14d0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306038938800, "endTime": 314306040268700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "b6c376a5-d959-4607-9b65-21a8e6ca1196"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c3048db-f590-4091-a1af-44bace901744", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306040275400, "endTime": 314306040281700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "ec122331-4b5f-4bbe-9cff-40187e4b288c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2b5cb1f-a884-40f5-80dd-f6f8bf66b2ba", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305759907100, "endTime": 314305759961900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "ebf0e810-129a-4a6b-89d5-a0cedd8a0931"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebf0e810-129a-4a6b-89d5-a0cedd8a0931", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305759907100, "endTime": 314305759961900}, "additional": {"logType": "info", "children": [], "durationId": "d2b5cb1f-a884-40f5-80dd-f6f8bf66b2ba", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "6ca032c7-ac8c-433d-b694-60c81dbe26f8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305767712200, "endTime": 314305767729000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "f818cfea-d286-4ad3-9a71-61bd5fb8d6b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f818cfea-d286-4ad3-9a71-61bd5fb8d6b4", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305767712200, "endTime": 314305767729000}, "additional": {"logType": "info", "children": [], "durationId": "6ca032c7-ac8c-433d-b694-60c81dbe26f8", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "e745c78c-f7ae-40e7-86d3-98ef1bf030a5", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305767786400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f22f157-134a-4d72-b85e-89eacd181ef4", "name": "Cache service initialization finished in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305778169800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4629ab74-d12a-44b2-aa4f-6643d9573251", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305753400000, "endTime": 314305778401500}, "additional": {"logType": "info", "children": [], "durationId": "e34cafc1-c7bb-46bf-91ed-f5c2a7c8a61f", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "4b39b8a4-b05e-4f6c-beb9-783a48ea7871", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305790587000, "endTime": 314305790608400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "c59c5d2a-b10c-4c01-8f29-8574465c1470"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "544696e9-5339-403e-a5ed-e6157f477a46", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305790636800, "endTime": 314305796090400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "c2f48ff2-9816-4e40-9a97-2a873dae3c7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305796117000, "endTime": 314305954901000}, "additional": {"children": ["b644f2a1-a0e4-471b-a0d9-97cec608c781", "422f1558-0327-4c89-b690-26e2349ddc41", "b5184fe0-75cb-4aaa-bfec-4f2a6c4a548f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "39c94dc0-6196-43ee-a4c0-7702eda194c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1da55320-49c9-401a-86c6-1b4f93d48a82", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305954922800, "endTime": 314305987882400}, "additional": {"children": ["0eb11cc7-2efd-4358-9079-1f7cdba9d63e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "3322cfd5-246a-4db6-b034-d60284f429cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7121be60-eaab-4c1b-aec5-57edd75833d9", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305987891100, "endTime": 314306012054000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "c520146f-30a6-4287-b735-5b09aa3ac1b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fddbfb32-8fb7-4df4-9163-4e1e1f58fe73", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306013732700, "endTime": 314306025234500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "f5faa267-aeba-40cf-bed5-c4ae61cba9c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "034301ba-2042-45e3-8fa9-5e440c22f140", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306025264900, "endTime": 314306038742700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "9fa2916e-a991-46de-a3a9-d778e2bfa695"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1898c6f7-90aa-4226-aeeb-e69d04a05a99", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306038765000, "endTime": 314306038886500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "04333cfe-79d5-4bc8-95c2-564f317799e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c59c5d2a-b10c-4c01-8f29-8574465c1470", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305790587000, "endTime": 314305790608400}, "additional": {"logType": "info", "children": [], "durationId": "4b39b8a4-b05e-4f6c-beb9-783a48ea7871", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "c2f48ff2-9816-4e40-9a97-2a873dae3c7a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305790636800, "endTime": 314305796090400}, "additional": {"logType": "info", "children": [], "durationId": "544696e9-5339-403e-a5ed-e6157f477a46", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "b644f2a1-a0e4-471b-a0d9-97cec608c781", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305798010800, "endTime": 314305798052400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "logId": "fc447e06-b015-4b78-9b9e-e6cf3faff56d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc447e06-b015-4b78-9b9e-e6cf3faff56d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305798010800, "endTime": 314305798052400}, "additional": {"logType": "info", "children": [], "durationId": "b644f2a1-a0e4-471b-a0d9-97cec608c781", "parent": "39c94dc0-6196-43ee-a4c0-7702eda194c9"}}, {"head": {"id": "422f1558-0327-4c89-b690-26e2349ddc41", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305802211200, "endTime": 314305953624600}, "additional": {"children": ["7292c674-91fe-4439-ba13-ac1e1f8fcd78", "95f64ef1-43af-4448-9845-abe971afea79"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "logId": "351db401-1515-4a24-9510-fbae3e8363fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7292c674-91fe-4439-ba13-ac1e1f8fcd78", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305802213200, "endTime": 314305809684600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "422f1558-0327-4c89-b690-26e2349ddc41", "logId": "200dcafa-2298-4a11-a75d-c524a6a97544"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95f64ef1-43af-4448-9845-abe971afea79", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305809703900, "endTime": 314305953609200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "422f1558-0327-4c89-b690-26e2349ddc41", "logId": "8ff77ea7-fb7d-418c-ab36-20612005adfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86ce5036-2288-417b-93b9-a47a05005a1c", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305802222900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a285395b-8a49-4b19-af49-7b9463d9518d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305809531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200dcafa-2298-4a11-a75d-c524a6a97544", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305802213200, "endTime": 314305809684600}, "additional": {"logType": "info", "children": [], "durationId": "7292c674-91fe-4439-ba13-ac1e1f8fcd78", "parent": "351db401-1515-4a24-9510-fbae3e8363fb"}}, {"head": {"id": "9ef51f6f-687f-4ccc-84e8-74c27472e6e2", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305809718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841b84d2-d757-4c3e-9a8e-ee4679aa804f", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305820249300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ddacd7-f78e-48d1-97d8-bf964f4ff984", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305820380900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6967fef4-c377-4e1f-a955-8974722170b9", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305820548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deef04bf-09bc-48a7-8bf3-311a40fd2c27", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305820653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4872b067-a1ab-4dd8-982d-c99cdfb7e4e1", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305823555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2187257f-f2a8-445b-ab10-15096b24ef52", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305828766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f819c3-f6e5-4473-9ecd-7fc88e32f443", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305861081700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cadf662e-387c-46af-b136-1edb5aa1b6a8", "name": "Sdk init in 94 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305923261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa28eb3-2c2b-43fe-9efd-658cde13d75c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305923399000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 5}, "markType": "other"}}, {"head": {"id": "cf9ec9a2-fada-4fa2-bd34-4eb749370a60", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305923543300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 5}, "markType": "other"}}, {"head": {"id": "8082271a-55c2-45df-ba28-b6959a1b96f3", "name": "Project task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305953192700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab59b453-aa2c-40ee-827f-b28e95823f81", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305953373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a7f940-20bb-426b-95bc-bd6d0e9ffbb8", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305953465500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a3812d-1323-47b3-969e-19b9d76799d6", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305953541400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff77ea7-fb7d-418c-ab36-20612005adfb", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305809703900, "endTime": 314305953609200}, "additional": {"logType": "info", "children": [], "durationId": "95f64ef1-43af-4448-9845-abe971afea79", "parent": "351db401-1515-4a24-9510-fbae3e8363fb"}}, {"head": {"id": "351db401-1515-4a24-9510-fbae3e8363fb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305802211200, "endTime": 314305953624600}, "additional": {"logType": "info", "children": ["200dcafa-2298-4a11-a75d-c524a6a97544", "8ff77ea7-fb7d-418c-ab36-20612005adfb"], "durationId": "422f1558-0327-4c89-b690-26e2349ddc41", "parent": "39c94dc0-6196-43ee-a4c0-7702eda194c9"}}, {"head": {"id": "b5184fe0-75cb-4aaa-bfec-4f2a6c4a548f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305954815100, "endTime": 314305954835900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "logId": "207b6cae-4653-4c16-b1a9-e26879846b8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "207b6cae-4653-4c16-b1a9-e26879846b8d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305954815100, "endTime": 314305954835900}, "additional": {"logType": "info", "children": [], "durationId": "b5184fe0-75cb-4aaa-bfec-4f2a6c4a548f", "parent": "39c94dc0-6196-43ee-a4c0-7702eda194c9"}}, {"head": {"id": "39c94dc0-6196-43ee-a4c0-7702eda194c9", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305796117000, "endTime": 314305954901000}, "additional": {"logType": "info", "children": ["fc447e06-b015-4b78-9b9e-e6cf3faff56d", "351db401-1515-4a24-9510-fbae3e8363fb", "207b6cae-4653-4c16-b1a9-e26879846b8d"], "durationId": "e2b77a7f-16a4-4872-a2bb-094e0c1d2283", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "0eb11cc7-2efd-4358-9079-1f7cdba9d63e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305956027700, "endTime": 314305987869700}, "additional": {"children": ["d40208cf-8057-4b37-81b7-2df43bfe61c1", "b1d05cb9-af57-4481-b030-1f75f1e30a4d", "278c255f-d326-4af6-b8b1-1e0733dc501a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da55320-49c9-401a-86c6-1b4f93d48a82", "logId": "783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d40208cf-8057-4b37-81b7-2df43bfe61c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305960567900, "endTime": 314305960596400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0eb11cc7-2efd-4358-9079-1f7cdba9d63e", "logId": "48481a39-b9e4-4ac4-8caa-0a20e88f851d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48481a39-b9e4-4ac4-8caa-0a20e88f851d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305960567900, "endTime": 314305960596400}, "additional": {"logType": "info", "children": [], "durationId": "d40208cf-8057-4b37-81b7-2df43bfe61c1", "parent": "783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9"}}, {"head": {"id": "b1d05cb9-af57-4481-b030-1f75f1e30a4d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305963892900, "endTime": 314305985825600}, "additional": {"children": ["ac06b243-2083-4e9d-a42c-6e28f945cda0", "a27d6be1-419b-480b-a780-39b9f33cb4b1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0eb11cc7-2efd-4358-9079-1f7cdba9d63e", "logId": "ab920925-f231-4ad1-bda6-5b163e19f6c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac06b243-2083-4e9d-a42c-6e28f945cda0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305963895400, "endTime": 314305968083900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1d05cb9-af57-4481-b030-1f75f1e30a4d", "logId": "24e1ebb2-e5c9-471b-bcbc-041b53efb758"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a27d6be1-419b-480b-a780-39b9f33cb4b1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305968101100, "endTime": 314305985807500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1d05cb9-af57-4481-b030-1f75f1e30a4d", "logId": "09641f02-773b-46f3-a7f1-9880b87c0566"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5319feca-c045-4d33-8a84-1f6dc1ea2954", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305963906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084d68fc-81f7-46c8-96b6-35c9da8b1a8c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305967946100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e1ebb2-e5c9-471b-bcbc-041b53efb758", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305963895400, "endTime": 314305968083900}, "additional": {"logType": "info", "children": [], "durationId": "ac06b243-2083-4e9d-a42c-6e28f945cda0", "parent": "ab920925-f231-4ad1-bda6-5b163e19f6c8"}}, {"head": {"id": "f07ed108-1916-4621-9ebd-dcbc78bf0080", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305968115100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66765dcf-7758-40c4-b5c2-5a84c8817c2d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305977937800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0a2d8f-679f-418b-a654-d0f77e8a4ef6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305978130300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cdba6e7-7258-49c5-a767-60898e5da136", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305978516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af361e5-f7a3-445e-9369-5677afda74fc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305978769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88689357-09c5-4268-8ce6-5ad92644e773", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305978897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a0326c-11ab-4ce7-9ee3-c1afb25baac1", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305979004300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c30bc13-93c5-4559-95d9-1d3510191d0b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305979124500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b86d9a-a7b1-4121-b2a8-8642d5986f1c", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305985267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c05a8e0-ab15-463e-bf52-9a8abc7884d5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305985485900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6629a32d-0214-4be0-b1ea-0db4a1562756", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305985608500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7798f7b-1626-4e55-8622-3e75bf9f598f", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305985710900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09641f02-773b-46f3-a7f1-9880b87c0566", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305968101100, "endTime": 314305985807500}, "additional": {"logType": "info", "children": [], "durationId": "a27d6be1-419b-480b-a780-39b9f33cb4b1", "parent": "ab920925-f231-4ad1-bda6-5b163e19f6c8"}}, {"head": {"id": "ab920925-f231-4ad1-bda6-5b163e19f6c8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305963892900, "endTime": 314305985825600}, "additional": {"logType": "info", "children": ["24e1ebb2-e5c9-471b-bcbc-041b53efb758", "09641f02-773b-46f3-a7f1-9880b87c0566"], "durationId": "b1d05cb9-af57-4481-b030-1f75f1e30a4d", "parent": "783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9"}}, {"head": {"id": "278c255f-d326-4af6-b8b1-1e0733dc501a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305987842100, "endTime": 314305987853700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0eb11cc7-2efd-4358-9079-1f7cdba9d63e", "logId": "8c9952d2-394e-4ae9-9adb-2c95d609146f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c9952d2-394e-4ae9-9adb-2c95d609146f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305987842100, "endTime": 314305987853700}, "additional": {"logType": "info", "children": [], "durationId": "278c255f-d326-4af6-b8b1-1e0733dc501a", "parent": "783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9"}}, {"head": {"id": "783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305956027700, "endTime": 314305987869700}, "additional": {"logType": "info", "children": ["48481a39-b9e4-4ac4-8caa-0a20e88f851d", "ab920925-f231-4ad1-bda6-5b163e19f6c8", "8c9952d2-394e-4ae9-9adb-2c95d609146f"], "durationId": "0eb11cc7-2efd-4358-9079-1f7cdba9d63e", "parent": "3322cfd5-246a-4db6-b034-d60284f429cc"}}, {"head": {"id": "3322cfd5-246a-4db6-b034-d60284f429cc", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305954922800, "endTime": 314305987882400}, "additional": {"logType": "info", "children": ["783a36d4-9f8a-4d0d-b2ab-2ec3aecd79a9"], "durationId": "1da55320-49c9-401a-86c6-1b4f93d48a82", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "b7b7fc22-dce5-4080-8f81-c6c0e94e20d2", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306011177400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef8f61d-2c81-44fc-91e0-ae4730b946ed", "name": "hvigorfile, resolve hvigorfile dependencies in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306011958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c520146f-30a6-4287-b735-5b09aa3ac1b5", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305987891100, "endTime": 314306012054000}, "additional": {"logType": "info", "children": [], "durationId": "7121be60-eaab-4c1b-aec5-57edd75833d9", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "f6f9398d-d209-4bca-bb7e-2d193fa13fca", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306013343700, "endTime": 314306013708500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "logId": "c67a2250-ad5c-45be-8c38-e2788bb1c312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7da31e7-4436-474e-9491-c49fa86c63d5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306013394400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67a2250-ad5c-45be-8c38-e2788bb1c312", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306013343700, "endTime": 314306013708500}, "additional": {"logType": "info", "children": [], "durationId": "f6f9398d-d209-4bca-bb7e-2d193fa13fca", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "2adc40c0-2d85-47b6-8f2b-3168ba538ba9", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306016050500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f660a1-b15e-4564-b55b-d32447f77032", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306023964400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5faa267-aeba-40cf-bed5-c4ae61cba9c5", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306013732700, "endTime": 314306025234500}, "additional": {"logType": "info", "children": [], "durationId": "fddbfb32-8fb7-4df4-9163-4e1e1f58fe73", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "cf5ba005-7a62-43a5-993d-4c0525f0454c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306025291000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95131165-d4e1-4c15-9c00-8b35a88acc4a", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306032614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013c686a-cb2c-4358-844e-9b6e0bb5b12f", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306032738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e528087-a42e-4c4e-afcc-b7b439473bea", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306032988200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce75258-c687-42c5-8de3-1b6a14225a83", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306035453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103b96af-e0cb-4f07-ac62-22d0e71ad2b9", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306035545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa2916e-a991-46de-a3a9-d778e2bfa695", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306025264900, "endTime": 314306038742700}, "additional": {"logType": "info", "children": [], "durationId": "034301ba-2042-45e3-8fa9-5e440c22f140", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "19a4150c-8635-497d-baf9-9123ee5aab12", "name": "Configuration phase cost:249 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306038789300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04333cfe-79d5-4bc8-95c2-564f317799e9", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306038765000, "endTime": 314306038886500}, "additional": {"logType": "info", "children": [], "durationId": "1898c6f7-90aa-4226-aeeb-e69d04a05a99", "parent": "fe64d47b-188e-4050-afbf-51be05ff2db0"}}, {"head": {"id": "fe64d47b-188e-4050-afbf-51be05ff2db0", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305778433500, "endTime": 314306038914000}, "additional": {"logType": "info", "children": ["c59c5d2a-b10c-4c01-8f29-8574465c1470", "c2f48ff2-9816-4e40-9a97-2a873dae3c7a", "39c94dc0-6196-43ee-a4c0-7702eda194c9", "3322cfd5-246a-4db6-b034-d60284f429cc", "c520146f-30a6-4287-b735-5b09aa3ac1b5", "f5faa267-aeba-40cf-bed5-c4ae61cba9c5", "9fa2916e-a991-46de-a3a9-d778e2bfa695", "04333cfe-79d5-4bc8-95c2-564f317799e9", "c67a2250-ad5c-45be-8c38-e2788bb1c312"], "durationId": "1ec8a670-5b91-449b-993b-ae89cc9f40dc", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "8fbff2de-ad69-4a0f-9338-49c255cdea94", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306040240400, "endTime": 314306040254500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63", "logId": "fbfb2033-e5c9-4f60-b067-65b97bea0a00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbfb2033-e5c9-4f60-b067-65b97bea0a00", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306040240400, "endTime": 314306040254500}, "additional": {"logType": "info", "children": [], "durationId": "8fbff2de-ad69-4a0f-9338-49c255cdea94", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "b6c376a5-d959-4607-9b65-21a8e6ca1196", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306038938800, "endTime": 314306040268700}, "additional": {"logType": "info", "children": [], "durationId": "cba97223-c454-418c-92f6-b63c8f0d14d0", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "ec122331-4b5f-4bbe-9cff-40187e4b288c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306040275400, "endTime": 314306040281700}, "additional": {"logType": "info", "children": [], "durationId": "9c3048db-f590-4091-a1af-44bace901744", "parent": "12964837-0d53-47eb-b749-a756cce3dc91"}}, {"head": {"id": "12964837-0d53-47eb-b749-a756cce3dc91", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305753397400, "endTime": 314306040287700}, "additional": {"logType": "info", "children": ["4629ab74-d12a-44b2-aa4f-6643d9573251", "fe64d47b-188e-4050-afbf-51be05ff2db0", "b6c376a5-d959-4607-9b65-21a8e6ca1196", "ec122331-4b5f-4bbe-9cff-40187e4b288c", "ebf0e810-129a-4a6b-89d5-a0cedd8a0931", "f818cfea-d286-4ad3-9a71-61bd5fb8d6b4", "fbfb2033-e5c9-4f60-b067-65b97bea0a00"], "durationId": "2f34d9e0-6ce6-4abd-af7f-778dc84acd63"}}, {"head": {"id": "b1a9fc28-7876-4901-9c50-95346ffbde0b", "name": "Configuration task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306040542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d74a24c-075d-4992-9d59-290f094cd473", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306046122900, "endTime": 314306056886300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e17b6690-be6b-48fd-9fa0-531b8842897d", "logId": "7bd330ce-a3a0-42ef-83d1-60b76fbf896d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e17b6690-be6b-48fd-9fa0-531b8842897d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306042339800}, "additional": {"logType": "detail", "children": [], "durationId": "1d74a24c-075d-4992-9d59-290f094cd473"}}, {"head": {"id": "0903c1f9-da5c-44c6-90ad-84d3bc8f68fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306042933900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16cbf5f2-c4c9-4e3d-961d-cad037fcd957", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306043039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d866de81-bf41-4ce6-a036-cf1783d6055b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306046142000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90bfea68-76db-4b06-927f-f94d18c671a4", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306056591300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cdc1e4f-80dd-4bbf-b312-2f75f648bbc5", "name": "entry : default@PreBuild cost memory 0.2916412353515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306056791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd330ce-a3a0-42ef-83d1-60b76fbf896d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306046122900, "endTime": 314306056886300}, "additional": {"logType": "info", "children": [], "durationId": "1d74a24c-075d-4992-9d59-290f094cd473"}}, {"head": {"id": "ae9fe8f3-5935-4c53-8460-fcfda7779bbe", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306063319500, "endTime": 314306066165200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "46211c4d-c5c3-4f32-9dcd-fcda25fac9ab", "logId": "558aac0b-7b94-4f6d-bbfb-e412854fbdcf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46211c4d-c5c3-4f32-9dcd-fcda25fac9ab", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306061546900}, "additional": {"logType": "detail", "children": [], "durationId": "ae9fe8f3-5935-4c53-8460-fcfda7779bbe"}}, {"head": {"id": "693c6ea8-3f72-493d-82df-f9445e3b00f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306062101900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cc7dc9-d1ac-44c3-955a-faae4897594a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306062234600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51d9824-7210-4672-89af-6238d94c86d3", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306063337400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0080d338-0855-4a06-9eed-b5a0b9943a60", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306065851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bea9374-b383-412a-a75e-1f60792ee0c0", "name": "entry : default@MergeProfile cost memory 0.1147918701171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306066046800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558aac0b-7b94-4f6d-bbfb-e412854fbdcf", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306063319500, "endTime": 314306066165200}, "additional": {"logType": "info", "children": [], "durationId": "ae9fe8f3-5935-4c53-8460-fcfda7779bbe"}}, {"head": {"id": "85ba1c07-8eaa-4b6d-a16c-e84ae6eac5c9", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306072836400, "endTime": 314306076672000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "949671e0-c8d8-4613-b858-24e301af1ee1", "logId": "6d2351e7-0064-41f0-8e54-3561fc122c14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "949671e0-c8d8-4613-b858-24e301af1ee1", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306070127600}, "additional": {"logType": "detail", "children": [], "durationId": "85ba1c07-8eaa-4b6d-a16c-e84ae6eac5c9"}}, {"head": {"id": "5c09df76-4c6c-41c0-91b2-a18ae7709dbc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306071182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959d1a4c-bb3b-4a0f-a6af-3fb205ba541b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306071469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca83e8f-ddd7-4cc4-8454-fd443c1e677b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306072851200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7baec2aa-b89f-42bf-ace0-1e75cc34dcfc", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306074273700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2cb237f-d1f5-4d30-8b18-041e8b3875e4", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306076393400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb2d8e38-1b24-4a7e-bbf5-56b753bd1dda", "name": "entry : default@CreateBuildProfile cost memory 0.100860595703125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306076556700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2351e7-0064-41f0-8e54-3561fc122c14", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306072836400, "endTime": 314306076672000}, "additional": {"logType": "info", "children": [], "durationId": "85ba1c07-8eaa-4b6d-a16c-e84ae6eac5c9"}}, {"head": {"id": "a5853f8a-2773-49eb-b496-a3d1bc68cd5e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082396900, "endTime": 314306082913600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1eb57fc9-4f99-4c62-92d4-ab3927337311", "logId": "82c49748-5cb6-4760-b7af-762bee0ca0ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1eb57fc9-4f99-4c62-92d4-ab3927337311", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306080572700}, "additional": {"logType": "detail", "children": [], "durationId": "a5853f8a-2773-49eb-b496-a3d1bc68cd5e"}}, {"head": {"id": "a4332f63-5abf-487b-a9d1-b0b7815773fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306081325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ba416b-a48f-4817-8639-a0eec973ac4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306081459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96c3d4b-49fe-4f87-82bd-7523f79dc396", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082411600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e560433-7c0f-4a34-9300-129d13f3721e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452e0edb-63ef-4a54-902c-874bcdd022a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082632700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7dbdf9-3228-43e6-b262-5def0cb9db81", "name": "entry : default@PreCheckSyscap cost memory 0.0377197265625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a23d2c0-59cb-4423-80e0-cef0fc421d50", "name": "runTaskFromQueue task cost before running: 338 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082825600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c49748-5cb6-4760-b7af-762bee0ca0ee", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306082396900, "endTime": 314306082913600, "totalTime": 406900}, "additional": {"logType": "info", "children": [], "durationId": "a5853f8a-2773-49eb-b496-a3d1bc68cd5e"}}, {"head": {"id": "c5da59af-2cfb-44c2-a0a8-3bb6db821d92", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306096582500, "endTime": 314306098121300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ddde61e-eb0f-4be4-a5d5-6f1aa94254be", "logId": "09314c58-f7d6-4636-837f-0c3a94b1cfff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ddde61e-eb0f-4be4-a5d5-6f1aa94254be", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306085322600}, "additional": {"logType": "detail", "children": [], "durationId": "c5da59af-2cfb-44c2-a0a8-3bb6db821d92"}}, {"head": {"id": "321308d0-919d-4593-8dbd-8d3fe4dfcd28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306086075500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370c182a-c043-442a-a077-e092612798e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306086228000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c43c9a80-0c00-4e28-95c6-76a1c32396f1", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306096602800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71319a55-5b6e-4d17-80ad-8e8818e6254f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306096857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8642ade-910b-4dc6-b82f-a1450ac32899", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306097902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3dccec6-d9b3-4274-ad2d-7fd01bc23b16", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06714630126953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306098026400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09314c58-f7d6-4636-837f-0c3a94b1cfff", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306096582500, "endTime": 314306098121300}, "additional": {"logType": "info", "children": [], "durationId": "c5da59af-2cfb-44c2-a0a8-3bb6db821d92"}}, {"head": {"id": "b4cbad15-4f00-4a5b-8977-7f056051e485", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306102029200, "endTime": 314306103454700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "575d0c06-75e0-45d4-be55-6f68fc7f7be8", "logId": "1b4cb854-ad5f-402e-8eda-33cfb68c0620"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "575d0c06-75e0-45d4-be55-6f68fc7f7be8", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306100107800}, "additional": {"logType": "detail", "children": [], "durationId": "b4cbad15-4f00-4a5b-8977-7f056051e485"}}, {"head": {"id": "105da00e-55c7-4027-b877-bf87f084bda9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306100680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54fd06c6-ec75-4133-a201-8d16cc857898", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306100824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312a15d8-4ddd-4352-b8b6-f0a41ab64db9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306102039000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd23b3c-15c7-4853-ad42-28ba2af0207b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306103263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af48b1b3-bb6e-43c6-98bf-21dfaeba748d", "name": "entry : default@ProcessProfile cost memory 0.05908966064453125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306103379200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4cb854-ad5f-402e-8eda-33cfb68c0620", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306102029200, "endTime": 314306103454700}, "additional": {"logType": "info", "children": [], "durationId": "b4cbad15-4f00-4a5b-8977-7f056051e485"}}, {"head": {"id": "63219dce-7759-4b7d-92c3-c3f1382295d0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306108486900, "endTime": 314306116130700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "61dd35de-f6ab-4da4-87c4-bdf9ea130ac3", "logId": "bfc3f1b8-e0f2-46f8-bbc6-ff2734bbee22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61dd35de-f6ab-4da4-87c4-bdf9ea130ac3", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306105574400}, "additional": {"logType": "detail", "children": [], "durationId": "63219dce-7759-4b7d-92c3-c3f1382295d0"}}, {"head": {"id": "7d56547a-cf20-4f70-97bb-800b8f65f709", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306106247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf76f4e2-8ae2-44c7-9b67-9e7021b3d14b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306106379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64717411-7336-49ab-8ad7-4a00423e686a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306108503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54ca756-b837-46eb-ac74-889b142234b3", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306115897200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25928847-34f2-43ff-aeaa-74b79d793d2a", "name": "entry : default@ProcessRouterMap cost memory 0.2007598876953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306116043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc3f1b8-e0f2-46f8-bbc6-ff2734bbee22", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306108486900, "endTime": 314306116130700}, "additional": {"logType": "info", "children": [], "durationId": "63219dce-7759-4b7d-92c3-c3f1382295d0"}}, {"head": {"id": "3d31d917-0f34-44f5-a786-a6912fea8338", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306126947500, "endTime": 314306132349200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d3c1976a-0c90-460c-b600-92d6c1c52469", "logId": "0c372cbb-dcc9-4e16-82f7-c20a5b491783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3c1976a-0c90-460c-b600-92d6c1c52469", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306120441900}, "additional": {"logType": "detail", "children": [], "durationId": "3d31d917-0f34-44f5-a786-a6912fea8338"}}, {"head": {"id": "b4abc71e-82ed-4fdb-ac73-bb0579a1a776", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306121223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ea8554-1127-44a0-be1a-14a38acd2212", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306121372000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b1a6a9c-8bda-47c0-bed7-29415f2999c5", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306122854300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b2ba90-373d-4803-8b0d-1c9e1f69759c", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306129114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3930f5d-c2a6-4939-8055-9f4d401db926", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306129401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa50591-e0e9-4b8b-808d-f649540443e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306129520100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78333bf9-a87d-40f5-acc1-d2e4bda7bff9", "name": "entry : default@PreviewProcessResource cost memory 0.0688323974609375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306129704000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2933e3-229b-49c5-bc39-c1c126a6be67", "name": "runTaskFromQueue task cost before running: 387 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306132194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c372cbb-dcc9-4e16-82f7-c20a5b491783", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306126947500, "endTime": 314306132349200, "totalTime": 2906800}, "additional": {"logType": "info", "children": [], "durationId": "3d31d917-0f34-44f5-a786-a6912fea8338"}}, {"head": {"id": "865421c8-91d0-4943-ae3a-a30ee7d4e428", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306144273000, "endTime": 314306170045100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a69ff5bf-489c-4695-8cf9-9aa7365d1b24", "logId": "a8a16f56-43ba-483e-bf27-96fa1fbe39b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a69ff5bf-489c-4695-8cf9-9aa7365d1b24", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306138105800}, "additional": {"logType": "detail", "children": [], "durationId": "865421c8-91d0-4943-ae3a-a30ee7d4e428"}}, {"head": {"id": "7d6d7f6e-dbb3-42de-bdfc-d534dbbe3e61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306138847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69415682-75c1-480c-804e-0983d2c6550b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306138992600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1e5387-adb5-4df1-9f44-c38adf5b541f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306144290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd92aac-55ac-4f0a-b9f2-8562490a5459", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306169749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167585d6-e2b3-4ea5-bd63-2bfd6dca13f8", "name": "entry : default@GenerateLoaderJson cost memory 0.72576904296875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306169900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a16f56-43ba-483e-bf27-96fa1fbe39b4", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306144273000, "endTime": 314306170045100}, "additional": {"logType": "info", "children": [], "durationId": "865421c8-91d0-4943-ae3a-a30ee7d4e428"}}, {"head": {"id": "f78ce5f1-73e2-46ca-b6e0-ed65b0cda473", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306183680400, "endTime": 314306209514900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "fc7b8eea-4e2c-447d-aa32-c753069aa640", "logId": "ba9670eb-a8cc-465c-bcf8-5fdbb56236bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc7b8eea-4e2c-447d-aa32-c753069aa640", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306179412900}, "additional": {"logType": "detail", "children": [], "durationId": "f78ce5f1-73e2-46ca-b6e0-ed65b0cda473"}}, {"head": {"id": "52867f71-87a5-44f2-bcd4-f65e991a2f51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306180083400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be07e329-4442-4f9d-a666-76d17a224469", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306180209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b416d77c-ada8-4f6c-a3b4-dfd0168be3bd", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306181303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a83956c-f471-4bda-a066-436818422b89", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306183706100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf7283e9-cc2a-43c1-92f7-365e1b3a1e91", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306209161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18775ba5-e2f4-4cfe-916f-44f435da13c5", "name": "entry : default@PreviewCompileResource cost memory 0.7784881591796875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306209367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba9670eb-a8cc-465c-bcf8-5fdbb56236bf", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306183680400, "endTime": 314306209514900}, "additional": {"logType": "info", "children": [], "durationId": "f78ce5f1-73e2-46ca-b6e0-ed65b0cda473"}}, {"head": {"id": "ee50430d-667f-4ba5-8556-835b4b17de22", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214072300, "endTime": 314306214570000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "dede7779-12c3-427b-bc71-ca52c3d0316d", "logId": "35102877-cbfa-46d2-8a16-9a0010353986"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dede7779-12c3-427b-bc71-ca52c3d0316d", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306213030600}, "additional": {"logType": "detail", "children": [], "durationId": "ee50430d-667f-4ba5-8556-835b4b17de22"}}, {"head": {"id": "8518f402-e00b-4fec-8a47-41bcc363dee7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306213801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba66347-b57f-40cf-9b69-5ecf2667878e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306213950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57afd3f3-12d6-4289-8db5-d395b146551b", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214083300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a308f79-ee48-45d4-abee-27018e75c06d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214208200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2704905-a313-4261-94c0-84fc71caae4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214280100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca74501c-a23f-4e82-beea-5d5a5f3edb31", "name": "entry : default@PreviewHookCompileResource cost memory 0.038970947265625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214387900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa67848c-a8a0-431d-b989-5d4238bc3bbb", "name": "runTaskFromQueue task cost before running: 469 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35102877-cbfa-46d2-8a16-9a0010353986", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306214072300, "endTime": 314306214570000, "totalTime": 396400}, "additional": {"logType": "info", "children": [], "durationId": "ee50430d-667f-4ba5-8556-835b4b17de22"}}, {"head": {"id": "ff6249b5-7c89-440d-bc03-d569f66698f0", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306219016800, "endTime": 314306242987700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c78fe976-fff9-4087-8e2f-d23e6c410374", "logId": "1dc5c079-044e-420e-8f23-91d94456a1d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c78fe976-fff9-4087-8e2f-d23e6c410374", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306217294900}, "additional": {"logType": "detail", "children": [], "durationId": "ff6249b5-7c89-440d-bc03-d569f66698f0"}}, {"head": {"id": "ded9b35a-7a38-4918-9c29-4790d53ee3c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306218047900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5146a09-b172-4d4f-a041-d628a254593b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306218198400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc4ea95-24cb-4e14-997f-58c078dc1f02", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306219031100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342291ab-e72e-4025-afaf-677b85cdb7d7", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306242736700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a507e39e-0857-4a00-b94a-85d46e384058", "name": "entry : default@CopyPreviewProfile cost memory 0.1017608642578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306242899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc5c079-044e-420e-8f23-91d94456a1d8", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306219016800, "endTime": 314306242987700}, "additional": {"logType": "info", "children": [], "durationId": "ff6249b5-7c89-440d-bc03-d569f66698f0"}}, {"head": {"id": "811a8672-705e-4cf5-a0c5-0c239a4959da", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306249582000, "endTime": 314306250421800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "de82567f-6230-4e34-9cd1-7bb2e0c79aa3", "logId": "7dbb2610-e89f-4670-bc6b-38c67a1a9796"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de82567f-6230-4e34-9cd1-7bb2e0c79aa3", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306247367200}, "additional": {"logType": "detail", "children": [], "durationId": "811a8672-705e-4cf5-a0c5-0c239a4959da"}}, {"head": {"id": "e77da9e6-3297-4f17-9ed4-e8bd6243bf43", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306248202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564156c9-6d56-4241-889c-1b2a74f2c512", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306248372000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2fde2a4-981d-4d3f-bdf9-ccab6679a2c3", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306249596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb324375-baac-4850-8926-dbd510817530", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306249826900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2036356-d6d2-441d-913f-f516d15036ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306249958400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c212e25c-eaa8-40b2-bb7d-f167a8cae6e3", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306250145800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1b08e8-bb0f-45bc-9975-87b4658b719a", "name": "runTaskFromQueue task cost before running: 505 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306250308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dbb2610-e89f-4670-bc6b-38c67a1a9796", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306249582000, "endTime": 314306250421800, "totalTime": 690800}, "additional": {"logType": "info", "children": [], "durationId": "811a8672-705e-4cf5-a0c5-0c239a4959da"}}, {"head": {"id": "9a0283e3-93ea-4194-a8cd-2322522bd464", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254141800, "endTime": 314306254829200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0c426763-f476-4da8-9e99-8b43aa00eccb", "logId": "0752b04f-8467-4426-92b5-5c3b5840a5dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c426763-f476-4da8-9e99-8b43aa00eccb", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254042700}, "additional": {"logType": "detail", "children": [], "durationId": "9a0283e3-93ea-4194-a8cd-2322522bd464"}}, {"head": {"id": "8a95bb8a-be73-4434-96ee-69458f8a73fb", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75008213-e075-4d5f-9af1-75918acd00e0", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254411800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49558219-c2a9-40b0-a1d9-70303a775cfe", "name": "runTaskFromQueue task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0752b04f-8467-4426-92b5-5c3b5840a5dd", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306254141800, "endTime": 314306254829200, "totalTime": 407900}, "additional": {"logType": "info", "children": [], "durationId": "9a0283e3-93ea-4194-a8cd-2322522bd464"}}, {"head": {"id": "c17ae7fd-250d-406b-bb48-247af96c91d8", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306260553400, "endTime": 314306264201600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "edb37a67-6d49-40e1-af59-e8c627498dc3", "logId": "29da1aa4-4281-47d0-b749-eece07496196"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edb37a67-6d49-40e1-af59-e8c627498dc3", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306258752900}, "additional": {"logType": "detail", "children": [], "durationId": "c17ae7fd-250d-406b-bb48-247af96c91d8"}}, {"head": {"id": "8eb6be95-4ec9-4cc9-905f-21604ad6574f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306259425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710b013b-a83f-4c18-8a64-94e5c072dfdf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306259554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91aeca3e-783c-43f9-8578-a686a212b288", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306260567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df3cc10b-79ce-4203-8fb8-fa2979286057", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306263693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f85dbe1-a5cd-4aad-9c60-deb3c086a5ff", "name": "entry : default@PreviewUpdateAssets cost memory 0.1063079833984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306264024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29da1aa4-4281-47d0-b749-eece07496196", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306260553400, "endTime": 314306264201600}, "additional": {"logType": "info", "children": [], "durationId": "c17ae7fd-250d-406b-bb48-247af96c91d8"}}, {"head": {"id": "9f799dff-51bc-413d-ae97-7a9589b01a1f", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306279196900, "endTime": 314350660666900}, "additional": {"children": ["69b7a594-0ceb-4470-8e58-266ad23d39a3"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8e72048d-3911-4d23-880b-aa63e866b2ec", "logId": "2d07bb84-780d-48ec-8a16-166707d5b756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e72048d-3911-4d23-880b-aa63e866b2ec", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306269384500}, "additional": {"logType": "detail", "children": [], "durationId": "9f799dff-51bc-413d-ae97-7a9589b01a1f"}}, {"head": {"id": "f6e028ed-0e42-4278-b8c8-8e79481f5c73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306270306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab43f292-9b92-481d-a33e-c6a5a597b3e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306270470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5362252-5f68-4fc1-bd47-002ef1ed7b39", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306279220000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker3", "startTime": 314306310457800, "endTime": 314350660345300}, "additional": {"children": ["4eddef94-bf14-4e09-949d-b9f74a917a46", "46da44aa-e541-4366-bea2-49cd7baf9133", "d43a3f21-7928-44e4-ab79-ce594924cba6", "8259c046-e49b-4c89-9a55-d74187cb2d0e"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9f799dff-51bc-413d-ae97-7a9589b01a1f", "logId": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de54eb5f-3787-424d-ab81-21a1ba05c76f", "name": "entry : default@PreviewArkTS cost memory 0.8968887329101562", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306314168100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7fc9da-0453-4764-ab54-1dc0ee518198", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331760738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eddef94-bf14-4e09-949d-b9f74a917a46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331762067500, "endTime": 314331762090200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "logId": "1b5b84b4-0f39-47af-bbf9-b8960f2e6165"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b5b84b4-0f39-47af-bbf9-b8960f2e6165", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331762067500, "endTime": 314331762090200}, "additional": {"logType": "info", "children": [], "durationId": "4eddef94-bf14-4e09-949d-b9f74a917a46", "parent": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312"}}, {"head": {"id": "8d45e802-a8de-47ef-b270-5aea1dab3af7", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331762208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69a2f6c5-a759-4bc0-bb9b-e75b296e99f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331763716800, "endTime": 314331763743900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1254ed12-0e2a-466e-930d-797917f53a56", "logId": "6286b6f6-6958-40ee-8208-f38d10f67236"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6286b6f6-6958-40ee-8208-f38d10f67236", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314331763716800, "endTime": 314331763743900}, "additional": {"logType": "info", "children": [], "durationId": "69a2f6c5-a759-4bc0-bb9b-e75b296e99f7"}}, {"head": {"id": "02e72e8e-751d-4a4c-b48d-1e374e5d5b22", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342469762400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b78872e-3efd-4b14-806c-e567525de73d", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342470356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49dc7be5-66be-4ac7-92b7-99562d0af122", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342596427300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46da44aa-e541-4366-bea2-49cd7baf9133", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342597660700, "endTime": 314342597685500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "logId": "9c535a44-aa9c-4443-945e-65d6e5977c49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c535a44-aa9c-4443-945e-65d6e5977c49", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342597660700, "endTime": 314342597685500}, "additional": {"logType": "info", "children": [], "durationId": "46da44aa-e541-4366-bea2-49cd7baf9133", "parent": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312"}}, {"head": {"id": "818140e7-38e4-4408-8163-fdd4b1334508", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342879153900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43a3f21-7928-44e4-ab79-ce594924cba6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342882791000, "endTime": 314342882836900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "logId": "d2d60522-2cab-4191-a56a-f444c0201d06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2d60522-2cab-4191-a56a-f444c0201d06", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314342882791000, "endTime": 314342882836900}, "additional": {"logType": "info", "children": [], "durationId": "d43a3f21-7928-44e4-ab79-ce594924cba6", "parent": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312"}}, {"head": {"id": "7019dd00-def0-4744-ba7b-4a19398e6846", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350659130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8259c046-e49b-4c89-9a55-d74187cb2d0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350660233200, "endTime": 314350660249100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "logId": "9b87f4b9-4375-420f-ade3-c8533107ab7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b87f4b9-4375-420f-ade3-c8533107ab7d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350660233200, "endTime": 314350660249100}, "additional": {"logType": "info", "children": [], "durationId": "8259c046-e49b-4c89-9a55-d74187cb2d0e", "parent": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312"}}, {"head": {"id": "0b3cd9f4-d816-4ca1-a9e5-0be92509c312", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker3", "startTime": 314306310457800, "endTime": 314350660345300}, "additional": {"logType": "error", "children": ["1b5b84b4-0f39-47af-bbf9-b8960f2e6165", "9c535a44-aa9c-4443-945e-65d6e5977c49", "d2d60522-2cab-4191-a56a-f444c0201d06", "9b87f4b9-4375-420f-ade3-c8533107ab7d"], "durationId": "69b7a594-0ceb-4470-8e58-266ad23d39a3", "parent": "2d07bb84-780d-48ec-8a16-166707d5b756"}}, {"head": {"id": "54546433-8fae-4245-8fd4-f705ca1431fb", "name": "default@PreviewArkTS watch work[3] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350660383900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d07bb84-780d-48ec-8a16-166707d5b756", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314306279196900, "endTime": 314350660666900}, "additional": {"logType": "error", "children": ["0b3cd9f4-d816-4ca1-a9e5-0be92509c312"], "durationId": "9f799dff-51bc-413d-ae97-7a9589b01a1f"}}, {"head": {"id": "7ee8b4d2-74f6-4063-8401-d6723206105e", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350660920100}, "additional": {"logType": "debug", "children": [], "durationId": "9f799dff-51bc-413d-ae97-7a9589b01a1f"}}, {"head": {"id": "b1ba257b-7ca6-47f7-b9a7-8f59d2b4150b", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/Index.ets:61:16\n Unknown resource name 'app_icon'.\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/LoginPage.ets:33:20\n The input parameter is not supported.\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350693888200}, "additional": {"logType": "debug", "children": [], "durationId": "9f799dff-51bc-413d-ae97-7a9589b01a1f"}}, {"head": {"id": "60fb65fc-5707-4e63-9b38-a51d5702c19e", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350707194100, "endTime": 314350707653300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51c0b8f6-e66b-4a4c-aed8-2cd51bf6082b", "logId": "a378ade1-022c-4453-9259-e0e3be6d4c68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a378ade1-022c-4453-9259-e0e3be6d4c68", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350707194100, "endTime": 314350707653300}, "additional": {"logType": "info", "children": [], "durationId": "60fb65fc-5707-4e63-9b38-a51d5702c19e"}}, {"head": {"id": "01a60cf3-f459-4a3e-a1cd-0887934e959f", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314305745701100, "endTime": 314350708008200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 6}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "03c02f6d-864c-4f28-b2c3-7bade5ee8fed", "name": "BUILD FAILED in 44 s 963 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350708067200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "2cc21aca-2521-4634-8284-5a4bcbb3525e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350708485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d0c804-d39d-4ffa-b278-def10dd07afe", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350708665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a35a584e-a59e-48ba-bb85-dcf5926e2dd2", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350708809800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280b8fb6-1c4b-4bba-937c-d9641ea90d3a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350708948600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30997512-e900-40a2-a5f9-478f50308989", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3764f7-cb3c-4066-aa9b-6ed64e0f05f9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c77907-cc4c-441b-a2d5-1294015e2c56", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709331300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d9e593-4c06-4dbd-807d-07bf8b4d1076", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b789b0-8857-429f-ab48-c2b86fe900b2", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709627600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b6ff71-ccd9-4b99-bf5d-f9dccad898ce", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350709757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c10a1f7-042a-49b0-bf26-3547cf7350d3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350719135100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df6b904-8513-430c-a17a-5ecdc37c3aaf", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350721438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c5b77ea-afc3-4fc7-9c7e-52a24407d8ad", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350722255700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35dd1e08-5dae-4aff-aed3-5cceacb1c150", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350723042800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364c1d47-3466-4a4c-bdf2-dad98d6ced32", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350725722500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f493c2-373c-499a-a37d-e01c8e993700", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350743892400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66e17c6-af15-4c66-9aa9-a9058dbccce9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350744337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ab66b3-6189-4683-964d-9149ebd126dd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350744732700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "225bc59c-a220-4292-b813-b7909753f0e2", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350745182300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cae5269-db7d-4ad0-bf49-20f43aef1aa2", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:36 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314350745639700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}