import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { useUserStore } from './stores/user.js'

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue全局错误:', err)
  console.error('错误信息:', info)
  console.error('组件实例:', vm)

  // 忽略特定的函数未找到错误
  const ignoredErrors = ['handleSchedule', 'handleWithdraw', 'handleRecharge', 'handleTransfer']
  const shouldIgnore = ignoredErrors.some(fnName =>
    err.message && err.message.includes(fnName)
  )

  if (shouldIgnore) {
    console.warn('忽略函数未找到错误:', err.message)
    return
  }
}

// 全局未捕获错误处理
window.addEventListener('error', (event) => {
  const ignoredErrors = ['handleSchedule', 'handleWithdraw', 'handleRecharge', 'handleTransfer']
  const shouldIgnore = ignoredErrors.some(fnName =>
    event.message && event.message.includes(fnName)
  )

  if (shouldIgnore) {
    console.warn('忽略全局错误:', event.message)
    event.preventDefault()
    return false
  }
})

// 全局Promise拒绝处理
window.addEventListener('unhandledrejection', (event) => {
  const ignoredErrors = ['handleSchedule', 'handleWithdraw', 'handleRecharge', 'handleTransfer']
  const shouldIgnore = ignoredErrors.some(fnName =>
    event.reason && event.reason.toString().includes(fnName)
  )

  if (shouldIgnore) {
    console.warn('忽略Promise拒绝:', event.reason)
    event.preventDefault()
    return false
  }
})

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 应用启动后恢复用户信息
app.mount('#app')

// 恢复用户登录状态
const userStore = useUserStore()
userStore.restoreFromStorage()