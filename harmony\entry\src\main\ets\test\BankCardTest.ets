import { BankCard, BankCardType, BankCardStatus } from '../common/types/index';

/**
 * 银行卡功能测试
 */
export class BankCardTest {
  
  /**
   * 测试银行卡数据创建
   */
  static testCreateBankCard(): BankCard {
    const testCard: BankCard = {
      cardId: 1,
      userId: 1,
      cardNo: '****************',
      cardType: BankCardType.DEBIT,
      bankName: '工商银行',
      holderName: '张三',
      isBound: BankCardStatus.BOUND,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00'
    };
    
    console.log('测试银行卡创建成功:', JSON.stringify(testCard));
    return testCard;
  }

  /**
   * 测试银行卡更新
   */
  static testUpdateBankCard(originalCard: BankCard, updates: Partial<BankCard>): BankCard {
    const updatedCard = Object.assign({}, originalCard, updates);
    console.log('测试银行卡更新成功:', JSON.stringify(updatedCard));
    return updatedCard;
  }

  /**
   * 测试银行卡列表操作
   */
  static testBankCardList(): BankCard[] {
    const cardList: BankCard[] = [
      {
        cardId: 1,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.DEBIT,
        bankName: '工商银行',
        holderName: '张三',
        isBound: BankCardStatus.BOUND,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00'
      },
      {
        cardId: 2,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.CREDIT,
        bankName: '中国银行',
        holderName: '张三',
        isBound: BankCardStatus.UNBOUND,
        createTime: '2024-01-02 10:00:00',
        updateTime: '2024-01-02 10:00:00'
      }
    ];

    console.log('测试银行卡列表创建成功，数量:', cardList.length);
    return cardList;
  }

  /**
   * 测试错误处理
   */
  static testErrorHandling(): void {
    try {
      throw new Error('测试错误');
    } catch (error: Error) {
      console.error('错误处理测试:', error.message);
    }
  }

  /**
   * 运行所有测试
   */
  static runAllTests(): void {
    console.log('开始银行卡功能测试...');
    
    // 测试创建
    const testCard = this.testCreateBankCard();
    
    // 测试更新
    const updatedCard = this.testUpdateBankCard(testCard, {
      bankName: '建设银行',
      updateTime: new Date().toISOString()
    });
    
    // 测试列表
    const cardList = this.testBankCardList();
    
    // 测试错误处理
    this.testErrorHandling();
    
    console.log('银行卡功能测试完成！');
  }
}
