// 交易记录存储工具
export class TransactionStorage {
  static STORAGE_KEY = 'wallet_transactions'

  // 获取所有交易记录
  static getAllTransactions() {
    try {
      const transactions = localStorage.getItem(this.STORAGE_KEY)
      return transactions ? JSON.parse(transactions) : []
    } catch (error) {
      console.error('获取交易记录失败:', error)
      return []
    }
  }

  // 添加新的交易记录
  static addTransaction(transaction) {
    try {
      const transactions = this.getAllTransactions()
      
      // 生成唯一ID
      const newTransaction = {
        id: this.generateTransactionId(transaction.transactionType),
        ...transaction,
        transactionTime: new Date().toISOString(),
        status: 'SUCCESS'
      }
      
      // 添加到数组开头（最新的在前面）
      transactions.unshift(newTransaction)
      
      // 限制最大记录数量（保留最近1000条）
      if (transactions.length > 1000) {
        transactions.splice(1000)
      }
      
      // 保存到localStorage
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(transactions))
      
      console.log('交易记录已保存:', newTransaction)
      return newTransaction
    } catch (error) {
      console.error('保存交易记录失败:', error)
      return null
    }
  }

  // 生成交易ID
  static generateTransactionId(type) {
    const prefix = {
      'PAYMENT': 'P',
      'DEPOSIT': 'D',
      'WITHDRAW': 'W',
      'TRANSFER': 'T',
      'RECEIVE': 'R'
    }
    
    const typePrefix = prefix[type] || 'T'
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    
    return `${typePrefix}${timestamp}${random}`
  }

  // 根据类型筛选交易记录
  static getTransactionsByType(type) {
    const allTransactions = this.getAllTransactions()
    return allTransactions.filter(transaction => transaction.transactionType === type)
  }

  // 删除指定ID的交易记录
  static deleteTransaction(transactionId) {
    try {
      console.log('开始删除交易记录:', transactionId)

      const transactions = this.getAllTransactions()
      console.log('当前交易记录数量:', transactions.length)

      const initialLength = transactions.length

      // 过滤掉要删除的交易记录
      const filteredTransactions = transactions.filter(transaction => {
        const shouldKeep = transaction.id !== transactionId
        if (!shouldKeep) {
          console.log('找到要删除的交易记录:', transaction)
        }
        return shouldKeep
      })

      console.log('过滤后交易记录数量:', filteredTransactions.length)

      if (filteredTransactions.length === initialLength) {
        console.warn('未找到要删除的交易记录:', transactionId)
        console.log('现有交易记录ID列表:', transactions.map(t => t.id))
        return false
      }

      // 保存更新后的交易记录
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredTransactions))

      console.log('交易记录删除成功:', transactionId)
      console.log('删除后剩余记录数量:', filteredTransactions.length)
      return true
    } catch (error) {
      console.error('删除交易记录失败:', error)
      return false
    }
  }

  // 清除所有交易记录（用于测试）
  static clearAllTransactions() {
    localStorage.removeItem(this.STORAGE_KEY)
    console.log('所有交易记录已清除')
  }

  // 调试工具：打印所有交易记录
  static debugPrintAllTransactions() {
    const transactions = this.getAllTransactions()
    console.log('=== 所有交易记录 ===')
    console.log('总数量:', transactions.length)
    transactions.forEach((transaction, index) => {
      console.log(`${index + 1}. ID: ${transaction.id}, 类型: ${transaction.transactionType}, 金额: ${transaction.amount}, 商品: ${transaction.targetAccount}`)
    })
    console.log('==================')
    return transactions
  }

  // 调试工具：检查交易记录是否存在
  static debugCheckTransaction(transactionId) {
    const transactions = this.getAllTransactions()
    const found = transactions.find(t => t.id === transactionId)
    console.log(`检查交易记录 ${transactionId}:`, found ? '存在' : '不存在')
    if (found) {
      console.log('交易详情:', found)
    }
    return found
  }

  // 初始化模拟数据（如果没有数据的话）
  static initMockData() {
    const existingTransactions = this.getAllTransactions()
    if (existingTransactions.length === 0) {
      const mockTransactions = [
        {
          id: 'P001',
          transactionType: 'PAYMENT',
          amount: 38.50,
          targetAccount: '星巴克咖啡',
          paymentMethod: 'WALLET',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - ********).toISOString(),
          status: 'SUCCESS'
        },
        {
          id: 'D001',
          transactionType: 'DEPOSIT',
          amount: 500.00,
          targetAccount: '银行卡充值',
          paymentMethod: 'BANK_CARD',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - *********).toISOString(),
          status: 'SUCCESS'
        },
        {
          id: 'W001',
          transactionType: 'WITHDRAW',
          amount: 200.00,
          targetAccount: '银行卡提现',
          paymentMethod: 'BANK_CARD',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - *********).toISOString(),
          status: 'SUCCESS'
        },
        {
          id: 'T001',
          transactionType: 'TRANSFER',
          amount: 150.00,
          targetAccount: '朋友转账',
          paymentMethod: 'WALLET',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - *********).toISOString(),
          status: 'SUCCESS'
        },
        {
          id: 'T002',
          transactionType: 'TRANSFER',
          amount: 80.00,
          targetAccount: '家人转账',
          paymentMethod: 'BANK_CARD',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - *********).toISOString(),
          status: 'SUCCESS'
        },
        {
          id: 'R001',
          transactionType: 'RECEIVE',
          amount: 300.00,
          targetAccount: '朋友转入',
          paymentMethod: 'WALLET',
          paymentChannel: 'MERCHANT',
          transactionTime: new Date(Date.now() - *********).toISOString(),
          status: 'SUCCESS'
        }
      ]
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mockTransactions))
      console.log('模拟交易数据已初始化')
    }
  }
}

// 支付方法映射
export const PAYMENT_METHODS = {
  WALLET: '钱包',
  BANK_CARD: '银行卡',
  QR_CODE: '扫码',
  NFC: 'NFC'
}

// 支付渠道映射
export const PAYMENT_CHANNELS = {
  MERCHANT: '商户',
  QR_CODE: '扫码',
  NFC: 'NFC'
}

// 交易类型映射
export const TRANSACTION_TYPES = {
  PAYMENT: '支付',
  DEPOSIT: '充值',
  WITHDRAW: '提现',
  TRANSFER: '转账',
  RECEIVE: '收款'
}
