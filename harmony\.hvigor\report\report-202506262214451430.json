{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "d31dffa3-020f-4f59-97d3-407cdb0cb3b7", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22119700789000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d0d5aea-a31e-48c6-94f3-3711c2c6c902", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22899620476400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bfcc72c-7c8d-432b-8ea6-c8d61edc6047", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22902437214800, "endTime": 22902677879800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "0c5a09f3-0d2f-44ac-9fe3-bd19b8758be4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c5a09f3-0d2f-44ac-9fe3-bd19b8758be4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22902437214800, "endTime": 22902677879800}, "additional": {"logType": "info", "children": [], "durationId": "3bfcc72c-7c8d-432b-8ea6-c8d61edc6047"}}, {"head": {"id": "92217f46-7cb3-43b3-9f17-9355bda0b5fb", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22902781235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fce85dd-8083-4812-87e3-80238f33cede", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22902782233400, "endTime": 22902782260300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "10a6c947-c258-4028-afc8-b613e1ad5970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10a6c947-c258-4028-afc8-b613e1ad5970", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22902782233400, "endTime": 22902782260300}, "additional": {"logType": "info", "children": [], "durationId": "8fce85dd-8083-4812-87e3-80238f33cede"}}, {"head": {"id": "ba5a151b-fed9-44bf-8b29-1cf3a9d7e156", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923527240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e8c3ca-4da8-4ce3-95ea-a858114e5e1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923597457800, "endTime": 22923597498000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "ec5aa0a1-2d1c-448c-aa80-ec5a10df7695"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec5aa0a1-2d1c-448c-aa80-ec5a10df7695", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923597457800, "endTime": 22923597498000}, "additional": {"logType": "info", "children": [], "durationId": "14e8c3ca-4da8-4ce3-95ea-a858114e5e1e"}}, {"head": {"id": "2b830e81-01d7-4e6c-903b-8de339bb0294", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923597674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f20ac85-9c95-4a7b-83dd-daf34185a5d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923599499100, "endTime": 22923599535000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "30360b4b-106f-4cbd-a0a4-7171c2abc192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30360b4b-106f-4cbd-a0a4-7171c2abc192", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923599499100, "endTime": 22923599535000}, "additional": {"logType": "info", "children": [], "durationId": "9f20ac85-9c95-4a7b-83dd-daf34185a5d8"}}, {"head": {"id": "a61d3dc7-638d-478b-85ea-a3a25977b19f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923599684000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c8f71c-98f6-49a0-b8d8-ef3067b22a20", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923647580400, "endTime": 22923647616500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "df72a592-4a96-40b9-a692-b686385d2f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df72a592-4a96-40b9-a692-b686385d2f5f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923647580400, "endTime": 22923647616500}, "additional": {"logType": "info", "children": [], "durationId": "14c8f71c-98f6-49a0-b8d8-ef3067b22a20"}}, {"head": {"id": "9891e69b-1532-436e-9641-0477d22c45b0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923647764900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e951b1fd-14b9-4df4-911b-17f0973afb19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923649231200, "endTime": 22923649280100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "978e623d-5edb-4f19-94b9-a89f87422287"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "978e623d-5edb-4f19-94b9-a89f87422287", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923649231200, "endTime": 22923649280100}, "additional": {"logType": "info", "children": [], "durationId": "e951b1fd-14b9-4df4-911b-17f0973afb19"}}, {"head": {"id": "73855f2c-f619-4b1b-a068-90a04864070f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923649426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca241be9-b2c4-4a4e-94e1-38459e100941", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923650977100, "endTime": 22923651013500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "d888d358-90e7-4002-8840-80acb93657ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d888d358-90e7-4002-8840-80acb93657ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923650977100, "endTime": 22923651013500}, "additional": {"logType": "info", "children": [], "durationId": "ca241be9-b2c4-4a4e-94e1-38459e100941"}}, {"head": {"id": "39ccf6c2-c9ea-4f9a-9c97-c077157feeb8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923651147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33fcbe5-8b11-4d76-ab04-8c3d34599d1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923652192400, "endTime": 22923652232700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "f9022486-5d7b-4ea7-a8a0-8ed6730d5c60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9022486-5d7b-4ea7-a8a0-8ed6730d5c60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923652192400, "endTime": 22923652232700}, "additional": {"logType": "info", "children": [], "durationId": "d33fcbe5-8b11-4d76-ab04-8c3d34599d1e"}}, {"head": {"id": "ba68f9c0-de65-4c47-be33-aa67c23899b1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923652357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed09951-6377-437b-9460-19a5065f0c12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923654294500, "endTime": 22923654349300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "279e35ce-1631-43b9-9168-565e09c8c9c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "279e35ce-1631-43b9-9168-565e09c8c9c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22923654294500, "endTime": 22923654349300}, "additional": {"logType": "info", "children": [], "durationId": "3ed09951-6377-437b-9460-19a5065f0c12"}}, {"head": {"id": "05625d34-35c3-4aae-8046-63bc97a52624", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927815106000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9701ce4a-4c74-4e5c-8541-e802fc804eac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927816189800, "endTime": 22927816209300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "1adf8094-ff64-4756-97ce-804035826092"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1adf8094-ff64-4756-97ce-804035826092", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927816189800, "endTime": 22927816209300}, "additional": {"logType": "info", "children": [], "durationId": "9701ce4a-4c74-4e5c-8541-e802fc804eac"}}, {"head": {"id": "df0b1a00-48cb-4714-adbf-80c2e7a7cbc2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927950914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115cd997-0430-4fc2-bb4d-0a8357007948", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927952070200, "endTime": 22927952089500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "9a2262de-2ab1-4f08-b792-75737945f526"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a2262de-2ab1-4f08-b792-75737945f526", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22927952070200, "endTime": 22927952089500}, "additional": {"logType": "info", "children": [], "durationId": "115cd997-0430-4fc2-bb4d-0a8357007948"}}, {"head": {"id": "a7be22ce-f5c0-4d48-bc8b-e808e0fc582b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928419439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d70bcd-5643-4bee-89fe-4680475adc29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928421852300, "endTime": 22928421943100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "ec98aaf8-7d56-4752-99bf-3775a86ada7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec98aaf8-7d56-4752-99bf-3775a86ada7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928421852300, "endTime": 22928421943100}, "additional": {"logType": "info", "children": [], "durationId": "a2d70bcd-5643-4bee-89fe-4680475adc29"}}, {"head": {"id": "f6bef308-ca2b-4bbe-abb0-a9043b6f2a31", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928588188500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4684c54f-2be4-443a-9843-69bc88fecd4b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928589714200, "endTime": 22928589748600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "f14f9d23-dc5f-4cae-af86-1e9708deb8c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f14f9d23-dc5f-4cae-af86-1e9708deb8c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928589714200, "endTime": 22928589748600}, "additional": {"logType": "info", "children": [], "durationId": "4684c54f-2be4-443a-9843-69bc88fecd4b"}}, {"head": {"id": "5191e87c-c22f-4df8-b2f4-d3dcc42d7a7e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928589969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05179c11-f880-47da-b5ce-888ccd08f260", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928591432400, "endTime": 22928591460100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "f716f2f4-c38a-4d74-92ba-c5169d3560ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f716f2f4-c38a-4d74-92ba-c5169d3560ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928591432400, "endTime": 22928591460100}, "additional": {"logType": "info", "children": [], "durationId": "05179c11-f880-47da-b5ce-888ccd08f260"}}, {"head": {"id": "a785c10d-e37f-4083-8e27-e00c607e6149", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928591570800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20aa4fc3-65e8-4f68-b43e-a16fbd9f41b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928638367900, "endTime": 22928638409900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "2e49de5e-b019-4342-9de3-9a32b81bf04d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e49de5e-b019-4342-9de3-9a32b81bf04d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928638367900, "endTime": 22928638409900}, "additional": {"logType": "info", "children": [], "durationId": "20aa4fc3-65e8-4f68-b43e-a16fbd9f41b2"}}, {"head": {"id": "2a920f8c-b370-426f-b422-8c69cf42a99f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928638771600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983e756a-74ca-4c02-9551-ade56f0fa03b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928641100300, "endTime": 22928641134200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "022b4f66-8ada-4728-a35b-1e98a66dc496"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "022b4f66-8ada-4728-a35b-1e98a66dc496", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928641100300, "endTime": 22928641134200}, "additional": {"logType": "info", "children": [], "durationId": "983e756a-74ca-4c02-9551-ade56f0fa03b"}}, {"head": {"id": "0890ddac-1af8-4c2a-872f-9dbc6d93acf3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928641312700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e78c0e5-53ae-45cb-983e-b81f87becb6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928643599800, "endTime": 22928643635900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "49310493-d7d1-4b91-8ec3-7dc4fd88cf38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49310493-d7d1-4b91-8ec3-7dc4fd88cf38", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928643599800, "endTime": 22928643635900}, "additional": {"logType": "info", "children": [], "durationId": "1e78c0e5-53ae-45cb-983e-b81f87becb6c"}}, {"head": {"id": "90187586-0cd0-42e7-a1af-9c35d5fb2343", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928643793400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee392fa0-a946-4927-ba3c-bb8dd041d4df", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928645433800, "endTime": 22928645466600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "887ed96d-b9b7-46c1-b59b-29c0a80d1fee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "887ed96d-b9b7-46c1-b59b-29c0a80d1fee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928645433800, "endTime": 22928645466600}, "additional": {"logType": "info", "children": [], "durationId": "ee392fa0-a946-4927-ba3c-bb8dd041d4df"}}, {"head": {"id": "7fd78438-b101-45ca-8b81-a2d609d19ceb", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928815966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4591ef9e-2a00-4138-b98d-473a5188556e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928817026300, "endTime": 22928817047200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "4adc2051-6c33-43bf-86e0-420752ccb3c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4adc2051-6c33-43bf-86e0-420752ccb3c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22928817026300, "endTime": 22928817047200}, "additional": {"logType": "info", "children": [], "durationId": "4591ef9e-2a00-4138-b98d-473a5188556e"}}, {"head": {"id": "60415459-5a15-49e3-b969-25486ad9a88d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933260906200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba1ceb67-f21c-4122-9219-bf87983e0fd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933263404700, "endTime": 22933263438600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "89af9bac-7a15-405e-b014-6474a074d1be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89af9bac-7a15-405e-b014-6474a074d1be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933263404700, "endTime": 22933263438600}, "additional": {"logType": "info", "children": [], "durationId": "ba1ceb67-f21c-4122-9219-bf87983e0fd1"}}, {"head": {"id": "76bd0677-e546-4dc3-ae74-de8959d773cf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933265642100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a732a6d6-3f8c-441b-96aa-f96a683adc79", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933269017400, "endTime": 22933269078900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "59d5b064-8af7-4d07-8e70-bb0cd8c28edc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59d5b064-8af7-4d07-8e70-bb0cd8c28edc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933269017400, "endTime": 22933269078900}, "additional": {"logType": "info", "children": [], "durationId": "a732a6d6-3f8c-441b-96aa-f96a683adc79"}}, {"head": {"id": "40274cea-afae-4659-b903-0fdd1dbac37a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933269314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4d4a4e-31fa-411e-81b0-1e29d5a0880f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933271182700, "endTime": 22933271212800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "31614fea-5ac5-40a6-8fcf-e6f6d5971ac7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31614fea-5ac5-40a6-8fcf-e6f6d5971ac7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933271182700, "endTime": 22933271212800}, "additional": {"logType": "info", "children": [], "durationId": "ec4d4a4e-31fa-411e-81b0-1e29d5a0880f"}}, {"head": {"id": "d3a0e3fd-9a7b-478d-967a-d68167f8c2e2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933271352400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7650c8b3-d8ec-47f8-8ee4-194445c6af8b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933272998700, "endTime": 22933273029400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5176d8a6-3381-4b95-a744-89ab339143a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5176d8a6-3381-4b95-a744-89ab339143a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933272998700, "endTime": 22933273029400}, "additional": {"logType": "info", "children": [], "durationId": "7650c8b3-d8ec-47f8-8ee4-194445c6af8b"}}, {"head": {"id": "e95256b3-fcdb-4623-9b23-230edd5f850e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933273147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72e93882-9d83-42a9-bd10-6fbc33ae3fef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933274682900, "endTime": 22933274708700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "df5a1dbe-2f06-4a44-82e6-73e5e926818d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df5a1dbe-2f06-4a44-82e6-73e5e926818d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933274682900, "endTime": 22933274708700}, "additional": {"logType": "info", "children": [], "durationId": "72e93882-9d83-42a9-bd10-6fbc33ae3fef"}}, {"head": {"id": "b37e4305-1b6c-40c2-b952-30f6fe9afd1f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933274830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304a2cd6-61d9-45a2-94f9-755d20135604", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933276224800, "endTime": 22933276248300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4f252481-c5f1-4b79-a2d4-7f921eafbe29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f252481-c5f1-4b79-a2d4-7f921eafbe29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933276224800, "endTime": 22933276248300}, "additional": {"logType": "info", "children": [], "durationId": "304a2cd6-61d9-45a2-94f9-755d20135604"}}, {"head": {"id": "1f19b703-b892-48b7-8703-b9505f585e0a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933276356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5109af7-e630-48f1-b0a3-104f0155cdee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933381380800, "endTime": 22933381403200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "eda45b46-437e-40b7-ae1b-b1e1e3c29365"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eda45b46-437e-40b7-ae1b-b1e1e3c29365", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22933381380800, "endTime": 22933381403200}, "additional": {"logType": "info", "children": [], "durationId": "a5109af7-e630-48f1-b0a3-104f0155cdee"}}, {"head": {"id": "b77fab5c-c54c-42af-94eb-f3e5e967b8ff", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942646102000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83004f7a-009c-49af-8381-f6ca72e771cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942647133300, "endTime": 22942647155100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "215bdd69-8995-4eaf-92c7-339333398291"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "215bdd69-8995-4eaf-92c7-339333398291", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942647133300, "endTime": 22942647155100}, "additional": {"logType": "info", "children": [], "durationId": "83004f7a-009c-49af-8381-f6ca72e771cb"}}, {"head": {"id": "b9035507-e234-4254-9568-02e5862212a7", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942746765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee68720-78f6-4df3-95f3-537c0b8bd249", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942748898400, "endTime": 22942748923600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "b9ff9130-24a6-47c9-b5b6-5e7814a2183f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9ff9130-24a6-47c9-b5b6-5e7814a2183f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22942748898400, "endTime": 22942748923600}, "additional": {"logType": "info", "children": [], "durationId": "fee68720-78f6-4df3-95f3-537c0b8bd249"}}, {"head": {"id": "4e7e6c76-fbd0-4230-8dc1-79625ccb4ef9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943187607700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fce56ba-d867-46f9-8ebd-0cd91ebb47ef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943188642300, "endTime": 22943188664100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2d807ecb-bea9-43eb-9713-d593021d41be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d807ecb-bea9-43eb-9713-d593021d41be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943188642300, "endTime": 22943188664100}, "additional": {"logType": "info", "children": [], "durationId": "5fce56ba-d867-46f9-8ebd-0cd91ebb47ef"}}, {"head": {"id": "cf7b1192-04a6-4a5c-ba4a-1816be61ee6c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943188765500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9599a6-39f2-4ced-b4fc-e2b12c28d3a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943189554000, "endTime": 22943189567700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ca449ea2-1338-4dd0-8d06-f288a44c16da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca449ea2-1338-4dd0-8d06-f288a44c16da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943189554000, "endTime": 22943189567700}, "additional": {"logType": "info", "children": [], "durationId": "5e9599a6-39f2-4ced-b4fc-e2b12c28d3a4"}}, {"head": {"id": "46b4828c-29e5-4303-ae56-a92117e2550e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943189634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab563c16-b505-4fd9-9a11-ef19e12aee53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943190663500, "endTime": 22943190687100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a4f1c058-7245-4d1c-b6b7-7e7878d563fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4f1c058-7245-4d1c-b6b7-7e7878d563fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943190663500, "endTime": 22943190687100}, "additional": {"logType": "info", "children": [], "durationId": "ab563c16-b505-4fd9-9a11-ef19e12aee53"}}, {"head": {"id": "3254c2ec-8906-4400-a429-540c6c432b35", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943190784800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be0b6e1-cba1-44e4-bf24-c42906caa8d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943191985600, "endTime": 22943192004000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "589ccfce-ae5d-4021-a2fe-da423a3239c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "589ccfce-ae5d-4021-a2fe-da423a3239c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943191985600, "endTime": 22943192004000}, "additional": {"logType": "info", "children": [], "durationId": "7be0b6e1-cba1-44e4-bf24-c42906caa8d8"}}, {"head": {"id": "b8fae466-b810-4d4a-842a-c09b58bb2168", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943192098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73866c87-def5-4698-a39e-948eb71d3133", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943193289800, "endTime": 22943193313100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ca7fc810-2df8-4165-8e8f-8f90ba9fb954"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca7fc810-2df8-4165-8e8f-8f90ba9fb954", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943193289800, "endTime": 22943193313100}, "additional": {"logType": "info", "children": [], "durationId": "73866c87-def5-4698-a39e-948eb71d3133"}}, {"head": {"id": "9b4d2cb3-7310-4a40-8938-73efe5d422fd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943193412500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f5258af-fe64-43f7-ad50-8efd9b1e1ef6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943194825300, "endTime": 22943194851200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0ef2cd04-c2e4-4916-9a77-6561a7b3abc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ef2cd04-c2e4-4916-9a77-6561a7b3abc0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943194825300, "endTime": 22943194851200}, "additional": {"logType": "info", "children": [], "durationId": "9f5258af-fe64-43f7-ad50-8efd9b1e1ef6"}}, {"head": {"id": "cb2a5744-d39b-4803-b61e-dcbb9b333a3b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943195045700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "896d3fed-d778-486f-a2b2-3e03ab3c57a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943196617800, "endTime": 22943196641700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9ca430a9-edf2-4cf7-a3ff-95f1871bc5bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ca430a9-edf2-4cf7-a3ff-95f1871bc5bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943196617800, "endTime": 22943196641700}, "additional": {"logType": "info", "children": [], "durationId": "896d3fed-d778-486f-a2b2-3e03ab3c57a0"}}, {"head": {"id": "4679d483-474d-4821-8c8f-c86c0512571c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943423805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8e11003-981c-44f1-9428-ca984a4151a1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943463715000, "endTime": 22943463764900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a44f4910-8a38-4662-9b0a-6b611b4b26f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a44f4910-8a38-4662-9b0a-6b611b4b26f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22943463715000, "endTime": 22943463764900}, "additional": {"logType": "info", "children": [], "durationId": "f8e11003-981c-44f1-9428-ca984a4151a1"}}, {"head": {"id": "4584b28d-11da-4dba-b81a-d1563b377bf3", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955046969600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b7483d-b5f2-4ebb-9352-fcf082c5a934", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955048133900, "endTime": 22955048154000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "755ef71c-57e6-497f-bb58-f476030362c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "755ef71c-57e6-497f-bb58-f476030362c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955048133900, "endTime": 22955048154000}, "additional": {"logType": "info", "children": [], "durationId": "15b7483d-b5f2-4ebb-9352-fcf082c5a934"}}, {"head": {"id": "08d5055f-5028-436b-8e83-cb6916389737", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955048236000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1301788e-3501-48f8-a933-f720129caa93", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955049438800, "endTime": 22955049460700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "98a19018-7d5e-4c6c-aea9-bcc7e157ad7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98a19018-7d5e-4c6c-aea9-bcc7e157ad7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22955049438800, "endTime": 22955049460700}, "additional": {"logType": "info", "children": [], "durationId": "1301788e-3501-48f8-a933-f720129caa93"}}, {"head": {"id": "e7756383-fab3-4581-910b-0350f189b394", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956346749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e17dac-2625-40a8-8325-3ec8495012ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956348539200, "endTime": 22956348575500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9d22388c-bcd6-4803-aa93-aca73452f5df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d22388c-bcd6-4803-aa93-aca73452f5df", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956348539200, "endTime": 22956348575500}, "additional": {"logType": "info", "children": [], "durationId": "59e17dac-2625-40a8-8325-3ec8495012ec"}}, {"head": {"id": "dba94eb5-07ef-4c65-9f19-185f5b899f5d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956348740900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e7dfb7e-1b04-40bc-a408-27f2fed18728", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956350426100, "endTime": 22956350461700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "cc300e54-1d91-4327-8902-c08b4c86007f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc300e54-1d91-4327-8902-c08b4c86007f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956350426100, "endTime": 22956350461700}, "additional": {"logType": "info", "children": [], "durationId": "2e7dfb7e-1b04-40bc-a408-27f2fed18728"}}, {"head": {"id": "258fb777-7274-48ae-ad93-208b76ba787a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956350613200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ae4ebf-da84-44bd-962b-2473cc97a51b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956352888500, "endTime": 22956352918900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "69d79913-2f43-4bb2-926e-3bd73cf8f4d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69d79913-2f43-4bb2-926e-3bd73cf8f4d2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956352888500, "endTime": 22956352918900}, "additional": {"logType": "info", "children": [], "durationId": "61ae4ebf-da84-44bd-962b-2473cc97a51b"}}, {"head": {"id": "011f4158-2bec-4000-ae01-b9f34a278169", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956353064400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4273a8c9-0f2a-4dc5-8f61-e106fe739a0d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956355133700, "endTime": 22956355168400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "642b34fc-3686-4fa1-b74a-2979f9dad859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "642b34fc-3686-4fa1-b74a-2979f9dad859", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956355133700, "endTime": 22956355168400}, "additional": {"logType": "info", "children": [], "durationId": "4273a8c9-0f2a-4dc5-8f61-e106fe739a0d"}}, {"head": {"id": "f7b99ed5-df92-48d9-abc7-621c686d5607", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956355385700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372bd7b6-eb08-4eac-b5cd-ec564f83b5c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956368056100, "endTime": 22956368091900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ae094ea6-7890-49a2-af44-6bbea076b197"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae094ea6-7890-49a2-af44-6bbea076b197", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956368056100, "endTime": 22956368091900}, "additional": {"logType": "info", "children": [], "durationId": "372bd7b6-eb08-4eac-b5cd-ec564f83b5c2"}}, {"head": {"id": "5aa87fb8-7970-445e-9fc5-1a2aa0ea55df", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956368284600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1f85d7a-6c3d-44f5-b7e3-4dd29138d7dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956370374100, "endTime": 22956370405300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4f1b60f9-dfcd-4bbe-92cb-030163e3eb66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f1b60f9-dfcd-4bbe-92cb-030163e3eb66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956370374100, "endTime": 22956370405300}, "additional": {"logType": "info", "children": [], "durationId": "f1f85d7a-6c3d-44f5-b7e3-4dd29138d7dd"}}, {"head": {"id": "167c4dd2-85a2-4d8e-88fb-213f728a02dc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956370561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad5225e-dbcd-4f2a-8a2b-dec626a8e17e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956372163900, "endTime": 22956372192900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "d7dd7ee5-74e8-4f07-9d70-ab601b027a4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7dd7ee5-74e8-4f07-9d70-ab601b027a4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956372163900, "endTime": 22956372192900}, "additional": {"logType": "info", "children": [], "durationId": "8ad5225e-dbcd-4f2a-8a2b-dec626a8e17e"}}, {"head": {"id": "ff5c4e2a-ff80-4cd7-a8b9-566040276543", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956615575200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ef81f1-c566-4005-bd2d-8739369595a1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956620555600, "endTime": 22956620586700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "fe1c089d-16f7-4d4a-83ea-a4b24171dd42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe1c089d-16f7-4d4a-83ea-a4b24171dd42", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956620555600, "endTime": 22956620586700}, "additional": {"logType": "info", "children": [], "durationId": "02ef81f1-c566-4005-bd2d-8739369595a1"}}, {"head": {"id": "0221b44b-1310-44ab-ba02-1b4c4affb218", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956620731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6738cb41-37e1-4a79-9626-84a632211478", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956645184200, "endTime": 22956645222700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "1e191b80-ff5e-4948-80a5-3411946b718e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e191b80-ff5e-4948-80a5-3411946b718e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956645184200, "endTime": 22956645222700}, "additional": {"logType": "info", "children": [], "durationId": "6738cb41-37e1-4a79-9626-84a632211478"}}, {"head": {"id": "09bb50e6-0d88-463a-a782-1d2ef172ac3a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956645367600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a8cf4c-c068-4a3a-92bf-fb4b445cf053", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956660570300, "endTime": 22956660620900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "cca64138-6104-4c37-b879-e5433b41fadf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cca64138-6104-4c37-b879-e5433b41fadf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956660570300, "endTime": 22956660620900}, "additional": {"logType": "info", "children": [], "durationId": "16a8cf4c-c068-4a3a-92bf-fb4b445cf053"}}, {"head": {"id": "c71aba22-3260-4e6c-a64c-8319038707c5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956660816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91e89e6-bf8a-4bc3-900e-35c809a9ab72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956662818100, "endTime": 22956662848900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "d28de9f8-119a-43cc-a048-875776bb802b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d28de9f8-119a-43cc-a048-875776bb802b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956662818100, "endTime": 22956662848900}, "additional": {"logType": "info", "children": [], "durationId": "a91e89e6-bf8a-4bc3-900e-35c809a9ab72"}}, {"head": {"id": "5c5922f8-ae58-4f7b-8f4c-c26831359cec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956663002300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28c4ac91-153a-4632-9bbd-81f385851c08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956664814800, "endTime": 22956664851100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "907ac751-2263-4f59-a92e-42d59a317bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "907ac751-2263-4f59-a92e-42d59a317bef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956664814800, "endTime": 22956664851100}, "additional": {"logType": "info", "children": [], "durationId": "28c4ac91-153a-4632-9bbd-81f385851c08"}}, {"head": {"id": "e01552e0-9341-458e-a05b-e144fd5c17f5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956665071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0fbb01c-df87-4e11-bf38-170b4046c6dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956667074700, "endTime": 22956667146900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "c92641c8-d982-4eaa-80cd-a081f9bbe7af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c92641c8-d982-4eaa-80cd-a081f9bbe7af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956667074700, "endTime": 22956667146900}, "additional": {"logType": "info", "children": [], "durationId": "c0fbb01c-df87-4e11-bf38-170b4046c6dd"}}, {"head": {"id": "bbff4729-9b46-4553-8a3f-22db7ece5690", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956667367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2d3427-ae4a-4c59-8d62-5be031b5d997", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956669577700, "endTime": 22956669608000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "b3481983-9340-4344-b108-f9e66642ff07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3481983-9340-4344-b108-f9e66642ff07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956669577700, "endTime": 22956669608000}, "additional": {"logType": "info", "children": [], "durationId": "7b2d3427-ae4a-4c59-8d62-5be031b5d997"}}, {"head": {"id": "98f54b21-0df1-4c1a-8474-d0994abac050", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956910439400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add28d26-c4e4-47ae-ae7d-d785b124a786", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956912963800, "endTime": 22956912998100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c38d4655-c6d1-4d85-a53d-4e013479d240"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c38d4655-c6d1-4d85-a53d-4e013479d240", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22956912963800, "endTime": 22956912998100}, "additional": {"logType": "info", "children": [], "durationId": "add28d26-c4e4-47ae-ae7d-d785b124a786"}}, {"head": {"id": "51068669-b07f-4707-8aa2-5ee2629af8bd", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22957164174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "792173de-f454-418e-9d44-5b1e1b28b980", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22957165811500, "endTime": 22957165843800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "79d6ce33-519f-437a-8c9c-da31a83ec136"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79d6ce33-519f-437a-8c9c-da31a83ec136", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22957165811500, "endTime": 22957165843800}, "additional": {"logType": "info", "children": [], "durationId": "792173de-f454-418e-9d44-5b1e1b28b980"}}, {"head": {"id": "39f61631-0487-42eb-8165-f785ab3c8977", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995244543300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d8f2446-ecd9-4187-9456-1f284763a12a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995247660900, "endTime": 22995247707400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7bc4ec73-08ca-4e8a-acea-5b279bf04da8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bc4ec73-08ca-4e8a-acea-5b279bf04da8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995247660900, "endTime": 22995247707400}, "additional": {"logType": "info", "children": [], "durationId": "4d8f2446-ecd9-4187-9456-1f284763a12a"}}, {"head": {"id": "c7ccff57-9a4e-4ce4-8078-3008349cf4b0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995247848000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f6d723a-cead-4d3e-a492-906a19d2bded", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995249558500, "endTime": 22995249587600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "1f4dcc33-0405-40ca-af79-52c1b40e7e19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f4dcc33-0405-40ca-af79-52c1b40e7e19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22995249558500, "endTime": 22995249587600}, "additional": {"logType": "info", "children": [], "durationId": "2f6d723a-cead-4d3e-a492-906a19d2bded"}}, {"head": {"id": "bba206b2-94ef-4247-a0f8-9cb7bda666d1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996611400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d87493-b215-4e92-9d3c-bac4ef780001", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996685087900, "endTime": 22996685123600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "28952249-e59b-43f3-b30a-2a121c079254"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28952249-e59b-43f3-b30a-2a121c079254", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996685087900, "endTime": 22996685123600}, "additional": {"logType": "info", "children": [], "durationId": "b2d87493-b215-4e92-9d3c-bac4ef780001"}}, {"head": {"id": "b9ffe4a7-d055-4582-b22d-39a7f48633e3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996685258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4dca120-ec3d-4517-b616-3c130a11138b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996686489500, "endTime": 22996686516000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "b5f1538e-ce84-4d81-b5ef-069acd46eedd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5f1538e-ce84-4d81-b5ef-069acd46eedd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996686489500, "endTime": 22996686516000}, "additional": {"logType": "info", "children": [], "durationId": "e4dca120-ec3d-4517-b616-3c130a11138b"}}, {"head": {"id": "28d2909a-545d-41dc-b509-f5d2f6c5dbf1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996686640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c92cb0-82a6-4e76-82a1-71ec04d3378a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996688485000, "endTime": 22996688521400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "989a3656-04c1-409c-855c-41ac6a08c901"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "989a3656-04c1-409c-855c-41ac6a08c901", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996688485000, "endTime": 22996688521400}, "additional": {"logType": "info", "children": [], "durationId": "b3c92cb0-82a6-4e76-82a1-71ec04d3378a"}}, {"head": {"id": "32a52665-535b-4f83-9ddf-68cc310c704c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996688678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b955fd9b-05c7-4562-92cd-52f06e3b3ffa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996690208300, "endTime": 22996690237500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "b21cc738-1aef-4f9e-a97f-3ec2e70eba0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b21cc738-1aef-4f9e-a97f-3ec2e70eba0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996690208300, "endTime": 22996690237500}, "additional": {"logType": "info", "children": [], "durationId": "b955fd9b-05c7-4562-92cd-52f06e3b3ffa"}}, {"head": {"id": "5ea2a22e-280e-4efe-ad9b-b879088e54cd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996690413500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb19d88-d86a-44ae-87c8-928dcc62d2b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996691959300, "endTime": 22996691989900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "92d2ab2c-e647-4355-bc74-97b5ecb890e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92d2ab2c-e647-4355-bc74-97b5ecb890e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996691959300, "endTime": 22996691989900}, "additional": {"logType": "info", "children": [], "durationId": "fbb19d88-d86a-44ae-87c8-928dcc62d2b6"}}, {"head": {"id": "d39e6895-1986-401f-bf2c-43c0be723f5e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996692163700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63dd6153-4c81-4e42-afff-fafd3c10812f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996694006200, "endTime": 22996694063200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "6225a960-21ae-41fb-b9be-b307cad4cea7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6225a960-21ae-41fb-b9be-b307cad4cea7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996694006200, "endTime": 22996694063200}, "additional": {"logType": "info", "children": [], "durationId": "63dd6153-4c81-4e42-afff-fafd3c10812f"}}, {"head": {"id": "272e64ac-c346-467d-8c74-457c3f42721e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996694236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e00305-5e87-4fea-ac81-7f7b8dda7ca8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996696471600, "endTime": 22996696514200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "e25bf01b-aa04-4faa-8e6e-67f5dcb740c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e25bf01b-aa04-4faa-8e6e-67f5dcb740c6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996696471600, "endTime": 22996696514200}, "additional": {"logType": "info", "children": [], "durationId": "50e00305-5e87-4fea-ac81-7f7b8dda7ca8"}}, {"head": {"id": "ca5e0550-8042-4366-98e4-1c0ed05d45bc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996770457400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a980c47-5b37-45ad-8dc5-f8a0a8143e38", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996771410300, "endTime": 22996771429000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ce12fa5d-6908-4e58-946a-31969c4da6c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce12fa5d-6908-4e58-946a-31969c4da6c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996771410300, "endTime": 22996771429000}, "additional": {"logType": "info", "children": [], "durationId": "0a980c47-5b37-45ad-8dc5-f8a0a8143e38"}}, {"head": {"id": "610bdbb1-11d8-4624-b174-a8ce287dcb20", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996771506600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25fb22ec-64e0-4d28-90e1-795ce5003d0c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996772293400, "endTime": 22996772308700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7d8a3895-04b8-4deb-81c1-42164103c729"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d8a3895-04b8-4deb-81c1-42164103c729", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996772293400, "endTime": 22996772308700}, "additional": {"logType": "info", "children": [], "durationId": "25fb22ec-64e0-4d28-90e1-795ce5003d0c"}}, {"head": {"id": "af98425d-0558-414a-871b-7550f6cc6e6d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996772373000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995f47e7-3cf7-4dc6-8459-c38c3f038e57", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996773719600, "endTime": 22996773748000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7229f53d-6de0-433b-a326-a0f1e8bdab47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7229f53d-6de0-433b-a326-a0f1e8bdab47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996773719600, "endTime": 22996773748000}, "additional": {"logType": "info", "children": [], "durationId": "995f47e7-3cf7-4dc6-8459-c38c3f038e57"}}, {"head": {"id": "367c2526-0dcf-4cbf-a3c9-dcbea4f2030c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996773930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4838b356-c30e-4077-a676-a80490576753", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996775611900, "endTime": 22996775640800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "b576b2a9-65a8-4e89-a226-6906eb254b1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b576b2a9-65a8-4e89-a226-6906eb254b1b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996775611900, "endTime": 22996775640800}, "additional": {"logType": "info", "children": [], "durationId": "4838b356-c30e-4077-a676-a80490576753"}}, {"head": {"id": "1f671283-4245-46fc-a31b-7a982677643d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996775805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18fc207d-4bff-4370-b839-2b871234678f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996778638400, "endTime": 22996778690400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0080a858-570b-408a-8d82-877fc17981c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0080a858-570b-408a-8d82-877fc17981c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996778638400, "endTime": 22996778690400}, "additional": {"logType": "info", "children": [], "durationId": "18fc207d-4bff-4370-b839-2b871234678f"}}, {"head": {"id": "36c1b761-b0fa-49bc-a467-16902309ff49", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996778875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f5b2ff-089e-4f23-a30e-cd491b0b8b09", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996782061400, "endTime": 22996782092300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "6f73f018-4749-4f13-bf68-28d3fc303392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f73f018-4749-4f13-bf68-28d3fc303392", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996782061400, "endTime": 22996782092300}, "additional": {"logType": "info", "children": [], "durationId": "13f5b2ff-089e-4f23-a30e-cd491b0b8b09"}}, {"head": {"id": "01fa554a-496a-45d8-9e7f-b13952ee3cea", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996782247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b29b36-a00a-4e8a-b1d3-71ea01b2209a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996783785100, "endTime": 22996783810200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "69fee949-9f4f-492f-bcbd-86b6f4100e04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69fee949-9f4f-492f-bcbd-86b6f4100e04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22996783785100, "endTime": 22996783810200}, "additional": {"logType": "info", "children": [], "durationId": "61b29b36-a00a-4e8a-b1d3-71ea01b2209a"}}, {"head": {"id": "99cab5b3-6da9-498b-b172-e5a129807707", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997206262300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752864c1-741d-4929-aefd-483a716275c4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997207831100, "endTime": 22997207853500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a176aa2-3c01-4271-a9d0-91558f442aa7", "logId": "e898f1fb-47ce-47ad-9534-8e85bfbd5938"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e898f1fb-47ce-47ad-9534-8e85bfbd5938", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997207831100, "endTime": 22997207853500}, "additional": {"logType": "info", "children": [], "durationId": "752864c1-741d-4929-aefd-483a716275c4"}}, {"head": {"id": "01afbf70-e2e1-4d28-a932-3b59fcb08b9f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997237272700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e40759-53c2-4f70-9394-68ce2cdbc3e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997238697600, "endTime": 22997238719100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "73da018d-ec75-422d-90c1-b6328ecce6ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73da018d-ec75-422d-90c1-b6328ecce6ba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 22997238697600, "endTime": 22997238719100}, "additional": {"logType": "info", "children": [], "durationId": "e6e40759-53c2-4f70-9394-68ce2cdbc3e2"}}, {"head": {"id": "ca2f5cad-eac7-47fc-a368-7749335391de", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23070761450400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e63238e4-27e7-42fe-8b88-ca19bb280a1e", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23071196550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3676e23-a061-4b23-87ff-076f6c6ba694", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23073035792800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074235857700, "endTime": 23081588035800}, "additional": {"children": ["dd82af61-a4e8-4bdf-8e0a-ec9234f22c9a", "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "1570ff7c-3e61-4c0b-a830-13a1bac9e443", "e71d3b92-f0d3-4d15-a54d-e09257c689d3", "aec180ca-6e86-475b-b21f-7a3b6afef11b", "5882d5b9-d148-4725-9e54-ca9fc8e0b6c3", "d549053d-ab59-4c7d-97da-80e7c824cba6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd82af61-a4e8-4bdf-8e0a-ec9234f22c9a", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074235861900, "endTime": 23075909191400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "931290cb-804b-475a-a9f9-ab21ec971eca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075909213500, "endTime": 23081562916600}, "additional": {"children": ["e4039205-a83f-4deb-9815-3cd81d0c2208", "35ef623d-6708-4627-b1a7-d7a7c8af160c", "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "cdb8525f-75e2-4c0b-a57b-b90c714f0ca8", "6cc98b96-0ae5-474c-8151-da069ca87e96", "891ca204-afa3-42c0-8ad1-d79150ffb00b", "649d8929-f1a1-433d-b401-f5fd3a3fd519", "e2155de3-bd5e-45c8-b824-dcff3300bfe1", "21aa7c33-ca7b-48fb-aa34-5f428d62399f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1570ff7c-3e61-4c0b-a830-13a1bac9e443", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081562955100, "endTime": 23081588025600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "dd6675be-d165-45a5-908e-a13bb30c0c5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e71d3b92-f0d3-4d15-a54d-e09257c689d3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081588030100, "endTime": 23081588032600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "e683949d-d248-4a67-b89c-141100509512"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aec180ca-6e86-475b-b21f-7a3b6afef11b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074912828400, "endTime": 23074912857400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "2e4df163-9494-453a-bb56-f76808a0b036"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e4df163-9494-453a-bb56-f76808a0b036", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074912828400, "endTime": 23074912857400}, "additional": {"logType": "info", "children": [], "durationId": "aec180ca-6e86-475b-b21f-7a3b6afef11b", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "5882d5b9-d148-4725-9e54-ca9fc8e0b6c3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075064896000, "endTime": 23075064914900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "c577e64f-2e9d-4a2d-8771-3ac9132f6cac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c577e64f-2e9d-4a2d-8771-3ac9132f6cac", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075064896000, "endTime": 23075064914900}, "additional": {"logType": "info", "children": [], "durationId": "5882d5b9-d148-4725-9e54-ca9fc8e0b6c3", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "2acd6943-452d-43ce-8841-32a04fa6ea9d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075252713000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3213c8dd-f500-4304-a19d-84f38392bb15", "name": "Cache service initialization finished in 568 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075909028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931290cb-804b-475a-a9f9-ab21ec971eca", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074235861900, "endTime": 23075909191400}, "additional": {"logType": "info", "children": [], "durationId": "dd82af61-a4e8-4bdf-8e0a-ec9234f22c9a", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "e4039205-a83f-4deb-9815-3cd81d0c2208", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075914548200, "endTime": 23075914556800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "785362fa-5b41-46e5-9076-e7e65021ac9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35ef623d-6708-4627-b1a7-d7a7c8af160c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075914573700, "endTime": 23076088711900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "68273fd9-f29a-45f2-9837-a9af4a5344ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076124084100, "endTime": 23079730237300}, "additional": {"children": ["90a3e363-f4d6-4a01-a0a6-d7dfc5533955", "ef8fd5af-9b37-4c94-bdc0-c409a185e28e", "88910fd8-ab3e-4324-8b49-226d727044df"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "02de36ff-c66d-4ecd-aafb-4cefa9894b46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdb8525f-75e2-4c0b-a57b-b90c714f0ca8", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079754057800, "endTime": 23080254943100}, "additional": {"children": ["187fd2cf-9cb0-46a7-8925-046ba7b26ddf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "0ea930b2-d9e7-440a-93cd-79ca621fbe5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cc98b96-0ae5-474c-8151-da069ca87e96", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080254953700, "endTime": 23081347274400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "2393a5e5-7ad7-40a8-963b-d56d24e4e534"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "891ca204-afa3-42c0-8ad1-d79150ffb00b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081348410000, "endTime": 23081425186200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "d957e3a6-414d-48bd-98e7-0f7d72289b8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "649d8929-f1a1-433d-b401-f5fd3a3fd519", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081425203300, "endTime": 23081562650200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "500b3fd7-9c81-43a7-8daf-4e06f2303a9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2155de3-bd5e-45c8-b824-dcff3300bfe1", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081562684200, "endTime": 23081562897200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "988bda87-6fec-45b2-8df8-d8b37aa1c98a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "785362fa-5b41-46e5-9076-e7e65021ac9f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075914548200, "endTime": 23075914556800}, "additional": {"logType": "info", "children": [], "durationId": "e4039205-a83f-4deb-9815-3cd81d0c2208", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "68273fd9-f29a-45f2-9837-a9af4a5344ec", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075914573700, "endTime": 23076088711900}, "additional": {"logType": "info", "children": [], "durationId": "35ef623d-6708-4627-b1a7-d7a7c8af160c", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "90a3e363-f4d6-4a01-a0a6-d7dfc5533955", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076127263600, "endTime": 23076127287000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "logId": "0b8f9020-308f-4a05-9842-9b7ce7ff43d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b8f9020-308f-4a05-9842-9b7ce7ff43d3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076127263600, "endTime": 23076127287000}, "additional": {"logType": "info", "children": [], "durationId": "90a3e363-f4d6-4a01-a0a6-d7dfc5533955", "parent": "02de36ff-c66d-4ecd-aafb-4cefa9894b46"}}, {"head": {"id": "ef8fd5af-9b37-4c94-bdc0-c409a185e28e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076162754900, "endTime": 23079729437200}, "additional": {"children": ["6265fdc2-9e1b-4fcb-b902-c7f2d81ebf2b", "defb5562-bedb-4cd2-ae9d-bbdd8690e317"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "logId": "b74e0684-99f8-4a2c-b7b1-a3fbfda9221e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6265fdc2-9e1b-4fcb-b902-c7f2d81ebf2b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076162756200, "endTime": 23077642970200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef8fd5af-9b37-4c94-bdc0-c409a185e28e", "logId": "4351bb47-a8ff-447f-8339-4115465ae98f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "defb5562-bedb-4cd2-ae9d-bbdd8690e317", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23077642990600, "endTime": 23079729424300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef8fd5af-9b37-4c94-bdc0-c409a185e28e", "logId": "0ad13d0e-0a4d-4b52-93ff-8dbf3b68179a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e224835-6f11-4438-942a-6dedbdbae55f", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076162760500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf825b2-2074-4bb9-8d72-b6b5b6206728", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23077642820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4351bb47-a8ff-447f-8339-4115465ae98f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076162756200, "endTime": 23077642970200}, "additional": {"logType": "info", "children": [], "durationId": "6265fdc2-9e1b-4fcb-b902-c7f2d81ebf2b", "parent": "b74e0684-99f8-4a2c-b7b1-a3fbfda9221e"}}, {"head": {"id": "1deda588-e4c4-4884-9741-d0d01950c4a6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23077643013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17b28d31-92f1-44fb-bdeb-b382caec39d0", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078479633800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eead3d8b-0fbf-4d5c-aadc-5808b22d2219", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078479758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dda8319-f759-44d7-b5fc-beb417879459", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078508313900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac062ac-bdda-43c6-b53e-f75c3551f94f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078511126900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd42e832-ed81-4aad-8e5e-1801d35b5808", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078553596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61e5291-e291-4437-ae4f-06d192650fe5", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23078842835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24480dc-4103-4239-83cd-c03dd4aedbad", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079217072800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db094dd6-6c28-43bf-895c-b5aba6b5bd66", "name": "Sdk init in 612 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079493997100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7adfa39-f2cd-400e-a7e9-d081faeb3f95", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079542139200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 22, "minute": 14}, "markType": "other"}}, {"head": {"id": "0a8c59de-3abc-4f40-aadb-e941e26ade59", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079542219200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 22, "minute": 14}, "markType": "other"}}, {"head": {"id": "2e527535-470d-4eae-af4c-0394986c0d35", "name": "Project task initialization takes 183 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079728626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c03036f-5553-443f-b3f4-7dfdbf61784f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079729247100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0098c65e-ea88-4f72-b135-07dcfe0fdf4a", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079729336700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed729d7-3132-47f8-8a4c-7da14416fd2d", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079729382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad13d0e-0a4d-4b52-93ff-8dbf3b68179a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23077642990600, "endTime": 23079729424300}, "additional": {"logType": "info", "children": [], "durationId": "defb5562-bedb-4cd2-ae9d-bbdd8690e317", "parent": "b74e0684-99f8-4a2c-b7b1-a3fbfda9221e"}}, {"head": {"id": "b74e0684-99f8-4a2c-b7b1-a3fbfda9221e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076162754900, "endTime": 23079729437200}, "additional": {"logType": "info", "children": ["4351bb47-a8ff-447f-8339-4115465ae98f", "0ad13d0e-0a4d-4b52-93ff-8dbf3b68179a"], "durationId": "ef8fd5af-9b37-4c94-bdc0-c409a185e28e", "parent": "02de36ff-c66d-4ecd-aafb-4cefa9894b46"}}, {"head": {"id": "88910fd8-ab3e-4324-8b49-226d727044df", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079730208900, "endTime": 23079730223800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "logId": "2e59c544-a568-405b-bff7-c92379150d14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e59c544-a568-405b-bff7-c92379150d14", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079730208900, "endTime": 23079730223800}, "additional": {"logType": "info", "children": [], "durationId": "88910fd8-ab3e-4324-8b49-226d727044df", "parent": "02de36ff-c66d-4ecd-aafb-4cefa9894b46"}}, {"head": {"id": "02de36ff-c66d-4ecd-aafb-4cefa9894b46", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23076124084100, "endTime": 23079730237300}, "additional": {"logType": "info", "children": ["0b8f9020-308f-4a05-9842-9b7ce7ff43d3", "b74e0684-99f8-4a2c-b7b1-a3fbfda9221e", "2e59c544-a568-405b-bff7-c92379150d14"], "durationId": "fd578eac-32ed-40fa-bc67-60c5fdd6b7ba", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "187fd2cf-9cb0-46a7-8925-046ba7b26ddf", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079755838800, "endTime": 23080254930000}, "additional": {"children": ["e7726d5e-1d30-4765-a2f3-1132549435f1", "fc49819b-6e81-491b-8a36-c63c8d71aa1e", "d8d214dc-0ba4-47f6-b681-6577741317f1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdb8525f-75e2-4c0b-a57b-b90c714f0ca8", "logId": "c6f9b279-58ca-4763-bf78-03c4786d50d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7726d5e-1d30-4765-a2f3-1132549435f1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079769873700, "endTime": 23079769905900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "187fd2cf-9cb0-46a7-8925-046ba7b26ddf", "logId": "16f3fc27-8848-46df-8ed3-e9e8550d048c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16f3fc27-8848-46df-8ed3-e9e8550d048c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079769873700, "endTime": 23079769905900}, "additional": {"logType": "info", "children": [], "durationId": "e7726d5e-1d30-4765-a2f3-1132549435f1", "parent": "c6f9b279-58ca-4763-bf78-03c4786d50d6"}}, {"head": {"id": "fc49819b-6e81-491b-8a36-c63c8d71aa1e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079774128200, "endTime": 23080253036100}, "additional": {"children": ["ff9243a3-abe4-4f20-ace5-bd32cb379794", "bdb8acd4-ae22-4ab7-bd7c-1d1d1f36eb4e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "187fd2cf-9cb0-46a7-8925-046ba7b26ddf", "logId": "76217b74-ee98-4023-9fcf-7ea21e03e46b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff9243a3-abe4-4f20-ace5-bd32cb379794", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079774131600, "endTime": 23079914874600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc49819b-6e81-491b-8a36-c63c8d71aa1e", "logId": "a628b8e9-8286-4ba9-9b69-5b22e77439bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdb8acd4-ae22-4ab7-bd7c-1d1d1f36eb4e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079915060000, "endTime": 23080253013900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fc49819b-6e81-491b-8a36-c63c8d71aa1e", "logId": "4b951e25-829f-4db7-9a4a-d0b73fbe2255"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d27f9551-2fec-4e89-9d96-4b2c80499f0a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079774140500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d64e54e-76ce-4f96-a58c-82c9ad9ff4ff", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079914296900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a628b8e9-8286-4ba9-9b69-5b22e77439bd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079774131600, "endTime": 23079914874600}, "additional": {"logType": "info", "children": [], "durationId": "ff9243a3-abe4-4f20-ace5-bd32cb379794", "parent": "76217b74-ee98-4023-9fcf-7ea21e03e46b"}}, {"head": {"id": "6bf353fb-6904-411c-b765-8613bd1f49fb", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079915168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809b81d5-e28b-4015-bad0-ab978727b2f0", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080030903100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c662ec61-dc5d-44a0-9265-740b0c8439f8", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080031083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0bf62f8-54b5-4224-8a07-cd5c62a3555f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080031320800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7967176a-221b-446b-9684-b9904b9ed969", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080032124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73674f7c-f8f0-4dfd-985f-7c5eb79e7edf", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080032229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a54b6d1c-81ad-48dc-bab6-53c4bc552a8e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080032279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048e9bae-a772-4b92-841a-cc325c51cd66", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080032343700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd9e47ea-106f-4a53-b4f3-2ae3fe3d7d04", "name": "Module entry task initialization takes 161 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080252590300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb8ea456-7c1e-4c36-84e0-60905b690811", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080252805700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69041a0a-420b-4a38-878f-745e8b731af3", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080252885800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba38dd1-37dc-423a-85b9-bce7a7b6f1f8", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080252952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b951e25-829f-4db7-9a4a-d0b73fbe2255", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079915060000, "endTime": 23080253013900}, "additional": {"logType": "info", "children": [], "durationId": "bdb8acd4-ae22-4ab7-bd7c-1d1d1f36eb4e", "parent": "76217b74-ee98-4023-9fcf-7ea21e03e46b"}}, {"head": {"id": "76217b74-ee98-4023-9fcf-7ea21e03e46b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079774128200, "endTime": 23080253036100}, "additional": {"logType": "info", "children": ["a628b8e9-8286-4ba9-9b69-5b22e77439bd", "4b951e25-829f-4db7-9a4a-d0b73fbe2255"], "durationId": "fc49819b-6e81-491b-8a36-c63c8d71aa1e", "parent": "c6f9b279-58ca-4763-bf78-03c4786d50d6"}}, {"head": {"id": "d8d214dc-0ba4-47f6-b681-6577741317f1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080254899900, "endTime": 23080254914600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "187fd2cf-9cb0-46a7-8925-046ba7b26ddf", "logId": "f673e8d3-c17e-4a50-8b7b-76c049716dec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f673e8d3-c17e-4a50-8b7b-76c049716dec", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080254899900, "endTime": 23080254914600}, "additional": {"logType": "info", "children": [], "durationId": "d8d214dc-0ba4-47f6-b681-6577741317f1", "parent": "c6f9b279-58ca-4763-bf78-03c4786d50d6"}}, {"head": {"id": "c6f9b279-58ca-4763-bf78-03c4786d50d6", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079755838800, "endTime": 23080254930000}, "additional": {"logType": "info", "children": ["16f3fc27-8848-46df-8ed3-e9e8550d048c", "76217b74-ee98-4023-9fcf-7ea21e03e46b", "f673e8d3-c17e-4a50-8b7b-76c049716dec"], "durationId": "187fd2cf-9cb0-46a7-8925-046ba7b26ddf", "parent": "0ea930b2-d9e7-440a-93cd-79ca621fbe5b"}}, {"head": {"id": "0ea930b2-d9e7-440a-93cd-79ca621fbe5b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23079754057800, "endTime": 23080254943100}, "additional": {"logType": "info", "children": ["c6f9b279-58ca-4763-bf78-03c4786d50d6"], "durationId": "cdb8525f-75e2-4c0b-a57b-b90c714f0ca8", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "2568596d-c683-4932-9fa2-144587a837ea", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081292079600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e63c482-0c37-416f-9815-75959480e8af", "name": "hvigorfile, resolve hvigorfile dependencies in 1 s 92 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081346933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2393a5e5-7ad7-40a8-963b-d56d24e4e534", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23080254953700, "endTime": 23081347274400}, "additional": {"logType": "info", "children": [], "durationId": "6cc98b96-0ae5-474c-8151-da069ca87e96", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "21aa7c33-ca7b-48fb-aa34-5f428d62399f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081348206300, "endTime": 23081348400700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "logId": "3f64a526-2bd0-4ca4-abed-2945e28671ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "742ce819-ece1-4b0b-a7e1-55f09f2ec76e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081348233100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f64a526-2bd0-4ca4-abed-2945e28671ea", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081348206300, "endTime": 23081348400700}, "additional": {"logType": "info", "children": [], "durationId": "21aa7c33-ca7b-48fb-aa34-5f428d62399f", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "3a246fe1-01a8-48e0-9af2-2f626061c509", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081349826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb92d77d-75bf-4b57-8310-356fa341bb4a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081424367100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d957e3a6-414d-48bd-98e7-0f7d72289b8b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081348410000, "endTime": 23081425186200}, "additional": {"logType": "info", "children": [], "durationId": "891ca204-afa3-42c0-8ad1-d79150ffb00b", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "db6ac419-5c58-4409-a1cd-876737e884d2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081425216600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0fb04ec-039c-4f2d-8b87-333b34912cd2", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081494277200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec269405-aeb7-4e53-8782-95168b043c49", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081494454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e562dff-9450-4455-811b-ee42a3fee470", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081531146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1dac3a-11aa-4082-93e1-b541f7c485bb", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081535437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a258d06-0a1a-499c-a250-49976744de5c", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081535575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "500b3fd7-9c81-43a7-8daf-4e06f2303a9d", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081425203300, "endTime": 23081562650200}, "additional": {"logType": "info", "children": [], "durationId": "649d8929-f1a1-433d-b401-f5fd3a3fd519", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "610d2af6-1114-46f9-b789-7dede02ff53e", "name": "Configuration phase cost:5 s 649 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081562729700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988bda87-6fec-45b2-8df8-d8b37aa1c98a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081562684200, "endTime": 23081562897200}, "additional": {"logType": "info", "children": [], "durationId": "e2155de3-bd5e-45c8-b824-dcff3300bfe1", "parent": "73d6c5e3-7225-4595-aeca-0d257e19f2fe"}}, {"head": {"id": "73d6c5e3-7225-4595-aeca-0d257e19f2fe", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23075909213500, "endTime": 23081562916600}, "additional": {"logType": "info", "children": ["785362fa-5b41-46e5-9076-e7e65021ac9f", "68273fd9-f29a-45f2-9837-a9af4a5344ec", "02de36ff-c66d-4ecd-aafb-4cefa9894b46", "0ea930b2-d9e7-440a-93cd-79ca621fbe5b", "2393a5e5-7ad7-40a8-963b-d56d24e4e534", "d957e3a6-414d-48bd-98e7-0f7d72289b8b", "500b3fd7-9c81-43a7-8daf-4e06f2303a9d", "988bda87-6fec-45b2-8df8-d8b37aa1c98a", "3f64a526-2bd0-4ca4-abed-2945e28671ea"], "durationId": "138cc3bc-1ddf-41b5-9a1d-1a15f3cc9864", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "d549053d-ab59-4c7d-97da-80e7c824cba6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081587991200, "endTime": 23081588012000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7", "logId": "a8113750-c70b-4c90-965d-e82ad97a478f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8113750-c70b-4c90-965d-e82ad97a478f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081587991200, "endTime": 23081588012000}, "additional": {"logType": "info", "children": [], "durationId": "d549053d-ab59-4c7d-97da-80e7c824cba6", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "dd6675be-d165-45a5-908e-a13bb30c0c5d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081562955100, "endTime": 23081588025600}, "additional": {"logType": "info", "children": [], "durationId": "1570ff7c-3e61-4c0b-a830-13a1bac9e443", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "e683949d-d248-4a67-b89c-141100509512", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081588030100, "endTime": 23081588032600}, "additional": {"logType": "info", "children": [], "durationId": "e71d3b92-f0d3-4d15-a54d-e09257c689d3", "parent": "12b330b6-8453-46c9-92df-e9f1e26fa71e"}}, {"head": {"id": "12b330b6-8453-46c9-92df-e9f1e26fa71e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23074235857700, "endTime": 23081588035800}, "additional": {"logType": "info", "children": ["931290cb-804b-475a-a9f9-ab21ec971eca", "73d6c5e3-7225-4595-aeca-0d257e19f2fe", "dd6675be-d165-45a5-908e-a13bb30c0c5d", "e683949d-d248-4a67-b89c-141100509512", "2e4df163-9494-453a-bb56-f76808a0b036", "c577e64f-2e9d-4a2d-8771-3ac9132f6cac", "a8113750-c70b-4c90-965d-e82ad97a478f"], "durationId": "32c4a45e-b0f1-4e68-ae5e-b3cfaa5978c7"}}, {"head": {"id": "3c24d2d2-3683-4f43-9f00-ed5def4f5869", "name": "Configuration task cost before running: 7 s 628 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081588856200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7506c8f-c51d-4546-9c4c-7ce58d249b9b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081618544900, "endTime": 23081695351900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "25399351-5b90-4bf0-86a1-c5e260f10eab", "logId": "0b4e4940-bc23-45d9-af8c-d912be0403f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25399351-5b90-4bf0-86a1-c5e260f10eab", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081590526800}, "additional": {"logType": "detail", "children": [], "durationId": "c7506c8f-c51d-4546-9c4c-7ce58d249b9b"}}, {"head": {"id": "293f8b26-5d22-47f0-a6f4-0ef137c73487", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081591170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3f6e63-0038-44f6-83d8-b99c3011a4ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081591248200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6198217-487d-418f-8ea9-3d9d86b8d129", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081618564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "220004ac-1c53-418c-ac30-d2b7a1636e16", "name": "Incremental task entry:default@PreBuild pre-execution cost: 74 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081694956800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1aaacf4-3325-4cea-9825-5c107cf2d2b1", "name": "entry : default@PreBuild cost memory -1.55499267578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081695217800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4e4940-bc23-45d9-af8c-d912be0403f6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081618544900, "endTime": 23081695351900}, "additional": {"logType": "info", "children": [], "durationId": "c7506c8f-c51d-4546-9c4c-7ce58d249b9b"}}, {"head": {"id": "5e8c08ab-7fde-44ab-9412-4811f965f03f", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081700721300, "endTime": 23081733102300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9ccd065f-1d05-4a86-91f3-ea665a914ecb", "logId": "19b9d487-d422-4465-abf2-fbf889c56443"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ccd065f-1d05-4a86-91f3-ea665a914ecb", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081699364600}, "additional": {"logType": "detail", "children": [], "durationId": "5e8c08ab-7fde-44ab-9412-4811f965f03f"}}, {"head": {"id": "1a8a3bd5-1912-4403-954a-d1f0d2a8d592", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081699858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f12f07-e8b7-4bb8-872f-8ee47cbe3e57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081699965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa2b4b1-d59c-4fd6-9cb0-1f1a34cafd1e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081700732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79972339-45be-4a9a-8f65-9b60121b5913", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 32 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081732771000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ddbebf-34d6-4d94-b87e-f98e5e69e9d8", "name": "entry : default@MergeProfile cost memory 0.10581207275390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081732946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b9d487-d422-4465-abf2-fbf889c56443", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081700721300, "endTime": 23081733102300}, "additional": {"logType": "info", "children": [], "durationId": "5e8c08ab-7fde-44ab-9412-4811f965f03f"}}, {"head": {"id": "d6254162-1057-48ed-8257-4a9effc0dbef", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081766652200, "endTime": 23081795203200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "69318581-14f1-44a4-b037-7686ae0aefea", "logId": "4ecc1061-a02f-41a5-a40e-f77bedea1a41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69318581-14f1-44a4-b037-7686ae0aefea", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081735156600}, "additional": {"logType": "detail", "children": [], "durationId": "d6254162-1057-48ed-8257-4a9effc0dbef"}}, {"head": {"id": "6fb53b4c-2911-4517-8daf-acd9f19fe413", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081763729200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24afaf5e-451c-4a18-a61d-a468a472dc22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081763922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb29bf5-4d85-4d7c-a433-84ed9defd824", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081766682500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e249b81-3643-4220-8d03-8a8b5651df40", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 27 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081793637000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72470398-5d12-4c79-bc0c-545202d222ee", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081795048800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dbefbc8-6fe3-4d1e-8449-631e16a4fc2c", "name": "entry : default@CreateBuildProfile cost memory 0.0940093994140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081795136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ecc1061-a02f-41a5-a40e-f77bedea1a41", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081766652200, "endTime": 23081795203200}, "additional": {"logType": "info", "children": [], "durationId": "d6254162-1057-48ed-8257-4a9effc0dbef"}}, {"head": {"id": "0e328fe6-8a25-463e-add9-de1cf543a749", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081824239400, "endTime": 23081838822200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "90228593-175f-49cd-8160-8e5ffec33783", "logId": "342cb725-768e-440c-89a5-7557a2a7844f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90228593-175f-49cd-8160-8e5ffec33783", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081822564000}, "additional": {"logType": "detail", "children": [], "durationId": "0e328fe6-8a25-463e-add9-de1cf543a749"}}, {"head": {"id": "bce45c2a-4695-4af0-b66c-da26f551df51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081823194800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac8f84b-7767-4c6e-9b38-a5f3641ebc75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081823300800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0fbbbd4-dc33-446e-aca8-33a924fab61e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081824251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb2be5d-d803-40df-954a-f71df8a30b40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081838489100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c38af4-d273-42d2-895d-43f62e49a85b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081838598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9694ddb9-c21c-4ed9-b200-da2bfdac9ce5", "name": "entry : default@PreCheckSyscap cost memory 0.03670501708984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081838683500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cd835c4-7d89-41a2-8efd-d431caf22322", "name": "runTaskFromQueue task cost before running: 7 s 878 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081838765300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342cb725-768e-440c-89a5-7557a2a7844f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081824239400, "endTime": 23081838822200, "totalTime": 14503100}, "additional": {"logType": "info", "children": [], "durationId": "0e328fe6-8a25-463e-add9-de1cf543a749"}}, {"head": {"id": "1ae16ff7-adfb-4312-b2fa-7e3df59941d7", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081850991000, "endTime": 23081851974500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1f3dd29e-decf-4782-8c4c-f2b817d9c6ab", "logId": "6bdad36c-4c31-41e5-91bf-c1fdb11eae28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f3dd29e-decf-4782-8c4c-f2b817d9c6ab", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081841615100}, "additional": {"logType": "detail", "children": [], "durationId": "1ae16ff7-adfb-4312-b2fa-7e3df59941d7"}}, {"head": {"id": "99386d09-b6ce-40f4-bd0a-e21f83d9b8c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081842125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb74517-e038-4ac5-b347-2640939bb634", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081842215800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5720112-71f6-4bae-9ac8-aa8e8c87e8db", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081851002700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7273f270-d6c1-454b-91ca-f0b5452c3ecf", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081851197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32083600-f107-4290-a692-58929eb4a662", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081851825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d24aa42-a548-43cb-8a95-6d25cf5cf3f6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.063201904296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081851907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdad36c-4c31-41e5-91bf-c1fdb11eae28", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081850991000, "endTime": 23081851974500}, "additional": {"logType": "info", "children": [], "durationId": "1ae16ff7-adfb-4312-b2fa-7e3df59941d7"}}, {"head": {"id": "8a62e836-0f10-46db-89c9-20acc290feb8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081855484900, "endTime": 23081910037400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "116d766f-ff34-4c70-b5e0-c4a41c1be4ac", "logId": "67a2c724-2764-407b-83bc-cf42f8828150"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "116d766f-ff34-4c70-b5e0-c4a41c1be4ac", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081853760700}, "additional": {"logType": "detail", "children": [], "durationId": "8a62e836-0f10-46db-89c9-20acc290feb8"}}, {"head": {"id": "0ce7c83c-0190-40e4-a5e3-80393a1f6ed0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081854264600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80ff258c-7e8a-4027-a80c-9e47d97a723f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081854349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c2c627-4d15-43ce-9b46-979e56a1653c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081855494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44d923b-41b1-4373-8c42-26153488311b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081909801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8adb49be-3bfa-4204-95a0-f7d95c313e89", "name": "entry : default@ProcessProfile cost memory 0.05429840087890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081909952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a2c724-2764-407b-83bc-cf42f8828150", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081855484900, "endTime": 23081910037400}, "additional": {"logType": "info", "children": [], "durationId": "8a62e836-0f10-46db-89c9-20acc290feb8"}}, {"head": {"id": "344b8788-dce2-4382-b6f2-916f41db6cb0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081915604000, "endTime": 23081953855000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "af69f2b9-2a54-4fca-9fd6-75d5a7ecd448", "logId": "3973ee80-782e-4529-981f-4430620cff27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af69f2b9-2a54-4fca-9fd6-75d5a7ecd448", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081913049400}, "additional": {"logType": "detail", "children": [], "durationId": "344b8788-dce2-4382-b6f2-916f41db6cb0"}}, {"head": {"id": "ab204b35-9fd7-490d-b774-d3f6c76e6e78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081913724200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb9853c-a313-4379-9933-bad047d90beb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081913833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7255bbfd-c29b-41ab-8400-31ffe17c9cd2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081915613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70a2f6c-cdb3-41a4-95eb-924c147ef3e0", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081953655100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b308ad8-f0fb-45ea-8f84-ece7f5466ecc", "name": "entry : default@ProcessRouterMap cost memory 0.18439483642578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081953781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3973ee80-782e-4529-981f-4430620cff27", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081915604000, "endTime": 23081953855000}, "additional": {"logType": "info", "children": [], "durationId": "344b8788-dce2-4382-b6f2-916f41db6cb0"}}, {"head": {"id": "d9cdbab7-6e60-4e5f-acb6-e67ad5f368ba", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081973795700, "endTime": 23081976984100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "84b2c9a5-3bf8-4ffa-ba43-53be0e8cbfcb", "logId": "a2430e52-6b90-496e-981b-0b6305c28549"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84b2c9a5-3bf8-4ffa-ba43-53be0e8cbfcb", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081957350700}, "additional": {"logType": "detail", "children": [], "durationId": "d9cdbab7-6e60-4e5f-acb6-e67ad5f368ba"}}, {"head": {"id": "bfa06386-d0c5-4e9c-bf84-6218d0779bdf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081957920300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9aa7aa7-85a5-48fb-a189-750079ce5bf3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081958027300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c72956-e82a-4d88-8c96-dff564e098a4", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081959143600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce1c348-c801-4ceb-9a9c-e07bfea03b24", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081975232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f6bb1dd-2a40-4e7a-b70a-fa6a1c49062c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081975391900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c9142a6-8d07-424b-a2ac-9303b40a682c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081975446200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e275478e-9ed4-4ea2-a2a7-2a86f966866e", "name": "entry : default@PreviewProcessResource cost memory 0.06763458251953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081975518400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b358f3f-b411-4ce5-8bdf-c225a4a3e7da", "name": "runTaskFromQueue task cost before running: 8 s 16 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081976893800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2430e52-6b90-496e-981b-0b6305c28549", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081973795700, "endTime": 23081976984100, "totalTime": 1790700}, "additional": {"logType": "info", "children": [], "durationId": "d9cdbab7-6e60-4e5f-acb6-e67ad5f368ba"}}, {"head": {"id": "17ae1306-fecc-4ebf-8055-feac11108b44", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081983790400, "endTime": 23082037546400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "78d383b6-d146-4b70-b70a-e1f075f6660f", "logId": "0a7cc1d5-a773-4650-86cc-d045f1df45dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78d383b6-d146-4b70-b70a-e1f075f6660f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081980020800}, "additional": {"logType": "detail", "children": [], "durationId": "17ae1306-fecc-4ebf-8055-feac11108b44"}}, {"head": {"id": "e98a2d75-1d7e-4c27-8838-8de40c9b4ca8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081980491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8b01f0-4cd2-4597-8899-344e497e7aca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081980569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a52a6af2-0c3a-451d-9db4-ae2390dee39e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081983804800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd2b8f6-75fc-451c-b380-fcc2033defa3", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 44 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082037136900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9323644e-f79d-4ca8-83bc-2478c1bcb7fd", "name": "entry : default@GenerateLoaderJson cost memory -1.0125656127929688", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082037386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7cc1d5-a773-4650-86cc-d045f1df45dc", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23081983790400, "endTime": 23082037546400}, "additional": {"logType": "info", "children": [], "durationId": "17ae1306-fecc-4ebf-8055-feac11108b44"}}, {"head": {"id": "afb15db9-bf0e-43be-80d1-bea0373e29a9", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082072871800, "endTime": 23082186000500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8136444d-bb65-4ff0-bd00-49a91687d903", "logId": "94453563-16c8-4790-919e-c1adc3213e69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8136444d-bb65-4ff0-bd00-49a91687d903", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082069074600}, "additional": {"logType": "detail", "children": [], "durationId": "afb15db9-bf0e-43be-80d1-bea0373e29a9"}}, {"head": {"id": "c1b64170-0999-4362-a8d2-0a3d7cddd6b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082069658500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac8160d6-b004-47ea-9106-996fa116c28d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082069742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb90c24-190b-4cb3-ad95-82fb31fc7c63", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082070735800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0914a58-ee75-4ac8-afd2-b29e64878132", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082072897500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35aa703c-09ea-4e06-b4da-1a4f62fcca48", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 112 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082185682600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29612686-c618-449a-8cec-5075b9a08a53", "name": "entry : default@PreviewCompileResource cost memory 0.6916427612304688", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082185828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94453563-16c8-4790-919e-c1adc3213e69", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082072871800, "endTime": 23082186000500}, "additional": {"logType": "info", "children": [], "durationId": "afb15db9-bf0e-43be-80d1-bea0373e29a9"}}, {"head": {"id": "b9014c37-40ea-4d53-a591-086965e67b5f", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224715800, "endTime": 23082225086400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "05d39b34-f1bb-4799-ac5a-c01191c680a6", "logId": "5d6d1c9d-fba9-44e0-aaf5-a71f414a2e3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05d39b34-f1bb-4799-ac5a-c01191c680a6", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082223957400}, "additional": {"logType": "detail", "children": [], "durationId": "b9014c37-40ea-4d53-a591-086965e67b5f"}}, {"head": {"id": "78bcc16a-665d-4fe5-898f-120d4b39350b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca6817d-398f-4649-8ae0-33715de0600d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224633400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1ddcaf-4fc1-4b67-96b5-be6668eab460", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224727000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035f8aa8-514e-4922-bcdc-a73d8c7f4f99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224806700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b96b742-d5f4-4134-94ed-a3bcca9215a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224846800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee240baf-d934-4896-8893-d94e0c6f1c5f", "name": "entry : default@PreviewHookCompileResource cost memory 0.03781890869140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224925400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d94a728b-4d7e-417d-9de6-2c79318b4516", "name": "runTaskFromQueue task cost before running: 8 s 264 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082225028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d6d1c9d-fba9-44e0-aaf5-a71f414a2e3d", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082224715800, "endTime": 23082225086400, "totalTime": 294200}, "additional": {"logType": "info", "children": [], "durationId": "b9014c37-40ea-4d53-a591-086965e67b5f"}}, {"head": {"id": "ab443e60-fde4-4c57-9643-9a3d4eba3f05", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082227859600, "endTime": 23082264512000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "bfc6e776-13c6-46cb-ae67-a81add4a77c4", "logId": "bf0bd2de-ea1b-4469-af95-5316a14acbf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfc6e776-13c6-46cb-ae67-a81add4a77c4", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082226676300}, "additional": {"logType": "detail", "children": [], "durationId": "ab443e60-fde4-4c57-9643-9a3d4eba3f05"}}, {"head": {"id": "0876bce1-b2c1-4ba3-b312-a16923f71aa4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082227116900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c100cc0-812b-4e69-ab66-b891f4f2d6d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082227189800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a737ba-018b-4192-a2a7-15d8e63a3e6d", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082227868600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f10642-ee03-4adf-a6f2-2b222becb79f", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082264322000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c615ea7a-929c-4fea-ad9e-d4c9a10cb8af", "name": "entry : default@CopyPreviewProfile cost memory 0.09339141845703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082264441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0bd2de-ea1b-4469-af95-5316a14acbf9", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082227859600, "endTime": 23082264512000}, "additional": {"logType": "info", "children": [], "durationId": "ab443e60-fde4-4c57-9643-9a3d4eba3f05"}}, {"head": {"id": "9e9a5ae6-237f-4333-bf4a-93d91697ca00", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269427200, "endTime": 23082270259600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ddc6b822-808e-4307-9d55-bb3a0f4c7bb0", "logId": "addc2552-f20d-444d-b0d0-6023e3d379ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddc6b822-808e-4307-9d55-bb3a0f4c7bb0", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082267184800}, "additional": {"logType": "detail", "children": [], "durationId": "9e9a5ae6-237f-4333-bf4a-93d91697ca00"}}, {"head": {"id": "7df9fc0c-29c3-41b5-aca2-0611cdfdedfa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082267795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e8c373-aa0e-47ad-ab5d-fe4c669ffac7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082267908300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa540688-67aa-4681-88c0-fc64ec79acd8", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c20816-53f7-4760-a278-8a4190f27a53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269543100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4096705-90d9-42bd-87f5-6e487c0bfe06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b996d2-a158-46bc-ad01-17e20b04c95c", "name": "entry : default@ReplacePreviewerPage cost memory 0.0377655029296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1580b0d-6d14-40b9-8f0c-8288142fa8c2", "name": "runTaskFromQueue task cost before running: 8 s 309 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addc2552-f20d-444d-b0d0-6023e3d379ad", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082269427200, "endTime": 23082270259600, "totalTime": 365400}, "additional": {"logType": "info", "children": [], "durationId": "9e9a5ae6-237f-4333-bf4a-93d91697ca00"}}, {"head": {"id": "24971e5e-7277-4db9-9157-fa9c4a5d1a0e", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272068900, "endTime": 23082272319500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e32af369-c30b-4784-a6a4-be01dfc322e5", "logId": "d01632a0-b322-46e8-8792-d174ff59127a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e32af369-c30b-4784-a6a4-be01dfc322e5", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272020900}, "additional": {"logType": "detail", "children": [], "durationId": "24971e5e-7277-4db9-9157-fa9c4a5d1a0e"}}, {"head": {"id": "2ee22083-311b-4ef3-97c3-2e03c9108913", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272078200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fdfdd87-5af6-4f98-a62d-0eaf1c86a6cc", "name": "entry : buildPreviewerResource cost memory 0.0116119384765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d640b154-9a99-4f0e-b9b1-d076b50d175d", "name": "runTaskFromQueue task cost before running: 8 s 312 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01632a0-b322-46e8-8792-d174ff59127a", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082272068900, "endTime": 23082272319500, "totalTime": 177600}, "additional": {"logType": "info", "children": [], "durationId": "24971e5e-7277-4db9-9157-fa9c4a5d1a0e"}}, {"head": {"id": "75b4455f-8dce-40a7-8e45-ab6fd1d7ceba", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082275202800, "endTime": 23082289986000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6ccb7941-4f74-4618-8cce-bd79350d811c", "logId": "00a128ae-036a-411a-8fa6-dcb39ce5c739"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ccb7941-4f74-4618-8cce-bd79350d811c", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082273867000}, "additional": {"logType": "detail", "children": [], "durationId": "75b4455f-8dce-40a7-8e45-ab6fd1d7ceba"}}, {"head": {"id": "113b7d4b-0ccb-4aba-9eac-429f4299d52d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082274359000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50581352-7ce9-4327-b1db-353e6f80dc54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082274447800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4d38b2-e6f2-4afe-872b-0b8f10fe38f9", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082275214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf7573d-575d-48bb-961b-4ee431cb82d3", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082277488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88204b6f-88c6-45e5-86f1-3803bdd683ee", "name": "entry : default@PreviewUpdateAssets cost memory 0.11060333251953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082277572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a128ae-036a-411a-8fa6-dcb39ce5c739", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082275202800, "endTime": 23082289986000}, "additional": {"logType": "info", "children": [], "durationId": "75b4455f-8dce-40a7-8e45-ab6fd1d7ceba"}}, {"head": {"id": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082298349000, "endTime": 23129336670100}, "additional": {"children": ["da727302-32ee-43b9-9808-0b30a57aa1ec"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed."], "detailId": "a46ad33d-8ad5-4c6a-a818-a0c8850d7232", "logId": "0972d2bb-6239-4b57-bfde-a738a8079312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a46ad33d-8ad5-4c6a-a818-a0c8850d7232", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082292416600}, "additional": {"logType": "detail", "children": [], "durationId": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d"}}, {"head": {"id": "2612baa8-b9e8-4648-bb7c-4cfdd757896b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082292924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35964328-69ec-4eaf-9b03-4431930e3087", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082293012400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8be488b-6d5f-4d20-964b-b51961c6067b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082298360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccba29c1-bbf4-4bfb-9196-7e73c7ce6b29", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082370244500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b822c9-46d2-4f91-88e7-e5acacdea1f1", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082370393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da727302-32ee-43b9-9808-0b30a57aa1ec", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker23", "startTime": 23082486164400, "endTime": 23129336510800}, "additional": {"children": ["83d936e8-2d32-427c-87a3-0863e51dd8ec", "45d1a235-c3d8-4962-98c2-c0ef68b65adf", "3069e705-a4a8-4507-ac14-377393a39265", "107a9bf7-ac4c-4674-ad93-e00cd609f005", "59e91cbe-c5e8-44dd-be8b-cd8568eacf24", "6084cd12-a0a0-4265-b932-bac5ac74acd7", "efd56fb0-e212-44b8-beb3-0566d55da330", "316ebcd7-6564-43a4-b0b5-dbba804da227", "bc9ca92d-e5f9-400a-b115-0f108821ea8a"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d", "logId": "f050d1b6-00ed-431d-8f24-79c5a401b532"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc36899c-3cdd-48da-9207-aa90d7d9eb50", "name": "entry : default@PreviewArkTS cost memory -0.253570556640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082503166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874ac1bf-e3e8-413d-aad4-3e66f48645ba", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23114898880700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d936e8-2d32-427c-87a3-0863e51dd8ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23114901360500, "endTime": 23114901401500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "469f9aa0-1a7e-4b64-917c-1044839ca774"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "469f9aa0-1a7e-4b64-917c-1044839ca774", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23114901360500, "endTime": 23114901401500}, "additional": {"logType": "info", "children": [], "durationId": "83d936e8-2d32-427c-87a3-0863e51dd8ec", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "237aaca0-bdab-46a1-8ad4-28db3cc8a88e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121262356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d1a235-c3d8-4962-98c2-c0ef68b65adf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121264531900, "endTime": 23121264569600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "95fdddb8-6701-4b45-803c-5487bc083a3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95fdddb8-6701-4b45-803c-5487bc083a3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121264531900, "endTime": 23121264569600}, "additional": {"logType": "info", "children": [], "durationId": "45d1a235-c3d8-4962-98c2-c0ef68b65adf", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "fc2c08a4-ef85-42b6-a081-457803075ce1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121264723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3069e705-a4a8-4507-ac14-377393a39265", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121266905600, "endTime": 23121266943800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "507caec0-6f15-43b0-bb5d-bbc25f2cede3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "507caec0-6f15-43b0-bb5d-bbc25f2cede3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121266905600, "endTime": 23121266943800}, "additional": {"logType": "info", "children": [], "durationId": "3069e705-a4a8-4507-ac14-377393a39265", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "49e07eec-eb2c-4e78-a628-fab7f4428af1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121267158500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "107a9bf7-ac4c-4674-ad93-e00cd609f005", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121269458100, "endTime": 23121269499000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "99588363-4295-4d0e-97c8-ff54f011e283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99588363-4295-4d0e-97c8-ff54f011e283", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121269458100, "endTime": 23121269499000}, "additional": {"logType": "info", "children": [], "durationId": "107a9bf7-ac4c-4674-ad93-e00cd609f005", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "4e3bb24b-b8cd-482a-9d1d-9c7bdf98db15", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121269684000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e91cbe-c5e8-44dd-be8b-cd8568eacf24", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121272158400, "endTime": 23121272195900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "0b9f88f8-2c68-4ab7-84d7-4e3ab1413662"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b9f88f8-2c68-4ab7-84d7-4e3ab1413662", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121272158400, "endTime": 23121272195900}, "additional": {"logType": "info", "children": [], "durationId": "59e91cbe-c5e8-44dd-be8b-cd8568eacf24", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "f1cdef9e-8169-4c10-9d79-6b560f331cf9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121272381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6084cd12-a0a0-4265-b932-bac5ac74acd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121274241600, "endTime": 23121274282700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "a11d2b52-9a5c-4f95-9c41-8f66eee9b9e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a11d2b52-9a5c-4f95-9c41-8f66eee9b9e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121274241600, "endTime": 23121274282700}, "additional": {"logType": "info", "children": [], "durationId": "6084cd12-a0a0-4265-b932-bac5ac74acd7", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "2a476d71-ca76-4c5b-af46-11b37f890952", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121274432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efd56fb0-e212-44b8-beb3-0566d55da330", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121280260200, "endTime": 23121280319700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "bb8ead1c-4bbc-4f12-8ca3-7eede1782eb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb8ead1c-4bbc-4f12-8ca3-7eede1782eb2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121280260200, "endTime": 23121280319700}, "additional": {"logType": "info", "children": [], "durationId": "efd56fb0-e212-44b8-beb3-0566d55da330", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "e5f2813f-8402-4956-b568-8612a20d1ad4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121280555900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316ebcd7-6564-43a4-b0b5-dbba804da227", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121282726200, "endTime": 23121282770900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "0739391a-bb4c-4622-8715-3dbf45a81859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0739391a-bb4c-4622-8715-3dbf45a81859", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23121282726200, "endTime": 23121282770900}, "additional": {"logType": "info", "children": [], "durationId": "316ebcd7-6564-43a4-b0b5-dbba804da227", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "d4c14e14-fb95-4229-a4bb-46e42fae63a3", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129335447800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9ca92d-e5f9-400a-b115-0f108821ea8a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129336432000, "endTime": 23129336448300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da727302-32ee-43b9-9808-0b30a57aa1ec", "logId": "24fcf4b0-d72b-41e4-a7ee-2e0d85ca2116"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24fcf4b0-d72b-41e4-a7ee-2e0d85ca2116", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129336432000, "endTime": 23129336448300}, "additional": {"logType": "info", "children": [], "durationId": "bc9ca92d-e5f9-400a-b115-0f108821ea8a", "parent": "f050d1b6-00ed-431d-8f24-79c5a401b532"}}, {"head": {"id": "f050d1b6-00ed-431d-8f24-79c5a401b532", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker23", "startTime": 23082486164400, "endTime": 23129336510800}, "additional": {"logType": "error", "children": ["469f9aa0-1a7e-4b64-917c-1044839ca774", "95fdddb8-6701-4b45-803c-5487bc083a3e", "507caec0-6f15-43b0-bb5d-bbc25f2cede3", "99588363-4295-4d0e-97c8-ff54f011e283", "0b9f88f8-2c68-4ab7-84d7-4e3ab1413662", "a11d2b52-9a5c-4f95-9c41-8f66eee9b9e1", "bb8ead1c-4bbc-4f12-8ca3-7eede1782eb2", "0739391a-bb4c-4622-8715-3dbf45a81859", "24fcf4b0-d72b-41e4-a7ee-2e0d85ca2116"], "durationId": "da727302-32ee-43b9-9808-0b30a57aa1ec", "parent": "0972d2bb-6239-4b57-bfde-a738a8079312"}}, {"head": {"id": "8cbbb866-f585-4ac6-bb31-67c086cecba9", "name": "default@PreviewArkTS watch work[23] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129336544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0972d2bb-6239-4b57-bfde-a738a8079312", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23082298349000, "endTime": 23129336670100}, "additional": {"logType": "error", "children": ["f050d1b6-00ed-431d-8f24-79c5a401b532"], "durationId": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d"}}, {"head": {"id": "fc5f7690-6f1d-414e-8ede-5ee7f5991fa4", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": **************}, "additional": {"logType": "debug", "children": [], "durationId": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d"}}, {"head": {"id": "471893d4-a251-4009-9319-6ed3311373b8", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:50:15\n Destructuring variable declarations are not supported (arkts-no-destruct-decls)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:84:12\n Array literals must contain elements of only inferrable types (arkts-no-noninferrable-arr-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:85:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:98:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:92:9\n Type '{ cardId: number; userId: number; cardNo: string; cardType: BankCardType.DEBIT; bankName: string; holderName: string; balance: number; creditLimit: number; isBound: BankCardStatus.BOUND; createTime: string; updateTime: string; }' is not assignable to type 'BankCard'.\n  Object literal may only specify known properties, and 'balance' does not exist in type 'BankCard'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/EditBankCardPage.ets:105:9\n Type '{ cardId: number; userId: number; cardNo: string; cardType: BankCardType.CREDIT; bankName: string; holderName: string; balance: number; creditLimit: number; isBound: BankCardStatus.BOUND; createTime: string; updateTime: string; }' is not assignable to type 'BankCard'.\n  Object literal may only specify known properties, and 'balance' does not exist in type 'BankCard'.\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": **************}, "additional": {"logType": "debug", "children": [], "durationId": "3ba30609-9cb6-45a1-a7ef-50cd0af35c2d"}}, {"head": {"id": "9087c959-ac64-4d78-b800-6ed7596541ce", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": **************, "endTime": **************}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2d378f7-ffe4-4e3e-8de0-2a545714342a", "logId": "fda7abb8-0b07-4ea0-b7fa-7e9e405f65ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda7abb8-0b07-4ea0-b7fa-7e9e405f65ae", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": **************, "endTime": **************}, "additional": {"logType": "info", "children": [], "durationId": "9087c959-ac64-4d78-b800-6ed7596541ce"}}, {"head": {"id": "9a0ebfae-9d60-477c-bd22-64472ddfc5ee", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23073961083400, "endTime": 23129386662600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 22, "minute": 15}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "19e3c6ca-978c-4618-892a-7e210441cfe5", "name": "BUILD FAILED in 55 s 426 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129386688900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "95a12720-854e-42c6-9f22-02f4a78acdfe", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129386852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884d503d-44f2-4199-b7f7-c4443419de2e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129386907800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef4f2b4-9753-4609-89ab-f5068a6c7d75", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129386951700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a583d9c-467e-455e-bc79-d2b1020466dc", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129386998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fccab49-894e-411e-a9a9-b7adea25e311", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129387046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8435417-bf2a-43ea-9836-be3cf676a662", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129387091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4717b9-f360-4323-ad3f-39e8fa6fd295", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129387130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fd29a5-f60f-42ba-bedd-0456529003b5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129410006900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b84c565-2d2e-47c7-94e3-658cac915075", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129410153000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1f66d81-6511-4410-b27b-0ab1f81451a4", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129410244400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d05bdff-518d-458d-bce3-09eb575c1ce0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129413503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b1f321-6cff-4320-99da-8165fe9d8a0a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129414363200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963ef69c-c085-4e5d-9c7b-190d4edd7c9d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129414664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3319d3d3-baa2-42d1-a641-bb9129e34be4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129414952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54198bc9-d253-4394-bf7b-d0ca9684d411", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129415756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e381c0-258c-4191-aa97-00b3b99c350e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129415828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "778554a2-00ea-4b6a-a843-5f91be74e860", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129416098200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f5f259-05e1-4813-8e2a-ea59a88f7b6b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129416371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeac9b3a-5477-4f7c-9284-f90f4be694b8", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129416689600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5b544b7-caad-43ad-b575-830b79e0305d", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 23129417816300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}