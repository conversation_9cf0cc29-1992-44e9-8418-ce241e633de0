if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardDetailPage_Params {
    cardDetail?: BankCard | null;
    isLoading?: boolean;
    cardId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import type { BankCard } from '../common/types/index';
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
class BankCardDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardDetail = new ObservedPropertyObjectPU(null, this, "cardDetail");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.__cardId = new ObservedPropertySimplePU(0, this, "cardId");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardDetailPage_Params) {
        if (params.cardDetail !== undefined) {
            this.cardDetail = params.cardDetail;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.cardId !== undefined) {
            this.cardId = params.cardId;
        }
    }
    updateStateVars(params: BankCardDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardDetail.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__cardId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardDetail.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__cardId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardDetail: ObservedPropertyObjectPU<BankCard | null>;
    get cardDetail() {
        return this.__cardDetail.get();
    }
    set cardDetail(newValue: BankCard | null) {
        this.__cardDetail.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __cardId: ObservedPropertySimplePU<number>;
    get cardId() {
        return this.__cardId.get();
    }
    set cardId(newValue: number) {
        this.__cardId.set(newValue);
    }
    aboutToAppear() {
        // 获取传入的银行卡ID
        const params = router.getParams() as Record<string, Object>;
        this.cardId = (params && params.cardId) ? params.cardId as number : 0;
        if (this.cardId > 0) {
            this.loadCardDetail();
        }
        else {
            promptAction.showToast({ message: '银行卡信息错误' });
            router.back();
        }
    }
    async loadCardDetail() {
        try {
            this.isLoading = true;
            this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
        }
        catch (error) {
            console.error('获取银行卡详情失败:', error);
            promptAction.showToast({ message: '获取银行卡详情失败' });
            router.back();
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(41:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(43:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('返回');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(44:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                router.back();
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡详情');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(51:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(57:9)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 加载状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(68:9)", "entry");
                        // 加载状态
                        Column.width('100%');
                        // 加载状态
                        Column.layoutWeight(1);
                        // 加载状态
                        Column.justifyContent(FlexAlign.Center);
                        // 加载状态
                        Column.alignItems(HorizontalAlign.Center);
                        // 加载状态
                        Column.backgroundColor('#F5F5F5');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(69:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(74:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    // 加载状态
                    Column.pop();
                });
            }
            else if (this.cardDetail) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡详情内容
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(86:9)", "entry");
                        // 银行卡详情内容
                        Scroll.layoutWeight(1);
                        // 银行卡详情内容
                        Scroll.backgroundColor('#F5F5F5');
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(87:11)", "entry");
                        Column.padding({ left: 16, right: 16, top: 16, bottom: 20 });
                    }, Column);
                    // 银行卡卡片
                    this.BankCardDisplay.bind(this)();
                    // 详细信息
                    this.CardDetailInfo.bind(this)();
                    // 操作按钮
                    this.ActionButtons.bind(this)();
                    Column.pop();
                    // 银行卡详情内容
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    BankCardDisplay(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(110:5)", "entry");
            Column.width('100%');
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡样式展示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(112:7)", "entry");
            // 银行卡样式展示
            Column.width('100%');
            // 银行卡样式展示
            Column.height(180);
            // 银行卡样式展示
            Column.padding(24);
            // 银行卡样式展示
            Column.borderRadius(16);
            // 银行卡样式展示
            Column.backgroundColor('#1976D2');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第一行：银行名称和卡片类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(114:9)", "entry");
            // 第一行：银行名称和卡片类型
            Row.width('100%');
            // 第一行：银行名称和卡片类型
            Row.margin({ bottom: 30 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.bankName || '中国工商银行');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(115:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCardTypeText());
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(121:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#E3F2FD');
        }, Text);
        Text.pop();
        // 第一行：银行名称和卡片类型
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第二行：卡号
            Text.create(`**** **** **** ${this.cardDetail?.cardNo.slice(-4) || '8888'}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(129:9)", "entry");
            // 第二行：卡号
            Text.fontSize(22);
            // 第二行：卡号
            Text.fontColor('#FFFFFF');
            // 第二行：卡号
            Text.fontWeight(FontWeight.Bold);
            // 第二行：卡号
            Text.letterSpacing(2);
            // 第二行：卡号
            Text.margin({ bottom: 20 });
        }, Text);
        // 第二行：卡号
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第三行：持卡人姓名和绑定状态
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(137:9)", "entry");
            // 第三行：持卡人姓名和绑定状态
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardDetail?.holderName || '罗曼勾');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(138:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#E3F2FD');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCardStatus());
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(143:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
            Text.backgroundColor('rgba(255, 255, 255, 0.2)');
            Text.padding({ left: 12, right: 12, top: 6, bottom: 6 });
            Text.borderRadius(12);
        }, Text);
        Text.pop();
        // 第三行：持卡人姓名和绑定状态
        Row.pop();
        // 银行卡样式展示
        Column.pop();
        Column.pop();
    }
    CardDetailInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(164:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡信息');
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(165:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.DetailItem.bind(this)('银行名称', this.cardDetail?.bankName || '中国工商银行');
        this.DetailItem.bind(this)('卡片类型', this.getCardTypeText());
        this.DetailItem.bind(this)('持卡人姓名', this.cardDetail?.holderName || '罗曼勾');
        this.DetailItem.bind(this)('卡号', this.formatCardNumber(this.cardDetail?.cardNo || '****************'));
        this.DetailItem.bind(this)('绑定状态', this.getCardStatus());
        this.DetailItem.bind(this)('绑定时间', this.formatDateTime(this.cardDetail?.createTime || '2025/06/23 14:30'));
        Column.pop();
    }
    DetailItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(188:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(189:7)", "entry");
            Row.width('100%');
            Row.height(50);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
            Row.padding({ left: 0, right: 0 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(190:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(195:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分隔线
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(208:7)", "entry");
            // 分隔线
            Divider.color('#F0F0F0');
            // 分隔线
            Divider.strokeWidth(0.5);
            // 分隔线
            Divider.margin({ top: 8 });
        }, Divider);
        Column.pop();
    }
    ActionButtons(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(218:5)", "entry");
            Column.width('100%');
            Column.padding({ left: 20, right: 20, top: 30, bottom: 50 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('解绑银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardDetailPage.ets(219:7)", "entry");
            Button.width('100%');
            Button.height(50);
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#F44336');
            Button.borderRadius(25);
            Button.onClick(() => {
                this.confirmUnbindCard();
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    // 工具方法
    getCardStatus(): string {
        return this.cardDetail?.isBound === 1 ? '已绑定' : '未绑定';
    }
    getCardStatusColor(): string {
        return this.cardDetail?.isBound === 1 ? '#4CAF50' : '#F44336';
    }
    getCardStatusBgColor(): string {
        return this.cardDetail?.isBound === 1 ? '#E8F5E8' : '#FFEBEE';
    }
    getCardTypeText(): string {
        if (!this.cardDetail?.cardType)
            return '储蓄卡';
        return this.cardDetail.cardType;
    }
    formatCardNumber(cardNo: string): string {
        if (!cardNo || cardNo.length < 8) {
            return cardNo;
        }
        // 根据图片显示格式：8888 ********** 8888
        const start = cardNo.substring(0, 4);
        const end = cardNo.substring(cardNo.length - 4);
        const middle = '*'.repeat(10); // 固定10个星号
        return `${start} ${middle} ${end}`;
    }
    formatDateTime(dateTime: string): string {
        if (!dateTime)
            return '2025/06/23 14:30';
        // 如果已经是正确格式，直接返回
        if (dateTime.includes('/')) {
            return dateTime;
        }
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        return `${year}/${month}/${day} ${hour}:${minute}`;
    }
    async confirmUnbindCard() {
        try {
            const result = await promptAction.showDialog({
                title: '确认解绑',
                message: `确定要解绑银行卡 ${this.cardDetail?.bankName}(${this.cardDetail?.cardNo.slice(-4)}) 吗？`,
                buttons: [
                    { text: '确定', color: '#F44336' },
                    { text: '取消', color: '#666666' }
                ]
            });
            if (result.index === 0) {
                await this.unbindCard();
            }
        }
        catch (error) {
            console.error('显示确认对话框失败:', error);
        }
    }
    async unbindCard() {
        try {
            await BankCardApi.unbindCard(this.cardId);
            promptAction.showToast({ message: '解绑成功' });
            // 通知银行卡列表页面刷新
            console.log('BankCardDetailPage - 设置银行卡解绑事件标志');
            tempDataManager.setData('BANK_CARD_UNBOUND', true);
            // 返回银行卡列表页
            router.back();
        }
        catch (error) {
            console.error('解绑银行卡失败:', error);
            promptAction.showToast({ message: '解绑失败，请重试' });
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardDetailPage";
    }
}
registerNamedRoute(() => new BankCardDetailPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardDetailPage", pageFullPath: "entry/src/main/ets/pages/BankCardDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
