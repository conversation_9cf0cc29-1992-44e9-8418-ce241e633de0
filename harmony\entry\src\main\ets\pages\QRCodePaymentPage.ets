import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi, PaymentRequest } from '../api/TransactionApi';

@Entry
@Component
struct QRCodePaymentPage {
  @State isScanning: boolean = false;

  aboutToAppear() {
    console.log('QRCodePaymentPage 页面加载成功');
  }

  // 处理支付完成
  private async handlePaymentComplete() {
    try {
      // 调用支付API生成交易记录
      await TransactionApi.payment({
        userId: 1, // 临时使用固定用户ID
        amount: 100.00, // 临时使用固定金额
        description: '扫码支付',
        paymentMethod: 'QR_CODE'
      });

      promptAction.showToast({
        message: '扫码支付完成！',
        duration: 2000
      });

      // 延迟跳转到交易记录页面
      setTimeout(() => {
        router.replaceUrl({
          url: 'pages/TransactionListPage'
        });
      }, 2000);
    } catch (error) {
      console.error('支付失败:', error);
      promptAction.showToast({
        message: '支付失败，请重试',
        duration: 2000
      });
    }
  }

  // 取消支付
  private handleCancel() {
    router.back();
  }

  // 模拟扫码
  private startScanning() {
    this.isScanning = true;

    // 模拟扫码过程
    setTimeout(() => {
      this.isScanning = false;
      promptAction.showToast({
        message: '扫码成功，正在处理支付...',
        duration: 2000
      });

      // 模拟支付处理
      setTimeout(() => {
        this.handlePaymentComplete();
      }, 2000);
    }, 3000);
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('×')
          .fontSize(24)
          .fontColor('#666666')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('扫码支付')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({
        width: { bottom: 1 },
        color: '#E5E5E5'
      })

      // 扫码内容区域
      Column() {
        // 提示文字
        Text('请扫描下方二维码完成支付')
          .fontSize(16)
          .fontColor('#666666')
          .margin({ top: 40, bottom: 40 })

        // 二维码区域
        Column() {
          if (this.isScanning) {
            // 扫描中状态
            Column() {
              LoadingProgress()
                .width(40)
                .height(40)
                .color('#4A90E2')
                .margin({ bottom: 20 })

              Text('正在扫描...')
                .fontSize(16)
                .fontColor('#4A90E2')
            }
            .width(200)
            .height(200)
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
            .backgroundColor('#F8F9FA')
            .border({
              width: 2,
              color: '#E5E5E5',
              style: BorderStyle.Dashed
            })
            .borderRadius(12)
          } else {
            // 二维码显示
            Column() {
              // 模拟二维码图标
              Text('📱')
                .fontSize(60)
                .margin({ bottom: 20 })

              Text('扫码支付')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
            }
            .width(200)
            .height(200)
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
            .backgroundColor('#F8F9FA')
            .border({
              width: 2,
              color: '#E5E5E5',
              style: BorderStyle.Dashed
            })
            .borderRadius(12)
            .onClick(() => {
              if (!this.isScanning) {
                this.startScanning();
              }
            })
          }
        }
        .margin({ bottom: 40 })

        // 支付方式说明
        Text('使用支付宝、微信扫码支付')
          .fontSize(14)
          .fontColor('#999999')
          .margin({ bottom: 60 })

        // 操作按钮
        Row() {
          Button('取消')
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .width(100)
            .height(44)
            .onClick(() => {
              this.handleCancel();
            })

          Button('支付完成')
            .fontSize(16)
            .fontColor('#FFFFFF')
            .backgroundColor('#4A90E2')
            .borderRadius(8)
            .width(120)
            .height(44)
            .margin({ left: 20 })
            .onClick(() => {
              this.handlePaymentComplete();
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
      }
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#FFFFFF')
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }
}
