import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi, PaymentRequest } from '../api/TransactionApi';

@Entry
@Component
struct NFCPaymentPage {
  @State isWaiting: boolean = false;

  aboutToAppear() {
    console.log('NFCPaymentPage 页面加载成功');
  }

  // 处理支付完成
  private async handlePaymentComplete() {
    try {
      // 调用支付API生成交易记录
      await TransactionApi.payment({
        userId: 1, // 临时使用固定用户ID
        amount: 100.00, // 临时使用固定金额
        description: 'NFC支付',
        paymentMethod: 'NFC'
      });

      promptAction.showToast({
        message: 'NFC支付成功！',
        duration: 2000
      });

      // 延迟跳转到交易记录页面
      setTimeout(() => {
        router.replaceUrl({
          url: 'pages/TransactionListPage'
        });
      }, 2000);
    } catch (error) {
      console.error('支付失败:', error);
      promptAction.showToast({
        message: '支付失败，请重试',
        duration: 2000
      });
    }
  }

  // 取消支付
  private handleCancel() {
    router.back();
  }

  // 开始等待NFC设备
  private startWaitingForNFC() {
    this.isWaiting = true;

    // 模拟NFC检测过程
    setTimeout(() => {
      this.isWaiting = false;
      promptAction.showToast({
        message: '检测到NFC设备，正在处理支付...',
        duration: 2000
      });

      // 模拟支付处理
      setTimeout(() => {
        this.handlePaymentComplete();
      }, 2000);
    }, 4000);
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('×')
          .fontSize(24)
          .fontColor('#666666')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('NFC支付')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({
        width: { bottom: 1 },
        color: '#E5E5E5'
      })

      // NFC支付内容区域
      Column() {
        // NFC图标和状态
        Column() {
          if (this.isWaiting) {
            // 等待中状态
            Column() {
              // 动画效果的NFC图标
              Text('📡')
                .fontSize(80)
                .margin({ bottom: 20 })

              LoadingProgress()
                .width(40)
                .height(40)
                .color('#4A90E2')
                .margin({ bottom: 20 })

              Text('正在等待NFC设备...')
                .fontSize(16)
                .fontColor('#4A90E2')
                .fontWeight(FontWeight.Medium)
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
          } else {
            // 等待状态
            Column() {
              Text('📡')
                .fontSize(80)
                .margin({ bottom: 20 })

              Text('等待NFC设备')
                .fontSize(20)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .margin({ bottom: 10 })

              Text('请将支持NFC的设备靠近感应区域')
                .fontSize(14)
                .fontColor('#666666')
                .textAlign(TextAlign.Center)
                .margin({ bottom: 40 })

              // 开始检测按钮
              Button('开始NFC支付')
                .fontSize(16)
                .fontColor('#FFFFFF')
                .backgroundColor('#4A90E2')
                .borderRadius(8)
                .width(160)
                .height(44)
                .onClick(() => {
                  if (!this.isWaiting) {
                    this.startWaitingForNFC();
                  }
                })
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
          }
        }
        .layoutWeight(1)
        .width('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)

        // 底部操作按钮
        Row() {
          Button('取消')
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E5E5E5' })
            .borderRadius(8)
            .width(100)
            .height(44)
            .onClick(() => {
              this.handleCancel();
            })

          if (this.isWaiting) {
            Button('停止检测')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .backgroundColor('#F44336')
              .borderRadius(8)
              .width(120)
              .height(44)
              .margin({ left: 20 })
              .onClick(() => {
                this.isWaiting = false;
              })
          } else {
            Button('确认NFC支付')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .backgroundColor('#4A90E2')
              .borderRadius(8)
              .width(140)
              .height(44)
              .margin({ left: 20 })
              .onClick(() => {
                this.handlePaymentComplete();
              })
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 40 })
      }
      .layoutWeight(1)
      .padding(20)
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }
}
