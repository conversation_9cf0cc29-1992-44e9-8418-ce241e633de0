{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "3cad35f9-bb22-4551-b418-a525deba5a30", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942717625900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6b2d07-0fb1-4147-adef-2a1c5f6c080d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942849050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9330d4-57b4-44b8-9a4b-9182724c9f48", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21942849255800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5222d0a8-d9ce-4a32-89ca-52ed5926fb51", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944549456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944564363700, "endTime": 21944879800800}, "additional": {"children": ["57944bcd-7a81-4a0b-ae6e-98f15f1ba4d3", "d87ae34f-9f79-4fe0-8198-b584c0045b69", "68af3533-5f48-44da-b920-de88b0774302", "efe04bbe-07b6-4ac8-b959-acbef6765b9b", "c1c65062-0754-4747-9491-05e3ad2d9d3f", "244848f0-ea87-46e6-a10f-767d0ac434a4", "c37ac0b2-0cd6-4a46-9138-6f7c313d93a9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57944bcd-7a81-4a0b-ae6e-98f15f1ba4d3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944564366000, "endTime": 21944578345100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "add89954-2843-4194-9348-b3d26e67d160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944578359600, "endTime": 21944878157100}, "additional": {"children": ["0de874c7-1de3-45fd-b782-368257fd2c69", "e281928d-597d-4fba-b635-313902c86096", "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "ff868cb3-fb24-4aa8-9b4b-e474326bff06", "7174b4c2-ff8a-457a-a726-a719ebb5ff04", "8ff2eb3e-b864-48cf-b7de-1eea5e74eaf7", "299f3e9f-18f5-4b6e-b16f-ca816c2aabcc", "f2ef23b2-bb94-4d91-ba88-a278af6dae0e", "4f8e866f-994a-4900-b9f5-7b0d65c7944c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68af3533-5f48-44da-b920-de88b0774302", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944878181100, "endTime": 21944879788100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "dff22dd0-2028-46e6-9b76-e7dd69370d65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efe04bbe-07b6-4ac8-b959-acbef6765b9b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944879794800, "endTime": 21944879796700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "1748a6ec-ba93-49a6-a7c6-e5ab59fd8e57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1c65062-0754-4747-9491-05e3ad2d9d3f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944569064900, "endTime": 21944569102500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "08262f55-115b-47ef-9870-032a5be64090"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08262f55-115b-47ef-9870-032a5be64090", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944569064900, "endTime": 21944569102500}, "additional": {"logType": "info", "children": [], "durationId": "c1c65062-0754-4747-9491-05e3ad2d9d3f", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "244848f0-ea87-46e6-a10f-767d0ac434a4", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944573877000, "endTime": 21944573887500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "2854b570-1570-47d3-b15f-6ee10de006db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2854b570-1570-47d3-b15f-6ee10de006db", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944573877000, "endTime": 21944573887500}, "additional": {"logType": "info", "children": [], "durationId": "244848f0-ea87-46e6-a10f-767d0ac434a4", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "0e9b77d2-7326-4f6d-83e1-d8f843859363", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944573951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e9b3a5-845c-4d4e-b8b5-bc52fc78e61e", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944578261100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add89954-2843-4194-9348-b3d26e67d160", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944564366000, "endTime": 21944578345100}, "additional": {"logType": "info", "children": [], "durationId": "57944bcd-7a81-4a0b-ae6e-98f15f1ba4d3", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "0de874c7-1de3-45fd-b782-368257fd2c69", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944583912300, "endTime": 21944583919600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "1411f822-e114-49e6-a574-7f227c38a9a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e281928d-597d-4fba-b635-313902c86096", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944583932700, "endTime": 21944588516800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "b2b1a544-cf97-4b0f-9af6-77632f895160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944588528900, "endTime": 21944713228200}, "additional": {"children": ["34b5af59-822f-4a76-a223-7c1df2811109", "1244a39f-e627-44db-b885-535341cc1ce1", "9e87c5ae-dd48-432c-a3bd-6e63283c4d4d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "7f30b0ca-a9c3-4bad-ab44-db15b82eb876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff868cb3-fb24-4aa8-9b4b-e474326bff06", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944713245500, "endTime": 21944735676800}, "additional": {"children": ["54e4f107-2b46-418c-af3a-8f633d275b18"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "61562c3a-bcef-44e1-98d8-c304ad1b25eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7174b4c2-ff8a-457a-a726-a719ebb5ff04", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944735697100, "endTime": 21944855720200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "31db5f68-63f0-4b5f-94ed-6bf0aa7aaa22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ff2eb3e-b864-48cf-b7de-1eea5e74eaf7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944856935200, "endTime": 21944866270600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "b58bcb9d-02bf-4929-becd-664361411250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "299f3e9f-18f5-4b6e-b16f-ca816c2aabcc", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944866296500, "endTime": 21944877972300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "6005aa47-885a-42f4-9b7c-9ec703b808bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2ef23b2-bb94-4d91-ba88-a278af6dae0e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944878019100, "endTime": 21944878141800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "dd1d97b2-587f-4a71-8136-d82835362c46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1411f822-e114-49e6-a574-7f227c38a9a7", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944583912300, "endTime": 21944583919600}, "additional": {"logType": "info", "children": [], "durationId": "0de874c7-1de3-45fd-b782-368257fd2c69", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "b2b1a544-cf97-4b0f-9af6-77632f895160", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944583932700, "endTime": 21944588516800}, "additional": {"logType": "info", "children": [], "durationId": "e281928d-597d-4fba-b635-313902c86096", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "34b5af59-822f-4a76-a223-7c1df2811109", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944589188800, "endTime": 21944589205900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "logId": "6cde7cb5-f3bb-44a0-a1e4-a71b176d25bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cde7cb5-f3bb-44a0-a1e4-a71b176d25bf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944589188800, "endTime": 21944589205900}, "additional": {"logType": "info", "children": [], "durationId": "34b5af59-822f-4a76-a223-7c1df2811109", "parent": "7f30b0ca-a9c3-4bad-ab44-db15b82eb876"}}, {"head": {"id": "1244a39f-e627-44db-b885-535341cc1ce1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944591199700, "endTime": 21944711793900}, "additional": {"children": ["c0000ac4-0211-408a-9296-3d4587046a02", "d5e9cba4-53f6-44b5-b37d-4bcaf4a418ef"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "logId": "53094eec-8eb2-491d-95da-a8a39fc6a7aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0000ac4-0211-408a-9296-3d4587046a02", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944591200800, "endTime": 21944595191200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1244a39f-e627-44db-b885-535341cc1ce1", "logId": "beb2dbb5-9a3a-4a12-8d89-3f0bd895b3d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5e9cba4-53f6-44b5-b37d-4bcaf4a418ef", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944595229900, "endTime": 21944711734800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1244a39f-e627-44db-b885-535341cc1ce1", "logId": "1799f796-2444-4238-bf94-0783c833e210"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9eec2e4b-818f-488d-859e-1503f6efaa42", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944591204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca354cb-4e29-4223-bb88-7450d06d6a39", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944595077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb2dbb5-9a3a-4a12-8d89-3f0bd895b3d9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944591200800, "endTime": 21944595191200}, "additional": {"logType": "info", "children": [], "durationId": "c0000ac4-0211-408a-9296-3d4587046a02", "parent": "53094eec-8eb2-491d-95da-a8a39fc6a7aa"}}, {"head": {"id": "345bd791-559b-46a2-aa3a-073759ee1958", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944595240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "264ce263-04e9-4739-942e-f38d3e297111", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944603074400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972c1416-f74a-4c0b-8f3c-55ff8325eb51", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944603170100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e531188-1cce-46a8-b8f2-65d7e94f16b1", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944603285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edcb7c89-4641-48b9-af0d-966652da446a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944603363900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2aac378-c090-40b8-9421-08acf41c2a27", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944604908900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b45eb16f-a16b-4cf4-a162-2d76de730a72", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944632765800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691d9e49-dec1-42cb-b157-1ac7aae1ed82", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944648261500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab943bf-c06c-4c02-808e-8b7688e1738b", "name": "Sdk init in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944679086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189586dc-9fec-4769-9b72-80d0fe7d5292", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944679205300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 55}, "markType": "other"}}, {"head": {"id": "33f7b246-82a3-45fd-b6ca-7bad91b8b93b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944679217500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 55}, "markType": "other"}}, {"head": {"id": "65ff193e-29b8-48ef-b797-ad023d852b04", "name": "Project task initialization takes 31 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944711235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8c7b17-b21d-4ca6-af02-f824c7190465", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944711418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f30d26e-2127-47c7-ad84-d5377c2eae6d", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944711531400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431df80a-f341-4b90-8f05-4ec8da2175ea", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944711625700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1799f796-2444-4238-bf94-0783c833e210", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944595229900, "endTime": 21944711734800}, "additional": {"logType": "info", "children": [], "durationId": "d5e9cba4-53f6-44b5-b37d-4bcaf4a418ef", "parent": "53094eec-8eb2-491d-95da-a8a39fc6a7aa"}}, {"head": {"id": "53094eec-8eb2-491d-95da-a8a39fc6a7aa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944591199700, "endTime": 21944711793900}, "additional": {"logType": "info", "children": ["beb2dbb5-9a3a-4a12-8d89-3f0bd895b3d9", "1799f796-2444-4238-bf94-0783c833e210"], "durationId": "1244a39f-e627-44db-b885-535341cc1ce1", "parent": "7f30b0ca-a9c3-4bad-ab44-db15b82eb876"}}, {"head": {"id": "9e87c5ae-dd48-432c-a3bd-6e63283c4d4d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944713188200, "endTime": 21944713207300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "logId": "f6a6df03-ede7-4a49-a4ee-557489a4664d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6a6df03-ede7-4a49-a4ee-557489a4664d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944713188200, "endTime": 21944713207300}, "additional": {"logType": "info", "children": [], "durationId": "9e87c5ae-dd48-432c-a3bd-6e63283c4d4d", "parent": "7f30b0ca-a9c3-4bad-ab44-db15b82eb876"}}, {"head": {"id": "7f30b0ca-a9c3-4bad-ab44-db15b82eb876", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944588528900, "endTime": 21944713228200}, "additional": {"logType": "info", "children": ["6cde7cb5-f3bb-44a0-a1e4-a71b176d25bf", "53094eec-8eb2-491d-95da-a8a39fc6a7aa", "f6a6df03-ede7-4a49-a4ee-557489a4664d"], "durationId": "95ad6ee9-971e-434c-bdc7-2671cee6fd50", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "54e4f107-2b46-418c-af3a-8f633d275b18", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944714134000, "endTime": 21944735664100}, "additional": {"children": ["5d4448f7-09b4-4df7-a22b-9b8eeaffca46", "c1681a92-12c5-4b44-bd6e-8f8a299380d7", "d16d51b2-cbd9-4935-b2b4-f9f5d9b1edf0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff868cb3-fb24-4aa8-9b4b-e474326bff06", "logId": "9b43bfab-8f80-49c7-93e1-11273bfb71ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d4448f7-09b4-4df7-a22b-9b8eeaffca46", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944718168500, "endTime": 21944718188600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54e4f107-2b46-418c-af3a-8f633d275b18", "logId": "e671cd33-d3d1-418d-8e8f-f474967a3770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e671cd33-d3d1-418d-8e8f-f474967a3770", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944718168500, "endTime": 21944718188600}, "additional": {"logType": "info", "children": [], "durationId": "5d4448f7-09b4-4df7-a22b-9b8eeaffca46", "parent": "9b43bfab-8f80-49c7-93e1-11273bfb71ed"}}, {"head": {"id": "c1681a92-12c5-4b44-bd6e-8f8a299380d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944721109900, "endTime": 21944733439000}, "additional": {"children": ["87e546f0-db1b-4e19-be4b-8c8d3205c033", "d15db9dd-da17-4176-9c22-d9e8e622468f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54e4f107-2b46-418c-af3a-8f633d275b18", "logId": "255d94be-a7da-4af9-9e0d-90b995349106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e546f0-db1b-4e19-be4b-8c8d3205c033", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944721111400, "endTime": 21944723434700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1681a92-12c5-4b44-bd6e-8f8a299380d7", "logId": "5f65ea26-d4b0-4b1f-911a-6cfcf261a926"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d15db9dd-da17-4176-9c22-d9e8e622468f", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944723449600, "endTime": 21944733427300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1681a92-12c5-4b44-bd6e-8f8a299380d7", "logId": "d7b898fe-2068-44a0-a094-4ec4dec4e889"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6d7c3ee-bb58-4aae-8b04-1b5ffc96b96d", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944721116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ffcda69-250e-4eb7-be46-f3389ef16401", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944723339500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f65ea26-d4b0-4b1f-911a-6cfcf261a926", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944721111400, "endTime": 21944723434700}, "additional": {"logType": "info", "children": [], "durationId": "87e546f0-db1b-4e19-be4b-8c8d3205c033", "parent": "255d94be-a7da-4af9-9e0d-90b995349106"}}, {"head": {"id": "c6fab76e-c3d5-4908-8778-a1d2845e43ff", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944723465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3005f4ee-e576-4aef-8756-0e89a04c618f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944729764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c11b02b-9074-44fa-b238-a07fa703ea36", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944729874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5d018d3-c5b7-4a08-9d67-54677deb2fc1", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944730046800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4e96fa-183f-4bd1-8b86-0c70414a1df3", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944730163900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cb16ed-72b6-418d-9710-053e2ba569ab", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944730217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb73d09-7433-4940-813c-f0d920160979", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944730261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64f6866-bc5a-4294-b0b8-1d28e488997b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944730306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3292fc-038a-41b7-8038-ba1fe9fcab72", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944733188300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37549d17-b48a-4b7b-8d3e-86d1a0774891", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944733293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe59a5ae-a827-4049-af9f-940bf7057a39", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944733345700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5843fbf1-98c6-4fae-8d1b-a8f0579dea76", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944733387500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b898fe-2068-44a0-a094-4ec4dec4e889", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944723449600, "endTime": 21944733427300}, "additional": {"logType": "info", "children": [], "durationId": "d15db9dd-da17-4176-9c22-d9e8e622468f", "parent": "255d94be-a7da-4af9-9e0d-90b995349106"}}, {"head": {"id": "255d94be-a7da-4af9-9e0d-90b995349106", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944721109900, "endTime": 21944733439000}, "additional": {"logType": "info", "children": ["5f65ea26-d4b0-4b1f-911a-6cfcf261a926", "d7b898fe-2068-44a0-a094-4ec4dec4e889"], "durationId": "c1681a92-12c5-4b44-bd6e-8f8a299380d7", "parent": "9b43bfab-8f80-49c7-93e1-11273bfb71ed"}}, {"head": {"id": "d16d51b2-cbd9-4935-b2b4-f9f5d9b1edf0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944735623100, "endTime": 21944735645200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "54e4f107-2b46-418c-af3a-8f633d275b18", "logId": "33e0afa7-9d8f-4d32-9698-270bda3c0337"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33e0afa7-9d8f-4d32-9698-270bda3c0337", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944735623100, "endTime": 21944735645200}, "additional": {"logType": "info", "children": [], "durationId": "d16d51b2-cbd9-4935-b2b4-f9f5d9b1edf0", "parent": "9b43bfab-8f80-49c7-93e1-11273bfb71ed"}}, {"head": {"id": "9b43bfab-8f80-49c7-93e1-11273bfb71ed", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944714134000, "endTime": 21944735664100}, "additional": {"logType": "info", "children": ["e671cd33-d3d1-418d-8e8f-f474967a3770", "255d94be-a7da-4af9-9e0d-90b995349106", "33e0afa7-9d8f-4d32-9698-270bda3c0337"], "durationId": "54e4f107-2b46-418c-af3a-8f633d275b18", "parent": "61562c3a-bcef-44e1-98d8-c304ad1b25eb"}}, {"head": {"id": "61562c3a-bcef-44e1-98d8-c304ad1b25eb", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944713245500, "endTime": 21944735676800}, "additional": {"logType": "info", "children": ["9b43bfab-8f80-49c7-93e1-11273bfb71ed"], "durationId": "ff868cb3-fb24-4aa8-9b4b-e474326bff06", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "9390ce0d-d2a5-43ea-8f16-a0c76933be13", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944855340300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec2b7ab-08ab-483a-bf69-bd76d1986228", "name": "hvigorfile, resolve hvigorfile dependencies in 120 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944855653400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31db5f68-63f0-4b5f-94ed-6bf0aa7aaa22", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944735697100, "endTime": 21944855720200}, "additional": {"logType": "info", "children": [], "durationId": "7174b4c2-ff8a-457a-a726-a719ebb5ff04", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "4f8e866f-994a-4900-b9f5-7b0d65c7944c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944856742900, "endTime": 21944856922100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "logId": "51d57b3a-f754-4082-b862-5fd621e6c38b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c838b6c5-5aa2-41b4-ba45-66b0557489da", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944856760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d57b3a-f754-4082-b862-5fd621e6c38b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944856742900, "endTime": 21944856922100}, "additional": {"logType": "info", "children": [], "durationId": "4f8e866f-994a-4900-b9f5-7b0d65c7944c", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "6276a2f4-9385-4bc3-99ba-86e0bf5aafcf", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944858586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad7e423-d234-4fca-88e0-a9e684eaa524", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944865256000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58bcb9d-02bf-4929-becd-664361411250", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944856935200, "endTime": 21944866270600}, "additional": {"logType": "info", "children": [], "durationId": "8ff2eb3e-b864-48cf-b7de-1eea5e74eaf7", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "010d9d2f-64a5-43c9-99ce-dcf46fac4e36", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944866309900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20775d4-6b03-4a80-a27e-6d1a873f3340", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944872769600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7f4c27-e967-4ed8-a3a2-81e1fd3ef51a", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944872882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88bd4029-e9eb-45ec-95ad-1bdc491108d4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944873069200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764eab40-c5f9-4f73-b641-747495eb680a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944875229000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394ee3a1-f868-40d5-b299-9d68d75cfabe", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944875297100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6005aa47-885a-42f4-9b7c-9ec703b808bf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944866296500, "endTime": 21944877972300}, "additional": {"logType": "info", "children": [], "durationId": "299f3e9f-18f5-4b6e-b16f-ca816c2aabcc", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "63c35d0f-c112-43f5-a53f-cb117ed1d376", "name": "Configuration phase cost:295 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944878044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1d97b2-587f-4a71-8136-d82835362c46", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944878019100, "endTime": 21944878141800}, "additional": {"logType": "info", "children": [], "durationId": "f2ef23b2-bb94-4d91-ba88-a278af6dae0e", "parent": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80"}}, {"head": {"id": "4f5f900f-c59e-4c3a-ad42-8e1b35755a80", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944578359600, "endTime": 21944878157100}, "additional": {"logType": "info", "children": ["1411f822-e114-49e6-a574-7f227c38a9a7", "b2b1a544-cf97-4b0f-9af6-77632f895160", "7f30b0ca-a9c3-4bad-ab44-db15b82eb876", "61562c3a-bcef-44e1-98d8-c304ad1b25eb", "31db5f68-63f0-4b5f-94ed-6bf0aa7aaa22", "b58bcb9d-02bf-4929-becd-664361411250", "6005aa47-885a-42f4-9b7c-9ec703b808bf", "dd1d97b2-587f-4a71-8136-d82835362c46", "51d57b3a-f754-4082-b862-5fd621e6c38b"], "durationId": "d87ae34f-9f79-4fe0-8198-b584c0045b69", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "c37ac0b2-0cd6-4a46-9138-6f7c313d93a9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944879749600, "endTime": 21944879771000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44b5597d-bc22-4d83-94aa-3928d8d0e15d", "logId": "115d3c1e-0ef3-460f-847f-030de95cf81f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "115d3c1e-0ef3-460f-847f-030de95cf81f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944879749600, "endTime": 21944879771000}, "additional": {"logType": "info", "children": [], "durationId": "c37ac0b2-0cd6-4a46-9138-6f7c313d93a9", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "dff22dd0-2028-46e6-9b76-e7dd69370d65", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944878181100, "endTime": 21944879788100}, "additional": {"logType": "info", "children": [], "durationId": "68af3533-5f48-44da-b920-de88b0774302", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "1748a6ec-ba93-49a6-a7c6-e5ab59fd8e57", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944879794800, "endTime": 21944879796700}, "additional": {"logType": "info", "children": [], "durationId": "efe04bbe-07b6-4ac8-b959-acbef6765b9b", "parent": "9f0742fd-0446-48e2-81dc-7cecbcff7631"}}, {"head": {"id": "9f0742fd-0446-48e2-81dc-7cecbcff7631", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944564363700, "endTime": 21944879800800}, "additional": {"logType": "info", "children": ["add89954-2843-4194-9348-b3d26e67d160", "4f5f900f-c59e-4c3a-ad42-8e1b35755a80", "dff22dd0-2028-46e6-9b76-e7dd69370d65", "1748a6ec-ba93-49a6-a7c6-e5ab59fd8e57", "08262f55-115b-47ef-9870-032a5be64090", "2854b570-1570-47d3-b15f-6ee10de006db", "115d3c1e-0ef3-460f-847f-030de95cf81f"], "durationId": "44b5597d-bc22-4d83-94aa-3928d8d0e15d"}}, {"head": {"id": "ad80a2aa-3888-4f22-9c97-dc4765308dd1", "name": "Configuration task cost before running: 324 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944880047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b4dd5c-c9ad-483e-9038-0813dbc1341e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944888134200, "endTime": 21944897984200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "87dd12fb-af0d-4c8a-8382-a62c528c9b6c", "logId": "5542698c-80a8-4242-9b9c-2d9563246d82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87dd12fb-af0d-4c8a-8382-a62c528c9b6c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944882220200}, "additional": {"logType": "detail", "children": [], "durationId": "46b4dd5c-c9ad-483e-9038-0813dbc1341e"}}, {"head": {"id": "b55185c7-685d-4541-9e9b-1a6a281cd2cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944882918800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc430a18-1465-47a6-91bc-48774b3c5f1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944883038800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4643ecd6-bf55-4e13-92ec-c716b4b29d34", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944888158200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1aa5f8f-1501-4b1b-b2c4-0c32995bbce6", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944897749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb8c37ab-e881-45a2-a5de-5979de01b542", "name": "entry : default@PreBuild cost memory -1.500274658203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944897915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5542698c-80a8-4242-9b9c-2d9563246d82", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944888134200, "endTime": 21944897984200}, "additional": {"logType": "info", "children": [], "durationId": "46b4dd5c-c9ad-483e-9038-0813dbc1341e"}}, {"head": {"id": "827cd58d-4133-4da3-a45d-f309e359ce24", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944902837800, "endTime": 21944904786100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "59668c85-07d7-46ad-9cce-c5121e797e92", "logId": "ae1b87f2-1cad-4b91-909f-cc75c7172124"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59668c85-07d7-46ad-9cce-c5121e797e92", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944901520600}, "additional": {"logType": "detail", "children": [], "durationId": "827cd58d-4133-4da3-a45d-f309e359ce24"}}, {"head": {"id": "4aec9b61-63b3-4a4d-9992-143df6739e3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944902056200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d7a276-ad20-453e-a7c9-aceba7d0ab1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944902148900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a183a8ef-1174-4c49-a318-987ace796cbf", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944902846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241130a1-17ed-4fb3-aeac-6db0d95bb21a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944904620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733ebfab-68fb-4fa9-9e08-7913b20a7436", "name": "entry : default@MergeProfile cost memory 0.10587310791015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944904719500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1b87f2-1cad-4b91-909f-cc75c7172124", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944902837800, "endTime": 21944904786100}, "additional": {"logType": "info", "children": [], "durationId": "827cd58d-4133-4da3-a45d-f309e359ce24"}}, {"head": {"id": "58023aba-3b00-4fa7-ae04-0721f704a30c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944907610100, "endTime": 21944909957900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c6586559-241c-4dfd-8fd8-103b30f9c313", "logId": "8d05494c-1f02-4a9b-816c-e33394626182"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6586559-241c-4dfd-8fd8-103b30f9c313", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944906303500}, "additional": {"logType": "detail", "children": [], "durationId": "58023aba-3b00-4fa7-ae04-0721f704a30c"}}, {"head": {"id": "6f7048d2-8fba-4e89-ade6-25f8c015a576", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944906779100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e46478d-a04a-4e1e-bbe2-a3a2d20ce162", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944906862900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9182b88-2b04-4f87-8fc8-26407905cb3a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944907619400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8561d3fd-b34b-4c97-b73d-3e1051c1eaa8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944908496400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a015e54e-09d9-41f0-a7b8-d747f60e95fe", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944909741500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f1b5d1-289b-4b6b-b55a-4549c38de51d", "name": "entry : default@CreateBuildProfile cost memory 0.09841156005859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944909866700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d05494c-1f02-4a9b-816c-e33394626182", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944907610100, "endTime": 21944909957900}, "additional": {"logType": "info", "children": [], "durationId": "58023aba-3b00-4fa7-ae04-0721f704a30c"}}, {"head": {"id": "ec3c6222-db27-4f3d-a620-18a3c306fb3c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944912664000, "endTime": 21944913209000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d63c24c0-4316-4ec0-b31b-8ec6d9c34dac", "logId": "e70f82d0-b098-48f2-9fad-bf12792510bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d63c24c0-4316-4ec0-b31b-8ec6d9c34dac", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944911384400}, "additional": {"logType": "detail", "children": [], "durationId": "ec3c6222-db27-4f3d-a620-18a3c306fb3c"}}, {"head": {"id": "78ba414c-94aa-4e5d-9596-1407bbce3eaa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944911876000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3590b54c-6d65-4887-aac2-9c7841745c4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944911943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d1e4a7-214d-46a1-b796-08aad10a10c1", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944912672200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7417e82-77ca-48b4-8098-14713c7a8bd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944912807000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9600b2be-11ec-4b62-9c1f-2f6d72c7af26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944912916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "887348aa-9574-49e1-a304-1cd02637318e", "name": "entry : default@PreCheckSyscap cost memory 0.036895751953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944913028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ee6c31-062a-42c7-ad09-4452c865e623", "name": "runTaskFromQueue task cost before running: 357 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944913131400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70f82d0-b098-48f2-9fad-bf12792510bb", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944912664000, "endTime": 21944913209000, "totalTime": 444400}, "additional": {"logType": "info", "children": [], "durationId": "ec3c6222-db27-4f3d-a620-18a3c306fb3c"}}, {"head": {"id": "4fe6debd-e49d-4134-a415-b65d5bd31e18", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922077900, "endTime": 21944923001200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eaa68e94-eeba-40fd-873f-f0d668557058", "logId": "a7737f34-ebe4-4c61-8ea9-817a9f30a625"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaa68e94-eeba-40fd-873f-f0d668557058", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944914890000}, "additional": {"logType": "detail", "children": [], "durationId": "4fe6debd-e49d-4134-a415-b65d5bd31e18"}}, {"head": {"id": "d2de02de-4507-4a59-99e4-92261255e3e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944915397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81689250-9c4f-4860-9f9a-c7f085d9a872", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944915470800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8f3a56-fe12-441f-a4ee-ea0a931601b4", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89221bff-d018-4a96-a383-76bb24e15fc5", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "127867fe-6d52-4d78-9ffa-572cfb826b0e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922852800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9012163-03aa-4bda-85eb-910a0e148d89", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06329345703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922937100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7737f34-ebe4-4c61-8ea9-817a9f30a625", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944922077900, "endTime": 21944923001200}, "additional": {"logType": "info", "children": [], "durationId": "4fe6debd-e49d-4134-a415-b65d5bd31e18"}}, {"head": {"id": "c5a66fb6-8e7b-425c-8248-4dc6d3249d34", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944927143300, "endTime": 21944928263300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b5969f31-b1e6-4844-b4d5-9d69a6d6b4cc", "logId": "82199b47-a8ce-43c7-9747-4542b05cd106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5969f31-b1e6-4844-b4d5-9d69a6d6b4cc", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944924536600}, "additional": {"logType": "detail", "children": [], "durationId": "c5a66fb6-8e7b-425c-8248-4dc6d3249d34"}}, {"head": {"id": "5aa960d7-e4b1-4a01-ac03-7157d0637a3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944925169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d74678-f9e8-4c8b-bb32-203c77c63dd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944925253100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "126d5b9c-55fc-4e24-aa01-85378f9afe97", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944927152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "603fb43b-ca2d-433e-8a77-3b0a36abf320", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944928114400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3ec2ae-d785-4392-b223-2bf05011c30a", "name": "entry : default@ProcessProfile cost memory 0.05364227294921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944928197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82199b47-a8ce-43c7-9747-4542b05cd106", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944927143300, "endTime": 21944928263300}, "additional": {"logType": "info", "children": [], "durationId": "c5a66fb6-8e7b-425c-8248-4dc6d3249d34"}}, {"head": {"id": "c67b1ad6-24bc-4173-b0c7-f8aa5dea52c2", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944933198500, "endTime": 21944941512500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "65c5ca70-1626-47c8-a096-fc386a997c10", "logId": "95436bdc-c5a1-4de7-8b70-82ac37430a63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65c5ca70-1626-47c8-a096-fc386a997c10", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944929855700}, "additional": {"logType": "detail", "children": [], "durationId": "c67b1ad6-24bc-4173-b0c7-f8aa5dea52c2"}}, {"head": {"id": "47ef1bfa-0240-4b6e-bdb2-4738c9c2c150", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944930351600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cbbd60b-d54a-4268-9a43-fd54d4e30512", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944930427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd87fcea-e6c2-4f78-8b1b-58d602ecb694", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944933214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a52a14c-755b-4bb1-9c87-afb84d8a1f5d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944941294200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bcd42b4-d26b-4a11-9ada-6d1c43939d2b", "name": "entry : default@ProcessRouterMap cost memory 0.18445587158203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944941431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95436bdc-c5a1-4de7-8b70-82ac37430a63", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944933198500, "endTime": 21944941512500}, "additional": {"logType": "info", "children": [], "durationId": "c67b1ad6-24bc-4173-b0c7-f8aa5dea52c2"}}, {"head": {"id": "772581b1-984c-4c44-88b1-ec136b267243", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944948616800, "endTime": 21944951297700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "497764f0-9237-4606-8532-7e525df2f934", "logId": "acc67f41-f2ce-4295-ad1b-a8a83dd3c2cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "497764f0-9237-4606-8532-7e525df2f934", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944944699800}, "additional": {"logType": "detail", "children": [], "durationId": "772581b1-984c-4c44-88b1-ec136b267243"}}, {"head": {"id": "04f12482-8fab-4469-a94c-d3bf80bac48a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944945286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33d12f7-14b0-48c9-a701-db15a99417bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944945392800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e79c5df-85be-4f76-9136-ae22f3798ecf", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944946401800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e6e931-499f-4de1-afae-2e904afb5996", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944949770300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "977e19b5-60ba-4b96-ac71-3b1a84d17913", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944949919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037d8587-f187-4095-b9a7-7a1f79c474f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944949973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280736e0-3b5e-45f0-9778-a8f1acb360fd", "name": "entry : default@PreviewProcessResource cost memory 0.067626953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944950046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "192b2aa2-cde9-4a15-a0cb-bc6500d123ca", "name": "runTaskFromQueue task cost before running: 395 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944951219100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc67f41-f2ce-4295-ad1b-a8a83dd3c2cd", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944948616800, "endTime": 21944951297700, "totalTime": 1490500}, "additional": {"logType": "info", "children": [], "durationId": "772581b1-984c-4c44-88b1-ec136b267243"}}, {"head": {"id": "e47edd0b-5400-4b9a-b1de-24f086f04e60", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944958108900, "endTime": 21944980811600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "06c810eb-a5b5-4d37-864e-d1210916c1fe", "logId": "49eaafac-f332-4a98-8c28-aa588b3a8c75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06c810eb-a5b5-4d37-864e-d1210916c1fe", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944954319700}, "additional": {"logType": "detail", "children": [], "durationId": "e47edd0b-5400-4b9a-b1de-24f086f04e60"}}, {"head": {"id": "3e2d602f-67e7-4dd4-b7ac-0ccb774089c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944954816300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e56d533-c7a9-47b2-9196-3a0e7675a094", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944954890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "880d5275-f95d-44f8-9dbf-46a2b5aba2b3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944958119300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c550c18-231c-4c9d-a806-0d21e4ce8849", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944980611700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf950901-ad2d-4f2b-983c-7d55402f324d", "name": "entry : default@GenerateLoaderJson cost memory -1.011322021484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944980741800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49eaafac-f332-4a98-8c28-aa588b3a8c75", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944958108900, "endTime": 21944980811600}, "additional": {"logType": "info", "children": [], "durationId": "e47edd0b-5400-4b9a-b1de-24f086f04e60"}}, {"head": {"id": "d9c3f5c3-2473-4bcf-a8f2-21c8316b0f67", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944992559800, "endTime": 21945014802500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "11a564ab-6e8a-48b8-93e2-036d2d29b386", "logId": "ffa9739c-13cb-411c-8526-06e85d2b4ebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11a564ab-6e8a-48b8-93e2-036d2d29b386", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944988837700}, "additional": {"logType": "detail", "children": [], "durationId": "d9c3f5c3-2473-4bcf-a8f2-21c8316b0f67"}}, {"head": {"id": "5c931984-2d06-4351-afb7-b37eb7a469d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944989342500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a61d59-efd4-4119-89ae-7a8add4707dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944989425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4adc1af9-bb53-4864-9859-d025b73ff3cb", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944990353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c478fc-a137-4202-9177-1540263c83fa", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944992586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9569e6d-e5f2-452d-9e6b-c2335bd87036", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945014575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aecb8cc3-31c5-47ca-bb68-9052aa23722e", "name": "entry : default@PreviewCompileResource cost memory 0.6938705444335938", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945014716400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa9739c-13cb-411c-8526-06e85d2b4ebb", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944992559800, "endTime": 21945014802500}, "additional": {"logType": "info", "children": [], "durationId": "d9c3f5c3-2473-4bcf-a8f2-21c8316b0f67"}}, {"head": {"id": "5cfe5c72-1ff7-4e74-a06b-bc6c0552e641", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017766000, "endTime": 21945018069000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8d283fd0-a19b-46df-a230-c02682e4ff75", "logId": "c145be42-09f4-4861-87cd-889791464496"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d283fd0-a19b-46df-a230-c02682e4ff75", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017148500}, "additional": {"logType": "detail", "children": [], "durationId": "5cfe5c72-1ff7-4e74-a06b-bc6c0552e641"}}, {"head": {"id": "2796ad3c-4c7f-4752-9672-16ee855d8e2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017614500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "790325fd-6a30-42e8-a27f-5005cfcbe80b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017692400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9b4a38-2c6e-4fe9-b57a-ba5e94eb950c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658ade22-7112-4bfd-9d89-8ba7b912399b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017846100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6478231e-d2db-4b0d-9095-2e2214ee8401", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017885400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521f3ec2-f5a0-43e7-b0c9-63f8a764142b", "name": "entry : default@PreviewHookCompileResource cost memory 0.03791046142578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017936200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3e374f-e2bf-4edb-8601-910ffab23f6e", "name": "runTaskFromQueue task cost before running: 462 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c145be42-09f4-4861-87cd-889791464496", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945017766000, "endTime": 21945018069000, "totalTime": 220400}, "additional": {"logType": "info", "children": [], "durationId": "5cfe5c72-1ff7-4e74-a06b-bc6c0552e641"}}, {"head": {"id": "eb9c9108-c8eb-42da-aae9-8a307a98e06b", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945021746300, "endTime": 21945024338200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c4cffee7-fbba-42cc-beb6-2890fc760b27", "logId": "0c65ea1c-ffb2-4992-ab69-9c0d99d7d3fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4cffee7-fbba-42cc-beb6-2890fc760b27", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945019799900}, "additional": {"logType": "detail", "children": [], "durationId": "eb9c9108-c8eb-42da-aae9-8a307a98e06b"}}, {"head": {"id": "017c419f-7eb5-4c72-8e96-d2121a4121bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945020246700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afdb8645-e03c-4f8d-ad0c-16364698da9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945020319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f428bbc-40ae-407b-bb35-b6991af312e6", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945021756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602b00f7-a804-4e3f-8262-bb67f812e377", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945024157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b875d33f-94d8-4317-82c0-11ba45ab2cb1", "name": "entry : default@CopyPreviewProfile cost memory 0.09345245361328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945024271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c65ea1c-ffb2-4992-ab69-9c0d99d7d3fe", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945021746300, "endTime": 21945024338200}, "additional": {"logType": "info", "children": [], "durationId": "eb9c9108-c8eb-42da-aae9-8a307a98e06b"}}, {"head": {"id": "132d6e50-7381-43dd-b5db-4176b01891bf", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027340100, "endTime": 21945027791200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "77bcb7d3-aad1-4c7a-b7ce-aa9bb065a2fe", "logId": "39c07421-d2a9-4a31-981d-aa185313895b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77bcb7d3-aad1-4c7a-b7ce-aa9bb065a2fe", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945026026600}, "additional": {"logType": "detail", "children": [], "durationId": "132d6e50-7381-43dd-b5db-4176b01891bf"}}, {"head": {"id": "527c2c37-51a5-4b7c-8fb4-99df49cb2378", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945026523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c1feb2-164a-4059-9102-2f88b1193279", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945026606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9288534f-3bf0-456f-bd13-680e4cc7c198", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a287e1-ed26-400c-bc93-c279f676b2f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027482200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b854c9f-f4a0-4fb5-b303-2bfec5a8d468", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c634d062-e256-47b0-8773-b536da61f84e", "name": "entry : default@ReplacePreviewerPage cost memory 0.03797149658203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027624900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc0d4b3a-44ab-49b9-86ef-06efe651f939", "name": "runTaskFromQueue task cost before running: 472 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027729000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c07421-d2a9-4a31-981d-aa185313895b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945027340100, "endTime": 21945027791200, "totalTime": 368400}, "additional": {"logType": "info", "children": [], "durationId": "132d6e50-7381-43dd-b5db-4176b01891bf"}}, {"head": {"id": "d4b5d0c2-18a0-4603-8a72-550b8862b278", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029620500, "endTime": 21945029880200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "290972ba-d13a-48bd-a5f8-6018ae6f55cc", "logId": "ddd94d9b-2049-4caf-a6dd-2886ff2a230b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "290972ba-d13a-48bd-a5f8-6018ae6f55cc", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029582800}, "additional": {"logType": "detail", "children": [], "durationId": "d4b5d0c2-18a0-4603-8a72-550b8862b278"}}, {"head": {"id": "5d5b970c-8706-4b7c-a3fe-01366a606885", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029628000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e73a737-0870-43a9-b51c-c66cc0141f23", "name": "entry : buildPreviewerResource cost memory 0.02679443359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029748500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecaf0beb-f53c-4b26-bbc4-2fae24c513b3", "name": "runTaskFromQueue task cost before running: 474 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029836600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddd94d9b-2049-4caf-a6dd-2886ff2a230b", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945029620500, "endTime": 21945029880200, "totalTime": 201400}, "additional": {"logType": "info", "children": [], "durationId": "d4b5d0c2-18a0-4603-8a72-550b8862b278"}}, {"head": {"id": "a3220a6e-77ba-4798-a99f-e3a240625ceb", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945032513800, "endTime": 21945035005600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "16ee9c90-95a9-4db0-933f-bb0f6e1347a7", "logId": "ccc0c988-8ff0-406a-82d4-d25cffb03ba1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16ee9c90-95a9-4db0-933f-bb0f6e1347a7", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945031318200}, "additional": {"logType": "detail", "children": [], "durationId": "a3220a6e-77ba-4798-a99f-e3a240625ceb"}}, {"head": {"id": "3a54317e-85a1-43e8-b331-cc90e7c9ea78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945031763500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5fa8c7-72c0-4890-a057-3996efb89ca9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945031828000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21973c0-e57b-4398-aead-a19cdb6b0c2e", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945032522200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c02fd7-6fd3-438a-a8d7-0f9741685a5e", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945034819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1f72b1-7fe5-4bfa-8acc-fb553d3e7999", "name": "entry : default@PreviewUpdateAssets cost memory 0.11277008056640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945034933900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc0c988-8ff0-406a-82d4-d25cffb03ba1", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945032513800, "endTime": 21945035005600}, "additional": {"logType": "info", "children": [], "durationId": "a3220a6e-77ba-4798-a99f-e3a240625ceb"}}, {"head": {"id": "79af9e12-42a9-4793-af3f-e8e8d358364b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945043295900, "endTime": 21965783024900}, "additional": {"children": ["01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "95ffc76f-354d-4ea1-917e-f01fe4144548", "logId": "0e5a5e4d-4dbc-495a-81d3-a39e5f29717b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95ffc76f-354d-4ea1-917e-f01fe4144548", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945037520700}, "additional": {"logType": "detail", "children": [], "durationId": "79af9e12-42a9-4793-af3f-e8e8d358364b"}}, {"head": {"id": "daec5cba-8d90-4b33-8c19-221177846efe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945038010000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59b53bf-6794-4aa7-b614-8abed85efbb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945038096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e978d2b-ae0d-49ea-ab31-11959b55d49a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945043308900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker21", "startTime": 21945066083900, "endTime": 21965782803400}, "additional": {"children": ["eeb8401d-c8fc-4aea-9b8c-66dcefdfc0ca", "ab154a8f-af86-4217-94b8-9e3d12da3974", "2eadaeb0-853f-4794-9a59-526f5d9ddeeb", "b6ef62cd-851f-4965-ac4f-0adbe66ea377", "f06804dd-bebd-4072-bfa1-97fd5ad58c48"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "79af9e12-42a9-4793-af3f-e8e8d358364b", "logId": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d94495ee-16e1-4c61-bb76-085f082afddf", "name": "entry : default@PreviewArkTS cost memory 0.945892333984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945069447300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6bc293-bc77-40c5-bf83-61b008f99e91", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21949277949400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb8401d-c8fc-4aea-9b8c-66dcefdfc0ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21949279007900, "endTime": 21949279025200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "logId": "67abc840-6e15-4f7a-a2b0-66ad7b1d4934"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67abc840-6e15-4f7a-a2b0-66ad7b1d4934", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21949279007900, "endTime": 21949279025200}, "additional": {"logType": "info", "children": [], "durationId": "eeb8401d-c8fc-4aea-9b8c-66dcefdfc0ca", "parent": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}}, {"head": {"id": "62998e4b-166e-49d3-a729-d9fbab03df72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960791313500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab154a8f-af86-4217-94b8-9e3d12da3974", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960793785300, "endTime": 21960793831400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "logId": "ac8f1aff-0d8a-4c6d-9c57-7e32619a20ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac8f1aff-0d8a-4c6d-9c57-7e32619a20ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960793785300, "endTime": 21960793831400}, "additional": {"logType": "info", "children": [], "durationId": "ab154a8f-af86-4217-94b8-9e3d12da3974", "parent": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}}, {"head": {"id": "be57706b-4adc-4abc-ac9e-b055ebc5eefc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960794057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eadaeb0-853f-4794-9a59-526f5d9ddeeb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960796495700, "endTime": 21960796546400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "logId": "1f4911cf-6840-4ce9-beb6-72efdea3ca0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f4911cf-6840-4ce9-beb6-72efdea3ca0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960796495700, "endTime": 21960796546400}, "additional": {"logType": "info", "children": [], "durationId": "2eadaeb0-853f-4794-9a59-526f5d9ddeeb", "parent": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}}, {"head": {"id": "27df7b27-922a-43c8-8434-396f101d9c96", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960796899300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ef62cd-851f-4965-ac4f-0adbe66ea377", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960800813500, "endTime": 21960800871600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "logId": "a5baf0f8-a411-4e32-b45c-b59d575b9651"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5baf0f8-a411-4e32-b45c-b59d575b9651", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21960800813500, "endTime": 21960800871600}, "additional": {"logType": "info", "children": [], "durationId": "b6ef62cd-851f-4965-ac4f-0adbe66ea377", "parent": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}}, {"head": {"id": "8b955cf3-9dfa-4018-a621-c171919efc12", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965781622900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06804dd-bebd-4072-bfa1-97fd5ad58c48", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965782710500, "endTime": 21965782731500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "logId": "d3c415fe-ccc8-4a36-a37b-20760c095b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3c415fe-ccc8-4a36-a37b-20760c095b91", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965782710500, "endTime": 21965782731500}, "additional": {"logType": "info", "children": [], "durationId": "f06804dd-bebd-4072-bfa1-97fd5ad58c48", "parent": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"}}, {"head": {"id": "6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker21", "startTime": 21945066083900, "endTime": 21965782803400}, "additional": {"logType": "error", "children": ["67abc840-6e15-4f7a-a2b0-66ad7b1d4934", "ac8f1aff-0d8a-4c6d-9c57-7e32619a20ce", "1f4911cf-6840-4ce9-beb6-72efdea3ca0b", "a5baf0f8-a411-4e32-b45c-b59d575b9651", "d3c415fe-ccc8-4a36-a37b-20760c095b91"], "durationId": "01ccd3fe-cfa0-4d3c-9f36-71e5089ddf19", "parent": "0e5a5e4d-4dbc-495a-81d3-a39e5f29717b"}}, {"head": {"id": "40de567d-2be9-4e5d-94c6-9ffc454ca226", "name": "default@PreviewArkTS watch work[21] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965782850200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e5a5e4d-4dbc-495a-81d3-a39e5f29717b", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21945043295900, "endTime": 21965783024900}, "additional": {"logType": "error", "children": ["6b1e2d33-4bb3-4a50-a4e1-f471f2505f1c"], "durationId": "79af9e12-42a9-4793-af3f-e8e8d358364b"}}, {"head": {"id": "ab653b10-5252-40a9-9036-93c380f749af", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965783155600}, "additional": {"logType": "debug", "children": [], "durationId": "79af9e12-42a9-4793-af3f-e8e8d358364b"}}, {"head": {"id": "ec8e657b-0212-4078-a09a-c07526aff908", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:113:24\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:122:24\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965783925000}, "additional": {"logType": "debug", "children": [], "durationId": "79af9e12-42a9-4793-af3f-e8e8d358364b"}}, {"head": {"id": "67a34804-6271-416a-a116-5558ddac26ba", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790258400, "endTime": 21965790336300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6a81d38-b583-4d72-a992-8cf77d37028e", "logId": "6d46b7fc-36ae-4c52-951c-9a407f5fd695"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d46b7fc-36ae-4c52-951c-9a407f5fd695", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790258400, "endTime": 21965790336300}, "additional": {"logType": "info", "children": [], "durationId": "67a34804-6271-416a-a116-5558ddac26ba"}}, {"head": {"id": "bf3b094b-d022-4e14-9424-2fc69d8209a2", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21944556284100, "endTime": 21965790514000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "37b5c40a-b2c6-4bfc-8b7f-7887a3a67f43", "name": "BUILD FAILED in 21 s 235 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790535500}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "231708b9-ad1b-471a-bd61-01bafa87ff49", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1196a30d-af5e-4038-8fa0-8bffe1f3535e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790736700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c1152b-7af4-4ecd-8164-793ee25db8df", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c0943c-81a2-48a1-be78-31f8e6888d7c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f95516-4739-4c98-b478-99008404658c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790884600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7e30b5-7fb2-45d4-9ee9-6b0cb32029fa", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd898fa-7a21-4234-b101-f1cd1fc05e5d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965790962500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd205aa-a699-4f5b-b749-ddb68d174183", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965791001100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7379c6e-fc76-467b-9d81-19d915ed4e87", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965791044300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea36cc0-3100-4f03-babe-a104cd1ece59", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965791084400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8799a74-d295-4565-b920-4d1ca171ec70", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965794287300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9277911a-8f31-4943-8784-7441eda13a15", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965795614400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c0ded8-fde6-4cac-91d4-8a89a4bd0ea6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965795973800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f53f13c5-4afd-4021-a9d1-a5b0de73ceaa", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965796267000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc4f49e-ef44-4f8a-9376-da0c7c19dd5b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965797084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a88e215f-5933-47a7-b7d8-f3f0d94a8953", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965808432800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1bef69-1d18-41a0-adc7-31428c4ac438", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965808739500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b33c236-3412-4c2b-ac37-8bbf3f4b4cc3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965809017900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34353bd6-9d57-456b-84d4-d9f82129a863", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965809317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d423c74b-98fb-44c9-a7cb-1b82c15fdf5f", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 21965809658400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}