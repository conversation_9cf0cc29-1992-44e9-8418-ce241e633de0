if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface RechargePage_Params {
    rechargeAmount?: string;
    selectedBankCard?: string;
    selectedCardId?: number;
    paymentPassword?: string;
    bankCards?: BankCard[];
    isLoading?: boolean;
    showBankCardPicker?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard, BankCardType, RechargeRequest } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class RechargePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__rechargeAmount = new ObservedPropertySimplePU('', this, "rechargeAmount");
        this.__selectedBankCard = new ObservedPropertySimplePU('', this, "selectedBankCard");
        this.__selectedCardId = new ObservedPropertySimplePU(0, this, "selectedCardId");
        this.__paymentPassword = new ObservedPropertySimplePU('', this, "paymentPassword");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showBankCardPicker = new ObservedPropertySimplePU(false, this, "showBankCardPicker");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: RechargePage_Params) {
        if (params.rechargeAmount !== undefined) {
            this.rechargeAmount = params.rechargeAmount;
        }
        if (params.selectedBankCard !== undefined) {
            this.selectedBankCard = params.selectedBankCard;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.paymentPassword !== undefined) {
            this.paymentPassword = params.paymentPassword;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showBankCardPicker !== undefined) {
            this.showBankCardPicker = params.showBankCardPicker;
        }
    }
    updateStateVars(params: RechargePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__rechargeAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedBankCard.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__paymentPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showBankCardPicker.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__rechargeAmount.aboutToBeDeleted();
        this.__selectedBankCard.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__paymentPassword.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showBankCardPicker.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __rechargeAmount: ObservedPropertySimplePU<string>;
    get rechargeAmount() {
        return this.__rechargeAmount.get();
    }
    set rechargeAmount(newValue: string) {
        this.__rechargeAmount.set(newValue);
    }
    private __selectedBankCard: ObservedPropertySimplePU<string>;
    get selectedBankCard() {
        return this.__selectedBankCard.get();
    }
    set selectedBankCard(newValue: string) {
        this.__selectedBankCard.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __paymentPassword: ObservedPropertySimplePU<string>;
    get paymentPassword() {
        return this.__paymentPassword.get();
    }
    set paymentPassword(newValue: string) {
        this.__paymentPassword.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showBankCardPicker: ObservedPropertySimplePU<boolean>;
    get showBankCardPicker() {
        return this.__showBankCardPicker.get();
    }
    set showBankCardPicker(newValue: boolean) {
        this.__showBankCardPicker.set(newValue);
    }
    aboutToAppear() {
        this.loadBankCards();
    }
    async loadBankCards() {
        try {
            const userId = 1; // 临时使用固定用户ID
            const response = await BankCardApi.getCardList(userId, true); // 只获取已绑定的银行卡
            this.bankCards = response || [];
            // 如果没有银行卡，添加测试数据
            if (this.bankCards.length === 0) {
                this.bankCards = [
                    {
                        cardId: 1,
                        userId: 1,
                        cardNo: '6217000010001234567',
                        bankName: '中国银行',
                        cardType: 'SAVINGS' as BankCardType,
                        holderName: '张三',
                        isBound: BankCardStatus.BOUND,
                        createTime: '2024-01-01 00:00:00',
                        updateTime: '2024-01-01 00:00:00'
                    } as BankCard,
                    {
                        cardId: 2,
                        userId: 1,
                        cardNo: '6228480010001234567',
                        bankName: '工商银行',
                        cardType: 'SAVINGS' as BankCardType,
                        holderName: '张三',
                        isBound: BankCardStatus.BOUND,
                        createTime: '2024-01-01 00:00:00',
                        updateTime: '2024-01-01 00:00:00'
                    } as BankCard,
                    {
                        cardId: 3,
                        userId: 1,
                        cardNo: '6225880010001234567',
                        bankName: '招商银行',
                        cardType: 'SAVINGS' as BankCardType,
                        holderName: '张三',
                        isBound: BankCardStatus.BOUND,
                        createTime: '2024-01-01 00:00:00',
                        updateTime: '2024-01-01 00:00:00'
                    } as BankCard
                ];
            }
        }
        catch (error) {
            console.error('加载银行卡失败:', error);
            // 添加测试数据
            this.bankCards = [
                {
                    cardId: 1,
                    userId: 1,
                    cardNo: '6217000010001234567',
                    bankName: '中国银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 2,
                    userId: 1,
                    cardNo: '6228480010001234567',
                    bankName: '工商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 3,
                    userId: 1,
                    cardNo: '6225880010001234567',
                    bankName: '招商银行',
                    cardType: 'SAVINGS' as BankCardType,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard
            ];
        }
    }
    async handleRecharge() {
        if (!this.rechargeAmount || !this.selectedCardId || !this.paymentPassword) {
            promptAction.showToast({
                message: '请填写完整信息',
                duration: 2000
            });
            return;
        }
        if (parseFloat(this.rechargeAmount) <= 0) {
            promptAction.showToast({
                message: '充值金额必须大于0',
                duration: 2000
            });
            return;
        }
        try {
            this.isLoading = true;
            // 调用充值API
            const rechargeData: RechargeRequest = {
                userId: 1,
                cardId: this.selectedCardId,
                amount: parseFloat(this.rechargeAmount)
            };
            // 调用实际的充值API
            await TransactionApi.recharge(rechargeData);
            promptAction.showToast({
                message: '充值成功',
                duration: 2000
            });
            // 重置表单
            this.rechargeAmount = '';
            this.selectedBankCard = '';
            this.selectedCardId = 0;
            this.paymentPassword = '';
            // 返回上一页
            router.back();
        }
        catch (error) {
            console.error('充值失败:', error);
            promptAction.showToast({
                message: '充值失败，请重试',
                duration: 2000
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(163:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
            Column.bindSheet({ value: this.showBankCardPicker, changeEvent: newValue => { this.showBankCardPicker = newValue; } }, { builder: () => {
                    this.BankCardPickerSheet.call(this);
                } }, {
                height: 300,
                showClose: true,
                dragBar: true
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(165:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(166:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('×');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(167:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(179:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(187:9)", "entry");
            // 占位
            Text.width(40);
            // 占位
            Text.height(40);
        }, Text);
        // 占位
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(196:7)", "entry");
            // 表单内容
            Column.layoutWeight(1);
            // 表单内容
            Column.padding({ left: 20, right: 20, top: 20 });
            // 表单内容
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(198:9)", "entry");
            // 充值金额
            Column.width('100%');
            // 充值金额
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(199:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(200:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值金额');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(204:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入充值金额' });
            TextInput.debugLine("entry/src/main/ets/pages/RechargePage.ets(211:11)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.fontSize(16);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.borderRadius(8);
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.rechargeAmount = value;
            });
        }, TextInput);
        // 充值金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(226:9)", "entry");
            // 银行卡选择
            Column.width('100%');
            // 银行卡选择
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(227:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(228:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值银行卡');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(232:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.selectedBankCard || '请选择银行卡');
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(239:11)", "entry");
            Button.width('100%');
            Button.height(50);
            Button.fontSize(14);
            Button.fontColor(this.selectedBankCard ? '#333333' : '#999999');
            Button.backgroundColor('#FFFFFF');
            Button.border({ width: 1, color: '#E0E0E0' });
            Button.borderRadius(8);
            Button.onClick(() => {
                this.showBankCardPicker = true;
            });
        }, Button);
        Button.pop();
        // 银行卡选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(255:9)", "entry");
            // 支付密码
            Column.width('100%');
            // 支付密码
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(256:11)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('*');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(257:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
            Text.margin({ right: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(261:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/RechargePage.ets(268:11)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.fontSize(16);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.borderRadius(8);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.paymentPassword = value;
            });
        }, TextInput);
        // 支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(283:9)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.End);
            // 操作按钮
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(284:11)", "entry");
            Button.width(100);
            Button.height(44);
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(8);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isLoading ? '充值中...' : '确认充值');
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(295:11)", "entry");
            Button.width(120);
            Button.height(44);
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.handleRecharge();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 表单内容
        Column.pop();
        Column.pop();
    }
    BankCardPickerSheet(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(327:5)", "entry");
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择充值银行卡');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(328:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create();
            List.debugLine("entry/src/main/ets/pages/RechargePage.ets(333:7)", "entry");
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const card = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/pages/RechargePage.ets(335:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Row.create();
                            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(336:13)", "entry");
                            Row.width('100%');
                            Row.padding(16);
                            Row.onClick(() => {
                                this.selectedBankCard = `${card.bankName} ****${card.cardNo.slice(-4)}`;
                                this.selectedCardId = card.cardId;
                                this.showBankCardPicker = false;
                            });
                        }, Row);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create('💳');
                            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(337:15)", "entry");
                            Text.fontSize(24);
                            Text.margin({ right: 12 });
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(341:15)", "entry");
                            Column.layoutWeight(1);
                            Column.alignItems(HorizontalAlign.Start);
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(card.bankName);
                            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(342:17)", "entry");
                            Text.fontSize(16);
                            Text.fontColor('#333333');
                            Text.alignSelf(ItemAlign.Start);
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(`${card.cardType} ****${card.cardNo.slice(-4)}`);
                            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(347:17)", "entry");
                            Text.fontSize(14);
                            Text.fontColor('#666666');
                            Text.alignSelf(ItemAlign.Start);
                            Text.margin({ top: 4 });
                        }, Text);
                        Text.pop();
                        Column.pop();
                        Row.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        List.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "RechargePage";
    }
}
registerNamedRoute(() => new RechargePage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/RechargePage", pageFullPath: "entry/src/main/ets/pages/RechargePage", integratedHsp: "false", moduleType: "followWithHap" });
