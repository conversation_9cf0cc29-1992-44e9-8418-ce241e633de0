-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        5.6.35 - MySQL Community Server (GPL)
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.10.0.7000
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 e_wallet 的数据库结构
CREATE DATABASE IF NOT EXISTS `e_wallet` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;
USE `e_wallet`;

-- 导出  表 e_wallet.bank_card 结构
CREATE TABLE IF NOT EXISTS `bank_card` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `card_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `card_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'DEBIT/CREDIT',
  `bank_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `balance` decimal(15,2) DEFAULT '0.00' COMMENT '当前余额',
  `credit_limit` decimal(15,2) DEFAULT '0.00' COMMENT '信用额度',
  `bind_status` tinyint(4) DEFAULT '1' COMMENT '1:绑定 0:解绑',
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `bank_card_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 正在导出表  e_wallet.bank_card 的数据：~13 rows (大约)
INSERT IGNORE INTO `bank_card` (`id`, `user_id`, `card_number`, `card_type`, `bank_name`, `balance`, `credit_limit`, `bind_status`) VALUES
	(1, 1, '****************', 'DEBIT', '中国银行', 4828.00, 0.00, 1),
	(2, 1, '****************', 'CREDIT', '建设银行', -135.00, 20000.00, 0),
	(3, 2, '****************', 'DEBIT', '工商银行', 1609.00, 0.00, 1),
	(6, 1, '**************', 'DEBIT', '中国银行', 5000.00, 0.00, 1),
	(7, 2, '****************', 'DEBIT', '中国银行', 2956.00, 0.00, 1),
	(17, 3, '2222222222222222222', 'CREDIT', '2222', 0.00, 0.00, 1),
	(26, 2, '*****************', 'CREDIT', '平安银行', 3000.00, ************.00, 1),
	(27, 2, '****************', 'DEBIT', '工商银行', 3000.00, 0.00, 1),
	(29, 2, '8888888888888888888', 'CREDIT', '农业银行', 2998.00, 5555555.00, 1),
	(31, 2, '*****************', 'DEBIT', '农业银行', ***********.00, 0.00, 1),
	(33, 2, '***********87987', 'DEBIT', '建设银行', 89897.00, 0.00, 1),
	(34, 2, '*****************', 'CREDIT', '工商银行', 9994.00, 0.00, 1),
	(36, 2, '*****************', 'DEBIT', '中国光大银行', 999991.00, 0.00, 1);

-- 导出  表 e_wallet.transaction 结构
CREATE TABLE IF NOT EXISTS `transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `transaction_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'PAYMENT/DEPOSIT/WITHDRAW/TRANSFER/RECEIVE',
  `amount` decimal(15,2) NOT NULL,
  `transaction_time` datetime NOT NULL,
  `payment_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'WALLET/BANK_CARD',
  `payment_channel` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MERCHANT/QR_CODE/NFC',
  `target_account` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对方账户或商户',
  `card_id` bigint(20) DEFAULT NULL COMMENT '关联的银行卡ID',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'SUCCESS' COMMENT '交易状态',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易描述',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `card_id` (`card_id`),
  CONSTRAINT `transaction_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `transaction_ibfk_2` FOREIGN KEY (`card_id`) REFERENCES `bank_card` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 正在导出表  e_wallet.transaction 的数据：~73 rows (大约)
INSERT IGNORE INTO `transaction` (`id`, `user_id`, `transaction_type`, `amount`, `transaction_time`, `payment_method`, `payment_channel`, `target_account`, `card_id`, `status`, `description`) VALUES
	(23, 2, 'DEPOSIT', 111.00, '2025-06-23 00:54:58', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(25, 2, 'PAYMENT', 11.00, '2025-06-23 00:56:08', 'BANK_CARD', 'MERCHANT', 'starbucks', 3, 'SUCCESS', NULL),
	(26, 2, 'DEPOSIT', 1000.00, '2025-06-23 00:58:13', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(27, 2, 'WITHDRAW', 3.00, '2025-06-23 00:58:45', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(28, 2, 'PAYMENT', 3.00, '2025-06-23 00:59:40', 'BANK_CARD', 'MERCHANT', 'mcdonalds', 3, 'SUCCESS', NULL),
	(29, 2, 'PAYMENT', 45.00, '2025-06-23 01:00:02', 'BANK_CARD', 'MERCHANT', 'gas_station', 7, 'SUCCESS', NULL),
	(30, 2, 'DEPOSIT', 2.00, '2025-06-23 01:30:33', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(31, 2, 'DEPOSIT', 2.00, '2025-06-23 01:32:56', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(32, 2, 'DEPOSIT', 3.00, '2025-06-23 01:33:43', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(33, 2, 'WITHDRAW', 3.00, '2025-06-23 01:33:52', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(34, 2, 'PAYMENT', 1111.00, '2025-06-23 01:34:12', 'WALLET', 'MERCHANT', 'starbucks', NULL, 'SUCCESS', NULL),
	(35, 2, 'PAYMENT', 222.00, '2025-06-23 01:34:23', 'BANK_CARD', 'MERCHANT', 'mcdonalds', 3, 'SUCCESS', NULL),
	(36, 2, 'DEPOSIT', 33.00, '2025-06-23 01:35:35', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(37, 2, 'WITHDRAW', 3.00, '2025-06-23 01:35:45', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(38, 2, 'DEPOSIT', 3.00, '2025-06-23 01:56:02', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(39, 2, 'WITHDRAW', 3.00, '2025-06-23 01:56:12', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(40, 2, 'WITHDRAW', 2.00, '2025-06-23 02:28:21', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(41, 2, 'DEPOSIT', 2.00, '2025-06-23 02:29:29', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(42, 2, 'WITHDRAW', 3.00, '2025-06-23 02:29:39', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(43, 2, 'DEPOSIT', 2.00, '2025-06-23 02:37:11', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(44, 2, 'PAYMENT', 333.00, '2025-06-23 02:37:31', 'WALLET', 'MERCHANT', 'starbucks', NULL, 'SUCCESS', NULL),
	(45, 1, 'DEPOSIT', 100.00, '2025-06-23 02:45:22', 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
	(46, 2, 'PAYMENT', 1.00, '2025-06-23 02:47:28', 'BANK_CARD', 'MERCHANT', 'starbucks', 3, 'SUCCESS', NULL),
	(47, 2, 'DEPOSIT', 2.00, '2025-06-23 02:52:23', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(48, 2, 'DEPOSIT', 2.00, '2025-06-23 02:55:37', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(51, 2, 'PAYMENT', 2.00, '2025-06-23 02:58:33', 'WALLET', 'MERCHANT', 'starbucks', NULL, 'SUCCESS', NULL),
	(52, 2, 'PAYMENT', 3.00, '2025-06-23 02:58:48', 'BANK_CARD', 'MERCHANT', 'starbucks', 3, 'SUCCESS', NULL),
	(53, 1, 'DEPOSIT', 100.00, '2025-06-23 03:04:00', 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
	(54, 1, 'WITHDRAW', 50.00, '2025-06-23 03:04:00', 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 1, 'SUCCESS', '提现到中国银行'),
	(55, 1, 'DEPOSIT', 100.00, '2025-06-23 03:04:03', 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
	(56, 1, 'WITHDRAW', 50.00, '2025-06-23 03:04:03', 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 1, 'SUCCESS', '提现到中国银行'),
	(57, 2, 'DEPOSIT', 2.00, '2025-06-23 03:04:15', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(58, 2, 'PAYMENT', 2.00, '2025-06-23 03:06:45', 'WALLET', 'MERCHANT', 'starbucks', NULL, 'SUCCESS', NULL),
	(59, 2, 'PAYMENT', 9.00, '2025-06-23 03:07:05', 'BANK_CARD', 'MERCHANT', 'starbucks', 3, 'SUCCESS', NULL),
	(60, 2, 'WITHDRAW', 2.00, '2025-06-23 03:08:07', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(62, 2, 'DEPOSIT', 2.00, '2025-06-23 03:47:02', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(63, 2, 'WITHDRAW', 2.00, '2025-06-23 03:47:12', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(64, 2, 'DEPOSIT', 2.00, '2025-06-23 03:52:32', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(65, 2, 'DEPOSIT', 2.00, '2025-06-23 03:59:21', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(66, 2, 'DEPOSIT', 2.00, '2025-06-23 05:37:36', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(67, 2, 'WITHDRAW', 4.00, '2025-06-23 05:38:04', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(68, 2, 'DEPOSIT', 2.00, '2025-06-23 05:39:21', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(69, 2, 'WITHDRAW', 2.00, '2025-06-23 05:39:32', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(71, 2, 'DEPOSIT', 2.00, '2025-06-23 05:49:13', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(72, 2, 'DEPOSIT', 2.00, '2025-06-23 05:49:16', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(73, 2, 'WITHDRAW', 2.00, '2025-06-23 05:49:35', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(74, 2, 'DEPOSIT', 2.00, '2025-06-23 05:55:41', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(75, 2, 'DEPOSIT', 2.00, '2025-06-23 06:10:06', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(76, 2, 'WITHDRAW', 1.00, '2025-06-23 06:10:25', 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 7, 'SUCCESS', '提现到中国银行'),
	(78, 2, 'DEPOSIT', 20.00, '2025-06-23 06:11:00', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(80, 2, 'DEPOSIT', 100.00, '2025-06-23 06:15:31', 'BANK_CARD', 'MERCHANT', '钱包充值', 31, 'SUCCESS', '从农业银行充值到钱包'),
	(81, 2, 'DEPOSIT', 900.00, '2025-06-23 06:15:51', 'BANK_CARD', 'MERCHANT', '钱包充值', 31, 'SUCCESS', '从农业银行充值到钱包'),
	(83, 2, 'WITHDRAW', 3.00, '2025-06-23 07:44:47', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(84, 2, 'DEPOSIT', 2.00, '2025-06-23 12:20:00', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(85, 2, 'WITHDRAW', 20.00, '2025-06-23 12:20:27', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(86, 2, 'DEPOSIT', 2.00, '2025-06-23 12:52:44', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(87, 2, 'DEPOSIT', 1.00, '2025-06-23 12:58:23', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(88, 2, 'DEPOSIT', 5.00, '2025-06-23 13:00:16', 'BANK_CARD', 'MERCHANT', '钱包充值', 34, 'SUCCESS', '从工商银行充值到钱包'),
	(89, 2, 'DEPOSIT', 2.00, '2025-06-23 13:13:23', 'BANK_CARD', 'MERCHANT', '钱包充值', 29, 'SUCCESS', '从农业银行充值到钱包'),
	(90, 2, 'WITHDRAW', 2.00, '2025-06-24 01:35:48', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(91, 2, 'DEPOSIT', 10.00, '2025-06-24 01:39:00', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(92, 2, 'WITHDRAW', 1.00, '2025-06-24 01:39:27', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(93, 2, 'WITHDRAW', 9.00, '2025-06-24 03:13:00', 'BANK_CARD', 'MERCHANT', '工商银行(****************)', 3, 'SUCCESS', '提现到工商银行'),
	(94, 2, 'DEPOSIT', 10.00, '2025-06-24 03:13:19', 'BANK_CARD', 'MERCHANT', '钱包充值', 3, 'SUCCESS', '从工商银行充值到钱包'),
	(95, 1, 'DEPOSIT', 23.00, '2025-06-26 12:38:14', 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
	(96, 1, 'DEPOSIT', 3.00, '2025-06-26 12:38:36', 'BANK_CARD', 'MERCHANT', '钱包充值', 2, 'SUCCESS', '从建设银行充值到钱包'),
	(97, 1, 'DEPOSIT', 4.00, '2025-06-26 12:38:50', 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
	(98, 1, 'DEPOSIT', 66.00, '2025-06-26 13:45:05', 'BANK_CARD', 'MERCHANT', '钱包充值', 2, 'SUCCESS', '从建设银行充值到钱包'),
	(99, 1, 'WITHDRAW', 45.00, '2025-06-26 13:48:33', 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 1, 'SUCCESS', '提现到中国银行'),
	(100, 1, 'WITHDRAW', 34.00, '2025-06-26 14:06:43', 'BANK_CARD', 'MERCHANT', '建设银行(****************)', 2, 'SUCCESS', '提现到建设银行'),
	(101, 1, 'PAYMENT', 45.00, '2025-06-26 14:07:51', 'WALLET', 'MERCHANT', '钱包支付-商品购买', NULL, 'SUCCESS', NULL),
	(102, 1, 'PAYMENT', 22.00, '2025-06-26 14:08:20', 'BANK_CARD', 'MERCHANT', '银行卡支付-商品购买', 1, 'SUCCESS', NULL),
	(103, 1, 'WITHDRAW', 32.00, '2025-06-26 14:29:14', 'BANK_CARD', 'MERCHANT', '中国银行(****************)', 1, 'SUCCESS', '提现到中国银行');

-- 导出  表 e_wallet.user 结构
CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account` decimal(15,2) DEFAULT '0.00' COMMENT '账户余额',
  `create_time` datetime DEFAULT NULL,
  `balance` decimal(15,2) DEFAULT '0.00' COMMENT '钱包余额',
  `payment_password` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付密码',
  `payment_limit` decimal(15,2) DEFAULT '10000.00' COMMENT '支付限额',
  `real_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '未设置' COMMENT '真实姓名',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱地址',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 正在导出表  e_wallet.user 的数据：~3 rows (大约)
INSERT IGNORE INTO `user` (`id`, `username`, `password`, `mobile`, `account`, `create_time`, `balance`, `payment_password`, `payment_limit`, `real_name`, `email`) VALUES
	(1, 'zhangsan', '********', '***********', 1140.00, '2025-06-17 08:57:46', 1000.00, '6141094', 5000.00, '未设置', NULL),
	(2, 'lili', '123456', '***********', 1200.00, '2025-06-17 08:57:46', 2000.00, '6141094', 500.00, '未设置', '<EMAIL>'),
	(3, 'gao', '6141094', '***********', ********.00, '2025-06-20 19:26:49', 80888.00, '123456', 10000.00, '未设置', NULL);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
