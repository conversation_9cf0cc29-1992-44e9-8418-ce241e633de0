{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "9f1fd152-c990-42a9-8d04-3da09b993579", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13999833110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480ee1fb-489f-4b1e-8232-e436ea942ba8", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14030494294000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7059d17e-9e7a-4797-8747-14da9fd13d76", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14030494767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76662184-b595-4c50-8801-9013ac2e371d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032028332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032038123200, "endTime": 14032915937000}, "additional": {"children": ["6eca43c9-4e02-4b77-80a1-2082b0bfbb41", "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "ce43dc7a-5f7a-44e8-88b7-d2baedde37cd", "cd0ed0af-6d3e-4699-b497-a966527bbc9c", "1d09f809-ac9e-40ca-b52f-55bd0461b4aa", "35dfd638-215f-416e-9b68-55c7e99afa53", "ac16400a-f2ea-4fcd-9589-6a4ea20036e7"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "ec234012-5469-4acc-9bc8-eee8c98ac139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eca43c9-4e02-4b77-80a1-2082b0bfbb41", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032038131600, "endTime": 14032051109500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "beac7ed0-e6ef-4d64-87d9-28eaebde959b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032051126500, "endTime": 14032892488600}, "additional": {"children": ["b891a251-c677-406d-8f6b-73d4dd65e9e7", "38517c47-fadc-4f23-a523-59d32417e550", "f7aa3923-aa78-4a08-a9f4-577873001019", "31acb082-c916-44aa-b225-813cd7c5aa06", "1a76cc4e-e7a1-4d5f-9add-c30f29477957", "34919353-de9f-4b4d-8a05-3410d578197c", "6c0ab67d-f3c8-456b-8b87-db3f24e2dd2a", "8510cc47-33d3-4696-9ac8-5936905a2768", "0ba19318-f0fc-4e7f-8e66-2df4eedec64f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce43dc7a-5f7a-44e8-88b7-d2baedde37cd", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032892530500, "endTime": 14032915917400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "beb66d49-20cd-4f0d-9f67-a2196259bc11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd0ed0af-6d3e-4699-b497-a966527bbc9c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032915924400, "endTime": 14032915931500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "995a3167-7e29-4915-bcb5-723a01d2ed73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d09f809-ac9e-40ca-b52f-55bd0461b4aa", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032041688500, "endTime": 14032041718000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "2bfccfc3-9e23-49fb-bbf0-cb2f97916d5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bfccfc3-9e23-49fb-bbf0-cb2f97916d5b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032041688500, "endTime": 14032041718000}, "additional": {"logType": "info", "children": [], "durationId": "1d09f809-ac9e-40ca-b52f-55bd0461b4aa", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "35dfd638-215f-416e-9b68-55c7e99afa53", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032046598700, "endTime": 14032046610200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "8800de6a-8919-436e-9ed9-adcf57b7b00f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8800de6a-8919-436e-9ed9-adcf57b7b00f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032046598700, "endTime": 14032046610200}, "additional": {"logType": "info", "children": [], "durationId": "35dfd638-215f-416e-9b68-55c7e99afa53", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "428f0956-91a7-4583-ab15-1138a7573b89", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032046651300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac928bf9-2c76-49f4-be0f-e21eae4445c8", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032050965800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beac7ed0-e6ef-4d64-87d9-28eaebde959b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032038131600, "endTime": 14032051109500}, "additional": {"logType": "info", "children": [], "durationId": "6eca43c9-4e02-4b77-80a1-2082b0bfbb41", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "b891a251-c677-406d-8f6b-73d4dd65e9e7", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032056971600, "endTime": 14032056981500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "ec79c12a-28a7-4349-bafa-6f9bd5552162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38517c47-fadc-4f23-a523-59d32417e550", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032056997700, "endTime": 14032061136400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "9a582faf-fa54-4407-958c-241529607a00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7aa3923-aa78-4a08-a9f4-577873001019", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032061160000, "endTime": 14032598999000}, "additional": {"children": ["22909d66-d24f-4f5e-a2ad-a97f74ee06c9", "a77cb428-c279-45a9-aa84-f40466b1b856", "6d823844-5b9e-4980-aeb1-c23c1d8264f7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "ab8b1d90-5404-4467-a2be-cc739ff31f2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31acb082-c916-44aa-b225-813cd7c5aa06", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032599017900, "endTime": 14032826431600}, "additional": {"children": ["fbf33f16-f632-45bd-b017-034e2192bbc8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "2947084d-1d00-43ef-ae31-b1e25a7a7a03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a76cc4e-e7a1-4d5f-9add-c30f29477957", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032826440300, "endTime": 14032850922300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "2220735c-779d-4d44-abfd-abe5300bb387"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34919353-de9f-4b4d-8a05-3410d578197c", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032852132400, "endTime": 14032880927200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "36cfd10c-cb2f-495a-95ac-701e6196dd54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c0ab67d-f3c8-456b-8b87-db3f24e2dd2a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032880950200, "endTime": 14032892229300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "95f69aea-1723-452f-92fd-7481e374b35d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8510cc47-33d3-4696-9ac8-5936905a2768", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032892295100, "endTime": 14032892468700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "9c98f680-31cf-4649-b3c3-8a5cc78ec80e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec79c12a-28a7-4349-bafa-6f9bd5552162", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032056971600, "endTime": 14032056981500}, "additional": {"logType": "info", "children": [], "durationId": "b891a251-c677-406d-8f6b-73d4dd65e9e7", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "9a582faf-fa54-4407-958c-241529607a00", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032056997700, "endTime": 14032061136400}, "additional": {"logType": "info", "children": [], "durationId": "38517c47-fadc-4f23-a523-59d32417e550", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "22909d66-d24f-4f5e-a2ad-a97f74ee06c9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032083139800, "endTime": 14032083173800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7aa3923-aa78-4a08-a9f4-577873001019", "logId": "c04fd7bf-47fe-4b50-b969-988f0b96e756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c04fd7bf-47fe-4b50-b969-988f0b96e756", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032083139800, "endTime": 14032083173800}, "additional": {"logType": "info", "children": [], "durationId": "22909d66-d24f-4f5e-a2ad-a97f74ee06c9", "parent": "ab8b1d90-5404-4467-a2be-cc739ff31f2f"}}, {"head": {"id": "a77cb428-c279-45a9-aa84-f40466b1b856", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032086668000, "endTime": 14032489289200}, "additional": {"children": ["0930e962-f4f4-48e6-8479-04d723a32f79", "5aa038b2-02e6-4665-877e-ad827af37489"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7aa3923-aa78-4a08-a9f4-577873001019", "logId": "ce0d4fc1-2ad7-44dc-a38d-5a56dfbcdf3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0930e962-f4f4-48e6-8479-04d723a32f79", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032086669200, "endTime": 14032113980800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a77cb428-c279-45a9-aa84-f40466b1b856", "logId": "9730f775-dd58-43b5-8ea3-054ec30ce651"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5aa038b2-02e6-4665-877e-ad827af37489", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032114005700, "endTime": 14032489267500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a77cb428-c279-45a9-aa84-f40466b1b856", "logId": "4d42aa52-fa06-4fa0-9065-3e18ae993c70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1158716d-a3db-416b-83e1-683010b75e58", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032086675200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f46c04-8e96-4615-ad67-2ea95ba3fa03", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032113794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9730f775-dd58-43b5-8ea3-054ec30ce651", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032086669200, "endTime": 14032113980800}, "additional": {"logType": "info", "children": [], "durationId": "0930e962-f4f4-48e6-8479-04d723a32f79", "parent": "ce0d4fc1-2ad7-44dc-a38d-5a56dfbcdf3b"}}, {"head": {"id": "67730775-e1dc-4d5c-9bfc-500041928d52", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032114018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03097274-9b9b-4310-ae6a-810e331e8f4c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032121566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e82703-23a4-4f05-9e59-440ea93c8825", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032121671200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399f6ed3-6563-4beb-830a-62638ae05f69", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032121797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb1cff77-7fb1-4ede-9006-4504695eb452", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032121887500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8952b7-c559-4097-bd30-ad4a8b11bc67", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032123396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6f9a78-1d21-4c53-ad28-94e4ea8828eb", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032127005000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9b265ee-9857-4d7f-89e3-6a08e2dedcbd", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032190085500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a910a57d-3fc8-48ae-8bf3-f4cbda31e0d9", "name": "Sdk init in 303 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032430915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e963a93-5ac8-4e2b-a5a7-7655b0725906", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032431088600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 44}, "markType": "other"}}, {"head": {"id": "36d7930f-ca25-4987-8a9a-b89c00ce7a0d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032431110800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 44}, "markType": "other"}}, {"head": {"id": "71725530-ee85-4c4b-aa81-ce94131b566d", "name": "Project task initialization takes 56 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032488759900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30d0261-fe97-43b5-a628-97368329f338", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032488959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03ed347-bff0-4807-b878-fdc6872e4e84", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032489076000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7145d718-0d54-43cc-a8f1-0b166b450e6c", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032489176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d42aa52-fa06-4fa0-9065-3e18ae993c70", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032114005700, "endTime": 14032489267500}, "additional": {"logType": "info", "children": [], "durationId": "5aa038b2-02e6-4665-877e-ad827af37489", "parent": "ce0d4fc1-2ad7-44dc-a38d-5a56dfbcdf3b"}}, {"head": {"id": "ce0d4fc1-2ad7-44dc-a38d-5a56dfbcdf3b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032086668000, "endTime": 14032489289200}, "additional": {"logType": "info", "children": ["9730f775-dd58-43b5-8ea3-054ec30ce651", "4d42aa52-fa06-4fa0-9065-3e18ae993c70"], "durationId": "a77cb428-c279-45a9-aa84-f40466b1b856", "parent": "ab8b1d90-5404-4467-a2be-cc739ff31f2f"}}, {"head": {"id": "6d823844-5b9e-4980-aeb1-c23c1d8264f7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032598953900, "endTime": 14032598973400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7aa3923-aa78-4a08-a9f4-577873001019", "logId": "82772c75-f6d7-4c99-b33c-79fc0ea0bfbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82772c75-f6d7-4c99-b33c-79fc0ea0bfbb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032598953900, "endTime": 14032598973400}, "additional": {"logType": "info", "children": [], "durationId": "6d823844-5b9e-4980-aeb1-c23c1d8264f7", "parent": "ab8b1d90-5404-4467-a2be-cc739ff31f2f"}}, {"head": {"id": "ab8b1d90-5404-4467-a2be-cc739ff31f2f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032061160000, "endTime": 14032598999000}, "additional": {"logType": "info", "children": ["c04fd7bf-47fe-4b50-b969-988f0b96e756", "ce0d4fc1-2ad7-44dc-a38d-5a56dfbcdf3b", "82772c75-f6d7-4c99-b33c-79fc0ea0bfbb"], "durationId": "f7aa3923-aa78-4a08-a9f4-577873001019", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "fbf33f16-f632-45bd-b017-034e2192bbc8", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032599804400, "endTime": 14032826416400}, "additional": {"children": ["275d5f18-ca17-491c-8fcf-42c8382d34ad", "ede70fe4-ded7-425b-b22a-992f4a7a98af", "09b5f156-0775-4e7a-b5d8-0a9b9782de50"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "31acb082-c916-44aa-b225-813cd7c5aa06", "logId": "b27428bc-7989-4741-bb36-05da29c324e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "275d5f18-ca17-491c-8fcf-42c8382d34ad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032807971100, "endTime": 14032807991900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fbf33f16-f632-45bd-b017-034e2192bbc8", "logId": "d09aab86-06ea-4d0d-b757-40f35c53c4be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d09aab86-06ea-4d0d-b757-40f35c53c4be", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032807971100, "endTime": 14032807991900}, "additional": {"logType": "info", "children": [], "durationId": "275d5f18-ca17-491c-8fcf-42c8382d34ad", "parent": "b27428bc-7989-4741-bb36-05da29c324e4"}}, {"head": {"id": "ede70fe4-ded7-425b-b22a-992f4a7a98af", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032810112000, "endTime": 14032824970900}, "additional": {"children": ["b0a6a7d5-544f-465f-96b1-d1168f8f154f", "35edeca0-0833-47c7-806f-ec9d8c263e67"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fbf33f16-f632-45bd-b017-034e2192bbc8", "logId": "1f090892-7d99-4c21-a9be-07054d4bd36f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0a6a7d5-544f-465f-96b1-d1168f8f154f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032810113100, "endTime": 14032813915700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ede70fe4-ded7-425b-b22a-992f4a7a98af", "logId": "8bb61c91-931d-43d2-a7a0-9a645d138fc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35edeca0-0833-47c7-806f-ec9d8c263e67", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032813932200, "endTime": 14032824956900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ede70fe4-ded7-425b-b22a-992f4a7a98af", "logId": "46c876df-598c-405a-af48-56983ed2c3e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3d70412-7fb9-4c0f-b085-e0d1acf5ddc1", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032810119500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7211f866-5228-43e3-b77e-8de420a81d89", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032813789000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb61c91-931d-43d2-a7a0-9a645d138fc9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032810113100, "endTime": 14032813915700}, "additional": {"logType": "info", "children": [], "durationId": "b0a6a7d5-544f-465f-96b1-d1168f8f154f", "parent": "1f090892-7d99-4c21-a9be-07054d4bd36f"}}, {"head": {"id": "b432c7a1-cd63-4d78-bb51-11da0a8c6488", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032813943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba6c95a8-dbe4-415c-b92c-3b2c69e4c2d0", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032820432200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce551abb-2b8a-4b20-8d16-40d5fd04f567", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032820558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140a65b1-b601-456f-8a49-4ed1fe3d928c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032820738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f8239b-d5b6-42f7-8b2f-9d206ab62b2f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032820886700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de910599-eb71-47d6-9f25-e723a845e32a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032820953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9b936c-aca3-4ccc-b321-e897e6efb389", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032821002700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec70532-5fe3-404e-84a7-15621bbcd5fe", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032821070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46896b8-26d5-4c05-b686-99358588f62c", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032824647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c282e2-dd88-492b-86dc-7d76482e12f8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032824793500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8b48ad-7a70-4033-a540-1dc96502b3b8", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032824860100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb144e65-d2f0-4390-b66e-52022d097a1e", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032824909100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c876df-598c-405a-af48-56983ed2c3e6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032813932200, "endTime": 14032824956900}, "additional": {"logType": "info", "children": [], "durationId": "35edeca0-0833-47c7-806f-ec9d8c263e67", "parent": "1f090892-7d99-4c21-a9be-07054d4bd36f"}}, {"head": {"id": "1f090892-7d99-4c21-a9be-07054d4bd36f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032810112000, "endTime": 14032824970900}, "additional": {"logType": "info", "children": ["8bb61c91-931d-43d2-a7a0-9a645d138fc9", "46c876df-598c-405a-af48-56983ed2c3e6"], "durationId": "ede70fe4-ded7-425b-b22a-992f4a7a98af", "parent": "b27428bc-7989-4741-bb36-05da29c324e4"}}, {"head": {"id": "09b5f156-0775-4e7a-b5d8-0a9b9782de50", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032826385000, "endTime": 14032826396500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fbf33f16-f632-45bd-b017-034e2192bbc8", "logId": "5b376b0c-218b-4b0f-93c5-dbda6d068649"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b376b0c-218b-4b0f-93c5-dbda6d068649", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032826385000, "endTime": 14032826396500}, "additional": {"logType": "info", "children": [], "durationId": "09b5f156-0775-4e7a-b5d8-0a9b9782de50", "parent": "b27428bc-7989-4741-bb36-05da29c324e4"}}, {"head": {"id": "b27428bc-7989-4741-bb36-05da29c324e4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032599804400, "endTime": 14032826416400}, "additional": {"logType": "info", "children": ["d09aab86-06ea-4d0d-b757-40f35c53c4be", "1f090892-7d99-4c21-a9be-07054d4bd36f", "5b376b0c-218b-4b0f-93c5-dbda6d068649"], "durationId": "fbf33f16-f632-45bd-b017-034e2192bbc8", "parent": "2947084d-1d00-43ef-ae31-b1e25a7a7a03"}}, {"head": {"id": "2947084d-1d00-43ef-ae31-b1e25a7a7a03", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032599017900, "endTime": 14032826431600}, "additional": {"logType": "info", "children": ["b27428bc-7989-4741-bb36-05da29c324e4"], "durationId": "31acb082-c916-44aa-b225-813cd7c5aa06", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "209dcd90-47ec-4de4-b938-ec0623d98cfc", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032850557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df1dfcf-a22a-4125-b3ab-070c9079cb59", "name": "hvigorfile, resolve hvigorfile dependencies in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032850840600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2220735c-779d-4d44-abfd-abe5300bb387", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032826440300, "endTime": 14032850922300}, "additional": {"logType": "info", "children": [], "durationId": "1a76cc4e-e7a1-4d5f-9add-c30f29477957", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "0ba19318-f0fc-4e7f-8e66-2df4eedec64f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032851893600, "endTime": 14032852114500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "logId": "59f28d74-48d9-4247-8b11-ebd3e504d311"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21123618-64f5-4d73-b4b4-5f89398b87a6", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032851929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f28d74-48d9-4247-8b11-ebd3e504d311", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032851893600, "endTime": 14032852114500}, "additional": {"logType": "info", "children": [], "durationId": "0ba19318-f0fc-4e7f-8e66-2df4eedec64f", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "c59671d9-b722-4572-a737-b07b8469d480", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032853818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0109e93-79f4-4ab5-9977-c465e849a3b0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032879983100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cfd10c-cb2f-495a-95ac-701e6196dd54", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032852132400, "endTime": 14032880927200}, "additional": {"logType": "info", "children": [], "durationId": "34919353-de9f-4b4d-8a05-3410d578197c", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "a2a174de-8117-4a89-baac-f394a5390530", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032880965600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f481b70-2f73-4976-a285-03a4e1105817", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032886654700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6c8305-539c-40bd-889d-25da44e5dc20", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032886766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ed4b7f-8589-46bb-ac36-daf85992e06f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032887015400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9cb5739-9533-4e6f-88e5-2d17e30f2f0c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032889276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0d3752-a72a-42e8-8955-a3517feb291b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032889356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f69aea-1723-452f-92fd-7481e374b35d", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032880950200, "endTime": 14032892229300}, "additional": {"logType": "info", "children": [], "durationId": "6c0ab67d-f3c8-456b-8b87-db3f24e2dd2a", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "720dc746-4199-42ca-95f9-2826632e8ed6", "name": "Configuration phase cost:836 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032892332800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c98f680-31cf-4649-b3c3-8a5cc78ec80e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032892295100, "endTime": 14032892468700}, "additional": {"logType": "info", "children": [], "durationId": "8510cc47-33d3-4696-9ac8-5936905a2768", "parent": "b273e53c-e346-4062-ad02-d3e0b99b5ff2"}}, {"head": {"id": "b273e53c-e346-4062-ad02-d3e0b99b5ff2", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032051126500, "endTime": 14032892488600}, "additional": {"logType": "info", "children": ["ec79c12a-28a7-4349-bafa-6f9bd5552162", "9a582faf-fa54-4407-958c-241529607a00", "ab8b1d90-5404-4467-a2be-cc739ff31f2f", "2947084d-1d00-43ef-ae31-b1e25a7a7a03", "2220735c-779d-4d44-abfd-abe5300bb387", "36cfd10c-cb2f-495a-95ac-701e6196dd54", "95f69aea-1723-452f-92fd-7481e374b35d", "9c98f680-31cf-4649-b3c3-8a5cc78ec80e", "59f28d74-48d9-4247-8b11-ebd3e504d311"], "durationId": "4cc9b259-d893-4cbb-a810-eee9cbed71a4", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "ac16400a-f2ea-4fcd-9589-6a4ea20036e7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032915883800, "endTime": 14032915900800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d98d8be-75ba-45c7-864f-92accfa1d3da", "logId": "01cd079e-3507-4fe5-86cc-473acd6c9e0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01cd079e-3507-4fe5-86cc-473acd6c9e0c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032915883800, "endTime": 14032915900800}, "additional": {"logType": "info", "children": [], "durationId": "ac16400a-f2ea-4fcd-9589-6a4ea20036e7", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "beb66d49-20cd-4f0d-9f67-a2196259bc11", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032892530500, "endTime": 14032915917400}, "additional": {"logType": "info", "children": [], "durationId": "ce43dc7a-5f7a-44e8-88b7-d2baedde37cd", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "995a3167-7e29-4915-bcb5-723a01d2ed73", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032915924400, "endTime": 14032915931500}, "additional": {"logType": "info", "children": [], "durationId": "cd0ed0af-6d3e-4699-b497-a966527bbc9c", "parent": "ec234012-5469-4acc-9bc8-eee8c98ac139"}}, {"head": {"id": "ec234012-5469-4acc-9bc8-eee8c98ac139", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032038123200, "endTime": 14032915937000}, "additional": {"logType": "info", "children": ["beac7ed0-e6ef-4d64-87d9-28eaebde959b", "b273e53c-e346-4062-ad02-d3e0b99b5ff2", "beb66d49-20cd-4f0d-9f67-a2196259bc11", "995a3167-7e29-4915-bcb5-723a01d2ed73", "2bfccfc3-9e23-49fb-bbf0-cb2f97916d5b", "8800de6a-8919-436e-9ed9-adcf57b7b00f", "01cd079e-3507-4fe5-86cc-473acd6c9e0c"], "durationId": "7d98d8be-75ba-45c7-864f-92accfa1d3da"}}, {"head": {"id": "69a09b15-4629-442d-af12-4c8774199d1d", "name": "Configuration task cost before running: 883 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032916086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b69f4035-5eed-4531-abc8-dc929d3aea9e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032920979400, "endTime": 14033145191000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "25e5a1e5-6acb-4e68-985e-8b8a686c9b70", "logId": "056e47be-d33e-46a7-91bf-d59238b80d5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25e5a1e5-6acb-4e68-985e-8b8a686c9b70", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032917593700}, "additional": {"logType": "detail", "children": [], "durationId": "b69f4035-5eed-4531-abc8-dc929d3aea9e"}}, {"head": {"id": "e55c1da2-e604-46dc-8bd5-96dc7ec6bcd9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032918074700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3367060-8b93-4b01-8bd7-20581ebe607a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032918166800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c097afa-2a11-4669-a840-5c7f25cd47c2", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032920992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c48947-270a-4d96-b4df-522e6a7aa333", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032930313800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acfa466-f24e-4e43-a84b-0268ad263144", "name": "entry : default@PreBuild cost memory -1.4997711181640625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032930561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056e47be-d33e-46a7-91bf-d59238b80d5d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032920979400, "endTime": 14033145191000}, "additional": {"logType": "info", "children": [], "durationId": "b69f4035-5eed-4531-abc8-dc929d3aea9e"}}, {"head": {"id": "cd2183fe-996b-48c2-9afa-555eec35c0cc", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033151400300, "endTime": 14033154462100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ec13ab4c-c734-43da-bd0c-270118a06b77", "logId": "084ee7a7-ed4b-4fbd-bcbe-387d5061ee00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec13ab4c-c734-43da-bd0c-270118a06b77", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033149839600}, "additional": {"logType": "detail", "children": [], "durationId": "cd2183fe-996b-48c2-9afa-555eec35c0cc"}}, {"head": {"id": "b6575187-277f-4240-8f7a-c25a6465e227", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033150451100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abc3c298-94ab-4608-bfcc-3d004706188a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033150584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30bd9e51-8326-434a-9d27-787d965fe248", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033151411600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6709884-f0ed-477d-a2d2-dfc8ddaafe34", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033153756600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8aa104-bd7c-4405-8975-06debd55e958", "name": "entry : default@MergeProfile cost memory 0.11080169677734375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033153874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084ee7a7-ed4b-4fbd-bcbe-387d5061ee00", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033151400300, "endTime": 14033154462100}, "additional": {"logType": "info", "children": [], "durationId": "cd2183fe-996b-48c2-9afa-555eec35c0cc"}}, {"head": {"id": "8ff0b0c6-9a6c-4f2c-ba84-ec22c4bc17ca", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033185727000, "endTime": 14033188648400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "94ae4001-baef-498b-aa68-188af1e86f40", "logId": "d2c98272-7697-4c95-a783-3da2541ca0e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94ae4001-baef-498b-aa68-188af1e86f40", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033156204500}, "additional": {"logType": "detail", "children": [], "durationId": "8ff0b0c6-9a6c-4f2c-ba84-ec22c4bc17ca"}}, {"head": {"id": "0f766088-c446-4d24-a319-0442fa6e5735", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033156747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c664fe-26f9-45c8-ae0e-29b9eda14360", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033156848000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4818fccd-ec13-4c23-8794-d8a7173debd1", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033185744100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d76625-55c8-4fed-a0b8-64ae4bc9a637", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033186860700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d54f06-5a8a-4ab7-89bf-448a4e5b2963", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033188397400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6d3431-93ec-4103-a1b1-81b75cb6e955", "name": "entry : default@CreateBuildProfile cost memory 0.09658050537109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033188544500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2c98272-7697-4c95-a783-3da2541ca0e9", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033185727000, "endTime": 14033188648400}, "additional": {"logType": "info", "children": [], "durationId": "8ff0b0c6-9a6c-4f2c-ba84-ec22c4bc17ca"}}, {"head": {"id": "7daecb3d-934c-4fbc-8ae2-e20407bc1e6b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033192767500, "endTime": 14033193222400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d3668c47-c1aa-4322-8c78-f43046eecc1f", "logId": "f469f267-e85b-4c02-b160-3487dc5526e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3668c47-c1aa-4322-8c78-f43046eecc1f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033191188500}, "additional": {"logType": "detail", "children": [], "durationId": "7daecb3d-934c-4fbc-8ae2-e20407bc1e6b"}}, {"head": {"id": "37e24769-9e5c-43bc-8a38-b977d5b1ae13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033191776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e59df389-35d9-4919-b26c-736894b28e4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033191890600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e111ae-46b8-457e-8b42-1915301110d6", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033192778200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f888a10c-c74c-4691-99aa-036ecd1f435c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033192899000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353b5bf0-f588-4960-9f3f-a76f331c82ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033192967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8069c2c-ae16-4469-a617-c348199bafdf", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033193050300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24f6aa2-b6f7-4768-bcf2-967aae3707ef", "name": "runTaskFromQueue task cost before running: 1 s 160 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033193156700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f469f267-e85b-4c02-b160-3487dc5526e0", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033192767500, "endTime": 14033193222400, "totalTime": 363700}, "additional": {"logType": "info", "children": [], "durationId": "7daecb3d-934c-4fbc-8ae2-e20407bc1e6b"}}, {"head": {"id": "fe9ce4dd-4982-4319-9a4b-00ffabd2f44c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033238459700, "endTime": 14033239641900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c416c78-774a-4f04-a5eb-7828b02cb131", "logId": "0e5b6a83-ca3c-4742-b738-18fbf246b90d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c416c78-774a-4f04-a5eb-7828b02cb131", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033229246600}, "additional": {"logType": "detail", "children": [], "durationId": "fe9ce4dd-4982-4319-9a4b-00ffabd2f44c"}}, {"head": {"id": "367a69fe-aabe-4ac6-9ee4-052927d49971", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033229864300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0513c7f6-09ab-4c6d-8c58-35bedcd29ef3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033230011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1386beb-acf1-498e-8499-8de9fcd0243c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033238477700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5433d1-e0dd-4e78-bcba-9c590b432a5e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033238731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a06cf00-d2c0-43f5-b17d-282b97c89b59", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033239441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac383f2-d205-4acf-a6c2-12e2d94202c8", "name": "entry : default@GeneratePkgContextInfo cost memory 0.066162109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033239570500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e5b6a83-ca3c-4742-b738-18fbf246b90d", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033238459700, "endTime": 14033239641900}, "additional": {"logType": "info", "children": [], "durationId": "fe9ce4dd-4982-4319-9a4b-00ffabd2f44c"}}, {"head": {"id": "85349ae3-f7c9-4a8c-8bfd-09fcccc32e97", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033331398500, "endTime": 14033332836900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6e9a6012-0a42-49ce-a2cc-819c3ae8f916", "logId": "d9bb73fc-3feb-4bf2-a740-b12430b3471d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e9a6012-0a42-49ce-a2cc-819c3ae8f916", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033329300000}, "additional": {"logType": "detail", "children": [], "durationId": "85349ae3-f7c9-4a8c-8bfd-09fcccc32e97"}}, {"head": {"id": "259b8045-a8f7-45d1-b84b-f81606d80658", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033329852600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795da49d-f12d-4fcf-a1fa-ce5b3eef61ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033329977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33235192-5be5-4678-a55c-b6d2af6fc682", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033331417500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed595c3a-c200-4819-8d57-8a1faec27639", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033332630800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64fdb512-93a4-413f-8079-c581ff1a9ea2", "name": "entry : default@ProcessProfile cost memory 0.057037353515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033332754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9bb73fc-3feb-4bf2-a740-b12430b3471d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033331398500, "endTime": 14033332836900}, "additional": {"logType": "info", "children": [], "durationId": "85349ae3-f7c9-4a8c-8bfd-09fcccc32e97"}}, {"head": {"id": "ee57e128-4dcf-4e87-9462-f2bdd035751c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033338040000, "endTime": 14033346516500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0f65e297-b790-4fcf-86db-c047c4ff8d56", "logId": "4a71977e-a7d7-43c5-aa48-138d08baade4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f65e297-b790-4fcf-86db-c047c4ff8d56", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033334848700}, "additional": {"logType": "detail", "children": [], "durationId": "ee57e128-4dcf-4e87-9462-f2bdd035751c"}}, {"head": {"id": "1e77f8a1-4d41-449e-a553-c99f2ab37987", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033335675300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d867359-d883-4f98-9792-6b80cc42afa7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033335841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9914e37-2b8f-4528-8870-d54d1d7f865e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033338056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21265e0-9991-409a-80b5-2b61b36985c5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033346191000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d2030c5-5a7c-4064-b637-55af6acbcaa3", "name": "entry : default@ProcessRouterMap cost memory 0.18756866455078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033346379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a71977e-a7d7-43c5-aa48-138d08baade4", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033338040000, "endTime": 14033346516500}, "additional": {"logType": "info", "children": [], "durationId": "ee57e128-4dcf-4e87-9462-f2bdd035751c"}}, {"head": {"id": "34247c59-b5a5-4702-95fd-0a6eb3d620e9", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033355632800, "endTime": 14033358783100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0f7d7042-6a91-4127-b400-29ddde4e30ed", "logId": "37e43f20-9211-4abf-88dd-8572ddc38df5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f7d7042-6a91-4127-b400-29ddde4e30ed", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033350270000}, "additional": {"logType": "detail", "children": [], "durationId": "34247c59-b5a5-4702-95fd-0a6eb3d620e9"}}, {"head": {"id": "87c64f85-cc0d-4e02-8783-510fecb95e75", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033350958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb857032-8b28-464b-a42e-969e8c866514", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033351094900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "265f46a2-c72a-4496-8dcf-a37f4263b1dd", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033352631200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fac888-73a9-40da-a157-8c5ca3f2adda", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033356884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2dcee4a-1d48-47d0-a113-a28c7556d95b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033357072600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdd3ae7d-0017-4b55-806d-2ae7ce3c4d51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033357146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba1c875-119d-445d-a6d6-3bf8a256ab3b", "name": "entry : default@PreviewProcessResource cost memory 0.0701904296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033357234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a922cf51-91dd-4852-820d-a80979b87472", "name": "runTaskFromQueue task cost before running: 1 s 326 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033358666700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e43f20-9211-4abf-88dd-8572ddc38df5", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033355632800, "endTime": 14033358783100, "totalTime": 1680800}, "additional": {"logType": "info", "children": [], "durationId": "34247c59-b5a5-4702-95fd-0a6eb3d620e9"}}, {"head": {"id": "bb513822-b9b9-4a36-918e-d3cc3988b45f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033367143200, "endTime": 14033445264400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7cbd1d40-7e76-4c98-84e6-b5bf1e23142f", "logId": "ce3ffd26-c418-4a44-b567-527c258fe083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cbd1d40-7e76-4c98-84e6-b5bf1e23142f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033362289300}, "additional": {"logType": "detail", "children": [], "durationId": "bb513822-b9b9-4a36-918e-d3cc3988b45f"}}, {"head": {"id": "989f56f6-9566-42d1-beaa-45866420c613", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033362969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1266892a-0d72-4394-8d56-738ed92b2aab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033363144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6cb9a66-872a-4125-b965-e007b26e1875", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033367160300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a026baf-5027-4519-bc73-07a50f603bbe", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 68 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033444746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e5f8ab-ae81-466b-83ce-562350e51f79", "name": "entry : default@GenerateLoaderJson cost memory -0.9891204833984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033444973300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3ffd26-c418-4a44-b567-527c258fe083", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033367143200, "endTime": 14033445264400}, "additional": {"logType": "info", "children": [], "durationId": "bb513822-b9b9-4a36-918e-d3cc3988b45f"}}, {"head": {"id": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033485880300, "endTime": 14042246469800}, "additional": {"children": ["5b0435dc-428c-4635-b02b-b6a7d668adf7", "304948fc-f66a-4aa4-9700-b68cfdb4c6b2", "a4da8b06-c707-470b-8b92-3f5f98e9a856", "56cbdc07-71c2-4392-b593-d9f3e159f33c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "adf8e0b7-cd13-415f-bbf1-b8f4521d5af7", "logId": "69d0ecb6-c7a6-4579-a1e4-dad53538abde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adf8e0b7-cd13-415f-bbf1-b8f4521d5af7", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033481951600}, "additional": {"logType": "detail", "children": [], "durationId": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5"}}, {"head": {"id": "7ada8a3d-5241-4e42-b370-bc5b45e6d6f7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033482483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "746a6c30-8f63-4a02-ac5a-f99b46822865", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033482596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14648e6c-604f-4303-b41e-931127fc11dd", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033483583800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c515da86-1d6b-41b0-acac-79e74f3b9db6", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033485906100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f706c53-ce3d-45c7-8ed5-ce6b84240d3d", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033512714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c15b052-5971-4614-ad6b-079f0d89d542", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033513018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b0435dc-428c-4635-b02b-b6a7d668adf7", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033515446700, "endTime": 14033624235100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5", "logId": "74b63920-ad31-4018-9e80-cc6f8fb0f669"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74b63920-ad31-4018-9e80-cc6f8fb0f669", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033515446700, "endTime": 14033624235100}, "additional": {"logType": "info", "children": [], "durationId": "5b0435dc-428c-4635-b02b-b6a7d668adf7", "parent": "69d0ecb6-c7a6-4579-a1e4-dad53538abde"}}, {"head": {"id": "75552ab7-c181-440d-9d61-eb324785069e", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033624389900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304948fc-f66a-4aa4-9700-b68cfdb4c6b2", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033625661600, "endTime": 14041761489200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5", "logId": "d62791c7-df92-4b07-a60c-8ea5cd2a5396"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38bf74d3-8f97-4cc6-bd26-4879f6a00fef", "name": "current process  memoryUsage: {\n  rss: 88457216,\n  heapTotal: 122454016,\n  heapUsed: 111353248,\n  external: 3142273,\n  arrayBuffers: 136138\n} os memoryUsage :6.501644134521484", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033627363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5243e4f0-cb86-41ee-af52-dfaca6302286", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041757283200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d62791c7-df92-4b07-a60c-8ea5cd2a5396", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033625661600, "endTime": 14041761489200}, "additional": {"logType": "info", "children": [], "durationId": "304948fc-f66a-4aa4-9700-b68cfdb4c6b2", "parent": "69d0ecb6-c7a6-4579-a1e4-dad53538abde"}}, {"head": {"id": "94a20dea-5333-4e35-aaa6-0ca50873dee3", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041761716400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4da8b06-c707-470b-8b92-3f5f98e9a856", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041763740700, "endTime": 14041897888300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5", "logId": "ea53c364-532b-4431-b0ab-ccf9782695df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95aa9160-aa91-4a8d-bffa-c0e518ca2eee", "name": "current process  memoryUsage: {\n  rss: 88891392,\n  heapTotal: 122454016,\n  heapUsed: 111639560,\n  external: 3142399,\n  arrayBuffers: 136279\n} os memoryUsage :6.243598937988281", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041765885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcbf037-afeb-4474-b2b8-39480c8493e4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041895606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea53c364-532b-4431-b0ab-ccf9782695df", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041763740700, "endTime": 14041897888300}, "additional": {"logType": "info", "children": [], "durationId": "a4da8b06-c707-470b-8b92-3f5f98e9a856", "parent": "69d0ecb6-c7a6-4579-a1e4-dad53538abde"}}, {"head": {"id": "952ecac5-ac30-42ae-8323-1cbd6b77e618", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041898143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56cbdc07-71c2-4392-b593-d9f3e159f33c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041899387400, "endTime": 14042245318900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5", "logId": "ba3a78d5-3c0b-49d6-8a96-a0eabbcf6c23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df0f4c8f-d944-47b2-8293-f76cda72754d", "name": "current process  memoryUsage: {\n  rss: 88915968,\n  heapTotal: 122454016,\n  heapUsed: 111911824,\n  external: 3142525,\n  arrayBuffers: 137219\n} os memoryUsage :6.243518829345703", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041900474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5eaeb2c-7219-42a8-99a0-163c63a94fab", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042242336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba3a78d5-3c0b-49d6-8a96-a0eabbcf6c23", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14041899387400, "endTime": 14042245318900}, "additional": {"logType": "info", "children": [], "durationId": "56cbdc07-71c2-4392-b593-d9f3e159f33c", "parent": "69d0ecb6-c7a6-4579-a1e4-dad53538abde"}}, {"head": {"id": "92d2b9f6-f834-49c8-9a4e-23d0d5cec154", "name": "entry : default@PreviewCompileResource cost memory 0.13135528564453125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042246206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d35c65-ad8a-44e2-8952-899cfd311150", "name": "runTaskFromQueue task cost before running: 10 s 214 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042246390800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d0ecb6-c7a6-4579-a1e4-dad53538abde", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14033485880300, "endTime": 14042246469800, "totalTime": 8760461200}, "additional": {"logType": "info", "children": ["74b63920-ad31-4018-9e80-cc6f8fb0f669", "d62791c7-df92-4b07-a60c-8ea5cd2a5396", "ea53c364-532b-4431-b0ab-ccf9782695df", "ba3a78d5-3c0b-49d6-8a96-a0eabbcf6c23"], "durationId": "8b3d60b9-3549-431d-a2ed-7e5bbcc6c5f5"}}, {"head": {"id": "41ebe747-8147-4cdc-9a07-e01a2caeb6b5", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249669500, "endTime": 14042250046300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9899fd14-54f5-4837-8046-f2ce4dfee9cc", "logId": "9521811b-f929-47f0-826c-749a4c3d3ad3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9899fd14-54f5-4837-8046-f2ce4dfee9cc", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042248977200}, "additional": {"logType": "detail", "children": [], "durationId": "41ebe747-8147-4cdc-9a07-e01a2caeb6b5"}}, {"head": {"id": "ff06bd92-93ee-46bf-8c13-60b9131333ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65cd18a-d61f-4e0d-9380-cc9da0bcfe29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b7b4ab-e5fd-42f9-b851-c6a2082c4718", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249675600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e75afab-4575-4744-9a73-57122b37f934", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929c525e-b6e7-4247-988e-870ea5adcf14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249819300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39a7d77-c35c-426b-90fd-0492f714e882", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249884700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3364dca7-6291-4017-96f0-a5c7820ec782", "name": "runTaskFromQueue task cost before running: 10 s 217 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249985400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9521811b-f929-47f0-826c-749a4c3d3ad3", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042249669500, "endTime": 14042250046300, "totalTime": 278800}, "additional": {"logType": "info", "children": [], "durationId": "41ebe747-8147-4cdc-9a07-e01a2caeb6b5"}}, {"head": {"id": "a8fffb3a-3691-4c63-a377-bbd1ec67c2e4", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042252995600, "endTime": 14042264482100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "4b20e4f2-a75a-41ae-9df6-a82201537fa2", "logId": "4a6cea67-7137-4dfb-869a-2c2106c7c6ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b20e4f2-a75a-41ae-9df6-a82201537fa2", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042251670500}, "additional": {"logType": "detail", "children": [], "durationId": "a8fffb3a-3691-4c63-a377-bbd1ec67c2e4"}}, {"head": {"id": "5b28da87-05e6-42a0-b44a-12426c4a45cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042252214700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d503f5e-f823-49e3-b8d5-9d520dd76513", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042252314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b189119-3942-40f0-9d8f-f38187bef6bb", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042253006300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9476590c-b41f-4c01-9c33-ca0f48708040", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042254323100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1995378a-09e8-4433-a6de-35b82c2a3060", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042254417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabf39e7-3125-451d-965a-90c2a7b22a16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042254490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a75cac09-17ad-46e5-a0d2-ee2be163d782", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042254542500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3fea92-df88-485f-86f9-33104a77a6a7", "name": "entry : default@CopyPreviewProfile cost memory 0.2042999267578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042263992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc0e629-859a-46e8-b2fa-8418a3c07f4b", "name": "runTaskFromQueue task cost before running: 10 s 231 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042264325400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6cea67-7137-4dfb-869a-2c2106c7c6ba", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042252995600, "endTime": 14042264482100, "totalTime": 11267500}, "additional": {"logType": "info", "children": [], "durationId": "a8fffb3a-3691-4c63-a377-bbd1ec67c2e4"}}, {"head": {"id": "2b52b132-7718-446f-ad39-ce58d2d35d20", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042292914300, "endTime": 14042310747400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "43f1417d-8c1d-4f95-a3fe-bc094bece917", "logId": "994ba5b0-30b8-4eaa-971e-adc89b0e2563"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43f1417d-8c1d-4f95-a3fe-bc094bece917", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042268644800}, "additional": {"logType": "detail", "children": [], "durationId": "2b52b132-7718-446f-ad39-ce58d2d35d20"}}, {"head": {"id": "b044a022-db25-43ad-aa3a-c792740664bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042269767900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673a16b4-b1bc-498b-934e-9c3263598992", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042269955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbec698-9b28-4e7e-9e00-01c3c0d5c1b8", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042292935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f3ddb83-1415-418d-9b01-b1552da5ffa7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042293163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b22c98-c193-4e40-a51d-8f33d6210810", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042293228700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e05e7966-9e95-4ae6-87bb-0a71cdec6287", "name": "entry : default@ReplacePreviewerPage cost memory -1.7132949829101562", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042310401700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3659947c-af19-44d2-833e-fe9c6745a396", "name": "runTaskFromQueue task cost before running: 10 s 278 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042310650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994ba5b0-30b8-4eaa-971e-adc89b0e2563", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042292914300, "endTime": 14042310747400, "totalTime": 17696900}, "additional": {"logType": "info", "children": [], "durationId": "2b52b132-7718-446f-ad39-ce58d2d35d20"}}, {"head": {"id": "8924c071-5ccb-45cd-a8ea-309545485890", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312715200, "endTime": 14042312992200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "341d1add-d729-48b7-b283-e93bfdabe065", "logId": "29139896-c509-424d-b3c3-e4bacd24eb92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "341d1add-d729-48b7-b283-e93bfdabe065", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312653400}, "additional": {"logType": "detail", "children": [], "durationId": "8924c071-5ccb-45cd-a8ea-309545485890"}}, {"head": {"id": "946a82e7-cff0-444e-b8f8-1b22c8227abc", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acc0295-60d2-4438-b167-eed27a32dfc8", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312846600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3ba31c-0897-4b9c-96bf-d97acc393425", "name": "runTaskFromQueue task cost before running: 10 s 280 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312931400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29139896-c509-424d-b3c3-e4bacd24eb92", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042312715200, "endTime": 14042312992200, "totalTime": 196200}, "additional": {"logType": "info", "children": [], "durationId": "8924c071-5ccb-45cd-a8ea-309545485890"}}, {"head": {"id": "9cfc6b7f-1d3d-479a-99e3-0ce928b2254c", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042315809600, "endTime": 14042318636300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "2a2c4a45-68b6-4edb-b639-a2b0ea8f033d", "logId": "3891cd40-9ddd-4a10-82e9-6d53d722cceb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a2c4a45-68b6-4edb-b639-a2b0ea8f033d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042314511800}, "additional": {"logType": "detail", "children": [], "durationId": "9cfc6b7f-1d3d-479a-99e3-0ce928b2254c"}}, {"head": {"id": "8c8d85a7-6295-436a-a9a3-dda51572dd79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042314980400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf06e29-3512-4199-941e-393c91f614c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042315077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e76eee67-580d-4f65-8722-1323756a7e94", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042315819300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "699e1fa3-0fa6-4124-bef6-ac045df9e881", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042317570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1032a31a-9fbf-4cf1-833f-e3f239325f22", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042317667500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f0e43f-888f-475a-8c2e-fa7e0e51b544", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042317745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f6c11c-47ee-4002-94d2-e1e776d72b3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042317798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1eb437d-f60a-4164-b325-b256a01c5eda", "name": "entry : default@PreviewUpdateAssets cost memory 0.129638671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042318457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14efeb23-5668-401a-baa7-94b2e32ccf3a", "name": "runTaskFromQueue task cost before running: 10 s 286 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042318562700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3891cd40-9ddd-4a10-82e9-6d53d722cceb", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042315809600, "endTime": 14042318636300, "totalTime": 2734100}, "additional": {"logType": "info", "children": [], "durationId": "9cfc6b7f-1d3d-479a-99e3-0ce928b2254c"}}, {"head": {"id": "aa9110b8-609a-411b-9bac-09bb10f65a9b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042332694200, "endTime": 14057715369600}, "additional": {"children": ["6bd6007e-25eb-4129-a0a5-725164506e89"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "525c21e4-f0cf-42de-98c4-b7c2315fe3cb", "logId": "5a00c182-4e34-4b8c-b917-1d090db59c95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "525c21e4-f0cf-42de-98c4-b7c2315fe3cb", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042320755400}, "additional": {"logType": "detail", "children": [], "durationId": "aa9110b8-609a-411b-9bac-09bb10f65a9b"}}, {"head": {"id": "d80d0208-6e33-4084-8153-1fe9aa11ab65", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042321265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84af30f3-4524-4c54-9490-3b0d791d176f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042321371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f38f091-4954-4686-b07e-7648a56f4a8a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042332711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d13c95-a6f1-4446-831e-98130a18c11a", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042343717800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e245b1e6-b4ff-4932-ad7d-b6a94e1652c0", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042343861400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd6007e-25eb-4129-a0a5-725164506e89", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14042358854700, "endTime": 14057711984100}, "additional": {"children": ["f1a592a9-9305-46ad-b78b-088bddb0e7c1", "77059dfb-9f03-4042-8a28-29ccbc772c75", "464bfbd5-b8e0-4e76-aeac-ce48766dae94", "40cbca8f-efb7-48b1-9735-94219c0380af", "a6593cf6-414f-434b-bd6c-7a36c119f87f", "70b7e725-80fb-4c8a-9212-fee3102cfc31"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aa9110b8-609a-411b-9bac-09bb10f65a9b", "logId": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dae9a7c4-2fb2-4a29-8316-0a23dbbacb1e", "name": "entry : default@PreviewArkTS cost memory 0.11301422119140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042361106800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716b4872-7300-4ef1-8fe5-8e0bfa1f09b6", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14048236044900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a592a9-9305-46ad-b78b-088bddb0e7c1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14048237122700, "endTime": 14048237139000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "ebfbe41c-6cad-4381-a0be-625ab463f161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebfbe41c-6cad-4381-a0be-625ab463f161", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14048237122700, "endTime": 14048237139000}, "additional": {"logType": "info", "children": [], "durationId": "f1a592a9-9305-46ad-b78b-088bddb0e7c1", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "0845349f-a7e5-4a2d-8e04-f1667a652c9e", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057710557600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77059dfb-9f03-4042-8a28-29ccbc772c75", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14057711678800, "endTime": 14057711727600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "065a12bb-657f-446f-9fce-06e3675d30b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "065a12bb-657f-446f-9fce-06e3675d30b8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057711678800, "endTime": 14057711727600}, "additional": {"logType": "info", "children": [], "durationId": "77059dfb-9f03-4042-8a28-29ccbc772c75", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14042358854700, "endTime": 14057711984100}, "additional": {"logType": "info", "children": ["ebfbe41c-6cad-4381-a0be-625ab463f161", "065a12bb-657f-446f-9fce-06e3675d30b8", "30a91262-a872-4973-aa19-d940133d93f7", "2ee52e27-6b54-4ac2-b1da-64acacd50d51", "5913d995-d0ea-42e2-9d12-ca3af6864e93", "a6bfaa8a-2930-4ff1-8510-3d20a51b3c42"], "durationId": "6bd6007e-25eb-4129-a0a5-725164506e89", "parent": "5a00c182-4e34-4b8c-b917-1d090db59c95"}}, {"head": {"id": "464bfbd5-b8e0-4e76-aeac-ce48766dae94", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14046756984300, "endTime": 14048178536700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "30a91262-a872-4973-aa19-d940133d93f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30a91262-a872-4973-aa19-d940133d93f7", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14046756984300, "endTime": 14048178536700}, "additional": {"logType": "info", "children": [], "durationId": "464bfbd5-b8e0-4e76-aeac-ce48766dae94", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "40cbca8f-efb7-48b1-9735-94219c0380af", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14048178747400, "endTime": 14048214542300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "2ee52e27-6b54-4ac2-b1da-64acacd50d51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ee52e27-6b54-4ac2-b1da-64acacd50d51", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14048178747400, "endTime": 14048214542300}, "additional": {"logType": "info", "children": [], "durationId": "40cbca8f-efb7-48b1-9735-94219c0380af", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "a6593cf6-414f-434b-bd6c-7a36c119f87f", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14048214648600, "endTime": 14048214776600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "5913d995-d0ea-42e2-9d12-ca3af6864e93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5913d995-d0ea-42e2-9d12-ca3af6864e93", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14048214648600, "endTime": 14048214776600}, "additional": {"logType": "info", "children": [], "durationId": "a6593cf6-414f-434b-bd6c-7a36c119f87f", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "70b7e725-80fb-4c8a-9212-fee3102cfc31", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker7", "startTime": 14048214840700, "endTime": 14057710585500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6bd6007e-25eb-4129-a0a5-725164506e89", "logId": "a6bfaa8a-2930-4ff1-8510-3d20a51b3c42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6bfaa8a-2930-4ff1-8510-3d20a51b3c42", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14048214840700, "endTime": 14057710585500}, "additional": {"logType": "info", "children": [], "durationId": "70b7e725-80fb-4c8a-9212-fee3102cfc31", "parent": "1aab2ec9-0915-43f3-9ed1-76eccc68df9a"}}, {"head": {"id": "5a00c182-4e34-4b8c-b917-1d090db59c95", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14042332694200, "endTime": 14057715369600, "totalTime": 15382661800}, "additional": {"logType": "info", "children": ["1aab2ec9-0915-43f3-9ed1-76eccc68df9a"], "durationId": "aa9110b8-609a-411b-9bac-09bb10f65a9b"}}, {"head": {"id": "cc20aa4f-aca4-487b-9486-363933402095", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720610700, "endTime": 14057720926600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8fb44fd8-4b0d-4d74-891f-28cb32348c4c", "logId": "7eb6777c-4a41-4407-bc69-a4b379f931bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fb44fd8-4b0d-4d74-891f-28cb32348c4c", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720548200}, "additional": {"logType": "detail", "children": [], "durationId": "cc20aa4f-aca4-487b-9486-363933402095"}}, {"head": {"id": "5474d8e7-9f6a-4c6c-aeb5-ccb126cd6a0c", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720623900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2b163b-9625-4319-b305-a50a45362e39", "name": "entry : PreviewBuild cost memory 0.01367950439453125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f00aa81-07c9-4a4f-b1d1-bba084f418bd", "name": "runTaskFromQueue task cost before running: 25 s 688 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb6777c-4a41-4407-bc69-a4b379f931bf", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057720610700, "endTime": 14057720926600, "totalTime": 225000}, "additional": {"logType": "info", "children": [], "durationId": "cc20aa4f-aca4-487b-9486-363933402095"}}, {"head": {"id": "b3c09df5-46bf-4421-977a-6a865a379724", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057726565100, "endTime": 14057726585800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14b5b37a-e31e-41a9-9d77-966ecdac5c7e", "logId": "62f2f03f-d3b1-4b0b-8a81-417e5e914573"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62f2f03f-d3b1-4b0b-8a81-417e5e914573", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057726565100, "endTime": 14057726585800}, "additional": {"logType": "info", "children": [], "durationId": "b3c09df5-46bf-4421-977a-6a865a379724"}}, {"head": {"id": "8ad68663-10c9-4fe2-9f42-4f769be1f8c6", "name": "BUILD SUCCESSFUL in 25 s 694 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057726626100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "0d8144c5-0b07-42e2-8e42-7507f284882e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14032033327500, "endTime": 14057726864100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 44}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9579ae77-860c-4d0f-9aa1-ec3b5fe7e2cf", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057726888000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac2be74e-b7a0-40c5-8bc5-78e87200c4f9", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057726954900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79519437-c904-4e4f-bd29-65c6f3871efe", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727008300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "585c71e8-ddcf-401e-bc1e-79485c505a95", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727059900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63701775-6f67-40be-bd7f-3ada892525ab", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727110700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ade42b-a5ba-4e46-a88e-a820594933d3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727162500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d909054-a6d8-4e23-a406-9b570cd3f65b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727213000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d702ce8-56a0-4cf2-94dc-c53570b87839", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057727994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf8b8c66-8f70-414f-9648-555d50f0293b", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057735804600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d02a0a4-d716-498b-8014-8dd44dfea792", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057736198700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e7038f-6a82-43d6-8fbe-1a14b7ec5757", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057747195200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4912eb6-95a4-473f-9147-993d55f6c80e", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:21 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057747891600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e9740b-85db-4e91-9d03-ecc469343464", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057748099900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7ca2f6e-4200-447f-b32e-02c59eaf032a", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057748917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b91448-a5c3-4fdb-9a5b-25b9fac54072", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057749760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d6f44b-2c2b-487e-bc14-bae978ab152c", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057750129300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9653193-d28a-42d7-bb7a-8488eb958447", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057750434300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd17d94d-cd5b-480c-8e5c-59cdc6554d75", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057750759400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d08cd1-889e-43cc-a980-75409aa35541", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057753939200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d34cb4ed-9a22-4a1d-93ea-32f17049ce08", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057754833100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01f1458-3946-4a06-98de-e5b2e18c1129", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057754943400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fcf3814-ef6b-4865-965a-6f6d4ad5116c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057755273300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d798fc97-e02d-4230-a28b-3632b230deee", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057756191900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8123b4-ff7f-4d51-a18f-6ea6fd7a8b55", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057766944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be79dfc0-9adb-41fc-b1af-772b92c80f3b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057767303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2bdf6cd-7375-40c0-a23e-014515ac8912", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057767597500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6fffabc-1585-4f96-a514-0f83051f5054", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057767956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd871f6-efca-4699-97eb-8a31f6dca53f", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14057768243500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}