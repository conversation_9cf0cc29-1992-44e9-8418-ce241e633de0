{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "c901cefd-fbdf-4ea8-88af-3a645ba4e149", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675359228200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13d6df66-1622-4167-bef6-87d049a31458", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675434271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af78368-500a-4c5c-82f0-69799ccf5743", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675434542300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f81aeeb-3682-4a72-8e6a-a3f8d82e1c4c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701821153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701830343700, "endTime": 15702048721500}, "additional": {"children": ["7a23145a-1655-45f8-865a-6a3d4e9f20cd", "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "5184794b-91e0-435e-aa9d-9161e57f59ab", "a48fb1c5-c0fa-4d9f-9ff9-ea4bb13143ab", "d7c26493-7388-4b2a-93db-b6048904ee1c", "9731f78e-760d-4511-be99-3a3b49a16402", "3cfde6c2-a8a5-455b-af91-6a0add8be894"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a23145a-1655-45f8-865a-6a3d4e9f20cd", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701830345100, "endTime": 15701844640900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "4f3b4901-581a-46cf-8f86-cc6cd48441ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701844658100, "endTime": 15702046885400}, "additional": {"children": ["f5d61444-ad38-48d0-b368-6f7fdbd45754", "3eb967c8-ef6d-4f64-a0e7-27be3d9ae03e", "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "c7bb87e3-afdf-403e-928e-fb534aea8771", "aa4ca354-37d4-449f-ac9a-261616c1e5aa", "e9c4547c-fbfe-425b-8636-5f18925b6dda", "28704db2-c4fa-4f36-98b1-79fcca8f228f", "3fb2b289-3cda-45f7-b056-c43ba12a0f68", "f548a684-7c3e-4e9d-bbac-a5b98b9bcb46"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "8332d207-7e70-48da-a970-27adda84d49a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5184794b-91e0-435e-aa9d-9161e57f59ab", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702046915100, "endTime": 15702048691200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "78b3e6c6-1152-45e7-8457-b9aa7dbb2447"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a48fb1c5-c0fa-4d9f-9ff9-ea4bb13143ab", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702048701100, "endTime": 15702048702800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "eb96f244-a1c9-41cf-8f9a-91c85cbb6368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7c26493-7388-4b2a-93db-b6048904ee1c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701834458300, "endTime": 15701834487200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "6d7eb1cd-90bb-4b70-b87e-385cd2d661e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d7eb1cd-90bb-4b70-b87e-385cd2d661e6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701834458300, "endTime": 15701834487200}, "additional": {"logType": "info", "children": [], "durationId": "d7c26493-7388-4b2a-93db-b6048904ee1c", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "9731f78e-760d-4511-be99-3a3b49a16402", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701839732700, "endTime": 15701839749200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "ea347a38-516e-4e90-b1ae-b404e469cb4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea347a38-516e-4e90-b1ae-b404e469cb4f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701839732700, "endTime": 15701839749200}, "additional": {"logType": "info", "children": [], "durationId": "9731f78e-760d-4511-be99-3a3b49a16402", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "8f165451-424d-41d8-b633-ca5ecf7d3891", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701839800100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c52a61-8045-47d4-a819-68b4e0b5e984", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701844512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3b4901-581a-46cf-8f86-cc6cd48441ad", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701830345100, "endTime": 15701844640900}, "additional": {"logType": "info", "children": [], "durationId": "7a23145a-1655-45f8-865a-6a3d4e9f20cd", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "f5d61444-ad38-48d0-b368-6f7fdbd45754", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701851094400, "endTime": 15701851102100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "2fb28125-e1c5-4f2a-a89b-39e5c723addc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3eb967c8-ef6d-4f64-a0e7-27be3d9ae03e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701851117800, "endTime": 15701856453400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "50bd6c7e-132c-4bc0-b891-1ca53b446eec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701856469600, "endTime": 15701959665700}, "additional": {"children": ["955a50dd-0c5b-4463-ae94-959c6ef36efa", "e4feb60e-07fc-4e28-be0a-e33592aea4a1", "f791670c-f563-4a31-805c-8ad917677815"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "608d598f-47de-43dd-af77-20c7d0567475"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7bb87e3-afdf-403e-928e-fb534aea8771", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701959682400, "endTime": 15701996226000}, "additional": {"children": ["4aef4c0b-1dc5-4ea1-accd-ece9c13da686"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "20cbb050-1a07-486c-83ae-6f42c08adb4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa4ca354-37d4-449f-ac9a-261616c1e5aa", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701996235900, "endTime": 15702025902600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "547b7729-090f-4b16-9270-2600b7f32bff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9c4547c-fbfe-425b-8636-5f18925b6dda", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702026900200, "endTime": 15702035515800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "633f44c3-9ffd-4abf-9ddf-da3e67481230"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28704db2-c4fa-4f36-98b1-79fcca8f228f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702035539100, "endTime": 15702046728300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "4ad820f3-0f3a-4aa5-b389-d6ed569d549c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fb2b289-3cda-45f7-b056-c43ba12a0f68", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702046766700, "endTime": 15702046872800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "3f71dea8-bef9-4558-aa55-214632b5a12e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fb28125-e1c5-4f2a-a89b-39e5c723addc", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701851094400, "endTime": 15701851102100}, "additional": {"logType": "info", "children": [], "durationId": "f5d61444-ad38-48d0-b368-6f7fdbd45754", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "50bd6c7e-132c-4bc0-b891-1ca53b446eec", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701851117800, "endTime": 15701856453400}, "additional": {"logType": "info", "children": [], "durationId": "3eb967c8-ef6d-4f64-a0e7-27be3d9ae03e", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "955a50dd-0c5b-4463-ae94-959c6ef36efa", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701857175300, "endTime": 15701857191700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "logId": "3ada8c48-251f-46e4-b43e-b8ae5daff8a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ada8c48-251f-46e4-b43e-b8ae5daff8a8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701857175300, "endTime": 15701857191700}, "additional": {"logType": "info", "children": [], "durationId": "955a50dd-0c5b-4463-ae94-959c6ef36efa", "parent": "608d598f-47de-43dd-af77-20c7d0567475"}}, {"head": {"id": "e4feb60e-07fc-4e28-be0a-e33592aea4a1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701859606700, "endTime": 15701958688100}, "additional": {"children": ["4ac6a8bd-84f8-4cb6-a778-ff1c77b62ffd", "6d754ad3-7e81-42a2-8f29-5131a079ed60"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "logId": "8a88abbf-c9d1-4147-b1d2-3c65a7015624"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ac6a8bd-84f8-4cb6-a778-ff1c77b62ffd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701859608400, "endTime": 15701865687300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4feb60e-07fc-4e28-be0a-e33592aea4a1", "logId": "65402a67-28c5-44e6-b054-7c90fb5dcee8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d754ad3-7e81-42a2-8f29-5131a079ed60", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701865705800, "endTime": 15701958648200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4feb60e-07fc-4e28-be0a-e33592aea4a1", "logId": "552aca25-5797-4085-a6f6-e5fba35732a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5419bf4-4994-40f3-8495-2a8190804b80", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701859614200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0daf503d-bbdd-4be2-96e6-f776179d73e1", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701865512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65402a67-28c5-44e6-b054-7c90fb5dcee8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701859608400, "endTime": 15701865687300}, "additional": {"logType": "info", "children": [], "durationId": "4ac6a8bd-84f8-4cb6-a778-ff1c77b62ffd", "parent": "8a88abbf-c9d1-4147-b1d2-3c65a7015624"}}, {"head": {"id": "c9cdd308-469d-4e74-96c2-1f4285fb01ec", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701865720800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b09575-62b0-4170-abf0-1209b444f2cd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701872867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1454b1c-f41b-41f2-8fb2-9b6894de4922", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701872984000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7391216d-5385-4e16-8588-dbb162843e2f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701873116200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e8bd73-5eaf-4e8a-a9e8-3c53f8ca4e86", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701873215200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9458cbb-15f7-4634-8fac-378a551119c3", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701875233300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70718fca-8bb8-436b-b552-73c362b91da8", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701880537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda89288-df96-44a1-806d-c4386dc50491", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701892710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bac845a-8cb8-477a-a725-0b779c165aa7", "name": "Sdk init in 41 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701921872000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c784cbc4-86f4-4882-94f4-d1ebb2bcabf4", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701922029100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 11}, "markType": "other"}}, {"head": {"id": "c3dda8b3-4c84-4904-ad77-97a00a2ce296", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701922048100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 11}, "markType": "other"}}, {"head": {"id": "fc8403d7-428c-4f2c-a0b4-ba286c1443a8", "name": "Project task initialization takes 35 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701958097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7d516a-8ba4-4cc8-89d2-3b3b20670fda", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701958299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa6dcfa-b381-415d-b5ce-78442153ef93", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701958429900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2c2af2-5b30-4571-ac4f-e74a161b5d5e", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701958544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552aca25-5797-4085-a6f6-e5fba35732a0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701865705800, "endTime": 15701958648200}, "additional": {"logType": "info", "children": [], "durationId": "6d754ad3-7e81-42a2-8f29-5131a079ed60", "parent": "8a88abbf-c9d1-4147-b1d2-3c65a7015624"}}, {"head": {"id": "8a88abbf-c9d1-4147-b1d2-3c65a7015624", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701859606700, "endTime": 15701958688100}, "additional": {"logType": "info", "children": ["65402a67-28c5-44e6-b054-7c90fb5dcee8", "552aca25-5797-4085-a6f6-e5fba35732a0"], "durationId": "e4feb60e-07fc-4e28-be0a-e33592aea4a1", "parent": "608d598f-47de-43dd-af77-20c7d0567475"}}, {"head": {"id": "f791670c-f563-4a31-805c-8ad917677815", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701959621400, "endTime": 15701959650000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "logId": "9630df5b-0bc5-4991-a654-c99c5e883d6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9630df5b-0bc5-4991-a654-c99c5e883d6d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701959621400, "endTime": 15701959650000}, "additional": {"logType": "info", "children": [], "durationId": "f791670c-f563-4a31-805c-8ad917677815", "parent": "608d598f-47de-43dd-af77-20c7d0567475"}}, {"head": {"id": "608d598f-47de-43dd-af77-20c7d0567475", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701856469600, "endTime": 15701959665700}, "additional": {"logType": "info", "children": ["3ada8c48-251f-46e4-b43e-b8ae5daff8a8", "8a88abbf-c9d1-4147-b1d2-3c65a7015624", "9630df5b-0bc5-4991-a654-c99c5e883d6d"], "durationId": "b36f372e-2e4d-4d3f-9c2c-fa991dcb4845", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "4aef4c0b-1dc5-4ea1-accd-ece9c13da686", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701960638800, "endTime": 15701996208400}, "additional": {"children": ["b4448948-52f4-4368-9e54-826ca7170dd7", "ef8943fc-b8fa-4e9f-aef7-6c88167f292e", "f8f6332a-596f-4294-9a7d-8a65b3cb2aab"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7bb87e3-afdf-403e-928e-fb534aea8771", "logId": "6234fd82-8acb-4d5c-b21e-da026ee6f208"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4448948-52f4-4368-9e54-826ca7170dd7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701966883100, "endTime": 15701966904800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4aef4c0b-1dc5-4ea1-accd-ece9c13da686", "logId": "9349e1d5-9248-4304-a28e-b717f2866ffc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9349e1d5-9248-4304-a28e-b717f2866ffc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701966883100, "endTime": 15701966904800}, "additional": {"logType": "info", "children": [], "durationId": "b4448948-52f4-4368-9e54-826ca7170dd7", "parent": "6234fd82-8acb-4d5c-b21e-da026ee6f208"}}, {"head": {"id": "ef8943fc-b8fa-4e9f-aef7-6c88167f292e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701970014700, "endTime": 15701994213600}, "additional": {"children": ["dc343f6a-39f8-4f9d-a9bf-8e8020ec2503", "5ef06699-d14e-42b5-b174-50a36cc030b0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4aef4c0b-1dc5-4ea1-accd-ece9c13da686", "logId": "d186538b-202e-4833-9be7-64197dd703d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc343f6a-39f8-4f9d-a9bf-8e8020ec2503", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701970016400, "endTime": 15701975431000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef8943fc-b8fa-4e9f-aef7-6c88167f292e", "logId": "89834b09-3549-4775-b027-e7e107dd8de7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ef06699-d14e-42b5-b174-50a36cc030b0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701975461600, "endTime": 15701994197800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef8943fc-b8fa-4e9f-aef7-6c88167f292e", "logId": "1f54161c-4958-4c37-91cf-809d6d6363c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc16c740-a59e-4dac-8984-d06fda68421a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701970024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cea7041-2086-4091-bcb0-0f82aa761b1e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701975218000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89834b09-3549-4775-b027-e7e107dd8de7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701970016400, "endTime": 15701975431000}, "additional": {"logType": "info", "children": [], "durationId": "dc343f6a-39f8-4f9d-a9bf-8e8020ec2503", "parent": "d186538b-202e-4833-9be7-64197dd703d7"}}, {"head": {"id": "3f37b356-aec0-4ee9-bb8b-d4f16474be24", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701975483100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29781d19-2698-45c4-a076-8cfeaad39bd4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701988471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff2cffc-c048-432f-9039-0c979e41ff94", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701988686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "121eb1b9-56b4-42aa-a60a-55c9093ce0fe", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701989692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd131c4-06ba-4cdf-a133-ccb758486833", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701989871900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce828f87-55f9-441b-92e5-3c3e1c8c659d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701989950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac63028-841f-421c-86f8-8c686a02713d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701990008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f144b33-ed07-4363-a49c-6248a8a8e66d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701990078000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11dd1979-21b8-4c4b-9d5b-f3d51e3191d1", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701993807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "864f1615-2335-4ffa-872f-ee0504a18685", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701993978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a30dbf5-9461-4712-a019-a23dffbe3d90", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701994067100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0af07a6-700e-4b64-a74f-a2300e2f7df1", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701994138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f54161c-4958-4c37-91cf-809d6d6363c9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701975461600, "endTime": 15701994197800}, "additional": {"logType": "info", "children": [], "durationId": "5ef06699-d14e-42b5-b174-50a36cc030b0", "parent": "d186538b-202e-4833-9be7-64197dd703d7"}}, {"head": {"id": "d186538b-202e-4833-9be7-64197dd703d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701970014700, "endTime": 15701994213600}, "additional": {"logType": "info", "children": ["89834b09-3549-4775-b027-e7e107dd8de7", "1f54161c-4958-4c37-91cf-809d6d6363c9"], "durationId": "ef8943fc-b8fa-4e9f-aef7-6c88167f292e", "parent": "6234fd82-8acb-4d5c-b21e-da026ee6f208"}}, {"head": {"id": "f8f6332a-596f-4294-9a7d-8a65b3cb2aab", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701996172000, "endTime": 15701996189000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4aef4c0b-1dc5-4ea1-accd-ece9c13da686", "logId": "0cdb2d15-0205-42e4-8c56-a50f0921d131"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cdb2d15-0205-42e4-8c56-a50f0921d131", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701996172000, "endTime": 15701996189000}, "additional": {"logType": "info", "children": [], "durationId": "f8f6332a-596f-4294-9a7d-8a65b3cb2aab", "parent": "6234fd82-8acb-4d5c-b21e-da026ee6f208"}}, {"head": {"id": "6234fd82-8acb-4d5c-b21e-da026ee6f208", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701960638800, "endTime": 15701996208400}, "additional": {"logType": "info", "children": ["9349e1d5-9248-4304-a28e-b717f2866ffc", "d186538b-202e-4833-9be7-64197dd703d7", "0cdb2d15-0205-42e4-8c56-a50f0921d131"], "durationId": "4aef4c0b-1dc5-4ea1-accd-ece9c13da686", "parent": "20cbb050-1a07-486c-83ae-6f42c08adb4c"}}, {"head": {"id": "20cbb050-1a07-486c-83ae-6f42c08adb4c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701959682400, "endTime": 15701996226000}, "additional": {"logType": "info", "children": ["6234fd82-8acb-4d5c-b21e-da026ee6f208"], "durationId": "c7bb87e3-afdf-403e-928e-fb534aea8771", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "0297fb4c-614c-47e9-a920-6d9ea54a0248", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702025449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4dab99-eb30-4a8b-86d0-b9b3877ca850", "name": "hvigorfile, resolve hvigorfile dependencies in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702025789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "547b7729-090f-4b16-9270-2600b7f32bff", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701996235900, "endTime": 15702025902600}, "additional": {"logType": "info", "children": [], "durationId": "aa4ca354-37d4-449f-ac9a-261616c1e5aa", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "f548a684-7c3e-4e9d-bbac-a5b98b9bcb46", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702026715100, "endTime": 15702026886700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "logId": "0d98734e-6217-4da4-ac0e-545aa7574b19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eec568e0-01af-4c17-90b0-19f2fd567be3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702026734900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d98734e-6217-4da4-ac0e-545aa7574b19", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702026715100, "endTime": 15702026886700}, "additional": {"logType": "info", "children": [], "durationId": "f548a684-7c3e-4e9d-bbac-a5b98b9bcb46", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "a8aeaa0a-d930-4785-8dbb-05f732ec7a33", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702028618200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e90b88d-39c0-45e7-ae0e-f6d1feb3139a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702034666200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "633f44c3-9ffd-4abf-9ddf-da3e67481230", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702026900200, "endTime": 15702035515800}, "additional": {"logType": "info", "children": [], "durationId": "e9c4547c-fbfe-425b-8636-5f18925b6dda", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "6105404d-7714-4cf0-8ae9-cbcd74fd1862", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702035549800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c2c9b2-5652-4552-a892-e2d54954106c", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702041236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58f9d4f-3301-4606-ae98-8aa7d33fdc79", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702041339600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27968f5c-2997-44ec-8780-c33ad072aa24", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702041536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e572e549-c852-40c4-b694-53f6be298928", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702043742000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee18a642-753a-4562-b950-659412f4e5dd", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702043817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad820f3-0f3a-4aa5-b389-d6ed569d549c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702035539100, "endTime": 15702046728300}, "additional": {"logType": "info", "children": [], "durationId": "28704db2-c4fa-4f36-98b1-79fcca8f228f", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "2d2e8f5d-051c-45b2-a5b8-f93e61b1c13e", "name": "Configuration phase cost:196 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702046786500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f71dea8-bef9-4558-aa55-214632b5a12e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702046766700, "endTime": 15702046872800}, "additional": {"logType": "info", "children": [], "durationId": "3fb2b289-3cda-45f7-b056-c43ba12a0f68", "parent": "8332d207-7e70-48da-a970-27adda84d49a"}}, {"head": {"id": "8332d207-7e70-48da-a970-27adda84d49a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701844658100, "endTime": 15702046885400}, "additional": {"logType": "info", "children": ["2fb28125-e1c5-4f2a-a89b-39e5c723addc", "50bd6c7e-132c-4bc0-b891-1ca53b446eec", "608d598f-47de-43dd-af77-20c7d0567475", "20cbb050-1a07-486c-83ae-6f42c08adb4c", "547b7729-090f-4b16-9270-2600b7f32bff", "633f44c3-9ffd-4abf-9ddf-da3e67481230", "4ad820f3-0f3a-4aa5-b389-d6ed569d549c", "3f71dea8-bef9-4558-aa55-214632b5a12e", "0d98734e-6217-4da4-ac0e-545aa7574b19"], "durationId": "0dadd5b0-6cef-431a-be27-2f6c9530b6ef", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "3cfde6c2-a8a5-455b-af91-6a0add8be894", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702048638200, "endTime": 15702048665100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c", "logId": "406c01f7-ff5b-445d-9466-542bde566ec1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "406c01f7-ff5b-445d-9466-542bde566ec1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702048638200, "endTime": 15702048665100}, "additional": {"logType": "info", "children": [], "durationId": "3cfde6c2-a8a5-455b-af91-6a0add8be894", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "78b3e6c6-1152-45e7-8457-b9aa7dbb2447", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702046915100, "endTime": 15702048691200}, "additional": {"logType": "info", "children": [], "durationId": "5184794b-91e0-435e-aa9d-9161e57f59ab", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "eb96f244-a1c9-41cf-8f9a-91c85cbb6368", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702048701100, "endTime": 15702048702800}, "additional": {"logType": "info", "children": [], "durationId": "a48fb1c5-c0fa-4d9f-9ff9-ea4bb13143ab", "parent": "d3a5ff58-5c07-472d-a6ff-143db98b73c5"}}, {"head": {"id": "d3a5ff58-5c07-472d-a6ff-143db98b73c5", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701830343700, "endTime": 15702048721500}, "additional": {"logType": "info", "children": ["4f3b4901-581a-46cf-8f86-cc6cd48441ad", "8332d207-7e70-48da-a970-27adda84d49a", "78b3e6c6-1152-45e7-8457-b9aa7dbb2447", "eb96f244-a1c9-41cf-8f9a-91c85cbb6368", "6d7eb1cd-90bb-4b70-b87e-385cd2d661e6", "ea347a38-516e-4e90-b1ae-b404e469cb4f", "406c01f7-ff5b-445d-9466-542bde566ec1"], "durationId": "89b3e5bf-ab4a-4bf2-b2d1-4f4ba6c3bb9c"}}, {"head": {"id": "dd0fff2b-f7a3-4f45-9acf-1986cb2c3c54", "name": "Configuration task cost before running: 224 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702048964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34fb9e7a-9966-448d-a552-b8a90b29a7c1", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702076577700, "endTime": 15702086361500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "82d53d33-84e0-4150-bfdd-d03a9ae6203d", "logId": "ed03ac7c-2dde-4e7a-98c6-c6f43521678e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82d53d33-84e0-4150-bfdd-d03a9ae6203d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702050736800}, "additional": {"logType": "detail", "children": [], "durationId": "34fb9e7a-9966-448d-a552-b8a90b29a7c1"}}, {"head": {"id": "03704c1b-ce2f-4188-aca7-2e9dbebf3aab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702051269000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc652c7d-c897-44d1-9482-b9427dc14c74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702051366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81cc21a5-610a-4dc4-b4da-b1e7a4172d4c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702076594800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf0d008f-7418-49ef-899b-fa5b603a6ab8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702086149600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e381d520-ab0d-4273-a9c7-fe0d2ef3643e", "name": "entry : default@PreBuild cost memory 0.27938079833984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702086285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed03ac7c-2dde-4e7a-98c6-c6f43521678e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702076577700, "endTime": 15702086361500}, "additional": {"logType": "info", "children": [], "durationId": "34fb9e7a-9966-448d-a552-b8a90b29a7c1"}}, {"head": {"id": "ec531486-77ff-4bfe-ba87-ac826dd6ac55", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702101487400, "endTime": 15702103597500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2ccfe8d0-f9be-47b3-b48a-09bb6158619a", "logId": "c57d07c2-1f82-4e08-81de-8bd7d2d30136"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ccfe8d0-f9be-47b3-b48a-09bb6158619a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702090071500}, "additional": {"logType": "detail", "children": [], "durationId": "ec531486-77ff-4bfe-ba87-ac826dd6ac55"}}, {"head": {"id": "6560f000-e5a7-40e9-8a3b-5f19ef8c80fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702090594900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5dc75d-3b31-4674-b9ce-ebea30f37366", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702100505800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da583d9f-63bb-4605-9b26-5b264714a311", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702101501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da445e62-1d68-488f-b6cc-f62fdfd0e421", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702103426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bebc18fa-c75c-4f58-93bd-1ee52a2aa2ac", "name": "entry : default@MergeProfile cost memory 0.11022186279296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702103526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57d07c2-1f82-4e08-81de-8bd7d2d30136", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702101487400, "endTime": 15702103597500}, "additional": {"logType": "info", "children": [], "durationId": "ec531486-77ff-4bfe-ba87-ac826dd6ac55"}}, {"head": {"id": "11c3f5ba-fe77-4b05-a27d-963e4f04f821", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702106495800, "endTime": 15702108803500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c1fbbb1-21be-4aba-8f31-008ac9423dbe", "logId": "d1942f92-925f-4d67-b955-1b6a30f1fe61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c1fbbb1-21be-4aba-8f31-008ac9423dbe", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702105187900}, "additional": {"logType": "detail", "children": [], "durationId": "11c3f5ba-fe77-4b05-a27d-963e4f04f821"}}, {"head": {"id": "39cfc440-9890-4e31-9774-73fbd3d3fd61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702105683000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000652dd-c0ba-4f74-b7ed-87080ded5bd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702105762900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7644e4-73ce-4946-acc3-e4e5a5eaea7b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702106505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c54c739-5935-4ec0-901d-4a907d54c25b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702107373000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c42c05-71a9-4c01-9c27-172c920b559e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702108621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c285bd3-e9a3-4120-b4bc-506bd4e5159a", "name": "entry : default@CreateBuildProfile cost memory 0.0959320068359375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702108713000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1942f92-925f-4d67-b955-1b6a30f1fe61", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702106495800, "endTime": 15702108803500}, "additional": {"logType": "info", "children": [], "durationId": "11c3f5ba-fe77-4b05-a27d-963e4f04f821"}}, {"head": {"id": "4296eaf4-dcf3-43f6-a3ff-826802a94d91", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111792700, "endTime": 15702112189700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1077b544-b7be-4d43-9706-f96ef11f6f00", "logId": "a9f6b166-666d-4264-b129-dd3059264d75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1077b544-b7be-4d43-9706-f96ef11f6f00", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702110437900}, "additional": {"logType": "detail", "children": [], "durationId": "4296eaf4-dcf3-43f6-a3ff-826802a94d91"}}, {"head": {"id": "830e17c0-ba75-47cf-81d0-4a0408803131", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702110941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f032a95d-cb9a-44a3-8ada-5b322e2712fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6402ef6e-717d-439a-bc20-5cf42d5004c9", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111803300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b103302-adea-4715-b46e-fc1f0eaf6b3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1980cb-1844-4fdc-98d2-3c9741c2a6d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2232df3-9269-454d-add9-9bf281b6ee01", "name": "entry : default@PreCheckSyscap cost memory 0.03774261474609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702112052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45448888-523c-42a8-b5b0-b2d479785d85", "name": "runTaskFromQueue task cost before running: 287 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702112133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f6b166-666d-4264-b129-dd3059264d75", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702111792700, "endTime": 15702112189700, "totalTime": 323700}, "additional": {"logType": "info", "children": [], "durationId": "4296eaf4-dcf3-43f6-a3ff-826802a94d91"}}, {"head": {"id": "dafef480-beaf-41af-8400-db61757cdc24", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702123148100, "endTime": 15702124256500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fcdb4b79-575f-4b90-b090-c605f48ea773", "logId": "c91e9ccd-9283-4e92-bb0d-b72166eaca8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcdb4b79-575f-4b90-b090-c605f48ea773", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702113802000}, "additional": {"logType": "detail", "children": [], "durationId": "dafef480-beaf-41af-8400-db61757cdc24"}}, {"head": {"id": "ac56a8a9-83f2-4d0b-a6bf-185b2136aa9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702114361000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b96443d-cb8e-4bad-b20e-b66ef108f1b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702114458700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd03347-7f29-4f31-9b61-3cb96f245cf6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702123166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df265d3c-79ed-42b8-a3bb-2a0abd07c94d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702123381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30681bf1-ec0e-4717-afde-85b8a5f0fb3a", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702124071700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a77d9d-ef48-4e19-a8c0-3ff3f6c66748", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06549072265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702124169500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c91e9ccd-9283-4e92-bb0d-b72166eaca8a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702123148100, "endTime": 15702124256500}, "additional": {"logType": "info", "children": [], "durationId": "dafef480-beaf-41af-8400-db61757cdc24"}}, {"head": {"id": "3dd8d79d-1962-451a-ab05-2cc8b7fd3d4d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702127783100, "endTime": 15702128955400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6fa29be6-fdc2-4698-b448-de83c8046725", "logId": "682ace42-f8fb-443b-9e2e-b84f967daf93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fa29be6-fdc2-4698-b448-de83c8046725", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702125979200}, "additional": {"logType": "detail", "children": [], "durationId": "3dd8d79d-1962-451a-ab05-2cc8b7fd3d4d"}}, {"head": {"id": "6a2d67ea-a13d-4ede-8b50-f1f056116a8c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702126517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8475dbdf-9f31-4fc9-a344-daa86606691f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702126607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e80012-c323-4a59-8b32-d4a64b8c9445", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702127793500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ccadfb-67c7-4092-95cb-69d69dbcd364", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702128796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9cacee-a3f1-4f22-80d0-3472d56b3029", "name": "entry : default@ProcessProfile cost memory 0.0559234619140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702128886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "682ace42-f8fb-443b-9e2e-b84f967daf93", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702127783100, "endTime": 15702128955400}, "additional": {"logType": "info", "children": [], "durationId": "3dd8d79d-1962-451a-ab05-2cc8b7fd3d4d"}}, {"head": {"id": "af4b4ae9-98c0-4475-b6d8-7760688345ad", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702133113300, "endTime": 15702139630100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bf8c3f50-003d-4e3e-9d3b-18b78d3d7c0e", "logId": "9aa2950c-927d-415a-a024-3eef870a9e5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf8c3f50-003d-4e3e-9d3b-18b78d3d7c0e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702130524100}, "additional": {"logType": "detail", "children": [], "durationId": "af4b4ae9-98c0-4475-b6d8-7760688345ad"}}, {"head": {"id": "7886b66e-0111-48de-9fac-a8a504774b83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702131032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac37a31a-e4b6-49ba-aa2d-64ca437f63c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702131119500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa052fb-d182-4a58-90c1-bc38254a9918", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702133126600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ab15fc-206b-464d-9856-56d2fac4775f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702139425600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54d5649-cc39-4952-9d79-074bca880bbe", "name": "entry : default@ProcessRouterMap cost memory 0.18758392333984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702139553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa2950c-927d-415a-a024-3eef870a9e5f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702133113300, "endTime": 15702139630100}, "additional": {"logType": "info", "children": [], "durationId": "af4b4ae9-98c0-4475-b6d8-7760688345ad"}}, {"head": {"id": "1568b20b-b2d6-4cda-ba51-51343be92cf5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702147070800, "endTime": 15702151272400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "904b05bf-1113-46d3-9897-23fbf3ee6fee", "logId": "c32092ba-4945-48e2-b596-0bb47258481c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "904b05bf-1113-46d3-9897-23fbf3ee6fee", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702142492700}, "additional": {"logType": "detail", "children": [], "durationId": "1568b20b-b2d6-4cda-ba51-51343be92cf5"}}, {"head": {"id": "e0c9ae43-5aab-4652-a3ea-7f3466f6b5ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702142996200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038d3d17-62dd-46bb-9d97-d2e5a72566d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702143085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8238455-f927-4892-a0e6-78f0feb3fae8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702144374500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbac030-3aeb-4538-90bf-ca04b7e710e6", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702148677700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cad8aae-2d2d-4066-a14b-b65582da0b17", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702148939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "059ba6cb-f6b4-4e34-9eb2-863446706f1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702149046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f19d8a9-87de-44fc-81d8-46edcc060de0", "name": "entry : default@PreviewProcessResource cost memory 0.0684051513671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702149187400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41f81dd-a792-4e40-8fef-d1290db0d55b", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702151147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c32092ba-4945-48e2-b596-0bb47258481c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702147070800, "endTime": 15702151272400, "totalTime": 2231800}, "additional": {"logType": "info", "children": [], "durationId": "1568b20b-b2d6-4cda-ba51-51343be92cf5"}}, {"head": {"id": "16b4bf84-8e18-483e-ade5-6101d4749e19", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702159856300, "endTime": 15702185269900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8b0a48eb-1fdc-4348-acbb-faaa2f3e713f", "logId": "6c7e9361-fcda-4127-b0cc-03c7553b7461"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b0a48eb-1fdc-4348-acbb-faaa2f3e713f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702155368400}, "additional": {"logType": "detail", "children": [], "durationId": "16b4bf84-8e18-483e-ade5-6101d4749e19"}}, {"head": {"id": "70f2be40-6f5a-4b97-a5ac-7b8ee6cc23c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702155880400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b99bf6-ba05-48f5-959d-20df17250346", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702155968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e309fbf1-f2de-4285-bb73-f90af9e80cec", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702159870600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374d6c82-d0f8-47de-920c-3a5eeb081829", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702184952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4416fa29-a2f1-494f-aec8-2ce8245a8a24", "name": "entry : default@GenerateLoaderJson cost memory 0.7125244140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702185177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7e9361-fcda-4127-b0cc-03c7553b7461", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702159856300, "endTime": 15702185269900}, "additional": {"logType": "info", "children": [], "durationId": "16b4bf84-8e18-483e-ade5-6101d4749e19"}}, {"head": {"id": "adf2057d-538b-438c-ae5a-3da33ea7f901", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702199012100, "endTime": 15702222454800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c7bb694b-e680-41b1-972b-3f8e1fc89f2a", "logId": "17d10b7a-4139-4357-b0b7-5510c701ea46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7bb694b-e680-41b1-972b-3f8e1fc89f2a", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702194238800}, "additional": {"logType": "detail", "children": [], "durationId": "adf2057d-538b-438c-ae5a-3da33ea7f901"}}, {"head": {"id": "34412256-5ddd-4cf3-b04f-11fbe1668c77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702194776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51be22fb-079e-43c1-8a1d-89874708541b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702194883900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb57f885-a0c2-4f86-9786-5dae88119df0", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702195890800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33dd9feb-1df0-4a81-b24e-ab3597a83c2c", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702199047000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5814db3c-5c27-4419-9ad8-28ae8ea7043e", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702222214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e8687e1-cb88-4bc2-a98f-a445f85027c2", "name": "entry : default@PreviewCompileResource cost memory -1.0776596069335938", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702222358300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17d10b7a-4139-4357-b0b7-5510c701ea46", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702199012100, "endTime": 15702222454800}, "additional": {"logType": "info", "children": [], "durationId": "adf2057d-538b-438c-ae5a-3da33ea7f901"}}, {"head": {"id": "d46da95d-65f2-476e-bca1-72b0c5d98c41", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225479700, "endTime": 15702225861700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0187e3f9-8574-4f0a-9efd-e8990f684295", "logId": "d94c6363-8e42-4fb3-a7dd-0f8c724b6eff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0187e3f9-8574-4f0a-9efd-e8990f684295", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702224781000}, "additional": {"logType": "detail", "children": [], "durationId": "d46da95d-65f2-476e-bca1-72b0c5d98c41"}}, {"head": {"id": "0c4888c1-9654-4d73-bdb4-2799abb20848", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4033ba2-76cb-497f-b9a5-b4f1d8c6c38d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225390100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf6108b-e354-4784-b7af-88b74b988bbe", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a0d152-2bea-4439-b634-58b7f8308019", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a5ff93-eb0d-4d60-95c2-572e001dd679", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225630300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04176fb2-1ae8-435c-b75b-e48701c1ef9b", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3622f09-c28c-424c-bf5f-e4b5b254f60c", "name": "runTaskFromQueue task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d94c6363-8e42-4fb3-a7dd-0f8c724b6eff", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702225479700, "endTime": 15702225861700, "totalTime": 283000}, "additional": {"logType": "info", "children": [], "durationId": "d46da95d-65f2-476e-bca1-72b0c5d98c41"}}, {"head": {"id": "0f1e9ed2-bf9b-4590-b2be-e1f2c7c9a924", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702228855700, "endTime": 15702231310700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7a188953-2058-41b5-b220-e8e207cd1c78", "logId": "18dcee76-2eab-4b19-80bf-c3782678fe66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a188953-2058-41b5-b220-e8e207cd1c78", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702227621500}, "additional": {"logType": "detail", "children": [], "durationId": "0f1e9ed2-bf9b-4590-b2be-e1f2c7c9a924"}}, {"head": {"id": "adb0ae07-b9c3-419b-83a2-1d613459fc4e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702228136100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea44013-09aa-4249-9143-0cf0c9d05f19", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702228236600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d03998-1651-4f49-876a-686965fe8be1", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702228865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "396ef40d-cf0a-4faf-85b7-34d630be4f04", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702231137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97fb0de0-8138-4cfd-93e7-439e17cc5e66", "name": "entry : default@CopyPreviewProfile cost memory 0.09510040283203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702231237900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18dcee76-2eab-4b19-80bf-c3782678fe66", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702228855700, "endTime": 15702231310700}, "additional": {"logType": "info", "children": [], "durationId": "0f1e9ed2-bf9b-4590-b2be-e1f2c7c9a924"}}, {"head": {"id": "53c55e30-cbdd-465b-b007-6bd22b5cc744", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702234970000, "endTime": 15702235414300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "a59f6fcf-162c-4205-a7b8-075bba5aa584", "logId": "5ceb4907-1efa-4ec6-a508-00436a1bbf1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a59f6fcf-162c-4205-a7b8-075bba5aa584", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702233522700}, "additional": {"logType": "detail", "children": [], "durationId": "53c55e30-cbdd-465b-b007-6bd22b5cc744"}}, {"head": {"id": "fddefce7-0d35-43c3-9ad1-1d0ce1a193a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702234082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc33e583-f70a-409c-954d-ebcbdfb449f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702234192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f48e9c-b302-452f-8dac-7c163910cb63", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702235001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7658fdd2-ddb3-4939-9ebb-c953851a9edb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702235110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c43297-ce1d-4d48-8093-b09c2edae7b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702235171600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00f3c532-526e-423d-ac2f-7c4b7c93e0e2", "name": "entry : default@ReplacePreviewerPage cost memory 0.038482666015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702235269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c207938c-4bce-4383-97bd-d107771b2a9b", "name": "runTaskFromQueue task cost before running: 411 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702235355300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ceb4907-1efa-4ec6-a508-00436a1bbf1d", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702234970000, "endTime": 15702235414300, "totalTime": 368600}, "additional": {"logType": "info", "children": [], "durationId": "53c55e30-cbdd-465b-b007-6bd22b5cc744"}}, {"head": {"id": "b791a429-869f-47af-80c2-1ebfb4e3b86a", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237593300, "endTime": 15702238030800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0d4f3407-11bb-4488-8b4b-d200bf51d856", "logId": "1f260301-24b6-463f-b486-07ff8e14bd81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d4f3407-11bb-4488-8b4b-d200bf51d856", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237534600}, "additional": {"logType": "detail", "children": [], "durationId": "b791a429-869f-47af-80c2-1ebfb4e3b86a"}}, {"head": {"id": "2d5d1e4c-b9a8-43c4-b9d0-410ef363618e", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a42290-e852-4e30-875e-ca48e61e7e65", "name": "entry : buildPreviewerResource cost memory 0.02690887451171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19a1f78-1dfe-4002-8a2e-645c7a4b9896", "name": "runTaskFromQueue task cost before running: 413 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237949900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f260301-24b6-463f-b486-07ff8e14bd81", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702237593300, "endTime": 15702238030800, "totalTime": 331800}, "additional": {"logType": "info", "children": [], "durationId": "b791a429-869f-47af-80c2-1ebfb4e3b86a"}}, {"head": {"id": "8ff990f1-f163-4a9b-8305-50a95a4a30fa", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702241526700, "endTime": 15702244072100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "006b336d-9673-4bb9-93fa-4cb57fea8da8", "logId": "9ee732eb-e433-4091-b41e-ddf1eef2944a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "006b336d-9673-4bb9-93fa-4cb57fea8da8", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702240112100}, "additional": {"logType": "detail", "children": [], "durationId": "8ff990f1-f163-4a9b-8305-50a95a4a30fa"}}, {"head": {"id": "72c1651d-1aab-407f-b0ea-dedcdccc0074", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702240671300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b7af1c-fdfe-49ba-9359-11f6f4328dfe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702240775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb77612-2df9-4621-8733-135192f712d9", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702241535700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f176dcb-5f1e-41a4-b61b-f095af4cd079", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702243846200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456a0529-4acf-48b9-b2b7-5b3c840230cf", "name": "entry : default@PreviewUpdateAssets cost memory 0.10341644287109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702244000500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee732eb-e433-4091-b41e-ddf1eef2944a", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702241526700, "endTime": 15702244072100}, "additional": {"logType": "info", "children": [], "durationId": "8ff990f1-f163-4a9b-8305-50a95a4a30fa"}}, {"head": {"id": "8816fe9b-0b70-424d-8dc9-c4da90aa8dba", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702254485900, "endTime": 15719436225200}, "additional": {"children": ["ae5445e8-7526-47ad-b996-94d185d58746"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "9504f090-8dd8-4b1f-b059-c93fd17a8ed3", "logId": "87ef36a7-9b66-4355-8be6-b6d762f0c8ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9504f090-8dd8-4b1f-b059-c93fd17a8ed3", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702246841500}, "additional": {"logType": "detail", "children": [], "durationId": "8816fe9b-0b70-424d-8dc9-c4da90aa8dba"}}, {"head": {"id": "9dcd0965-fc93-4940-b3f7-36e6566b0cfd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702247435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba2c36b-52d0-431d-8546-2809bac8db7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702247544200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce796a3a-482f-42b3-a82d-a55920abd566", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702254501500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4320ad30-cddc-43f2-8fcc-294c54f66c34", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702279801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8070dc-3bab-41f6-810c-019c02a7bf79", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702280057000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5445e8-7526-47ad-b996-94d185d58746", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15702294161600, "endTime": 15719430293600}, "additional": {"children": ["4ad9afec-4d7f-44a7-b9ca-fd3697d817af", "f044c21a-931a-4afa-aaab-e25cc16e53f7", "6c1b2b68-3936-4c58-b59d-ad36a328778e", "02a1487c-bb4f-465f-9552-a2dd3b37c5dc", "96018b23-7c05-4fa0-bc4a-92e0d38469f5", "f5662d87-68e9-4f90-b85e-34643fc535ee"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8816fe9b-0b70-424d-8dc9-c4da90aa8dba", "logId": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8281f9ce-4d02-46e6-a9b6-c4820a6c56fb", "name": "entry : default@PreviewArkTS cost memory 1.482940673828125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702296605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70fb4229-fd73-4612-acdf-d41b9150eb42", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15706945995600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad9afec-4d7f-44a7-b9ca-fd3697d817af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15706947178300, "endTime": 15706947199200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "e6f7fe35-3a03-4af8-a6bd-6246e436a1f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6f7fe35-3a03-4af8-a6bd-6246e436a1f4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15706947178300, "endTime": 15706947199200}, "additional": {"logType": "info", "children": [], "durationId": "4ad9afec-4d7f-44a7-b9ca-fd3697d817af", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "6fd7253d-8fe5-4612-a62d-5621c7b5c29e", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719427333500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f044c21a-931a-4afa-aaab-e25cc16e53f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15719429375700, "endTime": 15719429409700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "8da1761b-df52-4171-b882-e8d2f0d5f1f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8da1761b-df52-4171-b882-e8d2f0d5f1f6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719429375700, "endTime": 15719429409700}, "additional": {"logType": "info", "children": [], "durationId": "f044c21a-931a-4afa-aaab-e25cc16e53f7", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "7c21f95d-40fd-48f3-b417-3a40032d80e9", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15702294161600, "endTime": 15719430293600}, "additional": {"logType": "info", "children": ["e6f7fe35-3a03-4af8-a6bd-6246e436a1f4", "8da1761b-df52-4171-b882-e8d2f0d5f1f6", "e3ee574b-469c-4c72-aeba-36a122950c21", "b41f9e49-6155-42fd-9789-7a58ed43e9f9", "7200411e-85e3-4587-bf26-77547658b239", "fa31a3cf-96a7-40c2-ba1a-e3ecb87260de"], "durationId": "ae5445e8-7526-47ad-b996-94d185d58746", "parent": "87ef36a7-9b66-4355-8be6-b6d762f0c8ef"}}, {"head": {"id": "6c1b2b68-3936-4c58-b59d-ad36a328778e", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15705479923400, "endTime": 15706918278400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "e3ee574b-469c-4c72-aeba-36a122950c21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3ee574b-469c-4c72-aeba-36a122950c21", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15705479923400, "endTime": 15706918278400}, "additional": {"logType": "info", "children": [], "durationId": "6c1b2b68-3936-4c58-b59d-ad36a328778e", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "02a1487c-bb4f-465f-9552-a2dd3b37c5dc", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15706918488300, "endTime": 15706923626700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "b41f9e49-6155-42fd-9789-7a58ed43e9f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b41f9e49-6155-42fd-9789-7a58ed43e9f9", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15706918488300, "endTime": 15706923626700}, "additional": {"logType": "info", "children": [], "durationId": "02a1487c-bb4f-465f-9552-a2dd3b37c5dc", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "96018b23-7c05-4fa0-bc4a-92e0d38469f5", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15706923728500, "endTime": 15706923734000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "7200411e-85e3-4587-bf26-77547658b239"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7200411e-85e3-4587-bf26-77547658b239", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15706923728500, "endTime": 15706923734000}, "additional": {"logType": "info", "children": [], "durationId": "96018b23-7c05-4fa0-bc4a-92e0d38469f5", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "f5662d87-68e9-4f90-b85e-34643fc535ee", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker12", "startTime": 15706923835500, "endTime": 15719427519100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ae5445e8-7526-47ad-b996-94d185d58746", "logId": "fa31a3cf-96a7-40c2-ba1a-e3ecb87260de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa31a3cf-96a7-40c2-ba1a-e3ecb87260de", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15706923835500, "endTime": 15719427519100}, "additional": {"logType": "info", "children": [], "durationId": "f5662d87-68e9-4f90-b85e-34643fc535ee", "parent": "7c21f95d-40fd-48f3-b417-3a40032d80e9"}}, {"head": {"id": "87ef36a7-9b66-4355-8be6-b6d762f0c8ef", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15702254485900, "endTime": 15719436225200, "totalTime": 17181708800}, "additional": {"logType": "info", "children": ["7c21f95d-40fd-48f3-b417-3a40032d80e9"], "durationId": "8816fe9b-0b70-424d-8dc9-c4da90aa8dba"}}, {"head": {"id": "ba394ab5-b9a1-4f83-8b1d-9ff594ebef30", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446205000, "endTime": 15719446762600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "decfcab6-cb6b-4169-adb6-a7bb0a96bf17", "logId": "83a52a0d-9f5e-4aac-b773-e4845925249d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "decfcab6-cb6b-4169-adb6-a7bb0a96bf17", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446129000}, "additional": {"logType": "detail", "children": [], "durationId": "ba394ab5-b9a1-4f83-8b1d-9ff594ebef30"}}, {"head": {"id": "af2d0aea-71b4-4921-aa58-bd708bfaf6d1", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf48f24-8f62-4f0c-9699-682b678e76ae", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f609cf51-6003-4568-82fd-1ac080242525", "name": "runTaskFromQueue task cost before running: 17 s 622 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446637900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a52a0d-9f5e-4aac-b773-e4845925249d", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719446205000, "endTime": 15719446762600, "totalTime": 392700}, "additional": {"logType": "info", "children": [], "durationId": "ba394ab5-b9a1-4f83-8b1d-9ff594ebef30"}}, {"head": {"id": "41719d34-9d85-458b-b775-0fde5c910ca3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719457643900, "endTime": 15719457681100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96884042-1b50-42cd-b8a6-50e7cee8cbc7", "logId": "db92b7fa-4522-4300-a673-d5dcc5e356d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db92b7fa-4522-4300-a673-d5dcc5e356d7", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719457643900, "endTime": 15719457681100}, "additional": {"logType": "info", "children": [], "durationId": "41719d34-9d85-458b-b775-0fde5c910ca3"}}, {"head": {"id": "1b84945a-23cc-4c81-aef5-e8e760423d71", "name": "BUILD SUCCESSFUL in 17 s 633 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719457767000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "eb5403ec-7f88-4905-bca1-59152c56faaf", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15701825204800, "endTime": 15719458175800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 12}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "c083018c-bbe5-4dc2-ac8a-76f21f6ebc4b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a94766f-bf48-4bed-a206-1dbbb5930b0e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458368700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8f64c9-923b-4037-8977-63416491123c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458478700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d12a8b-8b32-4d2b-a290-241b08721247", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b6838b-1a5f-49c4-aa61-b02ec3df4dad", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44465d94-fb00-40a8-9b6a-45f666d1c40a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458767200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dddf28d-ba20-49a8-b076-cc3ad8cc758c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458864100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432ab152-ae20-4671-90cd-b0328ca360ea", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719458963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f30344ac-52b3-4eef-8beb-307379d49d5e", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719459124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ff4397-696d-4be8-ab43-189e7624444c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719459330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f95d8b9-6d81-4eb0-b919-c5092656f41b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719466352900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592442f0-444d-4557-8d95-00362fb0d1e0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719468251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34586bd-d3c7-4acc-b498-ec1ff168c285", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719468935000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c50a66e-5b1d-4300-89f3-e45cdc43ffa8", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719469588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1115904f-32c5-45b4-9b39-5ec7cd87810a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719471515500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e488cb-6762-42f6-ad41-d411d8cdc4c9", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719497233900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6e4ad5-1007-4b83-a032-f22ba4e5a57b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719497945400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af0a1b1-2530-424e-9908-0a5381061ef3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719498604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d0067b-766b-45ee-a337-e59755574a6b", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719499292000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c866589-9f5a-4cb2-8151-5686ee5cf8bf", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:41 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15719499803900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}