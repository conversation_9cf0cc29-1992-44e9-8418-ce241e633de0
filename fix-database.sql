-- 修复数据库表结构脚本
USE e_wallet;

-- 检查并添加transaction表的status字段
ALTER TABLE transaction 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '交易状态：SUCCESS成功/PENDING处理中/FAILED失败';

-- 检查并添加transaction表的description字段
ALTER TABLE transaction 
ADD COLUMN IF NOT EXISTS description VARCHAR(200) COMMENT '交易描述';

-- 检查并添加transaction表的create_time字段
ALTER TABLE transaction 
ADD COLUMN IF NOT EXISTS create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 更新现有记录的status字段（如果为NULL）
UPDATE transaction SET status = 'SUCCESS' WHERE status IS NULL;

-- 检查表结构
DESCRIBE transaction;

-- 显示修复结果
SELECT 'Transaction表结构修复完成' as message;
