{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "6f939f19-0d84-40dc-9f29-b22198da2513", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605857372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64fa79ae-45ba-44e0-95ba-524e2d6f45f3", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605927992800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ba4a88-3d80-4048-9f71-5711a995ce7e", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605928432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870e8673-3fdc-4808-8639-02eeae93cb29", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915574586200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e9dbf44-376f-4a27-95e5-842a6835016d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915584651500, "endTime": 315915792445700}, "additional": {"children": ["b1af1f3b-e07f-49ed-847c-e8b34418d578", "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "fd0cfad3-289e-4a7e-8664-451e25c5cd3c", "81da1d6d-36e4-4087-90e4-98f1a243d995", "0e82a2e1-410f-4a8f-af1a-0c87510c02f9", "9a6bf1a4-88de-436b-a04b-4a2119106065", "9c1cce9c-c62f-4d3a-afef-a40a0ad7aa07"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "b36a5b66-e968-41da-b220-a651894d35ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1af1f3b-e07f-49ed-847c-e8b34418d578", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915584654900, "endTime": 315915607112600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "e3fc6087-3bc4-4d5f-b531-1ed1e628bda7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915607132800, "endTime": 315915791082500}, "additional": {"children": ["881919c2-2b53-4ba4-8b1a-bcf12680ce19", "71006b51-158c-47c1-9034-b764f59a6b66", "a1166ba6-2c0a-491f-87e7-379029cc43d7", "110956bf-a346-43db-841b-dd268a21a03f", "c37b5eab-abd9-44b1-a3ea-f26340cc962f", "3441612d-1b64-44a0-a5f3-09a2a2a40bab", "9c2d5df1-c47e-43e4-b1e5-711ef0e75c81", "470fd1ef-f07d-4a37-99d1-0ab953491c1f", "966d98e2-7587-4349-86ca-f5a39bdc0954"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "a6363849-69fc-4433-8a57-1eaf4e134e57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd0cfad3-289e-4a7e-8664-451e25c5cd3c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915791106100, "endTime": 315915792427300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "90281b94-c029-46ea-a753-b2100f8e3685"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81da1d6d-36e4-4087-90e4-98f1a243d995", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915792433400, "endTime": 315915792440800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "66df02e7-cb5f-477f-9620-36330e5919f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e82a2e1-410f-4a8f-af1a-0c87510c02f9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915591124400, "endTime": 315915591189700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "88e14ef7-1870-4894-a25f-65df015f705d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88e14ef7-1870-4894-a25f-65df015f705d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915591124400, "endTime": 315915591189700}, "additional": {"logType": "info", "children": [], "durationId": "0e82a2e1-410f-4a8f-af1a-0c87510c02f9", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "9a6bf1a4-88de-436b-a04b-4a2119106065", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915600298200, "endTime": 315915600321200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "ef40af54-25aa-49d3-8f1b-0ef20d147254"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef40af54-25aa-49d3-8f1b-0ef20d147254", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915600298200, "endTime": 315915600321200}, "additional": {"logType": "info", "children": [], "durationId": "9a6bf1a4-88de-436b-a04b-4a2119106065", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "8cb7bba2-4e83-4ccb-9ab0-19409c6874ba", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915600384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "667206ac-8514-421a-9f16-e7abfd847cff", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915606960900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3fc6087-3bc4-4d5f-b531-1ed1e628bda7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915584654900, "endTime": 315915607112600}, "additional": {"logType": "info", "children": [], "durationId": "b1af1f3b-e07f-49ed-847c-e8b34418d578", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "881919c2-2b53-4ba4-8b1a-bcf12680ce19", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915615283100, "endTime": 315915615294800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "f3a1c223-b956-4447-831e-814007d48302"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71006b51-158c-47c1-9034-b764f59a6b66", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915615313600, "endTime": 315915620907600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "da28b8a7-6aa0-4558-a877-66397a3684b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1166ba6-2c0a-491f-87e7-379029cc43d7", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915620925100, "endTime": 315915716386700}, "additional": {"children": ["0ed3eb6f-858a-42b4-8d8b-eaf14c52d2d1", "aabc08ef-90ac-447c-87e1-64632fbe3c65", "06bdd9dc-e594-4587-8d7e-4c9425514728"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "2ed3d743-4b2f-4745-b061-6ba52eecf248"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "110956bf-a346-43db-841b-dd268a21a03f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915716401300, "endTime": 315915744808800}, "additional": {"children": ["592aa5c0-4044-44ec-9f7b-bd89b1075f9b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "4f09b363-448b-41b8-8ad4-4ed81938bc11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c37b5eab-abd9-44b1-a3ea-f26340cc962f", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915744817000, "endTime": 315915768327700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "ebfa3557-687d-4dab-a29b-1e05c9f40b71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3441612d-1b64-44a0-a5f3-09a2a2a40bab", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915769401000, "endTime": 315915778181700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "e4ef9992-35a1-4deb-9221-bca1566a04cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c2d5df1-c47e-43e4-b1e5-711ef0e75c81", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915778209600, "endTime": 315915790896600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "80e21e08-6269-4690-8ab2-03d83b41e421"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "470fd1ef-f07d-4a37-99d1-0ab953491c1f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915790935300, "endTime": 315915791070300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "81b80928-82a5-4fe2-9ad9-cf98b49fd5a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3a1c223-b956-4447-831e-814007d48302", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915615283100, "endTime": 315915615294800}, "additional": {"logType": "info", "children": [], "durationId": "881919c2-2b53-4ba4-8b1a-bcf12680ce19", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "da28b8a7-6aa0-4558-a877-66397a3684b8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915615313600, "endTime": 315915620907600}, "additional": {"logType": "info", "children": [], "durationId": "71006b51-158c-47c1-9034-b764f59a6b66", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "0ed3eb6f-858a-42b4-8d8b-eaf14c52d2d1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915621896100, "endTime": 315915621916600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1166ba6-2c0a-491f-87e7-379029cc43d7", "logId": "42be2cdf-4b06-447c-9f5e-91ef0362daed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42be2cdf-4b06-447c-9f5e-91ef0362daed", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915621896100, "endTime": 315915621916600}, "additional": {"logType": "info", "children": [], "durationId": "0ed3eb6f-858a-42b4-8d8b-eaf14c52d2d1", "parent": "2ed3d743-4b2f-4745-b061-6ba52eecf248"}}, {"head": {"id": "aabc08ef-90ac-447c-87e1-64632fbe3c65", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915625915300, "endTime": 315915715566600}, "additional": {"children": ["97c3916a-1e67-4ca5-aa9d-1b3c90642bf5", "fb4263c0-6752-418b-94f0-a123a75251d5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1166ba6-2c0a-491f-87e7-379029cc43d7", "logId": "049051eb-b2fe-4f7a-be7f-bf17fe30e9ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97c3916a-1e67-4ca5-aa9d-1b3c90642bf5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915625916500, "endTime": 315915630915100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aabc08ef-90ac-447c-87e1-64632fbe3c65", "logId": "249beb26-2deb-4f71-b132-fcd81d345f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb4263c0-6752-418b-94f0-a123a75251d5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915630937900, "endTime": 315915715552500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aabc08ef-90ac-447c-87e1-64632fbe3c65", "logId": "2788ad5a-2442-4f01-a3ce-cd1c20b7022e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7caa480-9ad0-4116-a153-a77b526aa421", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915625923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a54862ad-1323-49e9-9266-f69dd232e9ae", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915630758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "249beb26-2deb-4f71-b132-fcd81d345f5f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915625916500, "endTime": 315915630915100}, "additional": {"logType": "info", "children": [], "durationId": "97c3916a-1e67-4ca5-aa9d-1b3c90642bf5", "parent": "049051eb-b2fe-4f7a-be7f-bf17fe30e9ae"}}, {"head": {"id": "fcba92fc-84d7-4314-b4d8-ae00c67b8689", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915630951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ce6ee12-17ac-4368-a4a2-a1a7cc685f04", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915639193500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09f9886-f69f-48cf-9db9-fee842fd09ef", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915639339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb95987-b84c-4819-84a0-7a17a94b500a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915639503700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a31ec9a-9490-4e9b-aa25-4177c627f9fb", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915639616500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2424545e-16ea-4654-b66a-a2de27aa8231", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915641519900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a33a90-02b9-4d38-af21-749d9b7881c7", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915645205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94bf0063-ea3f-4857-a5cb-5797811395fd", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915657194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a7721e-223f-400d-bc39-b447f8ee43b5", "name": "Sdk init in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915686423200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f37ee1f-8111-4767-b165-d55208382605", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915686593600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 32}, "markType": "other"}}, {"head": {"id": "74fb1cfd-8298-47c7-93c3-44f2d08b67ee", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915686611000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 32}, "markType": "other"}}, {"head": {"id": "06d2aa93-0528-453d-aa1f-a764330a1f08", "name": "Project task initialization takes 27 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915715107900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11dc32e8-85c4-46ba-963f-42e135d75c66", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915715360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086094f3-09b8-40e0-a808-bf723c2fe490", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915715449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b29a0f-3e99-47a4-93d1-600013f12113", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915715502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2788ad5a-2442-4f01-a3ce-cd1c20b7022e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915630937900, "endTime": 315915715552500}, "additional": {"logType": "info", "children": [], "durationId": "fb4263c0-6752-418b-94f0-a123a75251d5", "parent": "049051eb-b2fe-4f7a-be7f-bf17fe30e9ae"}}, {"head": {"id": "049051eb-b2fe-4f7a-be7f-bf17fe30e9ae", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915625915300, "endTime": 315915715566600}, "additional": {"logType": "info", "children": ["249beb26-2deb-4f71-b132-fcd81d345f5f", "2788ad5a-2442-4f01-a3ce-cd1c20b7022e"], "durationId": "aabc08ef-90ac-447c-87e1-64632fbe3c65", "parent": "2ed3d743-4b2f-4745-b061-6ba52eecf248"}}, {"head": {"id": "06bdd9dc-e594-4587-8d7e-4c9425514728", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915716353500, "endTime": 315915716368300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1166ba6-2c0a-491f-87e7-379029cc43d7", "logId": "e88f5d61-2f71-433c-a4d4-9532b4bdf968"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e88f5d61-2f71-433c-a4d4-9532b4bdf968", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915716353500, "endTime": 315915716368300}, "additional": {"logType": "info", "children": [], "durationId": "06bdd9dc-e594-4587-8d7e-4c9425514728", "parent": "2ed3d743-4b2f-4745-b061-6ba52eecf248"}}, {"head": {"id": "2ed3d743-4b2f-4745-b061-6ba52eecf248", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915620925100, "endTime": 315915716386700}, "additional": {"logType": "info", "children": ["42be2cdf-4b06-447c-9f5e-91ef0362daed", "049051eb-b2fe-4f7a-be7f-bf17fe30e9ae", "e88f5d61-2f71-433c-a4d4-9532b4bdf968"], "durationId": "a1166ba6-2c0a-491f-87e7-379029cc43d7", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "592aa5c0-4044-44ec-9f7b-bd89b1075f9b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915717097600, "endTime": 315915744794600}, "additional": {"children": ["39629e23-264a-455f-99f7-573d7c0a834b", "626c5169-73c6-4cba-99a3-94f9d02c885a", "b2a3e411-5ef3-4579-8f77-0d7a3f997de9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "110956bf-a346-43db-841b-dd268a21a03f", "logId": "dfba6d5f-9806-4a80-b1f8-69456b5a865b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39629e23-264a-455f-99f7-573d7c0a834b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915721191700, "endTime": 315915721209500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "592aa5c0-4044-44ec-9f7b-bd89b1075f9b", "logId": "701cb964-2c77-42dd-8f49-eb12d9893127"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "701cb964-2c77-42dd-8f49-eb12d9893127", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915721191700, "endTime": 315915721209500}, "additional": {"logType": "info", "children": [], "durationId": "39629e23-264a-455f-99f7-573d7c0a834b", "parent": "dfba6d5f-9806-4a80-b1f8-69456b5a865b"}}, {"head": {"id": "626c5169-73c6-4cba-99a3-94f9d02c885a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915724106300, "endTime": 315915743167600}, "additional": {"children": ["ce9c499d-a396-44c6-b7a4-153d41f8d277", "c6a02055-8b4b-472c-859a-d4d2f16c79a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "592aa5c0-4044-44ec-9f7b-bd89b1075f9b", "logId": "e2eb3bc4-c1fb-4204-8758-a77365ac9f6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce9c499d-a396-44c6-b7a4-153d41f8d277", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915724107900, "endTime": 315915728520700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "626c5169-73c6-4cba-99a3-94f9d02c885a", "logId": "efae1269-b778-4a3b-a046-c81162677f69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6a02055-8b4b-472c-859a-d4d2f16c79a2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915728543400, "endTime": 315915743149200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "626c5169-73c6-4cba-99a3-94f9d02c885a", "logId": "76833029-ff98-43ca-95ba-22a4bd744f84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03992b18-132d-44b4-8404-5d891b449d0f", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915724155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f69feb-909e-44b2-b79d-4cfa5c1513cd", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915728350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efae1269-b778-4a3b-a046-c81162677f69", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915724107900, "endTime": 315915728520700}, "additional": {"logType": "info", "children": [], "durationId": "ce9c499d-a396-44c6-b7a4-153d41f8d277", "parent": "e2eb3bc4-c1fb-4204-8758-a77365ac9f6e"}}, {"head": {"id": "34e7292b-2a6d-4923-8d96-789e26e382db", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915728560200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f764f5-1dca-4506-b9bd-2e944fab97fe", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915736832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cad11da-7a3b-4ce3-9a38-b9b3437e2c5d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915736962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f635df7-3179-44d4-9f01-a22d3225055b", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915737164000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3658bbf-bc97-4eab-82c2-1d7ef44e036f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915737300800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef719994-c6e7-4a70-9ffc-1bd4ee75d302", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915737370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2879318a-1ae2-4ef5-96ec-afda703b2216", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915737425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4190f84-e8dc-4df3-8e0c-7e4a07b62385", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915737497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef35b7be-7b6c-437e-9989-616214a08d48", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915742729000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "662d62e7-6009-430c-a052-428cc2540155", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915742938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ebd9e65-7c48-4dfc-86da-75e5c9c9d1e9", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915743040800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b69bdd-1284-4b5e-b321-67679325fac5", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915743095800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76833029-ff98-43ca-95ba-22a4bd744f84", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915728543400, "endTime": 315915743149200}, "additional": {"logType": "info", "children": [], "durationId": "c6a02055-8b4b-472c-859a-d4d2f16c79a2", "parent": "e2eb3bc4-c1fb-4204-8758-a77365ac9f6e"}}, {"head": {"id": "e2eb3bc4-c1fb-4204-8758-a77365ac9f6e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915724106300, "endTime": 315915743167600}, "additional": {"logType": "info", "children": ["efae1269-b778-4a3b-a046-c81162677f69", "76833029-ff98-43ca-95ba-22a4bd744f84"], "durationId": "626c5169-73c6-4cba-99a3-94f9d02c885a", "parent": "dfba6d5f-9806-4a80-b1f8-69456b5a865b"}}, {"head": {"id": "b2a3e411-5ef3-4579-8f77-0d7a3f997de9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915744761000, "endTime": 315915744774100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "592aa5c0-4044-44ec-9f7b-bd89b1075f9b", "logId": "2ce98797-736e-4bcf-ba5a-b44a6be4cf8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ce98797-736e-4bcf-ba5a-b44a6be4cf8c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915744761000, "endTime": 315915744774100}, "additional": {"logType": "info", "children": [], "durationId": "b2a3e411-5ef3-4579-8f77-0d7a3f997de9", "parent": "dfba6d5f-9806-4a80-b1f8-69456b5a865b"}}, {"head": {"id": "dfba6d5f-9806-4a80-b1f8-69456b5a865b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915717097600, "endTime": 315915744794600}, "additional": {"logType": "info", "children": ["701cb964-2c77-42dd-8f49-eb12d9893127", "e2eb3bc4-c1fb-4204-8758-a77365ac9f6e", "2ce98797-736e-4bcf-ba5a-b44a6be4cf8c"], "durationId": "592aa5c0-4044-44ec-9f7b-bd89b1075f9b", "parent": "4f09b363-448b-41b8-8ad4-4ed81938bc11"}}, {"head": {"id": "4f09b363-448b-41b8-8ad4-4ed81938bc11", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915716401300, "endTime": 315915744808800}, "additional": {"logType": "info", "children": ["dfba6d5f-9806-4a80-b1f8-69456b5a865b"], "durationId": "110956bf-a346-43db-841b-dd268a21a03f", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "44aca74c-8fe9-4e4f-966c-902cf2aee141", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915767896800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a5c814-1219-4e04-a851-c78bdce7debf", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915768235300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebfa3557-687d-4dab-a29b-1e05c9f40b71", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915744817000, "endTime": 315915768327700}, "additional": {"logType": "info", "children": [], "durationId": "c37b5eab-abd9-44b1-a3ea-f26340cc962f", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "966d98e2-7587-4349-86ca-f5a39bdc0954", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915769203900, "endTime": 315915769385100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "logId": "ba667216-9f18-41a3-9874-5ee2fc288455"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "951c9c42-6ca8-4955-8f01-996ce4e3987a", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915769235500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba667216-9f18-41a3-9874-5ee2fc288455", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915769203900, "endTime": 315915769385100}, "additional": {"logType": "info", "children": [], "durationId": "966d98e2-7587-4349-86ca-f5a39bdc0954", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "5586efc8-9d25-4324-83a9-541f3c80ed65", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915771062000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21756d32-20ae-497d-9ee1-f92e2d65d1b8", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915777181000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ef9992-35a1-4deb-9221-bca1566a04cc", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915769401000, "endTime": 315915778181700}, "additional": {"logType": "info", "children": [], "durationId": "3441612d-1b64-44a0-a5f3-09a2a2a40bab", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "e76248d5-995c-4103-a7e1-af692ad5ce05", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915778230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff563f4-a606-4234-ba99-063e1593d0e7", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915784522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1885dc96-edc6-4e85-88a3-278919af19d1", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915784634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68871039-6f17-4904-a9ff-f76aa207be94", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915784861100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706e1087-108f-4617-8d1a-15a79e41e7a3", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915787716300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65849fb9-5526-4af2-8096-783c47d8369d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915787835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e21e08-6269-4690-8ab2-03d83b41e421", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915778209600, "endTime": 315915790896600}, "additional": {"logType": "info", "children": [], "durationId": "9c2d5df1-c47e-43e4-b1e5-711ef0e75c81", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "c2db4bd2-98a4-4629-9655-74fe35c130c5", "name": "Configuration phase cost:176 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915790963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b80928-82a5-4fe2-9ad9-cf98b49fd5a2", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915790935300, "endTime": 315915791070300}, "additional": {"logType": "info", "children": [], "durationId": "470fd1ef-f07d-4a37-99d1-0ab953491c1f", "parent": "a6363849-69fc-4433-8a57-1eaf4e134e57"}}, {"head": {"id": "a6363849-69fc-4433-8a57-1eaf4e134e57", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915607132800, "endTime": 315915791082500}, "additional": {"logType": "info", "children": ["f3a1c223-b956-4447-831e-814007d48302", "da28b8a7-6aa0-4558-a877-66397a3684b8", "2ed3d743-4b2f-4745-b061-6ba52eecf248", "4f09b363-448b-41b8-8ad4-4ed81938bc11", "ebfa3557-687d-4dab-a29b-1e05c9f40b71", "e4ef9992-35a1-4deb-9221-bca1566a04cc", "80e21e08-6269-4690-8ab2-03d83b41e421", "81b80928-82a5-4fe2-9ad9-cf98b49fd5a2", "ba667216-9f18-41a3-9874-5ee2fc288455"], "durationId": "49b07c1d-5e0c-48cd-b6dd-e99d5b4e4252", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "9c1cce9c-c62f-4d3a-afef-a40a0ad7aa07", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915792400100, "endTime": 315915792415700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9dbf44-376f-4a27-95e5-842a6835016d", "logId": "6c24de3b-287c-43ca-8ca2-0d28691050c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c24de3b-287c-43ca-8ca2-0d28691050c8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915792400100, "endTime": 315915792415700}, "additional": {"logType": "info", "children": [], "durationId": "9c1cce9c-c62f-4d3a-afef-a40a0ad7aa07", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "90281b94-c029-46ea-a753-b2100f8e3685", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915791106100, "endTime": 315915792427300}, "additional": {"logType": "info", "children": [], "durationId": "fd0cfad3-289e-4a7e-8664-451e25c5cd3c", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "66df02e7-cb5f-477f-9620-36330e5919f1", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915792433400, "endTime": 315915792440800}, "additional": {"logType": "info", "children": [], "durationId": "81da1d6d-36e4-4087-90e4-98f1a243d995", "parent": "b36a5b66-e968-41da-b220-a651894d35ea"}}, {"head": {"id": "b36a5b66-e968-41da-b220-a651894d35ea", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915584651500, "endTime": 315915792445700}, "additional": {"logType": "info", "children": ["e3fc6087-3bc4-4d5f-b531-1ed1e628bda7", "a6363849-69fc-4433-8a57-1eaf4e134e57", "90281b94-c029-46ea-a753-b2100f8e3685", "66df02e7-cb5f-477f-9620-36330e5919f1", "88e14ef7-1870-4894-a25f-65df015f705d", "ef40af54-25aa-49d3-8f1b-0ef20d147254", "6c24de3b-287c-43ca-8ca2-0d28691050c8"], "durationId": "8e9dbf44-376f-4a27-95e5-842a6835016d"}}, {"head": {"id": "13fa53be-a797-4dd3-8340-3efff2725e6e", "name": "Configuration task cost before running: 213 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915792580200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d3c24e-152a-41ae-8a5e-be9d30a2eeeb", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915797620300, "endTime": 315915807307500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "39decb42-73ec-48b6-8d66-b3922fa662dd", "logId": "84b57c39-5d36-40ad-84e1-2efccf5d4649"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39decb42-73ec-48b6-8d66-b3922fa662dd", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915794147000}, "additional": {"logType": "detail", "children": [], "durationId": "65d3c24e-152a-41ae-8a5e-be9d30a2eeeb"}}, {"head": {"id": "08f41351-ad13-47bf-abfe-79bfc9fdffa9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915794640300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e565d96d-9752-4220-a90a-1f009da0344c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915794725100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7124c2-4ce8-499f-abb9-6a271069ac40", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915797632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8ab936-c8ec-43d5-a670-fff3cff2adea", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915807059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d481212e-b6ec-4bf8-a45b-dfbd29c167c7", "name": "entry : default@PreBuild cost memory 0.26850128173828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915807227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b57c39-5d36-40ad-84e1-2efccf5d4649", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915797620300, "endTime": 315915807307500}, "additional": {"logType": "info", "children": [], "durationId": "65d3c24e-152a-41ae-8a5e-be9d30a2eeeb"}}, {"head": {"id": "7d05dea4-f2fa-4eb6-94c3-70f553dc1abe", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915813430100, "endTime": 315915815938600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c79307cd-4c5f-4e6f-b84a-9dd9c27b0a1a", "logId": "ce3a67eb-5de2-462f-8c49-452479b1b9b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c79307cd-4c5f-4e6f-b84a-9dd9c27b0a1a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915811872600}, "additional": {"logType": "detail", "children": [], "durationId": "7d05dea4-f2fa-4eb6-94c3-70f553dc1abe"}}, {"head": {"id": "f342a28a-a4aa-4acd-bbb9-9e919cb10f11", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915812521000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cced48c-eab7-489e-9bae-3f11e3fd4220", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915812640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a52ffbe4-312f-45f9-9fed-e87dbfd408ad", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915813440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2363c682-6d5f-4833-ac08-0bb748747bda", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915815690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d29eda6-94f4-4d94-bd19-8bc79bef831c", "name": "entry : default@MergeProfile cost memory 0.1113739013671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915815798600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3a67eb-5de2-462f-8c49-452479b1b9b3", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915813430100, "endTime": 315915815938600}, "additional": {"logType": "info", "children": [], "durationId": "7d05dea4-f2fa-4eb6-94c3-70f553dc1abe"}}, {"head": {"id": "8407f2f2-fadc-4aa1-949c-19792c8681c5", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915819414400, "endTime": 315915823023800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d58d334b-9757-4540-b692-48cc8c0f3e1f", "logId": "4f06a56f-7919-4c01-bca0-3c8e259de157"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d58d334b-9757-4540-b692-48cc8c0f3e1f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915817837100}, "additional": {"logType": "detail", "children": [], "durationId": "8407f2f2-fadc-4aa1-949c-19792c8681c5"}}, {"head": {"id": "da571e6b-9bf5-4337-b2a9-308748281793", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915818434900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c902d856-120e-43c8-8e24-08f1a55087c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915818564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30171d97-9056-47c2-a2b3-a1db023891b2", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915819424300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a28015d7-e236-437f-921c-ad6107515203", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915820679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80bdaf6e-1ece-40cd-84b5-01175d4a9355", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915822760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f974ea-18bc-4d72-9e95-d9fd25c4eb76", "name": "entry : default@CreateBuildProfile cost memory 0.09807586669921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915822911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f06a56f-7919-4c01-bca0-3c8e259de157", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915819414400, "endTime": 315915823023800}, "additional": {"logType": "info", "children": [], "durationId": "8407f2f2-fadc-4aa1-949c-19792c8681c5"}}, {"head": {"id": "e16bdfa3-7e0f-4a03-9031-ccd0ff1db8a2", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828299900, "endTime": 315915828907500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c19fe3f0-7dd4-43fa-8389-252a1b6eac9c", "logId": "39e82ebd-6ac0-4ec3-b241-6ecc4f8a65c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c19fe3f0-7dd4-43fa-8389-252a1b6eac9c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915826262800}, "additional": {"logType": "detail", "children": [], "durationId": "e16bdfa3-7e0f-4a03-9031-ccd0ff1db8a2"}}, {"head": {"id": "c1bce447-ef21-4cd6-b4e8-826494629a4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915826975400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e41797b-30d8-4cf3-a4e5-9d5c1705bdc9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915827109400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9dd5909-ed80-4eb9-907e-4f4893cf48b8", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ecf9b5-aa0d-438b-b880-dd3df8628deb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828470300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f57626-1454-4d52-aa86-a24ebb266e80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd7b6b65-3517-4ffd-bfda-c94eb8800c86", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828675800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85876025-e165-4354-afdb-70da8e510a94", "name": "runTaskFromQueue task cost before running: 250 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828831900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e82ebd-6ac0-4ec3-b241-6ecc4f8a65c8", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915828299900, "endTime": 315915828907500, "totalTime": 468300}, "additional": {"logType": "info", "children": [], "durationId": "e16bdfa3-7e0f-4a03-9031-ccd0ff1db8a2"}}, {"head": {"id": "be838f18-ef5b-4b1d-b0c9-8b79deed52a8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915841359700, "endTime": 315915842555200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aaa9f775-789d-481c-8d6a-49829abb20b7", "logId": "97b743d7-35a2-408c-8c1e-609a79f0f472"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaa9f775-789d-481c-8d6a-49829abb20b7", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915831164600}, "additional": {"logType": "detail", "children": [], "durationId": "be838f18-ef5b-4b1d-b0c9-8b79deed52a8"}}, {"head": {"id": "31a2e617-96cd-4389-b3d5-0d4a9bcf6f77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915831891900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2173f78-53eb-4587-b607-3f65008fabee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915832037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a14d0f-9e8d-47d8-8f75-7d8f30341ade", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915841376500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d9c6f2-ed6f-45eb-9462-a3243d7f4cdf", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915841606700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9075d38-fb50-4b29-93d0-219c9b6cb937", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915842365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0cd825a-f1e5-40ed-9986-2ff85fe965f8", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06620025634765625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915842479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b743d7-35a2-408c-8c1e-609a79f0f472", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915841359700, "endTime": 315915842555200}, "additional": {"logType": "info", "children": [], "durationId": "be838f18-ef5b-4b1d-b0c9-8b79deed52a8"}}, {"head": {"id": "1bd98f74-7636-47fd-a03b-8dc040fa007b", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915846666400, "endTime": 315915848271500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2f45c410-a26f-422b-85ab-5e41e3a1824c", "logId": "25a642e1-cf4a-4f7f-8f50-2411d83138d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f45c410-a26f-422b-85ab-5e41e3a1824c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915844655600}, "additional": {"logType": "detail", "children": [], "durationId": "1bd98f74-7636-47fd-a03b-8dc040fa007b"}}, {"head": {"id": "301fe029-650a-433b-9d8b-6cab3069cb81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915845245500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1096f986-0c7c-4208-bcee-5e5088b23d6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915845366300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f913b59-b45e-4695-9932-aefa685bdcd5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915846678100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35475ae2-155d-4b41-bff6-a7dc52f6b737", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915848070000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee0bab92-87d5-40ad-81ad-6ef7c0d34035", "name": "entry : default@ProcessProfile cost memory 0.05743408203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915848180900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25a642e1-cf4a-4f7f-8f50-2411d83138d5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915846666400, "endTime": 315915848271500}, "additional": {"logType": "info", "children": [], "durationId": "1bd98f74-7636-47fd-a03b-8dc040fa007b"}}, {"head": {"id": "8124c76a-7e9b-41c5-a5b7-70194ea7088c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915852486700, "endTime": 315915859161000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a09b5ea-ad29-4fac-9eda-a34f72214285", "logId": "1bbc20dd-6921-40e0-95e7-b20484e0e700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a09b5ea-ad29-4fac-9eda-a34f72214285", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915849940300}, "additional": {"logType": "detail", "children": [], "durationId": "8124c76a-7e9b-41c5-a5b7-70194ea7088c"}}, {"head": {"id": "f1476095-71c7-40e9-8923-e032b42032c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915850446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6d5608-158b-46a5-94b5-8a8eca9ccaf9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915850535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c262ab69-f5ae-4f66-b145-ee6f68ea0315", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915852498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ce320e0-660a-4beb-9225-2783ce3eced5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915858906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a47e930-70e7-4eaa-a551-4b0ef69525b9", "name": "entry : default@ProcessRouterMap cost memory 0.19033050537109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915859077600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bbc20dd-6921-40e0-95e7-b20484e0e700", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915852486700, "endTime": 315915859161000}, "additional": {"logType": "info", "children": [], "durationId": "8124c76a-7e9b-41c5-a5b7-70194ea7088c"}}, {"head": {"id": "25789787-bfbf-464a-8e5a-b1d4f78e20d5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915868289300, "endTime": 315915873479200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "516b56bc-4368-49f1-9fb3-b1b547bfac2d", "logId": "8feddbb5-7b56-424a-a679-05a75978242a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "516b56bc-4368-49f1-9fb3-b1b547bfac2d", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915862121400}, "additional": {"logType": "detail", "children": [], "durationId": "25789787-bfbf-464a-8e5a-b1d4f78e20d5"}}, {"head": {"id": "000733be-6574-47a5-9902-900ea5886f51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915862690200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96be553-e960-49e4-a2e7-6b4fd372708c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915862805300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "563175cd-d72d-4bf8-91a7-b01e37892936", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915863950400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8f9079-57de-462e-839f-28367728bdbe", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915871288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f0bf04-064f-475f-b84e-4d4084cca5eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915871501200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576714e1-1f4b-459c-b557-43f6a0022c5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915871596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60d9f549-5f9f-4e5f-b2d1-4d09ea355b45", "name": "entry : default@PreviewProcessResource cost memory -1.5597381591796875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915871728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0baf491-83cd-4a1d-8dcf-5f0d86936c77", "name": "runTaskFromQueue task cost before running: 294 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915873338300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8feddbb5-7b56-424a-a679-05a75978242a", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915868289300, "endTime": 315915873479200, "totalTime": 3517200}, "additional": {"logType": "info", "children": [], "durationId": "25789787-bfbf-464a-8e5a-b1d4f78e20d5"}}, {"head": {"id": "c31c6826-c0b9-45ba-9308-e967bf0157de", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915884074900, "endTime": 315915916401300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8624196f-e661-41f5-bd37-139204a5ecf1", "logId": "67e5f86b-c028-45dd-ade0-5bdfdae45b74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8624196f-e661-41f5-bd37-139204a5ecf1", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915878152300}, "additional": {"logType": "detail", "children": [], "durationId": "c31c6826-c0b9-45ba-9308-e967bf0157de"}}, {"head": {"id": "036a8bc2-b493-486b-9866-1454934723be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915878875600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09113b45-648b-4327-8a7e-d3294d1488e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915879049600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8176b713-aec0-4468-ac40-cef0189bec67", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915884098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6dceffa-3bb7-4bba-bf41-98e72f6af43d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915916155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06322eaf-4311-4bb7-bb0c-b5dc43900202", "name": "entry : default@GenerateLoaderJson cost memory 0.7255096435546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915916316100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e5f86b-c028-45dd-ade0-5bdfdae45b74", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915884074900, "endTime": 315915916401300}, "additional": {"logType": "info", "children": [], "durationId": "c31c6826-c0b9-45ba-9308-e967bf0157de"}}, {"head": {"id": "438d6777-5bfe-41ed-9cdd-12fcab620630", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915936617700, "endTime": 315916211656400}, "additional": {"children": ["1de3ee4d-c4f6-4f39-a362-5f200d393d93", "f9510064-a6f9-4ac1-b74d-21a9848db864", "fdf01967-229f-46b7-8842-b66ac550d9c4", "103d51fc-3d1d-4da4-ac51-ef139614775c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "193868b2-4106-4934-a2d8-9bbbd77c9a5d", "logId": "217c3604-b500-4590-a565-1f5b5a7e4ed5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "193868b2-4106-4934-a2d8-9bbbd77c9a5d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915932117500}, "additional": {"logType": "detail", "children": [], "durationId": "438d6777-5bfe-41ed-9cdd-12fcab620630"}}, {"head": {"id": "7a8b05c2-2d28-4a22-84fa-48d3316b7274", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915932714800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52021053-bf7a-4e47-bd80-fdbee36cb881", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915932831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7247f7c9-ec49-4fb3-9012-04d27ebe8c91", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915933955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7696d37-9775-4813-9faa-061d3bc71593", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915936646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61a88970-16de-4f8e-a576-e821cc418d41", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915964042300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdfa1e55-9103-4941-b407-c8ba64c6694a", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915964297100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de3ee4d-c4f6-4f39-a362-5f200d393d93", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915965705700, "endTime": 315916027207100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "438d6777-5bfe-41ed-9cdd-12fcab620630", "logId": "6f11c9a4-5431-489a-beb7-e81dfb438f3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f11c9a4-5431-489a-beb7-e81dfb438f3a", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915965705700, "endTime": 315916027207100}, "additional": {"logType": "info", "children": [], "durationId": "1de3ee4d-c4f6-4f39-a362-5f200d393d93", "parent": "217c3604-b500-4590-a565-1f5b5a7e4ed5"}}, {"head": {"id": "3fca2a6d-bd97-4f48-a021-85e2086fb8aa", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916027372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9510064-a6f9-4ac1-b74d-21a9848db864", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916028455200, "endTime": 315916061885900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "438d6777-5bfe-41ed-9cdd-12fcab620630", "logId": "d1600751-eaa8-4102-a661-23aea4253ea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9858949b-b304-4ac2-96db-2fe365362e38", "name": "current process  memoryUsage: {\n  rss: 118652928,\n  heapTotal: 129327104,\n  heapUsed: 123049784,\n  external: 3142217,\n  arrayBuffers: 136082\n} os memoryUsage :6.155727386474609", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916029539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca9749ec-84a5-42c1-8c51-be57d81e2beb", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916059104200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1600751-eaa8-4102-a661-23aea4253ea8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916028455200, "endTime": 315916061885900}, "additional": {"logType": "info", "children": [], "durationId": "f9510064-a6f9-4ac1-b74d-21a9848db864", "parent": "217c3604-b500-4590-a565-1f5b5a7e4ed5"}}, {"head": {"id": "3e90d450-e758-43e4-aaa2-3cace0c74f09", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916062051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf01967-229f-46b7-8842-b66ac550d9c4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916063595700, "endTime": 315916109081700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "438d6777-5bfe-41ed-9cdd-12fcab620630", "logId": "a1e05225-f596-425b-8ba6-63d5a3790031"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ceddc52-67f3-44ff-9c11-7492e33f8549", "name": "current process  memoryUsage: {\n  rss: 118681600,\n  heapTotal: 129327104,\n  heapUsed: 123303408,\n  external: 3142343,\n  arrayBuffers: 136223\n} os memoryUsage :6.163848876953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916064673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7954dc14-46f0-4640-9677-8a4a391d6385", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916106653200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e05225-f596-425b-8ba6-63d5a3790031", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916063595700, "endTime": 315916109081700}, "additional": {"logType": "info", "children": [], "durationId": "fdf01967-229f-46b7-8842-b66ac550d9c4", "parent": "217c3604-b500-4590-a565-1f5b5a7e4ed5"}}, {"head": {"id": "ba58678c-2dfb-4ea5-ad8e-3fb54a22dd66", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916109356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103d51fc-3d1d-4da4-ac51-ef139614775c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916110496300, "endTime": 315916210408900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "438d6777-5bfe-41ed-9cdd-12fcab620630", "logId": "d8a70523-d14c-48f7-bc56-8e99d4005deb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ea5b61c-a1ab-4267-afbf-e45cccc2ccb0", "name": "current process  memoryUsage: {\n  rss: 118706176,\n  heapTotal: 129327104,\n  heapUsed: 123584544,\n  external: 3142469,\n  arrayBuffers: 137163\n} os memoryUsage :6.180522918701172", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916111668400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f6c412a-89dd-46fd-bde7-cc8b15ecc00e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916206774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a70523-d14c-48f7-bc56-8e99d4005deb", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916110496300, "endTime": 315916210408900}, "additional": {"logType": "info", "children": [], "durationId": "103d51fc-3d1d-4da4-ac51-ef139614775c", "parent": "217c3604-b500-4590-a565-1f5b5a7e4ed5"}}, {"head": {"id": "ef7dae77-4f91-439c-8289-02fa08f0767e", "name": "entry : default@PreviewCompileResource cost memory -0.00682830810546875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916211352100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d37097-63f9-4195-b34e-2edc01aef0f1", "name": "runTaskFromQueue task cost before running: 632 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916211562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "217c3604-b500-4590-a565-1f5b5a7e4ed5", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915936617700, "endTime": 315916211656400, "totalTime": 274886800}, "additional": {"logType": "info", "children": ["6f11c9a4-5431-489a-beb7-e81dfb438f3a", "d1600751-eaa8-4102-a661-23aea4253ea8", "a1e05225-f596-425b-8ba6-63d5a3790031", "d8a70523-d14c-48f7-bc56-8e99d4005deb"], "durationId": "438d6777-5bfe-41ed-9cdd-12fcab620630"}}, {"head": {"id": "0be019d8-0717-43b1-85d4-e52bb62ae338", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916215435800, "endTime": 315916216396700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1445b844-0313-4775-b1a6-2317433f5007", "logId": "b0af2fac-1375-4e21-9be9-89f528965d8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1445b844-0313-4775-b1a6-2317433f5007", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916214614200}, "additional": {"logType": "detail", "children": [], "durationId": "0be019d8-0717-43b1-85d4-e52bb62ae338"}}, {"head": {"id": "30290271-166d-4bd7-a3f0-2a66a80efa47", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916215229400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bf870b-8a04-45f7-b04f-c789b8b27248", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916215339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ee6d63-712a-4718-92ec-999f6721de1c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916215444500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ba43cb-7788-497c-9759-b271395b52f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916216102000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb6eca09-af35-4ff0-b8fe-a37869116907", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916216170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faebd8e1-7d62-4042-9998-a6c9e1accb63", "name": "entry : default@PreviewHookCompileResource cost memory -1.6985931396484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916216251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd20e7c-8fb3-4e98-b175-b8b2d75f76d6", "name": "runTaskFromQueue task cost before running: 637 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916216339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0af2fac-1375-4e21-9be9-89f528965d8f", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916215435800, "endTime": 315916216396700, "totalTime": 880600}, "additional": {"logType": "info", "children": [], "durationId": "0be019d8-0717-43b1-85d4-e52bb62ae338"}}, {"head": {"id": "d3cbc498-c592-41b6-9767-8d6ec1559eb1", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916219530600, "endTime": 315916230413800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "bf4157ca-95eb-4c92-b9be-77ad2bf13227", "logId": "380dab18-2029-4e65-926d-7952293a229c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf4157ca-95eb-4c92-b9be-77ad2bf13227", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916218143800}, "additional": {"logType": "detail", "children": [], "durationId": "d3cbc498-c592-41b6-9767-8d6ec1559eb1"}}, {"head": {"id": "1810c1a5-5057-4290-b8f7-285c3ff5d7de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916218690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6100659-2e87-42a0-bf24-e10e04778eb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916218797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8726264f-8dbe-4000-8381-749cad25370f", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916219540400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3121e14-e013-4f6e-a1a0-4dbb251a48a1", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916220984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f858d5cc-2a8b-4488-b17f-2cafc927c5ae", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916221155100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78542c3-2877-4bdb-b461-b9b6f0a6b7c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916221258300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803595a4-c5d9-4f1d-bad1-0786d823464c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916221318300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273e54e6-bd9c-41c4-a445-eaac56b79428", "name": "entry : default@CopyPreviewProfile cost memory 0.2053070068359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916230131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a16dfc-f0f9-49d9-ba86-4ae74ab20128", "name": "runTaskFromQueue task cost before running: 651 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916230308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380dab18-2029-4e65-926d-7952293a229c", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916219530600, "endTime": 315916230413800, "totalTime": 10740100}, "additional": {"logType": "info", "children": [], "durationId": "d3cbc498-c592-41b6-9767-8d6ec1559eb1"}}, {"head": {"id": "8af22471-7340-46f8-9937-554255241bdb", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233585600, "endTime": 315916233975600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "f976173f-d2e8-4abc-8ac7-2c26c0307f17", "logId": "a15d84fa-8451-4020-8068-296e65aaf92b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f976173f-d2e8-4abc-8ac7-2c26c0307f17", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916232262500}, "additional": {"logType": "detail", "children": [], "durationId": "8af22471-7340-46f8-9937-554255241bdb"}}, {"head": {"id": "753ac779-adb2-4f3d-9b95-254224b3620c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916232743000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7d576f-4621-47c6-aa4b-d18577c9b1d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916232827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7597a779-7d9e-4687-a7c2-78b5cc8e0fc1", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233594500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417962d2-bdb9-46ed-aea9-e3b13bb0292f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233700500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e749be-28a0-46fd-a42c-08615da28d22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f82302-6e65-48d6-bd7a-5995de9bc789", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233842700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eb9d4cd-3254-40df-806d-863f3328177a", "name": "runTaskFromQueue task cost before running: 655 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233922000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15d84fa-8451-4020-8068-296e65aaf92b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916233585600, "endTime": 315916233975600, "totalTime": 317800}, "additional": {"logType": "info", "children": [], "durationId": "8af22471-7340-46f8-9937-554255241bdb"}}, {"head": {"id": "6a11d3e0-1c20-4612-9e45-db640c628910", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235554700, "endTime": 315916235808300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7dd992d7-b4a4-4bbd-b42d-2f871ca3a134", "logId": "96a1facb-498c-4b6e-be9c-695d38bfad5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dd992d7-b4a4-4bbd-b42d-2f871ca3a134", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235506700}, "additional": {"logType": "detail", "children": [], "durationId": "6a11d3e0-1c20-4612-9e45-db640c628910"}}, {"head": {"id": "5fc677e6-e851-436b-ab82-908fb2e4b372", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c51aeb3-54be-48c4-bee1-745b7834c059", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235668300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb579e2d-e7d7-4063-af9c-43a2c9940110", "name": "runTaskFromQueue task cost before running: 657 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a1facb-498c-4b6e-be9c-695d38bfad5a", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916235554700, "endTime": 315916235808300, "totalTime": 177000}, "additional": {"logType": "info", "children": [], "durationId": "6a11d3e0-1c20-4612-9e45-db640c628910"}}, {"head": {"id": "e238d68c-01f9-4845-b642-fe8f4421f593", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916239183100, "endTime": 315916243965900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "ae769156-cf72-4a86-8e84-8931bf280e4f", "logId": "ca24e049-1802-4e02-8382-380ad011818d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae769156-cf72-4a86-8e84-8931bf280e4f", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916237488200}, "additional": {"logType": "detail", "children": [], "durationId": "e238d68c-01f9-4845-b642-fe8f4421f593"}}, {"head": {"id": "ddd234e3-881c-40f1-91d1-38de8144f4ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916238071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f6eacf6-acdf-4e7c-a2d8-15dff0e486ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916238246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4daeb092-4557-447f-87f2-fc78a497e795", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916239195000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cddbe5dc-5224-47c5-91d0-7d53c9ef3845", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916241806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "139d4ebd-0d12-44b9-b2ec-89545a559756", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916242060200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b63fd31c-47ee-4641-83ee-79d28b87a143", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916242225600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f76b24e-edc1-4f18-837d-e37956bd42af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916242342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd4f6f3-c29a-46d8-a862-0680af8dc416", "name": "entry : default@PreviewUpdateAssets cost memory 0.13039398193359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916243595000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a41781-c9da-4441-b5b6-adab87a06dd2", "name": "runTaskFromQueue task cost before running: 665 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916243831000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca24e049-1802-4e02-8382-380ad011818d", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916239183100, "endTime": 315916243965900, "totalTime": 4589600}, "additional": {"logType": "info", "children": [], "durationId": "e238d68c-01f9-4845-b642-fe8f4421f593"}}, {"head": {"id": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916258849600, "endTime": 315930976118400}, "additional": {"children": ["fe69998a-3b82-43cd-a0e9-4cad282bdf8a"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "410a4316-f670-4ccd-b3bf-e9a4b893b205", "logId": "31efaec6-ac83-4641-b5ee-cf6d1004d63d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "410a4316-f670-4ccd-b3bf-e9a4b893b205", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916247702300}, "additional": {"logType": "detail", "children": [], "durationId": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116"}}, {"head": {"id": "cd07bc6f-82ab-45e8-9de7-3f86ba12d5d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916248555500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c39e22-d573-411e-a6c1-136f6ec8c760", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916248709300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9be78f3d-cac0-4692-8449-c7e9d4dfae0a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916258887100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker7", "startTime": 315916294128200, "endTime": 315930975644000}, "additional": {"children": ["5f541add-e0f5-4cd2-b24b-04aa42ac15d6", "283badf6-1a94-481b-b123-d06df65c9e04", "d6e8df15-5a48-4c0b-baf2-5539ed865ca1", "3f3fe046-7d9a-4f4c-816d-c2daa6f6f831", "1da523e7-2342-4a9c-8241-4b5a39fdab19", "1f252df2-ae44-4bcc-8b28-18ec6922765d", "b5385e03-50b3-48b5-8425-f31cea313f1c", "94ce8fe2-98cb-4e4b-a897-3a10b92653e2", "f76accbe-2864-455d-9875-bd9bd3aa7450", "8cd71ba5-7e5c-461c-beaf-8345763926c7", "e6898476-6700-455c-99c2-728888a34150", "c3aac34b-2f06-409f-bed9-dfc0ef732911", "611d06a9-5abc-4554-9d0c-f22b827f9779", "b95593c7-105b-40ac-b6b3-e03f95472a2b", "deb9f9e3-bd10-4149-9eae-e7b45cf4d49d", "e317a49e-0836-473c-9bfe-b9fd1a6a43ac", "3c978a01-ce64-4002-8f75-6bd26c9d36df", "2338ff55-152c-4390-9fb5-fdb5ec808094", "bb170c6c-6128-4f76-8fbf-625011a17cee", "ebacfa49-7f82-46a9-b272-1e3a7b7ecee7", "428b3dba-a5a4-4292-aa15-dc00cbc36ad9", "a2a77d19-dda9-48f2-bbb4-dc7bd01cabd1", "0a58830c-398d-4ae5-b6cc-e3dc90940d81", "e17bafb8-2587-4228-b9a1-3decf840d63d", "db8e0b53-0df1-4ae1-90b1-1e00807438cc", "51ec2b8a-c730-4db3-9286-6a957102ee9e", "84157ed7-83e8-4009-a1cc-739e13ede534", "707dd202-7e8c-4569-923c-35630697e351", "3133b4c0-1871-43f1-82e6-364711d21564", "0dc66550-4869-44ff-8626-ae01cbc20cbe", "347bdf91-afa2-4f91-bf5c-94becb5c6f9a", "0990f620-8f3d-41bb-9248-2965b3b8cb75", "b7d89206-e10c-49b2-b955-d97fdc698619", "6a1e8686-9dfd-4985-9887-89dffad717c0", "c31b12bf-5eab-4d7c-9458-d855531d7802", "e820afdf-4213-4f7c-b634-ad85fe836591", "0f2da6aa-d1f3-41f3-981c-68bbdd592d31", "41dc7bd5-fe8b-4a7c-9dfb-a2bb0d884faa", "eeddf758-790c-49b8-a1e4-5690c1487e9c", "f9542fa2-12f6-422e-ac7a-090b28b33234", "6bb2b0c9-f270-41fb-bd4e-d4a344d39124", "aa17d256-3f1d-4855-b89f-0eda2c6d76fb", "9630e8a0-991a-401a-bf2c-811862541214", "27d5d7df-fa5c-4d6c-b2fa-2c71c4299c6c", "60df2ecf-eb8e-4860-96f7-21e33f84a769", "9aa77ddd-e944-408b-9685-d547f669c9a9", "7e799e5c-e13f-476d-83f4-5da9559fd19e", "f8d119cd-9bd9-4c44-85e0-168492fbb4a3", "52b07cb3-d3a3-4640-8769-3ff0de216a45", "dfeacd40-b394-47a8-8499-f2277b92f3c3", "7f764f75-9158-4b98-bb85-81d7eedea6af", "edb6b54c-83dc-400f-854c-e77328e4fa4d", "b5dec2b2-a929-464f-92c7-39a94bb8902e", "90a57601-8eb1-47a5-adc8-a016b65f481f", "267d9f95-def5-4e37-bb7a-9df96081226e", "529e7ab7-563b-4828-a76a-a76339043771", "36c554bc-68b6-481c-9ed4-98a0c958b3de", "d23761de-a014-497b-bae1-61eb2aabf289", "232bde76-3dc2-4a95-aa64-24f4eb06c757", "b08fd903-07e6-4e88-baa4-a36ce491e5ca", "d35c8112-4a55-443e-a72e-e519b235b5b7", "c4ee001b-4b93-4e38-bfff-debd8aa628b2", "910b46a0-f88d-4ca1-b1b4-dd987e80e0cc", "308245ff-30f9-48ae-bf7f-381e8a58b3da", "21a12c70-75b6-4cfe-abd9-c91083c9d635", "70b6621a-cf17-46be-99a7-781370e57b83", "ce3a253c-8c95-4d3f-9c13-eefccb2e3d02", "fc47fab9-7b92-4499-a63c-5e5ba12a54db", "63958bab-4baa-495d-9d05-3988a427ac1f", "f0709429-1de2-44a8-8b09-aecf32669d27"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116", "logId": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d45fe390-ec4b-4b22-82c3-e51d8a5df3f6", "name": "entry : default@PreviewArkTS cost memory -0.2169036865234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916298353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed3a5cd-7f2b-46c0-a319-7b770091bd49", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315921577853800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f541add-e0f5-4cd2-b24b-04aa42ac15d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315921579017600, "endTime": 315921579039000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "e34b300a-0e3f-4158-99cf-ae3d6c8f0a4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e34b300a-0e3f-4158-99cf-ae3d6c8f0a4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315921579017600, "endTime": 315921579039000}, "additional": {"logType": "info", "children": [], "durationId": "5f541add-e0f5-4cd2-b24b-04aa42ac15d6", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "6d041ba3-03d3-4c62-9237-b2fbb334e7cb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930386133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283badf6-1a94-481b-b123-d06df65c9e04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930387254300, "endTime": 315930387274600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "2c610f08-4ccd-452c-aabb-ac4b91dc397d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c610f08-4ccd-452c-aabb-ac4b91dc397d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930387254300, "endTime": 315930387274600}, "additional": {"logType": "info", "children": [], "durationId": "283badf6-1a94-481b-b123-d06df65c9e04", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "b8715839-935d-422a-916b-932fb8d7c45b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930387362500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e8df15-5a48-4c0b-baf2-5539ed865ca1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930388203200, "endTime": 315930388217000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "67979dcb-3f49-40ab-b37c-e562bcaa6e03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67979dcb-3f49-40ab-b37c-e562bcaa6e03", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930388203200, "endTime": 315930388217000}, "additional": {"logType": "info", "children": [], "durationId": "d6e8df15-5a48-4c0b-baf2-5539ed865ca1", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "293947f4-0010-47ce-bd33-e1cebf93c25d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930388291800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3fe046-7d9a-4f4c-816d-c2daa6f6f831", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930389105700, "endTime": 315930389119100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "55f19d16-77e2-489e-b9fa-bf367bda485d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55f19d16-77e2-489e-b9fa-bf367bda485d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930389105700, "endTime": 315930389119100}, "additional": {"logType": "info", "children": [], "durationId": "3f3fe046-7d9a-4f4c-816d-c2daa6f6f831", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "6f0fa74b-850a-4ac6-b7f2-e250c8e294e7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930389196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da523e7-2342-4a9c-8241-4b5a39fdab19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930390042000, "endTime": 315930390054800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "94c196c4-4386-47ff-b598-3bf452e0b670"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94c196c4-4386-47ff-b598-3bf452e0b670", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930390042000, "endTime": 315930390054800}, "additional": {"logType": "info", "children": [], "durationId": "1da523e7-2342-4a9c-8241-4b5a39fdab19", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "e4a5208d-8c01-45c8-8452-83969d754c15", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930390126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f252df2-ae44-4bcc-8b28-18ec6922765d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930390945900, "endTime": 315930390959100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "be159118-5faf-47a9-9cad-89cf3e400243"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be159118-5faf-47a9-9cad-89cf3e400243", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930390945900, "endTime": 315930390959100}, "additional": {"logType": "info", "children": [], "durationId": "1f252df2-ae44-4bcc-8b28-18ec6922765d", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "ace04003-925e-4364-92c8-0713f1d153c9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930391036300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5385e03-50b3-48b5-8425-f31cea313f1c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930391878700, "endTime": 315930391891900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "d0f2d576-6875-4d0d-9e15-a92975423430"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0f2d576-6875-4d0d-9e15-a92975423430", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930391878700, "endTime": 315930391891900}, "additional": {"logType": "info", "children": [], "durationId": "b5385e03-50b3-48b5-8425-f31cea313f1c", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "cddaaeb2-5cba-45ec-a4db-1ee33b38b926", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930392752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ce8fe2-98cb-4e4b-a897-3a10b92653e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930393990900, "endTime": 315930394009000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "06892cc2-6f20-4621-bde9-425b4c8a5734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06892cc2-6f20-4621-bde9-425b4c8a5734", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930393990900, "endTime": 315930394009000}, "additional": {"logType": "info", "children": [], "durationId": "94ce8fe2-98cb-4e4b-a897-3a10b92653e2", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "184ee004-181a-4aeb-b3e3-8310557787e6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930394097500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76accbe-2864-455d-9875-bd9bd3aa7450", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930531137500, "endTime": 315930531174500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "458fa6c7-dd0b-453a-abd9-eea12b8b2e66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "458fa6c7-dd0b-453a-abd9-eea12b8b2e66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930531137500, "endTime": 315930531174500}, "additional": {"logType": "info", "children": [], "durationId": "f76accbe-2864-455d-9875-bd9bd3aa7450", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "7d4a72a3-4edd-4ebf-9789-40cfef31baad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930531343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd71ba5-7e5c-461c-beaf-8345763926c7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930533051300, "endTime": 315930533081000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "9e92d447-0aab-4483-9427-5c3eeb89cbca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e92d447-0aab-4483-9427-5c3eeb89cbca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930533051300, "endTime": 315930533081000}, "additional": {"logType": "info", "children": [], "durationId": "8cd71ba5-7e5c-461c-beaf-8345763926c7", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "124b7881-bd6f-42a4-9a3f-b3af95e33df1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930533253200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6898476-6700-455c-99c2-728888a34150", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930535172300, "endTime": 315930535202300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "2b13f208-9ead-473a-bdbf-72a750139f30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b13f208-9ead-473a-bdbf-72a750139f30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930535172300, "endTime": 315930535202300}, "additional": {"logType": "info", "children": [], "durationId": "e6898476-6700-455c-99c2-728888a34150", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "3997287d-0741-47ae-bb14-c4d34e675ace", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930535367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3aac34b-2f06-409f-bed9-dfc0ef732911", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930537036100, "endTime": 315930537060600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "a9f1c1ee-145a-4cc2-9898-0aa424700fc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f1c1ee-145a-4cc2-9898-0aa424700fc2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930537036100, "endTime": 315930537060600}, "additional": {"logType": "info", "children": [], "durationId": "c3aac34b-2f06-409f-bed9-dfc0ef732911", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "c6a95513-9ba9-4089-ba74-cf54a56d3cc7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930537208400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "611d06a9-5abc-4554-9d0c-f22b827f9779", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930538855900, "endTime": 315930538890500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "2abd3bd6-53ca-447a-82d2-d6a19dc1cdb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2abd3bd6-53ca-447a-82d2-d6a19dc1cdb0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930538855900, "endTime": 315930538890500}, "additional": {"logType": "info", "children": [], "durationId": "611d06a9-5abc-4554-9d0c-f22b827f9779", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "37d4c270-95b3-4691-a99d-4d583f5115bd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930539069300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95593c7-105b-40ac-b6b3-e03f95472a2b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930541823800, "endTime": 315930541881000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "9019aadd-678f-4a97-bef2-76f3b151ebfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9019aadd-678f-4a97-bef2-76f3b151ebfa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930541823800, "endTime": 315930541881000}, "additional": {"logType": "info", "children": [], "durationId": "b95593c7-105b-40ac-b6b3-e03f95472a2b", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "f5766f41-ec14-4e51-b63d-2fa92ebf0649", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930542077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb9f9e3-bd10-4149-9eae-e7b45cf4d49d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930543891000, "endTime": 315930543920000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3b96d6ea-b7d1-46a3-ab78-36106763093a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b96d6ea-b7d1-46a3-ab78-36106763093a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930543891000, "endTime": 315930543920000}, "additional": {"logType": "info", "children": [], "durationId": "deb9f9e3-bd10-4149-9eae-e7b45cf4d49d", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "2d389900-2a22-4160-bd34-80bfd7e12cb0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930544126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e317a49e-0836-473c-9bfe-b9fd1a6a43ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930545821800, "endTime": 315930545850400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "ff43b1b9-0eed-497e-8c72-423fa91c189a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff43b1b9-0eed-497e-8c72-423fa91c189a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930545821800, "endTime": 315930545850400}, "additional": {"logType": "info", "children": [], "durationId": "e317a49e-0836-473c-9bfe-b9fd1a6a43ac", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "4ae8cf60-e47c-4164-bae8-97088c837fd0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930546000200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c978a01-ce64-4002-8f75-6bd26c9d36df", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930547911300, "endTime": 315930547935800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "c9d6ceed-1ca2-4a9d-803c-11638767c781"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9d6ceed-1ca2-4a9d-803c-11638767c781", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930547911300, "endTime": 315930547935800}, "additional": {"logType": "info", "children": [], "durationId": "3c978a01-ce64-4002-8f75-6bd26c9d36df", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "6036d897-62b4-4466-8f66-c3d7300bf094", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930548081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2338ff55-152c-4390-9fb5-fdb5ec808094", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930549762000, "endTime": 315930549796100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "2dc67d70-9ca3-43f0-b696-f0cce5dd955f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dc67d70-9ca3-43f0-b696-f0cce5dd955f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930549762000, "endTime": 315930549796100}, "additional": {"logType": "info", "children": [], "durationId": "2338ff55-152c-4390-9fb5-fdb5ec808094", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "d120cdc3-cd1a-478c-80f9-9d48b4b77f37", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930549953700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb170c6c-6128-4f76-8fbf-625011a17cee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930551605600, "endTime": 315930551630100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "356e25e9-7e2f-44fe-96f3-518e398b52a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "356e25e9-7e2f-44fe-96f3-518e398b52a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930551605600, "endTime": 315930551630100}, "additional": {"logType": "info", "children": [], "durationId": "bb170c6c-6128-4f76-8fbf-625011a17cee", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "098fac2d-187b-42e9-b3e9-71ae5e221b9d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930551793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebacfa49-7f82-46a9-b272-1e3a7b7ecee7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930553527500, "endTime": 315930553553900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "4806f2cc-6053-484e-94df-4a052c6c96b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4806f2cc-6053-484e-94df-4a052c6c96b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930553527500, "endTime": 315930553553900}, "additional": {"logType": "info", "children": [], "durationId": "ebacfa49-7f82-46a9-b272-1e3a7b7ecee7", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "4d4db18f-eca8-4dbe-bb3f-0398fbb126a2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930553705400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "428b3dba-a5a4-4292-aa15-dc00cbc36ad9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930556334200, "endTime": 315930556379000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "7642190f-69b4-4864-bfa8-492c5e870139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7642190f-69b4-4864-bfa8-492c5e870139", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930556334200, "endTime": 315930556379000}, "additional": {"logType": "info", "children": [], "durationId": "428b3dba-a5a4-4292-aa15-dc00cbc36ad9", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "5d466dca-317e-40e1-a6b1-878f97d41b89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930556634600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a77d19-dda9-48f2-bbb4-dc7bd01cabd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930570135100, "endTime": 315930570180000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "ae03b2ac-83d9-41fb-a7c4-73d733a62fc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae03b2ac-83d9-41fb-a7c4-73d733a62fc9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930570135100, "endTime": 315930570180000}, "additional": {"logType": "info", "children": [], "durationId": "a2a77d19-dda9-48f2-bbb4-dc7bd01cabd1", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "98893e3c-54b1-4575-9062-b6b9893f4e3c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930570376300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a58830c-398d-4ae5-b6cc-e3dc90940d81", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930572634700, "endTime": 315930572675700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "4df21fcd-3ecd-45a9-a5ef-40e0fab01cd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df21fcd-3ecd-45a9-a5ef-40e0fab01cd0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930572634700, "endTime": 315930572675700}, "additional": {"logType": "info", "children": [], "durationId": "0a58830c-398d-4ae5-b6cc-e3dc90940d81", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "119c078a-d813-4f46-81e3-2385fcdb0156", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930572909300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17bafb8-2587-4228-b9a1-3decf840d63d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930575014500, "endTime": 315930575048200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "d6e40e1e-d737-4a09-9230-f84e212292d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6e40e1e-d737-4a09-9230-f84e212292d0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930575014500, "endTime": 315930575048200}, "additional": {"logType": "info", "children": [], "durationId": "e17bafb8-2587-4228-b9a1-3decf840d63d", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "51ac6b46-54d9-4098-94c2-577a61a31fa3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930575271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db8e0b53-0df1-4ae1-90b1-1e00807438cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930577413000, "endTime": 315930577463500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "1c04f72c-663b-47ab-b075-0d1aaea2c44d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c04f72c-663b-47ab-b075-0d1aaea2c44d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930577413000, "endTime": 315930577463500}, "additional": {"logType": "info", "children": [], "durationId": "db8e0b53-0df1-4ae1-90b1-1e00807438cc", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "fc322f5d-b33b-4ab8-b3df-064687efc746", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930577793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ec2b8a-c730-4db3-9286-6a957102ee9e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930582257500, "endTime": 315930582306800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "745a7042-d756-43d2-97a1-6a0909158547"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "745a7042-d756-43d2-97a1-6a0909158547", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930582257500, "endTime": 315930582306800}, "additional": {"logType": "info", "children": [], "durationId": "51ec2b8a-c730-4db3-9286-6a957102ee9e", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "f9951815-5887-4934-a4a8-d3e89e235bb6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930582580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84157ed7-83e8-4009-a1cc-739e13ede534", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930585137400, "endTime": 315930585175800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "38ad6fe9-6e09-4dbf-b4fd-45328cf192c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38ad6fe9-6e09-4dbf-b4fd-45328cf192c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930585137400, "endTime": 315930585175800}, "additional": {"logType": "info", "children": [], "durationId": "84157ed7-83e8-4009-a1cc-739e13ede534", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "c5fd478a-0538-4c48-a436-38cd25ea5d9c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930585412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707dd202-7e8c-4569-923c-35630697e351", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930589257500, "endTime": 315930589295100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3cb43988-69df-4b5a-9955-c9e9e526c4f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb43988-69df-4b5a-9955-c9e9e526c4f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930589257500, "endTime": 315930589295100}, "additional": {"logType": "info", "children": [], "durationId": "707dd202-7e8c-4569-923c-35630697e351", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "bd4063f9-8c83-4b34-84e0-47f429f19df7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930589501800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3133b4c0-1871-43f1-82e6-364711d21564", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930592024800, "endTime": 315930592072900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "687a7278-4e16-4fc3-8cd1-fad9b83a1394"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "687a7278-4e16-4fc3-8cd1-fad9b83a1394", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930592024800, "endTime": 315930592072900}, "additional": {"logType": "info", "children": [], "durationId": "3133b4c0-1871-43f1-82e6-364711d21564", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "83606e3a-8578-4d8b-bf9a-936354f36899", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930592318600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc66550-4869-44ff-8626-ae01cbc20cbe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930594940300, "endTime": 315930594977600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "64646f3d-4a5c-4d84-9ceb-e609b35593ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64646f3d-4a5c-4d84-9ceb-e609b35593ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930594940300, "endTime": 315930594977600}, "additional": {"logType": "info", "children": [], "durationId": "0dc66550-4869-44ff-8626-ae01cbc20cbe", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "a85a669a-9c27-4b62-ab81-319e29bba592", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930595205500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347bdf91-afa2-4f91-bf5c-94becb5c6f9a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930601288800, "endTime": 315930601326600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "c1dd3c0e-55e2-4b08-b7b6-4481e4909c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1dd3c0e-55e2-4b08-b7b6-4481e4909c8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930601288800, "endTime": 315930601326600}, "additional": {"logType": "info", "children": [], "durationId": "347bdf91-afa2-4f91-bf5c-94becb5c6f9a", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "0d2c7aee-0166-40ec-b1e3-d221fdc432c6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930601520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0990f620-8f3d-41bb-9248-2965b3b8cb75", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930605074200, "endTime": 315930605108900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "fc122f77-dcd5-431a-9724-c221ce0eab46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc122f77-dcd5-431a-9724-c221ce0eab46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930605074200, "endTime": 315930605108900}, "additional": {"logType": "info", "children": [], "durationId": "0990f620-8f3d-41bb-9248-2965b3b8cb75", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "a07bce64-014c-4424-ae35-3f9e09159c49", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930605288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d89206-e10c-49b2-b955-d97fdc698619", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930607115200, "endTime": 315930607152600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "c5535718-16d6-49c6-bc1a-f59c87233002"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5535718-16d6-49c6-bc1a-f59c87233002", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930607115200, "endTime": 315930607152600}, "additional": {"logType": "info", "children": [], "durationId": "b7d89206-e10c-49b2-b955-d97fdc698619", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "4149033b-7b23-4679-8740-23212d306d2c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930607332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1e8686-9dfd-4985-9887-89dffad717c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930609069500, "endTime": 315930609106200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "672ac940-fc65-48e8-b62b-93217035ff44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "672ac940-fc65-48e8-b62b-93217035ff44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930609069500, "endTime": 315930609106200}, "additional": {"logType": "info", "children": [], "durationId": "6a1e8686-9dfd-4985-9887-89dffad717c0", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "87f1081f-577f-465c-9cb9-80f8a12a71e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930609294500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31b12bf-5eab-4d7c-9458-d855531d7802", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930611084000, "endTime": 315930611146700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3d54cbfa-31ae-492f-8e4f-8a2448031851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d54cbfa-31ae-492f-8e4f-8a2448031851", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930611084000, "endTime": 315930611146700}, "additional": {"logType": "info", "children": [], "durationId": "c31b12bf-5eab-4d7c-9458-d855531d7802", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "535e4f09-0d94-4096-aa07-1f32f2147b52", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930611318000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e820afdf-4213-4f7c-b634-ad85fe836591", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930614282600, "endTime": 315930614320900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3afa52bf-2ac3-42f7-85bc-90ae61504550"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3afa52bf-2ac3-42f7-85bc-90ae61504550", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930614282600, "endTime": 315930614320900}, "additional": {"logType": "info", "children": [], "durationId": "e820afdf-4213-4f7c-b634-ad85fe836591", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "5ec98b9a-a9ff-435f-88c0-2314edc25152", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930614528700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f2da6aa-d1f3-41f3-981c-68bbdd592d31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930616395400, "endTime": 315930616425500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "be5da107-81e0-457b-89f5-6b9b497016a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5da107-81e0-457b-89f5-6b9b497016a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930616395400, "endTime": 315930616425500}, "additional": {"logType": "info", "children": [], "durationId": "0f2da6aa-d1f3-41f3-981c-68bbdd592d31", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "b80a6258-21d0-476a-95a9-bc7e699526f2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930616585100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41dc7bd5-fe8b-4a7c-9dfb-a2bb0d884faa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930618545600, "endTime": 315930618575800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "9af5488f-313b-4d9f-a2ba-e82454b9ce44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9af5488f-313b-4d9f-a2ba-e82454b9ce44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930618545600, "endTime": 315930618575800}, "additional": {"logType": "info", "children": [], "durationId": "41dc7bd5-fe8b-4a7c-9dfb-a2bb0d884faa", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "e654e39f-307b-44c5-b173-0e0da7262ce1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930618744800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeddf758-790c-49b8-a1e4-5690c1487e9c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930620435600, "endTime": 315930620462400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "9e5d0a59-c786-4a85-a1ad-e47d8b6958be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e5d0a59-c786-4a85-a1ad-e47d8b6958be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930620435600, "endTime": 315930620462400}, "additional": {"logType": "info", "children": [], "durationId": "eeddf758-790c-49b8-a1e4-5690c1487e9c", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "3325aba1-b111-4ccb-9099-e897fd7877ad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930620737500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9542fa2-12f6-422e-ac7a-090b28b33234", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930624874100, "endTime": 315930624910500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "126977c7-fdb6-4c4c-b69f-c9cb7ae72e1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "126977c7-fdb6-4c4c-b69f-c9cb7ae72e1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930624874100, "endTime": 315930624910500}, "additional": {"logType": "info", "children": [], "durationId": "f9542fa2-12f6-422e-ac7a-090b28b33234", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "0b83c9f0-182d-463c-8d01-0b3bdee0df7e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930625104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bb2b0c9-f270-41fb-bd4e-d4a344d39124", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930627163600, "endTime": 315930627197400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "eedb8dfb-0ff1-45fc-8de9-17afad47dfeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eedb8dfb-0ff1-45fc-8de9-17afad47dfeb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930627163600, "endTime": 315930627197400}, "additional": {"logType": "info", "children": [], "durationId": "6bb2b0c9-f270-41fb-bd4e-d4a344d39124", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "dfe5ca01-155d-46d4-8ca9-1b1b9c1b3dde", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930627374200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa17d256-3f1d-4855-b89f-0eda2c6d76fb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930630048100, "endTime": 315930630080500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "842399b9-b536-45dc-8442-d47b0a0f508e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "842399b9-b536-45dc-8442-d47b0a0f508e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930630048100, "endTime": 315930630080500}, "additional": {"logType": "info", "children": [], "durationId": "aa17d256-3f1d-4855-b89f-0eda2c6d76fb", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "9994f8c4-b059-49ce-a0dd-e63082515186", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930630342900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9630e8a0-991a-401a-bf2c-811862541214", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930633621900, "endTime": 315930633657700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "77cd577e-d656-4408-b4f9-644eb9973b24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77cd577e-d656-4408-b4f9-644eb9973b24", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930633621900, "endTime": 315930633657700}, "additional": {"logType": "info", "children": [], "durationId": "9630e8a0-991a-401a-bf2c-811862541214", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "8206bc48-534d-4ba4-b15e-3ac4d6bb0553", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930633857400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d5d7df-fa5c-4d6c-b2fa-2c71c4299c6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930635896700, "endTime": 315930635930600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "ef59f47f-6f0d-470b-b050-f09bb3671c0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef59f47f-6f0d-470b-b050-f09bb3671c0d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930635896700, "endTime": 315930635930600}, "additional": {"logType": "info", "children": [], "durationId": "27d5d7df-fa5c-4d6c-b2fa-2c71c4299c6c", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "90605e00-ec7f-4c28-bc64-8e0d4647e14e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930636096300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60df2ecf-eb8e-4860-96f7-21e33f84a769", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930637531900, "endTime": 315930637578200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3fa12f31-7de2-418c-be93-128721d25770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fa12f31-7de2-418c-be93-128721d25770", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930637531900, "endTime": 315930637578200}, "additional": {"logType": "info", "children": [], "durationId": "60df2ecf-eb8e-4860-96f7-21e33f84a769", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "8c693eef-85bf-4393-8e11-9a71d952a312", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930637774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa77ddd-e944-408b-9685-d547f669c9a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930638993700, "endTime": 315930639017900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "dc3fb39c-d99b-4729-a4c5-77f9dbadca42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc3fb39c-d99b-4729-a4c5-77f9dbadca42", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930638993700, "endTime": 315930639017900}, "additional": {"logType": "info", "children": [], "durationId": "9aa77ddd-e944-408b-9685-d547f669c9a9", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "0bd86326-1612-40cc-a0c7-89f0466df201", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930639186100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e799e5c-e13f-476d-83f4-5da9559fd19e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930641395400, "endTime": 315930641442500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "32e086ee-d18a-435a-a39d-aa35e870464d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32e086ee-d18a-435a-a39d-aa35e870464d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930641395400, "endTime": 315930641442500}, "additional": {"logType": "info", "children": [], "durationId": "7e799e5c-e13f-476d-83f4-5da9559fd19e", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "969796a9-42a9-4bd4-b83a-4c8749278f73", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930641685600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d119cd-9bd9-4c44-85e0-168492fbb4a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930643271400, "endTime": 315930643295400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "cae726b1-7e05-4ac0-9ec8-faf3b1630cff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cae726b1-7e05-4ac0-9ec8-faf3b1630cff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930643271400, "endTime": 315930643295400}, "additional": {"logType": "info", "children": [], "durationId": "f8d119cd-9bd9-4c44-85e0-168492fbb4a3", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "7cdb8a55-8804-41cf-9ee9-c9bd99b544a4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930643429600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b07cb3-d3a3-4640-8769-3ff0de216a45", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930644777700, "endTime": 315930644801700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "e48846b7-8f16-4630-99c5-ab0f2c2beb3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e48846b7-8f16-4630-99c5-ab0f2c2beb3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930644777700, "endTime": 315930644801700}, "additional": {"logType": "info", "children": [], "durationId": "52b07cb3-d3a3-4640-8769-3ff0de216a45", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "6dae5829-4379-4dac-92af-27d1b43174ac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930644965500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfeacd40-b394-47a8-8499-f2277b92f3c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930646522800, "endTime": 315930646553600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "39cb2b51-7303-47fe-8e0a-7b8ae871aef1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39cb2b51-7303-47fe-8e0a-7b8ae871aef1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930646522800, "endTime": 315930646553600}, "additional": {"logType": "info", "children": [], "durationId": "dfeacd40-b394-47a8-8499-f2277b92f3c3", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "4050ee14-ffde-4b6d-b34e-1f5ab2b54c23", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930646728900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f764f75-9158-4b98-bb85-81d7eedea6af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930648210500, "endTime": 315930648242300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "71ade891-35ec-42e8-8a46-5756d54b7aaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ade891-35ec-42e8-8a46-5756d54b7aaf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930648210500, "endTime": 315930648242300}, "additional": {"logType": "info", "children": [], "durationId": "7f764f75-9158-4b98-bb85-81d7eedea6af", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "79b2030f-d6e4-4725-82a3-856391d390c7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930648421700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb6b54c-83dc-400f-854c-e77328e4fa4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930650430100, "endTime": 315930650480800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "4502750b-aacb-4e37-b62a-f1e8e01acdba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4502750b-aacb-4e37-b62a-f1e8e01acdba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930650430100, "endTime": 315930650480800}, "additional": {"logType": "info", "children": [], "durationId": "edb6b54c-83dc-400f-854c-e77328e4fa4d", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "ccb4aa08-cd57-4258-84f1-5da2da19dd72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930650772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5dec2b2-a929-464f-92c7-39a94bb8902e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930652744200, "endTime": 315930652780600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "99551037-bbaa-4582-8e82-994249e47c55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99551037-bbaa-4582-8e82-994249e47c55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930652744200, "endTime": 315930652780600}, "additional": {"logType": "info", "children": [], "durationId": "b5dec2b2-a929-464f-92c7-39a94bb8902e", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "540b07f5-7d7f-4af0-8561-2b27e91b38cc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930652973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a57601-8eb1-47a5-adc8-a016b65f481f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930654679800, "endTime": 315930654712400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "3f418125-ab10-48ab-ac9d-d8560bcdee10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f418125-ab10-48ab-ac9d-d8560bcdee10", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930654679800, "endTime": 315930654712400}, "additional": {"logType": "info", "children": [], "durationId": "90a57601-8eb1-47a5-adc8-a016b65f481f", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "1c1a73e0-cee8-47e4-ac73-4e5dca832e05", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930654884400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267d9f95-def5-4e37-bb7a-9df96081226e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930656366200, "endTime": 315930656391500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "253c82c5-452e-4223-be0a-e333c92b7d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "253c82c5-452e-4223-be0a-e333c92b7d60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930656366200, "endTime": 315930656391500}, "additional": {"logType": "info", "children": [], "durationId": "267d9f95-def5-4e37-bb7a-9df96081226e", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "de8ceb92-1bd2-45d1-816a-9b8c7a199dfe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930656560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "529e7ab7-563b-4828-a76a-a76339043771", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930658353700, "endTime": 315930658391500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "9dc4755a-7301-4d14-bafe-4480857950cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dc4755a-7301-4d14-bafe-4480857950cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930658353700, "endTime": 315930658391500}, "additional": {"logType": "info", "children": [], "durationId": "529e7ab7-563b-4828-a76a-a76339043771", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "52830a60-2f7c-44ff-aedb-7cd32cc4522f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930658582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36c554bc-68b6-481c-9ed4-98a0c958b3de", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930660919800, "endTime": 315930660965700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "e5b71239-cfdc-4d62-90dd-7cffd02b14e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5b71239-cfdc-4d62-90dd-7cffd02b14e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930660919800, "endTime": 315930660965700}, "additional": {"logType": "info", "children": [], "durationId": "36c554bc-68b6-481c-9ed4-98a0c958b3de", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "db457d55-553e-418e-b1b5-6c87461339d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930661184700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23761de-a014-497b-bae1-61eb2aabf289", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930663576700, "endTime": 315930663608700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "87535af3-2f9f-4f00-933b-d4defd06c91a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87535af3-2f9f-4f00-933b-d4defd06c91a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930663576700, "endTime": 315930663608700}, "additional": {"logType": "info", "children": [], "durationId": "d23761de-a014-497b-bae1-61eb2aabf289", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "867dfa7e-c302-455b-9df7-47e9acdf9769", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930663746500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232bde76-3dc2-4a95-aa64-24f4eb06c757", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930664932800, "endTime": 315930664955900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "823c903c-2600-4970-a0b6-2077aedf8dcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "823c903c-2600-4970-a0b6-2077aedf8dcd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930664932800, "endTime": 315930664955900}, "additional": {"logType": "info", "children": [], "durationId": "232bde76-3dc2-4a95-aa64-24f4eb06c757", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "ceac0dda-f79e-4d64-ab57-a96d7fe807a5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930665082200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08fd903-07e6-4e88-baa4-a36ce491e5ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930666748400, "endTime": 315930666796400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "84789b91-a9d3-4b01-848a-18032909e666"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84789b91-a9d3-4b01-848a-18032909e666", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930666748400, "endTime": 315930666796400}, "additional": {"logType": "info", "children": [], "durationId": "b08fd903-07e6-4e88-baa4-a36ce491e5ca", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "d77662af-0276-415d-a6f0-9323e4a28584", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930667061400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35c8112-4a55-443e-a72e-e519b235b5b7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930668508800, "endTime": 315930668534800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "b1b8efea-386a-4296-a164-a09d3c922a88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1b8efea-386a-4296-a164-a09d3c922a88", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930668508800, "endTime": 315930668534800}, "additional": {"logType": "info", "children": [], "durationId": "d35c8112-4a55-443e-a72e-e519b235b5b7", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "09ece815-0ab6-45e7-802d-f1cfe35f8fbb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930668660100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ee001b-4b93-4e38-bfff-debd8aa628b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930669842400, "endTime": 315930669873900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "120e1e91-c1c5-47a2-8b04-a5dfd47349f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "120e1e91-c1c5-47a2-8b04-a5dfd47349f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930669842400, "endTime": 315930669873900}, "additional": {"logType": "info", "children": [], "durationId": "c4ee001b-4b93-4e38-bfff-debd8aa628b2", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "1aeb3b46-13f2-4af8-9dac-9a141f244824", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930670040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "910b46a0-f88d-4ca1-b1b4-dd987e80e0cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930671666900, "endTime": 315930671697700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "29ef9d52-85ab-4fcd-9377-d81aca66651a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29ef9d52-85ab-4fcd-9377-d81aca66651a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930671666900, "endTime": 315930671697700}, "additional": {"logType": "info", "children": [], "durationId": "910b46a0-f88d-4ca1-b1b4-dd987e80e0cc", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "b3a77c95-d2df-4700-bcf8-ce814b8caa38", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930671876100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "308245ff-30f9-48ae-bf7f-381e8a58b3da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930673581600, "endTime": 315930673610100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "f8835563-65e1-4dd0-bc7f-5276e8a873a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8835563-65e1-4dd0-bc7f-5276e8a873a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930673581600, "endTime": 315930673610100}, "additional": {"logType": "info", "children": [], "durationId": "308245ff-30f9-48ae-bf7f-381e8a58b3da", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "58529255-688b-4266-a5d9-2c6ab70c962d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930673762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a12c70-75b6-4cfe-abd9-c91083c9d635", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930675184700, "endTime": 315930675207500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "4e28035b-d8f9-43e9-b3ad-8433312a20a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e28035b-d8f9-43e9-b3ad-8433312a20a1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930675184700, "endTime": 315930675207500}, "additional": {"logType": "info", "children": [], "durationId": "21a12c70-75b6-4cfe-abd9-c91083c9d635", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "a15888cd-310b-4103-a6ad-06e788e539f8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930675327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b6621a-cf17-46be-99a7-781370e57b83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930676794700, "endTime": 315930676822500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "1da798ac-97f4-45c4-af91-51fd453c7f0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1da798ac-97f4-45c4-af91-51fd453c7f0f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930676794700, "endTime": 315930676822500}, "additional": {"logType": "info", "children": [], "durationId": "70b6621a-cf17-46be-99a7-781370e57b83", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "6684c4cc-8a06-4186-ab80-ec2b29d4729d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930676984300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3a253c-8c95-4d3f-9c13-eefccb2e3d02", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930678487200, "endTime": 315930678516500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "0dfafa6b-9749-4100-93c8-4a14ba1e43ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dfafa6b-9749-4100-93c8-4a14ba1e43ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930678487200, "endTime": 315930678516500}, "additional": {"logType": "info", "children": [], "durationId": "ce3a253c-8c95-4d3f-9c13-eefccb2e3d02", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "59d1122e-6de4-49cc-b9da-78a817d1a59a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930678661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc47fab9-7b92-4499-a63c-5e5ba12a54db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930680201900, "endTime": 315930680229000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "53c425b9-0cb5-473a-ac81-8d47a0934877"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53c425b9-0cb5-473a-ac81-8d47a0934877", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930680201900, "endTime": 315930680229000}, "additional": {"logType": "info", "children": [], "durationId": "fc47fab9-7b92-4499-a63c-5e5ba12a54db", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "92523289-81d8-4b0a-86a9-43959d910aac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930935636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63958bab-4baa-495d-9d05-3988a427ac1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930937805200, "endTime": 315930937829700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "4861eddd-47e4-4b59-91e5-703e340e3690"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4861eddd-47e4-4b59-91e5-703e340e3690", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930937805200, "endTime": 315930937829700}, "additional": {"logType": "info", "children": [], "durationId": "63958bab-4baa-495d-9d05-3988a427ac1f", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "34337b59-532a-49ad-9f3f-a2d131724462", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930974194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0709429-1de2-44a8-8b09-aecf32669d27", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930975503300, "endTime": 315930975531700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "logId": "beb19ae1-1ae1-42e8-92a5-a13873590693"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beb19ae1-1ae1-42e8-92a5-a13873590693", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930975503300, "endTime": 315930975531700}, "additional": {"logType": "info", "children": [], "durationId": "f0709429-1de2-44a8-8b09-aecf32669d27", "parent": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"}}, {"head": {"id": "1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker7", "startTime": 315916294128200, "endTime": 315930975644000}, "additional": {"logType": "error", "children": ["e34b300a-0e3f-4158-99cf-ae3d6c8f0a4a", "2c610f08-4ccd-452c-aabb-ac4b91dc397d", "67979dcb-3f49-40ab-b37c-e562bcaa6e03", "55f19d16-77e2-489e-b9fa-bf367bda485d", "94c196c4-4386-47ff-b598-3bf452e0b670", "be159118-5faf-47a9-9cad-89cf3e400243", "d0f2d576-6875-4d0d-9e15-a92975423430", "06892cc2-6f20-4621-bde9-425b4c8a5734", "458fa6c7-dd0b-453a-abd9-eea12b8b2e66", "9e92d447-0aab-4483-9427-5c3eeb89cbca", "2b13f208-9ead-473a-bdbf-72a750139f30", "a9f1c1ee-145a-4cc2-9898-0aa424700fc2", "2abd3bd6-53ca-447a-82d2-d6a19dc1cdb0", "9019aadd-678f-4a97-bef2-76f3b151ebfa", "3b96d6ea-b7d1-46a3-ab78-36106763093a", "ff43b1b9-0eed-497e-8c72-423fa91c189a", "c9d6ceed-1ca2-4a9d-803c-11638767c781", "2dc67d70-9ca3-43f0-b696-f0cce5dd955f", "356e25e9-7e2f-44fe-96f3-518e398b52a4", "4806f2cc-6053-484e-94df-4a052c6c96b2", "7642190f-69b4-4864-bfa8-492c5e870139", "ae03b2ac-83d9-41fb-a7c4-73d733a62fc9", "4df21fcd-3ecd-45a9-a5ef-40e0fab01cd0", "d6e40e1e-d737-4a09-9230-f84e212292d0", "1c04f72c-663b-47ab-b075-0d1aaea2c44d", "745a7042-d756-43d2-97a1-6a0909158547", "38ad6fe9-6e09-4dbf-b4fd-45328cf192c2", "3cb43988-69df-4b5a-9955-c9e9e526c4f9", "687a7278-4e16-4fc3-8cd1-fad9b83a1394", "64646f3d-4a5c-4d84-9ceb-e609b35593ad", "c1dd3c0e-55e2-4b08-b7b6-4481e4909c8d", "fc122f77-dcd5-431a-9724-c221ce0eab46", "c5535718-16d6-49c6-bc1a-f59c87233002", "672ac940-fc65-48e8-b62b-93217035ff44", "3d54cbfa-31ae-492f-8e4f-8a2448031851", "3afa52bf-2ac3-42f7-85bc-90ae61504550", "be5da107-81e0-457b-89f5-6b9b497016a9", "9af5488f-313b-4d9f-a2ba-e82454b9ce44", "9e5d0a59-c786-4a85-a1ad-e47d8b6958be", "126977c7-fdb6-4c4c-b69f-c9cb7ae72e1f", "eedb8dfb-0ff1-45fc-8de9-17afad47dfeb", "842399b9-b536-45dc-8442-d47b0a0f508e", "77cd577e-d656-4408-b4f9-644eb9973b24", "ef59f47f-6f0d-470b-b050-f09bb3671c0d", "3fa12f31-7de2-418c-be93-128721d25770", "dc3fb39c-d99b-4729-a4c5-77f9dbadca42", "32e086ee-d18a-435a-a39d-aa35e870464d", "cae726b1-7e05-4ac0-9ec8-faf3b1630cff", "e48846b7-8f16-4630-99c5-ab0f2c2beb3a", "39cb2b51-7303-47fe-8e0a-7b8ae871aef1", "71ade891-35ec-42e8-8a46-5756d54b7aaf", "4502750b-aacb-4e37-b62a-f1e8e01acdba", "99551037-bbaa-4582-8e82-994249e47c55", "3f418125-ab10-48ab-ac9d-d8560bcdee10", "253c82c5-452e-4223-be0a-e333c92b7d60", "9dc4755a-7301-4d14-bafe-4480857950cd", "e5b71239-cfdc-4d62-90dd-7cffd02b14e1", "87535af3-2f9f-4f00-933b-d4defd06c91a", "823c903c-2600-4970-a0b6-2077aedf8dcd", "84789b91-a9d3-4b01-848a-18032909e666", "b1b8efea-386a-4296-a164-a09d3c922a88", "120e1e91-c1c5-47a2-8b04-a5dfd47349f1", "29ef9d52-85ab-4fcd-9377-d81aca66651a", "f8835563-65e1-4dd0-bc7f-5276e8a873a4", "4e28035b-d8f9-43e9-b3ad-8433312a20a1", "1da798ac-97f4-45c4-af91-51fd453c7f0f", "0dfafa6b-9749-4100-93c8-4a14ba1e43ac", "53c425b9-0cb5-473a-ac81-8d47a0934877", "4861eddd-47e4-4b59-91e5-703e340e3690", "beb19ae1-1ae1-42e8-92a5-a13873590693"], "durationId": "fe69998a-3b82-43cd-a0e9-4cad282bdf8a", "parent": "31efaec6-ac83-4641-b5ee-cf6d1004d63d"}}, {"head": {"id": "41e5e141-dc17-42e5-98ef-c95c375a9f97", "name": "default@PreviewArkTS watch work[7] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930975908800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31efaec6-ac83-4641-b5ee-cf6d1004d63d", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315916258849600, "endTime": 315930976118400}, "additional": {"logType": "error", "children": ["1a91798e-1f83-4b8b-8a4e-dab21aa3a5b2"], "durationId": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116"}}, {"head": {"id": "74c87b9a-3cdd-4d16-b178-f5f7ce0af8dc", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930976294300}, "additional": {"logType": "debug", "children": [], "durationId": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116"}}, {"head": {"id": "ded41280-8918-429c-9b04-0be9253dc8b7", "name": "ERROR: stacktrace = Error: Compilation failed\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930977079700}, "additional": {"logType": "debug", "children": [], "durationId": "97aa81ea-6b92-4c6c-9406-6bf44ca3a116"}}, {"head": {"id": "0d126537-d99f-4500-8922-73e7d91b94a5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930985626100, "endTime": 315930985690900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67e58224-3000-4d3b-a9c5-1770aff4301f", "logId": "319490c5-bcb4-47dd-8ef1-c2150877b78f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "319490c5-bcb4-47dd-8ef1-c2150877b78f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930985626100, "endTime": 315930985690900}, "additional": {"logType": "info", "children": [], "durationId": "0d126537-d99f-4500-8922-73e7d91b94a5"}}, {"head": {"id": "2f3797b4-d49e-451f-aac5-2165c4b99e52", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315915579674200, "endTime": 315930985894200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 32}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "e65b25fa-9394-4562-858a-f70db8e591b5", "name": "BUILD FAILED in 15 s 407 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930985942400}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "c1c73a7c-0cff-449c-b198-b7108df63390", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986206900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a517ed74-1cbd-4e57-b8f6-32fc522723b5", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b9b57d-3e5d-4a46-bab8-41b6cbe23fac", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986371400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc13d091-6b55-422a-805a-d489e0c8d800", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986437300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84d9386-9456-472b-8616-368b7ed0cd50", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986501900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd981fb6-a078-4477-9efd-38aca3daae6e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986568700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b055d0a0-92f5-4dfb-a1d1-47d0d8a57db7", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930986635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83c16402-98e2-44d7-9177-97fdeaba5992", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930988101000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af47e74-2107-42f2-87c1-250c8a2e0f82", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930998650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f798b0b-3155-432c-8d74-9d72d8d33ff6", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315930999615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8780b3-b46c-4660-9e21-6fc506132482", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931022013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994680a6-5596-4bce-8fe9-c14868da1733", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:37 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931023044600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45f7ae5-f1fa-4af5-8d10-bfb4147627c7", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931023335500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab08ceb6-e3e8-4473-b5d3-39765ed2e67e", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931024411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3c5ce87-6aee-4143-a149-a6ed24d95cbf", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931025752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c050b1f4-2cc6-4021-aad4-309debd3a694", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931026476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb848723-e9c5-44e3-a70d-9848c0d5bec0", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931027131800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06055933-645a-4ed4-8ecf-9972404b8ec0", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931027645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85c95010-cfa9-4992-a05d-6d1fa3b00637", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931032126900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5bdca0b-079f-4edc-b6cd-85e5d8131333", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931033798100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "044ea1eb-ad01-47b1-8372-755ede490f9c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931034484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf33590-97f6-4d61-9f64-934a15a410cd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931035194800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff692d07-f795-4c9b-b7a6-4ba9146a35f4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931037076600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b888022d-4532-4732-aba9-83d7e7f092f2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931056508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21377bc8-e6a2-4459-ad0e-0ccc33066bb3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931057227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee6d375-1d4f-4f02-bf98-1c5b3fee6e81", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931057865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0cd2e36-c996-41e1-b7f3-58f1f43829e0", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931058650000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd4a101-c207-4973-aa43-451b41aa<PERSON>c", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:32 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931059223200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}