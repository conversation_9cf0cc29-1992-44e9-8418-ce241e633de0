if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface QRCodePaymentPage_Params {
    isScanning?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class QRCodePaymentPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isScanning = new ObservedPropertySimplePU(false, this, "isScanning");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: QRCodePaymentPage_Params) {
        if (params.isScanning !== undefined) {
            this.isScanning = params.isScanning;
        }
    }
    updateStateVars(params: QRCodePaymentPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isScanning.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isScanning.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isScanning: ObservedPropertySimplePU<boolean>;
    get isScanning() {
        return this.__isScanning.get();
    }
    set isScanning(newValue: boolean) {
        this.__isScanning.set(newValue);
    }
    aboutToAppear() {
        console.log('QRCodePaymentPage 页面加载成功');
    }
    // 处理支付完成
    private handlePaymentComplete() {
        promptAction.showToast({
            message: '扫码支付完成！',
            duration: 2000
        });
        // 延迟返回
        setTimeout(() => {
            router.back();
        }, 2000);
    }
    // 取消支付
    private handleCancel() {
        router.back();
    }
    // 模拟扫码
    private startScanning() {
        this.isScanning = true;
        // 模拟扫码过程
        setTimeout(() => {
            this.isScanning = false;
            promptAction.showToast({
                message: '扫码成功，正在处理支付...',
                duration: 2000
            });
            // 模拟支付处理
            setTimeout(() => {
                this.handlePaymentComplete();
            }, 2000);
        }, 3000);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(51:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(53:7)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor('#FFFFFF');
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('×');
            Button.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(54:9)", "entry");
            Button.fontSize(24);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('扫码支付');
            Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(62:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(69:9)", "entry");
            Text.width(40);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 扫码内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(82:7)", "entry");
            // 扫码内容区域
            Column.layoutWeight(1);
            // 扫码内容区域
            Column.padding(20);
            // 扫码内容区域
            Column.backgroundColor('#FFFFFF');
            // 扫码内容区域
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提示文字
            Text.create('请扫描下方二维码完成支付');
            Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(84:9)", "entry");
            // 提示文字
            Text.fontSize(16);
            // 提示文字
            Text.fontColor('#666666');
            // 提示文字
            Text.margin({ top: 40, bottom: 40 });
        }, Text);
        // 提示文字
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 二维码区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(90:9)", "entry");
            // 二维码区域
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isScanning) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 扫描中状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(93:13)", "entry");
                        // 扫描中状态
                        Column.width(200);
                        // 扫描中状态
                        Column.height(200);
                        // 扫描中状态
                        Column.justifyContent(FlexAlign.Center);
                        // 扫描中状态
                        Column.alignItems(HorizontalAlign.Center);
                        // 扫描中状态
                        Column.backgroundColor('#F8F9FA');
                        // 扫描中状态
                        Column.border({
                            width: 2,
                            color: '#E5E5E5',
                            style: BorderStyle.Dashed
                        });
                        // 扫描中状态
                        Column.borderRadius(12);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(94:15)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#4A90E2');
                        LoadingProgress.margin({ bottom: 20 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在扫描...');
                        Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(100:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#4A90E2');
                    }, Text);
                    Text.pop();
                    // 扫描中状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 二维码显示
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(117:13)", "entry");
                        // 二维码显示
                        Column.width(200);
                        // 二维码显示
                        Column.height(200);
                        // 二维码显示
                        Column.justifyContent(FlexAlign.Center);
                        // 二维码显示
                        Column.alignItems(HorizontalAlign.Center);
                        // 二维码显示
                        Column.backgroundColor('#F8F9FA');
                        // 二维码显示
                        Column.border({
                            width: 2,
                            color: '#E5E5E5',
                            style: BorderStyle.Dashed
                        });
                        // 二维码显示
                        Column.borderRadius(12);
                        // 二维码显示
                        Column.onClick(() => {
                            if (!this.isScanning) {
                                this.startScanning();
                            }
                        });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 模拟二维码图标
                        Text.create('📱');
                        Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(119:15)", "entry");
                        // 模拟二维码图标
                        Text.fontSize(60);
                        // 模拟二维码图标
                        Text.margin({ bottom: 20 });
                    }, Text);
                    // 模拟二维码图标
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('扫码支付');
                        Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(123:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                    }, Text);
                    Text.pop();
                    // 二维码显示
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // 二维码区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付方式说明
            Text.create('使用支付宝、微信扫码支付');
            Text.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(149:9)", "entry");
            // 支付方式说明
            Text.fontSize(14);
            // 支付方式说明
            Text.fontColor('#999999');
            // 支付方式说明
            Text.margin({ bottom: 60 });
        }, Text);
        // 支付方式说明
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(155:9)", "entry");
            // 操作按钮
            Row.width('100%');
            // 操作按钮
            Row.justifyContent(FlexAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(156:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor('#F8F9FA');
            Button.border({ width: 1, color: '#E5E5E5' });
            Button.borderRadius(8);
            Button.width(100);
            Button.height(44);
            Button.onClick(() => {
                this.handleCancel();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('支付完成');
            Button.debugLine("entry/src/main/ets/pages/QRCodePaymentPage.ets(168:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#4A90E2');
            Button.borderRadius(8);
            Button.width(120);
            Button.height(44);
            Button.margin({ left: 20 });
            Button.onClick(() => {
                this.handlePaymentComplete();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 扫码内容区域
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "QRCodePaymentPage";
    }
}
registerNamedRoute(() => new QRCodePaymentPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/QRCodePaymentPage", pageFullPath: "entry/src/main/ets/pages/QRCodePaymentPage", integratedHsp: "false", moduleType: "followWithHap" });
