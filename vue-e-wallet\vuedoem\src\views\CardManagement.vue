<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { bankCardApi } from '../api/index.js'

const route = useRoute()
const isCreditCard = ref(route.query.type === 'credit')

const dialogVisible = ref(false)
const editDialogVisible = ref(false)
const formLabelWidth = '120px'
const currentCard = ref(null)
const loading = ref(false)
const cardFormRef = ref()
const editFormRef = ref()
const isEditing = ref(false)

// 获取当前用户ID（从localStorage或其他地方获取）
const getCurrentUserId = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.id || 1 // 默认用户ID为1
}

const cardForm = reactive({
  userId: getCurrentUserId(),
  cardNumber: '',
  cardType: 'DEBIT',
  bankName: '',
  balance: 0,
  creditLimit: 0,
  bindStatus: 1
})

// 编辑表单
const editForm = reactive({
  id: '',
  bankName: '',
  cardNumber: '',
  cardType: 'DEBIT',
  creditLimit: 0
})

// 表单验证规则
const cardFormRules = {
  cardNumber: [
    { required: true, message: '请输入银行卡号', trigger: 'blur' },
    { pattern: /^\d{16,19}$/, message: '银行卡号必须为16-19位数字', trigger: 'blur' }
  ],
  cardType: [
    { required: true, message: '请选择卡片类型', trigger: 'change' }
  ],
  bankName: [
    { required: true, message: '请输入银行名称', trigger: 'blur' },
    { min: 2, max: 50, message: '银行名称长度在2到50个字符', trigger: 'blur' }
  ],
  balance: [
    { required: true, message: '请输入余额', trigger: 'blur' },
    { type: 'number', min: 0, message: '余额不能为负数', trigger: 'blur' }
  ]
}

// 编辑表单验证规则
const editFormRules = {
  bankName: [
    { required: true, message: '请输入银行名称', trigger: 'blur' },
    { min: 2, max: 50, message: '银行名称长度在2到50个字符', trigger: 'blur' }
  ],
  cardNumber: [
    { required: true, message: '请输入银行卡号', trigger: 'blur' },
    { pattern: /^\d{16,19}$/, message: '银行卡号必须为16-19位数字', trigger: 'blur' }
  ],
  cardType: [
    { required: true, message: '请选择卡片类型', trigger: 'change' }
  ],
  creditLimit: [
    { type: 'number', min: 0, message: '信用额度不能为负数', trigger: 'blur' }
  ]
}

const cardList = ref([])

// 计算属性：根据卡片类型过滤
const filteredCardList = computed(() => {
  if (isCreditCard.value) {
    return cardList.value.filter(card => card.cardType === 'CREDIT')
  }
  return cardList.value
})

// 获取银行卡列表
const fetchCardList = async () => {
  try {
    loading.value = true
    const userId = getCurrentUserId()
    const response = await bankCardApi.getAllCards(userId)

    // axios拦截器已经处理了响应，response就是后端返回的数据
    cardList.value = response.data || []
    console.log('银行卡列表获取成功:', cardList.value)
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    cardList.value = []
    ElMessage.error('获取银行卡列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchCardList()
})

// 绑定/解绑银行卡
const handleBind = async (cardId, bind) => {
  try {
    if (bind) {
      // 绑定已存在的银行卡
      await bankCardApi.bindExistingCard(cardId)
    } else {
      await bankCardApi.unbindCard(cardId)
    }

    ElMessage.success(bind ? '绑定成功' : '解绑成功')
    await fetchCardList() // 刷新列表
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error(bind ? '绑定失败' : '解绑失败')
  }
}

const showCardDetail = (card) => {
  currentCard.value = card
  dialogVisible.value = true
}

const handleAddCard = () => {
  currentCard.value = null
  Object.assign(cardForm, {
    userId: getCurrentUserId(),
    cardNumber: '',
    cardType: 'DEBIT',
    bankName: '',
    balance: 0,
    creditLimit: 0,
    bindStatus: 1
  })
  dialogVisible.value = true
}

// 提交添加银行卡
const submitAddCard = async () => {
  try {
    // 表单验证
    await cardFormRef.value.validate()

    loading.value = true

    // 准备提交数据 - 使用表单格式
    const formData = new URLSearchParams()
    formData.append('userId', getCurrentUserId().toString())
    formData.append('bankName', cardForm.bankName)
    formData.append('cardNumber', cardForm.cardNumber)
    formData.append('cardType', cardForm.cardType)
    formData.append('balance', Number(cardForm.balance).toString())
    formData.append('creditLimit', (cardForm.cardType === 'CREDIT' ? Number(cardForm.creditLimit) : 0).toString())

    await bankCardApi.bindCard(formData)
    ElMessage.success('银行卡添加成功')
    dialogVisible.value = false
    await fetchCardList() // 刷新列表
  } catch (error) {
    console.error('添加银行卡失败:', error)
    const errorMessage = error.response?.data?.msg || error.message || '添加银行卡失败'
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 编辑银行卡
const handleEditCard = (card) => {
  Object.assign(editForm, {
    id: card.id,
    bankName: card.bankName,
    cardNumber: card.cardNumber,
    cardType: card.cardType,
    creditLimit: card.creditLimit || 0
  })
  editDialogVisible.value = true
}

// 提交修改银行卡
const submitEditCard = async () => {
  try {
    await editFormRef.value.validate()
    loading.value = true

    const cardData = {
      id: editForm.id,
      bankName: editForm.bankName,
      cardNumber: editForm.cardNumber,
      cardType: editForm.cardType,
      creditLimit: editForm.cardType === 'CREDIT' ? Number(editForm.creditLimit) : 0
    }

    await bankCardApi.updateCard(editForm.id, cardData)
    ElMessage.success('修改银行卡成功')
    editDialogVisible.value = false
    await fetchCardList()
  } catch (error) {
    console.error('修改银行卡失败:', error)
    const errorMessage = error.response?.data?.msg || error.message || '修改银行卡失败'
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 删除银行卡
const handleDeleteCard = async (card) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除银行卡 ${card.bankName} (**** ${card.cardNumber.slice(-4)}) 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true
    await bankCardApi.deleteCard(card.id)
    ElMessage.success('删除银行卡成功')
    await fetchCardList()
  } catch (error) {
    if (error === 'cancel') return
    console.error('删除银行卡失败:', error)
    const errorMessage = error.response?.data?.msg || error.message || '删除银行卡失败'
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}


</script>

<template>
  <div>
    <div>
      <el-button type="primary" @click="handleAddCard">添加银行卡</el-button>
    </div>

    <div style="max-height: 600px; overflow-y: auto; margin-top: 20px;">
      <el-table :data="filteredCardList" v-loading="loading" height="100%">
      <el-table-column prop="cardNumber" label="卡号" width="180">
        <template #default="{row}">
          **** **** **** {{ row.cardNumber.slice(-4) }}
        </template>
      </el-table-column>
      <el-table-column prop="cardType" label="卡片类型" width="120">
        <template #default="{row}">
          <el-tag :type="row.cardType === 'CREDIT' ? 'warning' : 'success'">
            {{ row.cardType === 'CREDIT' ? '信用卡' : '储蓄卡' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bankName" label="银行名称" width="150" />
      <el-table-column prop="balance" label="余额" width="120">
        <template #default="{row}">
          ¥{{ Number(row.balance).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="creditLimit" label="信用额度" width="120" v-if="isCreditCard">
        <template #default="{row}">
          <span v-if="row.cardType === 'CREDIT'">¥{{ Number(row.creditLimit).toFixed(2) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="bindStatus" label="绑定状态" width="120">
        <template #default="{row}">
          <el-tag :type="row.bindStatus === 1 ? 'success' : 'info'">
            {{ row.bindStatus === 1 ? '已绑定' : '未绑定' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="300">
        <template #default="{row}">
          <el-button type="primary" size="small" @click="showCardDetail(row)">详情</el-button>
          <el-button type="warning" size="small" @click="handleEditCard(row)">修改</el-button>

          <el-button
            :type="row.bindStatus === 1 ? 'danger' : 'success'"
            size="small"
            @click="handleBind(row.id, row.bindStatus === 0)"
          >
            {{ row.bindStatus === 1 ? '解绑' : '绑定' }}
          </el-button>
          <el-button type="danger" size="small" @click="handleDeleteCard(row)">删除</el-button>
        </template>
      </el-table-column>
      </el-table>
    </div>

    <el-dialog
      v-model="dialogVisible"
      :title="currentCard ? '银行卡详情' : '添加银行卡'"
    >
      <el-descriptions :column="2" border v-if="currentCard">
        <el-descriptions-item label="卡号">
          **** **** **** {{ currentCard.cardNumber.slice(-4) }}
        </el-descriptions-item>
        <el-descriptions-item label="卡片类型">
          {{ currentCard.cardType === 'CREDIT' ? '信用卡' : '储蓄卡' }}
        </el-descriptions-item>
        <el-descriptions-item label="银行名称">{{ currentCard.bankName }}</el-descriptions-item>
        <el-descriptions-item label="余额">¥{{ Number(currentCard.balance).toFixed(2) }}</el-descriptions-item>
        <template v-if="currentCard.cardType === 'CREDIT'">
          <el-descriptions-item label="信用额度">¥{{ Number(currentCard.creditLimit).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="可用额度">
            ¥{{ (Number(currentCard.creditLimit) - Number(currentCard.balance)).toFixed(2) }}
          </el-descriptions-item>
        </template>
        <el-descriptions-item label="绑定状态">
          <el-tag :type="currentCard.bindStatus === 1 ? 'success' : 'info'">
            {{ currentCard.bindStatus === 1 ? '已绑定' : '未绑定' }}
          </el-tag>
        </el-descriptions-item>

      </el-descriptions>

      <div v-else>
        <el-form
          ref="cardFormRef"
          :model="cardForm"
          :rules="cardFormRules"
          label-position="right"
          :label-width="formLabelWidth"
        >
          <el-form-item label="银行卡号" prop="cardNumber">
            <el-input
              v-model="cardForm.cardNumber"
              placeholder="请输入16-19位银行卡号"
              maxlength="19"
            />
          </el-form-item>
          <el-form-item label="卡片类型" prop="cardType">
            <el-select v-model="cardForm.cardType" placeholder="请选择卡片类型">
              <el-option label="储蓄卡" value="DEBIT" />
              <el-option label="信用卡" value="CREDIT" />
            </el-select>
          </el-form-item>
          <el-form-item label="银行名称" prop="bankName">
            <el-input v-model="cardForm.bankName" placeholder="请输入银行名称" />
          </el-form-item>
          <el-form-item label="初始余额" prop="balance">
            <el-input
              v-model.number="cardForm.balance"
              type="number"
              placeholder="请输入初始余额"
              :min="0"
            />
          </el-form-item>
          <el-form-item
            label="信用额度"
            prop="creditLimit"
            v-if="cardForm.cardType === 'CREDIT'"
          >
            <el-input
              v-model.number="cardForm.creditLimit"
              type="number"
              placeholder="请输入信用额度"
              :min="0"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitAddCard"
          v-if="!currentCard"
          :loading="loading"
        >
          确定添加
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑银行卡对话框 -->
    <el-dialog v-model="editDialogVisible" title="修改银行卡">
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-position="right"
        :label-width="formLabelWidth"
      >
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model="editForm.bankName" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="银行卡号" prop="cardNumber">
          <el-input
            v-model="editForm.cardNumber"
            placeholder="请输入16-19位银行卡号"
            maxlength="19"
          />
        </el-form-item>
        <el-form-item label="卡片类型" prop="cardType">
          <el-select v-model="editForm.cardType" placeholder="请选择卡片类型">
            <el-option label="储蓄卡" value="DEBIT" />
            <el-option label="信用卡" value="CREDIT" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="信用额度"
          prop="creditLimit"
          v-if="editForm.cardType === 'CREDIT'"
        >
          <el-input
            v-model.number="editForm.creditLimit"
            type="number"
            placeholder="请输入信用额度"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEditCard" :loading="loading">
          确定修改
        </el-button>
      </template>
    </el-dialog>


  </div>
</template>

