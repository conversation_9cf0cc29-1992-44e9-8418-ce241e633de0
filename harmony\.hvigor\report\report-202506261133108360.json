{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "3963deca-2f9a-4c31-9fd9-9aa9a04574cc", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315931088276300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e0f28b-e2fb-4dde-811a-442b394b281f", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315932561070300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22de7ef9-8b75-4896-b74e-3299220cc199", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315932561365300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69953565-7d08-4a34-8228-0a7644464725", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957139380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8875fc4-4ba4-419b-9242-df80f1000c92", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957152156400, "endTime": 315957453298300}, "additional": {"children": ["c00be5f3-047a-4769-a579-06be241d4072", "9f84e0dc-2ae9-4024-9dee-fb407610675b", "084ce0c3-1bd5-4883-a3c4-04340baac460", "692cd7da-9fc9-4dc6-b258-bbdd41be8c70", "38bd3213-9dba-490e-b4ae-ebeeb36dfde3", "81822ca3-6040-43f1-a5fd-585dc76e998e", "004cb203-0e73-44b5-bfdc-9de9a781596a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "436b6254-dade-48ae-a89b-445e53496035"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c00be5f3-047a-4769-a579-06be241d4072", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957152159400, "endTime": 315957178527300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "33928ede-8d0b-45bb-afa9-4e665ab8dda9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957178585900, "endTime": 315957450985200}, "additional": {"children": ["0fabe588-e89e-4af3-bf99-018215fa033f", "7156da84-0af8-4f97-b495-533ce273f7c0", "c73366b3-c743-4874-b782-569341a09ac8", "96175ada-d866-4c44-8b63-bcd528278d0f", "f9f55d30-6540-4a2f-982d-edd444c063b6", "34ba368c-3460-4168-b0cb-1e68190422eb", "273339e8-4a81-4768-a4eb-bd7fdd9aff2f", "911b31ff-ea29-4858-83fa-d54db6cdb506", "a1da4c53-32b2-4867-9085-d47870dc9900"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "084ce0c3-1bd5-4883-a3c4-04340baac460", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957451013400, "endTime": 315957453264000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "e8062cbe-4608-40ae-9121-db00a457c9cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "692cd7da-9fc9-4dc6-b258-bbdd41be8c70", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957453279900, "endTime": 315957453291500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "0f7955e0-a3c9-4cbf-9ec4-5839f0d43489"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38bd3213-9dba-490e-b4ae-ebeeb36dfde3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957158701900, "endTime": 315957159053800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "c83700fc-7ce5-45c4-adde-9e4b2ee9c858"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c83700fc-7ce5-45c4-adde-9e4b2ee9c858", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957158701900, "endTime": 315957159053800}, "additional": {"logType": "info", "children": [], "durationId": "38bd3213-9dba-490e-b4ae-ebeeb36dfde3", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "81822ca3-6040-43f1-a5fd-585dc76e998e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957169205100, "endTime": 315957169241500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "8e913390-a3f8-49d2-8174-435c3c6d263e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e913390-a3f8-49d2-8174-435c3c6d263e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957169205100, "endTime": 315957169241500}, "additional": {"logType": "info", "children": [], "durationId": "81822ca3-6040-43f1-a5fd-585dc76e998e", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "0bcf400e-83d1-45a1-9375-c82c2b238872", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957169357800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c0c39e-0af1-4d3b-8e1f-3c994d314ef2", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957178231800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33928ede-8d0b-45bb-afa9-4e665ab8dda9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957152159400, "endTime": 315957178527300}, "additional": {"logType": "info", "children": [], "durationId": "c00be5f3-047a-4769-a579-06be241d4072", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "0fabe588-e89e-4af3-bf99-018215fa033f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957190939800, "endTime": 315957190978900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "491e907a-5cf3-4887-b2e4-dd3d7c718c14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7156da84-0af8-4f97-b495-533ce273f7c0", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957191035400, "endTime": 315957199367600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "5febbb5c-a9c6-4e21-bc91-31ffb6dbbae6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c73366b3-c743-4874-b782-569341a09ac8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957199397600, "endTime": 315957334619500}, "additional": {"children": ["0cfc444d-6dda-40e7-a9e3-908776e25d63", "86426118-e2be-455f-ab6e-8620d85d4f36", "0d2f5fb8-5917-4007-b382-3dedbcccd8ac"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "e452fdc6-4f37-49ff-95fd-705dcfd0e502"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96175ada-d866-4c44-8b63-bcd528278d0f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957334645200, "endTime": 315957379468300}, "additional": {"children": ["f2900a1f-5f7f-45b3-b0d8-d869e65a7ace"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "bec630b2-0016-43b8-b2c4-aef99d6480b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9f55d30-6540-4a2f-982d-edd444c063b6", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957379484700, "endTime": 315957419483000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "ad18251d-a853-4e84-a0ed-0389055b2546"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34ba368c-3460-4168-b0cb-1e68190422eb", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957420959100, "endTime": 315957432652200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "f604d127-ba70-418e-8462-69f3498830f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "273339e8-4a81-4768-a4eb-bd7fdd9aff2f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957432681400, "endTime": 315957450734700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "5fe2e00d-0a53-40cc-a74d-7459103d7ed5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "911b31ff-ea29-4858-83fa-d54db6cdb506", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957450779500, "endTime": 315957450968000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "46b02cdd-82cd-4403-b20e-a6a0a01ca60d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "491e907a-5cf3-4887-b2e4-dd3d7c718c14", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957190939800, "endTime": 315957190978900}, "additional": {"logType": "info", "children": [], "durationId": "0fabe588-e89e-4af3-bf99-018215fa033f", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "5febbb5c-a9c6-4e21-bc91-31ffb6dbbae6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957191035400, "endTime": 315957199367600}, "additional": {"logType": "info", "children": [], "durationId": "7156da84-0af8-4f97-b495-533ce273f7c0", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "0cfc444d-6dda-40e7-a9e3-908776e25d63", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957200521200, "endTime": 315957200818600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c73366b3-c743-4874-b782-569341a09ac8", "logId": "fa27494a-75da-48cf-a2c2-6f09233f087f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa27494a-75da-48cf-a2c2-6f09233f087f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957200521200, "endTime": 315957200818600}, "additional": {"logType": "info", "children": [], "durationId": "0cfc444d-6dda-40e7-a9e3-908776e25d63", "parent": "e452fdc6-4f37-49ff-95fd-705dcfd0e502"}}, {"head": {"id": "86426118-e2be-455f-ab6e-8620d85d4f36", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957205065700, "endTime": 315957333514600}, "additional": {"children": ["2db516fa-e609-4d64-bae4-9fa244dbe26b", "23f0c39f-e43a-4e40-94fd-9ba7dc8996ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c73366b3-c743-4874-b782-569341a09ac8", "logId": "dbb085eb-ad04-41b2-99b2-040641f7cda4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2db516fa-e609-4d64-bae4-9fa244dbe26b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957205068600, "endTime": 315957213517500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86426118-e2be-455f-ab6e-8620d85d4f36", "logId": "d5789b39-0f84-46ff-bad6-b5e876db7bd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23f0c39f-e43a-4e40-94fd-9ba7dc8996ed", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957213548400, "endTime": 315957333499400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86426118-e2be-455f-ab6e-8620d85d4f36", "logId": "87014276-e652-4014-8e92-4d5bc43dbd0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2c731e7-8f96-418e-966e-ddaa3065b155", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957205081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0dd701-74c1-4470-a687-0f84d97613a1", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957213287000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5789b39-0f84-46ff-bad6-b5e876db7bd0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957205068600, "endTime": 315957213517500}, "additional": {"logType": "info", "children": [], "durationId": "2db516fa-e609-4d64-bae4-9fa244dbe26b", "parent": "dbb085eb-ad04-41b2-99b2-040641f7cda4"}}, {"head": {"id": "1af61461-ee3c-4bd1-a012-fcae27f60fe0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957213576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c79e387b-85ce-4f1f-9c52-f536d65634ea", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957227382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0599a46-6962-4a30-8d16-89303bd7fd9c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957227537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841fa888-77b9-4cd2-a465-ebe91a288317", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957227706700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcac0c2-25df-4871-921b-ee4dc20ac6b3", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957227829800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3590ba14-1d40-445c-932d-a652d1940648", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957231741500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a9134f-736f-40e7-9bf1-554a6e93ebb5", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957241595000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8da529-c9ab-4e82-a299-90504ff487c3", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957258901400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e01c33d-a7e4-44e2-ab2b-0752aa9d1196", "name": "Sdk init in 56 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957300034200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8dbd0fd-da12-496c-b1cb-0a3bdfa7c074", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957300212100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 33}, "markType": "other"}}, {"head": {"id": "aeb648a1-926e-4bd8-b164-3c4eab11088a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957300228800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 33}, "markType": "other"}}, {"head": {"id": "98d6fe4f-17d0-4a8e-868b-c2bf18d6160a", "name": "Project task initialization takes 31 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957332916500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8520ee79-7585-4fdf-809f-46cc114d5808", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957333230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279ad1d9-ed7c-481b-ab12-2ca54309f9cd", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957333331100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490696b6-dbf1-4aac-ba54-f009d8670309", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957333432100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87014276-e652-4014-8e92-4d5bc43dbd0a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957213548400, "endTime": 315957333499400}, "additional": {"logType": "info", "children": [], "durationId": "23f0c39f-e43a-4e40-94fd-9ba7dc8996ed", "parent": "dbb085eb-ad04-41b2-99b2-040641f7cda4"}}, {"head": {"id": "dbb085eb-ad04-41b2-99b2-040641f7cda4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957205065700, "endTime": 315957333514600}, "additional": {"logType": "info", "children": ["d5789b39-0f84-46ff-bad6-b5e876db7bd0", "87014276-e652-4014-8e92-4d5bc43dbd0a"], "durationId": "86426118-e2be-455f-ab6e-8620d85d4f36", "parent": "e452fdc6-4f37-49ff-95fd-705dcfd0e502"}}, {"head": {"id": "0d2f5fb8-5917-4007-b382-3dedbcccd8ac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957334568500, "endTime": 315957334592100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c73366b3-c743-4874-b782-569341a09ac8", "logId": "40a63b58-e0a3-470e-87c8-507eff8af51b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40a63b58-e0a3-470e-87c8-507eff8af51b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957334568500, "endTime": 315957334592100}, "additional": {"logType": "info", "children": [], "durationId": "0d2f5fb8-5917-4007-b382-3dedbcccd8ac", "parent": "e452fdc6-4f37-49ff-95fd-705dcfd0e502"}}, {"head": {"id": "e452fdc6-4f37-49ff-95fd-705dcfd0e502", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957199397600, "endTime": 315957334619500}, "additional": {"logType": "info", "children": ["fa27494a-75da-48cf-a2c2-6f09233f087f", "dbb085eb-ad04-41b2-99b2-040641f7cda4", "40a63b58-e0a3-470e-87c8-507eff8af51b"], "durationId": "c73366b3-c743-4874-b782-569341a09ac8", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "f2900a1f-5f7f-45b3-b0d8-d869e65a7ace", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957336033100, "endTime": 315957379443000}, "additional": {"children": ["2130042d-694c-4013-a860-83ec16e8a823", "8be1b158-1f6d-4d0e-a952-de763600d8a7", "8619f96d-743b-4102-98db-2738d92cf52d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96175ada-d866-4c44-8b63-bcd528278d0f", "logId": "9becb76f-f4a7-4ed4-b6f4-215985777790"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2130042d-694c-4013-a860-83ec16e8a823", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957343069600, "endTime": 315957343097200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2900a1f-5f7f-45b3-b0d8-d869e65a7ace", "logId": "214977f7-3c6f-495a-b040-bd4c1d3de5ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "214977f7-3c6f-495a-b040-bd4c1d3de5ce", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957343069600, "endTime": 315957343097200}, "additional": {"logType": "info", "children": [], "durationId": "2130042d-694c-4013-a860-83ec16e8a823", "parent": "9becb76f-f4a7-4ed4-b6f4-215985777790"}}, {"head": {"id": "8be1b158-1f6d-4d0e-a952-de763600d8a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957347521300, "endTime": 315957376545800}, "additional": {"children": ["19e54400-6173-4788-9c84-d7c1ab71bf8e", "d0462089-3504-4a05-93eb-933b8d3c5cfa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2900a1f-5f7f-45b3-b0d8-d869e65a7ace", "logId": "9c60a869-a49a-4ec2-9bbf-e993579d0234"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19e54400-6173-4788-9c84-d7c1ab71bf8e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957347523900, "endTime": 315957353359600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8be1b158-1f6d-4d0e-a952-de763600d8a7", "logId": "e17a79ad-447d-4c95-87b0-2d03d604377a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0462089-3504-4a05-93eb-933b8d3c5cfa", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957353382600, "endTime": 315957376530100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8be1b158-1f6d-4d0e-a952-de763600d8a7", "logId": "b33021f9-7732-455a-9c08-b4cc5bfd6bbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5f40a72-b05f-411d-856d-2c8060c6fbe2", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957347536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f3e77d-54e5-4294-b9cb-0ced1b3d4376", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957353202400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17a79ad-447d-4c95-87b0-2d03d604377a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957347523900, "endTime": 315957353359600}, "additional": {"logType": "info", "children": [], "durationId": "19e54400-6173-4788-9c84-d7c1ab71bf8e", "parent": "9c60a869-a49a-4ec2-9bbf-e993579d0234"}}, {"head": {"id": "77a64bec-e414-449d-bc9f-ae727e7c53e8", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957353401600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42878047-2fcf-458d-b2b4-487af66a4617", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957365592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f832bb-5508-44ba-99ad-088accbb56ec", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957365809000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1e14e0-7af2-4d7a-8e5d-9c1cdd065a86", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957367478900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6a8166-db55-4927-86e4-07d3728c20bc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957367974700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b74287-e3f6-4c4b-88c9-1bc255c0e0c6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957368149800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f7b67ea-855c-41bf-8178-a9874363a9e6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957368243600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0739ac19-5519-4027-8b91-af97d668cac2", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957368531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e5d6519-5bb8-4ea6-8602-4ce92d889114", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957376127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35aa65d5-6754-4378-9358-10616fdbb892", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957376317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d72371-602a-47b0-8928-1b4ea86d53fd", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957376397000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b850b59f-d5c6-4b3a-9505-4e652581af02", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957376472900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33021f9-7732-455a-9c08-b4cc5bfd6bbd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957353382600, "endTime": 315957376530100}, "additional": {"logType": "info", "children": [], "durationId": "d0462089-3504-4a05-93eb-933b8d3c5cfa", "parent": "9c60a869-a49a-4ec2-9bbf-e993579d0234"}}, {"head": {"id": "9c60a869-a49a-4ec2-9bbf-e993579d0234", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957347521300, "endTime": 315957376545800}, "additional": {"logType": "info", "children": ["e17a79ad-447d-4c95-87b0-2d03d604377a", "b33021f9-7732-455a-9c08-b4cc5bfd6bbd"], "durationId": "8be1b158-1f6d-4d0e-a952-de763600d8a7", "parent": "9becb76f-f4a7-4ed4-b6f4-215985777790"}}, {"head": {"id": "8619f96d-743b-4102-98db-2738d92cf52d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957379384400, "endTime": 315957379409500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2900a1f-5f7f-45b3-b0d8-d869e65a7ace", "logId": "c6782d6b-168f-4a03-8b5a-650a8e68c635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6782d6b-168f-4a03-8b5a-650a8e68c635", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957379384400, "endTime": 315957379409500}, "additional": {"logType": "info", "children": [], "durationId": "8619f96d-743b-4102-98db-2738d92cf52d", "parent": "9becb76f-f4a7-4ed4-b6f4-215985777790"}}, {"head": {"id": "9becb76f-f4a7-4ed4-b6f4-215985777790", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957336033100, "endTime": 315957379443000}, "additional": {"logType": "info", "children": ["214977f7-3c6f-495a-b040-bd4c1d3de5ce", "9c60a869-a49a-4ec2-9bbf-e993579d0234", "c6782d6b-168f-4a03-8b5a-650a8e68c635"], "durationId": "f2900a1f-5f7f-45b3-b0d8-d869e65a7ace", "parent": "bec630b2-0016-43b8-b2c4-aef99d6480b6"}}, {"head": {"id": "bec630b2-0016-43b8-b2c4-aef99d6480b6", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957334645200, "endTime": 315957379468300}, "additional": {"logType": "info", "children": ["9becb76f-f4a7-4ed4-b6f4-215985777790"], "durationId": "96175ada-d866-4c44-8b63-bcd528278d0f", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "7be1e73a-0645-40cd-8fff-024bb55aa206", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957418410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7dd3c0-f11c-48ff-b77b-8a930ab3b355", "name": "hvigorfile, resolve hvigorfile dependencies in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957419278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad18251d-a853-4e84-a0ed-0389055b2546", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957379484700, "endTime": 315957419483000}, "additional": {"logType": "info", "children": [], "durationId": "f9f55d30-6540-4a2f-982d-edd444c063b6", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "a1da4c53-32b2-4867-9085-d47870dc9900", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957420655100, "endTime": 315957420923400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "logId": "3039e7ec-20e9-472e-bf0a-9a051828921c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cb627b6-e7b0-484d-9b12-5ff8ee7c7248", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957420695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3039e7ec-20e9-472e-bf0a-9a051828921c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957420655100, "endTime": 315957420923400}, "additional": {"logType": "info", "children": [], "durationId": "a1da4c53-32b2-4867-9085-d47870dc9900", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "f30d845c-c52d-471d-b97a-fddefd258540", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957424139900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "767d9e0f-eb7e-42cb-9fdb-0c492bab4b50", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957431467000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f604d127-ba70-418e-8462-69f3498830f4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957420959100, "endTime": 315957432652200}, "additional": {"logType": "info", "children": [], "durationId": "34ba368c-3460-4168-b0cb-1e68190422eb", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "c75afc94-8362-4ef5-a6d0-45a9e463f3fc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957432713300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d6d0d4e-2884-4ca4-a763-b15f848dfa70", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957441770000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940624dc-045e-403d-8621-f112c940b3da", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957441968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4105c1-7ef5-447c-9921-292e742f3f1f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957442325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5da298e9-bccf-4630-8cf1-c4b21a42dd9b", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957445911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7612dd8-edf1-44f7-b3d9-8bc63f3c7350", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957446065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe2e00d-0a53-40cc-a74d-7459103d7ed5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957432681400, "endTime": 315957450734700}, "additional": {"logType": "info", "children": [], "durationId": "273339e8-4a81-4768-a4eb-bd7fdd9aff2f", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "d1efae0b-29ca-401e-be51-de182202a098", "name": "Configuration phase cost:260 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957450819500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b02cdd-82cd-4403-b20e-a6a0a01ca60d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957450779500, "endTime": 315957450968000}, "additional": {"logType": "info", "children": [], "durationId": "911b31ff-ea29-4858-83fa-d54db6cdb506", "parent": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649"}}, {"head": {"id": "be8e5dfd-7e3e-4a8b-95e5-99b027a39649", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957178585900, "endTime": 315957450985200}, "additional": {"logType": "info", "children": ["491e907a-5cf3-4887-b2e4-dd3d7c718c14", "5febbb5c-a9c6-4e21-bc91-31ffb6dbbae6", "e452fdc6-4f37-49ff-95fd-705dcfd0e502", "bec630b2-0016-43b8-b2c4-aef99d6480b6", "ad18251d-a853-4e84-a0ed-0389055b2546", "f604d127-ba70-418e-8462-69f3498830f4", "5fe2e00d-0a53-40cc-a74d-7459103d7ed5", "46b02cdd-82cd-4403-b20e-a6a0a01ca60d", "3039e7ec-20e9-472e-bf0a-9a051828921c"], "durationId": "9f84e0dc-2ae9-4024-9dee-fb407610675b", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "004cb203-0e73-44b5-bfdc-9de9a781596a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957452842400, "endTime": 315957452863600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8875fc4-4ba4-419b-9242-df80f1000c92", "logId": "c2e2461b-200d-45fb-9dbd-0edb8883eae5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2e2461b-200d-45fb-9dbd-0edb8883eae5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957452842400, "endTime": 315957452863600}, "additional": {"logType": "info", "children": [], "durationId": "004cb203-0e73-44b5-bfdc-9de9a781596a", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "e8062cbe-4608-40ae-9121-db00a457c9cc", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957451013400, "endTime": 315957453264000}, "additional": {"logType": "info", "children": [], "durationId": "084ce0c3-1bd5-4883-a3c4-04340baac460", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "0f7955e0-a3c9-4cbf-9ec4-5839f0d43489", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957453279900, "endTime": 315957453291500}, "additional": {"logType": "info", "children": [], "durationId": "692cd7da-9fc9-4dc6-b258-bbdd41be8c70", "parent": "436b6254-dade-48ae-a89b-445e53496035"}}, {"head": {"id": "436b6254-dade-48ae-a89b-445e53496035", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957152156400, "endTime": 315957453298300}, "additional": {"logType": "info", "children": ["33928ede-8d0b-45bb-afa9-4e665ab8dda9", "be8e5dfd-7e3e-4a8b-95e5-99b027a39649", "e8062cbe-4608-40ae-9121-db00a457c9cc", "0f7955e0-a3c9-4cbf-9ec4-5839f0d43489", "c83700fc-7ce5-45c4-adde-9e4b2ee9c858", "8e913390-a3f8-49d2-8174-435c3c6d263e", "c2e2461b-200d-45fb-9dbd-0edb8883eae5"], "durationId": "f8875fc4-4ba4-419b-9242-df80f1000c92"}}, {"head": {"id": "f3597a10-b9c9-4ed7-8092-d1047e551275", "name": "Configuration task cost before running: 307 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957453459900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da0dbd8-3539-4b71-9794-9a0d4e0b28dd", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957462668700, "endTime": 315957477212200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2bbbf46f-ab55-4b2d-a4bb-35bf161d6a63", "logId": "37e0b159-1e8d-4644-8f34-748fab70d874"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bbbf46f-ab55-4b2d-a4bb-35bf161d6a63", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957455762700}, "additional": {"logType": "detail", "children": [], "durationId": "9da0dbd8-3539-4b71-9794-9a0d4e0b28dd"}}, {"head": {"id": "8a2962f5-0ce7-4d06-8448-ab8da62ea441", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957456759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a0d143-a59b-4777-84ac-f27b32e9d63b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957456976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0afbf9f1-0158-4dc2-9aab-79a10804539d", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957462695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "322b7cef-f7db-414c-925c-e6c39a9f52d4", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957476969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03e5bb2-846b-4385-9e0a-893814f8003b", "name": "entry : default@PreBuild cost memory 0.26880645751953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957477126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e0b159-1e8d-4644-8f34-748fab70d874", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957462668700, "endTime": 315957477212200}, "additional": {"logType": "info", "children": [], "durationId": "9da0dbd8-3539-4b71-9794-9a0d4e0b28dd"}}, {"head": {"id": "c04dacfe-bab0-42ae-a59c-6e9901a3fc59", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957484855700, "endTime": 315957487713500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f3523d46-e3eb-463e-ab0a-86d7ec0b3554", "logId": "4833aa8f-9988-4743-851d-2c017c12d1ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3523d46-e3eb-463e-ab0a-86d7ec0b3554", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957482862400}, "additional": {"logType": "detail", "children": [], "durationId": "c04dacfe-bab0-42ae-a59c-6e9901a3fc59"}}, {"head": {"id": "a5c19bc4-0479-4cac-aa7c-93abd83894e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957483776100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81651474-d1f7-4c08-86db-4120474201d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957483955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58049be3-3106-419a-9e5e-219804c72109", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957484882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764e2125-d02e-4cf7-9402-1ec81efca6ee", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957487452000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218fc6fc-4879-49c2-8b91-706f256bfb3f", "name": "entry : default@MergeProfile cost memory 0.1116180419921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957487634600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4833aa8f-9988-4743-851d-2c017c12d1ed", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957484855700, "endTime": 315957487713500}, "additional": {"logType": "info", "children": [], "durationId": "c04dacfe-bab0-42ae-a59c-6e9901a3fc59"}}, {"head": {"id": "d7ed3030-6518-48af-a28a-fe5a180b44ea", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957493383500, "endTime": 315957497100000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d3e4ffae-1a4f-4c61-b5e3-2928f5f63e07", "logId": "944ccb3e-e8be-458f-a001-784eb2047728"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3e4ffae-1a4f-4c61-b5e3-2928f5f63e07", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957490882800}, "additional": {"logType": "detail", "children": [], "durationId": "d7ed3030-6518-48af-a28a-fe5a180b44ea"}}, {"head": {"id": "3b9515d9-d4b2-4dce-9ed2-a19673506677", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957491670400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38261b28-8d68-4ce0-9544-c0a6961281d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957491853000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3655292b-1b1d-434e-ba3a-508a1ab85638", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957493402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba4c00a-8f6f-43c6-aa2d-cbf31b969167", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957494925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8a2466-c037-45b8-bd89-a2d548c54be6", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957496824700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ea3d25-78d2-4245-8428-e1a7e6617fb8", "name": "entry : default@CreateBuildProfile cost memory 0.09807586669921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957496978900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "944ccb3e-e8be-458f-a001-784eb2047728", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957493383500, "endTime": 315957497100000}, "additional": {"logType": "info", "children": [], "durationId": "d7ed3030-6518-48af-a28a-fe5a180b44ea"}}, {"head": {"id": "a9f73d20-904c-466a-a978-3f7fab273f8e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957501844600, "endTime": 315957502391400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1db7f04e-33d6-46b8-830e-8f81ce4149f3", "logId": "1a9c00c8-422f-4fea-8398-95aab69d807a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1db7f04e-33d6-46b8-830e-8f81ce4149f3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957500039200}, "additional": {"logType": "detail", "children": [], "durationId": "a9f73d20-904c-466a-a978-3f7fab273f8e"}}, {"head": {"id": "f6ddc179-f985-4b34-9d44-ed268a693ca7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957500711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f0c8f8-33c8-45c6-87f9-086f18f79876", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957500836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bee686-6a4e-4d27-912a-e95bb10ba063", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957501858000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430e41d2-265f-447a-be93-384ed03af0da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957502015100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8557b29c-58eb-4556-90b9-d379f085c66c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957502084500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "203851b4-73ca-4380-9063-7d1e60cff11a", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957502189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb96488-7091-4417-8433-0a72e957a1ba", "name": "runTaskFromQueue task cost before running: 356 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957502307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9c00c8-422f-4fea-8398-95aab69d807a", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957501844600, "endTime": 315957502391400, "totalTime": 438100}, "additional": {"logType": "info", "children": [], "durationId": "a9f73d20-904c-466a-a978-3f7fab273f8e"}}, {"head": {"id": "d0e54729-fdd4-4657-9aac-66d61e173cac", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957517347200, "endTime": 315957518600100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "989a7754-60ba-47a2-bb00-67387a4e3200", "logId": "5e546892-7ed7-4861-bca4-3467e27836e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "989a7754-60ba-47a2-bb00-67387a4e3200", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957505603400}, "additional": {"logType": "detail", "children": [], "durationId": "d0e54729-fdd4-4657-9aac-66d61e173cac"}}, {"head": {"id": "905e604f-6743-4ba1-83b2-8c432c0ee10c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957506434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db50b8be-7005-440b-a4ba-faa82e5e19b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957506607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf143d1d-14d5-49d9-bc24-0494452eb124", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957517364700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23f3602-ff6a-479d-a719-5dda8f20eff8", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957517590300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aca9035-38d2-4e11-a576-dd476c5c002e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957518352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac21456-e9bc-4cfe-a852-df3395c0232f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0662994384765625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957518486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e546892-7ed7-4861-bca4-3467e27836e2", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957517347200, "endTime": 315957518600100}, "additional": {"logType": "info", "children": [], "durationId": "d0e54729-fdd4-4657-9aac-66d61e173cac"}}, {"head": {"id": "96138d97-0d71-464e-9c6b-f1cb59ddbb82", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957522680900, "endTime": 315957524042300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2fe5e64a-d4e2-49a3-a5f5-8e1703423f0b", "logId": "e9c875dd-b403-4c4c-9d00-a7a844cb1c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fe5e64a-d4e2-49a3-a5f5-8e1703423f0b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957520837100}, "additional": {"logType": "detail", "children": [], "durationId": "96138d97-0d71-464e-9c6b-f1cb59ddbb82"}}, {"head": {"id": "5edd6bf2-85bd-464a-b439-9a7ef00adcff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957521380100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f644d0b-0fb8-40dd-abc0-9692f873248f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957521489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e50e94-2dc3-4ad0-83d3-0b2a4d83761d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957522690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f4fefe-e78a-4502-8570-f1874adf2bf2", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957523829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942e52a5-bba9-4e9a-a2a7-f5d82a81c209", "name": "entry : default@ProcessProfile cost memory 0.056732177734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957523965900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9c875dd-b403-4c4c-9d00-a7a844cb1c8d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957522680900, "endTime": 315957524042300}, "additional": {"logType": "info", "children": [], "durationId": "96138d97-0d71-464e-9c6b-f1cb59ddbb82"}}, {"head": {"id": "7b0b0ca2-0a87-4ac9-a53a-a3945725a3c7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957528678700, "endTime": 315957535020800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d317eb39-73d6-4a44-9ed0-503ba75db9e2", "logId": "411bc2bb-b70c-4a59-970e-5652f3303857"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d317eb39-73d6-4a44-9ed0-503ba75db9e2", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957525864000}, "additional": {"logType": "detail", "children": [], "durationId": "7b0b0ca2-0a87-4ac9-a53a-a3945725a3c7"}}, {"head": {"id": "b61c4352-18bf-4c5a-b85f-e246918305d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957526432600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20ddd5d-2e69-4a2f-b1d6-57c3eb8d83af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957526553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973f4f62-9d09-403b-b661-cd3c0805d8f0", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957528692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f5c080-4d60-4fda-ac7f-329a9f40ac67", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957534817000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aeebc6c-8adf-4c57-91c5-a709fa7f0e16", "name": "entry : default@ProcessRouterMap cost memory 0.18952178955078125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957534946600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411bc2bb-b70c-4a59-970e-5652f3303857", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957528678700, "endTime": 315957535020800}, "additional": {"logType": "info", "children": [], "durationId": "7b0b0ca2-0a87-4ac9-a53a-a3945725a3c7"}}, {"head": {"id": "bbecc5e5-e41e-4175-897e-bcdde96f7c38", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957544192400, "endTime": 315957547792200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "001fab91-4bb5-4962-b673-a4d6f3ebabbf", "logId": "12b77020-4657-486b-8022-05c86ce854fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "001fab91-4bb5-4962-b673-a4d6f3ebabbf", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957538414800}, "additional": {"logType": "detail", "children": [], "durationId": "bbecc5e5-e41e-4175-897e-bcdde96f7c38"}}, {"head": {"id": "8c3be7c7-4448-4404-858a-0654d403038f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957539096600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c6b2f64-effc-49ac-94a8-c149b0b1e23c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957539227900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9961a3cf-f8a1-4739-9cf2-adf0fefcc215", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957540878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211cd24e-2bfc-4f34-90ed-01946f53f02b", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957545660600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b40d2e-244f-4085-8657-6dd9a64e4d0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957545856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64081845-73fb-4858-8787-047d79013236", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957545933900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7154f821-1dca-4560-9d63-dfe361b3a0ab", "name": "entry : default@PreviewProcessResource cost memory 0.06855010986328125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957546033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de10ace-1df6-41eb-80ac-9a165b5c0976", "name": "runTaskFromQueue task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957547638000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12b77020-4657-486b-8022-05c86ce854fd", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957544192400, "endTime": 315957547792200, "totalTime": 1914300}, "additional": {"logType": "info", "children": [], "durationId": "bbecc5e5-e41e-4175-897e-bcdde96f7c38"}}, {"head": {"id": "e147907c-832a-425b-a378-b9ca030032c0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957556176000, "endTime": 315957589189700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ae267d54-03d1-4fe3-a0cc-d3bdd6f11fc2", "logId": "7e88dc2c-627f-4c05-823a-103a19693617"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae267d54-03d1-4fe3-a0cc-d3bdd6f11fc2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957551175300}, "additional": {"logType": "detail", "children": [], "durationId": "e147907c-832a-425b-a378-b9ca030032c0"}}, {"head": {"id": "26e3311b-7c59-4c43-a4b4-01bd8196c05f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957551869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d15d7f-0324-4ea1-9a95-e11202e33cd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957551996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e00ec19-8942-45a9-8f24-011292ea5774", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957556200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e439f5-927a-4a08-a660-7deed3893012", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957588798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a2c1bac-1c81-4398-b515-68e26fce9a40", "name": "entry : default@GenerateLoaderJson cost memory -0.9506072998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957589027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e88dc2c-627f-4c05-823a-103a19693617", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957556176000, "endTime": 315957589189700}, "additional": {"logType": "info", "children": [], "durationId": "e147907c-832a-425b-a378-b9ca030032c0"}}, {"head": {"id": "66d8d624-430b-42f8-b660-41aa511823e3", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957614620700, "endTime": 315957645670400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0821bdeb-5dd0-48c1-a41c-de5cdcc089cc", "logId": "093836dc-cd6b-4b96-8c1c-a8de98bb39d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0821bdeb-5dd0-48c1-a41c-de5cdcc089cc", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957605696000}, "additional": {"logType": "detail", "children": [], "durationId": "66d8d624-430b-42f8-b660-41aa511823e3"}}, {"head": {"id": "07f31a40-d8b2-44df-a0c8-eab0d73bd561", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957606769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388d229a-e8a6-485d-a0ac-e70a1a24e31a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957607029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ff9fda-f740-4def-b40d-30f5ce110957", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957609405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ff3acf-7147-44d8-ba7d-896e2945650a", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957614655100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca647802-ac55-420f-99aa-baf01dec595d", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957645436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072aa68d-798f-4917-bbd8-67a5475a0654", "name": "entry : default@PreviewCompileResource cost memory -1.0441665649414062", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957645579300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "093836dc-cd6b-4b96-8c1c-a8de98bb39d6", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957614620700, "endTime": 315957645670400}, "additional": {"logType": "info", "children": [], "durationId": "66d8d624-430b-42f8-b660-41aa511823e3"}}, {"head": {"id": "0f75d093-e23a-44d0-a73e-590b01d34cf6", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650250500, "endTime": 315957650940800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b866d624-febe-41e8-a01e-6f3a2afd3cfe", "logId": "bdc53506-b2ee-4fc3-829e-71257efe8260"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b866d624-febe-41e8-a01e-6f3a2afd3cfe", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957649237600}, "additional": {"logType": "detail", "children": [], "durationId": "0f75d093-e23a-44d0-a73e-590b01d34cf6"}}, {"head": {"id": "8ef28927-4304-44ef-ae52-efbc060f29f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957649967300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78293217-fadb-4f07-91f9-4825db468421", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650113100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e0f9c4-3641-42a3-af5a-5c291a8135c7", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650262400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93c2ed09-3795-454f-a4e8-75fe8f7511a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650413200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de660d48-4296-4212-9039-c08a08f8fbc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650563800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dfcbca0-468c-49f7-a52c-93c8bc083181", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea95c93e-09f5-444b-89bc-6556661933f7", "name": "runTaskFromQueue task cost before running: 504 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc53506-b2ee-4fc3-829e-71257efe8260", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957650250500, "endTime": 315957650940800, "totalTime": 555600}, "additional": {"logType": "info", "children": [], "durationId": "0f75d093-e23a-44d0-a73e-590b01d34cf6"}}, {"head": {"id": "beb089c5-bb3b-4377-ae8b-04d4307cc5a2", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957655058800, "endTime": 315957657752100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1bceb916-1540-48b4-9283-01979d20f402", "logId": "8ba2cfcf-45d3-4345-a110-bf4cf2c1fa7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bceb916-1540-48b4-9283-01979d20f402", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957653515600}, "additional": {"logType": "detail", "children": [], "durationId": "beb089c5-bb3b-4377-ae8b-04d4307cc5a2"}}, {"head": {"id": "3c36f21c-6f64-42dc-902c-63f45bebb53b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957654249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b7f61e-0f36-404a-a603-7357d215d8c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957654364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e4c1a0-7e14-4e9e-97b3-0fe1cbc7c7ef", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957655069700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f61b314-dd89-4ae3-8179-8b4c20f1a408", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957657554200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c291ffe5-c544-4c86-8be1-e6c2228ee41e", "name": "entry : default@CopyPreviewProfile cost memory 0.0952606201171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957657669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba2cfcf-45d3-4345-a110-bf4cf2c1fa7f", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957655058800, "endTime": 315957657752100}, "additional": {"logType": "info", "children": [], "durationId": "beb089c5-bb3b-4377-ae8b-04d4307cc5a2"}}, {"head": {"id": "9fdb6195-0a24-4009-9ba4-531b3802e9b5", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661492600, "endTime": 315957661921400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "6893768b-4265-42a4-9574-2ea9ed913379", "logId": "cce935a9-4afd-4771-8bed-8d6796b1c1d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6893768b-4265-42a4-9574-2ea9ed913379", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957660015100}, "additional": {"logType": "detail", "children": [], "durationId": "9fdb6195-0a24-4009-9ba4-531b3802e9b5"}}, {"head": {"id": "ca633697-0901-4472-a482-d63f3c9de209", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957660573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36aedcac-244a-4385-b18f-f846f8f02bc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957660679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d27d496b-9582-48c7-92d6-fc428ab01687", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abaaa94c-54d6-4d50-be63-28c1c5fb7b03", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c43804-fcf1-4c06-bb15-c2feb2c4eeae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661678000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea950b9-1022-4507-bfd2-da8df54f2cd9", "name": "entry : default@ReplacePreviewerPage cost memory 0.03858184814453125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4d61c2-437c-40b9-b34e-d6e3db77cf1d", "name": "runTaskFromQueue task cost before running: 515 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661854000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce935a9-4afd-4771-8bed-8d6796b1c1d7", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957661492600, "endTime": 315957661921400, "totalTime": 342200}, "additional": {"logType": "info", "children": [], "durationId": "9fdb6195-0a24-4009-9ba4-531b3802e9b5"}}, {"head": {"id": "491561be-8178-41cd-a7cc-485d4c693bbe", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957663842700, "endTime": 315957664206800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8cf8b0cc-2e54-4fc8-a364-a533b0da8c85", "logId": "8e4d62a4-0ecf-4cf8-81ac-6b5284421084"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cf8b0cc-2e54-4fc8-a364-a533b0da8c85", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957663782500}, "additional": {"logType": "detail", "children": [], "durationId": "491561be-8178-41cd-a7cc-485d4c693bbe"}}, {"head": {"id": "353bb547-8526-46ae-9060-cc43b8a8b6fe", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957663851900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad7daa8-b70e-4905-94d5-40d7d9a731e8", "name": "entry : buildPreviewerResource cost memory 0.181427001953125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957664032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "904f66bf-5ceb-4d73-ac3c-9a9a46f8ce0a", "name": "runTaskFromQueue task cost before running: 518 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957664135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e4d62a4-0ecf-4cf8-81ac-6b5284421084", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957663842700, "endTime": 315957664206800, "totalTime": 266700}, "additional": {"logType": "info", "children": [], "durationId": "491561be-8178-41cd-a7cc-485d4c693bbe"}}, {"head": {"id": "0f4de4d5-8ee7-4a0f-a1c1-222f0ae9fd87", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957670694700, "endTime": 315957675510300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4b0a6af6-b779-4cc3-88d4-a4cdd171b005", "logId": "b4588efc-8e13-4e1b-bc95-1ff38b167e9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b0a6af6-b779-4cc3-88d4-a4cdd171b005", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957667744700}, "additional": {"logType": "detail", "children": [], "durationId": "0f4de4d5-8ee7-4a0f-a1c1-222f0ae9fd87"}}, {"head": {"id": "c008d163-4842-4f0d-a9fe-a9fda7c0b413", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957668597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7f616e-845e-4e45-a09c-78194617a283", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957668971100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc77eabe-c511-4a6f-a0ba-eef9613b870e", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957670713300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a060ba-1222-4a6d-bee6-e76842d1dc4c", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957675004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189be952-2fb4-476c-97fe-d4e23a0b8923", "name": "entry : default@PreviewUpdateAssets cost memory 0.10277557373046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957675193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4588efc-8e13-4e1b-bc95-1ff38b167e9a", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957670694700, "endTime": 315957675510300}, "additional": {"logType": "info", "children": [], "durationId": "0f4de4d5-8ee7-4a0f-a1c1-222f0ae9fd87"}}, {"head": {"id": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957703801200, "endTime": 315976907713700}, "additional": {"children": ["73ec98b1-b6f7-4e66-a000-99622dc8b45e"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3f753d05-04aa-4d25-83e7-ddff171dc19e", "logId": "0b6f56f1-0687-4b56-82d6-4b5622108ee7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f753d05-04aa-4d25-83e7-ddff171dc19e", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957679747500}, "additional": {"logType": "detail", "children": [], "durationId": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9"}}, {"head": {"id": "87b00bcf-0543-4827-ab37-3a94351f9a71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957680642600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02cff34-2771-4606-9257-e38816f673ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957680805700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "794a68b9-dd6b-4b8c-843c-5161f35205e9", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957703824900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker8", "startTime": 315957734761100, "endTime": 315976907006000}, "additional": {"children": ["ee493c37-8308-4b78-a7b9-9597361add7b", "2183b61f-e9a8-4646-945a-fcfed32f18ff", "c776e2a4-b74e-439c-93cd-4971872b6def", "5a5bd2b8-b121-486a-bbb1-dadd965d6b91", "c65547ef-ef7b-40f5-a416-c5daae4020cc", "b98768d8-c22f-4b50-8b5a-cade168f8b46", "4823b90f-db8a-43f7-b703-5547df3309d6", "88062995-4671-4061-af9d-2dcceb46e77c", "6693a687-b02a-44d0-b5ed-3238c6bfaf0c", "09da6647-8351-45f9-af4b-e9d8fb203006", "064f80a0-0ea5-4719-ab7d-1bd5d7f5ab7c", "253ec710-e438-4749-a6fc-5f5be4184a7c", "4a8bf410-720f-404f-9ebf-73da85f81a74", "bc028328-69e0-4d72-94ab-3f56f96a9528", "c61ecd6e-737f-471b-9712-ed71a7c33cdf", "acd80583-d54f-42ea-8d40-191aa829d2c2", "3b250623-3724-46b8-8941-98f4d8887cc3", "8f17dc97-1b77-4618-9193-9caab182a574", "1f5612e4-4f4e-482b-8e3b-df4413d456fe", "e8e8ea9f-24e4-422b-8d36-e98964871787", "0e90e574-5192-47ca-92c3-66d9ca6699ec", "23561ba4-5354-4b0b-999e-7e10df8cc2f2", "d6a1ef3d-b814-4cc9-9cdd-ff987ba8972c", "5cb9d8ff-9b37-4aef-b7b4-33393cae1aac", "6f34e5cf-e1d9-4d82-a7e7-643e1b24fe35", "bb5b80af-3902-45e4-942e-0f50d02685af", "5582d91b-bc2a-46e5-a5e9-53945fd7f39c", "b193f827-2fff-409c-81f9-765a9f32b123", "e8974135-5ea9-40a7-87b4-54cdbd32280b", "09907dec-e7f8-4958-af73-317ead52b76b", "3923ac34-dd7d-4ce4-9132-0716aa92b33b", "367275dc-5f6b-4483-9d1f-1eee42025398", "5b531520-4ded-42da-86d1-de4ebd1b05da", "eedabc35-23a6-46bd-ad59-e169f14dcf33", "e6b9f4ed-bce1-4549-8dff-ee1570b5e7e7", "b262aea4-5fb6-44b9-9bff-5d88e506c475", "d4537c65-0c42-412f-9769-e6c2e20f50b2", "aa272782-baae-4c21-bc14-737f4d533fde", "92624444-8d51-4751-a3a3-06a1bdeb89d4", "ba12e4fc-2c5f-4b28-8a52-f4c1a51146d4", "14960a30-90aa-4aa7-8008-5dfdf0375540", "dbddb28a-204a-4f7b-a676-8f3d73914314", "aa2f2f89-9aa2-45b6-b9b5-c85eae808ca8", "dfd4c9e3-d94b-4afd-9397-b256cfc534ee", "53d75143-bfe3-4a04-b8f0-819626df2faa", "5c52cbda-25ce-4fca-9c0c-2187a5a3b10a", "b7cbba4a-cf24-4ebc-9858-6513fc5db7dc", "94a3ccd9-8b82-465a-b4a8-7d6a0187ebbe", "a9a78162-779d-4243-a366-d6b5952d5b36", "83b62780-4286-4719-b832-66b53780ab1d", "2e55503b-8054-4db1-8d97-bf3d8f27c949", "1526790e-d7ee-43c0-b73d-97e3c867f9ab", "c1190e19-dd7e-4120-8df6-e5df968cfd6b", "c22387fb-a3dd-46fc-b316-f5e9d361054c", "0dd95d84-29f2-4308-b39c-fbe6c0e602b3", "8c5b7c73-f08c-484d-94e8-af6a15ee819c", "61a9cedf-0718-4d3f-a64e-584c010b10bf", "475e5e22-8a59-48b8-95ea-62f3d83610a0", "19674709-4614-440b-89e8-51e28b775bc0", "fec29be5-20ac-41a4-8180-b8478fd039cc", "985e77c4-315a-4d18-b6b1-41e2c27aacf5", "7826c330-4c7d-43a7-adeb-3c45ac092a70", "4138f566-8b05-4d55-84bf-6c5ceb2a00db", "3f8b9cef-7cbb-4ced-834d-a9a5059e00d1", "15dec02d-8f43-4fec-9e1c-f5b2d01699d1", "c3c1647c-0950-4bce-8091-33aac8250621", "5e1f5ce6-d001-453a-9173-6bac62024c6a", "1b822db5-5d3e-4101-af11-be96e1c3b7c6", "18309bb6-f781-4113-98ce-3eec240281cf", "a9a549bd-d05b-44d9-94f3-dcfc1349628f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9", "logId": "de60da5a-b535-482f-b4e0-f6716133562a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97b3c641-9081-4bbf-b47f-3ad098bf69a4", "name": "entry : default@PreviewArkTS cost memory 1.1741180419921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957737459300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b858967-3eba-4f9e-837f-353f22ffac34", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315965701999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee493c37-8308-4b78-a7b9-9597361add7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315965703047600, "endTime": 315965703067200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "27224a0d-87bb-4821-8c67-42353672cd9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27224a0d-87bb-4821-8c67-42353672cd9d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315965703047600, "endTime": 315965703067200}, "additional": {"logType": "info", "children": [], "durationId": "ee493c37-8308-4b78-a7b9-9597361add7b", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "1f38345f-248e-41a8-a720-f2e5ed503fda", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976110145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2183b61f-e9a8-4646-945a-fcfed32f18ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976112863400, "endTime": 315976112915600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "132636f0-25a0-4bea-846e-c3c460762a66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "132636f0-25a0-4bea-846e-c3c460762a66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976112863400, "endTime": 315976112915600}, "additional": {"logType": "info", "children": [], "durationId": "2183b61f-e9a8-4646-945a-fcfed32f18ff", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "5256a8f3-45aa-4a6a-b78b-1579b339442e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976113156300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c776e2a4-b74e-439c-93cd-4971872b6def", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976115964700, "endTime": 315976116016200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "cf96444c-7e20-4f59-a05e-27031eccb01b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf96444c-7e20-4f59-a05e-27031eccb01b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976115964700, "endTime": 315976116016200}, "additional": {"logType": "info", "children": [], "durationId": "c776e2a4-b74e-439c-93cd-4971872b6def", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "ab446db6-78e7-4f17-a552-827f308ee1c1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976116287200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a5bd2b8-b121-486a-bbb1-dadd965d6b91", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976119555200, "endTime": 315976119608300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e53f7e68-ce49-41af-b50a-5ae64f835b61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e53f7e68-ce49-41af-b50a-5ae64f835b61", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976119555200, "endTime": 315976119608300}, "additional": {"logType": "info", "children": [], "durationId": "5a5bd2b8-b121-486a-bbb1-dadd965d6b91", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "adc62128-de41-46bc-9703-123e330a93f2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976119865500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65547ef-ef7b-40f5-a416-c5daae4020cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976123359300, "endTime": 315976123408600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "f4a0c8eb-0410-41ea-9f29-ebd0fae8e256"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4a0c8eb-0410-41ea-9f29-ebd0fae8e256", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976123359300, "endTime": 315976123408600}, "additional": {"logType": "info", "children": [], "durationId": "c65547ef-ef7b-40f5-a416-c5daae4020cc", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "4a5b59b5-7f4d-4270-a2ed-74ea8361941b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976123646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98768d8-c22f-4b50-8b5a-cade168f8b46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976126595700, "endTime": 315976126647000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "4135c963-af41-4a7c-ac4e-48fbc5f393a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4135c963-af41-4a7c-ac4e-48fbc5f393a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976126595700, "endTime": 315976126647000}, "additional": {"logType": "info", "children": [], "durationId": "b98768d8-c22f-4b50-8b5a-cade168f8b46", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "5d018ed2-bc7c-4974-a094-d9e125674337", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976126960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4823b90f-db8a-43f7-b703-5547df3309d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976130052200, "endTime": 315976130155500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e7d0296f-1bac-43fa-8fb0-15f85cd578dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7d0296f-1bac-43fa-8fb0-15f85cd578dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976130052200, "endTime": 315976130155500}, "additional": {"logType": "info", "children": [], "durationId": "4823b90f-db8a-43f7-b703-5547df3309d6", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "529048b2-d0b6-4722-9e8f-39e78402ff05", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976130444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88062995-4671-4061-af9d-2dcceb46e77c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976133999000, "endTime": 315976134081500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "70fcc6c7-3512-4c6e-b6f7-d9079680d51b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70fcc6c7-3512-4c6e-b6f7-d9079680d51b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976133999000, "endTime": 315976134081500}, "additional": {"logType": "info", "children": [], "durationId": "88062995-4671-4061-af9d-2dcceb46e77c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "41a2dbe9-5206-44e3-b7ac-30c583c8b3a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976134430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6693a687-b02a-44d0-b5ed-3238c6bfaf0c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976138035800, "endTime": 315976138086900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "8d538f29-e464-4474-b5e1-53366352944f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d538f29-e464-4474-b5e1-53366352944f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976138035800, "endTime": 315976138086900}, "additional": {"logType": "info", "children": [], "durationId": "6693a687-b02a-44d0-b5ed-3238c6bfaf0c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "ee530ffc-2323-4351-9d39-1d60c250e560", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976138320900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09da6647-8351-45f9-af4b-e9d8fb203006", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976148401900, "endTime": 315976148480900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "a439e0d0-18c1-48ef-a9e2-e7214db09283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a439e0d0-18c1-48ef-a9e2-e7214db09283", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976148401900, "endTime": 315976148480900}, "additional": {"logType": "info", "children": [], "durationId": "09da6647-8351-45f9-af4b-e9d8fb203006", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "c4f772f2-0413-47a1-9b7e-b0f85515ee8c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976149225200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064f80a0-0ea5-4719-ab7d-1bd5d7f5ab7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976165779600, "endTime": 315976165834500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "f2241f60-1883-4985-abd9-38bbf8a641ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2241f60-1883-4985-abd9-38bbf8a641ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976165779600, "endTime": 315976165834500}, "additional": {"logType": "info", "children": [], "durationId": "064f80a0-0ea5-4719-ab7d-1bd5d7f5ab7c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "43b14c57-b728-4bad-bf44-ce08af1d1388", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976168199900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253ec710-e438-4749-a6fc-5f5be4184a7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976174422500, "endTime": 315976174476600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e4d8dc67-0ae8-4454-8fc2-4db6a13c0679"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4d8dc67-0ae8-4454-8fc2-4db6a13c0679", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976174422500, "endTime": 315976174476600}, "additional": {"logType": "info", "children": [], "durationId": "253ec710-e438-4749-a6fc-5f5be4184a7c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "353f3d08-a7b2-49ac-8e61-211d0f8933c0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976176521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8bf410-720f-404f-9ebf-73da85f81a74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976181802800, "endTime": 315976181857300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "72b56ee5-e5d8-4282-a4c7-f25916165385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72b56ee5-e5d8-4282-a4c7-f25916165385", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976181802800, "endTime": 315976181857300}, "additional": {"logType": "info", "children": [], "durationId": "4a8bf410-720f-404f-9ebf-73da85f81a74", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "7b5f448b-3876-490e-b69c-9b4f867cb5a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976182156500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc028328-69e0-4d72-94ab-3f56f96a9528", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976187527800, "endTime": 315976187607600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "dd0167b6-f509-44b8-9fec-b4b280845260"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd0167b6-f509-44b8-9fec-b4b280845260", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976187527800, "endTime": 315976187607600}, "additional": {"logType": "info", "children": [], "durationId": "bc028328-69e0-4d72-94ab-3f56f96a9528", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "1dc411ad-23a9-4ba3-86ed-5f5a21cc0db1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976187865200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c61ecd6e-737f-471b-9712-ed71a7c33cdf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976193614600, "endTime": 315976193659400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "754d5725-ab5b-4671-967b-060f31f275b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "754d5725-ab5b-4671-967b-060f31f275b9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976193614600, "endTime": 315976193659400}, "additional": {"logType": "info", "children": [], "durationId": "c61ecd6e-737f-471b-9712-ed71a7c33cdf", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "bc75b967-a2c7-4b8b-9c11-976a3564b447", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976193898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd80583-d54f-42ea-8d40-191aa829d2c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976196686300, "endTime": 315976196730500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "cf0039b2-8d88-42b8-92ff-b2f5886848a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf0039b2-8d88-42b8-92ff-b2f5886848a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976196686300, "endTime": 315976196730500}, "additional": {"logType": "info", "children": [], "durationId": "acd80583-d54f-42ea-8d40-191aa829d2c2", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "ef4aa797-feb6-43b9-a509-33656ed1aa93", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976196967000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b250623-3724-46b8-8941-98f4d8887cc3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976199775700, "endTime": 315976199829700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e82e9a1c-a0b8-432c-8efc-285cce6f8cb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e82e9a1c-a0b8-432c-8efc-285cce6f8cb5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976199775700, "endTime": 315976199829700}, "additional": {"logType": "info", "children": [], "durationId": "3b250623-3724-46b8-8941-98f4d8887cc3", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "12dd7699-74a3-425c-bb09-63876a0a4caf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976200066000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f17dc97-1b77-4618-9193-9caab182a574", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976203058200, "endTime": 315976203107200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "3956238e-f4ae-41f3-bc7c-c39db35a5349"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3956238e-f4ae-41f3-bc7c-c39db35a5349", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976203058200, "endTime": 315976203107200}, "additional": {"logType": "info", "children": [], "durationId": "8f17dc97-1b77-4618-9193-9caab182a574", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "8f9e5a77-3d27-4baf-9e9b-54c9c94a1762", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976203421800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5612e4-4f4e-482b-8e3b-df4413d456fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976208227400, "endTime": 315976208287800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "b7bc84f9-fc81-4b26-b7ee-8f6f86b6d15b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7bc84f9-fc81-4b26-b7ee-8f6f86b6d15b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976208227400, "endTime": 315976208287800}, "additional": {"logType": "info", "children": [], "durationId": "1f5612e4-4f4e-482b-8e3b-df4413d456fe", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "f3e22182-f936-42b0-b525-88040c8f093e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976208945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8e8ea9f-24e4-422b-8d36-e98964871787", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976212069800, "endTime": 315976212121500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "71c651b1-c2ab-4a90-8246-65f1270a6be7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71c651b1-c2ab-4a90-8246-65f1270a6be7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976212069800, "endTime": 315976212121500}, "additional": {"logType": "info", "children": [], "durationId": "e8e8ea9f-24e4-422b-8d36-e98964871787", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "97af78b6-6369-4ba3-b1ed-0c684040e11a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976212409800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e90e574-5192-47ca-92c3-66d9ca6699ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976215502200, "endTime": 315976215549000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "49c3f190-ae79-4558-9998-29dd1d555203"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49c3f190-ae79-4558-9998-29dd1d555203", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976215502200, "endTime": 315976215549000}, "additional": {"logType": "info", "children": [], "durationId": "0e90e574-5192-47ca-92c3-66d9ca6699ec", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "493e4e9b-6d68-476e-a0f4-bc5f31f2b170", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976215862100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23561ba4-5354-4b0b-999e-7e10df8cc2f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976219428000, "endTime": 315976219476700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e1e75aff-aa88-4c6e-9d0b-57bdeb15823f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1e75aff-aa88-4c6e-9d0b-57bdeb15823f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976219428000, "endTime": 315976219476700}, "additional": {"logType": "info", "children": [], "durationId": "23561ba4-5354-4b0b-999e-7e10df8cc2f2", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "3faedc6b-a3d4-470d-a395-d0c1e6ed0106", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976219829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a1ef3d-b814-4cc9-9cdd-ff987ba8972c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976223425700, "endTime": 315976223476100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "0be9cc26-be51-4aec-a65b-b35890a1d498"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0be9cc26-be51-4aec-a65b-b35890a1d498", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976223425700, "endTime": 315976223476100}, "additional": {"logType": "info", "children": [], "durationId": "d6a1ef3d-b814-4cc9-9cdd-ff987ba8972c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "bb85eaf2-ffb4-485c-9b93-8ec4f781ad5f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976223763200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cb9d8ff-9b37-4aef-b7b4-33393cae1aac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976229654600, "endTime": 315976229701900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "43cd5628-5954-4e56-90a3-0adebd8f2603"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43cd5628-5954-4e56-90a3-0adebd8f2603", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976229654600, "endTime": 315976229701900}, "additional": {"logType": "info", "children": [], "durationId": "5cb9d8ff-9b37-4aef-b7b4-33393cae1aac", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "40ae0f85-5922-42ce-85c8-08aa947acb53", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976229967300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f34e5cf-e1d9-4d82-a7e7-643e1b24fe35", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976232943900, "endTime": 315976232987300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "cb9b7e44-d177-42ae-8dd7-5af218986192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb9b7e44-d177-42ae-8dd7-5af218986192", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976232943900, "endTime": 315976232987300}, "additional": {"logType": "info", "children": [], "durationId": "6f34e5cf-e1d9-4d82-a7e7-643e1b24fe35", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "2777a482-eb78-4f8a-8af4-1760c0d5f3e2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976233208700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb5b80af-3902-45e4-942e-0f50d02685af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976236464100, "endTime": 315976236512100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "9d263dac-2f10-48d7-88e1-60f8923e3f9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d263dac-2f10-48d7-88e1-60f8923e3f9e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976236464100, "endTime": 315976236512100}, "additional": {"logType": "info", "children": [], "durationId": "bb5b80af-3902-45e4-942e-0f50d02685af", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "1a16ac64-3b1c-4533-a2cd-31bdb7e8c38f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976237209800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5582d91b-bc2a-46e5-a5e9-53945fd7f39c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976240957700, "endTime": 315976241009100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "69888fff-ca05-43d0-b8ed-8b5eb28f24bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69888fff-ca05-43d0-b8ed-8b5eb28f24bb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976240957700, "endTime": 315976241009100}, "additional": {"logType": "info", "children": [], "durationId": "5582d91b-bc2a-46e5-a5e9-53945fd7f39c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "eb304050-284d-4be1-a9af-404de3a54a2a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976241252000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b193f827-2fff-409c-81f9-765a9f32b123", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976247874300, "endTime": 315976247924900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "deba947e-43db-4c72-a533-7041b974f523"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deba947e-43db-4c72-a533-7041b974f523", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976247874300, "endTime": 315976247924900}, "additional": {"logType": "info", "children": [], "durationId": "b193f827-2fff-409c-81f9-765a9f32b123", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "31d69a1f-83dc-46ac-a6ab-ea0d84f75310", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976248166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8974135-5ea9-40a7-87b4-54cdbd32280b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976251809900, "endTime": 315976251859000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "f1682ca2-44f9-43c3-8d25-ad9a1a2b494b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1682ca2-44f9-43c3-8d25-ad9a1a2b494b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976251809900, "endTime": 315976251859000}, "additional": {"logType": "info", "children": [], "durationId": "e8974135-5ea9-40a7-87b4-54cdbd32280b", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "0cff4dcf-e8cc-4375-87b2-8e5738be50d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976253698900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09907dec-e7f8-4958-af73-317ead52b76b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976259501100, "endTime": 315976259561700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "c3e77b94-995c-4dc8-82fd-e3c3314f5998"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3e77b94-995c-4dc8-82fd-e3c3314f5998", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976259501100, "endTime": 315976259561700}, "additional": {"logType": "info", "children": [], "durationId": "09907dec-e7f8-4958-af73-317ead52b76b", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "a6c78e73-451f-467a-a451-d64a9f2ac422", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976261628200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3923ac34-dd7d-4ce4-9132-0716aa92b33b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976269192600, "endTime": 315976269253300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "52675145-bdc7-41ef-b7fe-ceb2e682905a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52675145-bdc7-41ef-b7fe-ceb2e682905a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976269192600, "endTime": 315976269253300}, "additional": {"logType": "info", "children": [], "durationId": "3923ac34-dd7d-4ce4-9132-0716aa92b33b", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "a7ee8d6e-d9b8-4457-a863-3dc4b9ae33dc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976269531800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367275dc-5f6b-4483-9d1f-1eee42025398", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976275005800, "endTime": 315976275061000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "05cbee55-7800-44b3-bf3f-58aecd9511f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05cbee55-7800-44b3-bf3f-58aecd9511f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976275005800, "endTime": 315976275061000}, "additional": {"logType": "info", "children": [], "durationId": "367275dc-5f6b-4483-9d1f-1eee42025398", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "aeb3e00b-f51a-4b4d-a12c-eef74b293cbe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976275640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b531520-4ded-42da-86d1-de4ebd1b05da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976279606100, "endTime": 315976279655600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "bb2fbc83-741d-4749-b017-7e8729867a2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb2fbc83-741d-4749-b017-7e8729867a2a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976279606100, "endTime": 315976279655600}, "additional": {"logType": "info", "children": [], "durationId": "5b531520-4ded-42da-86d1-de4ebd1b05da", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "dbd044b6-35ee-4997-8916-2b33f8b27a33", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976279929800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eedabc35-23a6-46bd-ad59-e169f14dcf33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976282845900, "endTime": 315976282894000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "211d7881-47cb-4265-b82f-a03c94e46bc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "211d7881-47cb-4265-b82f-a03c94e46bc1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976282845900, "endTime": 315976282894000}, "additional": {"logType": "info", "children": [], "durationId": "eedabc35-23a6-46bd-ad59-e169f14dcf33", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "ddeef9c5-cadb-4ebe-a1ed-954c60f6fe6a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976283141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b9f4ed-bce1-4549-8dff-ee1570b5e7e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976290974500, "endTime": 315976291028200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "82a0db3d-93b6-4095-8ce7-8670d1c41958"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82a0db3d-93b6-4095-8ce7-8670d1c41958", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976290974500, "endTime": 315976291028200}, "additional": {"logType": "info", "children": [], "durationId": "e6b9f4ed-bce1-4549-8dff-ee1570b5e7e7", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "17e7d71d-555d-41bc-af9e-0522f24f5d47", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976293220400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b262aea4-5fb6-44b9-9bff-5d88e506c475", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976296614400, "endTime": 315976296712300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "99c581e9-0b83-4190-91ec-98edd5f2ade2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99c581e9-0b83-4190-91ec-98edd5f2ade2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976296614400, "endTime": 315976296712300}, "additional": {"logType": "info", "children": [], "durationId": "b262aea4-5fb6-44b9-9bff-5d88e506c475", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "03318457-bb57-4112-9d66-127481486b97", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976296959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4537c65-0c42-412f-9769-e6c2e20f50b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976299727100, "endTime": 315976299794900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "ef7fafa7-b94c-4b21-8819-20342aa29222"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef7fafa7-b94c-4b21-8819-20342aa29222", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976299727100, "endTime": 315976299794900}, "additional": {"logType": "info", "children": [], "durationId": "d4537c65-0c42-412f-9769-e6c2e20f50b2", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "fe29e898-d3ff-4760-8797-924941a6834c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976300024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa272782-baae-4c21-bc14-737f4d533fde", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976303497300, "endTime": 315976303547300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e0805f94-d203-476b-8c21-37295656b87f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0805f94-d203-476b-8c21-37295656b87f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976303497300, "endTime": 315976303547300}, "additional": {"logType": "info", "children": [], "durationId": "aa272782-baae-4c21-bc14-737f4d533fde", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "f4cb81f7-0246-4a98-9dc0-5241b930b379", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976303998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92624444-8d51-4751-a3a3-06a1bdeb89d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976309531000, "endTime": 315976309580200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "d601322e-6789-4604-8aa3-4ce21a4bf9ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d601322e-6789-4604-8aa3-4ce21a4bf9ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976309531000, "endTime": 315976309580200}, "additional": {"logType": "info", "children": [], "durationId": "92624444-8d51-4751-a3a3-06a1bdeb89d4", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "e6ce01cb-a6b4-47b3-8423-9d4772ce7259", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976309860600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba12e4fc-2c5f-4b28-8a52-f4c1a51146d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976313109800, "endTime": 315976313152500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "cb55c25b-3bcc-4344-82fa-769caca63b37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb55c25b-3bcc-4344-82fa-769caca63b37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976313109800, "endTime": 315976313152500}, "additional": {"logType": "info", "children": [], "durationId": "ba12e4fc-2c5f-4b28-8a52-f4c1a51146d4", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "0a03aca5-6382-4ce7-9358-f55eda0af2c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976313371500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14960a30-90aa-4aa7-8008-5dfdf0375540", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976316264400, "endTime": 315976316308100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "5e0910ce-e312-4f42-a5f2-acfcb11b6c9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e0910ce-e312-4f42-a5f2-acfcb11b6c9b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976316264400, "endTime": 315976316308100}, "additional": {"logType": "info", "children": [], "durationId": "14960a30-90aa-4aa7-8008-5dfdf0375540", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "2eccd032-595c-4a5e-a4e3-974424d82b3e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976316536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbddb28a-204a-4f7b-a676-8f3d73914314", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976320061100, "endTime": 315976320110100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "2ceab38c-8278-465a-a6c0-57b102c66276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ceab38c-8278-465a-a6c0-57b102c66276", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976320061100, "endTime": 315976320110100}, "additional": {"logType": "info", "children": [], "durationId": "dbddb28a-204a-4f7b-a676-8f3d73914314", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "3de3ae35-c188-46b8-a010-3725fec64938", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976320386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2f2f89-9aa2-45b6-b9b5-c85eae808ca8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976326793100, "endTime": 315976326850100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "dff187f7-ca31-4385-882e-2f16d68d262e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dff187f7-ca31-4385-882e-2f16d68d262e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976326793100, "endTime": 315976326850100}, "additional": {"logType": "info", "children": [], "durationId": "aa2f2f89-9aa2-45b6-b9b5-c85eae808ca8", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "f9f59d9a-ae06-4272-9986-9e5330a9bedf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976329113200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd4c9e3-d94b-4afd-9397-b256cfc534ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976332825100, "endTime": 315976332878600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "27c2a8a6-636c-4e95-b416-b500300238f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27c2a8a6-636c-4e95-b416-b500300238f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976332825100, "endTime": 315976332878600}, "additional": {"logType": "info", "children": [], "durationId": "dfd4c9e3-d94b-4afd-9397-b256cfc534ee", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "d35adf1c-466c-4228-816f-cb4dd92f2f40", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976333139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d75143-bfe3-4a04-b8f0-819626df2faa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976337000500, "endTime": 315976337048700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "0851f9a9-a32f-423b-aebc-7020ea109281"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0851f9a9-a32f-423b-aebc-7020ea109281", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976337000500, "endTime": 315976337048700}, "additional": {"logType": "info", "children": [], "durationId": "53d75143-bfe3-4a04-b8f0-819626df2faa", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "b0c7ed04-1c93-463a-8e45-6b65d450ca23", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976339001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c52cbda-25ce-4fca-9c0c-2187a5a3b10a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976346241300, "endTime": 315976346290100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "1d2e335e-7589-40c0-96f7-f665a6b9e006"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d2e335e-7589-40c0-96f7-f665a6b9e006", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976346241300, "endTime": 315976346290100}, "additional": {"logType": "info", "children": [], "durationId": "5c52cbda-25ce-4fca-9c0c-2187a5a3b10a", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "51e52d03-5193-4790-a739-f41a256d21b3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976346708800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7cbba4a-cf24-4ebc-9858-6513fc5db7dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976350207800, "endTime": 315976350251900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "29396692-44b8-4393-9f63-fb156c9afb3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29396692-44b8-4393-9f63-fb156c9afb3c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976350207800, "endTime": 315976350251900}, "additional": {"logType": "info", "children": [], "durationId": "b7cbba4a-cf24-4ebc-9858-6513fc5db7dc", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "cbc1959a-bb7d-42d6-bffe-d7db7c4eaffc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976351064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a3ccd9-8b82-465a-b4a8-7d6a0187ebbe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976355209400, "endTime": 315976355259800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "481150d8-d0c6-4254-ae37-39b0abc0159b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "481150d8-d0c6-4254-ae37-39b0abc0159b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976355209400, "endTime": 315976355259800}, "additional": {"logType": "info", "children": [], "durationId": "94a3ccd9-8b82-465a-b4a8-7d6a0187ebbe", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "d75ca766-4945-457f-87bc-738ef70976a0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976357107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a78162-779d-4243-a366-d6b5952d5b36", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976360886400, "endTime": 315976360943800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "aa6c1f42-9e33-4be8-b726-45821c293d26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa6c1f42-9e33-4be8-b726-45821c293d26", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976360886400, "endTime": 315976360943800}, "additional": {"logType": "info", "children": [], "durationId": "a9a78162-779d-4243-a366-d6b5952d5b36", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "b64ce7ac-aba4-4a10-9882-d3582b33a393", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976362997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b62780-4286-4719-b832-66b53780ab1d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976366301700, "endTime": 315976366360700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "dab86092-e160-4be0-a931-7925bf9268e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dab86092-e160-4be0-a931-7925bf9268e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976366301700, "endTime": 315976366360700}, "additional": {"logType": "info", "children": [], "durationId": "83b62780-4286-4719-b832-66b53780ab1d", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "4007191d-2ed2-4881-afe1-79f26dc9b9d9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976369122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e55503b-8054-4db1-8d97-bf3d8f27c949", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976373840400, "endTime": 315976373901400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "dce20802-d5d4-4b1e-9307-a57932eff1d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dce20802-d5d4-4b1e-9307-a57932eff1d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976373840400, "endTime": 315976373901400}, "additional": {"logType": "info", "children": [], "durationId": "2e55503b-8054-4db1-8d97-bf3d8f27c949", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "4518cf57-c428-45e4-a2b0-9c5ce7e01a1b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976374208600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1526790e-d7ee-43c0-b73d-97e3c867f9ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976378309300, "endTime": 315976378361100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "521de3a9-4e58-4ff5-9b06-b1f1a8d66d2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "521de3a9-4e58-4ff5-9b06-b1f1a8d66d2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976378309300, "endTime": 315976378361100}, "additional": {"logType": "info", "children": [], "durationId": "1526790e-d7ee-43c0-b73d-97e3c867f9ab", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "99d7b916-3d97-4468-a356-6feece951238", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976378715100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1190e19-dd7e-4120-8df6-e5df968cfd6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976382323800, "endTime": 315976382375900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "505cbb04-8147-476c-b1d6-9523a406098d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "505cbb04-8147-476c-b1d6-9523a406098d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976382323800, "endTime": 315976382375900}, "additional": {"logType": "info", "children": [], "durationId": "c1190e19-dd7e-4120-8df6-e5df968cfd6b", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "d7d1102e-b2ff-4c87-97e5-1c0dcd20e369", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976384198600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c22387fb-a3dd-46fc-b316-f5e9d361054c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976390320500, "endTime": 315976390668100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "2be3d1a7-e1ae-4c4d-937c-3492af0852a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2be3d1a7-e1ae-4c4d-937c-3492af0852a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976390320500, "endTime": 315976390668100}, "additional": {"logType": "info", "children": [], "durationId": "c22387fb-a3dd-46fc-b316-f5e9d361054c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "17872b77-d514-4bac-a1ea-e041e86ae963", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976391047400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd95d84-29f2-4308-b39c-fbe6c0e602b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976394070900, "endTime": 315976394116500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "fec92ad1-4142-4821-b190-fe2d01bb1b0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fec92ad1-4142-4821-b190-fe2d01bb1b0a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976394070900, "endTime": 315976394116500}, "additional": {"logType": "info", "children": [], "durationId": "0dd95d84-29f2-4308-b39c-fbe6c0e602b3", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "55e8b59c-52c7-4ce8-8673-16b571cdaaa2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976394486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5b7c73-f08c-484d-94e8-af6a15ee819c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976398197000, "endTime": 315976398243200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "48401fad-5c47-4844-9d98-62beda077c38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48401fad-5c47-4844-9d98-62beda077c38", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976398197000, "endTime": 315976398243200}, "additional": {"logType": "info", "children": [], "durationId": "8c5b7c73-f08c-484d-94e8-af6a15ee819c", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "966324f4-e9f7-4e1e-8a4e-fc6db807616e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976399485700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61a9cedf-0718-4d3f-a64e-584c010b10bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976405011500, "endTime": 315976405068600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "ffd27521-f84b-4b96-bad7-1e6011c5e53a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffd27521-f84b-4b96-bad7-1e6011c5e53a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976405011500, "endTime": 315976405068600}, "additional": {"logType": "info", "children": [], "durationId": "61a9cedf-0718-4d3f-a64e-584c010b10bf", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "1bd0bbc5-6a36-4824-b617-0baf503091b3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976405825700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475e5e22-8a59-48b8-95ea-62f3d83610a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976410235700, "endTime": 315976410287600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "b5137520-972a-4f93-b3ad-06a5c2f46f8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5137520-972a-4f93-b3ad-06a5c2f46f8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976410235700, "endTime": 315976410287600}, "additional": {"logType": "info", "children": [], "durationId": "475e5e22-8a59-48b8-95ea-62f3d83610a0", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "2b0dd9b4-5747-4350-a18b-7a161d0079fe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976410550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19674709-4614-440b-89e8-51e28b775bc0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976414573700, "endTime": 315976414625200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "507f6896-67ca-49d7-ac49-f70c93f6388a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "507f6896-67ca-49d7-ac49-f70c93f6388a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976414573700, "endTime": 315976414625200}, "additional": {"logType": "info", "children": [], "durationId": "19674709-4614-440b-89e8-51e28b775bc0", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "408293c0-1637-4b06-91cf-798d75015e21", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976415228700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec29be5-20ac-41a4-8180-b8478fd039cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976419168500, "endTime": 315976419219800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "e412c9c2-1d30-48d1-8286-dd02316752bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e412c9c2-1d30-48d1-8286-dd02316752bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976419168500, "endTime": 315976419219800}, "additional": {"logType": "info", "children": [], "durationId": "fec29be5-20ac-41a4-8180-b8478fd039cc", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "e46b83cb-8741-47b3-8575-69fbc8d01e95", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976419529000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "985e77c4-315a-4d18-b6b1-41e2c27aacf5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976422521900, "endTime": 315976422570200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "c3275448-7832-4517-97b7-dcb5361c6592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3275448-7832-4517-97b7-dcb5361c6592", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976422521900, "endTime": 315976422570200}, "additional": {"logType": "info", "children": [], "durationId": "985e77c4-315a-4d18-b6b1-41e2c27aacf5", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "026c6d40-091e-452a-bad1-583a9470ca4e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976422798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7826c330-4c7d-43a7-adeb-3c45ac092a70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976425730400, "endTime": 315976425778400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "d40000dc-c3a6-450d-ae35-02e7553fa4d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d40000dc-c3a6-450d-ae35-02e7553fa4d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976425730400, "endTime": 315976425778400}, "additional": {"logType": "info", "children": [], "durationId": "7826c330-4c7d-43a7-adeb-3c45ac092a70", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "95ee8fed-8bb7-44fa-8ac2-415bd55b5736", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976426011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4138f566-8b05-4d55-84bf-6c5ceb2a00db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976428970200, "endTime": 315976429017800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "353c4c3d-cbf5-410e-8e40-a97646603e8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "353c4c3d-cbf5-410e-8e40-a97646603e8b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976428970200, "endTime": 315976429017800}, "additional": {"logType": "info", "children": [], "durationId": "4138f566-8b05-4d55-84bf-6c5ceb2a00db", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "8f73f0a6-aeae-48af-9f25-889ab3c260aa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976429229200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f8b9cef-7cbb-4ced-834d-a9a5059e00d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976432102200, "endTime": 315976432153300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "68b7284d-f36d-4e23-9144-1b858388615c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68b7284d-f36d-4e23-9144-1b858388615c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976432102200, "endTime": 315976432153300}, "additional": {"logType": "info", "children": [], "durationId": "3f8b9cef-7cbb-4ced-834d-a9a5059e00d1", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "0b3967d7-699e-42db-be0d-a59bc8634710", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976432388400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15dec02d-8f43-4fec-9e1c-f5b2d01699d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976440228300, "endTime": 315976440320200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "1b24d8ce-b4ce-44e0-b45d-ca844b2a374d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b24d8ce-b4ce-44e0-b45d-ca844b2a374d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976440228300, "endTime": 315976440320200}, "additional": {"logType": "info", "children": [], "durationId": "15dec02d-8f43-4fec-9e1c-f5b2d01699d1", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "423c82d6-347f-4e16-8e8c-f84e3d579930", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976443197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c1647c-0950-4bce-8091-33aac8250621", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976450421300, "endTime": 315976450472800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "3931e5ba-17f5-4a4a-aa4a-0545920077f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3931e5ba-17f5-4a4a-aa4a-0545920077f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976450421300, "endTime": 315976450472800}, "additional": {"logType": "info", "children": [], "durationId": "c3c1647c-0950-4bce-8091-33aac8250621", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "4f3f46be-025b-445f-ab52-73c259bddce4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976452461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1f5ce6-d001-453a-9173-6bac62024c6a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976467985700, "endTime": 315976468040100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "42ea08f1-ed10-42ad-97d3-d12de890ae31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42ea08f1-ed10-42ad-97d3-d12de890ae31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976467985700, "endTime": 315976468040100}, "additional": {"logType": "info", "children": [], "durationId": "5e1f5ce6-d001-453a-9173-6bac62024c6a", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "9e4be1cb-8edf-41de-89b6-0b3ac45bd4c0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976470334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b822db5-5d3e-4101-af11-be96e1c3b7c6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976475740500, "endTime": 315976475843600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "c26287e4-3450-45cf-8fdc-8eabb74658f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c26287e4-3450-45cf-8fdc-8eabb74658f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976475740500, "endTime": 315976475843600}, "additional": {"logType": "info", "children": [], "durationId": "1b822db5-5d3e-4101-af11-be96e1c3b7c6", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "f341d65a-4d85-4d96-8dcb-565fd484c771", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976896815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18309bb6-f781-4113-98ce-3eec240281cf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976898132500, "endTime": 315976898157400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "4ca6eb4e-4e74-4a4d-b9f1-a2545c121d0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca6eb4e-4e74-4a4d-b9f1-a2545c121d0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976898132500, "endTime": 315976898157400}, "additional": {"logType": "info", "children": [], "durationId": "18309bb6-f781-4113-98ce-3eec240281cf", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "d1f70179-7579-4298-b4d2-c43974303403", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976904425800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a549bd-d05b-44d9-94f3-dcfc1349628f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976906783600, "endTime": 315976906818000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "d4195ca3-6d1c-4599-ad08-79fde4801547"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4195ca3-6d1c-4599-ad08-79fde4801547", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976906783600, "endTime": 315976906818000}, "additional": {"logType": "info", "children": [], "durationId": "a9a549bd-d05b-44d9-94f3-dcfc1349628f", "parent": "de60da5a-b535-482f-b4e0-f6716133562a"}}, {"head": {"id": "de60da5a-b535-482f-b4e0-f6716133562a", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker8", "startTime": 315957734761100, "endTime": 315976907006000}, "additional": {"logType": "error", "children": ["27224a0d-87bb-4821-8c67-42353672cd9d", "132636f0-25a0-4bea-846e-c3c460762a66", "cf96444c-7e20-4f59-a05e-27031eccb01b", "e53f7e68-ce49-41af-b50a-5ae64f835b61", "f4a0c8eb-0410-41ea-9f29-ebd0fae8e256", "4135c963-af41-4a7c-ac4e-48fbc5f393a4", "e7d0296f-1bac-43fa-8fb0-15f85cd578dc", "70fcc6c7-3512-4c6e-b6f7-d9079680d51b", "8d538f29-e464-4474-b5e1-53366352944f", "a439e0d0-18c1-48ef-a9e2-e7214db09283", "f2241f60-1883-4985-abd9-38bbf8a641ff", "e4d8dc67-0ae8-4454-8fc2-4db6a13c0679", "72b56ee5-e5d8-4282-a4c7-f25916165385", "dd0167b6-f509-44b8-9fec-b4b280845260", "754d5725-ab5b-4671-967b-060f31f275b9", "cf0039b2-8d88-42b8-92ff-b2f5886848a0", "e82e9a1c-a0b8-432c-8efc-285cce6f8cb5", "3956238e-f4ae-41f3-bc7c-c39db35a5349", "b7bc84f9-fc81-4b26-b7ee-8f6f86b6d15b", "71c651b1-c2ab-4a90-8246-65f1270a6be7", "49c3f190-ae79-4558-9998-29dd1d555203", "e1e75aff-aa88-4c6e-9d0b-57bdeb15823f", "0be9cc26-be51-4aec-a65b-b35890a1d498", "43cd5628-5954-4e56-90a3-0adebd8f2603", "cb9b7e44-d177-42ae-8dd7-5af218986192", "9d263dac-2f10-48d7-88e1-60f8923e3f9e", "69888fff-ca05-43d0-b8ed-8b5eb28f24bb", "deba947e-43db-4c72-a533-7041b974f523", "f1682ca2-44f9-43c3-8d25-ad9a1a2b494b", "c3e77b94-995c-4dc8-82fd-e3c3314f5998", "52675145-bdc7-41ef-b7fe-ceb2e682905a", "05cbee55-7800-44b3-bf3f-58aecd9511f2", "bb2fbc83-741d-4749-b017-7e8729867a2a", "211d7881-47cb-4265-b82f-a03c94e46bc1", "82a0db3d-93b6-4095-8ce7-8670d1c41958", "99c581e9-0b83-4190-91ec-98edd5f2ade2", "ef7fafa7-b94c-4b21-8819-20342aa29222", "e0805f94-d203-476b-8c21-37295656b87f", "d601322e-6789-4604-8aa3-4ce21a4bf9ae", "cb55c25b-3bcc-4344-82fa-769caca63b37", "5e0910ce-e312-4f42-a5f2-acfcb11b6c9b", "2ceab38c-8278-465a-a6c0-57b102c66276", "dff187f7-ca31-4385-882e-2f16d68d262e", "27c2a8a6-636c-4e95-b416-b500300238f9", "0851f9a9-a32f-423b-aebc-7020ea109281", "1d2e335e-7589-40c0-96f7-f665a6b9e006", "29396692-44b8-4393-9f63-fb156c9afb3c", "481150d8-d0c6-4254-ae37-39b0abc0159b", "aa6c1f42-9e33-4be8-b726-45821c293d26", "dab86092-e160-4be0-a931-7925bf9268e2", "dce20802-d5d4-4b1e-9307-a57932eff1d7", "521de3a9-4e58-4ff5-9b06-b1f1a8d66d2c", "505cbb04-8147-476c-b1d6-9523a406098d", "2be3d1a7-e1ae-4c4d-937c-3492af0852a2", "fec92ad1-4142-4821-b190-fe2d01bb1b0a", "48401fad-5c47-4844-9d98-62beda077c38", "ffd27521-f84b-4b96-bad7-1e6011c5e53a", "b5137520-972a-4f93-b3ad-06a5c2f46f8d", "507f6896-67ca-49d7-ac49-f70c93f6388a", "e412c9c2-1d30-48d1-8286-dd02316752bd", "c3275448-7832-4517-97b7-dcb5361c6592", "d40000dc-c3a6-450d-ae35-02e7553fa4d8", "353c4c3d-cbf5-410e-8e40-a97646603e8b", "68b7284d-f36d-4e23-9144-1b858388615c", "1b24d8ce-b4ce-44e0-b45d-ca844b2a374d", "3931e5ba-17f5-4a4a-aa4a-0545920077f8", "42ea08f1-ed10-42ad-97d3-d12de890ae31", "c26287e4-3450-45cf-8fdc-8eabb74658f2", "4ca6eb4e-4e74-4a4d-b9f1-a2545c121d0e", "d4195ca3-6d1c-4599-ad08-79fde4801547"], "durationId": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "parent": "0b6f56f1-0687-4b56-82d6-4b5622108ee7"}}, {"head": {"id": "38be3247-725d-479c-95eb-441c40fb9e68", "name": "default@PreviewArkTS watch work[8] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976907383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b6f56f1-0687-4b56-82d6-4b5622108ee7", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957703801200, "endTime": 315976907713700}, "additional": {"logType": "error", "children": ["de60da5a-b535-482f-b4e0-f6716133562a"], "durationId": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9"}}, {"head": {"id": "735e3c1e-758e-4237-a67e-67c0ff70eb77", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976908016700}, "additional": {"logType": "debug", "children": [], "durationId": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9"}}, {"head": {"id": "3f990311-89ea-4a3f-9f36-9bc29501da46", "name": "ERROR: stacktrace = Error: Compilation failed\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976909367700}, "additional": {"logType": "debug", "children": [], "durationId": "e1cbd5d8-1a2f-4bd5-b145-1646fc620ba9"}}, {"head": {"id": "9a31cd17-62f4-46bd-841d-d44973f3d8ee", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976922323600, "endTime": 315976922394200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "def8e335-3e22-41f3-9c03-83c2d15094a3", "logId": "57ffc20a-6013-4ea0-871c-e8de750543e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57ffc20a-6013-4ea0-871c-e8de750543e2", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976922323600, "endTime": 315976922394200}, "additional": {"logType": "info", "children": [], "durationId": "9a31cd17-62f4-46bd-841d-d44973f3d8ee"}}, {"head": {"id": "5693a16b-f82e-4785-972e-ca905ae4fd1a", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315957146842800, "endTime": 315976922582900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 33}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "1e67f6c1-1211-43dd-b048-14c1d0622ec3", "name": "BUILD FAILED in 19 s 776 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976922621100}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "a39a0acb-0c43-4dae-a743-a537e865c697", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976922839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386d1a6e-45e7-4323-939e-9c2553959a88", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976922918900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29bf6b20-8da2-4d97-a696-ec5195f6abaa", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aa7df0f-76e3-423d-bb90-3e8eb3974613", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70ad5e5-8690-41c2-b169-545af2261694", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3975724c-f3a8-4be9-a33d-e6165ee86dbd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923301700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211681c0-ea9a-4312-b61a-f79e0aa56f91", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d852a3ea-8022-46b2-a8ac-fab642ce2d99", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923549700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af6b616f-72ac-4fb9-a760-50708918bf48", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923663200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41cf35ed-2eb3-4b04-99c7-bb57d287b05e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976923763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8dacf1-d722-4276-b1c6-6c7c1474afeb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976930358100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5321500-a60d-4815-806a-a7669d07a5a2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976931997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3c93d2-b55b-4a21-a65c-dc1518d0c103", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976932656100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab6b6a7-40a5-487c-8240-f9e64260500d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976933229500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1293ea99-7b61-4a14-a891-da850535ddd5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976934831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cfa5fa-f8bc-4f1e-b7ef-9503c356f14a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976950099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b55023-c263-4c2f-8be3-0357b359f26f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976950797000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcee9f43-bd00-4917-b270-55bbf920a364", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976951545200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ad7831-b82d-48bb-9e7c-e266c52950ed", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976952684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8d8b74-2e82-4ac1-85fd-c5b4655f742a", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:30 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976953184500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}