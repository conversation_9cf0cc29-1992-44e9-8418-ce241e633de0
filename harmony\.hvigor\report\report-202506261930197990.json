{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "909cc25d-86a2-4826-a4d6-2c0f1f4ce5bc", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 12792030652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "071f1c02-7012-42ff-89e8-bd6b9e379e3c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13105106022100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd61fde-9dfd-43e1-808a-37e2d8801847", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13105865522000, "endTime": 13105888307000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "ce5edbcc-7886-4cd3-8d79-519d459ffa1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce5edbcc-7886-4cd3-8d79-519d459ffa1a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13105865522000, "endTime": 13105888307000}, "additional": {"logType": "info", "children": [], "durationId": "fbd61fde-9dfd-43e1-808a-37e2d8801847"}}, {"head": {"id": "4726a415-2835-49cd-b43e-30d307caf56e", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129399015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16aac6c6-47e6-4f5c-83e2-ba9511de904e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129434627600, "endTime": 13129434674400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "4ac08acb-7012-4582-b0bb-4b75cb58d4f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ac08acb-7012-4582-b0bb-4b75cb58d4f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129434627600, "endTime": 13129434674400}, "additional": {"logType": "info", "children": [], "durationId": "16aac6c6-47e6-4f5c-83e2-ba9511de904e"}}, {"head": {"id": "0730521d-6469-47f7-9e50-d4ad14782606", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129499793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7ff19e-4a1d-476d-90a5-d34451448f07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129501912400, "endTime": 13129501945000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "fad62e7e-275b-423f-bd52-ced2b8194804"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fad62e7e-275b-423f-bd52-ced2b8194804", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13129501912400, "endTime": 13129501945000}, "additional": {"logType": "info", "children": [], "durationId": "ad7ff19e-4a1d-476d-90a5-d34451448f07"}}, {"head": {"id": "e172e9f6-a82a-48c9-8f08-45ef5c240821", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13131216473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823cc4cf-edfb-44cd-b1bf-1d7c29180b59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13131217573800, "endTime": 13131217592600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86e0215f-487c-4a0f-9522-9057742c20a9", "logId": "86283bb5-efd4-40c1-bdf7-c0175da93e47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86283bb5-efd4-40c1-bdf7-c0175da93e47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13131217573800, "endTime": 13131217592600}, "additional": {"logType": "info", "children": [], "durationId": "823cc4cf-edfb-44cd-b1bf-1d7c29180b59"}}, {"head": {"id": "8bb1c71c-c45c-40da-974a-6f479a983f29", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13206309201500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ae21d5-e6b7-428f-b75d-25556bea3c50", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13206342379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4055cbf-0763-4434-a1ae-1beea2a95bd8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208349809800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208490956200, "endTime": 13213743406300}, "additional": {"children": ["9c22d6a0-ecb8-4eae-9f06-fb3d38051eb1", "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "f2135697-66a7-4dd5-aba5-688dc2f0ca27", "e7c538bb-73e1-4743-b6cb-1d5efb5ea795", "4fbe8aea-76ca-4de1-9980-2be9bf7e162d", "a42d6598-da39-4dd4-800e-3a22f80f9ae5", "7562a929-53ae-4de1-908a-a7ff10960b04"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "52f8ca6b-8149-4e50-b521-d61df3d50616"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c22d6a0-ecb8-4eae-9f06-fb3d38051eb1", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208490957300, "endTime": 13209052787500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "aace742b-c1c5-4c25-b925-55769e253feb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209052809100, "endTime": 13213742105000}, "additional": {"children": ["9acfb2cd-11c1-432b-98e8-8fac5a6d6859", "34cd69a8-d606-4b2c-81a5-3084079e7083", "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "8589d971-8b4f-475f-8a6f-70ebc17c10a0", "e902df11-bd04-4c51-85db-8b5b2f19f94a", "a0a520ca-7a2a-4c88-9cd4-aea7635c13a1", "59fc6da6-9c07-40af-8c48-f0539d9373b8", "31d27fc4-44ca-43fe-81fc-a9681b766f87", "d5cd6518-b460-4978-aca5-41e36eaeaf8e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2135697-66a7-4dd5-aba5-688dc2f0ca27", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213742134400, "endTime": 13213743373100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "1e1e396d-4627-4c49-bae9-70acfec25dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7c538bb-73e1-4743-b6cb-1d5efb5ea795", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213743378700, "endTime": 13213743400800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "a9e49011-68a6-4385-bf69-d30a26a1244b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fbe8aea-76ca-4de1-9980-2be9bf7e162d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208638081800, "endTime": 13208638149900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "b59754cb-1ade-44e7-8dc3-e75b187b6fc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b59754cb-1ade-44e7-8dc3-e75b187b6fc9", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208638081800, "endTime": 13208638149900}, "additional": {"logType": "info", "children": [], "durationId": "4fbe8aea-76ca-4de1-9980-2be9bf7e162d", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "a42d6598-da39-4dd4-800e-3a22f80f9ae5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208712793300, "endTime": 13208712809400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "343c3ab9-6f64-42f9-bdc1-bded524e5b9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "343c3ab9-6f64-42f9-bdc1-bded524e5b9e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208712793300, "endTime": 13208712809400}, "additional": {"logType": "info", "children": [], "durationId": "a42d6598-da39-4dd4-800e-3a22f80f9ae5", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "05154ad6-8074-4aa9-856a-35559c8d967a", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208742010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21f547da-c360-407f-ab4c-2292fbff40c9", "name": "Cache service initialization finished in 299 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209052626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aace742b-c1c5-4c25-b925-55769e253feb", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208490957300, "endTime": 13209052787500}, "additional": {"logType": "info", "children": [], "durationId": "9c22d6a0-ecb8-4eae-9f06-fb3d38051eb1", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "9acfb2cd-11c1-432b-98e8-8fac5a6d6859", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209170928100, "endTime": 13209170945900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "45aac093-9736-41aa-87de-447f3ec0bf0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34cd69a8-d606-4b2c-81a5-3084079e7083", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209170974600, "endTime": 13209254054400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "53146155-a924-4c92-a498-0135f1ef98a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209254071000, "endTime": 13211419622300}, "additional": {"children": ["c82aa1e8-c4dd-4b9e-9579-af6b97f58f19", "effbd378-f70e-45b1-ab4c-087b858efaf4", "0fd4ab6a-3d9e-4d43-923b-6b73d566727d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "1ca37062-83f9-441c-b0e2-9912692e6a60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8589d971-8b4f-475f-8a6f-70ebc17c10a0", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211419655100, "endTime": 13211535658300}, "additional": {"children": ["9ca435c9-4ecd-4721-8382-211d280390ec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "5aa7ebae-976d-44ae-80c9-56392eda3a2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e902df11-bd04-4c51-85db-8b5b2f19f94a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211535678400, "endTime": 13213133652800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "c68acf99-6ff6-42fa-a0b8-23f3b9388214"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0a520ca-7a2a-4c88-9cd4-aea7635c13a1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213148771700, "endTime": 13213620181300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "83cc8973-ae2e-4bff-9040-e484c76451d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59fc6da6-9c07-40af-8c48-f0539d9373b8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213620208600, "endTime": 13213741945400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "cfcbbf52-9cad-4617-8ae0-deaa8a710cab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31d27fc4-44ca-43fe-81fc-a9681b766f87", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213741967700, "endTime": 13213742091000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "314c4e7b-998e-44de-a61d-758fc90bdb73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45aac093-9736-41aa-87de-447f3ec0bf0c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209170928100, "endTime": 13209170945900}, "additional": {"logType": "info", "children": [], "durationId": "9acfb2cd-11c1-432b-98e8-8fac5a6d6859", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "53146155-a924-4c92-a498-0135f1ef98a8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209170974600, "endTime": 13209254054400}, "additional": {"logType": "info", "children": [], "durationId": "34cd69a8-d606-4b2c-81a5-3084079e7083", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "c82aa1e8-c4dd-4b9e-9579-af6b97f58f19", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209254730800, "endTime": 13209254753800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "logId": "cfcafedb-a074-436f-9d77-451e6efd5770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfcafedb-a074-436f-9d77-451e6efd5770", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209254730800, "endTime": 13209254753800}, "additional": {"logType": "info", "children": [], "durationId": "c82aa1e8-c4dd-4b9e-9579-af6b97f58f19", "parent": "1ca37062-83f9-441c-b0e2-9912692e6a60"}}, {"head": {"id": "effbd378-f70e-45b1-ab4c-087b858efaf4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209257026300, "endTime": 13211417689200}, "additional": {"children": ["f7d52f60-54b2-4384-9e79-3235f4685bad", "0624d1aa-750c-4517-9aae-4792b4aea7ca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "logId": "ed599319-7db5-4465-8457-ab6084565f1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7d52f60-54b2-4384-9e79-3235f4685bad", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209257027500, "endTime": 13210031543700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbd378-f70e-45b1-ab4c-087b858efaf4", "logId": "599f54e5-81c4-4ab5-b5c6-119693c0481a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0624d1aa-750c-4517-9aae-4792b4aea7ca", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210031580100, "endTime": 13211417660200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "effbd378-f70e-45b1-ab4c-087b858efaf4", "logId": "12fb139a-1523-497d-b923-61afb5627876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f835b88-afbf-42fc-851c-463a570a286e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209257033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3e1a79-5266-4873-9681-13d427111ded", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210031242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599f54e5-81c4-4ab5-b5c6-119693c0481a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209257027500, "endTime": 13210031543700}, "additional": {"logType": "info", "children": [], "durationId": "f7d52f60-54b2-4384-9e79-3235f4685bad", "parent": "ed599319-7db5-4465-8457-ab6084565f1d"}}, {"head": {"id": "fedec586-1e96-44cf-9822-f0e5b153e939", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210031613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf632d1-ab17-4b58-9cda-76676cdad9fb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210239437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f54df1a-b3ea-4f06-8eb3-bf1a32a9dba1", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210239570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43cb357b-410e-4b5b-a7fc-5b3cec3619d8", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210239733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a37ce0-141b-417d-a59b-cae3d0fc91fc", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210244998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170991ad-c74e-43c5-8407-c91205e602c5", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210437805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf53482b-b902-4623-8d2d-d59f79665048", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210475231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fcfa593-e5a0-4d55-b88a-9265be87e474", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210839442600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8f0b5a-816e-49f9-aea4-fe47707d54be", "name": "Sdk init in 813 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211289576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51c86c9-684a-4610-a893-1eca08e9d97a", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211289821000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 30}, "markType": "other"}}, {"head": {"id": "9b876525-2432-4c2b-b4ad-755de54249e4", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211289843600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 30}, "markType": "other"}}, {"head": {"id": "141897b5-e865-4bca-9a84-64e0a8f646ba", "name": "Project task initialization takes 125 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211416840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e5b2025-d645-4074-8bba-91ab14623aa6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211417249300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a46bfcc-ac9d-4289-9c71-136492d39b33", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211417417300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb2f7e2-788a-47b2-9fa9-c79908249441", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211417542300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fb139a-1523-497d-b923-61afb5627876", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13210031580100, "endTime": 13211417660200}, "additional": {"logType": "info", "children": [], "durationId": "0624d1aa-750c-4517-9aae-4792b4aea7ca", "parent": "ed599319-7db5-4465-8457-ab6084565f1d"}}, {"head": {"id": "ed599319-7db5-4465-8457-ab6084565f1d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209257026300, "endTime": 13211417689200}, "additional": {"logType": "info", "children": ["599f54e5-81c4-4ab5-b5c6-119693c0481a", "12fb139a-1523-497d-b923-61afb5627876"], "durationId": "effbd378-f70e-45b1-ab4c-087b858efaf4", "parent": "1ca37062-83f9-441c-b0e2-9912692e6a60"}}, {"head": {"id": "0fd4ab6a-3d9e-4d43-923b-6b73d566727d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211419552300, "endTime": 13211419591800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "logId": "91b7ba55-ec60-44e5-a11a-cbe3f5f5ac47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91b7ba55-ec60-44e5-a11a-cbe3f5f5ac47", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211419552300, "endTime": 13211419591800}, "additional": {"logType": "info", "children": [], "durationId": "0fd4ab6a-3d9e-4d43-923b-6b73d566727d", "parent": "1ca37062-83f9-441c-b0e2-9912692e6a60"}}, {"head": {"id": "1ca37062-83f9-441c-b0e2-9912692e6a60", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209254071000, "endTime": 13211419622300}, "additional": {"logType": "info", "children": ["cfcafedb-a074-436f-9d77-451e6efd5770", "ed599319-7db5-4465-8457-ab6084565f1d", "91b7ba55-ec60-44e5-a11a-cbe3f5f5ac47"], "durationId": "1aa82b1f-da90-401a-a7d1-3b46aa919a52", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "9ca435c9-4ecd-4721-8382-211d280390ec", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211421172900, "endTime": 13211535626600}, "additional": {"children": ["1fd3149b-d1d6-435d-b4b6-2655f9a7aa05", "3359d769-9db6-4f9a-b195-09530216371f", "fcc7cca4-27b1-41a3-a6ab-4fafc4b08268"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8589d971-8b4f-475f-8a6f-70ebc17c10a0", "logId": "c36bc3b5-d03f-4612-beb7-25de82b4436b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fd3149b-d1d6-435d-b4b6-2655f9a7aa05", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211429486500, "endTime": 13211429534000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ca435c9-4ecd-4721-8382-211d280390ec", "logId": "8ad0e7be-182c-4585-bff3-a45a212f2dfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ad0e7be-182c-4585-bff3-a45a212f2dfc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211429486500, "endTime": 13211429534000}, "additional": {"logType": "info", "children": [], "durationId": "1fd3149b-d1d6-435d-b4b6-2655f9a7aa05", "parent": "c36bc3b5-d03f-4612-beb7-25de82b4436b"}}, {"head": {"id": "3359d769-9db6-4f9a-b195-09530216371f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211456412200, "endTime": 13211532697700}, "additional": {"children": ["eff9dc40-74b8-40eb-b4bd-0fabdf36ed65", "3762f034-0404-4449-9268-f7e438cacd20"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ca435c9-4ecd-4721-8382-211d280390ec", "logId": "c9de8b71-2cfa-41d0-86e8-3c87fb622f2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eff9dc40-74b8-40eb-b4bd-0fabdf36ed65", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211456414500, "endTime": 13211509042200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3359d769-9db6-4f9a-b195-09530216371f", "logId": "ec4c1d95-3bed-4c6d-a485-105329df8f85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3762f034-0404-4449-9268-f7e438cacd20", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211509073700, "endTime": 13211532673900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3359d769-9db6-4f9a-b195-09530216371f", "logId": "ae85ade5-5014-4cc9-a9cf-199667dd7a41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89fb7fb4-72b9-46bc-8f9e-2421dadbe309", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211456425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d350a0de-2a4d-48ec-9159-47626302ef03", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211508784200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4c1d95-3bed-4c6d-a485-105329df8f85", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211456414500, "endTime": 13211509042200}, "additional": {"logType": "info", "children": [], "durationId": "eff9dc40-74b8-40eb-b4bd-0fabdf36ed65", "parent": "c9de8b71-2cfa-41d0-86e8-3c87fb622f2f"}}, {"head": {"id": "4c13ad94-bfe6-47e6-aa27-6d9337318f87", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211509096600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a54eff0-0f6f-4d8e-9594-fda38c689eed", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211523594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1a3990a-e6e4-4398-8d56-72e4aaa05340", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211523853200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df0d908-a29b-43b3-89d5-84fe903311df", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211524264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1bc0b18-b4f0-40a5-bcc0-243e63ef493f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211524589100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6f397a-19eb-40ff-b721-bde2176fa0b0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211524751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b98ef9b-32dc-42de-a15f-ad67941a01c4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211524908500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a1e85c-05f6-4a00-9b5b-db01b5cc2570", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211525060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80622db-3b46-45d0-9184-19e33a7b6092", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211531667500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035e475a-065a-4bf4-bb94-52646c50ccfd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211532272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e93fa5c-a71b-448a-952b-a02f3eba8a0e", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211532462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeecaf37-68d6-4837-9564-ea6a5eb0a294", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211532573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae85ade5-5014-4cc9-a9cf-199667dd7a41", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211509073700, "endTime": 13211532673900}, "additional": {"logType": "info", "children": [], "durationId": "3762f034-0404-4449-9268-f7e438cacd20", "parent": "c9de8b71-2cfa-41d0-86e8-3c87fb622f2f"}}, {"head": {"id": "c9de8b71-2cfa-41d0-86e8-3c87fb622f2f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211456412200, "endTime": 13211532697700}, "additional": {"logType": "info", "children": ["ec4c1d95-3bed-4c6d-a485-105329df8f85", "ae85ade5-5014-4cc9-a9cf-199667dd7a41"], "durationId": "3359d769-9db6-4f9a-b195-09530216371f", "parent": "c36bc3b5-d03f-4612-beb7-25de82b4436b"}}, {"head": {"id": "fcc7cca4-27b1-41a3-a6ab-4fafc4b08268", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211535573700, "endTime": 13211535598000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ca435c9-4ecd-4721-8382-211d280390ec", "logId": "b9e4fad5-702c-4854-95af-616ec3b99da4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9e4fad5-702c-4854-95af-616ec3b99da4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211535573700, "endTime": 13211535598000}, "additional": {"logType": "info", "children": [], "durationId": "fcc7cca4-27b1-41a3-a6ab-4fafc4b08268", "parent": "c36bc3b5-d03f-4612-beb7-25de82b4436b"}}, {"head": {"id": "c36bc3b5-d03f-4612-beb7-25de82b4436b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211421172900, "endTime": 13211535626600}, "additional": {"logType": "info", "children": ["8ad0e7be-182c-4585-bff3-a45a212f2dfc", "c9de8b71-2cfa-41d0-86e8-3c87fb622f2f", "b9e4fad5-702c-4854-95af-616ec3b99da4"], "durationId": "9ca435c9-4ecd-4721-8382-211d280390ec", "parent": "5aa7ebae-976d-44ae-80c9-56392eda3a2d"}}, {"head": {"id": "5aa7ebae-976d-44ae-80c9-56392eda3a2d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211419655100, "endTime": 13211535658300}, "additional": {"logType": "info", "children": ["c36bc3b5-d03f-4612-beb7-25de82b4436b"], "durationId": "8589d971-8b4f-475f-8a6f-70ebc17c10a0", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "c2e7fa6f-353e-462a-b13c-9988240d3389", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213132220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ccc1fd-b624-496e-9a42-df0191e6f252", "name": "hvigorfile, resolve hvigorfile dependencies in 1 s 598 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213133385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68acf99-6ff6-42fa-a0b8-23f3b9388214", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13211535678400, "endTime": 13213133652800}, "additional": {"logType": "info", "children": [], "durationId": "e902df11-bd04-4c51-85db-8b5b2f19f94a", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "d5cd6518-b460-4978-aca5-41e36eaeaf8e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213148193000, "endTime": 13213148728800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "logId": "aedae754-c7e3-400f-9a48-71d28d3e1cc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5a22dc6-b6bc-46d1-a5ec-cff35788f06d", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213148272900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aedae754-c7e3-400f-9a48-71d28d3e1cc5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213148193000, "endTime": 13213148728800}, "additional": {"logType": "info", "children": [], "durationId": "d5cd6518-b460-4978-aca5-41e36eaeaf8e", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "2b62ae6c-eb08-4a88-8d8d-0f9b0375276e", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213151622500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992f53dc-726f-4079-81a5-257d6d835c2b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213619180900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83cc8973-ae2e-4bff-9040-e484c76451d1", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213148771700, "endTime": 13213620181300}, "additional": {"logType": "info", "children": [], "durationId": "a0a520ca-7a2a-4c88-9cd4-aea7635c13a1", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "9b6104ef-1e09-40ec-a0f7-f49d17a62ec3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213620234800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5b7e1c-a553-4c41-baf5-f6afc57f2c70", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213735660000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555e9d31-a9a9-489f-a370-487145968a79", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213735820000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e96d1f-80b4-4180-8f5c-751fad17437a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213736166700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5ba4276-f2f6-4722-92ff-97fa5fbb1d78", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213738798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca36d50-f715-40ba-8531-53a9f759a0ed", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213738887200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfcbbf52-9cad-4617-8ae0-deaa8a710cab", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213620208600, "endTime": 13213741945400}, "additional": {"logType": "info", "children": [], "durationId": "59fc6da6-9c07-40af-8c48-f0539d9373b8", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "bb245f56-81a7-44cc-94d0-41d6a946755e", "name": "Configuration phase cost:4 s 572 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213741999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314c4e7b-998e-44de-a61d-758fc90bdb73", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213741967700, "endTime": 13213742091000}, "additional": {"logType": "info", "children": [], "durationId": "31d27fc4-44ca-43fe-81fc-a9681b766f87", "parent": "19f09561-d2f4-4af6-a620-38d8d2ad5e17"}}, {"head": {"id": "19f09561-d2f4-4af6-a620-38d8d2ad5e17", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13209052809100, "endTime": 13213742105000}, "additional": {"logType": "info", "children": ["45aac093-9736-41aa-87de-447f3ec0bf0c", "53146155-a924-4c92-a498-0135f1ef98a8", "1ca37062-83f9-441c-b0e2-9912692e6a60", "5aa7ebae-976d-44ae-80c9-56392eda3a2d", "c68acf99-6ff6-42fa-a0b8-23f3b9388214", "83cc8973-ae2e-4bff-9040-e484c76451d1", "cfcbbf52-9cad-4617-8ae0-deaa8a710cab", "314c4e7b-998e-44de-a61d-758fc90bdb73", "aedae754-c7e3-400f-9a48-71d28d3e1cc5"], "durationId": "417d5599-a86d-4850-926a-1cb0d4d8d0b8", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "7562a929-53ae-4de1-908a-a7ff10960b04", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213743345300, "endTime": 13213743361900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c", "logId": "ae904f52-3a66-42a5-aacc-c329d3c7a61c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae904f52-3a66-42a5-aacc-c329d3c7a61c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213743345300, "endTime": 13213743361900}, "additional": {"logType": "info", "children": [], "durationId": "7562a929-53ae-4de1-908a-a7ff10960b04", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "1e1e396d-4627-4c49-bae9-70acfec25dc2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213742134400, "endTime": 13213743373100}, "additional": {"logType": "info", "children": [], "durationId": "f2135697-66a7-4dd5-aba5-688dc2f0ca27", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "a9e49011-68a6-4385-bf69-d30a26a1244b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213743378700, "endTime": 13213743400800}, "additional": {"logType": "info", "children": [], "durationId": "e7c538bb-73e1-4743-b6cb-1d5efb5ea795", "parent": "52f8ca6b-8149-4e50-b521-d61df3d50616"}}, {"head": {"id": "52f8ca6b-8149-4e50-b521-d61df3d50616", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208490956200, "endTime": 13213743406300}, "additional": {"logType": "info", "children": ["aace742b-c1c5-4c25-b925-55769e253feb", "19f09561-d2f4-4af6-a620-38d8d2ad5e17", "1e1e396d-4627-4c49-bae9-70acfec25dc2", "a9e49011-68a6-4385-bf69-d30a26a1244b", "b59754cb-1ade-44e7-8dc3-e75b187b6fc9", "343c3ab9-6f64-42f9-bdc1-bded524e5b9e", "ae904f52-3a66-42a5-aacc-c329d3c7a61c"], "durationId": "98ab6a7c-33eb-4165-b253-bdec99c9ba1c"}}, {"head": {"id": "ad3f5ef2-7f1d-447a-b248-777b89ecd249", "name": "Configuration task cost before running: 5 s 257 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213743533800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "517f4917-bbb6-40d2-8a36-acd3f357c0f2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213748681500, "endTime": 13213758146100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a4ef2ae2-4721-438e-96b1-e5ac0bebc067", "logId": "b34e6013-5182-4145-9d1f-d78101273a46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4ef2ae2-4721-438e-96b1-e5ac0bebc067", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213745068600}, "additional": {"logType": "detail", "children": [], "durationId": "517f4917-bbb6-40d2-8a36-acd3f357c0f2"}}, {"head": {"id": "d23ae353-4e39-4e9f-8223-d00860055e81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213745561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2b8655-6720-4bb4-8e48-5a8b013bab0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213745634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2647943e-a478-4e22-a887-c8a9f6c317b3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213748691900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c2c880-ce5f-4330-a619-c18f1d4db591", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213757918200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e55eff0b-c849-4c53-8198-5f8661e29668", "name": "entry : default@PreBuild cost memory 0.49095916748046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213758061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b34e6013-5182-4145-9d1f-d78101273a46", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213748681500, "endTime": 13213758146100}, "additional": {"logType": "info", "children": [], "durationId": "517f4917-bbb6-40d2-8a36-acd3f357c0f2"}}, {"head": {"id": "7d4a0030-248a-46db-ad00-f174e4e87f53", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213763120600, "endTime": 13213765226800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "04b2831a-2c14-403c-ae61-68e3a28d2a7a", "logId": "d90ec339-d6e6-4054-9567-444bde1d3065"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04b2831a-2c14-403c-ae61-68e3a28d2a7a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213761825100}, "additional": {"logType": "detail", "children": [], "durationId": "7d4a0030-248a-46db-ad00-f174e4e87f53"}}, {"head": {"id": "7b79eb9c-46c2-49c2-b21d-5cad53d46299", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213762341500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f459dfdb-b386-4824-ab5c-bfa8d933fcfa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213762415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb284221-8efa-41f2-8b04-7d8fa3fedd57", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213763129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55544ba1-b88b-41cb-8dd4-292f4b8643a0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213765068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10da8ffe-8ecb-4a2e-9133-a952598b95e9", "name": "entry : default@MergeProfile cost memory 0.11083984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213765157600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d90ec339-d6e6-4054-9567-444bde1d3065", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213763120600, "endTime": 13213765226800}, "additional": {"logType": "info", "children": [], "durationId": "7d4a0030-248a-46db-ad00-f174e4e87f53"}}, {"head": {"id": "1cbf53d0-762f-4b77-9eec-8286bbb1422b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213768548900, "endTime": 13213771832200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2f3781ed-d7bf-414b-9dd4-e7c31677dbc9", "logId": "3442fa23-f354-4e81-8675-9cbb3ea2cfc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f3781ed-d7bf-414b-9dd4-e7c31677dbc9", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213766873800}, "additional": {"logType": "detail", "children": [], "durationId": "1cbf53d0-762f-4b77-9eec-8286bbb1422b"}}, {"head": {"id": "2bc21915-e6eb-4317-8153-5a997309faab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213767631500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1218be3-964f-4771-bf50-0d049339cf3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213767739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad850b1-62fa-4392-ae06-e100df0672d7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213768557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7775ecd0-a535-4b92-8c8f-59fc91815021", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213770354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d9d6a8c-3a01-4723-b1ff-5da7f61b3735", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213771677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e2292a1-7655-4a41-b06e-25366f9a3372", "name": "entry : default@CreateBuildProfile cost memory 0.0974884033203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213771761300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3442fa23-f354-4e81-8675-9cbb3ea2cfc6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213768548900, "endTime": 13213771832200}, "additional": {"logType": "info", "children": [], "durationId": "1cbf53d0-762f-4b77-9eec-8286bbb1422b"}}, {"head": {"id": "993924b4-f43f-427c-8da8-51b936184841", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798506900, "endTime": 13213799007000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6dc42fa3-8884-48c9-bd95-de5bdba0187d", "logId": "93bd74e1-0ffc-462c-bd16-aff6e289e53f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dc42fa3-8884-48c9-bd95-de5bdba0187d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213773344700}, "additional": {"logType": "detail", "children": [], "durationId": "993924b4-f43f-427c-8da8-51b936184841"}}, {"head": {"id": "66de42af-afd1-44ff-96ee-3bbdb146bf34", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213773825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c2bb51-accb-4255-a2a1-f581d2844700", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213797342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78d2ac2-86c0-4afe-9135-461d87f527b7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798521500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c997483-c864-475e-99a1-857c8c372d18", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798720400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9645ba-7008-4b19-81f1-97ac685a58c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f7ff26-71ca-45d7-9d33-03a3c8d297b3", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b88f7e-274d-44b9-8ad2-c50e4604b99e", "name": "runTaskFromQueue task cost before running: 5 s 313 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93bd74e1-0ffc-462c-bd16-aff6e289e53f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213798506900, "endTime": 13213799007000, "totalTime": 421000}, "additional": {"logType": "info", "children": [], "durationId": "993924b4-f43f-427c-8da8-51b936184841"}}, {"head": {"id": "2aec3393-bbe8-4fd1-a065-39bec1bf6f1e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213818230100, "endTime": 13213819300200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4392af22-ae6b-4008-9535-9ba3a354ebcb", "logId": "d568e9d0-526d-4785-bc1a-a0d2afe0cd21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4392af22-ae6b-4008-9535-9ba3a354ebcb", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213800726600}, "additional": {"logType": "detail", "children": [], "durationId": "2aec3393-bbe8-4fd1-a065-39bec1bf6f1e"}}, {"head": {"id": "fb497cbf-b025-4593-b0eb-bc036ad080e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213801279900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6d75bb-ea8f-4703-9551-98d1db144add", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213801370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab86b42-fe04-4ffa-87e3-e1b71c66c953", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213818246400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd575013-ee2c-44f2-8185-cd41b2440e29", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213818468700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f6bde7-df02-4a5f-9a97-7907c601145f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213819144000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df3c3d46-92db-4611-8975-8e3aa091017e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0662689208984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213819230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d568e9d0-526d-4785-bc1a-a0d2afe0cd21", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213818230100, "endTime": 13213819300200}, "additional": {"logType": "info", "children": [], "durationId": "2aec3393-bbe8-4fd1-a065-39bec1bf6f1e"}}, {"head": {"id": "52d948b2-9265-44f0-94ce-58eafd6d6c11", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213822894800, "endTime": 13213824602100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d0507268-fd71-4f3a-90d2-87b142b318ab", "logId": "fdd2e578-1e9e-417a-ad24-b6f4ee418f4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0507268-fd71-4f3a-90d2-87b142b318ab", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213821001300}, "additional": {"logType": "detail", "children": [], "durationId": "52d948b2-9265-44f0-94ce-58eafd6d6c11"}}, {"head": {"id": "b66a94ea-3867-4568-81c3-90f8d3694056", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213821531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0019e0dd-dfa8-4819-9989-04c411297c0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213821622200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca30f057-31d0-4760-9971-8ffeb83cf558", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213822909300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8319aa0f-c5af-4929-b0ff-c844f5c2be4c", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213824288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed12846-b355-4ef7-9fc3-84068e191b25", "name": "entry : default@ProcessProfile cost memory 0.0570831298828125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213824471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdd2e578-1e9e-417a-ad24-b6f4ee418f4d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213822894800, "endTime": 13213824602100}, "additional": {"logType": "info", "children": [], "durationId": "52d948b2-9265-44f0-94ce-58eafd6d6c11"}}, {"head": {"id": "2cd6434d-be68-4b67-82bd-f59eec7e3853", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213829566500, "endTime": 13213836470600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a8df26a3-74d2-47ac-85cc-c861038082d3", "logId": "69f297ab-4305-4539-9abe-486a54a16ed1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8df26a3-74d2-47ac-85cc-c861038082d3", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213827095200}, "additional": {"logType": "detail", "children": [], "durationId": "2cd6434d-be68-4b67-82bd-f59eec7e3853"}}, {"head": {"id": "39c325b0-6df7-441a-933c-9d2e5a9823e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213827633500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9226a38c-1ea3-4d90-b8c5-69350fed74e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213827732200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b525c523-1020-4f46-a6f5-40f237979e9f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213829577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3edfbd56-fbeb-452a-949f-d84db9013e19", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213836275700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb9c9151-7c59-495e-92e3-7c7ee3a2eb34", "name": "entry : default@ProcessRouterMap cost memory 0.1884002685546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213836396100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f297ab-4305-4539-9abe-486a54a16ed1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213829566500, "endTime": 13213836470600}, "additional": {"logType": "info", "children": [], "durationId": "2cd6434d-be68-4b67-82bd-f59eec7e3853"}}, {"head": {"id": "de7f75c6-df91-4bb3-a61f-88d2bb74f6f7", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213906783000, "endTime": 13213924355100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c0b04b28-abf8-40a3-9f78-f87eb3da18a1", "logId": "e39aaf82-c595-4083-854c-cf469bb0b75c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b04b28-abf8-40a3-9f78-f87eb3da18a1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213839321600}, "additional": {"logType": "detail", "children": [], "durationId": "de7f75c6-df91-4bb3-a61f-88d2bb74f6f7"}}, {"head": {"id": "d95a5fa8-9ae4-4ad2-9942-b60507f92577", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213839838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51254064-6b14-4a71-ab99-f6040920aee0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213839915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "868a990c-c098-4b93-9e60-c5ce8e8b5bc8", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213841165900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601ff42e-9def-4c61-8608-59b9508afb3d", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213922485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e93d990-ed8e-4998-9508-53fb81b0a89e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213922786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eec86b7-7fd8-4b78-aea0-fe7246341ec4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213922862700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0512dfd2-ee1e-4a07-87fc-842fcf04cad4", "name": "entry : default@PreviewProcessResource cost memory 0.06937408447265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213922947800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ce68cf-11d4-4223-9344-9cb731735739", "name": "runTaskFromQueue task cost before running: 5 s 438 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213924254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39aaf82-c595-4083-854c-cf469bb0b75c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213906783000, "endTime": 13213924355100, "totalTime": 16236400}, "additional": {"logType": "info", "children": [], "durationId": "de7f75c6-df91-4bb3-a61f-88d2bb74f6f7"}}, {"head": {"id": "785de176-68f6-42b1-8d51-1bd9de32b2e5", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213931843300, "endTime": 13213952771700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "982486d0-7299-4cb7-8ba4-f2eb22de0702", "logId": "a06084b1-46f7-4672-abf3-a5aa5931c9f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "982486d0-7299-4cb7-8ba4-f2eb22de0702", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213927246500}, "additional": {"logType": "detail", "children": [], "durationId": "785de176-68f6-42b1-8d51-1bd9de32b2e5"}}, {"head": {"id": "80e36ba9-9a3e-4df5-8363-1f6af6dc8d07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213927783700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae3d42a-15f4-49cc-9aad-5d041de5de4c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213927878400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81ad31c-2c88-4035-ae53-ab19df7879e3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213931860200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3122da8a-300e-4939-8eef-b1decd6016f4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213952523700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76dcec2-7248-416e-9570-6f283e84172f", "name": "entry : default@GenerateLoaderJson cost memory 0.7144699096679688", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213952690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06084b1-46f7-4672-abf3-a5aa5931c9f2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213931843300, "endTime": 13213952771700}, "additional": {"logType": "info", "children": [], "durationId": "785de176-68f6-42b1-8d51-1bd9de32b2e5"}}, {"head": {"id": "8213fc49-8dfd-4941-95a0-53a3396c1fc6", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213964980900, "endTime": 13220065537200}, "additional": {"children": ["843402ce-6f20-4f2c-a813-ef6a4c3a6938", "e8e09494-85e9-4b2b-87fa-374739bdcfdc", "1a366e6a-4ce4-4ca5-8fff-9b78d84d78db", "d0dd67d3-ca3e-46c0-9d88-9fd7d0c13cd6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "113a2bcd-46b0-4baa-b47c-937b71132ff6", "logId": "13642902-fa87-484b-b122-0a94a9615e93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "113a2bcd-46b0-4baa-b47c-937b71132ff6", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213960884400}, "additional": {"logType": "detail", "children": [], "durationId": "8213fc49-8dfd-4941-95a0-53a3396c1fc6"}}, {"head": {"id": "f30f3920-bf09-4085-a207-cdd58715a187", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213961480000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d97989-9c03-4462-8a75-23f050db8a04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213961592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc8cae8-1fea-4d64-a44f-159209bbad64", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213962645900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d12ea2-4fd0-4a5f-8288-8b6076652c8f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213965018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c3d759-e46d-45cc-9565-b8b26bd1288d", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13214187305700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ffa88a6-3b98-4b5b-8936-7249c062e294", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 222 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13214187486200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843402ce-6f20-4f2c-a813-ef6a4c3a6938", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13214188647200, "endTime": 13215194640900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8213fc49-8dfd-4941-95a0-53a3396c1fc6", "logId": "e3d26780-3e31-4c6b-999e-a6454e2ab9fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3d26780-3e31-4c6b-999e-a6454e2ab9fa", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13214188647200, "endTime": 13215194640900}, "additional": {"logType": "info", "children": [], "durationId": "843402ce-6f20-4f2c-a813-ef6a4c3a6938", "parent": "13642902-fa87-484b-b122-0a94a9615e93"}}, {"head": {"id": "c14bc326-dcd7-4ce5-8903-ac26fc3a78ca", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13215195444900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8e09494-85e9-4b2b-87fa-374739bdcfdc", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13215197977400, "endTime": 13218388953100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8213fc49-8dfd-4941-95a0-53a3396c1fc6", "logId": "0ab8e8f7-f771-46eb-b6ec-fa0b20894d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c5e4846-7245-43e0-ad16-c00b9267dcbb", "name": "current process  memoryUsage: {\n  rss: 105017344,\n  heapTotal: 121667584,\n  heapUsed: 104940032,\n  external: 3100293,\n  arrayBuffers: 94158\n} os memoryUsage :6.6768035888671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13215199859100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab3446c-3140-469e-ae83-80e86cfe625f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218386885200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab8e8f7-f771-46eb-b6ec-fa0b20894d2e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13215197977400, "endTime": 13218388953100}, "additional": {"logType": "info", "children": [], "durationId": "e8e09494-85e9-4b2b-87fa-374739bdcfdc", "parent": "13642902-fa87-484b-b122-0a94a9615e93"}}, {"head": {"id": "03e01582-9cc4-4346-a99a-16523b5fc71e", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218389088600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a366e6a-4ce4-4ca5-8fff-9b78d84d78db", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218390233800, "endTime": 13218541349600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8213fc49-8dfd-4941-95a0-53a3396c1fc6", "logId": "94a7451f-13fa-4f8f-ba34-0d427e7ac7d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0e1c3b0-9cc3-41c0-a1b3-2fbfa7dea4c2", "name": "current process  memoryUsage: {\n  rss: 105111552,\n  heapTotal: 121667584,\n  heapUsed: 105212152,\n  external: 3100419,\n  arrayBuffers: 94299\n} os memoryUsage :6.762912750244141", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218391285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25bcfd3-2356-484b-867c-455c3d6b373b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218537291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a7451f-13fa-4f8f-ba34-0d427e7ac7d3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218390233800, "endTime": 13218541349600}, "additional": {"logType": "info", "children": [], "durationId": "1a366e6a-4ce4-4ca5-8fff-9b78d84d78db", "parent": "13642902-fa87-484b-b122-0a94a9615e93"}}, {"head": {"id": "18ac001c-688e-42a4-90db-e3549419dace", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218541763200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0dd67d3-ca3e-46c0-9d88-9fd7d0c13cd6", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218543440500, "endTime": 13220064376600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8213fc49-8dfd-4941-95a0-53a3396c1fc6", "logId": "93931b14-e4e9-4a16-9a39-ff1bc92bf306"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42e27ed9-8367-4e7d-8a61-22ebf26257c6", "name": "current process  memoryUsage: {\n  rss: 105144320,\n  heapTotal: 121667584,\n  heapUsed: 105489040,\n  external: 3100545,\n  arrayBuffers: 95239\n} os memoryUsage :6.775379180908203", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218545104400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02e575d6-9fa3-41c6-a779-8ef5cc0bd60d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220060047000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93931b14-e4e9-4a16-9a39-ff1bc92bf306", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13218543440500, "endTime": 13220064376600}, "additional": {"logType": "info", "children": [], "durationId": "d0dd67d3-ca3e-46c0-9d88-9fd7d0c13cd6", "parent": "13642902-fa87-484b-b122-0a94a9615e93"}}, {"head": {"id": "fc79653e-7402-4a0c-9144-39b0c7d5de64", "name": "entry : default@PreviewCompileResource cost memory -1.4762954711914062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220065293000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a956242-f3d4-4887-9b3e-383c903e67df", "name": "runTaskFromQueue task cost before running: 11 s 579 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220065464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13642902-fa87-484b-b122-0a94a9615e93", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13213964980900, "endTime": 13220065537200, "totalTime": 6100432900}, "additional": {"logType": "info", "children": ["e3d26780-3e31-4c6b-999e-a6454e2ab9fa", "0ab8e8f7-f771-46eb-b6ec-fa0b20894d2e", "94a7451f-13fa-4f8f-ba34-0d427e7ac7d3", "93931b14-e4e9-4a16-9a39-ff1bc92bf306"], "durationId": "8213fc49-8dfd-4941-95a0-53a3396c1fc6"}}, {"head": {"id": "3c36df7f-fe8c-4225-9c04-c737d2a72754", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072397700, "endTime": 13220072980700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d179cf50-3278-46e9-bd3d-83fd1322a18a", "logId": "87584a31-c732-4c37-90a0-5afc90b15e32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d179cf50-3278-46e9-bd3d-83fd1322a18a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220071175300}, "additional": {"logType": "detail", "children": [], "durationId": "3c36df7f-fe8c-4225-9c04-c737d2a72754"}}, {"head": {"id": "a3bd2a43-251b-4a70-a5a1-b7644e1c01cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072030000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53738c5f-745f-4560-b6f5-50e51af55504", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9ce4c8-b1d4-4536-8e97-ae2eee84548d", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072411900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb78f1c1-d768-4cef-b536-f4699428ec88", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072556000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4593139-aa0f-4e71-ba32-3fc530c503ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6fbe375-3f04-4071-81ec-ba5bc87c0863", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84790354-5d43-4ad3-8ef7-7925f4f09873", "name": "runTaskFromQueue task cost before running: 11 s 587 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87584a31-c732-4c37-90a0-5afc90b15e32", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220072397700, "endTime": 13220072980700, "totalTime": 435700}, "additional": {"logType": "info", "children": [], "durationId": "3c36df7f-fe8c-4225-9c04-c737d2a72754"}}, {"head": {"id": "2c72a120-5ec3-471f-bfa2-efb4944f8055", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220076797800, "endTime": 13221117709100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "8312348d-4891-4620-9712-5f890ea4fb89", "logId": "1f46dec0-73ea-411f-9946-ca6e75aceafc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8312348d-4891-4620-9712-5f890ea4fb89", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220075402600}, "additional": {"logType": "detail", "children": [], "durationId": "2c72a120-5ec3-471f-bfa2-efb4944f8055"}}, {"head": {"id": "d8e9abfe-c493-4af0-a132-709d72407d89", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220075989100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d06f10-80bb-4ec3-8967-76b516f55a0e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220076095000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba3755f-567d-4538-8cd6-851072cb3685", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220076808700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d34cff2a-1492-4b9c-b3db-f7bc82e55c0a", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220078348900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "820fc9f5-8d1f-49ee-81f0-51404ece034e", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220078460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7829e495-96b6-4ddd-9af1-685bf08920db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220078553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d8ece1-dea4-45bc-ab47-bcad27a04644", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220078632300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9ca07b-8068-40de-b3bd-e8a927d2f172", "name": "entry : default@CopyPreviewProfile cost memory 0.223236083984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221117072100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "466546f9-4c1c-42f5-a4a7-718bb06b5a66", "name": "runTaskFromQueue task cost before running: 12 s 631 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221117469400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f46dec0-73ea-411f-9946-ca6e75aceafc", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13220076797800, "endTime": 13221117709100, "totalTime": 1040583300}, "additional": {"logType": "info", "children": [], "durationId": "2c72a120-5ec3-471f-bfa2-efb4944f8055"}}, {"head": {"id": "5244d5e5-4dcb-4bb8-b42d-59a14cc41def", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126144900, "endTime": 13221127135600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1313753a-f121-4228-8463-43f29eaa98ec", "logId": "55399204-b360-4eb2-bd98-afc3a2cc98a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1313753a-f121-4228-8463-43f29eaa98ec", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221122713100}, "additional": {"logType": "detail", "children": [], "durationId": "5244d5e5-4dcb-4bb8-b42d-59a14cc41def"}}, {"head": {"id": "58bc65a3-d8a0-4610-ac26-2c89c02e317b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221124035400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cfb79c8-0e1c-47f9-bccf-703c91205fb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221124257900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f6ec0c-9571-4e33-9d47-672066554f13", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126166000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba93afe7-7f82-477e-9a42-ff68b1ae1a04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126421600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f95836-bc65-4a0b-b143-7a30cb7e1b4b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62969b1b-3234-46ae-835b-25b301bab6cb", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24188dcb-5b38-48bc-ac5f-9a08eedea0bf", "name": "runTaskFromQueue task cost before running: 12 s 641 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126990300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55399204-b360-4eb2-bd98-afc3a2cc98a0", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221126144900, "endTime": 13221127135600, "totalTime": 798500}, "additional": {"logType": "info", "children": [], "durationId": "5244d5e5-4dcb-4bb8-b42d-59a14cc41def"}}, {"head": {"id": "84a70629-6f8e-4abf-b936-eeb6840b457a", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221131000900, "endTime": 13221131598500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e1e1bc9a-e7c6-4254-a0cb-81186387c5c6", "logId": "21eab68c-05b0-4282-8367-4ee798a73e8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1e1bc9a-e7c6-4254-a0cb-81186387c5c6", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221130889500}, "additional": {"logType": "detail", "children": [], "durationId": "84a70629-6f8e-4abf-b936-eeb6840b457a"}}, {"head": {"id": "4d206888-2142-4216-9cea-d3433711e0e8", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221131016000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d851636-7e0d-4dc5-8776-ce3f8c2b9c43", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221131248400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db3db76-4dc5-4d84-aef8-5e444e7053d9", "name": "runTaskFromQueue task cost before running: 12 s 645 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221131450200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21eab68c-05b0-4282-8367-4ee798a73e8e", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221131000900, "endTime": 13221131598500, "totalTime": 404800}, "additional": {"logType": "info", "children": [], "durationId": "84a70629-6f8e-4abf-b936-eeb6840b457a"}}, {"head": {"id": "e5eff068-1e32-4221-927b-73419558b0eb", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221138862200, "endTime": 13221146720700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "ca380d07-2b74-4dca-9364-9944cf14153d", "logId": "da9d1fc2-0588-4fa8-a854-9d05b22c04d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca380d07-2b74-4dca-9364-9944cf14153d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221135464200}, "additional": {"logType": "detail", "children": [], "durationId": "e5eff068-1e32-4221-927b-73419558b0eb"}}, {"head": {"id": "702a7642-514e-49b3-b007-c1a0204b4fdb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221136771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3762d9c0-dd11-4e6b-b772-a0bc099b397c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221136973900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824b91bd-0f41-488c-902b-1a8919164221", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221138886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9168fc6-e25e-4e32-9a54-9f53468e8c82", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221143580100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94561bc7-bb76-4954-8a67-8e6fd65d7764", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221143843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73296e65-95e6-4f75-aaa9-6cd3ba3caaa7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221144090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98aadc6-eba2-4bb4-912b-a997b855570b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221144237200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a24bf52b-bd9e-4cc0-ab59-82f58d99d6a5", "name": "entry : default@PreviewUpdateAssets cost memory 0.13834381103515625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221146259900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e449bfec-0df2-4609-b63d-ce03e7c22e8e", "name": "runTaskFromQueue task cost before running: 12 s 660 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221146554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9d1fc2-0588-4fa8-a854-9d05b22c04d6", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221138862200, "endTime": 13221146720700, "totalTime": 7640200}, "additional": {"logType": "info", "children": [], "durationId": "e5eff068-1e32-4221-927b-73419558b0eb"}}, {"head": {"id": "26e6e98e-ff33-4a89-b0b6-137f7f04b887", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221163947800, "endTime": 13263553205100}, "additional": {"children": ["abf3e70e-16ab-426e-a04d-ff9219475ac5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "2b76b84e-9b8d-4aec-af3b-779217328621", "logId": "ee559239-0b70-4c43-bca1-71bc91be2bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b76b84e-9b8d-4aec-af3b-779217328621", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221152392800}, "additional": {"logType": "detail", "children": [], "durationId": "26e6e98e-ff33-4a89-b0b6-137f7f04b887"}}, {"head": {"id": "7894f0a5-db52-4313-901b-a0d9c39dddab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221153693500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bfc2298-b3a6-4b5a-8c16-3de74d20bc18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221153912100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36890a2a-3b37-4207-b047-362cbf3e09e4", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221163965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b414c661-d9a1-4163-948c-fde3221c6286", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221177679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc7fb48e-3a57-48e4-929f-cd4b739784ab", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221177848600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13221199033600, "endTime": 13263549790900}, "additional": {"children": ["042fd4ad-03cb-4879-ab0e-dfe8c6472bd4", "7057aa8f-61f1-4c27-9dcf-cbfb51963157", "f725abd5-a5f3-4d42-93c6-640958876328", "68cf5453-1dcf-4d60-ad62-47ae08ded3fa", "457323ed-6d67-46df-809f-60285f6f1a81", "145cd780-4c8a-4ffd-96fc-ee9acdb2faa1"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "26e6e98e-ff33-4a89-b0b6-137f7f04b887", "logId": "f522af31-b63a-416f-be42-7ec18278d563"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd388a56-3ff0-45ee-babe-e804c0e6e236", "name": "entry : default@PreviewArkTS cost memory 1.0531463623046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221201221700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92808f15-92f1-46fe-aa73-c32b8830fb1e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13252944391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042fd4ad-03cb-4879-ab0e-dfe8c6472bd4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13252945540100, "endTime": 13252945557000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "395d4334-1586-48ba-b94f-a8a95f8124ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "395d4334-1586-48ba-b94f-a8a95f8124ef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13252945540100, "endTime": 13252945557000}, "additional": {"logType": "info", "children": [], "durationId": "042fd4ad-03cb-4879-ab0e-dfe8c6472bd4", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "9bf51982-25a1-476d-8473-72f103e09db8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263535683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7057aa8f-61f1-4c27-9dcf-cbfb51963157", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13263549590800, "endTime": 13263549615100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "807de9cc-8969-4016-8cce-51aa3149be5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "807de9cc-8969-4016-8cce-51aa3149be5a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263549590800, "endTime": 13263549615100}, "additional": {"logType": "info", "children": [], "durationId": "7057aa8f-61f1-4c27-9dcf-cbfb51963157", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "f522af31-b63a-416f-be42-7ec18278d563", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13221199033600, "endTime": 13263549790900}, "additional": {"logType": "info", "children": ["395d4334-1586-48ba-b94f-a8a95f8124ef", "807de9cc-8969-4016-8cce-51aa3149be5a", "5aec79d1-faef-4ef4-b855-c81438e2d94f", "d752d6ed-bd28-4f40-9046-834c871170a6", "8ec3c164-2521-4d11-9b6b-4864c2d76624", "50c88f9f-1ce2-489b-ba2a-cd567bba2b42"], "durationId": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "parent": "ee559239-0b70-4c43-bca1-71bc91be2bcb"}}, {"head": {"id": "f725abd5-a5f3-4d42-93c6-640958876328", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13247786477100, "endTime": 13252697223700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "5aec79d1-faef-4ef4-b855-c81438e2d94f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5aec79d1-faef-4ef4-b855-c81438e2d94f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13247786477100, "endTime": 13252697223700}, "additional": {"logType": "info", "children": [], "durationId": "f725abd5-a5f3-4d42-93c6-640958876328", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "68cf5453-1dcf-4d60-ad62-47ae08ded3fa", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13252697804700, "endTime": 13252814516600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "d752d6ed-bd28-4f40-9046-834c871170a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d752d6ed-bd28-4f40-9046-834c871170a6", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13252697804700, "endTime": 13252814516600}, "additional": {"logType": "info", "children": [], "durationId": "68cf5453-1dcf-4d60-ad62-47ae08ded3fa", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "457323ed-6d67-46df-809f-60285f6f1a81", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13252814619400, "endTime": 13252814789100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "8ec3c164-2521-4d11-9b6b-4864c2d76624"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ec3c164-2521-4d11-9b6b-4864c2d76624", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13252814619400, "endTime": 13252814789100}, "additional": {"logType": "info", "children": [], "durationId": "457323ed-6d67-46df-809f-60285f6f1a81", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "145cd780-4c8a-4ffd-96fc-ee9acdb2faa1", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker5", "startTime": 13252814851900, "endTime": 13263535710700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "abf3e70e-16ab-426e-a04d-ff9219475ac5", "logId": "50c88f9f-1ce2-489b-ba2a-cd567bba2b42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50c88f9f-1ce2-489b-ba2a-cd567bba2b42", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13252814851900, "endTime": 13263535710700}, "additional": {"logType": "info", "children": [], "durationId": "145cd780-4c8a-4ffd-96fc-ee9acdb2faa1", "parent": "f522af31-b63a-416f-be42-7ec18278d563"}}, {"head": {"id": "ee559239-0b70-4c43-bca1-71bc91be2bcb", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13221163947800, "endTime": 13263553205100, "totalTime": 42389249100}, "additional": {"logType": "info", "children": ["f522af31-b63a-416f-be42-7ec18278d563"], "durationId": "26e6e98e-ff33-4a89-b0b6-137f7f04b887"}}, {"head": {"id": "5c105f7c-bb5d-4543-8098-3fd4bcb181dc", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263558993600, "endTime": 13263559290500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f913a738-bb41-45fc-a6ae-064970ecd558", "logId": "971ef7f1-beff-4366-94c8-eeddaf74ac1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f913a738-bb41-45fc-a6ae-064970ecd558", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263558933400}, "additional": {"logType": "detail", "children": [], "durationId": "5c105f7c-bb5d-4543-8098-3fd4bcb181dc"}}, {"head": {"id": "ba0c0fcb-b32b-4b8f-90ea-b597e6281d48", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263559004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "469683ac-a1f5-4448-b929-b59b6714c5f7", "name": "entry : PreviewBuild cost memory 0.0165863037109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263559139100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4501dcb-25f3-4ba2-9a62-886bf21f9212", "name": "runTaskFromQueue task cost before running: 55 s 73 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263559229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "971ef7f1-beff-4366-94c8-eeddaf74ac1e", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263558993600, "endTime": 13263559290500, "totalTime": 212700}, "additional": {"logType": "info", "children": [], "durationId": "5c105f7c-bb5d-4543-8098-3fd4bcb181dc"}}, {"head": {"id": "b81e6bd2-a5b3-4230-b5bd-57083b4ae92f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565071100, "endTime": 13263565090100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e3f19ca2-3094-446c-b74d-ce3a3c6bc868", "logId": "f403a177-256a-4ead-a6d3-a5b8eeb47840"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f403a177-256a-4ead-a6d3-a5b8eeb47840", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565071100, "endTime": 13263565090100}, "additional": {"logType": "info", "children": [], "durationId": "b81e6bd2-a5b3-4230-b5bd-57083b4ae92f"}}, {"head": {"id": "146ed395-2b2c-487f-b05d-682b0b57a5fb", "name": "BUILD SUCCESSFUL in 55 s 79 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565130200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d3138e53-ed04-42c3-a7c3-1e2ed27c81bb", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13208486684900, "endTime": 13263565379600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 19, "minute": 31}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "0970b8a4-ad61-4a8b-ab87-de879196ca15", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565408900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9839a05-f919-4177-8d56-1ea657bda430", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565472700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0981bffd-65ca-4554-8ea9-b278360b1b13", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7911d0-79d7-4522-adfe-a0da6e469ef6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14dc3ff3-87f0-451c-91ed-ea6c6dddc9f2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b276893c-6b0d-480d-9197-f9b332dc59fa", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e54a80-27ac-4e1d-b297-8f9c55cb7c48", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263565748000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d11e96-245f-44c1-859a-b275b255ecd9", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263566440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "725c10af-3490-439f-bba0-6cc05680be80", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263575113700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c792ad7-faa2-4bd0-bcda-c69b3e4a529a", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263575491300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28395fb8-4611-438a-a185-9df7ad823832", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263587332800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3cb40d-bf00-45b6-be84-91d6a1efac3c", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263588082200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5108bdce-648a-4cb5-92ce-8b986c3bae3c", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263588340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cca31b1-6408-4eec-b0e4-0eeab2e30a37", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263589229900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1776f9-77fe-45ab-b2c9-18a280ff92a9", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263590101500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8787b380-c2fe-4511-a3cd-2eec2088f517", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263590584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a83222-42c7-47bc-8fc6-5a8e6e0a554d", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263591129500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1e7bab-2a94-4911-8a30-5edf65bab3de", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263591658900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d67fc26-10ee-406a-bd5f-cccc3722788b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263594782900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497ec641-b4c1-4f5e-bd75-c575d4078875", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263595616500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc10b65d-b56c-422e-94d8-ca917a607532", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263595737300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb0d3ca-aaf5-4a1f-8f01-d8cccbe61481", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263596048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b61bc98-6c97-4584-bf16-b0db87da1525", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263597007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5103aa-452b-4395-bda6-c7bb12a5dc3c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263609138000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ec84e8-0fd0-45ae-a998-0a738591de9c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263609700900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f367db26-9027-434c-8c56-d0b74f8ff7a2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263610118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b282383-e16f-4388-8786-d682bce56c19", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263610493900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5ec25b-7850-406d-bb17-c518a41a9643", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 13263610907800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}