{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "8f9c0917-2aba-430e-93f1-038be7151e3a", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139864630000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5827b579-41cc-4171-93ca-a1b3b8891b7c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140872307500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57287ea-cf32-44c4-87de-217ebb3e266b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140884059200, "endTime": 316141146198300}, "additional": {"children": ["7e9a4c7c-f223-4965-80ce-27e9e6b4f1fe", "3cf64010-48a8-421b-ac58-e211cc8128ad", "5a614713-5c50-473c-9413-a19afdc87b5b", "702a374b-c306-4737-bf22-3f2f8b9e2af7", "d50e5ea6-906e-47be-94ed-b679dfd84e2e", "229a5756-2a09-4fbb-b15f-91f2be9e9103", "3316d7e4-0ff6-49c5-9242-f8dc5c4496b1"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e9a4c7c-f223-4965-80ce-27e9e6b4f1fe", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140884060500, "endTime": 316140903329400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "73f758a8-d663-4077-9ffc-28c25d3c9f5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cf64010-48a8-421b-ac58-e211cc8128ad", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140903353700, "endTime": 316141143078300}, "additional": {"children": ["0650bd25-613e-4e32-86ea-ef75202e712a", "0ed6d909-cbd9-44f0-a7a8-942425dcab22", "28840ff9-d765-470a-965e-39e72c4e59c2", "1db06d05-270b-459f-8b5c-e29c99e35ca9", "8fd03cb9-0de7-4212-9c99-129c2fd04233", "75b42571-3824-41f2-81a8-330f8759d2de", "cdbfd1e4-23a4-450e-809e-e8e8b877403d", "e4ca5a78-c328-474f-aa62-dbcfe7e6c2b9", "bba738c2-31ce-427e-a0bf-dbdfcb208b94"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a614713-5c50-473c-9413-a19afdc87b5b", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141143117800, "endTime": 316141146186000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "844e00eb-e07d-4e38-bfad-fa8af9b8bfbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "702a374b-c306-4737-bf22-3f2f8b9e2af7", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141146192400, "endTime": 316141146194000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "b177f13b-6506-428a-8a10-70d3f523f9c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d50e5ea6-906e-47be-94ed-b679dfd84e2e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140888433200, "endTime": 316140888616500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "2fddf674-f999-41e2-9fdf-6ce417bde231"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fddf674-f999-41e2-9fdf-6ce417bde231", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140888433200, "endTime": 316140888616500}, "additional": {"logType": "info", "children": [], "durationId": "d50e5ea6-906e-47be-94ed-b679dfd84e2e", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "229a5756-2a09-4fbb-b15f-91f2be9e9103", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140896738200, "endTime": 316140896919000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "7663d501-79cb-415b-b25d-ff567cff4ea1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7663d501-79cb-415b-b25d-ff567cff4ea1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140896738200, "endTime": 316140896919000}, "additional": {"logType": "info", "children": [], "durationId": "229a5756-2a09-4fbb-b15f-91f2be9e9103", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "a4a5063a-1c55-4960-b8ce-7a7e60a87fd3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140896990900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57fd297b-bae1-458b-8daa-14659dd94cdd", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140903155400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f758a8-d663-4077-9ffc-28c25d3c9f5d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140884060500, "endTime": 316140903329400}, "additional": {"logType": "info", "children": [], "durationId": "7e9a4c7c-f223-4965-80ce-27e9e6b4f1fe", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "0650bd25-613e-4e32-86ea-ef75202e712a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140913421600, "endTime": 316140913435100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "dbd09fe3-6b58-4f00-8be2-eb5df1a0f1fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ed6d909-cbd9-44f0-a7a8-942425dcab22", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140913460600, "endTime": 316140919526400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "1a95606c-627a-41be-826a-e88d27a67ae0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28840ff9-d765-470a-965e-39e72c4e59c2", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140919547400, "endTime": 316141038127300}, "additional": {"children": ["a25cd512-f0f6-4eaa-bfbe-c207ed9c5d80", "4c223574-0af9-48c0-ba5d-482608927976", "c250c3c0-fa72-4586-b411-1121a3fdeec7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "90198b55-052e-47cf-81d3-78283a5c3f20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1db06d05-270b-459f-8b5c-e29c99e35ca9", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141038163900, "endTime": 316141070924500}, "additional": {"children": ["6c153a91-7d54-4daf-baaf-e1e9e211afdd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "22fc85ac-028a-4cae-96d2-5fc6ef80a22f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fd03cb9-0de7-4212-9c99-129c2fd04233", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141070944500, "endTime": 316141110825300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "63231583-bd33-4f1b-89e1-f68cee67f60f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75b42571-3824-41f2-81a8-330f8759d2de", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141112443600, "endTime": 316141125396600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "ef7953e8-db7b-4060-bdc3-48429170592b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdbfd1e4-23a4-450e-809e-e8e8b877403d", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141125420700, "endTime": 316141142888000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "f3e64c62-8597-4e7a-a2ca-c27026f89a79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4ca5a78-c328-474f-aa62-dbcfe7e6c2b9", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141142913600, "endTime": 316141143053900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "8a0bd136-ad87-4098-8faf-267fb550540c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbd09fe3-6b58-4f00-8be2-eb5df1a0f1fa", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140913421600, "endTime": 316140913435100}, "additional": {"logType": "info", "children": [], "durationId": "0650bd25-613e-4e32-86ea-ef75202e712a", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "1a95606c-627a-41be-826a-e88d27a67ae0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140913460600, "endTime": 316140919526400}, "additional": {"logType": "info", "children": [], "durationId": "0ed6d909-cbd9-44f0-a7a8-942425dcab22", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "a25cd512-f0f6-4eaa-bfbe-c207ed9c5d80", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140920385300, "endTime": 316140920404100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28840ff9-d765-470a-965e-39e72c4e59c2", "logId": "c75a577d-98d4-4fd7-834e-0c8b790b1556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c75a577d-98d4-4fd7-834e-0c8b790b1556", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140920385300, "endTime": 316140920404100}, "additional": {"logType": "info", "children": [], "durationId": "a25cd512-f0f6-4eaa-bfbe-c207ed9c5d80", "parent": "90198b55-052e-47cf-81d3-78283a5c3f20"}}, {"head": {"id": "4c223574-0af9-48c0-ba5d-482608927976", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140923271600, "endTime": 316141037263000}, "additional": {"children": ["ad7f5a63-549c-4d00-86e3-985d20ff6343", "60339c4d-b2f0-42b7-963b-9399c5ff2c44"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28840ff9-d765-470a-965e-39e72c4e59c2", "logId": "950cb844-732a-49f8-9987-bcfddcf6a4e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad7f5a63-549c-4d00-86e3-985d20ff6343", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140923273500, "endTime": 316140926502300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c223574-0af9-48c0-ba5d-482608927976", "logId": "57f3809e-8405-4fd6-a0cc-77376eac9f2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60339c4d-b2f0-42b7-963b-9399c5ff2c44", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140926525900, "endTime": 316141037249300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c223574-0af9-48c0-ba5d-482608927976", "logId": "e66aeea6-bc8d-434c-b20c-8c976878a2ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9455ec31-11d5-4086-a9f7-f6bd1ecc026e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140923280700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b01e761-2804-49c1-80c9-566709468bc4", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140926349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f3809e-8405-4fd6-a0cc-77376eac9f2b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140923273500, "endTime": 316140926502300}, "additional": {"logType": "info", "children": [], "durationId": "ad7f5a63-549c-4d00-86e3-985d20ff6343", "parent": "950cb844-732a-49f8-9987-bcfddcf6a4e8"}}, {"head": {"id": "783fad49-c375-4c14-bba5-49aa020a88de", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140926540500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d1def0-770d-47da-8243-81597803b00d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140935956100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d197d7c-de63-43be-8291-cf7ebbc969ea", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140936148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbaeb4e9-6e8a-40ff-ad11-d6da4aa3a843", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140936389600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16dbd35-3e8b-4c0f-b4d5-7f9b96fa72cf", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140936571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e800fd81-ea36-49fd-8058-8eee4137c1a0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140940073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303696dc-cd3d-4917-bd08-7f86a741c859", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140946544900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d724960d-5efd-41b8-bec7-f0fd910bd41c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140960292300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4f3165-e564-4f89-9b0d-9aed1f15129c", "name": "Sdk init in 49 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140996695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d107ef12-f294-44c8-8062-aa47ee17cd73", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140996898200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "markType": "other"}}, {"head": {"id": "2bb4fbe0-1626-45ba-9a3d-5afdd39bee7d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140996917500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "markType": "other"}}, {"head": {"id": "f3490ebe-857a-443f-b0ad-1dff501668e9", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141036910300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc74509-1d53-4542-bf8e-92402ac59821", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141037053100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c623d94-7925-4397-8cdf-c79e3c4a647f", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141037128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113cdaa5-d70f-4bdc-8ebf-0800a3b31b19", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141037201700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66aeea6-bc8d-434c-b20c-8c976878a2ec", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140926525900, "endTime": 316141037249300}, "additional": {"logType": "info", "children": [], "durationId": "60339c4d-b2f0-42b7-963b-9399c5ff2c44", "parent": "950cb844-732a-49f8-9987-bcfddcf6a4e8"}}, {"head": {"id": "950cb844-732a-49f8-9987-bcfddcf6a4e8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140923271600, "endTime": 316141037263000}, "additional": {"logType": "info", "children": ["57f3809e-8405-4fd6-a0cc-77376eac9f2b", "e66aeea6-bc8d-434c-b20c-8c976878a2ec"], "durationId": "4c223574-0af9-48c0-ba5d-482608927976", "parent": "90198b55-052e-47cf-81d3-78283a5c3f20"}}, {"head": {"id": "c250c3c0-fa72-4586-b411-1121a3fdeec7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141037982200, "endTime": 316141037995400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28840ff9-d765-470a-965e-39e72c4e59c2", "logId": "0d4dbd86-862e-4e4a-b522-4741dba36533"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d4dbd86-862e-4e4a-b522-4741dba36533", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141037982200, "endTime": 316141037995400}, "additional": {"logType": "info", "children": [], "durationId": "c250c3c0-fa72-4586-b411-1121a3fdeec7", "parent": "90198b55-052e-47cf-81d3-78283a5c3f20"}}, {"head": {"id": "90198b55-052e-47cf-81d3-78283a5c3f20", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140919547400, "endTime": 316141038127300}, "additional": {"logType": "info", "children": ["c75a577d-98d4-4fd7-834e-0c8b790b1556", "950cb844-732a-49f8-9987-bcfddcf6a4e8", "0d4dbd86-862e-4e4a-b522-4741dba36533"], "durationId": "28840ff9-d765-470a-965e-39e72c4e59c2", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "6c153a91-7d54-4daf-baaf-e1e9e211afdd", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141039045800, "endTime": 316141070909400}, "additional": {"children": ["5d00d709-c3d8-4ae9-a3cf-0633507d555a", "210f9c10-b21c-43c9-b800-c389f9df22bd", "d8accd36-8eb3-4528-9541-ea5d13d233e6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1db06d05-270b-459f-8b5c-e29c99e35ca9", "logId": "0eff5154-fb31-4d24-add6-be8a15455141"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d00d709-c3d8-4ae9-a3cf-0633507d555a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141043796200, "endTime": 316141043816200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c153a91-7d54-4daf-baaf-e1e9e211afdd", "logId": "3c6d0ad8-4503-4644-8a58-381cc5e08bd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c6d0ad8-4503-4644-8a58-381cc5e08bd2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141043796200, "endTime": 316141043816200}, "additional": {"logType": "info", "children": [], "durationId": "5d00d709-c3d8-4ae9-a3cf-0633507d555a", "parent": "0eff5154-fb31-4d24-add6-be8a15455141"}}, {"head": {"id": "210f9c10-b21c-43c9-b800-c389f9df22bd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141046865900, "endTime": 316141068605700}, "additional": {"children": ["ec881df9-492f-44c8-9353-d53b655bd73d", "930e56b7-81a3-471a-a1ed-90ac2c1896c6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c153a91-7d54-4daf-baaf-e1e9e211afdd", "logId": "35aaac6d-f493-4acc-a084-15387f7672a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec881df9-492f-44c8-9353-d53b655bd73d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141046867800, "endTime": 316141050546400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "210f9c10-b21c-43c9-b800-c389f9df22bd", "logId": "f6f87568-7d0b-4293-bb9e-f78ad4ee2b2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "930e56b7-81a3-471a-a1ed-90ac2c1896c6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141050572000, "endTime": 316141068586700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "210f9c10-b21c-43c9-b800-c389f9df22bd", "logId": "9908a551-7fe7-433c-89ea-233dc4ad2c89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e51f2cfb-978f-4c6f-a869-42f3492bbcef", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141046875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f7873e-ed70-41c8-868e-f7e98021445a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141050381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f87568-7d0b-4293-bb9e-f78ad4ee2b2b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141046867800, "endTime": 316141050546400}, "additional": {"logType": "info", "children": [], "durationId": "ec881df9-492f-44c8-9353-d53b655bd73d", "parent": "35aaac6d-f493-4acc-a084-15387f7672a7"}}, {"head": {"id": "3e573ef3-3f92-4fa3-b187-4e71cdb9b862", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141050718900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feda1c7a-f5c1-4b4f-bdd6-887e304432a2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141061971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c6746f1-d65d-4090-b546-9abc6c467dbf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141062160800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac6e9c2-d757-4167-88f4-ba3c51e1b1da", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141062467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24a017a-5fa0-49a2-9cd0-8d3d36742352", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141062772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4976ba1-2ace-4043-9f85-0d3f15052632", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141062930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b300f7d-16f8-412f-b2e0-f40c16347ca6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141063024600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00049ffa-fc48-4d20-8618-7417c337b34e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141063133200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41f1d2a6-1b8b-4784-a204-3d0174ee092c", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141068086800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adcec28-7f1a-4836-99f6-55c6eb11f8a0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141068317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a18ac27-474c-4c17-a915-eb5d47210bd8", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141068424300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9b266f5-9473-4caf-b0f1-1fb1d6d3a7c5", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141068507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9908a551-7fe7-433c-89ea-233dc4ad2c89", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141050572000, "endTime": 316141068586700}, "additional": {"logType": "info", "children": [], "durationId": "930e56b7-81a3-471a-a1ed-90ac2c1896c6", "parent": "35aaac6d-f493-4acc-a084-15387f7672a7"}}, {"head": {"id": "35aaac6d-f493-4acc-a084-15387f7672a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141046865900, "endTime": 316141068605700}, "additional": {"logType": "info", "children": ["f6f87568-7d0b-4293-bb9e-f78ad4ee2b2b", "9908a551-7fe7-433c-89ea-233dc4ad2c89"], "durationId": "210f9c10-b21c-43c9-b800-c389f9df22bd", "parent": "0eff5154-fb31-4d24-add6-be8a15455141"}}, {"head": {"id": "d8accd36-8eb3-4528-9541-ea5d13d233e6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141070871300, "endTime": 316141070888900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c153a91-7d54-4daf-baaf-e1e9e211afdd", "logId": "b28f6789-1ca3-4860-ba64-7b5a321a2b1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b28f6789-1ca3-4860-ba64-7b5a321a2b1a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141070871300, "endTime": 316141070888900}, "additional": {"logType": "info", "children": [], "durationId": "d8accd36-8eb3-4528-9541-ea5d13d233e6", "parent": "0eff5154-fb31-4d24-add6-be8a15455141"}}, {"head": {"id": "0eff5154-fb31-4d24-add6-be8a15455141", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141039045800, "endTime": 316141070909400}, "additional": {"logType": "info", "children": ["3c6d0ad8-4503-4644-8a58-381cc5e08bd2", "35aaac6d-f493-4acc-a084-15387f7672a7", "b28f6789-1ca3-4860-ba64-7b5a321a2b1a"], "durationId": "6c153a91-7d54-4daf-baaf-e1e9e211afdd", "parent": "22fc85ac-028a-4cae-96d2-5fc6ef80a22f"}}, {"head": {"id": "22fc85ac-028a-4cae-96d2-5fc6ef80a22f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141038163900, "endTime": 316141070924500}, "additional": {"logType": "info", "children": ["0eff5154-fb31-4d24-add6-be8a15455141"], "durationId": "1db06d05-270b-459f-8b5c-e29c99e35ca9", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "138f63b8-7f49-4f96-b63e-0f1915069a4d", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141109903900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a62d0e-cc5f-40f2-97e8-dd04b89fddbd", "name": "hvigorfile, resolve hvigorfile dependencies in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141110641900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63231583-bd33-4f1b-89e1-f68cee67f60f", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141070944500, "endTime": 316141110825300}, "additional": {"logType": "info", "children": [], "durationId": "8fd03cb9-0de7-4212-9c99-129c2fd04233", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "bba738c2-31ce-427e-a0bf-dbdfcb208b94", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141112189600, "endTime": 316141112425500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cf64010-48a8-421b-ac58-e211cc8128ad", "logId": "b8e592ee-b44d-4d3e-9ec4-1c29e1da738c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bb377fb-7ba3-4912-920a-e9dc69d166c3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141112221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e592ee-b44d-4d3e-9ec4-1c29e1da738c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141112189600, "endTime": 316141112425500}, "additional": {"logType": "info", "children": [], "durationId": "bba738c2-31ce-427e-a0bf-dbdfcb208b94", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "c867d3ac-4167-4d35-a4a0-a9cf7346e978", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141114207900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22628e49-924d-4e01-a811-4e8473c20ccb", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141124211000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7953e8-db7b-4060-bdc3-48429170592b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141112443600, "endTime": 316141125396600}, "additional": {"logType": "info", "children": [], "durationId": "75b42571-3824-41f2-81a8-330f8759d2de", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "7d960fa1-9bdf-48be-b7ba-072aa6942cb5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141125432000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8aa4dad-d259-46e7-bdd7-7af9d3f06800", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141134437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d724bd7f-0da4-4146-af69-b378f0209ae3", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141134614400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d799281a-a929-47cd-a4af-02be1bce93fb", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141134960600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72b9e2a-3fda-475c-a643-3b459b075450", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141138516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b64a4f-0c40-4bd2-b8fc-7ef33737f3f7", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141138676700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e64c62-8597-4e7a-a2ca-c27026f89a79", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141125420700, "endTime": 316141142888000}, "additional": {"logType": "info", "children": [], "durationId": "cdbfd1e4-23a4-450e-809e-e8e8b877403d", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "a0df3030-2f54-4b90-81bf-d446dec2c169", "name": "Configuration phase cost:230 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141142941300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0bd136-ad87-4098-8faf-267fb550540c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141142913600, "endTime": 316141143053900}, "additional": {"logType": "info", "children": [], "durationId": "e4ca5a78-c328-474f-aa62-dbcfe7e6c2b9", "parent": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b"}}, {"head": {"id": "9de4ebbc-06d4-4698-bdea-1b6932b2c49b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140903353700, "endTime": 316141143078300}, "additional": {"logType": "info", "children": ["dbd09fe3-6b58-4f00-8be2-eb5df1a0f1fa", "1a95606c-627a-41be-826a-e88d27a67ae0", "90198b55-052e-47cf-81d3-78283a5c3f20", "22fc85ac-028a-4cae-96d2-5fc6ef80a22f", "63231583-bd33-4f1b-89e1-f68cee67f60f", "ef7953e8-db7b-4060-bdc3-48429170592b", "f3e64c62-8597-4e7a-a2ca-c27026f89a79", "8a0bd136-ad87-4098-8faf-267fb550540c", "b8e592ee-b44d-4d3e-9ec4-1c29e1da738c"], "durationId": "3cf64010-48a8-421b-ac58-e211cc8128ad", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "3316d7e4-0ff6-49c5-9242-f8dc5c4496b1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141146138900, "endTime": 316141146166200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c57287ea-cf32-44c4-87de-217ebb3e266b", "logId": "4bed2919-1d97-4b9d-b896-7f8c31a58556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bed2919-1d97-4b9d-b896-7f8c31a58556", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141146138900, "endTime": 316141146166200}, "additional": {"logType": "info", "children": [], "durationId": "3316d7e4-0ff6-49c5-9242-f8dc5c4496b1", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "844e00eb-e07d-4e38-bfad-fa8af9b8bfbb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141143117800, "endTime": 316141146186000}, "additional": {"logType": "info", "children": [], "durationId": "5a614713-5c50-473c-9413-a19afdc87b5b", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "b177f13b-6506-428a-8a10-70d3f523f9c6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141146192400, "endTime": 316141146194000}, "additional": {"logType": "info", "children": [], "durationId": "702a374b-c306-4737-bf22-3f2f8b9e2af7", "parent": "4bf863cc-705a-4c30-9dab-4b1f4a053039"}}, {"head": {"id": "4bf863cc-705a-4c30-9dab-4b1f4a053039", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140884059200, "endTime": 316141146198300}, "additional": {"logType": "info", "children": ["73f758a8-d663-4077-9ffc-28c25d3c9f5d", "9de4ebbc-06d4-4698-bdea-1b6932b2c49b", "844e00eb-e07d-4e38-bfad-fa8af9b8bfbb", "b177f13b-6506-428a-8a10-70d3f523f9c6", "2fddf674-f999-41e2-9fdf-6ce417bde231", "7663d501-79cb-415b-b25d-ff567cff4ea1", "4bed2919-1d97-4b9d-b896-7f8c31a58556"], "durationId": "c57287ea-cf32-44c4-87de-217ebb3e266b"}}, {"head": {"id": "70fe50f9-464d-4b52-8893-587479f6ba53", "name": "Configuration task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141146363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a253aca7-ef81-4ad7-9a6f-57d243a3fccd", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141154261700, "endTime": 316141168571000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e3bcd6c6-cb1c-44e7-8d9e-b90c1bfc348c", "logId": "b061ee63-1337-4355-bd7a-2fac16dcc453"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3bcd6c6-cb1c-44e7-8d9e-b90c1bfc348c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141148536800}, "additional": {"logType": "detail", "children": [], "durationId": "a253aca7-ef81-4ad7-9a6f-57d243a3fccd"}}, {"head": {"id": "333044a1-e3e3-4214-a78e-ffeff073add5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141149154500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc83dd71-742e-4d88-bea5-0983a8a15adc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141149286000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c87e9e-4e0a-44ff-89d0-8fb22b7e61b3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141154287800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d16b7fd-3fd2-415d-acd2-45aad605df42", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141168319000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff34eef-1f1d-4588-983a-a8bcc80ae344", "name": "entry : default@PreBuild cost memory 0.26876068115234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141168474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b061ee63-1337-4355-bd7a-2fac16dcc453", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141154261700, "endTime": 316141168571000}, "additional": {"logType": "info", "children": [], "durationId": "a253aca7-ef81-4ad7-9a6f-57d243a3fccd"}}, {"head": {"id": "b72966d2-a6c1-4669-9dc6-52f94f25c471", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141176954600, "endTime": 316141180385800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5775b3fc-6549-4f23-9490-a50bcae0c71f", "logId": "08f98c71-5f5f-4427-a447-2f160ec9e100"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5775b3fc-6549-4f23-9490-a50bcae0c71f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141174832300}, "additional": {"logType": "detail", "children": [], "durationId": "b72966d2-a6c1-4669-9dc6-52f94f25c471"}}, {"head": {"id": "bcd41906-fb94-4431-ab1c-adf821513f45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141175655700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fd39b7-c64d-4a0b-bef2-dbe6b776e8a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141175804700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99ba415d-c75b-4250-baa8-19bd78baae8e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141176969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faff9cf5-ddf9-4075-ab33-9281b3715e67", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141180049000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c6fd64-1d96-4148-bf5b-cea2d71bb86f", "name": "entry : default@MergeProfile cost memory 0.11086273193359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141180267100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f98c71-5f5f-4427-a447-2f160ec9e100", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141176954600, "endTime": 316141180385800}, "additional": {"logType": "info", "children": [], "durationId": "b72966d2-a6c1-4669-9dc6-52f94f25c471"}}, {"head": {"id": "1b0c2df7-0644-45ba-a7d8-e98d4c4655c8", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141185448100, "endTime": 316141189639400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8451f0e0-100f-44f4-875f-43f2d5f1bf6c", "logId": "759003de-bbee-46af-870e-d1f22bac36f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8451f0e0-100f-44f4-875f-43f2d5f1bf6c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141183397100}, "additional": {"logType": "detail", "children": [], "durationId": "1b0c2df7-0644-45ba-a7d8-e98d4c4655c8"}}, {"head": {"id": "3d0d68ca-5c8c-41d1-9475-90c778a4156d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141184109600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f5ce81-519e-43b9-a753-2ce2fb93e37e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141184261000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56275691-2e66-4e8b-b5ff-5059e0799978", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141185487800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b26a31e-b29d-42d6-9ca0-c6a35a6e238d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141187054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "032ffe1c-1278-4c25-8255-60da1cc28b4f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141189327900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e7a1585-63bb-4070-b6c5-feaba3a7cb9a", "name": "entry : default@CreateBuildProfile cost memory 0.097900390625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141189511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "759003de-bbee-46af-870e-d1f22bac36f5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141185448100, "endTime": 316141189639400}, "additional": {"logType": "info", "children": [], "durationId": "1b0c2df7-0644-45ba-a7d8-e98d4c4655c8"}}, {"head": {"id": "11bce73b-9f7d-4623-a1b6-56ec37fbd798", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141194305700, "endTime": 316141195733700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3029f4a0-5001-41f5-a1c1-62443c1bf300", "logId": "afecd028-7f16-41ae-af97-f4c0f1cd6f4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3029f4a0-5001-41f5-a1c1-62443c1bf300", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141192270500}, "additional": {"logType": "detail", "children": [], "durationId": "11bce73b-9f7d-4623-a1b6-56ec37fbd798"}}, {"head": {"id": "310083f6-0252-44ff-b560-4712e5354300", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141192873700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "865b2cf5-0a6f-427e-bedd-17ad936ec22f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141192992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5babc7dc-144d-4fd7-96ec-41ac6bd18530", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141194332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c04b9986-e16b-4d4c-b3ee-dca52eff5003", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141194799600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f763c008-32f8-4d5c-b1ab-fa678545e3ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141195078400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d67e332-b51a-453a-9238-4098cddee1b6", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141195313300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4348c40-b75d-4a41-a8d3-b564824ba1c8", "name": "runTaskFromQueue task cost before running: 318 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141195542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afecd028-7f16-41ae-af97-f4c0f1cd6f4c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141194305700, "endTime": 316141195733700, "totalTime": 1183900}, "additional": {"logType": "info", "children": [], "durationId": "11bce73b-9f7d-4623-a1b6-56ec37fbd798"}}, {"head": {"id": "4f05f2b4-41f7-481f-ae86-a91e3c33eea8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141207572800, "endTime": 316141209141600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "06f34868-fd5c-4db2-a3f7-ed7cae889c60", "logId": "5200e94e-14f9-481f-91c0-51abcbbb1f06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06f34868-fd5c-4db2-a3f7-ed7cae889c60", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141198200700}, "additional": {"logType": "detail", "children": [], "durationId": "4f05f2b4-41f7-481f-ae86-a91e3c33eea8"}}, {"head": {"id": "de006d94-65b7-444a-9f52-684d51d47c3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141198934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad56445c-aaf0-4cd0-bcfc-66fa07635efd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141199098500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9696e7b-c00b-48c8-a3d7-158455df6699", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141207598100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023a4d9d-b93b-4d79-b46e-c7d4742a2aee", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141207915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7899e0e-af3d-487b-a70a-1493ed10d4f6", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141208870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf4f867-25cd-4d0f-bae8-cfb2d6929945", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0661773681640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141209012500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5200e94e-14f9-481f-91c0-51abcbbb1f06", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141207572800, "endTime": 316141209141600}, "additional": {"logType": "info", "children": [], "durationId": "4f05f2b4-41f7-481f-ae86-a91e3c33eea8"}}, {"head": {"id": "9cea6abc-8b94-473a-94d7-49e7b3724dd2", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141214139800, "endTime": 316141216114100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "35e3cbfe-d942-4527-93a4-9f0f81ed2d78", "logId": "6cf86ed9-8dfa-4b97-8169-4ee94388f6af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e3cbfe-d942-4527-93a4-9f0f81ed2d78", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141212103500}, "additional": {"logType": "detail", "children": [], "durationId": "9cea6abc-8b94-473a-94d7-49e7b3724dd2"}}, {"head": {"id": "b0ae797a-18f8-4abf-add2-2a669cece319", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141212694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b0f1de-e007-4f79-aae2-d948910e1d13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141212809700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffdaeb0-936f-4d7e-b5b4-a706abd40658", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141214150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ac3767-5a7c-4438-ae74-3a3e2758fbc9", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141215857600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d64ac4-2b33-49d4-9aef-396e9623c380", "name": "entry : default@ProcessProfile cost memory 0.05709075927734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141216023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cf86ed9-8dfa-4b97-8169-4ee94388f6af", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141214139800, "endTime": 316141216114100}, "additional": {"logType": "info", "children": [], "durationId": "9cea6abc-8b94-473a-94d7-49e7b3724dd2"}}, {"head": {"id": "8cf56498-0254-41b3-b43e-345cb80cfb52", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141221607200, "endTime": 316141229204400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b435ef04-4a2c-4c35-873a-cd8f3a2fe9d9", "logId": "90d08e35-e4a6-4674-b7b6-a41717666d4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b435ef04-4a2c-4c35-873a-cd8f3a2fe9d9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141218632100}, "additional": {"logType": "detail", "children": [], "durationId": "8cf56498-0254-41b3-b43e-345cb80cfb52"}}, {"head": {"id": "0c319ec3-ea5b-4f35-a657-dcdc2d01e43b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141219245700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9add0702-798a-4b0b-a293-3a764d96be63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141219370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f243ee-17ab-4c1b-9b43-d4e7e10fa06d", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141221620700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f2bc513-80cc-4795-b2c9-37e79e96af5b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141228840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f23959f-d520-4141-8f21-57b52cd96af1", "name": "entry : default@ProcessRouterMap cost memory 0.189361572265625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141229056400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d08e35-e4a6-4674-b7b6-a41717666d4f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141221607200, "endTime": 316141229204400}, "additional": {"logType": "info", "children": [], "durationId": "8cf56498-0254-41b3-b43e-345cb80cfb52"}}, {"head": {"id": "de195b38-8be3-4185-abe9-25aad8504c7c", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141237792000, "endTime": 316141241613800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a981cebb-5e15-414e-b5ad-f7a2a3c86bb5", "logId": "e78f7ecf-d4e8-4278-98c9-720277405d99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a981cebb-5e15-414e-b5ad-f7a2a3c86bb5", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141233063800}, "additional": {"logType": "detail", "children": [], "durationId": "de195b38-8be3-4185-abe9-25aad8504c7c"}}, {"head": {"id": "51529e23-b09c-4175-8309-8bbe67a178d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141233565600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f541be-d99d-4ceb-99b9-8dc7160a9e8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141233674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ccd559-2d4c-4076-bbd3-55f3de8454b6", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141234812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f5db5d9-285f-425e-82cc-5d882d556bab", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141239514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea4e8db-851b-4fb2-b72c-b91895886e7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141239700200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66691a0-abaf-40ea-8eb0-981c816b44ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141239770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce07b1dd-2c6b-4916-94da-a4b5514b0e4a", "name": "entry : default@PreviewProcessResource cost memory 0.069000244140625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141239874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3a9057-e1de-400e-94ae-d7f0b6d2cdff", "name": "runTaskFromQueue task cost before running: 364 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141241500200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e78f7ecf-d4e8-4278-98c9-720277405d99", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141237792000, "endTime": 316141241613800, "totalTime": 2170500}, "additional": {"logType": "info", "children": [], "durationId": "de195b38-8be3-4185-abe9-25aad8504c7c"}}, {"head": {"id": "0ad5e030-93a4-4e32-a6cd-74218c888e42", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141252040700, "endTime": 316141287932400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dd5f0b65-7315-4b1d-9b5f-756a2dc392b4", "logId": "629a7f9e-505a-409c-9ec4-c6dfbbbcc7c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd5f0b65-7315-4b1d-9b5f-756a2dc392b4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141245615600}, "additional": {"logType": "detail", "children": [], "durationId": "0ad5e030-93a4-4e32-a6cd-74218c888e42"}}, {"head": {"id": "d0248ebb-35a4-4ac3-a16e-0bf1a26de9e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141246256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69593d20-df41-46d5-9d0d-6a79b410210e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141246416100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf29c09-b479-44f6-ae22-148edba43c2e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141252064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4960adf2-ca3e-4d19-9a28-a4ae9b1bc06d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141287623100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e83036-a94e-40fa-b4aa-83b27df5fa4a", "name": "entry : default@GenerateLoaderJson cost memory -2.7658538818359375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141287833500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "629a7f9e-505a-409c-9ec4-c6dfbbbcc7c3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141252040700, "endTime": 316141287932400}, "additional": {"logType": "info", "children": [], "durationId": "0ad5e030-93a4-4e32-a6cd-74218c888e42"}}, {"head": {"id": "2ec4c88a-c3ba-4757-b9a8-08ed06a5ab47", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141308473700, "endTime": 316141340307600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d15855c0-32ee-4d71-a6bb-f77226bdd293", "logId": "ad67b88b-be85-42c0-bc82-8919b32fb7a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d15855c0-32ee-4d71-a6bb-f77226bdd293", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141301288100}, "additional": {"logType": "detail", "children": [], "durationId": "2ec4c88a-c3ba-4757-b9a8-08ed06a5ab47"}}, {"head": {"id": "e080fca3-5f34-441c-b9db-48e25b66b71d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141302188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95e999b-02a4-40cc-8566-6b333b030691", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141302359800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81a92e70-97b7-4c97-8e0d-7499923cb3ad", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141304112400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f0cc08-425e-415b-9375-02eb10d024af", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141308504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0f8da6-d222-4045-9d0c-291a55fb756f", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141339999800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9659b4-ad8b-4235-a31e-66cc5a68e5c6", "name": "entry : default@PreviewCompileResource cost memory 0.700714111328125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141340179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad67b88b-be85-42c0-bc82-8919b32fb7a2", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141308473700, "endTime": 316141340307600}, "additional": {"logType": "info", "children": [], "durationId": "2ec4c88a-c3ba-4757-b9a8-08ed06a5ab47"}}, {"head": {"id": "4cde7072-e30a-40a6-8724-fc7972034f46", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346580900, "endTime": 316141347769500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "5c7beed3-46e7-4499-ad50-140d705d73a3", "logId": "2e2174f5-97c7-46f8-ac8d-1124c8f9ba37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c7beed3-46e7-4499-ad50-140d705d73a3", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141344945900}, "additional": {"logType": "detail", "children": [], "durationId": "4cde7072-e30a-40a6-8724-fc7972034f46"}}, {"head": {"id": "1d49e0fb-d57c-4ff5-9e25-d1f8865f415a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346017800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78d2a54-6302-4860-9c44-432e3132888b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346335700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cfb9c18-54d7-4bec-befd-29cae271e700", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346608200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed39b40-b960-4fc5-94be-d9afeb4f9198", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346895600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776121e5-72cb-4ba7-a512-bf718ff6c179", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141347110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8937dbc-6243-49a0-a392-c9efcca46dbe", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384521484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141347342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd08f1d3-aca1-4d6c-a428-cef0141be2c1", "name": "runTaskFromQueue task cost before running: 470 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141347563900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2174f5-97c7-46f8-ac8d-1124c8f9ba37", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141346580900, "endTime": 316141347769500, "totalTime": 905800}, "additional": {"logType": "info", "children": [], "durationId": "4cde7072-e30a-40a6-8724-fc7972034f46"}}, {"head": {"id": "2ded4b23-085a-4d72-997f-3c15b113974e", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141353773900, "endTime": 316141357552800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "30d05d7a-2429-47ae-9b1d-2aa779873ec6", "logId": "b5a9fe6f-3565-4acc-bbc1-4837ed0b56b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30d05d7a-2429-47ae-9b1d-2aa779873ec6", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141351606100}, "additional": {"logType": "detail", "children": [], "durationId": "2ded4b23-085a-4d72-997f-3c15b113974e"}}, {"head": {"id": "5806246c-9dde-4db5-b112-1a543b860328", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141352432700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d1fe55-af5c-4320-89fc-5f2b15fdc399", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141352604400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a36e83-09f8-453f-b93d-25e6cdf2c26a", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141353796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ea1f4c-1b06-4cee-ad00-7b4c3074b8b5", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141357266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f02bf1b-72c9-468d-a572-ff03b5e7e786", "name": "entry : default@CopyPreviewProfile cost memory 0.095550537109375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141357450500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5a9fe6f-3565-4acc-bbc1-4837ed0b56b9", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141353773900, "endTime": 316141357552800}, "additional": {"logType": "info", "children": [], "durationId": "2ded4b23-085a-4d72-997f-3c15b113974e"}}, {"head": {"id": "79fc4a42-6847-480e-a295-ad3d6eceb6e9", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141364522300, "endTime": 316141365710800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ce8ce983-c606-4489-ab03-6e09ffed10a7", "logId": "573fd27d-e659-40c2-ac79-f1e8109e5582"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce8ce983-c606-4489-ab03-6e09ffed10a7", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141361246500}, "additional": {"logType": "detail", "children": [], "durationId": "79fc4a42-6847-480e-a295-ad3d6eceb6e9"}}, {"head": {"id": "a072abbc-aa09-42ab-af1a-b0dcb565e78d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141362273600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7e17c0-1c23-4697-b562-a215fdda7c72", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141362606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3f5f6a-4eb9-4280-996b-91301b406ccf", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141364545300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d610ddc1-ea8e-422a-b9e8-750eba3694cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141364882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "208ae09f-4880-4b27-a8b8-2e9e0de7dbfc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141365110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78e48c16-77b7-42de-946f-dcb3492eb0ca", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141365400600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14171563-8547-434e-bd0c-9a3d85ed2807", "name": "runTaskFromQueue task cost before running: 488 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141365605600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "573fd27d-e659-40c2-ac79-f1e8109e5582", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141364522300, "endTime": 316141365710800, "totalTime": 1039100}, "additional": {"logType": "info", "children": [], "durationId": "79fc4a42-6847-480e-a295-ad3d6eceb6e9"}}, {"head": {"id": "6418563b-c5b2-4e8f-aa64-799dcd18cc15", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141369075100, "endTime": 316141369546400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "cd3a9b8d-4ee1-4fba-89c8-9f4bee9e2b5c", "logId": "8fe7dcc7-2695-4872-a61c-ef6b10e6ede9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd3a9b8d-4ee1-4fba-89c8-9f4bee9e2b5c", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141368992400}, "additional": {"logType": "detail", "children": [], "durationId": "6418563b-c5b2-4e8f-aa64-799dcd18cc15"}}, {"head": {"id": "43a724b9-bcac-4653-a502-d2f32c6898e0", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141369089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825936fd-73b1-4cd7-87d3-7c1340d910a6", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141369266600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b3919c-f2d3-49c8-a8f7-ac2955aea66b", "name": "runTaskFromQueue task cost before running: 492 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141369413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe7dcc7-2695-4872-a61c-ef6b10e6ede9", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141369075100, "endTime": 316141369546400, "totalTime": 304300}, "additional": {"logType": "info", "children": [], "durationId": "6418563b-c5b2-4e8f-aa64-799dcd18cc15"}}, {"head": {"id": "7853f5fa-acdf-49fe-974a-de425fd490d0", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141374618400, "endTime": 316141379799000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f2e02ab7-326b-4385-9c40-89da8dc5b867", "logId": "0ab9bd5c-98e0-41f7-8e94-8f9a1c87859a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2e02ab7-326b-4385-9c40-89da8dc5b867", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141372402400}, "additional": {"logType": "detail", "children": [], "durationId": "7853f5fa-acdf-49fe-974a-de425fd490d0"}}, {"head": {"id": "3241e6c1-66a2-4ffd-98b5-534713e461be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141373188400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd3909a0-a7ba-4939-81c2-470440158b6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141373331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4392410-4b93-4347-a358-789acc5a8099", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141374638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf701673-4e13-4582-9d41-7dd798294414", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141378925800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0ef76f-558e-400f-8f7e-f1d306302ff2", "name": "entry : default@PreviewUpdateAssets cost memory 0.1045074462890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141379420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab9bd5c-98e0-41f7-8e94-8f9a1c87859a", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141374618400, "endTime": 316141379799000}, "additional": {"logType": "info", "children": [], "durationId": "7853f5fa-acdf-49fe-974a-de425fd490d0"}}, {"head": {"id": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141395382800, "endTime": 316159448101300}, "additional": {"children": ["9ec58c12-cadc-49fe-b756-8dfda5167dcc"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ce4c6a29-46e2-4035-812d-fd621992a12e", "logId": "859f3d22-716f-435c-aa3b-cf99fc855b7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce4c6a29-46e2-4035-812d-fd621992a12e", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141384527500}, "additional": {"logType": "detail", "children": [], "durationId": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92"}}, {"head": {"id": "78dcdae8-b018-4777-8acb-47efc6382934", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141385479500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729856c4-8f6f-41b1-b1a9-45c67fca93f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141385667700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d37ff3-f59d-4b23-843c-7d09a6edd6bf", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141395406700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker10", "startTime": 316141423885100, "endTime": 316159446793100}, "additional": {"children": ["e742b71e-99c4-4bcb-9bc6-9c57342b5439", "dc15157f-72a6-4f05-9dc0-45d3d7214653", "c595bec6-324b-49e1-a4d5-bff8834f128a", "7c8039ef-a108-4772-933a-4c1043e898a5", "ee8d00a1-dea8-428e-a80f-e2494572acb5", "db384f72-80a3-4f5b-8867-5bb38dba4228", "6c5028fb-0755-4704-b61a-9007408f8f4d", "fd307e88-20b6-4866-a148-c6bb2b35284f", "dbefccef-78c6-44c9-8879-4ff6417f142e", "e47d5426-d46a-40ec-8386-3810808df81a", "fdcaa2ff-7630-4b0d-9862-f8d7d822b7ab", "add7cf52-9800-4eb5-b617-234405f4b922", "bc5cb720-04ef-4d08-a90f-edbaba17c277", "7d2c4882-d897-4c2a-bde9-782e08071c3e", "3f98daa9-ac9f-4f79-9ba8-0a21e3220efd", "2fda4795-e913-4818-812e-f3e8dec87336", "28e3a81b-665e-41f9-a0bf-a026e6719a59", "6f1b418f-683c-4d40-b6cd-b57351f18f50", "d09c2ade-a00c-47c2-8a09-0c1e02b85896", "397ad4f4-8427-4820-9c09-2fb588476e84", "b89b69b5-6525-4049-82ac-84e50d392618", "e2c0d1d7-f73f-4e19-8e8c-3cdf1e771ebd", "3086fcbc-c604-4749-a226-0b368264abf3", "dfb41db6-4148-476c-8d69-e2ea0fe0965c", "27417d59-f0dd-4cd4-a224-c0b4d44859c1", "f1358bc9-0010-4e78-ad90-833315318788", "a8ab150d-ab67-4f3a-ae21-9eba82df430e", "bbe5a638-5b6d-4fe8-9962-f1df90357103", "aca41d2c-0836-4983-9edd-5e1c2a46893b", "ce9b8c78-575d-49c3-82b3-888cc0cb9f9e", "b2eb69b5-9048-4627-9efd-3fc8ee9d2c53", "a0f20a45-a470-477e-b6f9-fbe4f77bf05e", "117680cb-bd09-4617-a960-5c446c44e6d7", "136fc0c3-42bf-4184-911c-1309c8b2d752", "0113057f-8bec-4adc-a134-4a37c39c7f72", "123ba9c8-e426-498f-b009-4a5ed892d875", "01215ae5-3a9a-4302-af90-1320dc6ccb34", "4a42c54f-ed52-4099-b3f3-8354e42bb337", "7185e317-56a5-44ac-864b-d165835ee2e9", "0e09d4b6-6230-4664-8202-8118814fc06a", "ff1a3804-1ac4-4364-87df-de3b15cd85ae", "abcb2198-da6e-4ad7-97f8-cec9cb136983", "eaf80f6b-8091-40b7-9734-9357fdbc11e8", "76c82715-d022-4662-b3dd-1c0a4be9d195", "c5cfef17-8140-475f-b41d-b73773b8af4d", "65809eb0-8328-4191-a6e4-5345740f68bc", "92109b7f-a2a5-42ab-bde0-161a419a6800", "6756e68d-c4cf-4a3a-9110-b4137dee2c2c", "5fb4b277-dd19-4b01-95ca-0b05534eb118", "cd6a50bd-53ae-4ea9-b131-3e4048b9efdd", "b1768c45-a945-46ae-92c5-14644a4e337e", "ea7ef6ca-c8fc-46af-85b1-48b522b9aff5", "b27d2b55-6b9d-4d84-899c-0fab82587d9c", "fb32c97c-e0f6-4a31-ae60-bb278a4fc112", "4f5c9d4b-d61f-4636-bbea-68163272550d", "9ab8524e-39fb-4a49-82b5-31612ae098b3", "70409534-fae3-4a21-b8bc-cf925b5d175b", "ce1c9814-4f26-417e-9034-4b63d361d902", "a3046ae8-cfc5-4c32-b06d-8bc8b23a087a", "7120b80f-40b5-43b6-a16a-446693cb5a0f", "657760de-94d7-4cfd-8c39-87401d848d44", "7269b0ea-c9b8-4e3b-b4a1-a8905a640218", "acf224cd-9f0c-45f2-9743-41e77d0bb662", "f2a8bf61-d7ce-4130-8503-7971a8368eaf", "ccacdc21-5271-417c-91dd-1c1568a9e82d", "9fbbd1a0-e747-4811-867f-71d6838ef57d", "4dfb0d1a-d936-45af-ab15-87f36fff3adf", "5fe06586-a9c7-4b22-8f32-c6120d46e3c8", "94ebfa12-dce6-4c5b-9584-5b7eca92b612", "e52c61e4-9586-410d-ad38-2b6f3d083b54"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92", "logId": "f69bb975-ef4f-4781-8c47-fa663f15885c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09f65933-a8ce-4122-a93c-59056ba70ab6", "name": "entry : default@PreviewArkTS cost memory 0.92779541015625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141427407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443dbd9f-ec55-412e-92d5-00eddd809b55", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316145247054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea5f793-b45a-4c11-80ca-f47860570e04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316145248647900, "endTime": 316145248671500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "bff2d7ac-461a-4290-9a59-326fffc9701e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bff2d7ac-461a-4290-9a59-326fffc9701e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316145248647900, "endTime": 316145248671500}, "additional": {"logType": "info", "children": [], "durationId": "3ea5f793-b45a-4c11-80ca-f47860570e04"}}, {"head": {"id": "a41389ac-0c4c-48f6-9cb6-8caa00b7011a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316147775861600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e742b71e-99c4-4bcb-9bc6-9c57342b5439", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316147778823900, "endTime": 316147778856500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "cc89a04f-9a2e-464a-b03f-568869a196c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc89a04f-9a2e-464a-b03f-568869a196c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316147778823900, "endTime": 316147778856500}, "additional": {"logType": "info", "children": [], "durationId": "e742b71e-99c4-4bcb-9bc6-9c57342b5439", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "2d1ce417-0470-4e12-be69-b4feaf54acc0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156032511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5debc46-a7a8-4cb1-9d37-8442e5bb0b23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156034180600, "endTime": 316156034203400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "7c3c79c5-b128-4667-b539-7e1e3c31bb67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c3c79c5-b128-4667-b539-7e1e3c31bb67", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156034180600, "endTime": 316156034203400}, "additional": {"logType": "info", "children": [], "durationId": "a5debc46-a7a8-4cb1-9d37-8442e5bb0b23"}}, {"head": {"id": "bed8e8e0-8c14-4ee1-8947-05f0f3f23e6d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156034317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b2dc70-8128-4d07-9cb9-c3741c74315e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156035480200, "endTime": 316156035500700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "4aab13e1-6154-47f1-bcc5-1b3a2fbf11df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4aab13e1-6154-47f1-bcc5-1b3a2fbf11df", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156035480200, "endTime": 316156035500700}, "additional": {"logType": "info", "children": [], "durationId": "63b2dc70-8128-4d07-9cb9-c3741c74315e"}}, {"head": {"id": "d07f5e25-ef7f-40e1-b181-a70e7da54bf2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156035599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef8d3e4-8f29-45ae-b8cf-3ed6864ddc3f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156036879400, "endTime": 316156036931800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "9b1f54bd-37fa-4296-84e0-ea069e12e25f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b1f54bd-37fa-4296-84e0-ea069e12e25f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156036879400, "endTime": 316156036931800}, "additional": {"logType": "info", "children": [], "durationId": "bef8d3e4-8f29-45ae-b8cf-3ed6864ddc3f"}}, {"head": {"id": "99e110cf-8974-4eb4-b8d9-fab310bf0fee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156037085100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a370f18-1142-42ed-8e6d-1fddc5f3b71e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156039177800, "endTime": 316156039205200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "b2d91a0e-8eea-4e51-b954-6890eb4188e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2d91a0e-8eea-4e51-b954-6890eb4188e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156039177800, "endTime": 316156039205200}, "additional": {"logType": "info", "children": [], "durationId": "4a370f18-1142-42ed-8e6d-1fddc5f3b71e"}}, {"head": {"id": "86cc9736-f74f-4331-9d8b-12be1e66f54d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156039372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd99ac6d-6c84-4edc-8c10-73e092c16833", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156041273700, "endTime": 316156041299100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "f84a9724-edec-4478-8ae9-415cd4d73d53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f84a9724-edec-4478-8ae9-415cd4d73d53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156041273700, "endTime": 316156041299100}, "additional": {"logType": "info", "children": [], "durationId": "dd99ac6d-6c84-4edc-8c10-73e092c16833"}}, {"head": {"id": "973f0d7d-d6de-4af7-bb86-0e49f06c643b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156041427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9e053b-2b6f-4542-88bd-f5105b24edac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156042885800, "endTime": 316156042907700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "26c8225c-5564-4295-99ce-fa4c82a471f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26c8225c-5564-4295-99ce-fa4c82a471f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156042885800, "endTime": 316156042907700}, "additional": {"logType": "info", "children": [], "durationId": "2e9e053b-2b6f-4542-88bd-f5105b24edac"}}, {"head": {"id": "aa3f6736-a4d3-4682-a1eb-3bdfb65d5718", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156043021200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc3d1381-760c-4a0a-a805-edb8be49cce7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156044638400, "endTime": 316156044664300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "f7676f0d-be27-49da-8d4a-15d0f5423baa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7676f0d-be27-49da-8d4a-15d0f5423baa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156044638400, "endTime": 316156044664300}, "additional": {"logType": "info", "children": [], "durationId": "dc3d1381-760c-4a0a-a805-edb8be49cce7"}}, {"head": {"id": "40096c93-9f67-43df-92cf-3e161e8f5ad4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156044798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401a1e93-9520-406a-8cc9-7a17988a3d68", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156046466100, "endTime": 316156046499900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "2b97759f-6bc5-48c4-a17d-3e50cec59bb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b97759f-6bc5-48c4-a17d-3e50cec59bb6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156046466100, "endTime": 316156046499900}, "additional": {"logType": "info", "children": [], "durationId": "401a1e93-9520-406a-8cc9-7a17988a3d68"}}, {"head": {"id": "272d3cae-eeef-4344-a3db-637950fe2815", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156046638300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6819627a-2b51-4b17-894e-817481d09219", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156048111500, "endTime": 316156048154300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "e8816476-f2df-414a-b1c4-51db67cbe841"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8816476-f2df-414a-b1c4-51db67cbe841", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156048111500, "endTime": 316156048154300}, "additional": {"logType": "info", "children": [], "durationId": "6819627a-2b51-4b17-894e-817481d09219"}}, {"head": {"id": "10778818-2601-45a7-918d-1b9e8d9506e0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156048293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b34f8c-c427-4e93-afb9-02aef058e038", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156052287100, "endTime": 316156052317700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "055ed8e8-5d05-46b5-b736-a4f60544a0e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "055ed8e8-5d05-46b5-b736-a4f60544a0e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156052287100, "endTime": 316156052317700}, "additional": {"logType": "info", "children": [], "durationId": "e6b34f8c-c427-4e93-afb9-02aef058e038"}}, {"head": {"id": "8b853cde-049b-4aee-982a-928488f77368", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156052481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79d26ec-af6d-4d3c-8e90-2703ea2552ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156057667800, "endTime": 316156057697500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "1f37f570-8bb9-4abc-839e-0de7822858ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f37f570-8bb9-4abc-839e-0de7822858ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156057667800, "endTime": 316156057697500}, "additional": {"logType": "info", "children": [], "durationId": "b79d26ec-af6d-4d3c-8e90-2703ea2552ab"}}, {"head": {"id": "e7faaee3-364b-4a0d-8c56-b680b8aec4b5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156057851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6e78b7-dd06-4350-8fcd-14b150a53920", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156059523700, "endTime": 316156059549500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "f3e8b3bb-e4f3-4511-8ce6-13174a4865e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3e8b3bb-e4f3-4511-8ce6-13174a4865e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156059523700, "endTime": 316156059549500}, "additional": {"logType": "info", "children": [], "durationId": "db6e78b7-dd06-4350-8fcd-14b150a53920"}}, {"head": {"id": "d5ada077-4366-4f24-a798-8d67178d6a49", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156059960000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04045b37-4947-467f-84f2-f3b49ae26567", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156061806700, "endTime": 316156061832900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "1f6dfd7d-1bc8-4b8e-9287-0b8764ae2ff0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f6dfd7d-1bc8-4b8e-9287-0b8764ae2ff0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156061806700, "endTime": 316156061832900}, "additional": {"logType": "info", "children": [], "durationId": "04045b37-4947-467f-84f2-f3b49ae26567"}}, {"head": {"id": "10f1c7e8-d6d7-4d35-9fec-0a0085f35149", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156061965800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1ff325-ec71-4bad-8026-384374656c05", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156063499200, "endTime": 316156063527200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "3e2660ad-a9f7-4c77-9fe0-c65dab3eccc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e2660ad-a9f7-4c77-9fe0-c65dab3eccc1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156063499200, "endTime": 316156063527200}, "additional": {"logType": "info", "children": [], "durationId": "bb1ff325-ec71-4bad-8026-384374656c05"}}, {"head": {"id": "b8ecc107-5909-4914-8e28-12c2a3aa49f3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156063659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4daaa2d-d7bf-4eee-8adf-458612a3daee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156064908300, "endTime": 316156064930800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "e749a1fc-f186-4419-ac81-41e468360020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e749a1fc-f186-4419-ac81-41e468360020", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156064908300, "endTime": 316156064930800}, "additional": {"logType": "info", "children": [], "durationId": "c4daaa2d-d7bf-4eee-8adf-458612a3daee"}}, {"head": {"id": "72508f00-5ec1-4c34-afe5-6c4fe404d7c1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156065051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90904784-6ae3-4968-9a43-14d9af65ccde", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156066231800, "endTime": 316156066255400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "63b8d5ec-78d9-4939-84d3-82125cd80cd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63b8d5ec-78d9-4939-84d3-82125cd80cd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156066231800, "endTime": 316156066255400}, "additional": {"logType": "info", "children": [], "durationId": "90904784-6ae3-4968-9a43-14d9af65ccde"}}, {"head": {"id": "2c0aef9b-4c59-486c-981b-b92aec5bd444", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156066380800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eadbe838-3048-4b79-9406-f5bede9b569b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156067719500, "endTime": 316156067742400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "bc82c78e-3cc3-4b72-9f11-ee1ee1f21f6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc82c78e-3cc3-4b72-9f11-ee1ee1f21f6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156067719500, "endTime": 316156067742400}, "additional": {"logType": "info", "children": [], "durationId": "eadbe838-3048-4b79-9406-f5bede9b569b"}}, {"head": {"id": "2cf8aae9-a76a-4137-89e6-06dc80956593", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156067868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c996a12-f4f6-458b-91b2-10655864d034", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156069567800, "endTime": 316156069589700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "cc71473d-c758-4715-8c49-07d20927758e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc71473d-c758-4715-8c49-07d20927758e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156069567800, "endTime": 316156069589700}, "additional": {"logType": "info", "children": [], "durationId": "2c996a12-f4f6-458b-91b2-10655864d034"}}, {"head": {"id": "9fe8e21d-70fc-442b-b85b-8996a0ebcdd4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156069710600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177f5d25-3cd3-48ce-81b5-813d2dd98e3f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156071435900, "endTime": 316156071461800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "5faa81da-72da-484c-bd60-6e7f4f113d62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5faa81da-72da-484c-bd60-6e7f4f113d62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156071435900, "endTime": 316156071461800}, "additional": {"logType": "info", "children": [], "durationId": "177f5d25-3cd3-48ce-81b5-813d2dd98e3f"}}, {"head": {"id": "aff885db-1f78-4811-a8bf-8d5d8c6263e3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156071599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5df836-0c3d-4a14-9d14-c8b60491499e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156073255100, "endTime": 316156073282700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "a6ed76f1-e6cf-4e6e-bcd6-32b2caa01b14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ed76f1-e6cf-4e6e-bcd6-32b2caa01b14", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156073255100, "endTime": 316156073282700}, "additional": {"logType": "info", "children": [], "durationId": "1d5df836-0c3d-4a14-9d14-c8b60491499e"}}, {"head": {"id": "d00e9fb0-d933-4e4b-80f1-fec4a597ccea", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156073428400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "749000f9-e8e7-47cb-bcdf-93223423e520", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156075133900, "endTime": 316156075154100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "17d15f46-8a51-478f-b136-e0995b2c8aef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17d15f46-8a51-478f-b136-e0995b2c8aef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156075133900, "endTime": 316156075154100}, "additional": {"logType": "info", "children": [], "durationId": "749000f9-e8e7-47cb-bcdf-93223423e520"}}, {"head": {"id": "d1a76752-1051-460f-b90e-ca2d58c5925f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156075283800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd20e2d-0ba0-44c3-a921-4a84e29ceb3b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156076611500, "endTime": 316156076631600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "63ce747a-0cd9-47a0-a79d-9e565cbeb950"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63ce747a-0cd9-47a0-a79d-9e565cbeb950", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156076611500, "endTime": 316156076631600}, "additional": {"logType": "info", "children": [], "durationId": "fbd20e2d-0ba0-44c3-a921-4a84e29ceb3b"}}, {"head": {"id": "3e1b0746-3977-477a-8791-6712d7c7dc9c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156076767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1487c41-652b-4fd3-8405-77078275c6d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156078393900, "endTime": 316156078419700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "8a9677ce-343f-4db5-b679-3c8c3eb7fe82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a9677ce-343f-4db5-b679-3c8c3eb7fe82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156078393900, "endTime": 316156078419700}, "additional": {"logType": "info", "children": [], "durationId": "e1487c41-652b-4fd3-8405-77078275c6d7"}}, {"head": {"id": "8c1943e6-b7c5-4cab-9b8f-153725322603", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156078583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e6d6df-35ee-4bee-a6b0-98b58614acf4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156080501700, "endTime": 316156080526700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "66beed11-a925-44ae-953d-3b9b281f60fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66beed11-a925-44ae-953d-3b9b281f60fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156080501700, "endTime": 316156080526700}, "additional": {"logType": "info", "children": [], "durationId": "59e6d6df-35ee-4bee-a6b0-98b58614acf4"}}, {"head": {"id": "557e877e-ac79-4692-bab0-7cbda867e811", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156080658900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8528534e-5b23-454d-a663-a630ee9ab25d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156082229700, "endTime": 316156082257900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "d1fd6377-07b2-4041-a02c-7bd7209d429e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1fd6377-07b2-4041-a02c-7bd7209d429e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156082229700, "endTime": 316156082257900}, "additional": {"logType": "info", "children": [], "durationId": "8528534e-5b23-454d-a663-a630ee9ab25d"}}, {"head": {"id": "3dbefa30-7b20-42e1-9abe-aaaf899edb20", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156082408700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5787bf-7407-46a3-b17b-1a46bc248511", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156084129300, "endTime": 316156084154300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "fbc743b8-0892-4611-a815-ea77db0e77d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbc743b8-0892-4611-a815-ea77db0e77d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156084129300, "endTime": 316156084154300}, "additional": {"logType": "info", "children": [], "durationId": "df5787bf-7407-46a3-b17b-1a46bc248511"}}, {"head": {"id": "d0ed9fe0-d4ed-4875-9a1e-47cbc5aa00a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156084292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0f23ae-31ca-4a3a-8545-89f4be3ed871", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156085950900, "endTime": 316156085977900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "e105aeac-9c40-4d2f-8444-14dcc620bd28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e105aeac-9c40-4d2f-8444-14dcc620bd28", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156085950900, "endTime": 316156085977900}, "additional": {"logType": "info", "children": [], "durationId": "ff0f23ae-31ca-4a3a-8545-89f4be3ed871"}}, {"head": {"id": "559f079c-5b03-4e18-b7be-767efe420984", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156086118700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b4d1c09-5663-41fb-84ef-571401981a23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156087636000, "endTime": 316156087664200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "73192c78-0779-4ad4-bf1e-cb5b4a7601b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73192c78-0779-4ad4-bf1e-cb5b4a7601b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156087636000, "endTime": 316156087664200}, "additional": {"logType": "info", "children": [], "durationId": "9b4d1c09-5663-41fb-84ef-571401981a23"}}, {"head": {"id": "42e2f1d8-2746-485f-946e-082cfa570e77", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156087804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499e7817-7462-4efc-be56-1bf79bf75c4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156089729800, "endTime": 316156089760000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "b7b9a3ab-28eb-45cb-aae9-b676eccaa26d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7b9a3ab-28eb-45cb-aae9-b676eccaa26d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156089729800, "endTime": 316156089760000}, "additional": {"logType": "info", "children": [], "durationId": "499e7817-7462-4efc-be56-1bf79bf75c4d"}}, {"head": {"id": "ee479d3e-e184-4521-a57e-f6b2a8653134", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156089915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc33af0-5561-40b3-919c-d1c28eae94f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156091590300, "endTime": 316156091617000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "81a71f19-a9a6-40e9-a545-17e7e36075e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81a71f19-a9a6-40e9-a545-17e7e36075e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156091590300, "endTime": 316156091617000}, "additional": {"logType": "info", "children": [], "durationId": "9cc33af0-5561-40b3-919c-d1c28eae94f8"}}, {"head": {"id": "90d3c470-6eb4-4394-8051-9fb6a30ae070", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156091754500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f634e0b3-3596-4789-acf6-924df29cc976", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156093307800, "endTime": 316156093332900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "ae40953f-85b0-4d84-8222-158ce1ea6940"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae40953f-85b0-4d84-8222-158ce1ea6940", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156093307800, "endTime": 316156093332900}, "additional": {"logType": "info", "children": [], "durationId": "f634e0b3-3596-4789-acf6-924df29cc976"}}, {"head": {"id": "377a9e7e-bd20-4793-8eeb-d3b93097b378", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156093495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7599b2b0-1b53-41c1-80bc-7a373e1c2704", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156094694500, "endTime": 316156094714800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "9df5121f-1355-455b-bc58-998bbef24e65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9df5121f-1355-455b-bc58-998bbef24e65", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156094694500, "endTime": 316156094714800}, "additional": {"logType": "info", "children": [], "durationId": "7599b2b0-1b53-41c1-80bc-7a373e1c2704"}}, {"head": {"id": "3878023d-7f31-48a6-ac8a-52eb04cc6caf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156094832500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04e0074-8040-4600-ae86-25ebca150cfe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156095939500, "endTime": 316156095958900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "b3741a48-2814-405a-a052-8818e55be949"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3741a48-2814-405a-a052-8818e55be949", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156095939500, "endTime": 316156095958900}, "additional": {"logType": "info", "children": [], "durationId": "b04e0074-8040-4600-ae86-25ebca150cfe"}}, {"head": {"id": "b27e2552-3664-451f-8025-2f5256b9bdb6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156096050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "686a7453-28c6-4dd6-9f4b-47dba9e4f7b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156097116200, "endTime": 316156097140300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "918dd9de-4741-40a7-9a60-2b450cafd2f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "918dd9de-4741-40a7-9a60-2b450cafd2f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156097116200, "endTime": 316156097140300}, "additional": {"logType": "info", "children": [], "durationId": "686a7453-28c6-4dd6-9f4b-47dba9e4f7b1"}}, {"head": {"id": "b0a7dc25-fc9d-4846-a90b-95ef858bfd22", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156097279200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ee7fbc-9cb3-4a6c-9a83-0e58ebb901f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156098614200, "endTime": 316156098644100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "4a0a29b9-fa76-40be-ad3b-8c83b669536a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a0a29b9-fa76-40be-ad3b-8c83b669536a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156098614200, "endTime": 316156098644100}, "additional": {"logType": "info", "children": [], "durationId": "03ee7fbc-9cb3-4a6c-9a83-0e58ebb901f8"}}, {"head": {"id": "87f84bae-2632-45dd-888f-ca3bbf8527de", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156098804600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b61f269-9ed7-40aa-bede-0e56d26f3bf1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156100372200, "endTime": 316156100393800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "0455db5d-cfcb-4b63-b69f-0a0d018d5342"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0455db5d-cfcb-4b63-b69f-0a0d018d5342", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156100372200, "endTime": 316156100393800}, "additional": {"logType": "info", "children": [], "durationId": "5b61f269-9ed7-40aa-bede-0e56d26f3bf1"}}, {"head": {"id": "7f1f77f8-9147-4afe-9bdb-e8eb55dab1b9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156100499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b77481d5-0de0-4518-9289-36b1270684e6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156102089300, "endTime": 316156102120800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "d81212f7-6024-40f5-a5db-69b34a0572e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d81212f7-6024-40f5-a5db-69b34a0572e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156102089300, "endTime": 316156102120800}, "additional": {"logType": "info", "children": [], "durationId": "b77481d5-0de0-4518-9289-36b1270684e6"}}, {"head": {"id": "0247b0fd-84a0-41cb-925a-3cb967cc6187", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156102266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1235fc-244a-4914-a2de-7299b51935b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156104404300, "endTime": 316156104436500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "d0af4391-81bf-448a-bb88-eab79a5ab5cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0af4391-81bf-448a-bb88-eab79a5ab5cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156104404300, "endTime": 316156104436500}, "additional": {"logType": "info", "children": [], "durationId": "dc1235fc-244a-4914-a2de-7299b51935b1"}}, {"head": {"id": "7142e05a-fad9-4b34-8017-a49b022b39c5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156104601900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429d1d4a-7dca-4e6d-832a-ad12513848f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156106355400, "endTime": 316156106383500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "b7a1cc96-28e3-4daa-8e48-47d68df2fd92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7a1cc96-28e3-4daa-8e48-47d68df2fd92", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156106355400, "endTime": 316156106383500}, "additional": {"logType": "info", "children": [], "durationId": "429d1d4a-7dca-4e6d-832a-ad12513848f3"}}, {"head": {"id": "ec270b67-05c3-4ee0-b84b-1f0fa1384283", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156106541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77023b35-2d43-48aa-835b-faf4424553cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156108156900, "endTime": 316156108199900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "2d996f5d-1c50-42d2-968e-3ee78de2e5eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d996f5d-1c50-42d2-968e-3ee78de2e5eb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156108156900, "endTime": 316156108199900}, "additional": {"logType": "info", "children": [], "durationId": "77023b35-2d43-48aa-835b-faf4424553cb"}}, {"head": {"id": "5df2a697-4628-4dba-8afe-da74bf64bbe8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156108353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff110235-c278-42fa-8195-d8f70a84c4e6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156110015700, "endTime": 316156110045600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "f7fb1d1a-7401-4480-9d72-8a003ff42191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7fb1d1a-7401-4480-9d72-8a003ff42191", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156110015700, "endTime": 316156110045600}, "additional": {"logType": "info", "children": [], "durationId": "ff110235-c278-42fa-8195-d8f70a84c4e6"}}, {"head": {"id": "2beae761-056b-41a8-856f-ea67bc7eeb00", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156110188300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27bf70a0-63f4-4fe0-b3ff-1f147ab2564c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156111681700, "endTime": 316156111705300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "0202a3cc-93f9-4de3-bbe4-3431cacfd931"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0202a3cc-93f9-4de3-bbe4-3431cacfd931", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156111681700, "endTime": 316156111705300}, "additional": {"logType": "info", "children": [], "durationId": "27bf70a0-63f4-4fe0-b3ff-1f147ab2564c"}}, {"head": {"id": "b9159626-5f57-433e-ba37-234174512cb7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156111846300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af46ee0d-d55e-45cf-9ae0-76197d0f8108", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156113379700, "endTime": 316156113408700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "462f360d-660d-475f-97b8-481959e2d4fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "462f360d-660d-475f-97b8-481959e2d4fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156113379700, "endTime": 316156113408700}, "additional": {"logType": "info", "children": [], "durationId": "af46ee0d-d55e-45cf-9ae0-76197d0f8108"}}, {"head": {"id": "12059ba9-4a97-4d18-91b8-cce70b83e307", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156113552400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e4942b-a66a-4580-be02-6dee83292a6d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156115164700, "endTime": 316156115189800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "336896e4-b1a1-40a6-8bbb-94c7cea0b645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "336896e4-b1a1-40a6-8bbb-94c7cea0b645", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156115164700, "endTime": 316156115189800}, "additional": {"logType": "info", "children": [], "durationId": "66e4942b-a66a-4580-be02-6dee83292a6d"}}, {"head": {"id": "776e425a-7b7f-4355-82bc-1d1f5a7a283e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156115316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314953a7-ab38-47ce-b78f-f0e29ab6e4b7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156116955800, "endTime": 316156116983600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "0cbd4acb-af2b-4515-b006-1cf2c0fb448b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cbd4acb-af2b-4515-b006-1cf2c0fb448b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156116955800, "endTime": 316156116983600}, "additional": {"logType": "info", "children": [], "durationId": "314953a7-ab38-47ce-b78f-f0e29ab6e4b7"}}, {"head": {"id": "3eefbf08-6c3b-4c88-b9cf-465381e47c95", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156117142500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b6fd81-49ae-44a3-b5a8-faced6d35a0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156119082400, "endTime": 316156119110800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "eb7d8324-21ee-4687-a12d-7d1531bc02c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb7d8324-21ee-4687-a12d-7d1531bc02c4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156119082400, "endTime": 316156119110800}, "additional": {"logType": "info", "children": [], "durationId": "42b6fd81-49ae-44a3-b5a8-faced6d35a0b"}}, {"head": {"id": "63508c24-a6f1-4c33-afa2-251445e2c84f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156119277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7954312a-cebb-4ead-b18e-2c4938d429d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156121125200, "endTime": 316156121157100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "601e39ad-ffcb-4daf-ad6e-1c25aec1aa5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "601e39ad-ffcb-4daf-ad6e-1c25aec1aa5f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156121125200, "endTime": 316156121157100}, "additional": {"logType": "info", "children": [], "durationId": "7954312a-cebb-4ead-b18e-2c4938d429d8"}}, {"head": {"id": "c1616859-990a-4d61-a545-00f4d8b26b92", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156121335400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a13c2ca-ade8-4b8c-8997-518aeae97d42", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156123289100, "endTime": 316156123322900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "2d4def26-5b91-4c26-9f99-0fcf95725a97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d4def26-5b91-4c26-9f99-0fcf95725a97", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156123289100, "endTime": 316156123322900}, "additional": {"logType": "info", "children": [], "durationId": "2a13c2ca-ade8-4b8c-8997-518aeae97d42"}}, {"head": {"id": "aa65268e-06a9-4ab4-9af4-1b988523cb81", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156123529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44083ba3-1be8-4201-8127-b89e144c4159", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156125533100, "endTime": 316156125563700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "372e90dc-0415-4953-aa5d-313b51fdab2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "372e90dc-0415-4953-aa5d-313b51fdab2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156125533100, "endTime": 316156125563700}, "additional": {"logType": "info", "children": [], "durationId": "44083ba3-1be8-4201-8127-b89e144c4159"}}, {"head": {"id": "e147c0f5-a286-478d-8218-fb8fd7350eed", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156125731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9300cf16-f456-425f-b8bc-fa649580bb5d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156127621600, "endTime": 316156127650300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "fe6c4b5b-ecef-4808-8f97-dccdd886c91e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe6c4b5b-ecef-4808-8f97-dccdd886c91e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156127621600, "endTime": 316156127650300}, "additional": {"logType": "info", "children": [], "durationId": "9300cf16-f456-425f-b8bc-fa649580bb5d"}}, {"head": {"id": "e53b87b0-5695-40e1-8e83-7de9f83a748f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156127798100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c695b75d-7546-45e8-9778-37ae6bbb3ca4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156129738400, "endTime": 316156129770500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "33d21770-b11d-49b2-bc75-0fb9af634d26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33d21770-b11d-49b2-bc75-0fb9af634d26", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156129738400, "endTime": 316156129770500}, "additional": {"logType": "info", "children": [], "durationId": "c695b75d-7546-45e8-9778-37ae6bbb3ca4"}}, {"head": {"id": "3bf621eb-f294-41e9-acc3-c01ddf226587", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156129949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b4c8ee-b12e-4d16-98fa-dbddb59d5cf0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156131651300, "endTime": 316156131677300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "d8ba50a4-4c36-4159-b0d8-5e191c8a7309"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8ba50a4-4c36-4159-b0d8-5e191c8a7309", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156131651300, "endTime": 316156131677300}, "additional": {"logType": "info", "children": [], "durationId": "b7b4c8ee-b12e-4d16-98fa-dbddb59d5cf0"}}, {"head": {"id": "a209aa8a-313b-4e33-990e-fb6729df000f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156131806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a169ab7-78be-4c6f-bcce-351309908d14", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156133419400, "endTime": 316156133445400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "35e4ba95-300c-40c5-b8da-fb76b7f2ce94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e4ba95-300c-40c5-b8da-fb76b7f2ce94", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156133419400, "endTime": 316156133445400}, "additional": {"logType": "info", "children": [], "durationId": "9a169ab7-78be-4c6f-bcce-351309908d14"}}, {"head": {"id": "d487110d-0cc1-4c2a-a2cc-0f4f04bd5dda", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156133582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e99ed9a-a554-4fd7-815f-56183d764418", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156135324300, "endTime": 316156135350600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "0467d2bc-ce6d-4b1a-9af5-468b056a5fc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0467d2bc-ce6d-4b1a-9af5-468b056a5fc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156135324300, "endTime": 316156135350600}, "additional": {"logType": "info", "children": [], "durationId": "5e99ed9a-a554-4fd7-815f-56183d764418"}}, {"head": {"id": "386b631f-c449-43c2-b506-1e6a13b23647", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156135493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a728e8-dff4-41b8-973f-8a03c94fde7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156137103200, "endTime": 316156137131100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "e60a7fb8-711d-42f1-8098-bb9db07cdb72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60a7fb8-711d-42f1-8098-bb9db07cdb72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156137103200, "endTime": 316156137131100}, "additional": {"logType": "info", "children": [], "durationId": "31a728e8-dff4-41b8-973f-8a03c94fde7b"}}, {"head": {"id": "788d5f4d-1f73-4599-b60a-c90d2662d332", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156137268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933eaca9-3ac7-465f-b12e-a9ce814cb414", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156138806600, "endTime": 316156138833800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "e8212335-515e-4062-8afe-dd6293677a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8212335-515e-4062-8afe-dd6293677a29", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156138806600, "endTime": 316156138833800}, "additional": {"logType": "info", "children": [], "durationId": "933eaca9-3ac7-465f-b12e-a9ce814cb414"}}, {"head": {"id": "f5025aeb-8c11-4b10-928f-865a04cfc8a2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156139007600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c4a6368-d2a4-48f8-8158-1586a230e634", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156140891400, "endTime": 316156140922900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "4813d4a8-094d-4df4-8aaa-2294a377ad31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4813d4a8-094d-4df4-8aaa-2294a377ad31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156140891400, "endTime": 316156140922900}, "additional": {"logType": "info", "children": [], "durationId": "2c4a6368-d2a4-48f8-8158-1586a230e634"}}, {"head": {"id": "2031ba3f-5eec-42a7-9b6f-acc1adb7ebcf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156141076700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da2cf1a6-44d4-45f7-b285-d144ec702cdd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156142899500, "endTime": 316156142925300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "557fbd22-16fa-4c83-b8a5-2c0214b01de8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "557fbd22-16fa-4c83-b8a5-2c0214b01de8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156142899500, "endTime": 316156142925300}, "additional": {"logType": "info", "children": [], "durationId": "da2cf1a6-44d4-45f7-b285-d144ec702cdd"}}, {"head": {"id": "1d00d139-1e8f-4ca5-bfcf-de63009a0467", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156143049500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f58ea2-e7b6-4eab-a7c2-fd32facb6c7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156144631200, "endTime": 316156144654100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "cbdc3321-7812-449c-8efd-43d4e2012aca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbdc3321-7812-449c-8efd-43d4e2012aca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156144631200, "endTime": 316156144654100}, "additional": {"logType": "info", "children": [], "durationId": "64f58ea2-e7b6-4eab-a7c2-fd32facb6c7b"}}, {"head": {"id": "54daf2f5-e777-4e81-85bd-d4a90f569189", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156144768900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa74d45a-fe4c-4604-9b8a-e371f1bc559c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156146285100, "endTime": 316156146307100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "57f56a4c-f55a-4961-9db4-a98816a6cc5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57f56a4c-f55a-4961-9db4-a98816a6cc5e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156146285100, "endTime": 316156146307100}, "additional": {"logType": "info", "children": [], "durationId": "aa74d45a-fe4c-4604-9b8a-e371f1bc559c"}}, {"head": {"id": "2b1ecf0a-82d1-4a59-911f-58c6ef9d9c26", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156146421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e79783a3-4af1-4cc7-a492-ba8b5e41baea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156147958100, "endTime": 316156147983300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "11bc1826-f041-4afe-a23e-6d7ec9fa2c08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11bc1826-f041-4afe-a23e-6d7ec9fa2c08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156147958100, "endTime": 316156147983300}, "additional": {"logType": "info", "children": [], "durationId": "e79783a3-4af1-4cc7-a492-ba8b5e41baea"}}, {"head": {"id": "707bb8a5-d119-4316-bd82-70c067cbcc05", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156148108500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a6376d-be94-4502-b6b8-8da2422a0154", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156149667200, "endTime": 316156149688900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "dff37d91-5eae-45b2-9508-24a072937b41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dff37d91-5eae-45b2-9508-24a072937b41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156149667200, "endTime": 316156149688900}, "additional": {"logType": "info", "children": [], "durationId": "d7a6376d-be94-4502-b6b8-8da2422a0154"}}, {"head": {"id": "0b37e68d-3741-44f0-9485-7322e67bc417", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156149801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e8fb12-2583-4a80-8320-f05e91631a0a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156151198800, "endTime": 316156151218200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "f975471d-3120-4e8f-b92e-fed87ecb3f79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f975471d-3120-4e8f-b92e-fed87ecb3f79", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156151198800, "endTime": 316156151218200}, "additional": {"logType": "info", "children": [], "durationId": "44e8fb12-2583-4a80-8320-f05e91631a0a"}}, {"head": {"id": "fee23c58-0f13-4696-9f94-5a92025a78c5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156151320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e9a6f6d-e8d7-49c8-b74f-f91e12195197", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156152713200, "endTime": 316156152733000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "a8c76542-1768-4cac-a2c2-90d5baee3e6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8c76542-1768-4cac-a2c2-90d5baee3e6a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156152713200, "endTime": 316156152733000}, "additional": {"logType": "info", "children": [], "durationId": "0e9a6f6d-e8d7-49c8-b74f-f91e12195197"}}, {"head": {"id": "5b7406a1-c0dc-491a-93c6-458422d61f17", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156152859100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b620f9-2cb0-4da7-8e61-5d01e5b57121", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156154392500, "endTime": 316156154416700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "68d5262d-2ff0-439b-b71c-0f89d0561bc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68d5262d-2ff0-439b-b71c-0f89d0561bc3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156154392500, "endTime": 316156154416700}, "additional": {"logType": "info", "children": [], "durationId": "92b620f9-2cb0-4da7-8e61-5d01e5b57121"}}, {"head": {"id": "1202fc96-a213-42f4-815e-1b8c3cd3caec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156154588000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c85dbfe0-2162-492d-9e2d-d68139dc03f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156156232100, "endTime": 316156156264700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "cc29c42c-6dab-4ee2-a310-978faf7dc864"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc29c42c-6dab-4ee2-a310-978faf7dc864", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156156232100, "endTime": 316156156264700}, "additional": {"logType": "info", "children": [], "durationId": "c85dbfe0-2162-492d-9e2d-d68139dc03f9"}}, {"head": {"id": "379c1d05-5c53-43ad-ace1-39090540c333", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156156427000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada71c4e-c827-4096-ae0d-3b5ea8044ba2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156158198500, "endTime": 316156158224400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "logId": "3f78bb21-f68c-4f17-81f5-c78455e5d721"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f78bb21-f68c-4f17-81f5-c78455e5d721", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156158198500, "endTime": 316156158224400}, "additional": {"logType": "info", "children": [], "durationId": "ada71c4e-c827-4096-ae0d-3b5ea8044ba2"}}, {"head": {"id": "17de2f6c-78e9-4209-a34e-abe5a34f4199", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156158360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "943395c7-c5bc-4b25-90cb-710cff450fb1", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316156181881800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e27d33a8-b834-44a9-a0bf-4510c9da5db3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158784917300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc15157f-72a6-4f05-9dc0-45d3d7214653", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158787201200, "endTime": 316158787235100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "94b10450-421a-4264-b035-e535ab856b56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94b10450-421a-4264-b035-e535ab856b56", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158787201200, "endTime": 316158787235100}, "additional": {"logType": "info", "children": [], "durationId": "dc15157f-72a6-4f05-9dc0-45d3d7214653", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "a47b34cf-1a3f-4516-b16f-80e27a35645d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158787409100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c595bec6-324b-49e1-a4d5-bff8834f128a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158790455400, "endTime": 316158790502700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "17cc6e82-191f-4430-8bd2-2a3109d3814d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17cc6e82-191f-4430-8bd2-2a3109d3814d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158790455400, "endTime": 316158790502700}, "additional": {"logType": "info", "children": [], "durationId": "c595bec6-324b-49e1-a4d5-bff8834f128a", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "541ab69b-30c0-4579-8494-bb802dda097a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158790742800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8039ef-a108-4772-933a-4c1043e898a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158794324400, "endTime": 316158794374200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "87288f86-8af6-41fe-b350-6a7e0937ca3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87288f86-8af6-41fe-b350-6a7e0937ca3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158794324400, "endTime": 316158794374200}, "additional": {"logType": "info", "children": [], "durationId": "7c8039ef-a108-4772-933a-4c1043e898a5", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "4ad21b40-fdb6-47e3-90a2-b6407106b6bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158794621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8d00a1-dea8-428e-a80f-e2494572acb5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158797454200, "endTime": 316158797492000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "e1b03980-8add-4f4d-90be-2d3da38b6697"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1b03980-8add-4f4d-90be-2d3da38b6697", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158797454200, "endTime": 316158797492000}, "additional": {"logType": "info", "children": [], "durationId": "ee8d00a1-dea8-428e-a80f-e2494572acb5", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "d935be17-8dc6-4d14-920e-ed3db038e96d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158797726200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db384f72-80a3-4f5b-8867-5bb38dba4228", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158800683000, "endTime": 316158800735400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "6a4d521e-6e1e-4681-9229-f5711157fbb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a4d521e-6e1e-4681-9229-f5711157fbb0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158800683000, "endTime": 316158800735400}, "additional": {"logType": "info", "children": [], "durationId": "db384f72-80a3-4f5b-8867-5bb38dba4228", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "bf1cbb07-c5ce-4436-a7cd-6de2bc27b4f1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158801002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5028fb-0755-4704-b61a-9007408f8f4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158803327100, "endTime": 316158803379600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "bc78c66a-9c41-4292-ace9-0810f23dbf8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc78c66a-9c41-4292-ace9-0810f23dbf8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158803327100, "endTime": 316158803379600}, "additional": {"logType": "info", "children": [], "durationId": "6c5028fb-0755-4704-b61a-9007408f8f4d", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "fe31311a-68cc-4d7d-9f85-7facbe30d038", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158803599100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd307e88-20b6-4866-a148-c6bb2b35284f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158806534100, "endTime": 316158806588400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "2e795455-6d21-4baf-a4f1-a362c4d376a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e795455-6d21-4baf-a4f1-a362c4d376a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158806534100, "endTime": 316158806588400}, "additional": {"logType": "info", "children": [], "durationId": "fd307e88-20b6-4866-a148-c6bb2b35284f", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "73b449fa-693d-4264-b973-0a8aad5a22ab", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158806945600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbefccef-78c6-44c9-8879-4ff6417f142e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158810377200, "endTime": 316158810423300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "15a69e06-7cae-43cb-8cde-d4293068a715"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15a69e06-7cae-43cb-8cde-d4293068a715", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158810377200, "endTime": 316158810423300}, "additional": {"logType": "info", "children": [], "durationId": "dbefccef-78c6-44c9-8879-4ff6417f142e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "8401f3f6-b022-4b0a-a6c5-cbd73c0a388d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158810614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47d5426-d46a-40ec-8386-3810808df81a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158813502300, "endTime": 316158813549900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "dd98be8b-1cb9-47ca-8fcb-372ceaf5e82e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd98be8b-1cb9-47ca-8fcb-372ceaf5e82e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158813502300, "endTime": 316158813549900}, "additional": {"logType": "info", "children": [], "durationId": "e47d5426-d46a-40ec-8386-3810808df81a", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "fd56026c-23df-4d8a-b6d8-f029d65fa814", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158813788200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcaa2ff-7630-4b0d-9862-f8d7d822b7ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158819227300, "endTime": 316158819283200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9a7b5111-37fa-4f2d-9f45-f5894e8535dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a7b5111-37fa-4f2d-9f45-f5894e8535dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158819227300, "endTime": 316158819283200}, "additional": {"logType": "info", "children": [], "durationId": "fdcaa2ff-7630-4b0d-9862-f8d7d822b7ab", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5b252122-6302-4549-92f1-be671acc6d89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158819991500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add7cf52-9800-4eb5-b617-234405f4b922", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158824828300, "endTime": 316158824879000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "09e0f8c7-215c-45b1-94bc-b2861d3ea852"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09e0f8c7-215c-45b1-94bc-b2861d3ea852", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158824828300, "endTime": 316158824879000}, "additional": {"logType": "info", "children": [], "durationId": "add7cf52-9800-4eb5-b617-234405f4b922", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "e7864a29-d265-4d67-b793-22a47be81108", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158825117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5cb720-04ef-4d08-a90f-edbaba17c277", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158830362900, "endTime": 316158830416700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "8ca33f5a-a258-4fe2-bbba-15ff0f956e4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ca33f5a-a258-4fe2-bbba-15ff0f956e4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158830362900, "endTime": 316158830416700}, "additional": {"logType": "info", "children": [], "durationId": "bc5cb720-04ef-4d08-a90f-edbaba17c277", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "ee667bb7-c977-4f58-a76f-38aa9ae3f6a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158830828800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2c4882-d897-4c2a-bde9-782e08071c3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158836227100, "endTime": 316158836283600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "a6c60b95-4473-4517-9f7f-286a1f3e76bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6c60b95-4473-4517-9f7f-286a1f3e76bb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158836227100, "endTime": 316158836283600}, "additional": {"logType": "info", "children": [], "durationId": "7d2c4882-d897-4c2a-bde9-782e08071c3e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "be64586c-d13f-4ef5-a074-98934d2deeb0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158836561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f98daa9-ac9f-4f79-9ba8-0a21e3220efd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158839670200, "endTime": 316158839724100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "524bb49b-ef3c-495c-b01c-3622fe1f4771"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "524bb49b-ef3c-495c-b01c-3622fe1f4771", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158839670200, "endTime": 316158839724100}, "additional": {"logType": "info", "children": [], "durationId": "3f98daa9-ac9f-4f79-9ba8-0a21e3220efd", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "eded1faf-6e4a-41cb-8b6c-a78bfc8424b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158839933700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fda4795-e913-4818-812e-f3e8dec87336", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158842592500, "endTime": 316158842637100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "8160d8ee-bdb0-41e8-a9e0-167da55d0a2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8160d8ee-bdb0-41e8-a9e0-167da55d0a2b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158842592500, "endTime": 316158842637100}, "additional": {"logType": "info", "children": [], "durationId": "2fda4795-e913-4818-812e-f3e8dec87336", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "1befd8be-ff4f-4962-aef2-66e8a464985f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158842863600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28e3a81b-665e-41f9-a0bf-a026e6719a59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158845499100, "endTime": 316158845536200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "af7e416b-d6a0-4564-9187-5b8f28937dd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af7e416b-d6a0-4564-9187-5b8f28937dd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158845499100, "endTime": 316158845536200}, "additional": {"logType": "info", "children": [], "durationId": "28e3a81b-665e-41f9-a0bf-a026e6719a59", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "f3e5c7b6-1b9e-4ead-a25c-834ab04540c5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158845737600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1b418f-683c-4d40-b6cd-b57351f18f50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158848125100, "endTime": 316158848169200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "e8b7f89e-35e4-4704-a7ef-9c5ed846aecb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8b7f89e-35e4-4704-a7ef-9c5ed846aecb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158848125100, "endTime": 316158848169200}, "additional": {"logType": "info", "children": [], "durationId": "6f1b418f-683c-4d40-b6cd-b57351f18f50", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "00d2a6d1-755f-4339-991d-fee338d173e9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158848396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09c2ade-a00c-47c2-8a09-0c1e02b85896", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158851337500, "endTime": 316158851380100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "26f8e08b-7452-4cb0-80e6-c1b9cc55837e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26f8e08b-7452-4cb0-80e6-c1b9cc55837e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158851337500, "endTime": 316158851380100}, "additional": {"logType": "info", "children": [], "durationId": "d09c2ade-a00c-47c2-8a09-0c1e02b85896", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "bbfbad12-9582-47f0-9c65-b3c99fdecadd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158851583000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397ad4f4-8427-4820-9c09-2fb588476e84", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158853780000, "endTime": 316158853826100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "29f37280-123c-4038-aaf8-2bf3854a1c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29f37280-123c-4038-aaf8-2bf3854a1c1b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158853780000, "endTime": 316158853826100}, "additional": {"logType": "info", "children": [], "durationId": "397ad4f4-8427-4820-9c09-2fb588476e84", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "6eff3bdb-604f-4acd-b826-3aa71bee3d22", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158854054600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89b69b5-6525-4049-82ac-84e50d392618", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158856333600, "endTime": 316158856368800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "4a04b2b7-84c4-4d92-ab92-8901b9b123d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a04b2b7-84c4-4d92-ab92-8901b9b123d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158856333600, "endTime": 316158856368800}, "additional": {"logType": "info", "children": [], "durationId": "b89b69b5-6525-4049-82ac-84e50d392618", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5e364cf1-7c17-478f-9a2a-2654ee361513", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158856542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c0d1d7-f73f-4e19-8e8c-3cdf1e771ebd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158859387100, "endTime": 316158859420600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9a2a85f0-3f23-45b7-854a-67c8e8551e77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a2a85f0-3f23-45b7-854a-67c8e8551e77", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158859387100, "endTime": 316158859420600}, "additional": {"logType": "info", "children": [], "durationId": "e2c0d1d7-f73f-4e19-8e8c-3cdf1e771ebd", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "4cf8a21c-55a7-4b22-ac7c-c287919c5e6c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158859624500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3086fcbc-c604-4749-a226-0b368264abf3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158862646000, "endTime": 316158862708200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "6bc8b30c-c7d8-4cee-b374-237977c1c4a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bc8b30c-c7d8-4cee-b374-237977c1c4a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158862646000, "endTime": 316158862708200}, "additional": {"logType": "info", "children": [], "durationId": "3086fcbc-c604-4749-a226-0b368264abf3", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "6af5e884-7f32-4481-9bb9-45ea81a24a96", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158862968000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb41db6-4148-476c-8d69-e2ea0fe0965c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158866011900, "endTime": 316158866051400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "6120c80a-0c51-4c53-a185-63cdca40fd7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6120c80a-0c51-4c53-a185-63cdca40fd7e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158866011900, "endTime": 316158866051400}, "additional": {"logType": "info", "children": [], "durationId": "dfb41db6-4148-476c-8d69-e2ea0fe0965c", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "f30c99b6-85b9-4695-a6d7-3b263510fbc5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158866297600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27417d59-f0dd-4cd4-a224-c0b4d44859c1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158869566600, "endTime": 316158869603400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "afb154f8-d8cf-442f-b9ed-e9c84d78251a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afb154f8-d8cf-442f-b9ed-e9c84d78251a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158869566600, "endTime": 316158869603400}, "additional": {"logType": "info", "children": [], "durationId": "27417d59-f0dd-4cd4-a224-c0b4d44859c1", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5ece1560-835f-4347-a408-c133d47bd4c6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158869916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1358bc9-0010-4e78-ad90-833315318788", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158872777900, "endTime": 316158872815300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "0594c5fe-c74d-45f1-b74b-8df216a58c17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0594c5fe-c74d-45f1-b74b-8df216a58c17", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158872777900, "endTime": 316158872815300}, "additional": {"logType": "info", "children": [], "durationId": "f1358bc9-0010-4e78-ad90-833315318788", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "83d6680e-1f28-4ead-a80e-2ab015c7e904", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158873013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ab150d-ab67-4f3a-ae21-9eba82df430e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158878493300, "endTime": 316158878574600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "864d0efa-b435-4b3c-be2e-601065656301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "864d0efa-b435-4b3c-be2e-601065656301", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158878493300, "endTime": 316158878574600}, "additional": {"logType": "info", "children": [], "durationId": "a8ab150d-ab67-4f3a-ae21-9eba82df430e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "b98fa2fe-5f39-4c9f-b9cb-cfad3e102d10", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158879027200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe5a638-5b6d-4fe8-9962-f1df90357103", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158881871200, "endTime": 316158881915000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "3119b291-c91d-4ebf-b673-513a4276af8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3119b291-c91d-4ebf-b673-513a4276af8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158881871200, "endTime": 316158881915000}, "additional": {"logType": "info", "children": [], "durationId": "bbe5a638-5b6d-4fe8-9962-f1df90357103", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "8f439b4e-835a-4c74-ac7a-e410d4b429c7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158882146000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca41d2c-0836-4983-9edd-5e1c2a46893b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158884829200, "endTime": 316158884872700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "4731c3ee-994f-447d-add8-b4432ae<PERSON>da9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4731c3ee-994f-447d-add8-b4432ae<PERSON>da9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158884829200, "endTime": 316158884872700}, "additional": {"logType": "info", "children": [], "durationId": "aca41d2c-0836-4983-9edd-5e1c2a46893b", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5ebbec61-cc2f-4e96-b79d-3c30150512d6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158885090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9b8c78-575d-49c3-82b3-888cc0cb9f9e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158887750700, "endTime": 316158887791900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "971fc9f8-d7c9-4029-89c4-65e19fd8bb5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "971fc9f8-d7c9-4029-89c4-65e19fd8bb5a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158887750700, "endTime": 316158887791900}, "additional": {"logType": "info", "children": [], "durationId": "ce9b8c78-575d-49c3-82b3-888cc0cb9f9e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "bd6bc409-92b8-43fd-9dc7-7f788991c6f3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158888006100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2eb69b5-9048-4627-9efd-3fc8ee9d2c53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158890811500, "endTime": 316158890868900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "c9a78c56-4d9a-404b-839d-96ac7547b843"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9a78c56-4d9a-404b-839d-96ac7547b843", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158890811500, "endTime": 316158890868900}, "additional": {"logType": "info", "children": [], "durationId": "b2eb69b5-9048-4627-9efd-3fc8ee9d2c53", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "0db3ec71-e8ab-45b1-ab2a-8f2b7db284f0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158891174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f20a45-a470-477e-b6f9-fbe4f77bf05e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158894137300, "endTime": 316158894237600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "5519c978-5e21-4141-b7d8-9fc8918d1033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5519c978-5e21-4141-b7d8-9fc8918d1033", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158894137300, "endTime": 316158894237600}, "additional": {"logType": "info", "children": [], "durationId": "a0f20a45-a470-477e-b6f9-fbe4f77bf05e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "d23e7c80-6f65-409d-a6ec-fda428d9e60b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158894476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117680cb-bd09-4617-a960-5c446c44e6d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158897611300, "endTime": 316158897654300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "7dfdc365-aba7-4fa3-82b9-6ee7327767ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dfdc365-aba7-4fa3-82b9-6ee7327767ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158897611300, "endTime": 316158897654300}, "additional": {"logType": "info", "children": [], "durationId": "117680cb-bd09-4617-a960-5c446c44e6d7", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "6ee20d68-98e2-4108-935c-9f98a3367f46", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158897895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "136fc0c3-42bf-4184-911c-1309c8b2d752", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158901221500, "endTime": 316158901260900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9621525a-84cf-4ec3-9ec5-a7bedb7f8a4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9621525a-84cf-4ec3-9ec5-a7bedb7f8a4f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158901221500, "endTime": 316158901260900}, "additional": {"logType": "info", "children": [], "durationId": "136fc0c3-42bf-4184-911c-1309c8b2d752", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "25202642-9652-46e3-ac1b-6bf68c8dd589", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158901446100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0113057f-8bec-4adc-a134-4a37c39c7f72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158903783200, "endTime": 316158903825100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "130a6f66-0223-42cf-877e-e958a17ac870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "130a6f66-0223-42cf-877e-e958a17ac870", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158903783200, "endTime": 316158903825100}, "additional": {"logType": "info", "children": [], "durationId": "0113057f-8bec-4adc-a134-4a37c39c7f72", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "2b106d16-7d8c-42fd-a196-d0eb9d747eb5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158904134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123ba9c8-e426-498f-b009-4a5ed892d875", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158910404400, "endTime": 316158910444800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "d944f56a-df79-4ac6-a38f-29d14fe9be1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d944f56a-df79-4ac6-a38f-29d14fe9be1c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158910404400, "endTime": 316158910444800}, "additional": {"logType": "info", "children": [], "durationId": "123ba9c8-e426-498f-b009-4a5ed892d875", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "a81de958-53fc-4179-957c-87988f727640", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158910674300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01215ae5-3a9a-4302-af90-1320dc6ccb34", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158913447800, "endTime": 316158913506000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "c2e2421c-8ae4-4a33-8117-eed9d34d6b0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2e2421c-8ae4-4a33-8117-eed9d34d6b0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158913447800, "endTime": 316158913506000}, "additional": {"logType": "info", "children": [], "durationId": "01215ae5-3a9a-4302-af90-1320dc6ccb34", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5b2c6b17-379e-4035-8693-2cc619275a28", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158913756300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a42c54f-ed52-4099-b3f3-8354e42bb337", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158917193200, "endTime": 316158917251400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "6904177a-acc6-47fa-b449-56f92469b5ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6904177a-acc6-47fa-b449-56f92469b5ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158917193200, "endTime": 316158917251400}, "additional": {"logType": "info", "children": [], "durationId": "4a42c54f-ed52-4099-b3f3-8354e42bb337", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "f470e0ad-8562-4b11-a5ca-053d621819db", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158917566000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7185e317-56a5-44ac-864b-d165835ee2e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158920621900, "endTime": 316158920674800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "4337a089-f938-4c6e-86e0-f4e853c33cf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4337a089-f938-4c6e-86e0-f4e853c33cf7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158920621900, "endTime": 316158920674800}, "additional": {"logType": "info", "children": [], "durationId": "7185e317-56a5-44ac-864b-d165835ee2e9", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "ed425718-9404-4988-9eaf-081c1e5b6b38", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158920910000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e09d4b6-6230-4664-8202-8118814fc06a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158923815700, "endTime": 316158923862800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "e2974b66-884f-4ef7-9265-e8b11f33cdba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2974b66-884f-4ef7-9265-e8b11f33cdba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158923815700, "endTime": 316158923862800}, "additional": {"logType": "info", "children": [], "durationId": "0e09d4b6-6230-4664-8202-8118814fc06a", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "f589f2cd-2413-4e8f-aac8-02a8caadc139", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158924094200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff1a3804-1ac4-4364-87df-de3b15cd85ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158927602300, "endTime": 316158927654100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9496908a-7e29-4f80-b09d-173161563568"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9496908a-7e29-4f80-b09d-173161563568", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158927602300, "endTime": 316158927654100}, "additional": {"logType": "info", "children": [], "durationId": "ff1a3804-1ac4-4364-87df-de3b15cd85ae", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "2b14f41d-b113-4df1-b9f4-4129bbbe72c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158927986600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abcb2198-da6e-4ad7-97f8-cec9cb136983", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158931971300, "endTime": 316158932022700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "3889dfdd-154a-48c9-9fd5-d4c2dbed6de6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3889dfdd-154a-48c9-9fd5-d4c2dbed6de6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158931971300, "endTime": 316158932022700}, "additional": {"logType": "info", "children": [], "durationId": "abcb2198-da6e-4ad7-97f8-cec9cb136983", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "c76b350d-9d9a-457b-83fe-80fe9bb6f737", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158932339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf80f6b-8091-40b7-9734-9357fdbc11e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158935436500, "endTime": 316158935490900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "c1c51f14-7fea-4856-9b27-764ac0088dca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1c51f14-7fea-4856-9b27-764ac0088dca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158935436500, "endTime": 316158935490900}, "additional": {"logType": "info", "children": [], "durationId": "eaf80f6b-8091-40b7-9734-9357fdbc11e8", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "8232606d-d154-4825-b92c-7564d2d0742c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158935756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c82715-d022-4662-b3dd-1c0a4be9d195", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158940937300, "endTime": 316158940985300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "440d26b2-8d2f-4136-a2a2-ebf5d65b71be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "440d26b2-8d2f-4136-a2a2-ebf5d65b71be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158940937300, "endTime": 316158940985300}, "additional": {"logType": "info", "children": [], "durationId": "76c82715-d022-4662-b3dd-1c0a4be9d195", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "75108657-c3b0-4560-aa81-45824f2c9027", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158941276300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cfef17-8140-475f-b41d-b73773b8af4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158945875200, "endTime": 316158945913100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "29e05cd6-dbbd-4261-a5f5-25b6a52a40af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29e05cd6-dbbd-4261-a5f5-25b6a52a40af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158945875200, "endTime": 316158945913100}, "additional": {"logType": "info", "children": [], "durationId": "c5cfef17-8140-475f-b41d-b73773b8af4d", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "7e25bd8f-8ffb-4d43-9758-e27a8212cd4c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158946104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65809eb0-8328-4191-a6e4-5345740f68bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158948822300, "endTime": 316158948869300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "1ce8fa43-fada-426b-90d4-eb6846be6327"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ce8fa43-fada-426b-90d4-eb6846be6327", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158948822300, "endTime": 316158948869300}, "additional": {"logType": "info", "children": [], "durationId": "65809eb0-8328-4191-a6e4-5345740f68bc", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "eae8b93c-83a8-414a-b3e0-4f58a732bd9d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158949119400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92109b7f-a2a5-42ab-bde0-161a419a6800", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158953823700, "endTime": 316158953876200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9b342335-162c-4f8a-911d-35ac38637309"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b342335-162c-4f8a-911d-35ac38637309", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158953823700, "endTime": 316158953876200}, "additional": {"logType": "info", "children": [], "durationId": "92109b7f-a2a5-42ab-bde0-161a419a6800", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "a27e5027-928d-4887-b714-753230912475", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158954136800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6756e68d-c4cf-4a3a-9110-b4137dee2c2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158957313000, "endTime": 316158957364700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "758cd7da-823d-4303-97c7-7eac3030b71e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "758cd7da-823d-4303-97c7-7eac3030b71e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158957313000, "endTime": 316158957364700}, "additional": {"logType": "info", "children": [], "durationId": "6756e68d-c4cf-4a3a-9110-b4137dee2c2c", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "fd6098b0-40f8-46d9-b106-b69e695638ac", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158957651200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb4b277-dd19-4b01-95ca-0b05534eb118", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158960769700, "endTime": 316158960819000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "4a4a0a54-ff09-4646-85d7-f3b3c9163485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a4a0a54-ff09-4646-85d7-f3b3c9163485", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158960769700, "endTime": 316158960819000}, "additional": {"logType": "info", "children": [], "durationId": "5fb4b277-dd19-4b01-95ca-0b05534eb118", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "aa465f17-d80b-4cad-961d-45b5fd054df8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158961084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6a50bd-53ae-4ea9-b131-3e4048b9efdd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158964140500, "endTime": 316158964189800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "0684fcf9-d6db-43fc-ab3b-6d3687bbff3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0684fcf9-d6db-43fc-ab3b-6d3687bbff3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158964140500, "endTime": 316158964189800}, "additional": {"logType": "info", "children": [], "durationId": "cd6a50bd-53ae-4ea9-b131-3e4048b9efdd", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "e4405dfd-ca01-4b21-bbad-345dde84687f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158964400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1768c45-a945-46ae-92c5-14644a4e337e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158967047100, "endTime": 316158967103300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "75a62569-2296-428d-96f9-ed2c429120e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75a62569-2296-428d-96f9-ed2c429120e4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158967047100, "endTime": 316158967103300}, "additional": {"logType": "info", "children": [], "durationId": "b1768c45-a945-46ae-92c5-14644a4e337e", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "c67580bc-c4f2-4d12-841d-9ef8b9abfbba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158967407200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea7ef6ca-c8fc-46af-85b1-48b522b9aff5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158970503100, "endTime": 316158970544000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "9ed6dd17-4031-4409-a6f6-428c6a42cc08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ed6dd17-4031-4409-a6f6-428c6a42cc08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158970503100, "endTime": 316158970544000}, "additional": {"logType": "info", "children": [], "durationId": "ea7ef6ca-c8fc-46af-85b1-48b522b9aff5", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "5d79002b-1eae-4b5d-a397-fb137bd7fe7e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158970776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27d2b55-6b9d-4d84-899c-0fab82587d9c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158973891500, "endTime": 316158973946300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "38cf6ccb-f680-421f-a1f1-a43005ea3378"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38cf6ccb-f680-421f-a1f1-a43005ea3378", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158973891500, "endTime": 316158973946300}, "additional": {"logType": "info", "children": [], "durationId": "b27d2b55-6b9d-4d84-899c-0fab82587d9c", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "4de8b0ef-e613-4f94-8885-4bb0b097707d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158974226300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb32c97c-e0f6-4a31-ae60-bb278a4fc112", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158977378400, "endTime": 316158977433500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "828e8eda-66da-430a-9da8-af28d39c17c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "828e8eda-66da-430a-9da8-af28d39c17c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158977378400, "endTime": 316158977433500}, "additional": {"logType": "info", "children": [], "durationId": "fb32c97c-e0f6-4a31-ae60-bb278a4fc112", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "ed2b5514-f0f8-410b-9e8c-7a8b07b39557", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158977691900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f5c9d4b-d61f-4636-bbea-68163272550d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158981124900, "endTime": 316158981174600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "05731b17-4135-46bb-8ec2-3ef04bfc1c7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05731b17-4135-46bb-8ec2-3ef04bfc1c7d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158981124900, "endTime": 316158981174600}, "additional": {"logType": "info", "children": [], "durationId": "4f5c9d4b-d61f-4636-bbea-68163272550d", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "10c36d37-8919-49f8-80e4-6fef11da0fa4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158981458000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab8524e-39fb-4a49-82b5-31612ae098b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158985708100, "endTime": 316158985758500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "946eba9c-d51e-4393-9a10-6447cc4f074e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "946eba9c-d51e-4393-9a10-6447cc4f074e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158985708100, "endTime": 316158985758500}, "additional": {"logType": "info", "children": [], "durationId": "9ab8524e-39fb-4a49-82b5-31612ae098b3", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "cc4e2a13-3b9f-4411-b266-cd0f99de794f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158986012200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70409534-fae3-4a21-b8bc-cf925b5d175b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158989159200, "endTime": 316158989423900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "998d1203-31ef-43f0-b768-3d3c511001c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "998d1203-31ef-43f0-b768-3d3c511001c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158989159200, "endTime": 316158989423900}, "additional": {"logType": "info", "children": [], "durationId": "70409534-fae3-4a21-b8bc-cf925b5d175b", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "643e7ad4-bbce-4400-a8c0-e8cb95fdcba2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158989706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce1c9814-4f26-417e-9034-4b63d361d902", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158994281400, "endTime": 316158994328100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "c8919ebe-1697-4ef1-a4da-f404bcf40215"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8919ebe-1697-4ef1-a4da-f404bcf40215", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158994281400, "endTime": 316158994328100}, "additional": {"logType": "info", "children": [], "durationId": "ce1c9814-4f26-417e-9034-4b63d361d902", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "8f44fc04-e01b-451e-ab79-d9a6bd4c514c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158994550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3046ae8-cfc5-4c32-b06d-8bc8b23a087a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158997540400, "endTime": 316158997574800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "202101f5-71ab-43a4-a037-2cc730b61029"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "202101f5-71ab-43a4-a037-2cc730b61029", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158997540400, "endTime": 316158997574800}, "additional": {"logType": "info", "children": [], "durationId": "a3046ae8-cfc5-4c32-b06d-8bc8b23a087a", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "b14f3f7f-20de-46ff-b991-1b8a5d0be1c0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316158997823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7120b80f-40b5-43b6-a16a-446693cb5a0f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159000813100, "endTime": 316159000857400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "65c026e5-5a86-4da4-8493-10d4e3cb9d3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65c026e5-5a86-4da4-8493-10d4e3cb9d3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159000813100, "endTime": 316159000857400}, "additional": {"logType": "info", "children": [], "durationId": "7120b80f-40b5-43b6-a16a-446693cb5a0f", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "77db120e-4263-4902-b80a-98ce6aa3af81", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159001099800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "657760de-94d7-4cfd-8c39-87401d848d44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159004031200, "endTime": 316159004076400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "a26cfb95-326d-4d34-8033-dde47c2bffa7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a26cfb95-326d-4d34-8033-dde47c2bffa7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159004031200, "endTime": 316159004076400}, "additional": {"logType": "info", "children": [], "durationId": "657760de-94d7-4cfd-8c39-87401d848d44", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "e19345ae-58e7-4f4e-9ac3-57e3193578bb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159004327000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7269b0ea-c9b8-4e3b-b4a1-a8905a640218", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159007269100, "endTime": 316159007316600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "1f6c901a-0e41-42ad-959c-44f1dbb19f42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f6c901a-0e41-42ad-959c-44f1dbb19f42", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159007269100, "endTime": 316159007316600}, "additional": {"logType": "info", "children": [], "durationId": "7269b0ea-c9b8-4e3b-b4a1-a8905a640218", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "202ba48d-0f67-4a75-b1ce-538ce272bad8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159007757300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf224cd-9f0c-45f2-9743-41e77d0bb662", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159011209500, "endTime": 316159011288400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "4092675d-b9b7-411b-9016-a8f7591439fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4092675d-b9b7-411b-9016-a8f7591439fb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159011209500, "endTime": 316159011288400}, "additional": {"logType": "info", "children": [], "durationId": "acf224cd-9f0c-45f2-9743-41e77d0bb662", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "448d7074-3397-457e-856c-c8109cef1dec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159011552300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2a8bf61-d7ce-4130-8503-7971a8368eaf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159014805100, "endTime": 316159014852600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "c78dc697-bcdf-42b1-8f42-b4eba24527ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c78dc697-bcdf-42b1-8f42-b4eba24527ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159014805100, "endTime": 316159014852600}, "additional": {"logType": "info", "children": [], "durationId": "f2a8bf61-d7ce-4130-8503-7971a8368eaf", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "03a8942a-08a5-4d95-98df-b104a12c974e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159015097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccacdc21-5271-417c-91dd-1c1568a9e82d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159018027700, "endTime": 316159018077100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "a7a0cc90-a9ec-474c-9c85-12f0f1cc4fd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7a0cc90-a9ec-474c-9c85-12f0f1cc4fd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159018027700, "endTime": 316159018077100}, "additional": {"logType": "info", "children": [], "durationId": "ccacdc21-5271-417c-91dd-1c1568a9e82d", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "e01c1209-7c1d-4c0d-bdc8-2532b13dfa3a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159018382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbbd1a0-e747-4811-867f-71d6838ef57d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159021415000, "endTime": 316159021463000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "6c4b5bbb-fac3-4205-95c0-e7bcb7833da9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c4b5bbb-fac3-4205-95c0-e7bcb7833da9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159021415000, "endTime": 316159021463000}, "additional": {"logType": "info", "children": [], "durationId": "9fbbd1a0-e747-4811-867f-71d6838ef57d", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "15f1c489-620a-46ae-9bde-2de99ad80f2f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159021738600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dfb0d1a-d936-45af-ab15-87f36fff3adf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159026813700, "endTime": 316159026867700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "e6c31e3b-50fc-48af-a7ca-3a55cff38a60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6c31e3b-50fc-48af-a7ca-3a55cff38a60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159026813700, "endTime": 316159026867700}, "additional": {"logType": "info", "children": [], "durationId": "4dfb0d1a-d936-45af-ab15-87f36fff3adf", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "67cac48d-79d5-447e-843c-1712c18b63b2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159027138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe06586-a9c7-4b22-8f32-c6120d46e3c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159031149300, "endTime": 316159031199800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "d05e721f-11c3-4308-a0d5-0a477dcdb0fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d05e721f-11c3-4308-a0d5-0a477dcdb0fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159031149300, "endTime": 316159031199800}, "additional": {"logType": "info", "children": [], "durationId": "5fe06586-a9c7-4b22-8f32-c6120d46e3c8", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "c63b31fc-fe92-46db-a816-35be01d0fd96", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159428148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ebfa12-dce6-4c5b-9584-5b7eca92b612", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159430814200, "endTime": 316159430851000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "e34ba86b-0455-4a1a-9c68-d29fdf5333c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e34ba86b-0455-4a1a-9c68-d29fdf5333c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159430814200, "endTime": 316159430851000}, "additional": {"logType": "info", "children": [], "durationId": "94ebfa12-dce6-4c5b-9584-5b7eca92b612", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "085b6691-c974-48eb-ab24-2729665727d6", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159443811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52c61e4-9586-410d-ad38-2b6f3d083b54", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159446559300, "endTime": 316159446592400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "logId": "16dcd867-68ae-4e35-8ea2-a91890e4a2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16dcd867-68ae-4e35-8ea2-a91890e4a2fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159446559300, "endTime": 316159446592400}, "additional": {"logType": "info", "children": [], "durationId": "e52c61e4-9586-410d-ad38-2b6f3d083b54", "parent": "f69bb975-ef4f-4781-8c47-fa663f15885c"}}, {"head": {"id": "f69bb975-ef4f-4781-8c47-fa663f15885c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker10", "startTime": 316141423885100, "endTime": 316159446793100}, "additional": {"logType": "error", "children": ["cc89a04f-9a2e-464a-b03f-568869a196c5", "94b10450-421a-4264-b035-e535ab856b56", "17cc6e82-191f-4430-8bd2-2a3109d3814d", "87288f86-8af6-41fe-b350-6a7e0937ca3e", "e1b03980-8add-4f4d-90be-2d3da38b6697", "6a4d521e-6e1e-4681-9229-f5711157fbb0", "bc78c66a-9c41-4292-ace9-0810f23dbf8d", "2e795455-6d21-4baf-a4f1-a362c4d376a3", "15a69e06-7cae-43cb-8cde-d4293068a715", "dd98be8b-1cb9-47ca-8fcb-372ceaf5e82e", "9a7b5111-37fa-4f2d-9f45-f5894e8535dd", "09e0f8c7-215c-45b1-94bc-b2861d3ea852", "8ca33f5a-a258-4fe2-bbba-15ff0f956e4d", "a6c60b95-4473-4517-9f7f-286a1f3e76bb", "524bb49b-ef3c-495c-b01c-3622fe1f4771", "8160d8ee-bdb0-41e8-a9e0-167da55d0a2b", "af7e416b-d6a0-4564-9187-5b8f28937dd8", "e8b7f89e-35e4-4704-a7ef-9c5ed846aecb", "26f8e08b-7452-4cb0-80e6-c1b9cc55837e", "29f37280-123c-4038-aaf8-2bf3854a1c1b", "4a04b2b7-84c4-4d92-ab92-8901b9b123d8", "9a2a85f0-3f23-45b7-854a-67c8e8551e77", "6bc8b30c-c7d8-4cee-b374-237977c1c4a4", "6120c80a-0c51-4c53-a185-63cdca40fd7e", "afb154f8-d8cf-442f-b9ed-e9c84d78251a", "0594c5fe-c74d-45f1-b74b-8df216a58c17", "864d0efa-b435-4b3c-be2e-601065656301", "3119b291-c91d-4ebf-b673-513a4276af8d", "4731c3ee-994f-447d-add8-b4432ae<PERSON>da9", "971fc9f8-d7c9-4029-89c4-65e19fd8bb5a", "c9a78c56-4d9a-404b-839d-96ac7547b843", "5519c978-5e21-4141-b7d8-9fc8918d1033", "7dfdc365-aba7-4fa3-82b9-6ee7327767ce", "9621525a-84cf-4ec3-9ec5-a7bedb7f8a4f", "130a6f66-0223-42cf-877e-e958a17ac870", "d944f56a-df79-4ac6-a38f-29d14fe9be1c", "c2e2421c-8ae4-4a33-8117-eed9d34d6b0b", "6904177a-acc6-47fa-b449-56f92469b5ce", "4337a089-f938-4c6e-86e0-f4e853c33cf7", "e2974b66-884f-4ef7-9265-e8b11f33cdba", "9496908a-7e29-4f80-b09d-173161563568", "3889dfdd-154a-48c9-9fd5-d4c2dbed6de6", "c1c51f14-7fea-4856-9b27-764ac0088dca", "440d26b2-8d2f-4136-a2a2-ebf5d65b71be", "29e05cd6-dbbd-4261-a5f5-25b6a52a40af", "1ce8fa43-fada-426b-90d4-eb6846be6327", "9b342335-162c-4f8a-911d-35ac38637309", "758cd7da-823d-4303-97c7-7eac3030b71e", "4a4a0a54-ff09-4646-85d7-f3b3c9163485", "0684fcf9-d6db-43fc-ab3b-6d3687bbff3a", "75a62569-2296-428d-96f9-ed2c429120e4", "9ed6dd17-4031-4409-a6f6-428c6a42cc08", "38cf6ccb-f680-421f-a1f1-a43005ea3378", "828e8eda-66da-430a-9da8-af28d39c17c0", "05731b17-4135-46bb-8ec2-3ef04bfc1c7d", "946eba9c-d51e-4393-9a10-6447cc4f074e", "998d1203-31ef-43f0-b768-3d3c511001c5", "c8919ebe-1697-4ef1-a4da-f404bcf40215", "202101f5-71ab-43a4-a037-2cc730b61029", "65c026e5-5a86-4da4-8493-10d4e3cb9d3a", "a26cfb95-326d-4d34-8033-dde47c2bffa7", "1f6c901a-0e41-42ad-959c-44f1dbb19f42", "4092675d-b9b7-411b-9016-a8f7591439fb", "c78dc697-bcdf-42b1-8f42-b4eba24527ac", "a7a0cc90-a9ec-474c-9c85-12f0f1cc4fd7", "6c4b5bbb-fac3-4205-95c0-e7bcb7833da9", "e6c31e3b-50fc-48af-a7ca-3a55cff38a60", "d05e721f-11c3-4308-a0d5-0a477dcdb0fe", "e34ba86b-0455-4a1a-9c68-d29fdf5333c0", "16dcd867-68ae-4e35-8ea2-a91890e4a2fe"], "durationId": "9ec58c12-cadc-49fe-b756-8dfda5167dcc", "parent": "859f3d22-716f-435c-aa3b-cf99fc855b7a"}}, {"head": {"id": "5e86d89c-f0be-419e-9ea8-ee550dc0721a", "name": "default@PreviewArkTS watch work[10] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159447502200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859f3d22-716f-435c-aa3b-cf99fc855b7a", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316141395382800, "endTime": 316159448101300}, "additional": {"logType": "error", "children": ["f69bb975-ef4f-4781-8c47-fa663f15885c"], "durationId": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92"}}, {"head": {"id": "6f18b156-6a41-4136-8f20-aa5e1de82248", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159448604500}, "additional": {"logType": "debug", "children": [], "durationId": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92"}}, {"head": {"id": "0f3e3bf3-4f9b-4695-a4f1-788841ad19f4", "name": "ERROR: stacktrace = Error: Compilation failed\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159451527800}, "additional": {"logType": "debug", "children": [], "durationId": "c8ffc5c4-a93f-435e-bff6-ecef77c32f92"}}, {"head": {"id": "a840ff84-4bf4-44d1-a751-212075d5f2cc", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159467591900, "endTime": 316159467717000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5340c5d7-6800-4b0f-bb29-338aa5c4e897", "logId": "c86265c6-2d60-4ae0-9632-1998c6397f1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c86265c6-2d60-4ae0-9632-1998c6397f1a", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159467591900, "endTime": 316159467717000}, "additional": {"logType": "info", "children": [], "durationId": "a840ff84-4bf4-44d1-a751-212075d5f2cc"}}, {"head": {"id": "67c05f8b-c857-4b0b-b04b-60f377825f63", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316140878153200, "endTime": 316159468268500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "d94036eb-c390-497e-94ce-3193d51f62e1", "name": "BUILD FAILED in 18 s 590 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159468807300}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "2da97ca2-de8e-4836-8bc8-bf00b6d72a53", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159469384500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b249ae3-9b12-4d5d-a4a2-d8e1fba8427b", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159469650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c8cd75-09c6-4e93-9833-9797157fd835", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159469943400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94cee755-242c-46c1-b313-ee7cc0e25fde", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159470199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd394bcf-eaeb-4525-ad23-17f02aa6a7ba", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159470443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00601064-ddeb-4b6f-8d35-2b97006fc436", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159470682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3e73a9-4973-4a02-96a2-27502f1c46d6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159470932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9305dbd-785e-4cdb-a616-f4fa825de3d2", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159471180600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92c7f8e-aeca-4f74-a000-f9344c9ff35f", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159471414800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16c3238-0964-4500-9381-f220fdc5a988", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159471659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5310016c-c8ea-4000-a9d2-79f11c7aef88", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159482263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf4f59f-7889-4f3d-9f68-d718f85d1ffa", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159485581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9451ba12-6a6c-4974-9dee-e673a228a83c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159486594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "244424ae-ce7f-4de5-9c46-6a2d3e73b33a", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159487399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196297ca-14aa-4fc7-a966-44554ac86321", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159489536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e645ea-56ef-4796-ac6d-5bec273b444c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159519411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03b49c6-3fa8-4bee-9d60-c2e307f9ad77", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159520327800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3efaa7-1cfd-499c-b06f-8c881d1ac561", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159521116600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d02e6d-79f0-4685-b679-736b2fbe37ef", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159522183200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8b3c7d-4687-444c-9fc4-e3f23aac1b44", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:52 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316159523350400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}