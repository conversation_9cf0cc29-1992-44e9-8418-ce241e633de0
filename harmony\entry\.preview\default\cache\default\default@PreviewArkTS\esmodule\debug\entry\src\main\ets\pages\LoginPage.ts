if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LoginPage_Params {
    username?: string;
    password?: string;
    verificationCode?: string;
    isLoading?: boolean;
    captchaCode?: string;
    captcha?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import type { UserLoginRequest } from '../common/types/index';
import http from "@ohos:net.http";
import type { BusinessError } from "@ohos:base";
// Spring Boot响应接口
interface SpringBootResponse {
    code: number;
    msg: string;
    data: string;
}
class LoginPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__username = new ObservedPropertySimplePU('admin', this, "username");
        this.__password = new ObservedPropertySimplePU('123456', this, "password");
        this.__verificationCode = new ObservedPropertySimplePU('', this, "verificationCode");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__captchaCode = new ObservedPropertySimplePU('9889', this, "captchaCode");
        this.__captcha = new ObservedPropertySimplePU('1234', this, "captcha");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LoginPage_Params) {
        if (params.username !== undefined) {
            this.username = params.username;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.verificationCode !== undefined) {
            this.verificationCode = params.verificationCode;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.captchaCode !== undefined) {
            this.captchaCode = params.captchaCode;
        }
        if (params.captcha !== undefined) {
            this.captcha = params.captcha;
        }
    }
    updateStateVars(params: LoginPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__username.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__verificationCode.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__captchaCode.purgeDependencyOnElmtId(rmElmtId);
        this.__captcha.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__username.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__verificationCode.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__captchaCode.aboutToBeDeleted();
        this.__captcha.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __username: ObservedPropertySimplePU<string>; // 默认用户名，方便测试
    get username() {
        return this.__username.get();
    }
    set username(newValue: string) {
        this.__username.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>; // 默认密码，方便测试
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __verificationCode: ObservedPropertySimplePU<string>;
    get verificationCode() {
        return this.__verificationCode.get();
    }
    set verificationCode(newValue: string) {
        this.__verificationCode.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __captchaCode: ObservedPropertySimplePU<string>; // 模拟验证码
    get captchaCode() {
        return this.__captchaCode.get();
    }
    set captchaCode(newValue: string) {
        this.__captchaCode.set(newValue);
    }
    private __captcha: ObservedPropertySimplePU<string>; // 验证码输入
    get captcha() {
        return this.__captcha.get();
    }
    set captcha(newValue: string) {
        this.__captcha.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(28:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFE4E1');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景渐变容器
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(30:7)", "entry");
            // 背景渐变容器
            Column.width('90%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Logo和标题区域
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(32:9)", "entry");
            // Logo和标题区域
            Column.margin({ bottom: 60 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Logo图标 (圆形头像样式)
            Image.create({ "id": 16777221, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/LoginPage.ets(34:11)", "entry");
            // Logo图标 (圆形头像样式)
            Image.width(80);
            // Logo图标 (圆形头像样式)
            Image.height(80);
            // Logo图标 (圆形头像样式)
            Image.borderRadius(40);
            // Logo图标 (圆形头像样式)
            Image.border({ width: 3, color: '#FF6B6B' });
            // Logo图标 (圆形头像样式)
            Image.margin({ top: 80 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 系统标题
            Text.create('电子钱包交易系统');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(42:11)", "entry");
            // 系统标题
            Text.fontSize(24);
            // 系统标题
            Text.fontWeight(FontWeight.Medium);
            // 系统标题
            Text.fontColor('#333333');
        }, Text);
        // 系统标题
        Text.pop();
        // Logo和标题区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录表单卡片
            Column.create({ space: 20 });
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(50:9)", "entry");
            // 登录表单卡片
            Column.width('100%');
            // 登录表单卡片
            Column.padding(24);
            // 登录表单卡片
            Column.backgroundColor(Color.White);
            // 登录表单卡片
            Column.borderRadius(12);
            // 登录表单卡片
            Column.shadow({ radius: 10, color: '#00000010', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户名输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(53:11)", "entry");
            // 用户名输入框
            Column.width('100%');
            // 用户名输入框
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(54:13)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('* ');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(55:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户名');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(58:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入用户名称', text: this.username });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(65:13)", "entry");
            TextInput.width('100%');
            TextInput.height(48);
            TextInput.backgroundColor(Color.White);
            TextInput.borderRadius(6);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.padding({ left: 12, right: 12 });
            TextInput.fontSize(16);
            TextInput.onChange((value: string) => {
                this.username = value;
            });
        }, TextInput);
        // 用户名输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 密码输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(83:11)", "entry");
            // 密码输入框
            Column.width('100%');
            // 密码输入框
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(84:13)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('* ');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(85:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('密码');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(88:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入密码', text: this.password });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(95:13)", "entry");
            TextInput.width('100%');
            TextInput.height(48);
            TextInput.backgroundColor(Color.White);
            TextInput.borderRadius(6);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.padding({ left: 12, right: 12 });
            TextInput.fontSize(16);
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.password = value;
            });
        }, TextInput);
        // 密码输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 验证码输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(114:11)", "entry");
            // 验证码输入框
            Column.width('100%');
            // 验证码输入框
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(115:13)", "entry");
            Row.alignSelf(ItemAlign.Start);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('* ');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(116:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF4444');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证码');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(119:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(126:13)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入验证码', text: this.verificationCode });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(127:15)", "entry");
            TextInput.layoutWeight(1);
            TextInput.height(48);
            TextInput.backgroundColor(Color.White);
            TextInput.borderRadius(6);
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.padding({ left: 12, right: 12 });
            TextInput.fontSize(16);
            TextInput.type(InputType.Number);
            TextInput.maxLength(4);
            TextInput.onChange((value: string) => {
                this.verificationCode = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.verificationCode.length}/4`);
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(141:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('获取验证码');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(146:15)", "entry");
            Button.height(48);
            Button.backgroundColor('#F0F0F0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.borderRadius(6);
            Button.border({ width: 1, color: '#E0E0E0' });
            Button.onClick(() => {
                this.generateCaptcha();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 验证码输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录按钮
            Button.createWithLabel(this.isLoading ? '登录中...' : '登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(165:11)", "entry");
            // 登录按钮
            Button.width('100%');
            // 登录按钮
            Button.height(48);
            // 登录按钮
            Button.backgroundColor(this.isLoginEnabled() ? '#4A90E2' : '#CCCCCC');
            // 登录按钮
            Button.borderRadius(6);
            // 登录按钮
            Button.fontSize(16);
            // 登录按钮
            Button.fontColor(Color.White);
            // 登录按钮
            Button.margin({ top: 20 });
            // 登录按钮
            Button.enabled(this.isLoginEnabled());
            // 登录按钮
            Button.onClick(() => {
                this.handleLogin();
            });
        }, Button);
        // 登录按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 测试连接按钮
            Button.createWithLabel('测试Spring Boot连接');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(179:11)", "entry");
            // 测试连接按钮
            Button.width('100%');
            // 测试连接按钮
            Button.height(48);
            // 测试连接按钮
            Button.backgroundColor('#4CAF50');
            // 测试连接按钮
            Button.borderRadius(6);
            // 测试连接按钮
            Button.fontSize(16);
            // 测试连接按钮
            Button.fontColor(Color.White);
            // 测试连接按钮
            Button.margin({ top: 10 });
            // 测试连接按钮
            Button.onClick(() => {
                router.pushUrl({
                    url: 'pages/TestConnectionPage'
                });
            });
        }, Button);
        // 测试连接按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 测试跳转按钮
            Button.createWithLabel('直接跳转测试');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(194:11)", "entry");
            // 测试跳转按钮
            Button.width('100%');
            // 测试跳转按钮
            Button.height(48);
            // 测试跳转按钮
            Button.backgroundColor('#FF6B6B');
            // 测试跳转按钮
            Button.borderRadius(6);
            // 测试跳转按钮
            Button.fontSize(16);
            // 测试跳转按钮
            Button.fontColor(Color.White);
            // 测试跳转按钮
            Button.margin({ top: 10 });
            // 测试跳转按钮
            Button.onClick(() => {
                console.log('=== 测试直接跳转 ===');
                router.replaceUrl({
                    url: 'pages/MyBankCardPage'
                }).then(() => {
                    console.log('测试跳转成功');
                }).catch((error: Error) => {
                    console.error('测试跳转失败:', error.message);
                    promptAction.showToast({ message: `跳转失败: ${error.message}` });
                });
            });
        }, Button);
        // 测试跳转按钮
        Button.pop();
        // 登录表单卡片
        Column.pop();
        // 背景渐变容器
        Column.pop();
        Column.pop();
    }
    /**
     * 生成验证码
     */
    generateCaptcha() {
        // 生成4位随机数字验证码
        this.captchaCode = Math.floor(1000 + Math.random() * 9000).toString();
        promptAction.showToast({
            message: `新验证码: ${this.captchaCode}`,
            duration: 2000
        });
    }
    /**
     * 判断是否可以登录
     */
    isLoginEnabled(): boolean {
        if (this.isLoading) {
            return false;
        }
        // 简化条件：只需要用户名和密码
        return !!(this.username && this.username.length >= 1 &&
            this.password && this.password.length >= 1);
    }
    /**
     * 处理登录
     */
    async handleLogin() {
        console.log('=== 登录按钮被点击 ===');
        console.log('当前isLoading状态:', this.isLoading);
        console.log('用户名:', this.username);
        console.log('密码:', this.password);
        console.log('isLoginEnabled():', this.isLoginEnabled());
        if (this.isLoading) {
            console.log('正在登录中，忽略重复点击');
            return;
        }
        // 简化表单验证 - 只要有用户名和密码就可以登录
        if (!this.username || this.username.length < 1) {
            console.log('用户名验证失败');
            promptAction.showToast({ message: '请输入用户名' });
            return;
        }
        if (!this.password || this.password.length < 1) {
            console.log('密码验证失败');
            promptAction.showToast({ message: '请输入密码' });
            return;
        }
        console.log('表单验证通过，开始登录流程');
        this.isLoading = true;
        try {
            console.log('连接Spring Boot后端进行登录验证...');
            // 调用Spring Boot登录API
            const loginData: UserLoginRequest = {
                username: this.username,
                password: this.password,
                captcha: this.captcha || '1234' // 临时使用固定验证码
            };
            console.log('发送登录请求:', loginData);
            const response = await UserApi.login(loginData);
            console.log('登录响应:', response);
            // 保存用户信息
            AppStorage.setOrCreate('userToken', response.token);
            AppStorage.setOrCreate('userInfo', response.userInfo);
            console.log('用户信息已保存，准备跳转');
            // 跳转到银行卡页面
            await router.replaceUrl({
                url: 'pages/MyBankCardPage'
            });
            console.log('页面跳转成功！');
            promptAction.showToast({ message: '登录成功' });
        }
        catch (error) {
            console.error('登录过程出错:', error);
            let errorMessage = '登录失败，请重试';
            if (error instanceof Error) {
                errorMessage = `登录失败: ${error.message}`;
                console.error('错误详情:', error.message);
                console.error('错误堆栈:', error.stack);
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            console.log('登录流程结束，重置loading状态');
            this.isLoading = false;
        }
    }
    /**
     * 测试Spring Boot后端连接
     */
    async testSpringBootConnection() {
        try {
            const response = await this.callSpringBootTest();
            // 检查业务状态码
            if (response.code === 200) {
                promptAction.showToast({
                    message: `✅ 连接成功: ${response.msg}`,
                    duration: 3000,
                    bottom: 50
                });
            }
            else {
                promptAction.showToast({
                    message: `❌ 业务错误: ${response.msg}`,
                    duration: 3000,
                    bottom: 50
                });
            }
        }
        catch (error) {
            promptAction.showToast({
                message: `❌ 连接失败: ${(error as Error).message}`,
                duration: 3000,
                bottom: 50
            });
        }
    }
    /**
     * 调用Spring Boot测试接口
     */
    private callSpringBootTest(): Promise<SpringBootResponse> {
        return new Promise((resolve, reject) => {
            const httpRequest = http.createHttp();
            const options: http.HttpRequestOptions = {
                method: http.RequestMethod.GET,
                header: {
                    'Accept': 'application/json'
                },
                readTimeout: 10000,
                connectTimeout: 10000
            };
            httpRequest.request('http://localhost:8080/test', options, (err: BusinessError, data: http.HttpResponse) => {
                if (err) {
                    console.error('HTTP请求失败:', err);
                    reject(new Error(err.message));
                    return;
                }
                try {
                    if (data.responseCode !== 200) {
                        reject(new Error(`HTTP ${data.responseCode}`));
                        return;
                    }
                    const response = JSON.parse(data.result as string) as SpringBootResponse;
                    resolve(response);
                }
                catch (parseError) {
                    console.error('响应解析失败:', parseError);
                    reject(new Error('响应解析失败'));
                }
                finally {
                    httpRequest.destroy();
                }
            });
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LoginPage";
    }
}
registerNamedRoute(() => new LoginPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/LoginPage", pageFullPath: "entry/src/main/ets/pages/LoginPage", integratedHsp: "false", moduleType: "followWithHap" });
