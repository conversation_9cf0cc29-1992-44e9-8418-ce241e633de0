{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "afbc7810-a9b6-4f00-933c-7b0a9b09952f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606269415000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b60468-4a04-403d-bb1f-f225acbca770", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606279239200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097c5bdb-2ccb-4494-bf6b-1882264a0476", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314606279732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d664d01-f14a-467c-b8c1-19f8c76963c4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028303044200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028313564100, "endTime": 315028582874100}, "additional": {"children": ["7b737d2a-b0cd-412a-ab3d-9ffdf0334579", "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "fa67b522-c06e-475b-84a3-c5f74702e082", "b63b0661-bb53-453e-9499-4fc66ce735e0", "1b8f710c-9a04-4a7b-9f46-0adba1bc784d", "9d72bd86-a554-496c-8f4e-d8ee384d5d3c", "27a5569e-0bbe-4cc0-aadc-2c7b7b91cbaa"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b737d2a-b0cd-412a-ab3d-9ffdf0334579", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028313565500, "endTime": 315028324973800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "02b03417-3ed1-4cee-9121-1dc241153d6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028324991100, "endTime": 315028581367500}, "additional": {"children": ["5618d9c0-7981-4847-9a7d-9f819ea96827", "5c8a209b-bcd2-4a2c-b390-bfe7055fa646", "9fabd4ed-3565-46fd-aa8c-48e03117015b", "e1064396-1df2-46d1-8e93-de9f8df9661e", "e622d0fb-19ff-4d63-847a-2e43041db587", "042634bc-2b15-42be-8e7f-9c007271f62b", "3f5f8b54-2353-47a8-83b2-6af7956935a8", "fdf89ce8-94d1-4e03-b74f-d93fdc17bbb0", "2bca974b-dcfa-4d76-82e8-73e6e8874a61"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "4e818df7-5762-47f1-a556-0876438dc876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa67b522-c06e-475b-84a3-c5f74702e082", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028581400500, "endTime": 315028582843800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "df954104-4f04-4f6d-b68d-945aed0ea495"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b63b0661-bb53-453e-9499-4fc66ce735e0", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028582850800, "endTime": 315028582868300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "ed1b38ab-dd88-4b0b-b81d-2dc4e36d285f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b8f710c-9a04-4a7b-9f46-0adba1bc784d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028316928300, "endTime": 315028316958300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "6fd08e78-42df-40bc-9fda-2f5c033f0020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fd08e78-42df-40bc-9fda-2f5c033f0020", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028316928300, "endTime": 315028316958300}, "additional": {"logType": "info", "children": [], "durationId": "1b8f710c-9a04-4a7b-9f46-0adba1bc784d", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "9d72bd86-a554-496c-8f4e-d8ee384d5d3c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028322812000, "endTime": 315028322827900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "783354b0-496a-4e69-9afb-140403c337d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "783354b0-496a-4e69-9afb-140403c337d1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028322812000, "endTime": 315028322827900}, "additional": {"logType": "info", "children": [], "durationId": "9d72bd86-a554-496c-8f4e-d8ee384d5d3c", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "4a534abf-3716-4b03-8bc1-ddf908e67de6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028322877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39cac61c-5442-4f5e-8289-65b29fa3ecac", "name": "Since current hvigor version 5.15.3 differs from last hvigor version \n      undefined, delete file-cache.json and task-cache.json.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028324671800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f25da3-a608-4a52-bc92-f949d44d37e6", "name": "Cache service initialization finished in 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028324895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b03417-3ed1-4cee-9121-1dc241153d6c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028313565500, "endTime": 315028324973800}, "additional": {"logType": "info", "children": [], "durationId": "7b737d2a-b0cd-412a-ab3d-9ffdf0334579", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "5618d9c0-7981-4847-9a7d-9f819ea96827", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028331727200, "endTime": 315028331737500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "ed6292cc-eb00-4be1-b717-c43e059cf0a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c8a209b-bcd2-4a2c-b390-bfe7055fa646", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028331751300, "endTime": 315028336148200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "47db29a2-d28d-4f98-affc-09e6656b8e2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fabd4ed-3565-46fd-aa8c-48e03117015b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028336165900, "endTime": 315028450943700}, "additional": {"children": ["37cbe9f2-47f4-4ea4-b115-aa5083d5ac7d", "583bd084-933a-4505-91ec-37666f199bd6", "518002e9-5a75-4f06-bdc3-893a59b0fb13"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "6ceaa02c-6a52-40f2-b158-0c10a936a108"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1064396-1df2-46d1-8e93-de9f8df9661e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028450959800, "endTime": 315028519983400}, "additional": {"children": ["84959e02-b7d2-442b-aeeb-f406a50e11d7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "873004db-9ced-4021-9cb6-a92a5ad607dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e622d0fb-19ff-4d63-847a-2e43041db587", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028520001700, "endTime": 315028556573900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "7e56b29d-b107-4114-a2df-dc4c0e8c0cc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "042634bc-2b15-42be-8e7f-9c007271f62b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028557741500, "endTime": 315028565259800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "b8878ec2-383e-4ffd-bafc-3183489d0406"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f5f8b54-2353-47a8-83b2-6af7956935a8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028565294200, "endTime": 315028581187500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "4b3c96ec-862b-4272-b48b-583e6a299b37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdf89ce8-94d1-4e03-b74f-d93fdc17bbb0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028581212700, "endTime": 315028581353300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "621e43ff-6739-4f67-a049-35de689f79da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed6292cc-eb00-4be1-b717-c43e059cf0a8", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028331727200, "endTime": 315028331737500}, "additional": {"logType": "info", "children": [], "durationId": "5618d9c0-7981-4847-9a7d-9f819ea96827", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "47db29a2-d28d-4f98-affc-09e6656b8e2c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028331751300, "endTime": 315028336148200}, "additional": {"logType": "info", "children": [], "durationId": "5c8a209b-bcd2-4a2c-b390-bfe7055fa646", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "37cbe9f2-47f4-4ea4-b115-aa5083d5ac7d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028337074700, "endTime": 315028337125200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9fabd4ed-3565-46fd-aa8c-48e03117015b", "logId": "a6858ba7-ee77-4985-bd3c-bdc487ec3652"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6858ba7-ee77-4985-bd3c-bdc487ec3652", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028337074700, "endTime": 315028337125200}, "additional": {"logType": "info", "children": [], "durationId": "37cbe9f2-47f4-4ea4-b115-aa5083d5ac7d", "parent": "6ceaa02c-6a52-40f2-b158-0c10a936a108"}}, {"head": {"id": "583bd084-933a-4505-91ec-37666f199bd6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028340767700, "endTime": 315028450027500}, "additional": {"children": ["fb6ae744-5934-4868-b091-37d2b9bd91a3", "d83ae354-a82f-4388-8280-5734cc0e9d34"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9fabd4ed-3565-46fd-aa8c-48e03117015b", "logId": "8fcbff3e-f351-4a3d-be2a-afb2e1ac8bbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb6ae744-5934-4868-b091-37d2b9bd91a3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028340770000, "endTime": 315028344904900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "583bd084-933a-4505-91ec-37666f199bd6", "logId": "54c65db0-de2d-4f5c-8f93-45301c108702"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d83ae354-a82f-4388-8280-5734cc0e9d34", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028344921100, "endTime": 315028450014000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "583bd084-933a-4505-91ec-37666f199bd6", "logId": "359fcad1-3375-40d3-b7b8-a848f4cd953b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90b574f1-078a-42dc-a699-fed6bda2fa61", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028340780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e000b2fc-8fa9-4d06-816a-3b32aaf6d66d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028344777500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c65db0-de2d-4f5c-8f93-45301c108702", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028340770000, "endTime": 315028344904900}, "additional": {"logType": "info", "children": [], "durationId": "fb6ae744-5934-4868-b091-37d2b9bd91a3", "parent": "8fcbff3e-f351-4a3d-be2a-afb2e1ac8bbf"}}, {"head": {"id": "28caaa14-255e-4a65-a9d2-16e6f63df155", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028344936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba1fc53-865a-4642-b34a-54a43e7b3ecd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028351692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e69444e-5180-48ce-a698-f43aa34229e4", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028351803300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18c27b0-e107-4616-8c22-e2973e9e8fff", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028352872900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653c6f05-c927-48f8-a4d7-fd3c7980d3ef", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028353205600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e4c8d3-6ad4-4920-a393-89d0a8184771", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028356877200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd500ccf-28fa-4898-87ef-19b216011c1b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028362220800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41721eee-e813-458e-a971-0bee4aff9451", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028378964700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885f7b23-7a88-4f6b-843b-35551499b487", "name": "Sdk init in 62 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028425020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0346840-ce93-4c92-9010-41507a8acff2", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028425162200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 17}, "markType": "other"}}, {"head": {"id": "67be94b9-e5f0-452f-aa22-c825933e1f8d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028425173600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 17}, "markType": "other"}}, {"head": {"id": "6fd0e186-0284-49c6-ae92-7ea6b345b648", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028449711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6c2512b-dfa5-4571-bc23-79dbcce1741f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028449836400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bb142f-7419-474b-8603-4a9db409ea10", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028449905400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c4b49f-0ddc-4e9a-99f8-1d883e54dff8", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028449961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "359fcad1-3375-40d3-b7b8-a848f4cd953b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028344921100, "endTime": 315028450014000}, "additional": {"logType": "info", "children": [], "durationId": "d83ae354-a82f-4388-8280-5734cc0e9d34", "parent": "8fcbff3e-f351-4a3d-be2a-afb2e1ac8bbf"}}, {"head": {"id": "8fcbff3e-f351-4a3d-be2a-afb2e1ac8bbf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028340767700, "endTime": 315028450027500}, "additional": {"logType": "info", "children": ["54c65db0-de2d-4f5c-8f93-45301c108702", "359fcad1-3375-40d3-b7b8-a848f4cd953b"], "durationId": "583bd084-933a-4505-91ec-37666f199bd6", "parent": "6ceaa02c-6a52-40f2-b158-0c10a936a108"}}, {"head": {"id": "518002e9-5a75-4f06-bdc3-893a59b0fb13", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028450901900, "endTime": 315028450924600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9fabd4ed-3565-46fd-aa8c-48e03117015b", "logId": "4643bfd4-253a-411c-bc2c-167fe369c6e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4643bfd4-253a-411c-bc2c-167fe369c6e6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028450901900, "endTime": 315028450924600}, "additional": {"logType": "info", "children": [], "durationId": "518002e9-5a75-4f06-bdc3-893a59b0fb13", "parent": "6ceaa02c-6a52-40f2-b158-0c10a936a108"}}, {"head": {"id": "6ceaa02c-6a52-40f2-b158-0c10a936a108", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028336165900, "endTime": 315028450943700}, "additional": {"logType": "info", "children": ["a6858ba7-ee77-4985-bd3c-bdc487ec3652", "8fcbff3e-f351-4a3d-be2a-afb2e1ac8bbf", "4643bfd4-253a-411c-bc2c-167fe369c6e6"], "durationId": "9fabd4ed-3565-46fd-aa8c-48e03117015b", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "84959e02-b7d2-442b-aeeb-f406a50e11d7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028452281900, "endTime": 315028519950600}, "additional": {"children": ["82bca91d-8acb-4f75-82fc-6f20f030cfb6", "84ac0b75-5a06-47f3-b012-e8e09e72c44b", "f1743bc0-cbff-4285-b74d-4536360c4622"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1064396-1df2-46d1-8e93-de9f8df9661e", "logId": "39f226e7-67fb-4746-b377-2f5af968d16d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82bca91d-8acb-4f75-82fc-6f20f030cfb6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028456479900, "endTime": 315028456501200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84959e02-b7d2-442b-aeeb-f406a50e11d7", "logId": "dd057ee3-d9bd-413d-ac48-aa85a99904e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd057ee3-d9bd-413d-ac48-aa85a99904e7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028456479900, "endTime": 315028456501200}, "additional": {"logType": "info", "children": [], "durationId": "82bca91d-8acb-4f75-82fc-6f20f030cfb6", "parent": "39f226e7-67fb-4746-b377-2f5af968d16d"}}, {"head": {"id": "84ac0b75-5a06-47f3-b012-e8e09e72c44b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028458940200, "endTime": 315028479004100}, "additional": {"children": ["284e0676-8bf8-4253-9c4a-d20bf9415856", "ad5e9086-6ec6-453c-b3a8-2d3dcc08567a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84959e02-b7d2-442b-aeeb-f406a50e11d7", "logId": "5ffc2677-0ab2-4310-ade8-62e77380d7e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "284e0676-8bf8-4253-9c4a-d20bf9415856", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028458941400, "endTime": 315028462417100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84ac0b75-5a06-47f3-b012-e8e09e72c44b", "logId": "39eb50b0-7ce7-4b5f-83d6-99ef683c5b1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad5e9086-6ec6-453c-b3a8-2d3dcc08567a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028462431800, "endTime": 315028478981200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84ac0b75-5a06-47f3-b012-e8e09e72c44b", "logId": "d9e393ac-d08b-4e9d-b9a5-f13227701918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11f98eb2-6b8a-43f6-95f3-f33ac959949e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028458946800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20a51e8-0d05-4310-8dfb-9300060bc96c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028462298100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39eb50b0-7ce7-4b5f-83d6-99ef683c5b1c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028458941400, "endTime": 315028462417100}, "additional": {"logType": "info", "children": [], "durationId": "284e0676-8bf8-4253-9c4a-d20bf9415856", "parent": "5ffc2677-0ab2-4310-ade8-62e77380d7e4"}}, {"head": {"id": "04cb12e0-be4b-4a37-8852-16771b849abf", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028462443900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc1bbd5-7612-4ba0-99bb-b73196f2587e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028471049600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d8da38-a192-47c4-9125-2920b8710f40", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028471573900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e35344-3b40-4fde-ba3f-99fedf7eccb3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028471925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ae9aa9-929e-43c6-b855-376640b41821", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028472324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ea5948-e788-443a-8987-6e9d2ca124cb", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028472443200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750b0d39-8d1a-4ab5-a1bb-df8735c219c2", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028472562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94302d1-dcc5-4dd5-98ea-6769840049ef", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028472688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d179ef2f-41ce-4435-8bb3-4b735f41c4c7", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028478130100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a18b9fd-dfda-49e1-a7c0-da2ad0eab5cd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028478608600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2254b988-e4ba-4ec7-b282-4ae045304161", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028478762500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98af5517-b41d-4cec-8425-af0b53468745", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028478874100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e393ac-d08b-4e9d-b9a5-f13227701918", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028462431800, "endTime": 315028478981200}, "additional": {"logType": "info", "children": [], "durationId": "ad5e9086-6ec6-453c-b3a8-2d3dcc08567a", "parent": "5ffc2677-0ab2-4310-ade8-62e77380d7e4"}}, {"head": {"id": "5ffc2677-0ab2-4310-ade8-62e77380d7e4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028458940200, "endTime": 315028479004100}, "additional": {"logType": "info", "children": ["39eb50b0-7ce7-4b5f-83d6-99ef683c5b1c", "d9e393ac-d08b-4e9d-b9a5-f13227701918"], "durationId": "84ac0b75-5a06-47f3-b012-e8e09e72c44b", "parent": "39f226e7-67fb-4746-b377-2f5af968d16d"}}, {"head": {"id": "f1743bc0-cbff-4285-b74d-4536360c4622", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028519880400, "endTime": 315028519916000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84959e02-b7d2-442b-aeeb-f406a50e11d7", "logId": "7b763ed8-fc0a-40df-b3fe-9fc570ccab28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b763ed8-fc0a-40df-b3fe-9fc570ccab28", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028519880400, "endTime": 315028519916000}, "additional": {"logType": "info", "children": [], "durationId": "f1743bc0-cbff-4285-b74d-4536360c4622", "parent": "39f226e7-67fb-4746-b377-2f5af968d16d"}}, {"head": {"id": "39f226e7-67fb-4746-b377-2f5af968d16d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028452281900, "endTime": 315028519950600}, "additional": {"logType": "info", "children": ["dd057ee3-d9bd-413d-ac48-aa85a99904e7", "5ffc2677-0ab2-4310-ade8-62e77380d7e4", "7b763ed8-fc0a-40df-b3fe-9fc570ccab28"], "durationId": "84959e02-b7d2-442b-aeeb-f406a50e11d7", "parent": "873004db-9ced-4021-9cb6-a92a5ad607dc"}}, {"head": {"id": "873004db-9ced-4021-9cb6-a92a5ad607dc", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028450959800, "endTime": 315028519983400}, "additional": {"logType": "info", "children": ["39f226e7-67fb-4746-b377-2f5af968d16d"], "durationId": "e1064396-1df2-46d1-8e93-de9f8df9661e", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "522760bf-90bd-469a-abe9-9670c0b3dc8d", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028555952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c66c99-981c-4443-b32c-f3ead90e47ad", "name": "hvigorfile, resolve hvigorfile dependencies in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028556476200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e56b29d-b107-4114-a2df-dc4c0e8c0cc8", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028520001700, "endTime": 315028556573900}, "additional": {"logType": "info", "children": [], "durationId": "e622d0fb-19ff-4d63-847a-2e43041db587", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "2bca974b-dcfa-4d76-82e8-73e6e8874a61", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028557507400, "endTime": 315028557725700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "logId": "e4186576-32fc-4272-9163-1e55ff194226"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a182803-eb78-4754-9b12-95606507389f", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028557537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4186576-32fc-4272-9163-1e55ff194226", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028557507400, "endTime": 315028557725700}, "additional": {"logType": "info", "children": [], "durationId": "2bca974b-dcfa-4d76-82e8-73e6e8874a61", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "578d4d8a-accc-41da-91f3-c5506f5c878b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028558981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7375fd51-32c4-4833-9aee-29f17252d283", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028563837200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8878ec2-383e-4ffd-bafc-3183489d0406", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028557741500, "endTime": 315028565259800}, "additional": {"logType": "info", "children": [], "durationId": "042634bc-2b15-42be-8e7f-9c007271f62b", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "aeb85dde-8ec7-4c25-812a-4b91b9b1ff32", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028565325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204bceec-884b-40ed-9c74-a0fd04cdb87d", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028573035900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47f3edb-064d-4fbe-89fe-5bccc097950a", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028573228700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfda99a3-b788-4a46-ad4d-7901761de33c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028573682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "748446a8-8c07-493e-a4fb-6fef76f200fb", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028577404400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb2f19b-5ac5-446f-bc1a-8f936bd13c4f", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028577539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3c96ec-862b-4272-b48b-583e6a299b37", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028565294200, "endTime": 315028581187500}, "additional": {"logType": "info", "children": [], "durationId": "3f5f8b54-2353-47a8-83b2-6af7956935a8", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "bb9d2b03-02a5-4d3a-9c11-fc8de76712ab", "name": "Configuration phase cost:250 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028581239300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621e43ff-6739-4f67-a049-35de689f79da", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028581212700, "endTime": 315028581353300}, "additional": {"logType": "info", "children": [], "durationId": "fdf89ce8-94d1-4e03-b74f-d93fdc17bbb0", "parent": "4e818df7-5762-47f1-a556-0876438dc876"}}, {"head": {"id": "4e818df7-5762-47f1-a556-0876438dc876", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028324991100, "endTime": 315028581367500}, "additional": {"logType": "info", "children": ["ed6292cc-eb00-4be1-b717-c43e059cf0a8", "47db29a2-d28d-4f98-affc-09e6656b8e2c", "6ceaa02c-6a52-40f2-b158-0c10a936a108", "873004db-9ced-4021-9cb6-a92a5ad607dc", "7e56b29d-b107-4114-a2df-dc4c0e8c0cc8", "b8878ec2-383e-4ffd-bafc-3183489d0406", "4b3c96ec-862b-4272-b48b-583e6a299b37", "621e43ff-6739-4f67-a049-35de689f79da", "e4186576-32fc-4272-9163-1e55ff194226"], "durationId": "b764104e-7fa9-4862-bcfe-1bd17cf5929b", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "27a5569e-0bbe-4cc0-aadc-2c7b7b91cbaa", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028582814700, "endTime": 315028582830200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7", "logId": "928f061d-a7c9-4ee1-ae10-a85d3a4b6e0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "928f061d-a7c9-4ee1-ae10-a85d3a4b6e0d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028582814700, "endTime": 315028582830200}, "additional": {"logType": "info", "children": [], "durationId": "27a5569e-0bbe-4cc0-aadc-2c7b7b91cbaa", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "df954104-4f04-4f6d-b68d-945aed0ea495", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028581400500, "endTime": 315028582843800}, "additional": {"logType": "info", "children": [], "durationId": "fa67b522-c06e-475b-84a3-c5f74702e082", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "ed1b38ab-dd88-4b0b-b81d-2dc4e36d285f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028582850800, "endTime": 315028582868300}, "additional": {"logType": "info", "children": [], "durationId": "b63b0661-bb53-453e-9499-4fc66ce735e0", "parent": "847ffd33-99cc-4a48-b67a-ecdcd8d91233"}}, {"head": {"id": "847ffd33-99cc-4a48-b67a-ecdcd8d91233", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028313564100, "endTime": 315028582874100}, "additional": {"logType": "info", "children": ["02b03417-3ed1-4cee-9121-1dc241153d6c", "4e818df7-5762-47f1-a556-0876438dc876", "df954104-4f04-4f6d-b68d-945aed0ea495", "ed1b38ab-dd88-4b0b-b81d-2dc4e36d285f", "6fd08e78-42df-40bc-9fda-2f5c033f0020", "783354b0-496a-4e69-9afb-140403c337d1", "928f061d-a7c9-4ee1-ae10-a85d3a4b6e0d"], "durationId": "b1a06bdb-1597-40c4-a0c3-c78c0d5780c7"}}, {"head": {"id": "bffa4c4f-5a0d-4233-8488-71286777869b", "name": "Configuration task cost before running: 275 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028583017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f611b49-3cb6-4264-9ab9-f6449261b2c1", "name": "entry:clean", "description": "Clear the cache information.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028586492800, "endTime": 315031761448500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "d80575bc-82e8-4635-a091-f83a9d269dae", "logId": "fe01edc6-8a84-4c3a-825a-55a8e1823e85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d80575bc-82e8-4635-a091-f83a9d269dae", "name": "create entry:clean task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028585224400}, "additional": {"logType": "detail", "children": [], "durationId": "4f611b49-3cb6-4264-9ab9-f6449261b2c1"}}, {"head": {"id": "53c34099-6f43-41ea-9b80-35fc563063fe", "name": "Executing task :entry:clean", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028586517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "089be9c8-95cf-4cc0-b78d-675bd648c1fb", "name": "clean: Worker pool is inactive.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028587002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af39a6df-277d-407b-854c-b874a97184b6", "name": "entry : clean cost memory 0.7977523803710938", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031760003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b270fdc-e522-4a38-8103-73b6759b231b", "name": "runTaskFromQueue task cost before running: 3 s 453 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031760847200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe01edc6-8a84-4c3a-825a-55a8e1823e85", "name": "Finished :entry:clean", "description": "Clear the cache information.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028586492800, "endTime": 315031761448500, "totalTime": 3174106500}, "additional": {"logType": "info", "children": [], "durationId": "4f611b49-3cb6-4264-9ab9-f6449261b2c1"}}, {"head": {"id": "db01ac8a-b5bb-4708-b109-2c9949ca455e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031790826300, "endTime": 315032422086200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4ca46749-51b4-4188-84fe-3a0b5aa1a251", "logId": "90c779c3-318d-4ebd-b18d-1ce03a2cdbce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca46749-51b4-4188-84fe-3a0b5aa1a251", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031770598500}, "additional": {"logType": "detail", "children": [], "durationId": "db01ac8a-b5bb-4708-b109-2c9949ca455e"}}, {"head": {"id": "0a19bb8b-68ea-426f-a681-f459ff1c5934", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031773983500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4ae2e6-534b-4f7a-9eae-8fc2d19cf6e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031774576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca202222-70c6-4bcc-add8-c1d30e9f29bb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031790854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea15e15a-e217-4caa-b0c0-b09bd0948fe1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031799886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a4a50f1-1a57-417a-811d-87f534596a09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031800024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3778bf3e-1ea0-4f61-b6d4-ed7eb4d5f234", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032418596300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a90c2d32-93f1-4dd6-9b1c-6c781bf904c4", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032420280600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35cd9274-767b-4a7e-b18e-91b0f74b1a6a", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032420576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4780d9-3c0a-4246-9593-ab73b2c18bbd", "name": "entry : default@PreBuild cost memory 13.067970275878906", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032421736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce252b9d-b4df-4b7e-b6fd-d9a7aa49face", "name": "runTaskFromQueue task cost before running: 4 s 114 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032421958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90c779c3-318d-4ebd-b18d-1ce03a2cdbce", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315031790826300, "endTime": 315032422086200, "totalTime": 631083600}, "additional": {"logType": "info", "children": [], "durationId": "db01ac8a-b5bb-4708-b109-2c9949ca455e"}}, {"head": {"id": "9defe670-99bc-4162-b781-7e614906dff2", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032430941900, "endTime": 315032436973700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f86c0f5c-8ea8-4686-a78d-bced760aa616", "logId": "880b8840-638f-4c5b-820a-1de7e6767aad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f86c0f5c-8ea8-4686-a78d-bced760aa616", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032428809500}, "additional": {"logType": "detail", "children": [], "durationId": "9defe670-99bc-4162-b781-7e614906dff2"}}, {"head": {"id": "0479fe6c-661f-4ec6-974f-28ddb2da0c09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032429613000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa362f1d-6be5-4623-be46-d7de5744d749", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032429748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66bee2b2-aab5-4b1b-93f3-963991f70bc1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032430954600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4bda72-2030-448f-95ad-72354c3178ae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032431394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ef9a59-d4a1-4088-a53c-fb2875ab31f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032431489800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17c3e31-e7c8-40c3-9467-8c2c1c3a3b0d", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032432095100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afb5378-c392-4e6d-ab7d-46d6aabecb35", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032432459600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eeabccf-bc85-4800-9a21-e30ef3d7db4e", "name": "Change app target API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032432560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250e6496-eba7-47e2-8512-74d2aa58a6e1", "name": "Change app minimum API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032432638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e15a9e4-951c-4c10-a510-4ca3d318d25a", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032432840900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32aac592-29da-4d2d-98ef-29c5ddc405b8", "name": "entry : default@MergeProfile cost memory 0.22540283203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032436712900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbc66b8-cc2f-4e16-9448-3b4ea86a07ca", "name": "runTaskFromQueue task cost before running: 4 s 129 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032436888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "880b8840-638f-4c5b-820a-1de7e6767aad", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032430941900, "endTime": 315032436973700, "totalTime": 5909600}, "additional": {"logType": "info", "children": [], "durationId": "9defe670-99bc-4162-b781-7e614906dff2"}}, {"head": {"id": "069c617e-1ae5-4a3c-81ad-2450937f00d9", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032440816900, "endTime": 315032444109300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "51b41159-f869-4123-85e0-3e89ac8dc4ba", "logId": "db91ec73-50e3-4d9a-864a-d67c463db74a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51b41159-f869-4123-85e0-3e89ac8dc4ba", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032439130700}, "additional": {"logType": "detail", "children": [], "durationId": "069c617e-1ae5-4a3c-81ad-2450937f00d9"}}, {"head": {"id": "10caa631-6b63-40ee-bfe5-21be36932d5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032439752900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7fb8f68-3d69-4ea9-980e-35ceb0ba3cd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032439868000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054d0789-9f42-492b-9749-6da5125b42e7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032440827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6ef031-4398-4a89-9985-f54ac36370df", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032441806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916d69f5-3c4c-4f3d-b242-f8fb0b5861f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032441981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2975d15-9262-4c77-8cdb-19b5c95a7d24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032442054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "104781f2-c0ac-4d52-9826-b45d72325bb8", "name": "entry : default@CreateBuildProfile cost memory 0.1128997802734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032443904700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d90bb5a5-b69b-40cd-b9f0-5537644bfdee", "name": "runTaskFromQueue task cost before running: 4 s 136 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032444035600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db91ec73-50e3-4d9a-864a-d67c463db74a", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032440816900, "endTime": 315032444109300, "totalTime": 3188200}, "additional": {"logType": "info", "children": [], "durationId": "069c617e-1ae5-4a3c-81ad-2450937f00d9"}}, {"head": {"id": "4079b933-97eb-48da-a518-82f43ae69c0c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449438700, "endTime": 315032449880900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1c99da53-7538-4c49-a639-67721ac0703d", "logId": "73bbd7ef-39ca-4c43-a18e-7fa263c002b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c99da53-7538-4c49-a639-67721ac0703d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032447501600}, "additional": {"logType": "detail", "children": [], "durationId": "4079b933-97eb-48da-a518-82f43ae69c0c"}}, {"head": {"id": "61de8592-6e1b-4769-932b-0778551bae78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032448343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d81fdb-7111-42bb-95cc-5c02a5231ce0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032448474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4655f4d5-737d-4960-9dbe-3a151a80d5e0", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9892cde3-c8b6-436d-9698-66ce859ba1c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449566800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62aca95e-42d8-4096-bd76-9e6c32eb6fb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449627600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7bbc001-e1ea-4c16-b345-22c07aec0ad6", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0944dd5-3135-472d-aca7-d0761b1b36ff", "name": "runTaskFromQueue task cost before running: 4 s 142 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449790400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73bbd7ef-39ca-4c43-a18e-7fa263c002b5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032449438700, "endTime": 315032449880900, "totalTime": 327400}, "additional": {"logType": "info", "children": [], "durationId": "4079b933-97eb-48da-a518-82f43ae69c0c"}}, {"head": {"id": "c53ea40e-3aff-4d5e-9b37-100e2f15e425", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032460951400, "endTime": 315032462038700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ff68126c-0a49-4ca3-abb8-8ec7e837d3e6", "logId": "4b088222-a18d-496e-bce1-6c558868f2ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff68126c-0a49-4ca3-abb8-8ec7e837d3e6", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032451826000}, "additional": {"logType": "detail", "children": [], "durationId": "c53ea40e-3aff-4d5e-9b37-100e2f15e425"}}, {"head": {"id": "2e805550-ec83-4ad7-a15b-7a5fb81f6a52", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032452466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2b6a19-e072-4ff6-ab9d-b5af05e02789", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032452571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa491550-269b-4910-a9e6-c4f8d24c486d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032460966300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5c40f7-0400-46d1-b9fa-20cf3dfe90d8", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032461186800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c84802-57ee-429d-ab18-d89c0659b8d6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.05225372314453125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032461845700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21dfb1db-60c4-4ae1-82d1-0a6bef796940", "name": "runTaskFromQueue task cost before running: 4 s 154 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032461970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b088222-a18d-496e-bce1-6c558868f2ab", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032460951400, "endTime": 315032462038700, "totalTime": 993200}, "additional": {"logType": "info", "children": [], "durationId": "c53ea40e-3aff-4d5e-9b37-100e2f15e425"}}, {"head": {"id": "a7fca47c-fb10-44fd-a580-16a8cfdc5c9e", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032465839500, "endTime": 315032609990300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "077ac725-59f7-48c0-b0c3-412bd47f65cf", "logId": "4686ce0f-db3a-40d7-863f-28205725f41e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "077ac725-59f7-48c0-b0c3-412bd47f65cf", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032463898800}, "additional": {"logType": "detail", "children": [], "durationId": "a7fca47c-fb10-44fd-a580-16a8cfdc5c9e"}}, {"head": {"id": "0ebeec43-8414-4021-a93f-5979e8be5509", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032464462000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aea4cf1d-d77f-43e1-bae7-53b5f73d806d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032464559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea591c5e-b9a5-4424-91c4-d45a7e4e856e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032465852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab68f22f-b93e-428f-9997-d83860759c6e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032466029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee9b58c-b4fd-434b-bc9b-62bf34eb7e77", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032466095900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b97e41b-c06f-481a-8ede-236809b633b5", "name": "********", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032606488800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "900e6011-e32f-43f0-95c3-95cb253105e8", "name": "entry : default@ProcessProfile cost memory 0.3201446533203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032609743200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93a9b0c3-9649-466f-a741-9bfa6337dff7", "name": "runTaskFromQueue task cost before running: 4 s 302 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032609901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4686ce0f-db3a-40d7-863f-28205725f41e", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032465839500, "endTime": 315032609990300, "totalTime": 144029000}, "additional": {"logType": "info", "children": [], "durationId": "a7fca47c-fb10-44fd-a580-16a8cfdc5c9e"}}, {"head": {"id": "3b9785d7-a50d-473c-81ae-bf916da6048f", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032614460500, "endTime": 315032619858700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33b56557-bf0c-40ac-a1e5-827f33293115", "logId": "0cf5c8c3-56d9-4a67-99bf-af6b762f7916"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33b56557-bf0c-40ac-a1e5-827f33293115", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032611948200}, "additional": {"logType": "detail", "children": [], "durationId": "3b9785d7-a50d-473c-81ae-bf916da6048f"}}, {"head": {"id": "a9987f1c-4c78-4e5f-891a-3df3e02095fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032612503600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a04d4c-2704-4a1b-a0dc-d88eaeaf0b16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032612605000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852e6f76-fd5e-499a-bc47-956282b57e1a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032614471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2e66a68-593b-4968-92a9-ce03fc00251e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032617236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0244094-321d-443c-827c-84e1e9ec5b73", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032617328600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd4f00cc-0a84-47cb-aea5-e6dd3f0b8e83", "name": "entry : default@ProcessRouterMap cost memory 0.14151763916015625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032619649900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7142a8ec-362e-4b4d-84cf-4c531a08b87f", "name": "runTaskFromQueue task cost before running: 4 s 312 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032619786700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf5c8c3-56d9-4a67-99bf-af6b762f7916", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032614460500, "endTime": 315032619858700, "totalTime": 5298400}, "additional": {"logType": "info", "children": [], "durationId": "3b9785d7-a50d-473c-81ae-bf916da6048f"}}, {"head": {"id": "87a442d8-ade5-4b48-ad32-6c2ea115cf93", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032636021200, "endTime": 315032639014900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7110b1dd-80de-450d-949f-4a05a257d086", "logId": "e181395d-a7c6-4eda-8f4c-3e6b3e9d69bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7110b1dd-80de-450d-949f-4a05a257d086", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032626588400}, "additional": {"logType": "detail", "children": [], "durationId": "87a442d8-ade5-4b48-ad32-6c2ea115cf93"}}, {"head": {"id": "d3062971-7f2b-40a1-aebf-58adf7f7ffe7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032631324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5838026b-0a1b-415d-9385-6c44ba207b11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032631490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a24209-cf1b-4291-8cf7-6349a70d3ef7", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032633100300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111a4fec-3df2-44bc-bf4c-a0dcc07499ce", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032637303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68d0f6b-cbf7-496d-a96f-bdc82d145c8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032637489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "437275e5-319a-4262-b1e7-682b1681bdc6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032637564500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00813333-55ae-4796-b4b6-639b12c7b418", "name": "entry : default@PreviewProcessResource cost memory 0.07866668701171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032637665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9350d5da-3543-4486-9ae7-c7744bbe0ded", "name": "runTaskFromQueue task cost before running: 4 s 331 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032638923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e181395d-a7c6-4eda-8f4c-3e6b3e9d69bd", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032636021200, "endTime": 315032639014900, "totalTime": 1729300}, "additional": {"logType": "info", "children": [], "durationId": "87a442d8-ade5-4b48-ad32-6c2ea115cf93"}}, {"head": {"id": "121548ca-a967-4bbf-8691-9a1b6d0677c4", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032646117700, "endTime": 315032669160400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cd40cd56-00e7-4998-a774-23a71aa776f4", "logId": "df41b552-9dc5-4297-8d48-3202ad6b961c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd40cd56-00e7-4998-a774-23a71aa776f4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032641830500}, "additional": {"logType": "detail", "children": [], "durationId": "121548ca-a967-4bbf-8691-9a1b6d0677c4"}}, {"head": {"id": "adb0a90b-adeb-4d25-8311-583d748874c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032642398500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf41b70-d9f4-4dbd-bfb4-79c4f589d20d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032642497200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f6c70a-cff7-4d61-b1d2-0072e5a2efdb", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032646128900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dfc87fa-6cf7-47cc-b1de-82fd9693b000", "name": "entry : default@GenerateLoaderJson cost memory 0.7815933227539062", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032668927300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab46219c-5329-468a-b3d2-714aeecf22f0", "name": "runTaskFromQueue task cost before running: 4 s 361 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032669083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df41b552-9dc5-4297-8d48-3202ad6b961c", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032646117700, "endTime": 315032669160400, "totalTime": 22936300}, "additional": {"logType": "info", "children": [], "durationId": "121548ca-a967-4bbf-8691-9a1b6d0677c4"}}, {"head": {"id": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032681080100, "endTime": 315032866301500}, "additional": {"children": ["986b1c38-67a5-410f-863f-9a28409f78d5", "6198d65b-8761-4341-b7b0-a27856322b9e", "542a35b7-5195-4cad-a83b-afb363e457c9", "08f85f68-b656-4e7e-aee8-1e2dc64fa5b9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f6abead3-d78d-4a7c-8b74-b5f47da86742", "logId": "a9177344-896b-40f8-8b8e-5523cd83ad5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6abead3-d78d-4a7c-8b74-b5f47da86742", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032676974600}, "additional": {"logType": "detail", "children": [], "durationId": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361"}}, {"head": {"id": "64119414-85d8-4210-bd3a-54363446bb83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032677522000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bc29112-346e-41e0-bf62-e49db4f976dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032677613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d42aff-35fa-4779-be2c-a7d2939d8126", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032678646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddacacdf-2f81-4043-8e3f-9d232fef27bd", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032681112600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986b1c38-67a5-410f-863f-9a28409f78d5", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032684019200, "endTime": 315032687060300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361", "logId": "b2262c53-7898-42c7-a1c3-87bb03449336"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2262c53-7898-42c7-a1c3-87bb03449336", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032684019200, "endTime": 315032687060300}, "additional": {"logType": "info", "children": [], "durationId": "986b1c38-67a5-410f-863f-9a28409f78d5", "parent": "a9177344-896b-40f8-8b8e-5523cd83ad5d"}}, {"head": {"id": "80e95c07-12bf-465e-bff4-4330826bfef3", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032687452000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6198d65b-8761-4341-b7b0-a27856322b9e", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032688474400, "endTime": 315032719140600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361", "logId": "41b3642c-7ccf-40f5-b586-6bfeb5e59249"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "676667aa-4e0e-4bb5-82c0-40379f4cdc2a", "name": "current process  memoryUsage: {\n  rss: 127205376,\n  heapTotal: 135094272,\n  heapUsed: 118042576,\n  external: 3215816,\n  arrayBuffers: 209681\n} os memoryUsage :5.5611724853515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032689418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3430510-5c84-435d-b006-ef7f8182cfa2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032716562900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b3642c-7ccf-40f5-b586-6bfeb5e59249", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032688474400, "endTime": 315032719140600}, "additional": {"logType": "info", "children": [], "durationId": "6198d65b-8761-4341-b7b0-a27856322b9e", "parent": "a9177344-896b-40f8-8b8e-5523cd83ad5d"}}, {"head": {"id": "89e46653-98b9-46ae-aa4e-f8f34c8791cf", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032719407800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542a35b7-5195-4cad-a83b-afb363e457c9", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032720952200, "endTime": 315032783153500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361", "logId": "fc6936b8-f64e-4c4d-9dfd-9c16273e0155"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e5c2218-a854-4030-8b96-65a8d5cba3a2", "name": "current process  memoryUsage: {\n  rss: 127225856,\n  heapTotal: 135094272,\n  heapUsed: 118311464,\n  external: 3215942,\n  arrayBuffers: 209822\n} os memoryUsage :5.561187744140625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032722332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4d2bd8-1cc5-41cb-86f7-9ea60b2331b9", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032780841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc6936b8-f64e-4c4d-9dfd-9c16273e0155", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032720952200, "endTime": 315032783153500}, "additional": {"logType": "info", "children": [], "durationId": "542a35b7-5195-4cad-a83b-afb363e457c9", "parent": "a9177344-896b-40f8-8b8e-5523cd83ad5d"}}, {"head": {"id": "524db906-44b3-4445-baf7-aace394fee98", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032783369700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f85f68-b656-4e7e-aee8-1e2dc64fa5b9", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032784559400, "endTime": 315032865259000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361", "logId": "ae9dc0ec-7844-4aca-9df1-4012fc75e097"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a29156f-206f-426b-9d75-07c11bb2d7e6", "name": "current process  memoryUsage: {\n  rss: 127242240,\n  heapTotal: 135094272,\n  heapUsed: 118696904,\n  external: 3216068,\n  arrayBuffers: 210603\n} os memoryUsage :5.561119079589844", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032785972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f04ea8b-1ad1-41fe-be07-26261fa51195", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032862116500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9dc0ec-7844-4aca-9df1-4012fc75e097", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032784559400, "endTime": 315032865259000}, "additional": {"logType": "info", "children": [], "durationId": "08f85f68-b656-4e7e-aee8-1e2dc64fa5b9", "parent": "a9177344-896b-40f8-8b8e-5523cd83ad5d"}}, {"head": {"id": "6f444458-706a-4cf1-a046-c792386a1c11", "name": "entry : default@PreviewCompileResource cost memory 1.1096725463867188", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032866045200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d485497c-e6b2-4942-8136-c3e93d703dae", "name": "runTaskFromQueue task cost before running: 4 s 558 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032866224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9177344-896b-40f8-8b8e-5523cd83ad5d", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032681080100, "endTime": 315032866301500, "totalTime": 185087000}, "additional": {"logType": "info", "children": ["b2262c53-7898-42c7-a1c3-87bb03449336", "41b3642c-7ccf-40f5-b586-6bfeb5e59249", "fc6936b8-f64e-4c4d-9dfd-9c16273e0155", "ae9dc0ec-7844-4aca-9df1-4012fc75e097"], "durationId": "7ddf612b-ed4c-4dec-91ba-8f6fa1e7f361"}}, {"head": {"id": "ff8cbef0-e841-4700-8a24-0c8958ae549d", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869933100, "endTime": 315032870288400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "da7fcba5-626a-4214-bed9-d7f0930686dd", "logId": "90b2c895-a267-44e7-b4de-c2a08a58a803"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da7fcba5-626a-4214-bed9-d7f0930686dd", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869100200}, "additional": {"logType": "detail", "children": [], "durationId": "ff8cbef0-e841-4700-8a24-0c8958ae549d"}}, {"head": {"id": "482ed769-0dce-42b3-b246-12fd9a7e4ab7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ce04a0-71ec-4f2f-b013-d857d770abc9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869832600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a102e8-5abf-4c7f-a11a-9734b86371da", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869940900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3df10a8-b258-4f67-a91f-01b4cfb73794", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032870030000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9dde569-97ec-461d-a15f-2ae2a8d1f766", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032870081100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39573584-65a9-4d1c-915d-db7142954c4f", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032870153600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2b052b4-b36b-433e-b2ac-9003903a3759", "name": "runTaskFromQueue task cost before running: 4 s 562 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032870234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b2c895-a267-44e7-b4de-c2a08a58a803", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032869933100, "endTime": 315032870288400, "totalTime": 279000}, "additional": {"logType": "info", "children": [], "durationId": "ff8cbef0-e841-4700-8a24-0c8958ae549d"}}, {"head": {"id": "7fb834d6-60c0-43b1-b540-8747b94ff2a6", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032873117400, "endTime": 315032902648200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "70add016-d298-40df-814d-2d514a47f21a", "logId": "c6fca097-8cd8-490e-91be-e38c218fb0ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70add016-d298-40df-814d-2d514a47f21a", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032871890200}, "additional": {"logType": "detail", "children": [], "durationId": "7fb834d6-60c0-43b1-b540-8747b94ff2a6"}}, {"head": {"id": "64955b61-f182-494a-beff-5498ee34287d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032872395900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72999145-1561-4e22-a397-45c73552c7a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032872472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62652c0a-f66f-4378-864f-ef8c2c18ef1e", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032873126300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e5d78f-0cea-43f6-a4ac-c18e2a7015c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032873511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f02a61e-5970-4222-b0ce-f5e2d8c40752", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032873591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45059b58-575b-4bd0-a80a-36bd4b27ec50", "name": "entry : default@CopyPreviewProfile cost memory 0.154876708984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032902143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a6a74c-d849-441b-b08a-516e5bc04d46", "name": "runTaskFromQueue task cost before running: 4 s 595 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032902453800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6fca097-8cd8-490e-91be-e38c218fb0ec", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032873117400, "endTime": 315032902648200, "totalTime": 29270100}, "additional": {"logType": "info", "children": [], "durationId": "7fb834d6-60c0-43b1-b540-8747b94ff2a6"}}, {"head": {"id": "229f6dd5-59de-4a99-a85d-955af1152803", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911011400, "endTime": 315032911924500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "400a8502-6f59-4dcc-8ab4-c94585ef9c7a", "logId": "0fbfd87c-7071-4094-81db-851de3924bae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "400a8502-6f59-4dcc-8ab4-c94585ef9c7a", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032907849000}, "additional": {"logType": "detail", "children": [], "durationId": "229f6dd5-59de-4a99-a85d-955af1152803"}}, {"head": {"id": "c807d9d2-ecef-4070-9627-cdc2cadf4165", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032909060600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d79b9f65-ceb6-435b-9cf6-7acfa9724bb7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032909262600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2140e585-474e-42ee-ac82-11f3e6219595", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138245e4-9fef-4ca8-9d00-5c6791bd34cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911261000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0376a9-1e03-4b91-9f67-4ff29f48785f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed5742c-d523-4423-b50a-2097f1871fe0", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92749fbb-1836-4e53-a64f-1246f32c98f1", "name": "runTaskFromQueue task cost before running: 4 s 604 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fbfd87c-7071-4094-81db-851de3924bae", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032911011400, "endTime": 315032911924500, "totalTime": 741300}, "additional": {"logType": "info", "children": [], "durationId": "229f6dd5-59de-4a99-a85d-955af1152803"}}, {"head": {"id": "943e3c86-c451-432b-b44b-413ce244ad20", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032916013700, "endTime": 315032916613200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b29e8a79-b259-4659-b347-2b069be3d97a", "logId": "947101ee-7fd0-4d2f-8cb3-6457f79de10d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b29e8a79-b259-4659-b347-2b069be3d97a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032915896900}, "additional": {"logType": "detail", "children": [], "durationId": "943e3c86-c451-432b-b44b-413ce244ad20"}}, {"head": {"id": "85bd4001-3cc4-4a31-8bef-98508c0042ea", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032916031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "551109bf-6cff-4bf0-8634-f186297618c4", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032916285800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d88e58c-abdc-4106-a162-5769c42f9a6e", "name": "runTaskFromQueue task cost before running: 4 s 609 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032916478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947101ee-7fd0-4d2f-8cb3-6457f79de10d", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032916013700, "endTime": 315032916613200, "totalTime": 414900}, "additional": {"logType": "info", "children": [], "durationId": "943e3c86-c451-432b-b44b-413ce244ad20"}}, {"head": {"id": "bcf1491d-a690-4283-9501-d3d10fff2fc6", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032924449200, "endTime": 315032929489100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "28c40d44-d38f-42d7-aba3-ce24f325293d", "logId": "814fc63e-f5da-4ee1-be32-91ee5bc46579"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28c40d44-d38f-42d7-aba3-ce24f325293d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032921175000}, "additional": {"logType": "detail", "children": [], "durationId": "bcf1491d-a690-4283-9501-d3d10fff2fc6"}}, {"head": {"id": "0ea6f964-26db-4c01-bbe1-be4d00a70fad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032922374600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f547c1d-7e7e-49af-94aa-c20c69b5be50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032922624100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5afb9d-387d-49cb-87b7-30f2b82400b9", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032924469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a595840-342e-4a6d-a778-7b101c773cf8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032927419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5130f75-6e76-4747-85a1-d01bcbf5f7a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032927604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1063c95c-7ddc-4d5b-becc-0d0329e42ebd", "name": "entry : default@PreviewUpdateAssets cost memory 0.09586334228515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032929104400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02730999-1cf2-473d-9cf5-695dfed5549e", "name": "runTaskFromQueue task cost before running: 4 s 621 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032929344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814fc63e-f5da-4ee1-be32-91ee5bc46579", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032924449200, "endTime": 315032929489100, "totalTime": 4845800}, "additional": {"logType": "info", "children": [], "durationId": "bcf1491d-a690-4283-9501-d3d10fff2fc6"}}, {"head": {"id": "8dca5a38-c2c9-42ba-a20d-9b98c976e506", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032942276200, "endTime": 315054145341600}, "additional": {"children": ["55765640-bc3f-49f5-bcad-c1f1521c2623"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d4cd25df-fbda-460d-a0e8-840d8e4ac845", "logId": "2e09ddf2-5b6c-4812-8209-48df364499a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4cd25df-fbda-460d-a0e8-840d8e4ac845", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032933706500}, "additional": {"logType": "detail", "children": [], "durationId": "8dca5a38-c2c9-42ba-a20d-9b98c976e506"}}, {"head": {"id": "7052b4eb-1011-43a7-a552-e1c5f0491f3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032934602600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b459ba4a-ee47-4897-b3bc-117ca2c94cbc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032934783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03692d40-9b97-4063-80a8-ccf88d506190", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032942295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55765640-bc3f-49f5-bcad-c1f1521c2623", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315032975513000, "endTime": 315054137170500}, "additional": {"children": ["44eafc07-ccbb-4998-913c-d43de0aea581", "cc0791b9-bdf7-4c12-9766-f4e87b5cb889", "e234a651-8389-4504-880f-1573f9cbc415", "a3e88a9c-0586-4e8f-ba48-b55721bb44c8", "bac815ac-d020-456d-9937-ec7666c3d3e3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8dca5a38-c2c9-42ba-a20d-9b98c976e506", "logId": "6dd90448-906f-41f7-aa61-63d9739fef96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c5dc15d-1316-4886-8e5b-d422c7eed637", "name": "entry : default@PreviewArkTS cost memory -5.379920959472656", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032978140900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "588d09c3-c340-4ca2-be1c-8b56099f6843", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315037749661300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44eafc07-ccbb-4998-913c-d43de0aea581", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315037750855200, "endTime": 315037750873800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "55765640-bc3f-49f5-bcad-c1f1521c2623", "logId": "f95940f6-ad98-4174-8175-7027d0222e20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f95940f6-ad98-4174-8175-7027d0222e20", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315037750855200, "endTime": 315037750873800}, "additional": {"logType": "info", "children": [], "durationId": "44eafc07-ccbb-4998-913c-d43de0aea581", "parent": "6dd90448-906f-41f7-aa61-63d9739fef96"}}, {"head": {"id": "b003d389-8e59-4caf-9b48-802a00837c3c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054133169600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc0791b9-bdf7-4c12-9766-f4e87b5cb889", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315054136845700, "endTime": 315054136878800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "55765640-bc3f-49f5-bcad-c1f1521c2623", "logId": "4da395fb-cf5a-4cc4-ba47-52e561ec5726"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4da395fb-cf5a-4cc4-ba47-52e561ec5726", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054136845700, "endTime": 315054136878800}, "additional": {"logType": "info", "children": [], "durationId": "cc0791b9-bdf7-4c12-9766-f4e87b5cb889", "parent": "6dd90448-906f-41f7-aa61-63d9739fef96"}}, {"head": {"id": "6dd90448-906f-41f7-aa61-63d9739fef96", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315032975513000, "endTime": 315054137170500}, "additional": {"logType": "info", "children": ["f95940f6-ad98-4174-8175-7027d0222e20", "4da395fb-cf5a-4cc4-ba47-52e561ec5726", "8bfe4f70-ffae-42ff-bdd6-7add3bc7bf8f", "b064b318-6961-4fbf-a35b-390bd75828ad", "87ad0193-77ab-40b9-8c9a-e587bc7dc40e"], "durationId": "55765640-bc3f-49f5-bcad-c1f1521c2623", "parent": "2e09ddf2-5b6c-4812-8209-48df364499a5"}}, {"head": {"id": "e234a651-8389-4504-880f-1573f9cbc415", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315036217790700, "endTime": 315037725568500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "55765640-bc3f-49f5-bcad-c1f1521c2623", "logId": "8bfe4f70-ffae-42ff-bdd6-7add3bc7bf8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bfe4f70-ffae-42ff-bdd6-7add3bc7bf8f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315036217790700, "endTime": 315037725568500}, "additional": {"logType": "info", "children": [], "durationId": "e234a651-8389-4504-880f-1573f9cbc415", "parent": "6dd90448-906f-41f7-aa61-63d9739fef96"}}, {"head": {"id": "a3e88a9c-0586-4e8f-ba48-b55721bb44c8", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315037725769700, "endTime": 315037725974000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "55765640-bc3f-49f5-bcad-c1f1521c2623", "logId": "b064b318-6961-4fbf-a35b-390bd75828ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b064b318-6961-4fbf-a35b-390bd75828ad", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315037725769700, "endTime": 315037725974000}, "additional": {"logType": "info", "children": [], "durationId": "a3e88a9c-0586-4e8f-ba48-b55721bb44c8", "parent": "6dd90448-906f-41f7-aa61-63d9739fef96"}}, {"head": {"id": "bac815ac-d020-456d-9937-ec7666c3d3e3", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker5", "startTime": 315037726096500, "endTime": 315054114571000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "55765640-bc3f-49f5-bcad-c1f1521c2623", "logId": "87ad0193-77ab-40b9-8c9a-e587bc7dc40e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87ad0193-77ab-40b9-8c9a-e587bc7dc40e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315037726096500, "endTime": 315054114571000}, "additional": {"logType": "info", "children": [], "durationId": "bac815ac-d020-456d-9937-ec7666c3d3e3", "parent": "6dd90448-906f-41f7-aa61-63d9739fef96"}}, {"head": {"id": "2e09ddf2-5b6c-4812-8209-48df364499a5", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315032942276200, "endTime": 315054145341600, "totalTime": 21203040400}, "additional": {"logType": "info", "children": ["6dd90448-906f-41f7-aa61-63d9739fef96"], "durationId": "8dca5a38-c2c9-42ba-a20d-9b98c976e506"}}, {"head": {"id": "fd88ba0b-e6e8-4f26-8e7f-2773f3387450", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156380600, "endTime": 315054156965200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e45d0713-29c7-4713-b128-971805298f59", "logId": "b72c4e08-0837-4412-87b6-89d3667fe485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e45d0713-29c7-4713-b128-971805298f59", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156283900}, "additional": {"logType": "detail", "children": [], "durationId": "fd88ba0b-e6e8-4f26-8e7f-2773f3387450"}}, {"head": {"id": "952bb9dd-ac9e-4127-aa10-ece259a6f2f8", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e09f323-fe33-449a-b0a5-49ca16a1813c", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413be2de-3d8f-4edc-9552-edb33a1c256b", "name": "runTaskFromQueue task cost before running: 25 s 849 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72c4e08-0837-4412-87b6-89d3667fe485", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054156380600, "endTime": 315054156965200, "totalTime": 410900}, "additional": {"logType": "info", "children": [], "durationId": "fd88ba0b-e6e8-4f26-8e7f-2773f3387450"}}, {"head": {"id": "565db48f-4f58-4e21-895f-2942e5c1499c", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054169202300, "endTime": 315054169239300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebda020d-2e82-4c65-a76c-eef72750c695", "logId": "bea96bf0-04ff-4fc9-8d85-82fc7e5a26cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bea96bf0-04ff-4fc9-8d85-82fc7e5a26cf", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054169202300, "endTime": 315054169239300}, "additional": {"logType": "info", "children": [], "durationId": "565db48f-4f58-4e21-895f-2942e5c1499c"}}, {"head": {"id": "8ace1c78-8f64-4573-ae40-d4321da3a20f", "name": "BUILD SUCCESSFUL in 25 s 861 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054169331200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "59be2f35-940f-4afb-85ab-2381fafc5df7", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315028308364800, "endTime": 315054169851900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 18}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "de2a7880-0e3a-4997-a4a8-1cface4ed2fc", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054171643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "783152af-542f-491b-8d7a-1328151513e0", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054172586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902b07aa-9f64-492e-b57a-f99d1e750524", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054173338400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795e54e1-6e94-4ec0-a381-b94d55c68707", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054174103500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1a4d88-c737-49e5-9937-bd9cbca9230d", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054174776700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfdf1939-f294-40e7-8e5a-e4cf4cffde20", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054175439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff45f7d8-e2e9-4b22-9837-0a1e99def75b", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054176102600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7371dc8-68af-4f88-b29a-0bcaf93d1cc7", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054176739700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a487f71-ce09-4466-8c8c-30c83ca52fb1", "name": "Update task entry:default@PreBuild input file:F:\\e-wallet\\harmony\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054177440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "470572d3-4e85-4d90-affa-7c4bdb419b34", "name": "Incremental task entry:default@PreBuild post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054178376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da5b316a-81dd-4b5c-b858-49d2c9d6f108", "name": "Update task entry:default@MergeProfile input file:F:\\e-wallet\\harmony\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054178582800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e966224-055a-4e51-a8e4-b26a69b0bcf9", "name": "Update task entry:default@MergeProfile input file:F:\\e-wallet\\harmony\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054179539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a105c791-e065-45c1-9a96-a542a6a51ef2", "name": "Update task entry:default@MergeProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054180211400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f76db4-f7b9-4a65-b674-a864599e5d23", "name": "Update task entry:default@MergeProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054180887800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5596ce26-1f67-43ae-a658-ad51d80c93b1", "name": "Incremental task entry:default@MergeProfile post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054181777900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cce9aa3-c706-437e-be2b-f07d54b88d47", "name": "Update task entry:default@CreateBuildProfile input file:F:\\e-wallet\\harmony\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054181976000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3a77ac-049c-4306-a93d-793bedd83f80", "name": "Update task entry:default@CreateBuildProfile input file:F:\\e-wallet\\harmony\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054182624700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f787a5b-1a9b-4e09-8a5d-38740348bc21", "name": "Update task entry:default@CreateBuildProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054183623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdad433a-5ced-40a1-b899-a07543c6e612", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054184394600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7af8bc0-dc9f-45ce-acf1-0bbc30816493", "name": "Update task entry:default@GeneratePkgContextInfo output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054184587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23805ea8-1e7d-444a-b3d7-4539baedb7c0", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054185485000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97cb8276-e7ff-4e33-8b26-4b6bd05f5cf7", "name": "Update task entry:default@ProcessProfile input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054185705300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deee0331-4d83-4a0e-9432-9a21d70d9a7e", "name": "Update task entry:default@ProcessProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054186417000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d03ae0b-5bdd-4315-bcca-ebddf5eb3066", "name": "Incremental task entry:default@ProcessProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054187139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3373cb4f-b456-429f-a3ff-e2c0196fee2d", "name": "Update task entry:default@ProcessRouterMap input file:F:\\e-wallet\\harmony\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054190046400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38088c89-5862-44e8-807e-2c501c9e724f", "name": "Update task entry:default@ProcessRouterMap input file:F:\\e-wallet\\harmony\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054190760800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00cd9a0a-e20c-4edc-83e2-084bf8bfdf71", "name": "Update task entry:default@ProcessRouterMap input file:F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054191376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c4c1ef-3180-48ea-aa30-8a236f75c957", "name": "Update task entry:default@ProcessRouterMap input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054191976000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f53af401-41d8-49d6-bfb8-8e3948eb34ac", "name": "Update task entry:default@ProcessRouterMap output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054192578900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f9c86b-1071-4546-ad4e-d344aa78fdd5", "name": "Update task entry:default@ProcessRouterMap output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054193180700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc604ea2-7679-4e53-943b-0c947364bf57", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054194174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aade5634-f419-4490-b426-1869a96825bb", "name": "Update task entry:default@GenerateLoaderJson input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054219111900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af170c02-bbaf-4520-8c01-fa3a612790e9", "name": "Update task entry:default@GenerateLoaderJson input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054220364800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bca9b81-1b3c-404e-8271-5cc12ac45560", "name": "Update task entry:default@GenerateLoaderJson input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054221614200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e5eee2-a080-442d-9605-9452bd7ae4e7", "name": "Update task entry:default@GenerateLoaderJson output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054223253900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90cb0f0d-8cc3-4bb4-94e2-63f87c904beb", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:34 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054227659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e640952-26be-4b28-8bcd-ca54ecde966a", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054229391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50f14aa-b5f1-4140-9ed4-43b0572d7bb0", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054250955300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23c22b5f-ff53-4df5-b6ac-4f002e0696d7", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054251628700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06f8ced0-8b3d-4faa-9fd5-1b44696625a5", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054276722500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ccdf88-6b0a-49ab-a00a-64ced4a3a207", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:51 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054278397100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ec0ac0-f079-49b8-b2d8-11cf3df0cb7a", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054278872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c823c4-da19-4e39-8c3d-21999099955d", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054280713800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ae7e43-a007-4bf9-9d72-eb9a4b95a2e5", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054282679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf7ddfd-9e9c-4f5b-ac1e-ed92cc1a69b5", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054283457500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6fce0a6-e41a-41b8-adb5-aedaa9f1841f", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054284177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1776f20e-539d-466d-a1ef-989bff8afd8b", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054284926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d28ec219-598b-4274-9a3c-0c36283deddd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054291352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8f2a08-0fb8-471e-9bb4-ed8b9789ce0b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054293197300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc4b423-7b13-4c9c-a2cd-958d38dce15c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054293913200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c4f9f4d-84f8-4099-9aaf-a4344035598e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054294610900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74bcd46-c073-4bba-848a-8378c61330ad", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054296880400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57857b84-dd06-4081-964e-ac18544e2b61", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054322563500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8942ba-325c-4a1d-95d9-7965a02fa18e", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054323191200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00cd9d1f-b96c-493b-a773-b1751ed03223", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054323759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ebea5c5-2141-4dbb-a86f-faab1b31bf0b", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054324302300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e27fbd-7389-4cfd-a23c-54873696f7f7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:40 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054324825900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}