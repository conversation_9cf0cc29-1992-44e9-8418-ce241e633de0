<template>
  <div class="payment-container">
    <!-- 用户余额显示 -->
    <div class="balance-section">
      <el-card class="balance-card">
        <div class="balance-info">
          <div class="balance-label">支付中心</div>
        </div>
      </el-card>
    </div>

    <!-- 支付方式选择 -->
    <div class="payment-methods">
      <h3>选择支付方式</h3>
      <div class="payment-grid">
        <!-- 钱包支付 -->
        <el-card class="payment-card" @click="handleWalletPayment">
          <div class="payment-icon wallet-icon">💳</div>
          <div class="payment-title">钱包支付</div>
          <div class="payment-desc">使用钱包余额支付</div>
        </el-card>

        <!-- 银行卡支付 -->
        <el-card class="payment-card" @click="handleCardPayment">
          <div class="payment-icon card-icon">🏦</div>
          <div class="payment-title">银行卡支付</div>
          <div class="payment-desc">使用银行卡支付</div>
        </el-card>

        <!-- 扫码支付 -->
        <el-card class="payment-card" @click="handleQRPayment">
          <div class="payment-icon qr-icon">📱</div>
          <div class="payment-title">扫码支付</div>
          <div class="payment-desc">扫描二维码支付</div>
        </el-card>

        <!-- NFC支付 -->
        <el-card class="payment-card" @click="handleNFCPayment">
          <div class="payment-icon nfc-icon">📡</div>
          <div class="payment-title">NFC支付</div>
          <div class="payment-desc">近场通信支付</div>
        </el-card>
      </div>
    </div>

    <!-- 钱包支付对话框 -->
    <el-dialog v-model="walletDialog" title="钱包支付" width="500px">
      <el-form label-width="80px">
        <el-form-item label="商品">
          <el-input v-model="paymentForm.merchant" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="paymentForm.amount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="paymentForm.password" type="password" placeholder="请输入支付密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="walletDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmWalletPayment">确认支付</el-button>
      </template>
    </el-dialog>

    <!-- 银行卡支付对话框 -->
    <el-dialog v-model="cardDialog" title="银行卡支付" width="500px">
      <el-form label-width="80px">
        <el-form-item label="商品">
          <el-input v-model="cardForm.merchant" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="cardForm.amount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="银行卡">
          <el-select v-model="cardForm.cardId" placeholder="请选择银行卡" style="width: 100%">
            <el-option
              v-for="card in mockCards"
              :key="card.id"
              :label="`${card.bankName} (**** ${card.cardNumber.slice(-4)})`"
              :value="card.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="cardForm.password" type="password" placeholder="请输入支付密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cardDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmCardPayment">确认支付</el-button>
      </template>
    </el-dialog>

    <!-- 扫码支付对话框 -->
    <el-dialog v-model="qrDialog" title="扫码支付" width="400px">
      <div class="qr-payment">
        <div class="qr-title">请扫描下方二维码完成支付</div>
        <div class="qr-code">
          <div class="qr-placeholder">
            <div class="qr-icon">📱</div>
            <div>扫码支付</div>
          </div>
        </div>
        <div class="qr-tip">使用支付宝、微信等扫码支付</div>
      </div>
      <template #footer>
        <el-button @click="qrDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmQRPayment">支付完成</el-button>
      </template>
    </el-dialog>

    <!-- NFC支付对话框 -->
    <el-dialog v-model="nfcDialog" title="NFC支付" width="400px">
      <div class="nfc-payment">
        <div class="nfc-status">
          <div class="nfc-icon">📡</div>
          <div class="nfc-title">{{ nfcStatus.title }}</div>
          <div class="nfc-desc">{{ nfcStatus.description }}</div>
        </div>
        <div v-if="nfcPaymentInfo.amount" class="nfc-info">
          <div class="info-item">
            <span>商品:</span>
            <span>{{ nfcPaymentInfo.merchant }}</span>
          </div>
          <div class="info-item">
            <span>金额:</span>
            <span class="amount">¥{{ nfcPaymentInfo.amount }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelNFCPayment">
          {{ nfcStatus.status === 'success' ? '完成' : '取消' }}
        </el-button>
        <el-button 
          v-if="nfcStatus.status === 'waiting'" 
          type="primary" 
          @click="simulateNFCPayment"
        >
          模拟NFC支付
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TransactionStorage } from '../utils/transactionStorage.js'

// 用户信息
const userInfo = reactive({
  username: '用户',
  balance: 509.00
})

// 对话框控制
const walletDialog = ref(false)
const cardDialog = ref(false)
const qrDialog = ref(false)
const nfcDialog = ref(false)

// 支付表单
const paymentForm = reactive({
  merchant: '',
  amount: '',
  password: ''
})

const cardForm = reactive({
  merchant: '',
  amount: '',
  cardId: '',
  password: ''
})

// NFC支付状态
const nfcStatus = reactive({
  status: 'waiting',
  title: '等待NFC设备',
  description: '请将支持NFC的设备靠近感应区域'
})

// NFC支付信息
const nfcPaymentInfo = reactive({
  merchant: '',
  amount: ''
})

// 模拟银行卡数据
const mockCards = reactive([
  {
    id: 1,
    bankName: '中国银行',
    cardNumber: '****************',
    cardType: '储蓄卡'
  },
  {
    id: 2,
    bankName: '工商银行',
    cardNumber: '****************',
    cardType: '储蓄卡'
  },
  {
    id: 3,
    bankName: '建设银行',
    cardNumber: '****************',
    cardType: '储蓄卡'
  }
])

// 支付方法
const handleWalletPayment = () => {
  // 设置默认商品名称
  if (!paymentForm.merchant) {
    paymentForm.merchant = '商品购买'
  }
  walletDialog.value = true
}

const handleCardPayment = () => {
  // 设置默认商品名称
  if (!cardForm.merchant) {
    cardForm.merchant = '商品购买'
  }
  cardDialog.value = true
}

const handleQRPayment = () => {
  qrDialog.value = true
}

const handleNFCPayment = () => {
  Object.assign(nfcStatus, {
    status: 'waiting',
    title: '等待NFC设备',
    description: '请将支持NFC的设备靠近感应区域'
  })
  
  Object.assign(nfcPaymentInfo, {
    merchant: '',
    amount: ''
  })
  
  nfcDialog.value = true
}

// 初始化用户信息
const initUserInfo = () => {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    userInfo.username = user.username || '用户'
    userInfo.balance = user.balance || 1000.00 // 获取钱包余额
  } catch (error) {
    console.error('获取用户信息失败:', error)
    userInfo.username = '用户'
    userInfo.balance = 1000.00 // 默认余额
  }
}

// 钱包支付确认
const confirmWalletPayment = () => {
  if (!paymentForm.merchant || !paymentForm.amount || !paymentForm.password) {
    ElMessage.error('请填写完整信息')
    return
  }

  // 验证支付密码
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    const userPaymentPassword = user.paymentPassword || '123456'
    if (paymentForm.password !== userPaymentPassword) {
      ElMessage.error('支付密码错误')
      return
    }
  } catch (error) {
    console.error('获取用户支付密码失败:', error)
    if (paymentForm.password !== '123456') {
      ElMessage.error('支付密码错误')
      return
    }
  }

  const amount = Number(paymentForm.amount)
  if (amount <= 0) {
    ElMessage.error('支付金额必须大于0')
    return
  }

  // 检查钱包余额
  if (amount > userInfo.balance) {
    ElMessage.error(`钱包余额不足！当前余额：¥${userInfo.balance.toFixed(2)}，需要：¥${amount.toFixed(2)}`)
    return
  }

  // 扣减钱包余额
  userInfo.balance -= amount

  // 保存更新后的用户信息到本地存储
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    user.balance = userInfo.balance
    localStorage.setItem('user', JSON.stringify(user))
  } catch (error) {
    console.error('保存用户余额失败:', error)
  }

  // 创建交易记录
  const transaction = {
    transactionType: 'PAYMENT',
    amount: amount,
    targetAccount: paymentForm.merchant,
    paymentMethod: 'WALLET',
    paymentChannel: 'MERCHANT'
  }

  // 保存交易记录
  const savedTransaction = TransactionStorage.addTransaction(transaction)
  if (savedTransaction) {
    console.log('钱包支付交易记录已保存:', savedTransaction)

    // 触发自定义事件通知其他页面
    window.dispatchEvent(new CustomEvent('transactionAdded', {
      detail: savedTransaction
    }))

    // 触发余额更新事件
    window.dispatchEvent(new CustomEvent('balanceUpdated', {
      detail: { balance: userInfo.balance }
    }))
  }

  ElMessage.success(`钱包支付成功！商品：${paymentForm.merchant}，金额：¥${amount.toFixed(2)}`)
  walletDialog.value = false

  // 重置表单
  Object.assign(paymentForm, {
    merchant: '',
    amount: '',
    password: ''
  })
}

// 银行卡支付确认
const confirmCardPayment = () => {
  if (!cardForm.merchant || !cardForm.amount || !cardForm.cardId || !cardForm.password) {
    ElMessage.error('请填写完整信息')
    return
  }

  // 验证支付密码
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    const userPaymentPassword = user.paymentPassword || '123456'
    if (cardForm.password !== userPaymentPassword) {
      ElMessage.error('支付密码错误')
      return
    }
  } catch (error) {
    console.error('获取用户支付密码失败:', error)
    if (cardForm.password !== '123456') {
      ElMessage.error('支付密码错误')
      return
    }
  }

  const amount = Number(cardForm.amount)
  if (amount <= 0) {
    ElMessage.error('支付金额必须大于0')
    return
  }

  const selectedCard = mockCards.find(card => card.id === cardForm.cardId)

  // 创建交易记录
  const transaction = {
    transactionType: 'PAYMENT',
    amount: amount,
    targetAccount: cardForm.merchant,
    paymentMethod: 'BANK_CARD',
    paymentChannel: 'MERCHANT',
    cardInfo: selectedCard ? `${selectedCard.bankName} ****${selectedCard.cardNumber.slice(-4)}` : ''
  }

  // 保存交易记录
  const savedTransaction = TransactionStorage.addTransaction(transaction)
  if (savedTransaction) {
    console.log('银行卡支付交易记录已保存:', savedTransaction)

    // 触发自定义事件通知其他页面
    window.dispatchEvent(new CustomEvent('transactionAdded', {
      detail: savedTransaction
    }))
  }

  ElMessage.success(`银行卡支付成功！商品：${cardForm.merchant}，金额：¥${amount.toFixed(2)}，银行卡：${selectedCard?.bankName} ****${selectedCard?.cardNumber.slice(-4)}`)
  cardDialog.value = false

  // 重置表单
  Object.assign(cardForm, {
    merchant: '',
    amount: '',
    cardId: '',
    password: ''
  })
}

// 扫码支付确认
const confirmQRPayment = () => {
  // 模拟扫码支付完成
  const amount = Math.floor(Math.random() * 100) + 10
  const merchants = ['星巴克咖啡', '麦当劳', '肯德基', '便利店', '超市购物']
  const merchant = merchants[Math.floor(Math.random() * merchants.length)]

  // 创建交易记录
  const transaction = {
    transactionType: 'PAYMENT',
    amount: amount,
    targetAccount: merchant,
    paymentMethod: 'WALLET',
    paymentChannel: 'QR_CODE'
  }

  // 保存交易记录
  const savedTransaction = TransactionStorage.addTransaction(transaction)
  if (savedTransaction) {
    console.log('扫码支付交易记录已保存:', savedTransaction)

    // 触发自定义事件通知其他页面
    window.dispatchEvent(new CustomEvent('transactionAdded', {
      detail: savedTransaction
    }))
  }

  ElMessage.success(`扫码支付成功！商品：${merchant}，金额：¥${amount.toFixed(2)}`)
  qrDialog.value = false
}

// 模拟NFC支付
const simulateNFCPayment = async () => {
  try {
    // 第一步：检测设备
    Object.assign(nfcStatus, {
      status: 'detected',
      title: '设备已连接',
      description: '正在读取支付信息...'
    })

    await new Promise(resolve => setTimeout(resolve, 1500))

    // 第二步：显示支付信息
    const amount = Math.floor(Math.random() * 100) + 10
    const merchants = ['星巴克咖啡', '麦当劳', '肯德基', '便利店', '超市购物', 'NFC便民店']
    const merchant = merchants[Math.floor(Math.random() * merchants.length)]

    Object.assign(nfcPaymentInfo, {
      merchant: merchant,
      amount: amount.toFixed(2)
    })

    Object.assign(nfcStatus, {
      status: 'processing',
      title: '正在处理支付',
      description: '请稍候，正在安全处理您的支付...'
    })

    await new Promise(resolve => setTimeout(resolve, 2000))

    // 第三步：支付成功
    Object.assign(nfcStatus, {
      status: 'success',
      title: '支付成功',
      description: '您的NFC支付已完成'
    })

    // 创建交易记录
    const transaction = {
      transactionType: 'PAYMENT',
      amount: amount,
      targetAccount: merchant,
      paymentMethod: 'WALLET',
      paymentChannel: 'NFC'
    }

    // 保存交易记录
    const savedTransaction = TransactionStorage.addTransaction(transaction)
    if (savedTransaction) {
      console.log('NFC支付交易记录已保存:', savedTransaction)

      // 触发自定义事件通知其他页面
      window.dispatchEvent(new CustomEvent('transactionAdded', {
        detail: savedTransaction
      }))
    }

    ElMessage.success(`NFC支付成功！商品：${merchant}，金额：¥${amount.toFixed(2)}`)

    // 3秒后自动关闭对话框
    setTimeout(() => {
      nfcDialog.value = false
    }, 3000)

  } catch (error) {
    Object.assign(nfcStatus, {
      status: 'error',
      title: '支付失败',
      description: '支付过程中出现错误，请重试'
    })
    ElMessage.error('NFC支付失败')
  }
}

// 取消NFC支付
const cancelNFCPayment = () => {
  nfcDialog.value = false

  // 重置状态
  Object.assign(nfcStatus, {
    status: 'waiting',
    title: '等待NFC设备',
    description: '请将支持NFC的设备靠近感应区域'
  })

  Object.assign(nfcPaymentInfo, {
    merchant: '',
    amount: ''
  })
}

onMounted(() => {
  initUserInfo()
  TransactionStorage.initMockData()
})
</script>

<style scoped>
.payment-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.balance-section {
  margin-bottom: 30px;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.balance-info {
  text-align: center;
  padding: 20px;
}

.balance-label {
  font-size: 24px;
  font-weight: bold;
  color: white;
}



.payment-methods h3 {
  margin-bottom: 20px;
  color: #333;
}

.payment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.payment-card {
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  padding: 20px;
}

.payment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.payment-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.payment-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.payment-desc {
  font-size: 14px;
  color: #666;
}

.qr-payment {
  text-align: center;
  padding: 20px;
}

.qr-title {
  margin-bottom: 20px;
  font-size: 16px;
  color: #666;
}

.qr-code {
  margin: 20px 0;
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: #f9f9f9;
  color: #999;
}

.qr-placeholder .qr-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.qr-tip {
  font-size: 14px;
  color: #999;
  margin-top: 15px;
}

.nfc-payment {
  text-align: center;
  padding: 20px;
}

.nfc-status {
  margin-bottom: 20px;
}

.nfc-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.nfc-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.nfc-desc {
  font-size: 14px;
  color: #666;
}

.nfc-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.amount {
  color: #e74c3c;
  font-weight: bold;
  font-size: 16px;
}
</style>
