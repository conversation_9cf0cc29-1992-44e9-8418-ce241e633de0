import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';

@Entry
@Component
struct ChangePayPasswordPage {
  @State currentPayPassword: string = '';
  @State newPayPassword: string = '';
  @State confirmPayPassword: string = '';
  @State isLoading: boolean = false;

  async handleSave() {
    if (!this.currentPayPassword.trim()) {
      promptAction.showToast({ message: '请输入当前支付密码' });
      return;
    }

    if (this.currentPayPassword.length !== 6) {
      promptAction.showToast({ message: '当前支付密码应为6位数字' });
      return;
    }

    if (!this.newPayPassword.trim()) {
      promptAction.showToast({ message: '请输入新支付密码' });
      return;
    }

    if (this.newPayPassword.length !== 6) {
      promptAction.showToast({ message: '新支付密码应为6位数字' });
      return;
    }

    // 验证是否为纯数字
    const numberRegex = /^\d{6}$/;
    if (!numberRegex.test(this.newPayPassword)) {
      promptAction.showToast({ message: '支付密码只能包含数字' });
      return;
    }

    if (!this.confirmPayPassword.trim()) {
      promptAction.showToast({ message: '请确认新支付密码' });
      return;
    }

    if (this.newPayPassword !== this.confirmPayPassword) {
      promptAction.showToast({ message: '两次输入的支付密码不一致' });
      return;
    }

    if (this.currentPayPassword === this.newPayPassword) {
      promptAction.showToast({ message: '新支付密码不能与当前密码相同' });
      return;
    }

    this.isLoading = true;
    try {
      // 调用API修改支付密码
      await UserApi.changePayPassword({
        currentPayPassword: this.currentPayPassword,
        newPayPassword: this.newPayPassword
      });

      promptAction.showToast({ message: '支付密码修改成功' });
      router.back();
    } catch (error) {
      console.error('支付密码修改失败:', error);
      promptAction.showToast({ message: '支付密码修改失败，请检查当前密码是否正确' });
    } finally {
      this.isLoading = false;
    }
  }

  handleCancel() {
    router.back();
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('修改支付密码')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button('确认修改')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .enabled(!this.isLoading)
          .onClick(() => {
            this.handleSave();
          })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({ width: { bottom: 1 }, color: '#E5E7EB' })

      // 表单内容
      Column() {
        // 当前支付密码
        Column() {
          Row() {
            Text('当前支付密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(120)

            TextInput({ placeholder: '请输入当前支付密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Number)
              .maxLength(6)
              .onChange((value: string) => {
                this.currentPayPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 新支付密码
        Column() {
          Row() {
            Text('新支付密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(120)

            TextInput({ placeholder: '请输入新支付密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Number)
              .maxLength(6)
              .onChange((value: string) => {
                this.newPayPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 确认支付密码
        Column() {
          Row() {
            Text('确认支付密码')
              .fontSize(16)
              .fontColor('#333333')
              .width(120)

            TextInput({ placeholder: '请确认新支付密码' })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Number)
              .maxLength(6)
              .onChange((value: string) => {
                this.confirmPayPassword = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
      }
      .padding(20)
      .backgroundColor('#FFFFFF')
      .margin({ top: 16, left: 16, right: 16 })
      .borderRadius(12)

      // 支付密码要求提示
      Column() {
        Text('支付密码要求：')
          .fontSize(14)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        Text('• 支付密码必须为6位数字')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text('• 请勿使用生日、手机号等易被猜测的密码')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text('• 建议定期更换支付密码')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
      }
      .width('100%')
      .padding(20)
      .margin({ top: 16, left: 16, right: 16 })
      .backgroundColor('#F8F9FA')
      .borderRadius(12)

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
