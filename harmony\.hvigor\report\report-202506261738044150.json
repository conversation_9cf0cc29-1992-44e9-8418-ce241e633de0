{"version": "2.0", "ppid": 5188, "events": [{"head": {"id": "5bf91110-033d-497a-a97a-9d2cd1fdecb0", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472558447200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d9cf084-5fa8-408f-99f9-cab8e1218311", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472563200300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b478fad-ea6b-4333-a147-a7ccf4f1822e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472563601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7495a917-6dbe-4473-ae4a-fbe199dc6c37", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472569803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e291f8a4-2e37-409d-ba39-5df46c1a93af", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472570497400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1557881-0bbc-4eb1-b3a6-dd6c22ce31ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472767122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472775606300, "endTime": 6472978146000}, "additional": {"children": ["02f60698-a494-43ee-8457-17f83206b558", "8767f214-de30-4e51-8e56-75377f61a34f", "8ca6282a-00cd-4335-acef-7c346b5d72fe", "1a2aa0ee-30f7-4682-8c11-cf5659b10f42", "a433feb0-6faf-480b-a400-f9f8447bb459", "44bf6a94-3131-4e3d-8c2a-e16373618250", "5fcaf4a8-10ca-4ddf-b664-d31738dae8cb"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02f60698-a494-43ee-8457-17f83206b558", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472775618500, "endTime": 6472802560700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "2abc3d66-c4e8-4a14-bb49-dd3df7fe4fe4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8767f214-de30-4e51-8e56-75377f61a34f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472802585100, "endTime": 6472976850500}, "additional": {"children": ["f91f4930-6739-459c-abb5-571268becc9d", "b9a179b8-f39a-481d-b456-82f6d414c95b", "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "4173f813-58de-4e35-9177-d94d728ddc85", "42af564f-aad0-4f89-8087-fc19bdb83fc1", "b5265a51-b62f-4844-9872-aecdbfccb716", "c4ca8812-b0c3-4642-920e-fc8508dc4208", "7155aab9-8c49-42be-b222-20a80b4ffc7f", "2c8c89f5-627d-44b7-a571-6bb900a6754c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ca6282a-00cd-4335-acef-7c346b5d72fe", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472976874400, "endTime": 6472978116700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "974c264a-dd24-4fa5-aa8b-e3005bde0d62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a2aa0ee-30f7-4682-8c11-cf5659b10f42", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472978122700, "endTime": 6472978139600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "b345dae7-563d-4467-9097-289e66155f36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a433feb0-6faf-480b-a400-f9f8447bb459", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472779171800, "endTime": 6472779221100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "8db1012a-74c0-40e1-85a2-2886c6570346"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8db1012a-74c0-40e1-85a2-2886c6570346", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472779171800, "endTime": 6472779221100}, "additional": {"logType": "info", "children": [], "durationId": "a433feb0-6faf-480b-a400-f9f8447bb459", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "44bf6a94-3131-4e3d-8c2a-e16373618250", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472785368700, "endTime": 6472785388700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "0ea49c24-2ccf-4abe-ae6e-b604d3a53f76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ea49c24-2ccf-4abe-ae6e-b604d3a53f76", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472785368700, "endTime": 6472785388700}, "additional": {"logType": "info", "children": [], "durationId": "44bf6a94-3131-4e3d-8c2a-e16373618250", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "bff142b3-99ac-45d5-814d-a861e0a1e271", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472798808200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15f60bd1-5245-49fd-aeec-4bf970cff2c7", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472802378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2abc3d66-c4e8-4a14-bb49-dd3df7fe4fe4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472775618500, "endTime": 6472802560700}, "additional": {"logType": "info", "children": [], "durationId": "02f60698-a494-43ee-8457-17f83206b558", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "f91f4930-6739-459c-abb5-571268becc9d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472809144200, "endTime": 6472809157000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "63888e8f-b9d1-4350-9a0c-4f1a6d82c137"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9a179b8-f39a-481d-b456-82f6d414c95b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472809178700, "endTime": 6472815233000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "4af8338e-9f70-4f68-ba7d-c0cf5ca13e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472815246500, "endTime": 6472902292000}, "additional": {"children": ["d9967ce4-16aa-431f-9b54-aaf5949069a5", "400f5c8e-f8a8-450c-bb18-63d3f5e3ce40", "0d494e2b-90ef-4c87-82d9-ba0a2e300b25"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "aa54ebc2-5914-44c2-843d-5980eef9b14c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4173f813-58de-4e35-9177-d94d728ddc85", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472902308500, "endTime": 6472928790300}, "additional": {"children": ["d499a7e1-bb8f-424c-987f-66c828ab83c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "6de28a75-ce0a-4362-850e-26a88875e8cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42af564f-aad0-4f89-8087-fc19bdb83fc1", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472928798400, "endTime": 6472952745200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "9993edb4-c31b-42ab-b372-c7e670900bd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5265a51-b62f-4844-9872-aecdbfccb716", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472954261800, "endTime": 6472963700900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "3db76113-293a-4420-ae4a-e960efeda4d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4ca8812-b0c3-4642-920e-fc8508dc4208", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472963719400, "endTime": 6472976570700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "8f6bbac7-703c-4f66-a329-737958c1c160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7155aab9-8c49-42be-b222-20a80b4ffc7f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472976589800, "endTime": 6472976837100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "68df3418-bfb7-4f89-9eec-c60c0622297d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63888e8f-b9d1-4350-9a0c-4f1a6d82c137", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472809144200, "endTime": 6472809157000}, "additional": {"logType": "info", "children": [], "durationId": "f91f4930-6739-459c-abb5-571268becc9d", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "4af8338e-9f70-4f68-ba7d-c0cf5ca13e1c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472809178700, "endTime": 6472815233000}, "additional": {"logType": "info", "children": [], "durationId": "b9a179b8-f39a-481d-b456-82f6d414c95b", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "d9967ce4-16aa-431f-9b54-aaf5949069a5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472816163300, "endTime": 6472816181200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "logId": "c095c600-dd14-4b78-8921-4c973ff57a61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c095c600-dd14-4b78-8921-4c973ff57a61", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472816163300, "endTime": 6472816181200}, "additional": {"logType": "info", "children": [], "durationId": "d9967ce4-16aa-431f-9b54-aaf5949069a5", "parent": "aa54ebc2-5914-44c2-843d-5980eef9b14c"}}, {"head": {"id": "400f5c8e-f8a8-450c-bb18-63d3f5e3ce40", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472818806400, "endTime": 6472900839000}, "additional": {"children": ["e27b8d7e-207a-4dcd-ac41-1d2717a1ec3e", "54731518-bd56-47db-8e08-9370c4ce4b69"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "logId": "75bcd059-8638-41fb-94b7-636fc6a321cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e27b8d7e-207a-4dcd-ac41-1d2717a1ec3e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472818807700, "endTime": 6472826266700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "400f5c8e-f8a8-450c-bb18-63d3f5e3ce40", "logId": "3105baaa-336b-4f17-a98b-c04bce48c93d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54731518-bd56-47db-8e08-9370c4ce4b69", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472826300600, "endTime": 6472900828200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "400f5c8e-f8a8-450c-bb18-63d3f5e3ce40", "logId": "3126901b-6b24-40a2-9858-755cdd33deff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81fb5132-640a-4e9f-8e8d-30c0688e33ec", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472818816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8ecf9c-e231-44df-bf10-6d3fb327b63a", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472826097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3105baaa-336b-4f17-a98b-c04bce48c93d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472818807700, "endTime": 6472826266700}, "additional": {"logType": "info", "children": [], "durationId": "e27b8d7e-207a-4dcd-ac41-1d2717a1ec3e", "parent": "75bcd059-8638-41fb-94b7-636fc6a321cc"}}, {"head": {"id": "37d6e5a5-f814-4a12-b88c-c51483ff88ca", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472826316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85b42794-a2ed-4f85-9b7f-3717f0b89b98", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472834895200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7ee65d-65c1-4417-b56f-5d02f298a5d4", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472835039300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69592739-a112-4375-bd7a-380c92016988", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472835400300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54b9d02-8976-45ad-bf58-8e23455fae16", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472835676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53071ae5-4504-4b20-a0c0-0776b5eb1cea", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472837524600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dccafda6-34c3-4837-b211-2026ca3e7b77", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472842398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d47491-9b6c-42dd-8c07-a280bba4fae1", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472853194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe209f3-2089-4d5a-af90-71aef23c30dc", "name": "Sdk init in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472876967600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f549b00-a5d0-458d-b33b-12b43022210d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472877122300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 38}, "markType": "other"}}, {"head": {"id": "8d8168d9-eab0-474a-ba13-b31123ace6a2", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472877168900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 38}, "markType": "other"}}, {"head": {"id": "9dc2b214-5522-46de-baf6-1457f10f3e35", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472900531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09ed9033-2e0d-4b03-a626-c88f693e3445", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472900643400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83628c42-236e-49b7-8018-a8cda8e074c0", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472900721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce66141b-5e90-48b4-8b98-0bf025b02b91", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472900778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3126901b-6b24-40a2-9858-755cdd33deff", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472826300600, "endTime": 6472900828200}, "additional": {"logType": "info", "children": [], "durationId": "54731518-bd56-47db-8e08-9370c4ce4b69", "parent": "75bcd059-8638-41fb-94b7-636fc6a321cc"}}, {"head": {"id": "75bcd059-8638-41fb-94b7-636fc6a321cc", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472818806400, "endTime": 6472900839000}, "additional": {"logType": "info", "children": ["3105baaa-336b-4f17-a98b-c04bce48c93d", "3126901b-6b24-40a2-9858-755cdd33deff"], "durationId": "400f5c8e-f8a8-450c-bb18-63d3f5e3ce40", "parent": "aa54ebc2-5914-44c2-843d-5980eef9b14c"}}, {"head": {"id": "0d494e2b-90ef-4c87-82d9-ba0a2e300b25", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472902092900, "endTime": 6472902265400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "logId": "48fcc89c-5db2-484b-afc4-780886d961f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48fcc89c-5db2-484b-afc4-780886d961f9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472902092900, "endTime": 6472902265400}, "additional": {"logType": "info", "children": [], "durationId": "0d494e2b-90ef-4c87-82d9-ba0a2e300b25", "parent": "aa54ebc2-5914-44c2-843d-5980eef9b14c"}}, {"head": {"id": "aa54ebc2-5914-44c2-843d-5980eef9b14c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472815246500, "endTime": 6472902292000}, "additional": {"logType": "info", "children": ["c095c600-dd14-4b78-8921-4c973ff57a61", "75bcd059-8638-41fb-94b7-636fc6a321cc", "48fcc89c-5db2-484b-afc4-780886d961f9"], "durationId": "7fec2555-ee05-4bc7-8d72-87e18f9de3ce", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "d499a7e1-bb8f-424c-987f-66c828ab83c4", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472903177500, "endTime": 6472928777300}, "additional": {"children": ["a0506117-84c8-4632-90fe-99b8ba1d0482", "a122d76b-f932-4aab-9cfd-e18dc1ce2c01", "2191b0bc-7084-435e-a734-3bf36100711f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4173f813-58de-4e35-9177-d94d728ddc85", "logId": "45d56462-b207-48ec-a8a5-dbbe07f32b38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0506117-84c8-4632-90fe-99b8ba1d0482", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472907348500, "endTime": 6472907373900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d499a7e1-bb8f-424c-987f-66c828ab83c4", "logId": "4ee0bd36-2188-439b-bc93-13227e98d4a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ee0bd36-2188-439b-bc93-13227e98d4a3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472907348500, "endTime": 6472907373900}, "additional": {"logType": "info", "children": [], "durationId": "a0506117-84c8-4632-90fe-99b8ba1d0482", "parent": "45d56462-b207-48ec-a8a5-dbbe07f32b38"}}, {"head": {"id": "a122d76b-f932-4aab-9cfd-e18dc1ce2c01", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472909872200, "endTime": 6472927078700}, "additional": {"children": ["971a562b-8b9e-435f-90c5-7a987ed6bc1d", "d3eb2620-17df-42f2-b4f5-0bbe5d5f7679"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d499a7e1-bb8f-424c-987f-66c828ab83c4", "logId": "1cf263f9-b9b5-4b4d-8683-55f3c1be7345"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "971a562b-8b9e-435f-90c5-7a987ed6bc1d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472909873100, "endTime": 6472914705300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a122d76b-f932-4aab-9cfd-e18dc1ce2c01", "logId": "0c8d3064-0f43-471a-beb6-8c87712f9849"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3eb2620-17df-42f2-b4f5-0bbe5d5f7679", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472914721200, "endTime": 6472927067100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a122d76b-f932-4aab-9cfd-e18dc1ce2c01", "logId": "57024fa9-51df-42df-b42a-bcfa174ac171"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e33ef6a8-e2ca-416d-aa28-b8f54d78e289", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472909907000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d70fb563-dc4f-42fb-9960-857e72bc6515", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472914581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8d3064-0f43-471a-beb6-8c87712f9849", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472909873100, "endTime": 6472914705300}, "additional": {"logType": "info", "children": [], "durationId": "971a562b-8b9e-435f-90c5-7a987ed6bc1d", "parent": "1cf263f9-b9b5-4b4d-8683-55f3c1be7345"}}, {"head": {"id": "3ad5f1d5-c9ab-4c04-a674-4fc056e77526", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472914735100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99261426-b4f0-4bb6-a9b2-f241d88e853c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472922236800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2393ffda-7ce7-4a99-a8aa-dd0a8c2fad20", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472922551700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d14da71-2c2f-45be-b002-a8ca7b7d631f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472922847100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d38f0e95-4209-4f79-8fca-5cce74eca7cb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472923040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405b6fc3-5fa1-451f-9dfb-97fc12d979c0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472923127800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c220f73a-bacf-4a5c-ae78-fa05513f16db", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472923237500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a5c9ff-a13f-44e3-bd83-c9b51d840ee9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472923302100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d99d76-0f04-4c78-8ec0-563f6b73c6d8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472926710800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f151117-a339-44da-b578-6231f5b97381", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472926845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06008971-d3b6-4baf-83b6-8d204395ea23", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472926940900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a9ea91e-d1c2-4437-bc41-1ade7bdd43d5", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472927000000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57024fa9-51df-42df-b42a-bcfa174ac171", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472914721200, "endTime": 6472927067100}, "additional": {"logType": "info", "children": [], "durationId": "d3eb2620-17df-42f2-b4f5-0bbe5d5f7679", "parent": "1cf263f9-b9b5-4b4d-8683-55f3c1be7345"}}, {"head": {"id": "1cf263f9-b9b5-4b4d-8683-55f3c1be7345", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472909872200, "endTime": 6472927078700}, "additional": {"logType": "info", "children": ["0c8d3064-0f43-471a-beb6-8c87712f9849", "57024fa9-51df-42df-b42a-bcfa174ac171"], "durationId": "a122d76b-f932-4aab-9cfd-e18dc1ce2c01", "parent": "45d56462-b207-48ec-a8a5-dbbe07f32b38"}}, {"head": {"id": "2191b0bc-7084-435e-a734-3bf36100711f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472928749300, "endTime": 6472928764100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d499a7e1-bb8f-424c-987f-66c828ab83c4", "logId": "5be0e5c7-9685-4ed7-8686-c930b13ad31c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5be0e5c7-9685-4ed7-8686-c930b13ad31c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472928749300, "endTime": 6472928764100}, "additional": {"logType": "info", "children": [], "durationId": "2191b0bc-7084-435e-a734-3bf36100711f", "parent": "45d56462-b207-48ec-a8a5-dbbe07f32b38"}}, {"head": {"id": "45d56462-b207-48ec-a8a5-dbbe07f32b38", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472903177500, "endTime": 6472928777300}, "additional": {"logType": "info", "children": ["4ee0bd36-2188-439b-bc93-13227e98d4a3", "1cf263f9-b9b5-4b4d-8683-55f3c1be7345", "5be0e5c7-9685-4ed7-8686-c930b13ad31c"], "durationId": "d499a7e1-bb8f-424c-987f-66c828ab83c4", "parent": "6de28a75-ce0a-4362-850e-26a88875e8cc"}}, {"head": {"id": "6de28a75-ce0a-4362-850e-26a88875e8cc", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472902308500, "endTime": 6472928790300}, "additional": {"logType": "info", "children": ["45d56462-b207-48ec-a8a5-dbbe07f32b38"], "durationId": "4173f813-58de-4e35-9177-d94d728ddc85", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "086de611-9bae-40c1-9a82-61ed32fee406", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472952163700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c31922-8025-423f-a579-7d488973e1e6", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472952653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9993edb4-c31b-42ab-b372-c7e670900bd1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472928798400, "endTime": 6472952745200}, "additional": {"logType": "info", "children": [], "durationId": "42af564f-aad0-4f89-8087-fc19bdb83fc1", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "2c8c89f5-627d-44b7-a571-6bb900a6754c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472953973000, "endTime": 6472954237500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8767f214-de30-4e51-8e56-75377f61a34f", "logId": "f354a076-f390-4aa5-a8ad-4ff86f913466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "691a002c-9f0d-4daf-aba1-aaaf3a62c454", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472954013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f354a076-f390-4aa5-a8ad-4ff86f913466", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472953973000, "endTime": 6472954237500}, "additional": {"logType": "info", "children": [], "durationId": "2c8c89f5-627d-44b7-a571-6bb900a6754c", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "bf763cbc-0326-46bf-8d2f-3badd5c560d5", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472956509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52aa691-609d-4f3a-a36d-005b2e5bf90c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472962863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db76113-293a-4420-ae4a-e960efeda4d2", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472954261800, "endTime": 6472963700900}, "additional": {"logType": "info", "children": [], "durationId": "b5265a51-b62f-4844-9872-aecdbfccb716", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "d75be578-7e78-4cab-8377-1409f1a5844d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472963733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d062d8f2-8c0c-48fc-8e7f-d410acdb6e65", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472970013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1df6343-7565-4d02-98b3-075ccd26a3db", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472970128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e64bbe4-b031-4975-96a7-a028860169ef", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472970387500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0cd4338-f285-4ee1-9894-e7c899675c1b", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472973371500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04bcdd75-4b53-4693-bd1e-deb3e757c171", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472973476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6bbac7-703c-4f66-a329-737958c1c160", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472963719400, "endTime": 6472976570700}, "additional": {"logType": "info", "children": [], "durationId": "c4ca8812-b0c3-4642-920e-fc8508dc4208", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "44e6632e-ca20-424e-9003-61c27e24ef31", "name": "Configuration phase cost:168 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472976735300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68df3418-bfb7-4f89-9eec-c60c0622297d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472976589800, "endTime": 6472976837100}, "additional": {"logType": "info", "children": [], "durationId": "7155aab9-8c49-42be-b222-20a80b4ffc7f", "parent": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8"}}, {"head": {"id": "346aa2a6-95fe-466e-99d3-8c8dd8c253b8", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472802585100, "endTime": 6472976850500}, "additional": {"logType": "info", "children": ["63888e8f-b9d1-4350-9a0c-4f1a6d82c137", "4af8338e-9f70-4f68-ba7d-c0cf5ca13e1c", "aa54ebc2-5914-44c2-843d-5980eef9b14c", "6de28a75-ce0a-4362-850e-26a88875e8cc", "9993edb4-c31b-42ab-b372-c7e670900bd1", "3db76113-293a-4420-ae4a-e960efeda4d2", "8f6bbac7-703c-4f66-a329-737958c1c160", "68df3418-bfb7-4f89-9eec-c60c0622297d", "f354a076-f390-4aa5-a8ad-4ff86f913466"], "durationId": "8767f214-de30-4e51-8e56-75377f61a34f", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "5fcaf4a8-10ca-4ddf-b664-d31738dae8cb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472978092600, "endTime": 6472978105600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc", "logId": "ce096cb7-1f7c-487c-9db4-c2d28ca0ffc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce096cb7-1f7c-487c-9db4-c2d28ca0ffc0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472978092600, "endTime": 6472978105600}, "additional": {"logType": "info", "children": [], "durationId": "5fcaf4a8-10ca-4ddf-b664-d31738dae8cb", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "974c264a-dd24-4fa5-aa8b-e3005bde0d62", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472976874400, "endTime": 6472978116700}, "additional": {"logType": "info", "children": [], "durationId": "8ca6282a-00cd-4335-acef-7c346b5d72fe", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "b345dae7-563d-4467-9097-289e66155f36", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472978122700, "endTime": 6472978139600}, "additional": {"logType": "info", "children": [], "durationId": "1a2aa0ee-30f7-4682-8c11-cf5659b10f42", "parent": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69"}}, {"head": {"id": "b645866a-25a9-4a0a-ada0-e3d7f54e7e69", "name": "init", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472775606300, "endTime": 6472978146000}, "additional": {"logType": "info", "children": ["2abc3d66-c4e8-4a14-bb49-dd3df7fe4fe4", "346aa2a6-95fe-466e-99d3-8c8dd8c253b8", "974c264a-dd24-4fa5-aa8b-e3005bde0d62", "b345dae7-563d-4467-9097-289e66155f36", "8db1012a-74c0-40e1-85a2-2886c6570346", "0ea49c24-2ccf-4abe-ae6e-b604d3a53f76", "ce096cb7-1f7c-487c-9db4-c2d28ca0ffc0"], "durationId": "d1ca2bf9-0c76-46c1-a20d-4715b63d18dc"}}, {"head": {"id": "cc91b074-2933-4153-9f97-3819f147a535", "name": "Configuration task cost before running: 207 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472978437400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36341349-c870-4640-81ce-d6306cc3b121", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472987921900, "endTime": 6473003160800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1aa6f17a-241c-4f7a-ad67-e07cbca5eeac", "logId": "1f37f96f-26d4-492c-9191-317cb6e83a50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1aa6f17a-241c-4f7a-ad67-e07cbca5eeac", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472980786200}, "additional": {"logType": "detail", "children": [], "durationId": "36341349-c870-4640-81ce-d6306cc3b121"}}, {"head": {"id": "7eea7a0f-5629-43c2-bc36-d4f12926ca64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472982127800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eda38a2-9bb8-4faf-bc76-ebcd5a237269", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472982267400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e722c54c-a96d-4d31-a69c-18b9788e1ddf", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472987941400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4491ce87-ef98-451c-8a21-77a47d394dce", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473002824100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fae4362-b8d8-46ba-8af3-6161629c6ce1", "name": "entry : default@PreBuild cost memory 0.36519622802734375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473003029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f37f96f-26d4-492c-9191-317cb6e83a50", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472987921900, "endTime": 6473003160800}, "additional": {"logType": "info", "children": [], "durationId": "36341349-c870-4640-81ce-d6306cc3b121"}}, {"head": {"id": "97190e83-246e-428c-816e-0f02c018aa7e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473009632200, "endTime": 6473012656200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dfd96811-36ff-4396-99c7-269461a8b74d", "logId": "1a18141b-62aa-4524-be02-d6cf42943b33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfd96811-36ff-4396-99c7-269461a8b74d", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473007905100}, "additional": {"logType": "detail", "children": [], "durationId": "97190e83-246e-428c-816e-0f02c018aa7e"}}, {"head": {"id": "8aa898c3-8558-4b92-8232-108d0bbb4661", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473008588000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d2de5e-a39d-4280-99b1-5707716c1a0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473008697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d52ac5-6299-4cef-8d29-8a50416538e9", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473009645200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8c81b4-c75e-465a-bf1d-52e2b4750a31", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473012460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "686d2150-3bbe-4257-a787-74a91abed122", "name": "entry : default@MergeProfile cost memory 0.1293792724609375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473012579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a18141b-62aa-4524-be02-d6cf42943b33", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473009632200, "endTime": 6473012656200}, "additional": {"logType": "info", "children": [], "durationId": "97190e83-246e-428c-816e-0f02c018aa7e"}}, {"head": {"id": "f5c06de6-25f0-4b6b-8f65-b0955adb1a07", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473017314100, "endTime": 6473064070100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fc5a15c0-8cef-4695-893e-b70b8e0736ca", "logId": "6dae6c5c-ceed-4c6a-b545-d3dc1d071121"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc5a15c0-8cef-4695-893e-b70b8e0736ca", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473014851000}, "additional": {"logType": "detail", "children": [], "durationId": "f5c06de6-25f0-4b6b-8f65-b0955adb1a07"}}, {"head": {"id": "c7dfd906-19b3-415a-8682-edfefe8973cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473015553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7422854-a7aa-43c0-bca8-b4d91c6eefb5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473015765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e01dfa1c-7d0a-49b4-968b-1c344f021f74", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473017334900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e252073b-5c28-4f68-bdaf-6b6ea16b6aee", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473018941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63856df-7e77-4b18-b87e-757431018483", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 45 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473063772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cbe9cd2-5484-46e8-8169-bd05b21314eb", "name": "entry : default@CreateBuildProfile cost memory 0.12122344970703125", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473063964300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dae6c5c-ceed-4c6a-b545-d3dc1d071121", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473017314100, "endTime": 6473064070100}, "additional": {"logType": "info", "children": [], "durationId": "f5c06de6-25f0-4b6b-8f65-b0955adb1a07"}}, {"head": {"id": "dd787cfc-1db8-4006-94ae-34711ca3f058", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473068349500, "endTime": 6473069135900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a277396e-69ae-46c0-8619-6fb7d10dfe44", "logId": "653348d9-58d1-42c1-9d15-014717f4634c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a277396e-69ae-46c0-8619-6fb7d10dfe44", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473066596700}, "additional": {"logType": "detail", "children": [], "durationId": "dd787cfc-1db8-4006-94ae-34711ca3f058"}}, {"head": {"id": "bb7f8d7e-43ed-4067-aea1-93561fddfbe7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473067213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0b94c3-acd6-4431-8ec5-3ef10ed7be24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473067334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b2aced-7d7a-447e-b28d-7ae6d8e56500", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473068368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a62b9f-19fc-4643-95d1-98bb646474cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 647**********}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da05cc3-7ba6-4cf4-8877-d281cbd27ba2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473068742200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e35f581-3cfe-45a9-85c9-423141dd6c7e", "name": "entry : default@PreCheckSyscap cost memory 0.04291534423828125", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473068958800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2fce0c-b67f-4fff-abbb-9c5acc70dd1f", "name": "runTaskFromQueue task cost before running: 298 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473069068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653348d9-58d1-42c1-9d15-014717f4634c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473068349500, "endTime": 6473069135900, "totalTime": 696700}, "additional": {"logType": "info", "children": [], "durationId": "dd787cfc-1db8-4006-94ae-34711ca3f058"}}, {"head": {"id": "06afd2ad-b912-4d02-bd4c-1a10d92247ac", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473092408500, "endTime": 6473093852300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e0288093-6eac-4503-9e0b-def8c7318180", "logId": "85f32f17-1735-4a82-a8a6-5bdbe68a59db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0288093-6eac-4503-9e0b-def8c7318180", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473071293300}, "additional": {"logType": "detail", "children": [], "durationId": "06afd2ad-b912-4d02-bd4c-1a10d92247ac"}}, {"head": {"id": "eafe4f59-5da5-4052-8133-563fb1482d33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473071876000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bf2c9a-e0a0-4a5b-929c-62c4f2e5517c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473071985800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2edf5e55-86e3-4664-b74b-188cabc14fef", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473092430400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13596c11-d100-4317-86ac-b90dc108690a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473092795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ca092d0-36d9-4378-bbca-21fefba4e639", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473093645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3f8945-0e3e-45de-a8e8-9731e5af221d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07411956787109375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473093765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f32f17-1735-4a82-a8a6-5bdbe68a59db", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473092408500, "endTime": 6473093852300}, "additional": {"logType": "info", "children": [], "durationId": "06afd2ad-b912-4d02-bd4c-1a10d92247ac"}}, {"head": {"id": "427091eb-004c-4c2e-bd8c-d998ae8921d5", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473098501000, "endTime": 6473107420600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5187fca6-7041-42e8-ba47-1c7ba356ab0c", "logId": "1c6f34c6-ee8e-4420-a63b-e665227d0634"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5187fca6-7041-42e8-ba47-1c7ba356ab0c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473095826400}, "additional": {"logType": "detail", "children": [], "durationId": "427091eb-004c-4c2e-bd8c-d998ae8921d5"}}, {"head": {"id": "76d242cf-4937-42bc-b949-2fb80b3c7ba1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473096397200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8a3e5f-0d73-4559-8b94-736ea3a0a74e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473096500800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f64b9ed-5f30-43fb-8d1c-592791032102", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473098514500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee09bcf3-0598-4c76-99f9-7e9f6ea9aba4", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473107164500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d94b39a-c111-43b4-a237-4143fa7c88a2", "name": "entry : default@ProcessProfile cost memory 0.06255340576171875", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473107334600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c6f34c6-ee8e-4420-a63b-e665227d0634", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473098501000, "endTime": 6473107420600}, "additional": {"logType": "info", "children": [], "durationId": "427091eb-004c-4c2e-bd8c-d998ae8921d5"}}, {"head": {"id": "5b29f7cc-d014-40e7-b639-a407a7c262ec", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473112176900, "endTime": 6473148728100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "735dc602-7e34-4928-a395-0cc49c53e9d2", "logId": "7510e76c-477f-45a6-bbaf-832978aecacf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "735dc602-7e34-4928-a395-0cc49c53e9d2", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473109465000}, "additional": {"logType": "detail", "children": [], "durationId": "5b29f7cc-d014-40e7-b639-a407a7c262ec"}}, {"head": {"id": "7196123c-97a5-4af0-b4bf-1387125be1d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473110045900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "923141aa-c2f5-4333-a650-552190ba640e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473110150100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114cdb3c-360d-4df2-8b92-29fcf586394a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473112194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a898c40a-9d36-44ef-8c61-5ea76d53a482", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 35 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473148230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8b71e6-ad53-439d-9e08-5af781466f66", "name": "entry : default@ProcessRouterMap cost memory 0.200775146484375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473148530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7510e76c-477f-45a6-bbaf-832978aecacf", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473112176900, "endTime": 6473148728100}, "additional": {"logType": "info", "children": [], "durationId": "5b29f7cc-d014-40e7-b639-a407a7c262ec"}}, {"head": {"id": "cfbb6bcf-8a6f-4bcf-a77b-b21b0673f332", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473201999100, "endTime": 6473221122500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "436454d2-09a5-4fc8-a430-789724c64f73", "logId": "5f7c1da6-4f91-440e-acdb-9cdf8f0b5eea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "436454d2-09a5-4fc8-a430-789724c64f73", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473156858800}, "additional": {"logType": "detail", "children": [], "durationId": "cfbb6bcf-8a6f-4bcf-a77b-b21b0673f332"}}, {"head": {"id": "f8497131-88ea-45a2-ad7d-9099b9524e9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473158096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564e2056-141b-4479-bed8-ec392151fa9a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473158331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff2a479-da02-4471-9941-af63dd35f3f6", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473161881200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ee1daf-d10a-4df5-bd8f-151f760204a3", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473216584500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96350962-4707-40cc-bc0f-ffd9bdc3b8f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473217076100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40a86636-c977-4225-af0d-3b59c9a08ff7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473217258900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2d4dbf-4c97-4389-bdf3-b39d8d8ced52", "name": "entry : default@PreviewProcessResource cost memory 0.086273193359375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473217483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983feaf7-72a2-423c-ab02-0ebc3a2aca73", "name": "runTaskFromQueue task cost before running: 449 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473220892100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f7c1da6-4f91-440e-acdb-9cdf8f0b5eea", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473201999100, "endTime": 6473221122500, "totalTime": 15663800}, "additional": {"logType": "info", "children": [], "durationId": "cfbb6bcf-8a6f-4bcf-a77b-b21b0673f332"}}, {"head": {"id": "6ceb8f67-f55e-463f-a015-8a3076d0c757", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473241293100, "endTime": 6473294987300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5a11985c-5ffb-400d-af2e-af5179d73acc", "logId": "41f87236-207f-425c-99f2-1f44de0e6d33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a11985c-5ffb-400d-af2e-af5179d73acc", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473228920100}, "additional": {"logType": "detail", "children": [], "durationId": "6ceb8f67-f55e-463f-a015-8a3076d0c757"}}, {"head": {"id": "2acc8e73-f220-4ef5-868a-0cc02f02ef43", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473230169700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabd3b06-0635-4cef-bbce-fe01da16e99a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473230393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8038de7d-f3d7-490d-ac45-964331d2b469", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473241337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cf1e01-79e7-4830-a79d-a231e3a07c62", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473294719200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0396881b-8205-4cd3-8135-26041cf7e4fb", "name": "entry : default@GenerateLoaderJson cost memory 0.7723770141601562", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473294891800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41f87236-207f-425c-99f2-1f44de0e6d33", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473241293100, "endTime": 6473294987300}, "additional": {"logType": "info", "children": [], "durationId": "6ceb8f67-f55e-463f-a015-8a3076d0c757"}}, {"head": {"id": "f7359ba8-8d96-4c67-8f6f-e3c00a047ae1", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473307896500, "endTime": 6474113132800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7e6f99b8-45af-4967-97af-35637a6ef397", "logId": "fd7be993-f7e5-4d0d-8da4-a58ed90c8aa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e6f99b8-45af-4967-97af-35637a6ef397", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473303807100}, "additional": {"logType": "detail", "children": [], "durationId": "f7359ba8-8d96-4c67-8f6f-e3c00a047ae1"}}, {"head": {"id": "de536413-e06f-45f1-bf05-13bba59fb865", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473304379200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a157482b-5a0e-4e86-94cd-31d895b90518", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473304508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec7fd25-7222-4fce-9c99-4649b7c3c10d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473305591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3854736d-51fc-4d1f-811c-95a25e17819c", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473307999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71364be9-2e3d-42cd-8eca-aa61f5814c4b", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 804 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474112842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1115f86e-3279-4c90-832a-72b04016519e", "name": "entry : default@PreviewCompileResource cost memory 0.81207275390625", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474113019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7be993-f7e5-4d0d-8da4-a58ed90c8aa4", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6473307896500, "endTime": 6474113132800}, "additional": {"logType": "info", "children": [], "durationId": "f7359ba8-8d96-4c67-8f6f-e3c00a047ae1"}}, {"head": {"id": "70db6398-6cc5-4f98-8aef-75ca5896add9", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474117726100, "endTime": 6474118655000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "97f59964-f47f-47ee-a025-d8e5d45d8761", "logId": "cf5f291b-ae92-4006-bcfa-53dc2937a3a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97f59964-f47f-47ee-a025-d8e5d45d8761", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474116689300}, "additional": {"logType": "detail", "children": [], "durationId": "70db6398-6cc5-4f98-8aef-75ca5896add9"}}, {"head": {"id": "8a10f2f3-4234-48dc-9bac-7e06e9bf11c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474117351400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0825d7e2-a4fc-4b72-8eb7-954fad17deff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474117506900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1342c2b6-fdb5-4f8a-9a53-bba76d254d27", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474117750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0004944a-4c72-4a64-b84c-b96dfcbced12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474118018500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e1549f4-5bb0-45a6-bec2-74baf1366e0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474118143500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e72111-6769-4093-b7a1-89b1a186eee7", "name": "entry : default@PreviewHookCompileResource cost memory 0.038848876953125", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474118348700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e7b600-30f4-42e7-a1e5-5ddf5d8bfedb", "name": "runTaskFromQueue task cost before running: 1 s 347 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474118530000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5f291b-ae92-4006-bcfa-53dc2937a3a8", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474117726100, "endTime": 6474118655000, "totalTime": 760700}, "additional": {"logType": "info", "children": [], "durationId": "70db6398-6cc5-4f98-8aef-75ca5896add9"}}, {"head": {"id": "a661492f-8fac-47e1-92d0-c7e474259401", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474123239600, "endTime": 6474126433900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "73ecf0a8-6e75-4587-9a80-8c03b9b7b525", "logId": "a2f12a84-2918-4783-8397-fd272c201952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73ecf0a8-6e75-4587-9a80-8c03b9b7b525", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474121639200}, "additional": {"logType": "detail", "children": [], "durationId": "a661492f-8fac-47e1-92d0-c7e474259401"}}, {"head": {"id": "945df873-5107-43f3-9f2d-53de4fc54d90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474122325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc7766e-2d22-4ab2-a438-af072555cef9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474122445500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a3b860-7a81-4fbb-b592-b123e0d7cca1", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474123257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18450e17-cdd1-4f62-974c-4dfebdda576d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474126196100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c47851-86c0-4738-adf6-e9133db66a62", "name": "entry : default@CopyPreviewProfile cost memory 0.1103973388671875", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474126348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f12a84-2918-4783-8397-fd272c201952", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474123239600, "endTime": 6474126433900}, "additional": {"logType": "info", "children": [], "durationId": "a661492f-8fac-47e1-92d0-c7e474259401"}}, {"head": {"id": "26db13d0-175b-4374-9c00-afebeccb6655", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130270000, "endTime": 6474130958600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "272f47e6-8501-4699-a26a-2178331ed25f", "logId": "0210f059-f357-4f00-8a14-d727cdbcb951"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "272f47e6-8501-4699-a26a-2178331ed25f", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474128714900}, "additional": {"logType": "detail", "children": [], "durationId": "26db13d0-175b-4374-9c00-afebeccb6655"}}, {"head": {"id": "9c7cf2a7-c00c-4200-af8b-3b9a0e90a72e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474129322800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e233c22-9341-4add-aa02-bc98f964215d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474129433700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcfc59fd-3640-4949-825e-34573cd5a7db", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130282800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "800bfce4-c65a-4623-95fc-86202a731ac1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b904be14-1789-4a6a-9e5a-d5cdb11426e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130480300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31bdcc3b-0e8e-4c81-8ace-3954fa984ee1", "name": "entry : default@ReplacePreviewerPage cost memory 0.03887939453125", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d155395-bd5f-454f-9a43-36c22c7824b1", "name": "runTaskFromQueue task cost before running: 1 s 359 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0210f059-f357-4f00-8a14-d727cdbcb951", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474130270000, "endTime": 6474130958600, "totalTime": 589800}, "additional": {"logType": "info", "children": [], "durationId": "26db13d0-175b-4374-9c00-afebeccb6655"}}, {"head": {"id": "970746f9-87ad-4383-8822-16f022eb6f80", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133166800, "endTime": 6474133539300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7ebad306-00b8-48fe-80a2-93a0b2265dd8", "logId": "22a0dc49-c13d-4c7a-b96c-be27c7ba76d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ebad306-00b8-48fe-80a2-93a0b2265dd8", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133095600}, "additional": {"logType": "detail", "children": [], "durationId": "970746f9-87ad-4383-8822-16f022eb6f80"}}, {"head": {"id": "ee98a29b-2b80-4555-93d6-f20c4184b026", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133178800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718293d9-2f1e-4ba9-a0b8-1a6335a1b01d", "name": "entry : buildPreviewerResource cost memory 0.01215362548828125", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133364400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f166bb-15c8-4c88-be36-16ec5d5277a0", "name": "runTaskFromQueue task cost before running: 1 s 362 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a0dc49-c13d-4c7a-b96c-be27c7ba76d0", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474133166800, "endTime": 6474133539300, "totalTime": 281000}, "additional": {"logType": "info", "children": [], "durationId": "970746f9-87ad-4383-8822-16f022eb6f80"}}, {"head": {"id": "67d6f7b7-13c6-48b9-9f3a-28c741d046e5", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474137503500, "endTime": 6474140590300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "464488e2-c5e9-496f-a2de-3b0e8ebb7892", "logId": "0a71cc5a-70e7-45b1-aaaf-0661ae858a04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "464488e2-c5e9-496f-a2de-3b0e8ebb7892", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474135795000}, "additional": {"logType": "detail", "children": [], "durationId": "67d6f7b7-13c6-48b9-9f3a-28c741d046e5"}}, {"head": {"id": "ae3193ac-f8c3-4a13-831f-3d156b08c8ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474136445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "577ef2d1-98d6-4828-b5b4-8d60b767dc69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474136555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12465031-0359-4a8d-95ad-859fbb59df8b", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474137519800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bbce62f-62e4-409c-a5ed-6c994999015b", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474140373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81cd7b97-0d4d-4813-bce1-df4ecab3e4be", "name": "entry : default@PreviewUpdateAssets cost memory 0.11937713623046875", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474140502000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a71cc5a-70e7-45b1-aaaf-0661ae858a04", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474137503500, "endTime": 6474140590300}, "additional": {"logType": "info", "children": [], "durationId": "67d6f7b7-13c6-48b9-9f3a-28c741d046e5"}}, {"head": {"id": "8398c770-065f-400d-81b7-c37b69286dae", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474150943000, "endTime": 6496306241100}, "additional": {"children": ["673838b5-360e-46a8-96cb-f29a53f72aaf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "72151d40-a94f-4906-8912-4df9d6a701a3", "logId": "7dd6e334-8309-4b34-8259-b95417144ea3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72151d40-a94f-4906-8912-4df9d6a701a3", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474143468300}, "additional": {"logType": "detail", "children": [], "durationId": "8398c770-065f-400d-81b7-c37b69286dae"}}, {"head": {"id": "c064cd5f-dff5-46cc-b4fd-15d80c6da7ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474144121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1deb5f-179d-4f6b-8473-af1d20c93e0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474144232900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5ae7e7-3f2c-494b-b013-b45d7f25a82a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474150960500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a9f2c0d-09b4-427b-a3f5-b61544404c5f", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474178229600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394964f1-caad-432f-8cc1-4577caec146c", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474178482000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673838b5-360e-46a8-96cb-f29a53f72aaf", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6474249222200, "endTime": 6496302063400}, "additional": {"children": ["01e10554-a6a3-4be0-b21f-eb1db19e7bc4", "9046438e-2a25-447a-a9ee-f921990708d3", "5e973235-b227-4c80-8524-07b721ae9207", "7434a7a7-ec52-4a2a-9f5a-d6672e057463", "667dd819-cb71-4165-90f4-ad8a388c74c3", "6c9b6722-4e7f-49a2-a15f-508cad34cbec"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8398c770-065f-400d-81b7-c37b69286dae", "logId": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02cf6abc-94f4-4e19-9620-52005114a393", "name": "entry : default@PreviewArkTS cost memory 1.8654251098632812", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474289993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55299c07-9be5-49c4-bc31-9213964d5152", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6483451446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e10554-a6a3-4be0-b21f-eb1db19e7bc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6483453968300, "endTime": 6483454091300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "743a06e1-d804-4211-9a16-87eec62c69a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "743a06e1-d804-4211-9a16-87eec62c69a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6483453968300, "endTime": 6483454091300}, "additional": {"logType": "info", "children": [], "durationId": "01e10554-a6a3-4be0-b21f-eb1db19e7bc4", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "caaca031-f678-40f1-b71c-f1ef75ec43ea", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496300270400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9046438e-2a25-447a-a9ee-f921990708d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6496301810300, "endTime": 6496301836100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "db1c0d26-4e91-47b7-a076-352bc1a96642"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db1c0d26-4e91-47b7-a076-352bc1a96642", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496301810300, "endTime": 6496301836100}, "additional": {"logType": "info", "children": [], "durationId": "9046438e-2a25-447a-a9ee-f921990708d3", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "8cb082f6-6072-4352-8be1-dc760a7bf86d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6474249222200, "endTime": 6496302063400}, "additional": {"logType": "info", "children": ["743a06e1-d804-4211-9a16-87eec62c69a0", "db1c0d26-4e91-47b7-a076-352bc1a96642", "fa2588cf-7d8e-4415-9df9-4f8148eec684", "702daaf5-934a-48c0-a301-542215ed178c", "68815ad0-d917-41fe-bb04-be053712dbd6", "6cd6e446-97dd-46b6-a7db-8b6d84e60eef"], "durationId": "673838b5-360e-46a8-96cb-f29a53f72aaf", "parent": "7dd6e334-8309-4b34-8259-b95417144ea3"}}, {"head": {"id": "5e973235-b227-4c80-8524-07b721ae9207", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6477109781900, "endTime": 6482692288300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "fa2588cf-7d8e-4415-9df9-4f8148eec684"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa2588cf-7d8e-4415-9df9-4f8148eec684", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6477109781900, "endTime": 6482692288300}, "additional": {"logType": "info", "children": [], "durationId": "5e973235-b227-4c80-8524-07b721ae9207", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "7434a7a7-ec52-4a2a-9f5a-d6672e057463", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6482692544500, "endTime": 6483259071100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "702daaf5-934a-48c0-a301-542215ed178c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "702daaf5-934a-48c0-a301-542215ed178c", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6482692544500, "endTime": 6483259071100}, "additional": {"logType": "info", "children": [], "durationId": "7434a7a7-ec52-4a2a-9f5a-d6672e057463", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "667dd819-cb71-4165-90f4-ad8a388c74c3", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6483259579700, "endTime": 6483260219000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "68815ad0-d917-41fe-bb04-be053712dbd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68815ad0-d917-41fe-bb04-be053712dbd6", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6483259579700, "endTime": 6483260219000}, "additional": {"logType": "info", "children": [], "durationId": "667dd819-cb71-4165-90f4-ad8a388c74c3", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "6c9b6722-4e7f-49a2-a15f-508cad34cbec", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Worker1", "startTime": 6483260553700, "endTime": 6496300329500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "673838b5-360e-46a8-96cb-f29a53f72aaf", "logId": "6cd6e446-97dd-46b6-a7db-8b6d84e60eef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cd6e446-97dd-46b6-a7db-8b6d84e60eef", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6483260553700, "endTime": 6496300329500}, "additional": {"logType": "info", "children": [], "durationId": "6c9b6722-4e7f-49a2-a15f-508cad34cbec", "parent": "8cb082f6-6072-4352-8be1-dc760a7bf86d"}}, {"head": {"id": "7dd6e334-8309-4b34-8259-b95417144ea3", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6474150943000, "endTime": 6496306241100, "totalTime": 22155250000}, "additional": {"logType": "info", "children": ["8cb082f6-6072-4352-8be1-dc760a7bf86d"], "durationId": "8398c770-065f-400d-81b7-c37b69286dae"}}, {"head": {"id": "2c24fba8-425c-4e3c-adac-db60e085b717", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496312937600, "endTime": 6496313374800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ab344e04-ace6-48d9-b748-fdd4075749d5", "logId": "89a679bd-cbd3-4f21-992d-9660a60e2d28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab344e04-ace6-48d9-b748-fdd4075749d5", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496312839900}, "additional": {"logType": "detail", "children": [], "durationId": "2c24fba8-425c-4e3c-adac-db60e085b717"}}, {"head": {"id": "2bac26e3-5f0d-434e-8cc1-03514d109c45", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496312951800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "469b19f6-4547-4079-9ee8-84becdd0ec89", "name": "entry : PreviewBuild cost memory 0.011810302734375", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496313173900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b197e6-e9a8-44b2-92e0-db56cd0b5755", "name": "runTaskFromQueue task cost before running: 23 s 542 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496313298500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a679bd-cbd3-4f21-992d-9660a60e2d28", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496312937600, "endTime": 6496313374800, "totalTime": 325100}, "additional": {"logType": "info", "children": [], "durationId": "2c24fba8-425c-4e3c-adac-db60e085b717"}}, {"head": {"id": "f5f2b140-a8e1-4cda-8b39-7eec80fd2380", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496321291500, "endTime": 6496321313500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "835ad1b0-6cc4-4da9-a93a-093352b35902", "logId": "608558de-dd6a-46df-b065-cb7fd0657e7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "608558de-dd6a-46df-b065-cb7fd0657e7e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496321291500, "endTime": 6496321313500}, "additional": {"logType": "info", "children": [], "durationId": "f5f2b140-a8e1-4cda-8b39-7eec80fd2380"}}, {"head": {"id": "8482b414-c445-4660-a84f-cace1fc6c790", "name": "BUILD SUCCESSFUL in 23 s 550 ms ", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496321391600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "9f96d074-96c7-438f-b710-bd7a47d0dbb6", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6472771918800, "endTime": 6496321920600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 17, "minute": 38}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1b03c52a-1171-4a47-8737-a4589473db1b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c735c3a7-91e5-496c-a02d-d791afbe1c66", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322184600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd6a5a8-8959-4779-a7c9-514510b0be56", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60cf4b4c-9ce0-42a0-9d97-c2c9aa094f25", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322361200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d810c97-3842-4cb2-84ad-b51777aef6c5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322425200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0841d33d-ad33-49d4-9c3d-2f2c3991ef94", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322489700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740b3307-a033-445e-8022-a54fe1a61439", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7854369-318e-4509-b3ec-5e3ed60de5c0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322614400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ea9ee6-1e27-4375-a95d-d59acd20153e", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322681200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c47d74c-efca-4a9c-8a9a-00148891bd16", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496322758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f04ce96-5fd6-4d21-8141-1c21a1daa2ca", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496327229100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21f1d48f-d36c-45f7-81fa-08e5b88120f7", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496328405300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69894573-8c0c-4f81-bc72-c77e23bc2125", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496328784100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24033986-2180-4701-b45e-3c666810c4d2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496329119400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b5e166-3e78-47d3-be5a-474cc0d4cea0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496330318400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "529b344c-6d0f-4ff1-af23-ee43c9ab5b75", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496342884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7640efa3-265e-4034-afab-0974ce94c5b5", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496343418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0d93b5-8298-40f0-a062-12e6c7863b52", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496343955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd19ce1-5699-46a5-8434-aea24a30bbf4", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496344634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e246a8-b4f7-44dc-b658-22d70062dfd8", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 12680, "tid": "Main Thread", "startTime": 6496345568700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}