if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WalletPage_Params {
    balance?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class WalletPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__balance = new ObservedPropertySimplePU(1200.00, this, "balance");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WalletPage_Params) {
        if (params.balance !== undefined) {
            this.balance = params.balance;
        }
    }
    updateStateVars(params: WalletPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__balance.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__balance.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __balance: ObservedPropertySimplePU<number>;
    get balance() {
        return this.__balance.get();
    }
    set balance(newValue: number) {
        this.__balance.set(newValue);
    }
    aboutToAppear() {
        console.log('WalletPage 页面加载');
    }
    // 快速操作点击处理
    private handleQuickAction(action: string) {
        promptAction.showToast({
            message: `点击了${action}`,
            duration: 2000
        });
        // 根据不同操作跳转到对应页面
        switch (action) {
            case '转账':
                router.pushUrl({
                    url: 'pages/TransferFormPage'
                });
                break;
            case '充值':
                router.pushUrl({
                    url: 'pages/RechargePage'
                });
                break;
            case '提现':
                router.pushUrl({
                    url: 'pages/WithdrawPage'
                });
                break;
            case '交易记录':
                router.pushUrl({
                    url: 'pages/TransactionListPage'
                });
                break;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(46:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F8F9FA');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(48:7)", "entry");
            // 顶部导航栏
            Column.width('90%');
            // 顶部导航栏
            Column.margin({ top: 20 });
            // 顶部导航栏
            Column.borderRadius(16);
            // 顶部导航栏
            Column.backgroundColor('#ff3785f5');
            // 顶部导航栏
            Column.shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(49:9)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包管理');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(50:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部导航栏
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 余额卡片
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(66:7)", "entry");
            // 余额卡片
            Column.width('90%');
            // 余额卡片
            Column.padding(24);
            // 余额卡片
            Column.margin({ top: 20, bottom: 30 });
            // 余额卡片
            Column.borderRadius(16);
            // 余额卡片
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(67:9)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包余额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(68:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.width('100%');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥ ${this.balance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(79:9)", "entry");
            Text.fontSize(32);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('可用余额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(85:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('rgba(255, 255, 255, 0.8)');
        }, Text);
        Text.pop();
        // 余额卡片
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快速操作标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(99:7)", "entry");
            // 快速操作标题
            Row.width('90%');
            // 快速操作标题
            Row.justifyContent(FlexAlign.Start);
            // 快速操作标题
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(100:9)", "entry");
            Text.fontSize(16);
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快速操作');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(104:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        // 快速操作标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快速操作列表
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(114:7)", "entry");
            // 快速操作列表
            Column.width('90%');
            // 快速操作列表
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(116:9)", "entry");
            // 转账
            Row.width('100%');
            // 转账
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 转账
            Row.backgroundColor('#FFFFFF');
            // 转账
            Row.borderRadius(12);
            // 转账
            Row.margin({ bottom: 12 });
            // 转账
            Row.onClick(() => {
                this.handleQuickAction('转账');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(117:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💸');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(118:13)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(122:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(123:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('向其他账户转账');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(129:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 2 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(139:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 转账
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(153:9)", "entry");
            // 充值
            Row.width('100%');
            // 充值
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 充值
            Row.backgroundColor('#FFFFFF');
            // 充值
            Row.borderRadius(12);
            // 充值
            Row.margin({ bottom: 12 });
            // 充值
            Row.onClick(() => {
                this.handleQuickAction('充值');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(154:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(155:13)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(159:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(160:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('从银行卡充值到钱包');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(166:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 2 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(176:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 充值
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(190:9)", "entry");
            // 提现
            Row.width('100%');
            // 提现
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 提现
            Row.backgroundColor('#FFFFFF');
            // 提现
            Row.borderRadius(12);
            // 提现
            Row.margin({ bottom: 12 });
            // 提现
            Row.onClick(() => {
                this.handleQuickAction('提现');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(191:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(192:13)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(196:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(197:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('从钱包提现到银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(203:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 2 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(213:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 提现
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易记录
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(227:9)", "entry");
            // 交易记录
            Row.width('100%');
            // 交易记录
            Row.padding({ top: 16, bottom: 16, left: 16, right: 16 });
            // 交易记录
            Row.backgroundColor('#FFFFFF');
            // 交易记录
            Row.borderRadius(12);
            // 交易记录
            Row.margin({ bottom: 12 });
            // 交易记录
            Row.onClick(() => {
                this.handleQuickAction('交易记录');
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(228:11)", "entry");
            Row.layoutWeight(1);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(229:13)", "entry");
            Text.fontSize(24);
            Text.margin({ right: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(233:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(234:15)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看交易明细');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(240:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 2 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(250:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        // 交易记录
        Row.pop();
        // 快速操作列表
        Column.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        Column.pop();
    }
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(276:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({
                width: { top: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(278:7)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.alignItems(HorizontalAlign.Center);
            // 银行卡
            Column.padding({ top: 8, bottom: 8 });
            // 银行卡
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/MyBankCardPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(279:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(281:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(296:7)", "entry");
            // 交易
            Column.layoutWeight(1);
            // 交易
            Column.alignItems(HorizontalAlign.Center);
            // 交易
            Column.padding({ top: 8, bottom: 8 });
            // 交易
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/TransactionListPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(297:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(299:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包 (当前页面)
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(314:7)", "entry");
            // 钱包 (当前页面)
            Column.layoutWeight(1);
            // 钱包 (当前页面)
            Column.alignItems(HorizontalAlign.Center);
            // 钱包 (当前页面)
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(315:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(317:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#6366F1');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 钱包 (当前页面)
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付中心
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(327:7)", "entry");
            // 支付中心
            Column.layoutWeight(1);
            // 支付中心
            Column.alignItems(HorizontalAlign.Center);
            // 支付中心
            Column.padding({ top: 8, bottom: 8 });
            // 支付中心
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/PaymentCenterPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(328:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(330:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 支付中心
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(345:7)", "entry");
            // 我的
            Column.layoutWeight(1);
            // 我的
            Column.alignItems(HorizontalAlign.Center);
            // 我的
            Column.padding({ top: 8, bottom: 8 });
            // 我的
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(346:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(348:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 我的
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WalletPage";
    }
}
registerNamedRoute(() => new WalletPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/WalletPage", pageFullPath: "entry/src/main/ets/pages/WalletPage", integratedHsp: "false", moduleType: "followWithHap" });
