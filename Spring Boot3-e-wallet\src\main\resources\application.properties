# 数据源配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=

# MyBatis-Plus配置
mybatis-plus.type-aliases-package=com.icss.spring.entity
mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# 服务器配置
server.port=8080

# 日志配置
logging.level.com.icss.spring.mapper=debug