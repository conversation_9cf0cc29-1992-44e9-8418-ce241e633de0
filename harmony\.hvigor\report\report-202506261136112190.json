{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "3cd295e2-b562-4b59-a563-8acd202be449", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315976979315100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1be1d98c-4a35-400a-ae5c-1d4d35b8f11b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977219522700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e3dc25-d493-4e6d-9e0a-4529b311749f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977221007700, "endTime": 315977221030700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "ff6bbd89-fbae-4840-84a7-8dd3f6391baf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff6bbd89-fbae-4840-84a7-8dd3f6391baf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977221007700, "endTime": 315977221030700}, "additional": {"logType": "info", "children": [], "durationId": "f9e3dc25-d493-4e6d-9e0a-4529b311749f"}}, {"head": {"id": "085e7049-93a3-4c71-9c7a-ac468676c514", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977221144000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41db1ec-4163-47f3-8607-d586c910af55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977222593400, "endTime": 315977222623700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73ec98b1-b6f7-4e66-a000-99622dc8b45e", "logId": "a87bedb3-4e3c-4bcb-a959-ec6fa27dae72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a87bedb3-4e3c-4bcb-a959-ec6fa27dae72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315977222593400, "endTime": 315977222623700}, "additional": {"logType": "info", "children": [], "durationId": "c41db1ec-4163-47f3-8607-d586c910af55"}}, {"head": {"id": "3684e3ce-2c4d-4892-8d6f-49bcd41e3f49", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315978374389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3907e42e-828b-4f81-adb3-0d97dfb0cfa0", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315978374644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270b1963-7731-4f10-954e-f7a848070d98", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137523791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e386868-bcef-49c1-84c2-6deb717cc86e", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137533864300, "endTime": 316137772147200}, "additional": {"children": ["54ea7fd7-6d24-4f71-8b31-687bdb652679", "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "68a7b4dd-bf4a-419e-8876-ac0e2ee541da", "197b57b0-1d3c-438a-8912-6dc21901956f", "4bc1459f-f507-4bda-b490-93001402ae83", "56d84b2d-8952-4f99-a6a7-1aba3de2baac", "c24f801a-840f-457d-9850-d821a81591d9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54ea7fd7-6d24-4f71-8b31-687bdb652679", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137533866200, "endTime": 316137549406200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "f9fb8fac-3629-4568-b566-065b1298e2d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137549427900, "endTime": 316137770788600}, "additional": {"children": ["6af41f60-cd72-404e-a293-845fa7d195a3", "8f3b50e2-ec5c-4e9d-9b14-114d124edf4e", "80e5dabb-a261-4fb8-969f-9f1a7211166c", "b6cc6896-e36f-4869-9959-2afb1782c1ad", "0852a46a-c71d-45a8-b0ec-24bc7967e976", "345e37e9-7f0f-4d14-afaf-878718bdf93c", "87e2b321-7a40-4deb-aa41-0ba286acacc2", "061d35e0-6d57-4346-a75e-3a8674d37376", "44c9966b-9a50-48c1-8c21-cc81a9cde7d0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "7cd019eb-dab3-4382-98d3-a174856ca91f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68a7b4dd-bf4a-419e-8876-ac0e2ee541da", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137770814800, "endTime": 316137772104000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "dc96f20b-0eb9-4c4b-ab72-05166bdcf381"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "197b57b0-1d3c-438a-8912-6dc21901956f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137772109300, "endTime": 316137772141600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "96a77e6b-cf75-44b6-9abd-437b7bd8e267"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bc1459f-f507-4bda-b490-93001402ae83", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137537999200, "endTime": 316137538036700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "6a8c94be-89cc-4d67-8fba-58b45a46235e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a8c94be-89cc-4d67-8fba-58b45a46235e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137537999200, "endTime": 316137538036700}, "additional": {"logType": "info", "children": [], "durationId": "4bc1459f-f507-4bda-b490-93001402ae83", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "56d84b2d-8952-4f99-a6a7-1aba3de2baac", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137544311200, "endTime": 316137544332400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "f2fd795a-8d7e-41b0-917d-f313e171d3ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2fd795a-8d7e-41b0-917d-f313e171d3ca", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137544311200, "endTime": 316137544332400}, "additional": {"logType": "info", "children": [], "durationId": "56d84b2d-8952-4f99-a6a7-1aba3de2baac", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "89becf3e-9d09-4665-920f-587e7113cf0e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137544389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c82a4f-760a-4381-b582-7bbd735ddb2f", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137549274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fb8fac-3629-4568-b566-065b1298e2d7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137533866200, "endTime": 316137549406200}, "additional": {"logType": "info", "children": [], "durationId": "54ea7fd7-6d24-4f71-8b31-687bdb652679", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "6af41f60-cd72-404e-a293-845fa7d195a3", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137557922000, "endTime": 316137557946000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "95095f5a-c0fd-4b8e-b8ae-94bea9b092df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f3b50e2-ec5c-4e9d-9b14-114d124edf4e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137557974500, "endTime": 316137566297000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "5be67af3-e7f8-4085-9c43-2369012d1db0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80e5dabb-a261-4fb8-969f-9f1a7211166c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137566317400, "endTime": 316137687659200}, "additional": {"children": ["21d92252-a340-4ade-8685-ffbd2d5e14f0", "ac595d91-4a40-4d6c-af74-bd17eba2ac1c", "c55795a9-4eeb-4314-b1c1-39fcaaa58812"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "2a63a043-0182-4773-9cf7-306ecca24157"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6cc6896-e36f-4869-9959-2afb1782c1ad", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137687681800, "endTime": 316137718116200}, "additional": {"children": ["29d6bcdb-ea16-4aef-9afa-ac42fc0b494a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "c11bb451-99a3-497e-ab31-2788a63dea6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0852a46a-c71d-45a8-b0ec-24bc7967e976", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137718294900, "endTime": 316137744275900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "9d72f512-e347-4d79-b445-bd01d6de1bfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "345e37e9-7f0f-4d14-afaf-878718bdf93c", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137745489800, "endTime": 316137754519000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "f8457962-5ef3-4c68-9e63-fbde212e847c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e2b321-7a40-4deb-aa41-0ba286acacc2", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137754546200, "endTime": 316137770608800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "ce7f56b1-3a9e-4a71-9ff8-55b771387e1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "061d35e0-6d57-4346-a75e-3a8674d37376", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137770631700, "endTime": 316137770775900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "e1210334-6e27-47e2-9eee-0289c8fbfccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95095f5a-c0fd-4b8e-b8ae-94bea9b092df", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137557922000, "endTime": 316137557946000}, "additional": {"logType": "info", "children": [], "durationId": "6af41f60-cd72-404e-a293-845fa7d195a3", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "5be67af3-e7f8-4085-9c43-2369012d1db0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137557974500, "endTime": 316137566297000}, "additional": {"logType": "info", "children": [], "durationId": "8f3b50e2-ec5c-4e9d-9b14-114d124edf4e", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "21d92252-a340-4ade-8685-ffbd2d5e14f0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137567493400, "endTime": 316137567523500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80e5dabb-a261-4fb8-969f-9f1a7211166c", "logId": "4c6e7823-47e7-4542-a254-93b6625a4483"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c6e7823-47e7-4542-a254-93b6625a4483", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137567493400, "endTime": 316137567523500}, "additional": {"logType": "info", "children": [], "durationId": "21d92252-a340-4ade-8685-ffbd2d5e14f0", "parent": "2a63a043-0182-4773-9cf7-306ecca24157"}}, {"head": {"id": "ac595d91-4a40-4d6c-af74-bd17eba2ac1c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137572519300, "endTime": 316137686439600}, "additional": {"children": ["88a1370b-90f0-4c51-8f48-ec3b32e4f2f3", "42e3cb66-dcb2-4b40-aa1d-775231456963"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80e5dabb-a261-4fb8-969f-9f1a7211166c", "logId": "6499fa83-c22c-438c-95b6-7605f7644acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88a1370b-90f0-4c51-8f48-ec3b32e4f2f3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137572522400, "endTime": 316137579953300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac595d91-4a40-4d6c-af74-bd17eba2ac1c", "logId": "d41ab709-600e-4d52-8f20-65c1f6546705"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42e3cb66-dcb2-4b40-aa1d-775231456963", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137579991500, "endTime": 316137686416400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac595d91-4a40-4d6c-af74-bd17eba2ac1c", "logId": "d00a22a7-995b-4681-b8d3-9565d0f8c2c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "948085c0-17b9-467d-83e4-16d12255ae67", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137572534500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653c20c3-d8ab-4201-988e-afdff4cc3825", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137579649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d41ab709-600e-4d52-8f20-65c1f6546705", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137572522400, "endTime": 316137579953300}, "additional": {"logType": "info", "children": [], "durationId": "88a1370b-90f0-4c51-8f48-ec3b32e4f2f3", "parent": "6499fa83-c22c-438c-95b6-7605f7644acf"}}, {"head": {"id": "141eddb6-f151-4e59-a441-f4fc722c1077", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137580019500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cca8dc7-e1d8-4b14-8d9f-1e408f06cee2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137592157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "662b9926-9893-4ef6-bfdd-5b7a3d941e1b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137593209800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb8dc0c-6c40-4d00-b84b-ff82a0b3bff6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137593531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b027d26d-5efe-494e-afc8-51d3b813a859", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137593930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ed2760-cb75-40a6-b68f-b08b158953ee", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137597678700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c98a1e-9189-436a-acfd-e1a6a82d4029", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137606618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a87a749b-a903-4be7-9cf4-ad62d9e122d8", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137622302900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2680d3-50c8-4c1c-9827-79310605c1d2", "name": "Sdk init in 48 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137656756000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc45cd7-1d0b-440c-889d-88c5cf6ca7e1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137656938500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "markType": "other"}}, {"head": {"id": "d16c5484-b1b8-4bae-ba92-b61657a7a248", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137656953900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "markType": "other"}}, {"head": {"id": "c0c306e4-33ca-4507-b84c-a39a2ce86e9d", "name": "Project task initialization takes 27 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137685980900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7b63bb-e31b-42c3-93fc-567598f76777", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137686161300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a04ceb-0721-4e3e-8be2-878ec78dec03", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137686262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab96cd5-cacc-4bee-99ee-0b79a2994779", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137686341400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d00a22a7-995b-4681-b8d3-9565d0f8c2c6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137579991500, "endTime": 316137686416400}, "additional": {"logType": "info", "children": [], "durationId": "42e3cb66-dcb2-4b40-aa1d-775231456963", "parent": "6499fa83-c22c-438c-95b6-7605f7644acf"}}, {"head": {"id": "6499fa83-c22c-438c-95b6-7605f7644acf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137572519300, "endTime": 316137686439600}, "additional": {"logType": "info", "children": ["d41ab709-600e-4d52-8f20-65c1f6546705", "d00a22a7-995b-4681-b8d3-9565d0f8c2c6"], "durationId": "ac595d91-4a40-4d6c-af74-bd17eba2ac1c", "parent": "2a63a043-0182-4773-9cf7-306ecca24157"}}, {"head": {"id": "c55795a9-4eeb-4314-b1c1-39fcaaa58812", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137687617700, "endTime": 316137687636400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80e5dabb-a261-4fb8-969f-9f1a7211166c", "logId": "03c4d389-c28a-4057-8b50-ba2691b9c961"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03c4d389-c28a-4057-8b50-ba2691b9c961", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137687617700, "endTime": 316137687636400}, "additional": {"logType": "info", "children": [], "durationId": "c55795a9-4eeb-4314-b1c1-39fcaaa58812", "parent": "2a63a043-0182-4773-9cf7-306ecca24157"}}, {"head": {"id": "2a63a043-0182-4773-9cf7-306ecca24157", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137566317400, "endTime": 316137687659200}, "additional": {"logType": "info", "children": ["4c6e7823-47e7-4542-a254-93b6625a4483", "6499fa83-c22c-438c-95b6-7605f7644acf", "03c4d389-c28a-4057-8b50-ba2691b9c961"], "durationId": "80e5dabb-a261-4fb8-969f-9f1a7211166c", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "29d6bcdb-ea16-4aef-9afa-ac42fc0b494a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137688685800, "endTime": 316137718100600}, "additional": {"children": ["0506ebb0-18ac-485f-ab3a-752917fd1454", "88f5db35-5005-486c-adda-411e17608d5e", "406182ea-9cdd-443b-85e8-7ed9ab4217ec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6cc6896-e36f-4869-9959-2afb1782c1ad", "logId": "450b3cda-a756-4d21-8dbd-5238c08ba393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0506ebb0-18ac-485f-ab3a-752917fd1454", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137693700600, "endTime": 316137693717000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29d6bcdb-ea16-4aef-9afa-ac42fc0b494a", "logId": "af7ac455-8b38-4a5e-be04-4ce62f4f6358"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af7ac455-8b38-4a5e-be04-4ce62f4f6358", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137693700600, "endTime": 316137693717000}, "additional": {"logType": "info", "children": [], "durationId": "0506ebb0-18ac-485f-ab3a-752917fd1454", "parent": "450b3cda-a756-4d21-8dbd-5238c08ba393"}}, {"head": {"id": "88f5db35-5005-486c-adda-411e17608d5e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137696694400, "endTime": 316137716273400}, "additional": {"children": ["9c83848c-398f-4169-af72-173a1cd6efe6", "61bd2044-058d-4a3f-a1bd-0eebe839d939"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29d6bcdb-ea16-4aef-9afa-ac42fc0b494a", "logId": "921df58c-eb14-436c-97e8-7d657360c5b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c83848c-398f-4169-af72-173a1cd6efe6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137696696700, "endTime": 316137700555900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88f5db35-5005-486c-adda-411e17608d5e", "logId": "ecd9c0c0-8b76-4d04-81e8-e70846f358b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61bd2044-058d-4a3f-a1bd-0eebe839d939", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137700582100, "endTime": 316137716260200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88f5db35-5005-486c-adda-411e17608d5e", "logId": "bb1e800e-8de4-439a-be4d-37ed82ff105d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5a37608-a79c-4187-a1a3-306c0eda3bc2", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137696705500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51a62032-5f9c-41df-bf5c-572a1a059eb2", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137700362400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd9c0c0-8b76-4d04-81e8-e70846f358b5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137696696700, "endTime": 316137700555900}, "additional": {"logType": "info", "children": [], "durationId": "9c83848c-398f-4169-af72-173a1cd6efe6", "parent": "921df58c-eb14-436c-97e8-7d657360c5b7"}}, {"head": {"id": "cf17ebfc-7309-4779-afd7-7f4863b810fa", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137700602500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a18f79a-255c-45bd-839d-78827cf5ce41", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137710406000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a50999-b16e-4047-8266-ef1c2f58e4ce", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137710573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cde7fc8a-2356-4871-9ae5-20c3522f9628", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137710868000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023bb3a2-3742-4761-8cff-05935ae33ee5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137711281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3768ae3f-e760-480c-8783-2fb6669dd7c0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137711391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4867375d-9106-40c6-8bc7-df6601c71bcd", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137711492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b230be73-3ae7-4172-959b-5243b0869397", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137711642300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0bc5470-32a3-46ee-9219-6bf5bd4fa42e", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137715831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec49b167-e7f7-47e6-a47d-19017d8e0b9f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137716032000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0615bd7-d22f-4c0a-934f-b79640a0dcef", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137716125000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b9bd70-7a5c-4fb5-9789-6dbb1b1cb31a", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137716193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1e800e-8de4-439a-be4d-37ed82ff105d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137700582100, "endTime": 316137716260200}, "additional": {"logType": "info", "children": [], "durationId": "61bd2044-058d-4a3f-a1bd-0eebe839d939", "parent": "921df58c-eb14-436c-97e8-7d657360c5b7"}}, {"head": {"id": "921df58c-eb14-436c-97e8-7d657360c5b7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137696694400, "endTime": 316137716273400}, "additional": {"logType": "info", "children": ["ecd9c0c0-8b76-4d04-81e8-e70846f358b5", "bb1e800e-8de4-439a-be4d-37ed82ff105d"], "durationId": "88f5db35-5005-486c-adda-411e17608d5e", "parent": "450b3cda-a756-4d21-8dbd-5238c08ba393"}}, {"head": {"id": "406182ea-9cdd-443b-85e8-7ed9ab4217ec", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137718067500, "endTime": 316137718083000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29d6bcdb-ea16-4aef-9afa-ac42fc0b494a", "logId": "8e55b1f2-0e0b-4a33-9f90-76dda938119d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e55b1f2-0e0b-4a33-9f90-76dda938119d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137718067500, "endTime": 316137718083000}, "additional": {"logType": "info", "children": [], "durationId": "406182ea-9cdd-443b-85e8-7ed9ab4217ec", "parent": "450b3cda-a756-4d21-8dbd-5238c08ba393"}}, {"head": {"id": "450b3cda-a756-4d21-8dbd-5238c08ba393", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137688685800, "endTime": 316137718100600}, "additional": {"logType": "info", "children": ["af7ac455-8b38-4a5e-be04-4ce62f4f6358", "921df58c-eb14-436c-97e8-7d657360c5b7", "8e55b1f2-0e0b-4a33-9f90-76dda938119d"], "durationId": "29d6bcdb-ea16-4aef-9afa-ac42fc0b494a", "parent": "c11bb451-99a3-497e-ab31-2788a63dea6b"}}, {"head": {"id": "c11bb451-99a3-497e-ab31-2788a63dea6b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137687681800, "endTime": 316137718116200}, "additional": {"logType": "info", "children": ["450b3cda-a756-4d21-8dbd-5238c08ba393"], "durationId": "b6cc6896-e36f-4869-9959-2afb1782c1ad", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "c534437c-0042-431b-972b-38e59f24af3b", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137743487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0c28f47-808d-4757-a2a9-82aaf60fc4a2", "name": "hvigorfile, resolve hvigorfile dependencies in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137744123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d72f512-e347-4d79-b445-bd01d6de1bfe", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137718294900, "endTime": 316137744275900}, "additional": {"logType": "info", "children": [], "durationId": "0852a46a-c71d-45a8-b0ec-24bc7967e976", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "44c9966b-9a50-48c1-8c21-cc81a9cde7d0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137745251200, "endTime": 316137745469700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "logId": "d786ee81-b4b9-4e72-ab86-396ac8d64d46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5005322-0cf1-40c6-bed9-3292935036fb", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137745284900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d786ee81-b4b9-4e72-ab86-396ac8d64d46", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137745251200, "endTime": 316137745469700}, "additional": {"logType": "info", "children": [], "durationId": "44c9966b-9a50-48c1-8c21-cc81a9cde7d0", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "3b6c487e-cddb-4c8a-b815-ae2e7b5a4732", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137747336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4067b98a-e6bd-43f7-895c-435e6b3ae2dc", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137753558600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8457962-5ef3-4c68-9e63-fbde212e847c", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137745489800, "endTime": 316137754519000}, "additional": {"logType": "info", "children": [], "durationId": "345e37e9-7f0f-4d14-afaf-878718bdf93c", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "df8bcd10-0d68-4ba6-a054-8762f7db9997", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137754569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eceed49-db69-4f06-ade9-a358dd6a90c4", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137761134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3358245-bedb-49c2-8ced-969ea3c35022", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137761269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f93eb5-7755-439a-befb-a62677e36ee1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137761502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1929fb91-f265-421d-a929-693db2ee005c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137765515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd201ca-ef52-48fc-a3bc-e20fb8e10a98", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137765680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7f56b1-3a9e-4a71-9ff8-55b771387e1a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137754546200, "endTime": 316137770608800}, "additional": {"logType": "info", "children": [], "durationId": "87e2b321-7a40-4deb-aa41-0ba286acacc2", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "8cb80c24-bb21-4bd5-9c97-ec83c5a9db58", "name": "Configuration phase cost:213 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137770657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1210334-6e27-47e2-9eee-0289c8fbfccf", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137770631700, "endTime": 316137770775900}, "additional": {"logType": "info", "children": [], "durationId": "061d35e0-6d57-4346-a75e-3a8674d37376", "parent": "7cd019eb-dab3-4382-98d3-a174856ca91f"}}, {"head": {"id": "7cd019eb-dab3-4382-98d3-a174856ca91f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137549427900, "endTime": 316137770788600}, "additional": {"logType": "info", "children": ["95095f5a-c0fd-4b8e-b8ae-94bea9b092df", "5be67af3-e7f8-4085-9c43-2369012d1db0", "2a63a043-0182-4773-9cf7-306ecca24157", "c11bb451-99a3-497e-ab31-2788a63dea6b", "9d72f512-e347-4d79-b445-bd01d6de1bfe", "f8457962-5ef3-4c68-9e63-fbde212e847c", "ce7f56b1-3a9e-4a71-9ff8-55b771387e1a", "e1210334-6e27-47e2-9eee-0289c8fbfccf", "d786ee81-b4b9-4e72-ab86-396ac8d64d46"], "durationId": "d959ee01-856f-4d69-8086-9cf8ffe13d8f", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "c24f801a-840f-457d-9850-d821a81591d9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137772077500, "endTime": 316137772091600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e386868-bcef-49c1-84c2-6deb717cc86e", "logId": "bcfa8dff-e6a3-43ac-97a5-f0468e2e75bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bcfa8dff-e6a3-43ac-97a5-f0468e2e75bc", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137772077500, "endTime": 316137772091600}, "additional": {"logType": "info", "children": [], "durationId": "c24f801a-840f-457d-9850-d821a81591d9", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "dc96f20b-0eb9-4c4b-ab72-05166bdcf381", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137770814800, "endTime": 316137772104000}, "additional": {"logType": "info", "children": [], "durationId": "68a7b4dd-bf4a-419e-8876-ac0e2ee541da", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "96a77e6b-cf75-44b6-9abd-437b7bd8e267", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137772109300, "endTime": 316137772141600}, "additional": {"logType": "info", "children": [], "durationId": "197b57b0-1d3c-438a-8912-6dc21901956f", "parent": "82bc7d8f-f1b8-4115-88ba-0e5f66446337"}}, {"head": {"id": "82bc7d8f-f1b8-4115-88ba-0e5f66446337", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137533864300, "endTime": 316137772147200}, "additional": {"logType": "info", "children": ["f9fb8fac-3629-4568-b566-065b1298e2d7", "7cd019eb-dab3-4382-98d3-a174856ca91f", "dc96f20b-0eb9-4c4b-ab72-05166bdcf381", "96a77e6b-cf75-44b6-9abd-437b7bd8e267", "6a8c94be-89cc-4d67-8fba-58b45a46235e", "f2fd795a-8d7e-41b0-917d-f313e171d3ca", "bcfa8dff-e6a3-43ac-97a5-f0468e2e75bc"], "durationId": "0e386868-bcef-49c1-84c2-6deb717cc86e"}}, {"head": {"id": "85d3e91a-35bb-4efa-89d5-c66a6124627a", "name": "Configuration task cost before running: 244 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137772302600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989c4c57-f506-49c0-bdba-27c779de24d0", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137778834300, "endTime": 316137788635900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ae0278ed-7ffa-4906-9d3d-23b96f936267", "logId": "3039c40f-cb0f-47b9-b537-67c9a87f9d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae0278ed-7ffa-4906-9d3d-23b96f936267", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137774234400}, "additional": {"logType": "detail", "children": [], "durationId": "989c4c57-f506-49c0-bdba-27c779de24d0"}}, {"head": {"id": "1e4ed86b-3648-4bf1-9d7c-1bd458346546", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137774839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58dddc6a-7617-4f6a-9ce4-92999064ead8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137774952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8db4b08-4944-4e1a-ad66-4dd393982b8c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137778849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030548b1-28a9-4394-acc9-cdf8939120b7", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137788400500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84de1f13-2f22-4b0e-9127-8aba2ca7ef48", "name": "entry : default@PreBuild cost memory -1.5127639770507812", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137788554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3039c40f-cb0f-47b9-b537-67c9a87f9d72", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137778834300, "endTime": 316137788635900}, "additional": {"logType": "info", "children": [], "durationId": "989c4c57-f506-49c0-bdba-27c779de24d0"}}, {"head": {"id": "f38da5a8-aac3-42b6-958d-95d143b0c695", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137794702600, "endTime": 316137797235700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "40a3b347-2563-4f27-bc46-4588ea3c1583", "logId": "163b6696-f8cc-4145-b625-ca2cd7bc7fce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40a3b347-2563-4f27-bc46-4588ea3c1583", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137793100700}, "additional": {"logType": "detail", "children": [], "durationId": "f38da5a8-aac3-42b6-958d-95d143b0c695"}}, {"head": {"id": "e23c1d89-5376-485c-a227-1db3d2023016", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137793694400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e534713-2ab9-4b51-adce-0e5200945d3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137793833000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70060607-9d47-4814-ba33-9516cb9ad664", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137794713400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5632142a-0f74-4b9e-9dfe-6b06639b99d8", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137797019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5914ac1d-3420-484e-92ce-e43ca6d60b8a", "name": "entry : default@MergeProfile cost memory 0.1110992431640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137797155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "163b6696-f8cc-4145-b625-ca2cd7bc7fce", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137794702600, "endTime": 316137797235700}, "additional": {"logType": "info", "children": [], "durationId": "f38da5a8-aac3-42b6-958d-95d143b0c695"}}, {"head": {"id": "284e3b7d-f43e-43f4-9773-4697f3484b52", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137801714700, "endTime": 316137804540100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ffef7238-d664-4223-8efb-7894ad48a560", "logId": "ba0616c3-8eba-4f1e-ad08-4d0da3914e47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffef7238-d664-4223-8efb-7894ad48a560", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137799222100}, "additional": {"logType": "detail", "children": [], "durationId": "284e3b7d-f43e-43f4-9773-4697f3484b52"}}, {"head": {"id": "9069cbd9-5ecb-40ee-871b-ec195f3d027c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137800317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f0073c-6cf7-43aa-ae68-3a97dddb3f51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137800532600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdfcd6f8-270a-49ce-a465-4d6035fee35d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137801730500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafe593b-9ae8-4abf-961f-9a2a757ed7f4", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137802859200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b59d7c-96c4-4a85-bba5-01333ed2702c", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137804331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355c71b1-b9ef-45c1-9733-2cc9aff07606", "name": "entry : default@CreateBuildProfile cost memory 0.097259521484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137804455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0616c3-8eba-4f1e-ad08-4d0da3914e47", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137801714700, "endTime": 316137804540100}, "additional": {"logType": "info", "children": [], "durationId": "284e3b7d-f43e-43f4-9773-4697f3484b52"}}, {"head": {"id": "9059c18b-3b3b-4154-9f40-d7c6a16e2e6e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808012200, "endTime": 316137808526400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ac195c1c-6f3b-47d0-b0d5-ef30f061901e", "logId": "3108200e-2544-4015-85af-fa688774d730"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac195c1c-6f3b-47d0-b0d5-ef30f061901e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137806376500}, "additional": {"logType": "detail", "children": [], "durationId": "9059c18b-3b3b-4154-9f40-d7c6a16e2e6e"}}, {"head": {"id": "08ed20fa-b2ae-4c35-bf3c-27f9ee4c74eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137807006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5a2394-230d-4bee-830b-18bf5f2c01c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137807127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff74427-d4b8-4f1e-af80-a458d26f9f21", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808023500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b40ef9ee-600f-4b53-bbb4-c2de60b1399d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8205a8-e5f6-471b-9675-c621bf9ac593", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808255300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a4c324-5ba9-41dd-974e-7bbeae346090", "name": "entry : default@PreCheckSyscap cost memory 0.03742218017578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c6a79d-148b-44da-8697-2dc4e8338631", "name": "runTaskFromQueue task cost before running: 280 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3108200e-2544-4015-85af-fa688774d730", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137808012200, "endTime": 316137808526400, "totalTime": 412900}, "additional": {"logType": "info", "children": [], "durationId": "9059c18b-3b3b-4154-9f40-d7c6a16e2e6e"}}, {"head": {"id": "80488c22-5a88-44d9-9a91-805f7ec10eef", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137822807800, "endTime": 316137824718300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0d53e02e-fa1b-44c3-a43c-6c631fc0758d", "logId": "f8d64215-a71d-4058-bdba-8f6ffeb8dca6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d53e02e-fa1b-44c3-a43c-6c631fc0758d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137810705200}, "additional": {"logType": "detail", "children": [], "durationId": "80488c22-5a88-44d9-9a91-805f7ec10eef"}}, {"head": {"id": "b76408d3-8e14-4d78-825a-cf581aaea729", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137811382700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6545efc-3296-4835-bf90-316a67f8cc1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137811520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94e36c46-ab78-4c7a-9d56-69ef4f12c319", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137822831000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e78b2b1d-0e5a-4d2e-9d93-7e95a974da25", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137823145100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4c8737-1e94-4f06-931a-0ad64bd95554", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137824368900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7279f0c4-75b8-4d39-b6c1-4e9167ee2f49", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06694793701171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137824567400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d64215-a71d-4058-bdba-8f6ffeb8dca6", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137822807800, "endTime": 316137824718300}, "additional": {"logType": "info", "children": [], "durationId": "80488c22-5a88-44d9-9a91-805f7ec10eef"}}, {"head": {"id": "2e25c4db-e46a-4f9e-8aef-43cc84cf3362", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137831406400, "endTime": 316137833064200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "93ff3a1f-b5ff-402d-bfcb-896d105e9d01", "logId": "c1dedd98-785f-4e5f-99e9-c1463a80ea4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93ff3a1f-b5ff-402d-bfcb-896d105e9d01", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137827872700}, "additional": {"logType": "detail", "children": [], "durationId": "2e25c4db-e46a-4f9e-8aef-43cc84cf3362"}}, {"head": {"id": "0d341fc3-b5f9-499f-bfff-58616bdeb374", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137829272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541508fb-fd90-4955-9003-3fa19f2fcacc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137829446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0467c5a5-64b2-4e2f-a3c6-473cbef5a42e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137831422700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05908266-002e-4498-bbc2-91f5e710eee7", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137832775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b818a208-a35d-4ded-a40a-41bb88aec129", "name": "entry : default@ProcessProfile cost memory 0.05695343017578125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137832955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1dedd98-785f-4e5f-99e9-c1463a80ea4e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137831406400, "endTime": 316137833064200}, "additional": {"logType": "info", "children": [], "durationId": "2e25c4db-e46a-4f9e-8aef-43cc84cf3362"}}, {"head": {"id": "80f5b83c-b7d5-4a99-ac66-25bf637cfd08", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137839143600, "endTime": 316137846620100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eaa379c1-5f69-4fd0-9c83-0ea339426e2e", "logId": "432e9f38-ee79-4d82-bdc9-fda7a26fc19b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaa379c1-5f69-4fd0-9c83-0ea339426e2e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137835368300}, "additional": {"logType": "detail", "children": [], "durationId": "80f5b83c-b7d5-4a99-ac66-25bf637cfd08"}}, {"head": {"id": "251d15fe-b489-4117-a24b-fdf483864f22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137836073100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54650896-c243-4aa8-b416-10bd1248857e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137836219300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1431e9-6cd1-4709-abe4-4fada89f0f19", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137839179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6d172d1-2ef4-4485-93b2-c5d53166692f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137846336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82193613-9b9f-45d2-961f-18b6533e1daa", "name": "entry : default@ProcessRouterMap cost memory 0.18972015380859375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137846495600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432e9f38-ee79-4d82-bdc9-fda7a26fc19b", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137839143600, "endTime": 316137846620100}, "additional": {"logType": "info", "children": [], "durationId": "80f5b83c-b7d5-4a99-ac66-25bf637cfd08"}}, {"head": {"id": "4d2952ce-292c-41fb-98c9-6383e573b332", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137856824200, "endTime": 316137859931300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "db7d048a-41d0-441c-99ed-f32c684fbea6", "logId": "d9c60e3d-faab-40a8-bbcc-14df94f9886c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db7d048a-41d0-441c-99ed-f32c684fbea6", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137850891800}, "additional": {"logType": "detail", "children": [], "durationId": "4d2952ce-292c-41fb-98c9-6383e573b332"}}, {"head": {"id": "9950e904-e826-46c3-bd44-a4a1cbef5b22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137851914200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4883cb3a-4887-403d-9075-a667ca52c934", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137852092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b8065e-18a1-4bd7-8d8d-88636ec3e4d0", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137853750200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5c6a5f-089b-4da3-a253-d4b686ae9b39", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137858023400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f806ab5-d7ce-440d-9459-32a55294c746", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137858181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922012ae-9117-48ca-a265-6f31a56e3ab5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137858245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f38fac-1cae-47e8-8a49-4a842c0ef8eb", "name": "entry : default@PreviewProcessResource cost memory 0.06856536865234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137858334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b40ccbb-11fa-415b-b6d8-6c6bd6503980", "name": "runTaskFromQueue task cost before running: 331 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137859802100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9c60e3d-faab-40a8-bbcc-14df94f9886c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137856824200, "endTime": 316137859931300, "totalTime": 1595700}, "additional": {"logType": "info", "children": [], "durationId": "4d2952ce-292c-41fb-98c9-6383e573b332"}}, {"head": {"id": "89c7c961-693a-4c50-bace-7f92c2ff71da", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137868627300, "endTime": 316137895037200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e74ca4ee-d1bc-4fb7-9f67-9f54eff5e653", "logId": "ba20525c-f1c2-4a6e-8a9a-707ecba27d9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e74ca4ee-d1bc-4fb7-9f67-9f54eff5e653", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137863856700}, "additional": {"logType": "detail", "children": [], "durationId": "89c7c961-693a-4c50-bace-7f92c2ff71da"}}, {"head": {"id": "48646a76-c54a-4a69-a34e-5e5aa8573c49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137864478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e2423b7-f2c0-4435-96ba-95e33a691121", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137864593800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae5ca0c-c09a-4692-a6e4-ae914b0e7c70", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137868647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52000aa-c20c-49f3-a064-1dd33eb4fbdb", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137894772300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045f52d9-7acd-4501-9854-3bae2019356e", "name": "entry : default@GenerateLoaderJson cost memory -0.9531021118164062", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137894946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba20525c-f1c2-4a6e-8a9a-707ecba27d9b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137868627300, "endTime": 316137895037200}, "additional": {"logType": "info", "children": [], "durationId": "89c7c961-693a-4c50-bace-7f92c2ff71da"}}, {"head": {"id": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137908153300, "endTime": 316138160009600}, "additional": {"children": ["1281c458-f966-4441-8711-2f7c49ddb1bd", "b0cda5b9-67d3-43f0-b258-232bc914f68f", "e2fc3fbe-9ad0-4985-97ee-52f86cdb3fa6", "5f6f2dba-95cb-45fa-bd03-fdcc22a0492a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "a36b328b-68c8-4d6b-b5fb-ee8376297976", "logId": "727be7df-1816-472d-98a5-33dd6bcc20f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a36b328b-68c8-4d6b-b5fb-ee8376297976", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137903905000}, "additional": {"logType": "detail", "children": [], "durationId": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8"}}, {"head": {"id": "4bd47487-e12c-4d39-bd30-6d8cd2c97419", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137904488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f00c8a9c-05b5-415f-a1a1-7bd4b6d8e836", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137904590900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242038cd-4657-46cb-a9dd-d921e343e44c", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137905605100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac9ff806-bcc5-42b0-9194-88fda6fd1bc0", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137908182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd9f9c34-bf5a-463c-8015-e2663c5c2e6a", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137933906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5056570-81cb-4065-a360-483ff12ebe48", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137934085100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1281c458-f966-4441-8711-2f7c49ddb1bd", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137935738800, "endTime": 316137953720000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8", "logId": "4be19bbb-540b-494c-960a-193349fe4d1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4be19bbb-540b-494c-960a-193349fe4d1c", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137935738800, "endTime": 316137953720000}, "additional": {"logType": "info", "children": [], "durationId": "1281c458-f966-4441-8711-2f7c49ddb1bd", "parent": "727be7df-1816-472d-98a5-33dd6bcc20f0"}}, {"head": {"id": "dcf0594b-5620-4d2b-bd95-9d2966d18e31", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137954046400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0cda5b9-67d3-43f0-b258-232bc914f68f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137955226400, "endTime": 316137990465800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8", "logId": "aa525597-3e45-4375-899e-87db48fff36c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12e018ba-42a9-4231-a5c6-ab7e76140157", "name": "current process  memoryUsage: {\n  rss: 104898560,\n  heapTotal: 118317056,\n  heapUsed: 110350584,\n  external: 3108429,\n  arrayBuffers: 102294\n} os memoryUsage :6.297901153564453", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137956380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af9d2125-8d42-42c3-8365-b733b075b5e8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137987746300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa525597-3e45-4375-899e-87db48fff36c", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137955226400, "endTime": 316137990465800}, "additional": {"logType": "info", "children": [], "durationId": "b0cda5b9-67d3-43f0-b258-232bc914f68f", "parent": "727be7df-1816-472d-98a5-33dd6bcc20f0"}}, {"head": {"id": "f890373d-d139-4965-a6ab-a1b40bdb7440", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137990641000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fc3fbe-9ad0-4985-97ee-52f86cdb3fa6", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137992100100, "endTime": 316138061098000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8", "logId": "3595c78c-b1db-49df-aef1-663283160fc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d460a013-1d4d-40a4-ad69-1211dafea745", "name": "current process  memoryUsage: {\n  rss: 104914944,\n  heapTotal: 118317056,\n  heapUsed: 110602576,\n  external: 3108555,\n  arrayBuffers: 102435\n} os memoryUsage :6.314262390136719", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137993365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48089c26-8322-4ea6-b788-2a0ceb810edc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138058005000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3595c78c-b1db-49df-aef1-663283160fc5", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137992100100, "endTime": 316138061098000}, "additional": {"logType": "info", "children": [], "durationId": "e2fc3fbe-9ad0-4985-97ee-52f86cdb3fa6", "parent": "727be7df-1816-472d-98a5-33dd6bcc20f0"}}, {"head": {"id": "c88d0367-33b5-439b-bdd9-4a35682bc94e", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138061857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f6f2dba-95cb-45fa-bd03-fdcc22a0492a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138064183300, "endTime": 316138158655100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8", "logId": "481c7196-a1e6-4dc0-99b8-09ee70e47aaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83aefdb8-8ebc-4ce0-a8a7-ac64e6f58097", "name": "current process  memoryUsage: {\n  rss: 104964096,\n  heapTotal: 118317056,\n  heapUsed: 110960496,\n  external: 3108681,\n  arrayBuffers: 103375\n} os memoryUsage :6.287006378173828", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138066157900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ec1fde-16af-4821-ae92-5f4bf71b2065", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138154990500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481c7196-a1e6-4dc0-99b8-09ee70e47aaa", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138064183300, "endTime": 316138158655100}, "additional": {"logType": "info", "children": [], "durationId": "5f6f2dba-95cb-45fa-bd03-fdcc22a0492a", "parent": "727be7df-1816-472d-98a5-33dd6bcc20f0"}}, {"head": {"id": "236cacf0-b7e7-4e0d-932b-6c166fc30cf3", "name": "entry : default@PreviewCompileResource cost memory 0.21660614013671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138159692300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996c0291-47e8-4faf-9076-7d5b3930b44e", "name": "runTaskFromQueue task cost before running: 631 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138159919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727be7df-1816-472d-98a5-33dd6bcc20f0", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137908153300, "endTime": 316138160009600, "totalTime": 251707300}, "additional": {"logType": "info", "children": ["4be19bbb-540b-494c-960a-193349fe4d1c", "aa525597-3e45-4375-899e-87db48fff36c", "3595c78c-b1db-49df-aef1-663283160fc5", "481c7196-a1e6-4dc0-99b8-09ee70e47aaa"], "durationId": "3356fa7f-6ac6-42e7-91c0-cd602b2241c8"}}, {"head": {"id": "872fd830-811f-4dbc-b2fc-94e16a12e5ae", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163774000, "endTime": 316138164182200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3331dd22-6b5c-45f1-8a85-641afab46a77", "logId": "5d58336f-726f-4895-812d-fd5e10cab1f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3331dd22-6b5c-45f1-8a85-641afab46a77", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138162951500}, "additional": {"logType": "detail", "children": [], "durationId": "872fd830-811f-4dbc-b2fc-94e16a12e5ae"}}, {"head": {"id": "96c96172-55c7-4b47-94fe-f8460d871502", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163557700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ece0cc-556b-4bec-9f47-f64c4085edd9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163670400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "683bcf0c-a0c7-4d86-843e-ae5872ff2b49", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163782400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9c2ef31-4074-42f3-964d-f6e028e749c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163878800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb92ef00-cdc4-43e0-a802-bdadf3cf16ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163952400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3937e7e0-275f-4463-994a-1c483f394acf", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138164028200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d792de-cfb9-4552-a12d-e0f85c1a9a8c", "name": "runTaskFromQueue task cost before running: 635 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138164109300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d58336f-726f-4895-812d-fd5e10cab1f9", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138163774000, "endTime": 316138164182200, "totalTime": 314800}, "additional": {"logType": "info", "children": [], "durationId": "872fd830-811f-4dbc-b2fc-94e16a12e5ae"}}, {"head": {"id": "b609642a-223b-40ae-8f30-b15fdf7757b6", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138167553900, "endTime": 316138174573000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "942755ff-2b01-4beb-92c8-55d38a72ee9b", "logId": "d618e6a6-afa3-450b-b390-9d3c85d3412f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "942755ff-2b01-4beb-92c8-55d38a72ee9b", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138166262200}, "additional": {"logType": "detail", "children": [], "durationId": "b609642a-223b-40ae-8f30-b15fdf7757b6"}}, {"head": {"id": "109e94c9-6eb2-413f-b2b0-d2638367d802", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138166790200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758f46ff-5379-4380-9460-c0f14632ca9d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138166889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727d6d30-6c71-46d3-8730-3aab2cbd823e", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138167563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60183de8-dbf4-4405-91b0-5bbffe4a3fc8", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138169003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc5c469-dc62-414e-9b6e-ebe7f5b2940b", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138169142500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2505dffb-c0b5-4f0a-b54b-df5c35e28161", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138169258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f2a1f5-8960-4415-86ba-030ea52dfccb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138169324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa9991e-10ee-40ac-a1eb-22ddf2b7df7a", "name": "entry : default@CopyPreviewProfile cost memory 0.2083740234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138174324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e88db2-73a3-41f4-8404-f4af3f85591b", "name": "runTaskFromQueue task cost before running: 646 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138174487200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d618e6a6-afa3-450b-b390-9d3c85d3412f", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138167553900, "endTime": 316138174573000, "totalTime": 6904500}, "additional": {"logType": "info", "children": [], "durationId": "b609642a-223b-40ae-8f30-b15fdf7757b6"}}, {"head": {"id": "905e3173-2a36-453b-b83c-148b13f601f5", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138178186300, "endTime": 316138179705600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e113b53e-ac36-4a97-80e1-723dfcb9f2e8", "logId": "0d4e6d12-0133-45b5-8b59-fc5862b81303"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e113b53e-ac36-4a97-80e1-723dfcb9f2e8", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138176748200}, "additional": {"logType": "detail", "children": [], "durationId": "905e3173-2a36-453b-b83c-148b13f601f5"}}, {"head": {"id": "c60e2cba-098e-4e72-b2ff-a491792c4cbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138177311400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2585b5-29cd-496d-99d9-53b8122abfcf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138177411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ec3cab-2bb2-4ad8-9b62-775c59a73937", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138178196900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d266d43d-fa6d-4e2c-853d-b8f564c41595", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138178313300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a72fbfe-04a6-4302-b484-c3490a827325", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138179096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0613c339-f4bb-4916-9714-6d6f81f749e3", "name": "entry : default@ReplacePreviewerPage cost memory -1.70318603515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138179458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b32bf0-2adb-4fab-a84a-6f014d5dc5b9", "name": "runTaskFromQueue task cost before running: 651 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138179595500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d4e6d12-0133-45b5-8b59-fc5862b81303", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138178186300, "endTime": 316138179705600, "totalTime": 1376300}, "additional": {"logType": "info", "children": [], "durationId": "905e3173-2a36-453b-b83c-148b13f601f5"}}, {"head": {"id": "1da3a87c-90f5-456a-bb04-475f6b923ab9", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182094100, "endTime": 316138182455300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fe16d91f-8d54-458e-abdc-f7de3ddf86aa", "logId": "542455eb-6ab6-4585-92fc-d976d4b76b5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe16d91f-8d54-458e-abdc-f7de3ddf86aa", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182036500}, "additional": {"logType": "detail", "children": [], "durationId": "1da3a87c-90f5-456a-bb04-475f6b923ab9"}}, {"head": {"id": "0f296269-fe3c-451e-9416-ea2b1e6cf7cb", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182103000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "327dfafa-2f1a-4dee-9609-fc81e307138e", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182302100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "014cbef4-090c-4164-b6ed-8fd8bd900252", "name": "runTaskFromQueue task cost before running: 654 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182396600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542455eb-6ab6-4585-92fc-d976d4b76b5e", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138182094100, "endTime": 316138182455300, "totalTime": 280400}, "additional": {"logType": "info", "children": [], "durationId": "1da3a87c-90f5-456a-bb04-475f6b923ab9"}}, {"head": {"id": "3c009200-8182-43ad-b555-2e1463e7a6fe", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138186456900, "endTime": 316138190817500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "83933f14-550c-46a4-9111-1de883f71a1a", "logId": "efdf52ea-2214-42c0-9085-d251e3a06296"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83933f14-550c-46a4-9111-1de883f71a1a", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138184535600}, "additional": {"logType": "detail", "children": [], "durationId": "3c009200-8182-43ad-b555-2e1463e7a6fe"}}, {"head": {"id": "f2280514-5e7e-421b-87a5-d0af890fb992", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138185207900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a52362-6b67-4fd4-9e0e-1687e3e65915", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138185331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be078cf0-af33-42ea-b9f2-b9455a79b012", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138186469800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482e785f-31dd-4f6a-9065-21fbebcb3687", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138188976900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a740083-425a-464a-91f1-0b97885bc363", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138189187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51d2175-f764-49c2-92a2-1ed565b51f31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138189332000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21828b5-ab75-4ad7-aac5-be13a6425d95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138189439200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59606639-ca7d-4e50-a0b9-b49dccbbd9a0", "name": "entry : default@PreviewUpdateAssets cost memory 0.13399505615234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138190505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1c7e694-bf7f-4cff-962f-fbda498475d9", "name": "runTaskFromQueue task cost before running: 662 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138190697700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efdf52ea-2214-42c0-9085-d251e3a06296", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138186456900, "endTime": 316138190817500, "totalTime": 4201600}, "additional": {"logType": "info", "children": [], "durationId": "3c009200-8182-43ad-b555-2e1463e7a6fe"}}, {"head": {"id": "01ba6a33-88de-40f5-b0a5-e5f79d3510ca", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138205057600}, "additional": {"children": ["2b1e3dfe-16e5-4b00-8778-5362599f7484"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bd4637f9-e6c1-4862-801c-a6c189b93f0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd4637f9-e6c1-4862-801c-a6c189b93f0b", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138194672700}, "additional": {"logType": "detail", "children": [], "durationId": "01ba6a33-88de-40f5-b0a5-e5f79d3510ca"}}, {"head": {"id": "318550dd-3b00-498e-8639-089799602409", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138195506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4862f955-8d56-4913-b0f4-17b4fdb73d40", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138195667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80911c06-a74d-4de5-a8ef-bf1da77c9668", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138205087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1e3dfe-16e5-4b00-8778-5362599f7484", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker9", "startTime": 316138242230800}, "additional": {"children": [], "state": "running", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "01ba6a33-88de-40f5-b0a5-e5f79d3510ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf28228e-e5d6-4565-b2b6-2dbdf3625685", "name": "entry : default@PreviewArkTS cost memory -0.48946380615234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316138245840800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36de2624-f763-4901-8233-e78ddfd75cf0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700186000, "endTime": 316139700229600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83634bda-f0f3-4943-827d-345d591a7a15", "logId": "151dfecf-e52f-47d5-91e3-8a6995179016"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "151dfecf-e52f-47d5-91e3-8a6995179016", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700186000, "endTime": 316139700229600}, "additional": {"logType": "info", "children": [], "durationId": "36de2624-f763-4901-8233-e78ddfd75cf0"}}, {"head": {"id": "dc2cdaf7-fb2c-4a36-b7e8-b47e0618db53", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316137529178300, "endTime": 316139700408700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 36}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "60b44269-2bac-4cf7-a0e8-571749865f2b", "name": "BUILD FAILED in 2 s 172 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700456800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "491147b6-6981-485b-8ebf-d7bc89acc57f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700749700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b268ee4-0300-471e-8e4f-233faebde6f2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eefa398-9292-4ca4-ab50-3f4890dadb15", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139700995000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e80439-ed6c-434a-b1ad-d7cdc0da4c8d", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139701103700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96fc0c38-15ec-447f-8a73-cc35ac7d6b72", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139701214900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14943ccd-0631-4533-89b4-8e2d4e76dd74", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139701315400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7195fe76-42ba-4bac-af43-eb5f17debc50", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139701414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c3b777-d3d2-45d7-b0ae-044bab6e99b0", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139703118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d027450-f58c-4dbe-b386-a533c9af93fb", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139731196900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd01579-1463-4aa2-a17b-3baecda326ca", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139731776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542e3cc4-c408-49fc-9349-7b2c09f56796", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139765223300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ac7124-73e0-47fd-b730-d09d32152a49", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:67 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139768013500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e4bceb3-c5ab-42c2-bc93-84d711e4779c", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139769077200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a9fa3e-c934-4883-b27d-25f246813ae2", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139772253800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fb7d37-5b4a-4b58-8f98-4ee10f209a1f", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139774692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c578bf-5d12-47d5-bbd8-92331237d35a", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139776334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d478dfec-d450-42bd-b61f-2f1ee12eb0fb", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139780068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77737820-dc55-4510-be5d-85af9d51e373", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139781658700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e117a5d-07d7-4e7a-887a-4750de9264e0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139790331100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "932fd424-7dca-44dc-badf-f1baa1e0525b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139793339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aad4a78-8bc0-4618-981b-f34378453e15", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139797834300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aabc7c7-de57-4dbc-8035-d3d62a33ebc3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139799492000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1df6d06-9c5a-4e45-9be5-94591e740a8f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139801260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08edeaa7-9335-474d-be73-238b23444811", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139828224400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b611300-deb9-40b9-b159-3721dd65bcf1", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139829997700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00265465-bd1d-4f52-acc1-61a0d2aac594", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139830830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4bfdf7-8319-45af-ae51-c9ffbfe0ec1e", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139831641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a35d49-2684-4763-9197-0cfb84af0b14", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:51 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 316139832334100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}