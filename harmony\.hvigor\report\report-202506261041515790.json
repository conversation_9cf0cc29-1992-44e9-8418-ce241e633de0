{"version": "2.0", "ppid": 10700, "events": [{"head": {"id": "7c8f931d-a887-40e7-abe8-1cb5c9419346", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401273169000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb46775-4da9-4178-9652-70e52fa77fb0", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401281601200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5fa3bac-be39-44bb-960b-1f64aed08fef", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401281811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8407848-1285-45b4-992d-e1d921b45fcd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877884953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877892057200, "endTime": 312878198313700}, "additional": {"children": ["c42f1d9f-d1a0-4066-816b-099f3a9d6e76", "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "1849fe3e-910b-4c5c-b070-bad65bc1e6af", "7123f9c6-11dc-4cc9-b696-197da4dc169f", "f7a96213-d551-4d54-88da-fb201bc1a727", "00799dff-edfa-4fa6-b709-11e00e252b03", "0341d371-9729-4789-af0b-692f9a2dd92f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c42f1d9f-d1a0-4066-816b-099f3a9d6e76", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877892058400, "endTime": 312877907328300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "06b70ad6-67fa-4d8c-88ae-81b638274f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877907353500, "endTime": 312878196969000}, "additional": {"children": ["14a9947d-2b82-4787-96e2-05d110541817", "8155558b-3594-4ab7-949c-70d0dcf3a880", "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "673c3170-bab1-4764-9eb9-48eab973afa5", "e8a80f0c-9ea7-4322-b2e9-d03c5ed7c74a", "f6559eda-c899-48c9-bb73-787ddd3e3fdf", "0381564c-58e2-4c93-82f1-33418600d6cf", "f40f7da1-ca98-4023-9b3c-37ba3e7d1097", "7135bbb2-78f7-4713-9991-90dfe0b3e1a8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "ae503573-e933-4712-ade2-f1814974cb66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1849fe3e-910b-4c5c-b070-bad65bc1e6af", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878196992700, "endTime": 312878198288200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "62672263-2085-4b9a-8ea9-9a9e02b7cf88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7123f9c6-11dc-4cc9-b696-197da4dc169f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878198296700, "endTime": 312878198306300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "f5b5b911-0894-46f1-b7a6-c3db4db6ad7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7a96213-d551-4d54-88da-fb201bc1a727", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877896387000, "endTime": 312877896416000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "0a2d38ef-b8a9-4bec-a29d-09007c48818f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a2d38ef-b8a9-4bec-a29d-09007c48818f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877896387000, "endTime": 312877896416000}, "additional": {"logType": "info", "children": [], "durationId": "f7a96213-d551-4d54-88da-fb201bc1a727", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "00799dff-edfa-4fa6-b709-11e00e252b03", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877901969500, "endTime": 312877901988100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "e44fbb51-1f30-401c-a80d-901d82a48ab0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e44fbb51-1f30-401c-a80d-901d82a48ab0", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877901969500, "endTime": 312877901988100}, "additional": {"logType": "info", "children": [], "durationId": "00799dff-edfa-4fa6-b709-11e00e252b03", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "ec20c45f-ffba-4202-b09e-d7f9c3a45366", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877902043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7db6521-a465-4a61-a98f-88355480b7d7", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877907203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06b70ad6-67fa-4d8c-88ae-81b638274f66", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877892058400, "endTime": 312877907328300}, "additional": {"logType": "info", "children": [], "durationId": "c42f1d9f-d1a0-4066-816b-099f3a9d6e76", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "14a9947d-2b82-4787-96e2-05d110541817", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877914791600, "endTime": 312877914802600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "d2a5fe1f-c9c0-4956-b6c3-36457<PERSON>c47d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8155558b-3594-4ab7-949c-70d0dcf3a880", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877914819800, "endTime": 312877927555900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "da8f7027-4124-42a4-94d4-4a21f3d947c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877927575600, "endTime": 312878110299500}, "additional": {"children": ["0ea7f6c8-5dad-4ae3-9e0f-ef3a5bc87bba", "f88646e8-8233-4451-9c8b-6d9b0f114c36", "aa905601-16db-4914-881f-2eba63907a85"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "a2a36408-16ea-42db-b2b6-e5fd33679309"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "673c3170-bab1-4764-9eb9-48eab973afa5", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878110319000, "endTime": 312878145513200}, "additional": {"children": ["af575434-3eb8-4615-9930-7ae1ad114e14"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "0376cc40-576a-40b4-b123-1e5eeabf6f34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8a80f0c-9ea7-4322-b2e9-d03c5ed7c74a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878145524100, "endTime": 312878171905200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "ffbb5b53-07f6-4255-8be3-fcac363f90bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6559eda-c899-48c9-bb73-787ddd3e3fdf", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878173303900, "endTime": 312878183459500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "23522614-93ca-4623-82cb-2089c40b4dfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0381564c-58e2-4c93-82f1-33418600d6cf", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878183495600, "endTime": 312878196763700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "07d40cdf-5b1d-4917-b72b-279bc85245e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f40f7da1-ca98-4023-9b3c-37ba3e7d1097", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878196814200, "endTime": 312878196956900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "bde3afef-82a7-4a42-9aea-d2b0c8344615"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2a5fe1f-c9c0-4956-b6c3-36457<PERSON>c47d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877914791600, "endTime": 312877914802600}, "additional": {"logType": "info", "children": [], "durationId": "14a9947d-2b82-4787-96e2-05d110541817", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "da8f7027-4124-42a4-94d4-4a21f3d947c5", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877914819800, "endTime": 312877927555900}, "additional": {"logType": "info", "children": [], "durationId": "8155558b-3594-4ab7-949c-70d0dcf3a880", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "0ea7f6c8-5dad-4ae3-9e0f-ef3a5bc87bba", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877928381400, "endTime": 312877928403500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "logId": "c65822d1-d0d3-4a4e-8f32-7eec93ab6594"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c65822d1-d0d3-4a4e-8f32-7eec93ab6594", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877928381400, "endTime": 312877928403500}, "additional": {"logType": "info", "children": [], "durationId": "0ea7f6c8-5dad-4ae3-9e0f-ef3a5bc87bba", "parent": "a2a36408-16ea-42db-b2b6-e5fd33679309"}}, {"head": {"id": "f88646e8-8233-4451-9c8b-6d9b0f114c36", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877931281200, "endTime": 312878109341800}, "additional": {"children": ["f0f41344-a94e-421b-ad57-421e9126381c", "4bab5b26-caa4-4cad-828f-0640e6c7508e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "logId": "0ac7896a-2e36-49ba-90af-643a96d0de6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0f41344-a94e-421b-ad57-421e9126381c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877931282400, "endTime": 312877951751900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f88646e8-8233-4451-9c8b-6d9b0f114c36", "logId": "eefdb6a3-0c44-4e20-89e9-fdd9549740bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bab5b26-caa4-4cad-828f-0640e6c7508e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877951767700, "endTime": 312878109328200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f88646e8-8233-4451-9c8b-6d9b0f114c36", "logId": "0317abbb-2d83-4ede-ac00-dfdbde4a84db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1909553d-46d5-4dd6-a91e-433dabb4659a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877931288500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3292d6c4-439c-4ec8-a1ff-d33409783c6f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877951612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefdb6a3-0c44-4e20-89e9-fdd9549740bb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877931282400, "endTime": 312877951751900}, "additional": {"logType": "info", "children": [], "durationId": "f0f41344-a94e-421b-ad57-421e9126381c", "parent": "0ac7896a-2e36-49ba-90af-643a96d0de6e"}}, {"head": {"id": "d9f01b18-239d-43b0-8940-ec26abe2918b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877951780800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2142df65-5819-45ef-b2a4-ee5ab26bb2dd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877959291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5caeadf6-1442-406b-b1be-321786cb7d41", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877959420800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb0e482-a1dc-4748-bd3a-7399914ae3cb", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877959575100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b071ba-8e2d-4e8c-b292-cd6a5e4b5018", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877959690100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c706b4d-fed6-4050-b7d9-97a3f2cf69e1", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877961581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212ab947-1f7c-408a-a783-15445d536014", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877980246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983a9f56-3a7d-475b-bb2e-58748b0b147e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877995748400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "562638d9-68cb-408a-982d-9e0219322022", "name": "Sdk init in 86 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878066825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a938a79c-899f-45eb-8e4a-cc01a23ed606", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878067003300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 41}, "markType": "other"}}, {"head": {"id": "856a7acc-1b04-4671-b5ff-46537a43aef6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878067021000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 41}, "markType": "other"}}, {"head": {"id": "b56f5e79-b356-4d93-8c00-c47d28b51f93", "name": "Project task initialization takes 41 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878109045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0a3079-8e2e-440d-9dd0-e49a45f3d467", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878109168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d77209f-ef69-4975-bea0-e7071a5e6063", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878109230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bdb138-e117-48df-b180-168199fd05e1", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878109281900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0317abbb-2d83-4ede-ac00-dfdbde4a84db", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877951767700, "endTime": 312878109328200}, "additional": {"logType": "info", "children": [], "durationId": "4bab5b26-caa4-4cad-828f-0640e6c7508e", "parent": "0ac7896a-2e36-49ba-90af-643a96d0de6e"}}, {"head": {"id": "0ac7896a-2e36-49ba-90af-643a96d0de6e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877931281200, "endTime": 312878109341800}, "additional": {"logType": "info", "children": ["eefdb6a3-0c44-4e20-89e9-fdd9549740bb", "0317abbb-2d83-4ede-ac00-dfdbde4a84db"], "durationId": "f88646e8-8233-4451-9c8b-6d9b0f114c36", "parent": "a2a36408-16ea-42db-b2b6-e5fd33679309"}}, {"head": {"id": "aa905601-16db-4914-881f-2eba63907a85", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878110250800, "endTime": 312878110278500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "logId": "e5e9dde1-225b-4980-88f5-4ba5de8088f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5e9dde1-225b-4980-88f5-4ba5de8088f2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878110250800, "endTime": 312878110278500}, "additional": {"logType": "info", "children": [], "durationId": "aa905601-16db-4914-881f-2eba63907a85", "parent": "a2a36408-16ea-42db-b2b6-e5fd33679309"}}, {"head": {"id": "a2a36408-16ea-42db-b2b6-e5fd33679309", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877927575600, "endTime": 312878110299500}, "additional": {"logType": "info", "children": ["c65822d1-d0d3-4a4e-8f32-7eec93ab6594", "0ac7896a-2e36-49ba-90af-643a96d0de6e", "e5e9dde1-225b-4980-88f5-4ba5de8088f2"], "durationId": "b83ef88e-a5ff-4905-9d11-96ea806d5e4d", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "af575434-3eb8-4615-9930-7ae1ad114e14", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878111426000, "endTime": 312878145496500}, "additional": {"children": ["49eed0dc-e6ac-4c89-ab50-43cedbee77c9", "10cd24b7-7c03-4ebf-a136-3490666e0645", "2883e4af-16d8-44dc-919a-1207a053cf00"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "673c3170-bab1-4764-9eb9-48eab973afa5", "logId": "9d0a62e8-89aa-41e4-8580-8f35dee1412f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49eed0dc-e6ac-4c89-ab50-43cedbee77c9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878115715000, "endTime": 312878115743300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af575434-3eb8-4615-9930-7ae1ad114e14", "logId": "9784d185-5174-48ec-a4bf-f22f2659f6fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9784d185-5174-48ec-a4bf-f22f2659f6fb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878115715000, "endTime": 312878115743300}, "additional": {"logType": "info", "children": [], "durationId": "49eed0dc-e6ac-4c89-ab50-43cedbee77c9", "parent": "9d0a62e8-89aa-41e4-8580-8f35dee1412f"}}, {"head": {"id": "10cd24b7-7c03-4ebf-a136-3490666e0645", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878117977300, "endTime": 312878143542600}, "additional": {"children": ["cb65560a-19ce-471b-99c8-a31e08c2d1ce", "eb005f5c-667e-4f01-9d9f-11cdeaf808ae"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af575434-3eb8-4615-9930-7ae1ad114e14", "logId": "0fbf220b-ab90-4298-ad47-cd4d6d775a9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb65560a-19ce-471b-99c8-a31e08c2d1ce", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878117978500, "endTime": 312878121759600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10cd24b7-7c03-4ebf-a136-3490666e0645", "logId": "34efc475-2cc6-4bdd-8ce5-6d15865bcc55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb005f5c-667e-4f01-9d9f-11cdeaf808ae", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878121776400, "endTime": 312878143525000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10cd24b7-7c03-4ebf-a136-3490666e0645", "logId": "43545c19-e81c-455b-ae2c-5214bc3cbdc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1fcb788-51aa-4f67-8155-af45dac72412", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878117984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee73551-fabd-4efb-b947-6ed0659c8f68", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878121620600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34efc475-2cc6-4bdd-8ce5-6d15865bcc55", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878117978500, "endTime": 312878121759600}, "additional": {"logType": "info", "children": [], "durationId": "cb65560a-19ce-471b-99c8-a31e08c2d1ce", "parent": "0fbf220b-ab90-4298-ad47-cd4d6d775a9d"}}, {"head": {"id": "b48a6316-a33d-4d3a-add2-e0e0706f2ac6", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878121790000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb8606f1-2cd1-47ae-803b-beceeda28bd8", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878129947200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3457ba-4dfb-4bbc-83aa-d5a132cf9653", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23aa369e-e9e8-48f6-afdc-2cb8139d8b42", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130421300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93e3123-d80a-48f1-8624-06a6468842a7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a72fa74-dfd7-4ecc-a9a2-f237fdce00e8", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130691500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa18098-82f4-4977-affb-28174d0e8a20", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130746300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9ad4dd-a35b-4d93-b059-9e3a6f70364e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878130806600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf7e130-1a5c-4c30-8331-ab50ff35719d", "name": "Module entry task initialization takes 10 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878142898700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0abf3318-684d-45a1-be79-3eac19d696ae", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878143298500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8aecf0b-0676-49c0-80a7-57583148ec6c", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878143405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce12726f-ff5e-48f6-b7db-11e577e7d1e7", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878143469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43545c19-e81c-455b-ae2c-5214bc3cbdc1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878121776400, "endTime": 312878143525000}, "additional": {"logType": "info", "children": [], "durationId": "eb005f5c-667e-4f01-9d9f-11cdeaf808ae", "parent": "0fbf220b-ab90-4298-ad47-cd4d6d775a9d"}}, {"head": {"id": "0fbf220b-ab90-4298-ad47-cd4d6d775a9d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878117977300, "endTime": 312878143542600}, "additional": {"logType": "info", "children": ["34efc475-2cc6-4bdd-8ce5-6d15865bcc55", "43545c19-e81c-455b-ae2c-5214bc3cbdc1"], "durationId": "10cd24b7-7c03-4ebf-a136-3490666e0645", "parent": "9d0a62e8-89aa-41e4-8580-8f35dee1412f"}}, {"head": {"id": "2883e4af-16d8-44dc-919a-1207a053cf00", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878145460400, "endTime": 312878145477800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af575434-3eb8-4615-9930-7ae1ad114e14", "logId": "66684e48-6810-4c03-ae0d-1187442f3118"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66684e48-6810-4c03-ae0d-1187442f3118", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878145460400, "endTime": 312878145477800}, "additional": {"logType": "info", "children": [], "durationId": "2883e4af-16d8-44dc-919a-1207a053cf00", "parent": "9d0a62e8-89aa-41e4-8580-8f35dee1412f"}}, {"head": {"id": "9d0a62e8-89aa-41e4-8580-8f35dee1412f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878111426000, "endTime": 312878145496500}, "additional": {"logType": "info", "children": ["9784d185-5174-48ec-a4bf-f22f2659f6fb", "0fbf220b-ab90-4298-ad47-cd4d6d775a9d", "66684e48-6810-4c03-ae0d-1187442f3118"], "durationId": "af575434-3eb8-4615-9930-7ae1ad114e14", "parent": "0376cc40-576a-40b4-b123-1e5eeabf6f34"}}, {"head": {"id": "0376cc40-576a-40b4-b123-1e5eeabf6f34", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878110319000, "endTime": 312878145513200}, "additional": {"logType": "info", "children": ["9d0a62e8-89aa-41e4-8580-8f35dee1412f"], "durationId": "673c3170-bab1-4764-9eb9-48eab973afa5", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "7b681099-f5b3-4496-be79-f640d4485fe1", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878171345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7403c2d-f88c-410a-8020-1547d90ce1ce", "name": "hvigorfile, resolve hvigorfile dependencies in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878171776800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffbb5b53-07f6-4255-8be3-fcac363f90bf", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878145524100, "endTime": 312878171905200}, "additional": {"logType": "info", "children": [], "durationId": "e8a80f0c-9ea7-4322-b2e9-d03c5ed7c74a", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "7135bbb2-78f7-4713-9991-90dfe0b3e1a8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878173075200, "endTime": 312878173285600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "logId": "f9b55051-7292-4c7b-b424-0754c986498d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62a731c0-3070-40a4-a08b-4300219e428a", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878173105600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b55051-7292-4c7b-b424-0754c986498d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878173075200, "endTime": 312878173285600}, "additional": {"logType": "info", "children": [], "durationId": "7135bbb2-78f7-4713-9991-90dfe0b3e1a8", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "cb2610f4-422e-4b25-b27a-51a2ed5db366", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878175396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86850dcf-240e-4bc1-bb29-9f57bee868a1", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878182379500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23522614-93ca-4623-82cb-2089c40b4dfe", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878173303900, "endTime": 312878183459500}, "additional": {"logType": "info", "children": [], "durationId": "f6559eda-c899-48c9-bb73-787ddd3e3fdf", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "3b389bf9-5e5b-4696-bf05-b0e1f64bb914", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878183531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1d8ca7-5fb9-40d2-8702-547ac441bf62", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878189590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd59e09b-789a-4536-9702-caf2fbf169e1", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878189704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bddced8d-a76a-4529-84e9-446a55c3e053", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878189956700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a3e786-5468-4ad9-9a08-cb6d22948435", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878192574600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d05866f-2a01-4d78-9ce3-43f00eca9d4c", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878192680300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d40cdf-5b1d-4917-b72b-279bc85245e2", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878183495600, "endTime": 312878196763700}, "additional": {"logType": "info", "children": [], "durationId": "0381564c-58e2-4c93-82f1-33418600d6cf", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "5b1039c3-2d1a-4efb-abe7-459e730cbb7a", "name": "Configuration phase cost:283 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878196843700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde3afef-82a7-4a42-9aea-d2b0c8344615", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878196814200, "endTime": 312878196956900}, "additional": {"logType": "info", "children": [], "durationId": "f40f7da1-ca98-4023-9b3c-37ba3e7d1097", "parent": "ae503573-e933-4712-ade2-f1814974cb66"}}, {"head": {"id": "ae503573-e933-4712-ade2-f1814974cb66", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877907353500, "endTime": 312878196969000}, "additional": {"logType": "info", "children": ["d2a5fe1f-c9c0-4956-b6c3-36457<PERSON>c47d", "da8f7027-4124-42a4-94d4-4a21f3d947c5", "a2a36408-16ea-42db-b2b6-e5fd33679309", "0376cc40-576a-40b4-b123-1e5eeabf6f34", "ffbb5b53-07f6-4255-8be3-fcac363f90bf", "23522614-93ca-4623-82cb-2089c40b4dfe", "07d40cdf-5b1d-4917-b72b-279bc85245e2", "bde3afef-82a7-4a42-9aea-d2b0c8344615", "f9b55051-7292-4c7b-b424-0754c986498d"], "durationId": "ccc0906c-7ddc-4457-a5c0-f22fce6e481b", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "0341d371-9729-4789-af0b-692f9a2dd92f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878198251900, "endTime": 312878198271400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c", "logId": "56cd580e-ee4e-4275-864d-d329b23c7d0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56cd580e-ee4e-4275-864d-d329b23c7d0f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878198251900, "endTime": 312878198271400}, "additional": {"logType": "info", "children": [], "durationId": "0341d371-9729-4789-af0b-692f9a2dd92f", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "62672263-2085-4b9a-8ea9-9a9e02b7cf88", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878196992700, "endTime": 312878198288200}, "additional": {"logType": "info", "children": [], "durationId": "1849fe3e-910b-4c5c-b070-bad65bc1e6af", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "f5b5b911-0894-46f1-b7a6-c3db4db6ad7c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878198296700, "endTime": 312878198306300}, "additional": {"logType": "info", "children": [], "durationId": "7123f9c6-11dc-4cc9-b696-197da4dc169f", "parent": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39"}}, {"head": {"id": "9f8bfaa8-f28a-45d4-ae1f-f9784e0b7f39", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877892057200, "endTime": 312878198313700}, "additional": {"logType": "info", "children": ["06b70ad6-67fa-4d8c-88ae-81b638274f66", "ae503573-e933-4712-ade2-f1814974cb66", "62672263-2085-4b9a-8ea9-9a9e02b7cf88", "f5b5b911-0894-46f1-b7a6-c3db4db6ad7c", "0a2d38ef-b8a9-4bec-a29d-09007c48818f", "e44fbb51-1f30-401c-a80d-901d82a48ab0", "56cd580e-ee4e-4275-864d-d329b23c7d0f"], "durationId": "c85bdcea-ca1f-4215-b2de-f8e9d946f18c"}}, {"head": {"id": "a8790dda-35aa-40f3-a545-dd5062cf0b3a", "name": "Configuration task cost before running: 310 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878198432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4303282-3df7-4a40-84ad-7c008c410310", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878204618700, "endTime": 312878216170900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e14743b8-25de-496b-a93e-d09eea612027", "logId": "9fb26e9b-865d-4760-b770-5781ce1df65e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e14743b8-25de-496b-a93e-d09eea612027", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878200563700}, "additional": {"logType": "detail", "children": [], "durationId": "c4303282-3df7-4a40-84ad-7c008c410310"}}, {"head": {"id": "9d0137d2-d197-4d1d-8619-e2a657f3f5b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878201131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf55e2d-f67c-445e-bb65-e237c7730848", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878201228000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb68a65-52fb-48ea-912c-346e1498cfbd", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878204635200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdcc3a6-aa6c-497d-be89-bd7b0c0be5b4", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878215890800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df94b897-c13a-44f3-bdb2-963ef6368fa2", "name": "entry : default@PreBuild cost memory -1.515869140625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878216067100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb26e9b-865d-4760-b770-5781ce1df65e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878204618700, "endTime": 312878216170900}, "additional": {"logType": "info", "children": [], "durationId": "c4303282-3df7-4a40-84ad-7c008c410310"}}, {"head": {"id": "ca7eb009-ce71-4919-84e1-d1d67c1c28bc", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878222204700, "endTime": 312878225011500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "029c4ab2-d7fa-4b76-9413-3481f14834fe", "logId": "73611f22-5fde-49d6-8795-6f2f48426122"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "029c4ab2-d7fa-4b76-9413-3481f14834fe", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878220793300}, "additional": {"logType": "detail", "children": [], "durationId": "ca7eb009-ce71-4919-84e1-d1d67c1c28bc"}}, {"head": {"id": "ef983dfc-83d2-4aee-8f29-ea6aef39a6bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878221338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8375c74-e759-421b-b044-cae88fb21397", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878221448400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732f59f9-d687-4797-8018-4adb7f2d8d5a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878222216500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfee4ab4-20f1-42be-8df5-1f4b1bc31f64", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878224753500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2d8f61-0655-46f1-b849-0da0288b3c1b", "name": "entry : default@MergeProfile cost memory 0.11115264892578125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878224927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73611f22-5fde-49d6-8795-6f2f48426122", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878222204700, "endTime": 312878225011500}, "additional": {"logType": "info", "children": [], "durationId": "ca7eb009-ce71-4919-84e1-d1d67c1c28bc"}}, {"head": {"id": "6ba167b2-8790-4fad-8880-aa36f3d0f0aa", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878230111800, "endTime": 312878233417600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bf3037ea-e34a-4ce8-85ce-6df93de62809", "logId": "2c417698-65e2-4ffa-bf99-0a2b82707613"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf3037ea-e34a-4ce8-85ce-6df93de62809", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878228155900}, "additional": {"logType": "detail", "children": [], "durationId": "6ba167b2-8790-4fad-8880-aa36f3d0f0aa"}}, {"head": {"id": "648d2b5c-1ca7-46fc-9ade-4ad75f925290", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878228940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf10a67d-c53c-40da-817d-13463d716c6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878229075800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfabeba8-51cb-4537-81a7-b7c3a01b98ec", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878230124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ce6402-4395-4cde-aea5-00d2f1681ee7", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878231544500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8071cc0a-be1f-4aad-9b29-1538a1e6538d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878233209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f7d394-4d72-4c00-a17b-82a6d3927b1d", "name": "entry : default@CreateBuildProfile cost memory 0.09787750244140625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878233336400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c417698-65e2-4ffa-bf99-0a2b82707613", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878230111800, "endTime": 312878233417600}, "additional": {"logType": "info", "children": [], "durationId": "6ba167b2-8790-4fad-8880-aa36f3d0f0aa"}}, {"head": {"id": "3aebd2ce-5e81-478c-8bd8-72b8e7721800", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238121700, "endTime": 312878239245900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "652b4037-ded8-496a-b180-05bd1bb165d5", "logId": "d1db2619-719b-45ec-9daa-f79ebae9219b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "652b4037-ded8-496a-b180-05bd1bb165d5", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878235595900}, "additional": {"logType": "detail", "children": [], "durationId": "3aebd2ce-5e81-478c-8bd8-72b8e7721800"}}, {"head": {"id": "e05df27d-ff3e-4e83-9899-220cd3e385b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878236572500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7917e782-93a0-4f96-996a-226a1677e85e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878236743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e8af96-ffa2-4f51-b061-fa26fc107c43", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238140600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec5c3a6b-988b-4ac5-909f-36674189b6c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238430000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a8bd8fc-c6da-47a8-9f70-524f55fc3384", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579d2dd5-4f80-40b9-ac9b-0d530b935068", "name": "entry : default@PreCheckSyscap cost memory 0.03748321533203125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238956000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c3c15c-5017-4262-be48-132e1d731620", "name": "runTaskFromQueue task cost before running: 351 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878239123200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1db2619-719b-45ec-9daa-f79ebae9219b", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878238121700, "endTime": 312878239245900, "totalTime": 964700}, "additional": {"logType": "info", "children": [], "durationId": "3aebd2ce-5e81-478c-8bd8-72b8e7721800"}}, {"head": {"id": "bf18c74f-e975-4407-bb64-5998898ea88b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878267183500, "endTime": 312878268505500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5257ea90-a82c-4dee-a094-2feb6a50df49", "logId": "c9c0a002-f0da-4eb1-9250-7e2eb26b2c1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5257ea90-a82c-4dee-a094-2feb6a50df49", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878257586700}, "additional": {"logType": "detail", "children": [], "durationId": "bf18c74f-e975-4407-bb64-5998898ea88b"}}, {"head": {"id": "ae68b15f-c17e-4d52-ac4e-8574255d9db3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878258434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e88fcf-6978-476d-aa51-e7d86ecdf663", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878258590800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f191937c-cc30-4de8-9fce-a136fb9561d7", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878267203600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26681fa3-6e9a-42da-9425-a5137202b6a7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878267438000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7175ebd-a994-4b5e-b03c-31c68d4acf42", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878268200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "272426da-5d43-47a7-bb97-d7b0eb68f0d5", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0663604736328125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878268356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c0a002-f0da-4eb1-9250-7e2eb26b2c1e", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878267183500, "endTime": 312878268505500}, "additional": {"logType": "info", "children": [], "durationId": "bf18c74f-e975-4407-bb64-5998898ea88b"}}, {"head": {"id": "776443c5-f4bd-4f95-b50e-dca93db5d0f8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878274184900, "endTime": 312878275651600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5ba06428-7183-456c-a17c-df6caa3e5604", "logId": "ab8746ce-948b-4a42-9d5c-3caa47570538"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ba06428-7183-456c-a17c-df6caa3e5604", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878270920900}, "additional": {"logType": "detail", "children": [], "durationId": "776443c5-f4bd-4f95-b50e-dca93db5d0f8"}}, {"head": {"id": "24ccf2b8-591e-4d2c-a06c-860fa8237c9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878271857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06c2069-617e-4a70-8a5b-d2548e6c48b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878272847200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5476b35a-63a6-414f-b779-2d9195c2d9c9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878274199900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ead6fbae-922d-444b-8ecb-788cdbc0c33e", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878275440600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4184d2d6-5945-415b-b0b0-cf4056e7d7d7", "name": "entry : default@ProcessProfile cost memory 0.057281494140625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878275579500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8746ce-948b-4a42-9d5c-3caa47570538", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878274184900, "endTime": 312878275651600}, "additional": {"logType": "info", "children": [], "durationId": "776443c5-f4bd-4f95-b50e-dca93db5d0f8"}}, {"head": {"id": "5ecb628e-0462-4191-989f-6939daba91ba", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878281015900, "endTime": 312878288107400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "87a544d5-f243-4474-a726-7081198d1dd9", "logId": "e1533928-e258-417e-9b6d-ca4ca5572368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87a544d5-f243-4474-a726-7081198d1dd9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878278047900}, "additional": {"logType": "detail", "children": [], "durationId": "5ecb628e-0462-4191-989f-6939daba91ba"}}, {"head": {"id": "11e622f3-8d69-4720-9578-33513559d26b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878278702100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b66654-d589-4f68-a459-fb4902e36d51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878278812100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e93893-190b-4117-ba10-5fc77f414dce", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878281032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8642ed7-9cc9-424a-86ce-a4224ad67288", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878287863700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "520843a7-4e7a-4d96-8bab-3c03ff8822d0", "name": "entry : default@ProcessRouterMap cost memory 0.1889801025390625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878288025100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1533928-e258-417e-9b6d-ca4ca5572368", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878281015900, "endTime": 312878288107400}, "additional": {"logType": "info", "children": [], "durationId": "5ecb628e-0462-4191-989f-6939daba91ba"}}, {"head": {"id": "7925e344-27f8-4f2e-95bb-b5235dddf6c5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878296726300, "endTime": 312878299881400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "13724818-603a-4e7b-8d9f-6b5f07253f2c", "logId": "03e62d29-e90c-4106-aec2-d703cc0af3db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13724818-603a-4e7b-8d9f-6b5f07253f2c", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878291621200}, "additional": {"logType": "detail", "children": [], "durationId": "7925e344-27f8-4f2e-95bb-b5235dddf6c5"}}, {"head": {"id": "ca226696-72e2-439f-9493-001649f84ea1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878292240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e1e66f-b6ba-42be-a10b-83b7cd879b3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878292351100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c167bc6-72dd-4f4d-a58e-a9116961f57b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878293869600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fef4b4f-f418-4eea-8962-13b808c01bf3", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878297873700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc7edbb2-3a99-4d9f-a503-3a5d1b0e0a07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878298189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6684a1ad-47b1-43fc-b141-e386c7998939", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878298309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d712547e-1000-4508-b17b-3b850a881cb4", "name": "entry : default@PreviewProcessResource cost memory 0.0683746337890625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878298399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9b1dff-9476-486d-83eb-61f606ee7033", "name": "runTaskFromQueue task cost before running: 411 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878299763000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e62d29-e90c-4106-aec2-d703cc0af3db", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878296726300, "endTime": 312878299881400, "totalTime": 1736500}, "additional": {"logType": "info", "children": [], "durationId": "7925e344-27f8-4f2e-95bb-b5235dddf6c5"}}, {"head": {"id": "c3c3cfee-d0ec-4c85-837a-6a4b22ad4bdb", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878308048300, "endTime": 312878334204000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "79b05c53-1685-4899-8bf1-e783b3946983", "logId": "6826c4f9-423c-40e4-821d-e08b1d1de4c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79b05c53-1685-4899-8bf1-e783b3946983", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878303416500}, "additional": {"logType": "detail", "children": [], "durationId": "c3c3cfee-d0ec-4c85-837a-6a4b22ad4bdb"}}, {"head": {"id": "274f7b70-2c9d-4c1c-890b-671d6bc95b94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878304150800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff6a8047-9f35-4c20-92dc-5f6b7071ff0e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878304267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2275b56-240f-42b5-8530-d355a51dd0f2", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878308069500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3542c4-1a19-49f9-8e8f-3a01ed50f70f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878333936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c4390c5-9345-48b7-bb75-c04d204e0dd6", "name": "entry : default@GenerateLoaderJson cost memory -0.9719467163085938", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878334108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6826c4f9-423c-40e4-821d-e08b1d1de4c8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878308048300, "endTime": 312878334204000}, "additional": {"logType": "info", "children": [], "durationId": "c3c3cfee-d0ec-4c85-837a-6a4b22ad4bdb"}}, {"head": {"id": "ba8a863d-b6e5-492e-9313-9906df378f38", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878348486100, "endTime": 312878621140700}, "additional": {"children": ["571897d4-597b-42c0-bccf-1422d8b41c3d", "f8c294bf-5ee2-43cf-b8ad-c113040899ca", "53d8b316-7555-4367-b358-b4a3aae73709", "eca75844-f1fb-4bac-93a1-f57b5ee15995"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "9a9f8378-f94c-4781-9c13-a313085c2f7b", "logId": "90dd41fb-a97e-444f-961c-0151830eeac8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a9f8378-f94c-4781-9c13-a313085c2f7b", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878344181100}, "additional": {"logType": "detail", "children": [], "durationId": "ba8a863d-b6e5-492e-9313-9906df378f38"}}, {"head": {"id": "e3da86c4-8a1b-4535-8d95-75439b6741a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878344737500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e571fe8e-b124-4ca4-94a1-a65e073b6ac9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878344857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3073207-cb85-4493-9a0c-6b27189cf585", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878345862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8606fedb-cfd2-40af-b311-e1bd575953fc", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878348528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e31a7e85-0ab9-4808-ac29-21a1b1e7d5f8", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878372484100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9117b83-15c4-4fb2-8ace-b4df95e75932", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878372639600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571897d4-597b-42c0-bccf-1422d8b41c3d", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878373742300, "endTime": 312878435170000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba8a863d-b6e5-492e-9313-9906df378f38", "logId": "23783b13-8708-41b7-9e1b-e273b13e578f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23783b13-8708-41b7-9e1b-e273b13e578f", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878373742300, "endTime": 312878435170000}, "additional": {"logType": "info", "children": [], "durationId": "571897d4-597b-42c0-bccf-1422d8b41c3d", "parent": "90dd41fb-a97e-444f-961c-0151830eeac8"}}, {"head": {"id": "340ce16d-4d7d-4346-95b4-aea6b91d4fe4", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878435499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c294bf-5ee2-43cf-b8ad-c113040899ca", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878436766000, "endTime": 312878472003700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba8a863d-b6e5-492e-9313-9906df378f38", "logId": "f4641946-fc3f-49f2-a05c-9ecbc383db2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f986d2c-d06c-4d0f-9e70-2ae2134d33fb", "name": "current process  memoryUsage: {\n  rss: 105734144,\n  heapTotal: 121667584,\n  heapUsed: 103695704,\n  external: 3075092,\n  arrayBuffers: 68957\n} os memoryUsage :6.858623504638672", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878438019600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d285b4c-a196-4b17-ae03-8168209b98d4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878469348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4641946-fc3f-49f2-a05c-9ecbc383db2a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878436766000, "endTime": 312878472003700}, "additional": {"logType": "info", "children": [], "durationId": "f8c294bf-5ee2-43cf-b8ad-c113040899ca", "parent": "90dd41fb-a97e-444f-961c-0151830eeac8"}}, {"head": {"id": "c1e60b1a-b15d-4c88-8db4-67b3d48436d1", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878472133100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d8b316-7555-4367-b358-b4a3aae73709", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878473322800, "endTime": 312878524345500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba8a863d-b6e5-492e-9313-9906df378f38", "logId": "ad6b49ad-8822-4c07-9bae-e45485b45f6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "499dce28-4aa3-4f19-93f7-d1727d8d5de6", "name": "current process  memoryUsage: {\n  rss: 105750528,\n  heapTotal: 121667584,\n  heapUsed: 103947088,\n  external: 3075218,\n  arrayBuffers: 69098\n} os memoryUsage :6.866886138916016", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878474406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9b76428-d2e8-4ced-bded-2f21435be25c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878521721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad6b49ad-8822-4c07-9bae-e45485b45f6c", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878473322800, "endTime": 312878524345500}, "additional": {"logType": "info", "children": [], "durationId": "53d8b316-7555-4367-b358-b4a3aae73709", "parent": "90dd41fb-a97e-444f-961c-0151830eeac8"}}, {"head": {"id": "9fdd0052-c229-4054-bce9-9d6344052ffc", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878524711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca75844-f1fb-4bac-93a1-f57b5ee15995", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878526339500, "endTime": 312878619653800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba8a863d-b6e5-492e-9313-9906df378f38", "logId": "591568fe-8593-4b53-8d3f-c2c4be400628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c39b9cf-4f94-408c-a40c-b9980a8a6be9", "name": "current process  memoryUsage: {\n  rss: 105754624,\n  heapTotal: 121667584,\n  heapUsed: 104226592,\n  external: 3075344,\n  arrayBuffers: 70038\n} os memoryUsage :6.869117736816406", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878528096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f93018-b98d-4910-8f5b-aa5fc79e8664", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878615590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591568fe-8593-4b53-8d3f-c2c4be400628", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878526339500, "endTime": 312878619653800}, "additional": {"logType": "info", "children": [], "durationId": "eca75844-f1fb-4bac-93a1-f57b5ee15995", "parent": "90dd41fb-a97e-444f-961c-0151830eeac8"}}, {"head": {"id": "3e0502e2-4201-434c-abc7-d93f0dc05ad1", "name": "entry : default@PreviewCompileResource cost memory -9.83905029296875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878620766200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87e864c-9f03-46aa-a2c5-770f1728b848", "name": "runTaskFromQueue task cost before running: 733 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878621016300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90dd41fb-a97e-444f-961c-0151830eeac8", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878348486100, "endTime": 312878621140700, "totalTime": 272446200}, "additional": {"logType": "info", "children": ["23783b13-8708-41b7-9e1b-e273b13e578f", "f4641946-fc3f-49f2-a05c-9ecbc383db2a", "ad6b49ad-8822-4c07-9bae-e45485b45f6c", "591568fe-8593-4b53-8d3f-c2c4be400628"], "durationId": "ba8a863d-b6e5-492e-9313-9906df378f38"}}, {"head": {"id": "36241d15-4fb2-4a9a-8c89-7f3d34fe9ff4", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625561900, "endTime": 312878625985700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a5e23791-4d84-4be3-aff6-999376345888", "logId": "fa0ef683-a787-43a8-bea4-f7f11890e92f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5e23791-4d84-4be3-aff6-999376345888", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878624625200}, "additional": {"logType": "detail", "children": [], "durationId": "36241d15-4fb2-4a9a-8c89-7f3d34fe9ff4"}}, {"head": {"id": "1789262a-fb2c-449b-bd98-352b02eddf52", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625273100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99ec7bb-1b10-49e6-a0db-a568e1c38372", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06409cf8-a156-476e-99ea-edbd7e06393a", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a599c4-e1b2-4494-a293-484baa5ef32e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625679000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ced6bbd8-d9b9-4277-b22f-9d3a2f736963", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625738300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6666bfd1-f5d2-4668-9013-8935e34bfca6", "name": "entry : default@PreviewHookCompileResource cost memory 0.03859710693359375", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625820700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "871fc594-5aeb-42d6-a1e3-fb6ce5d34286", "name": "runTaskFromQueue task cost before running: 738 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa0ef683-a787-43a8-bea4-f7f11890e92f", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878625561900, "endTime": 312878625985700, "totalTime": 338900}, "additional": {"logType": "info", "children": [], "durationId": "36241d15-4fb2-4a9a-8c89-7f3d34fe9ff4"}}, {"head": {"id": "d1399b4c-f38b-4457-a07e-a2bc93acaa99", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878630602600, "endTime": 312878638570500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "652da251-9026-4b31-96ef-28e715199003", "logId": "24c3a0ec-364d-451d-bd5f-5c950e051027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "652da251-9026-4b31-96ef-28e715199003", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878628544800}, "additional": {"logType": "detail", "children": [], "durationId": "d1399b4c-f38b-4457-a07e-a2bc93acaa99"}}, {"head": {"id": "f497a6aa-9a4b-4009-a119-452aeb193a8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878629436300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1abc7c12-2da0-4ef2-bbbd-a1adf910c185", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878629585300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ca50a2-3778-419a-bb18-9834f6e483c6", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878630618400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de96e371-baae-4bc8-89f9-a63768b2977a", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878632763500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "682ab289-541f-43e5-a272-c404d6215f04", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878632968500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db1ea347-933a-4243-a5bd-ecefd6da9e90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878633109100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ac0812-49a4-40e3-811b-820d05a74787", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878633203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399348f1-0d31-4aa3-950d-6c297b027634", "name": "entry : default@CopyPreviewProfile cost memory 0.2130584716796875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878638354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4022e05b-67a1-4f4d-ac9e-deacf46d7245", "name": "runTaskFromQueue task cost before running: 750 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878638495600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c3a0ec-364d-451d-bd5f-5c950e051027", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878630602600, "endTime": 312878638570500, "totalTime": 7869500}, "additional": {"logType": "info", "children": [], "durationId": "d1399b4c-f38b-4457-a07e-a2bc93acaa99"}}, {"head": {"id": "d6f550f0-a462-4d6c-bba7-21ae9e919a6f", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642122900, "endTime": 312878642557000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "2df4e5f0-3e72-4e86-a24e-cd1259835a4d", "logId": "005bea1c-4385-4fb1-b25b-7dfb5cf51e75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2df4e5f0-3e72-4e86-a24e-cd1259835a4d", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878640678000}, "additional": {"logType": "detail", "children": [], "durationId": "d6f550f0-a462-4d6c-bba7-21ae9e919a6f"}}, {"head": {"id": "66b05b5c-1e66-41f2-acbf-efe11e570a6a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878641251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5f59cd-db82-4476-97aa-44f3c6c12877", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878641351700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be3ef0c-850d-4bac-972b-600c2cc71093", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04199dd-a98e-462b-8617-ec11858a6929", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642244000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84776185-cd15-4a2a-8abd-47ebaf9e9156", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642314400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcf082d-b9f7-49ee-abe5-ed8ab36bab38", "name": "entry : default@ReplacePreviewerPage cost memory 0.038543701171875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642411400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a29bbc-0c87-4119-8ada-18db17901ad3", "name": "runTaskFromQueue task cost before running: 754 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005bea1c-4385-4fb1-b25b-7dfb5cf51e75", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878642122900, "endTime": 312878642557000, "totalTime": 356300}, "additional": {"logType": "info", "children": [], "durationId": "d6f550f0-a462-4d6c-bba7-21ae9e919a6f"}}, {"head": {"id": "25c4d4f1-8cbd-40bc-83cc-dbf43ef7e643", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645171600, "endTime": 312878645481500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "45fc73d2-dafc-4e06-a608-142b4de04b9a", "logId": "299131d5-a225-4653-b0bf-ca82732c0739"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45fc73d2-dafc-4e06-a608-142b4de04b9a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645106400}, "additional": {"logType": "detail", "children": [], "durationId": "25c4d4f1-8cbd-40bc-83cc-dbf43ef7e643"}}, {"head": {"id": "b2155365-c488-4318-86dc-7aecca889c8d", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9635ec4f-6020-4603-a8e2-254aa5ee48de", "name": "entry : buildPreviewerResource cost memory 0.0119781494140625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645325900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ad8601-fcf3-4254-8525-9af71cbfd56a", "name": "runTaskFromQueue task cost before running: 757 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645419700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "299131d5-a225-4653-b0bf-ca82732c0739", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878645171600, "endTime": 312878645481500, "totalTime": 226400}, "additional": {"logType": "info", "children": [], "durationId": "25c4d4f1-8cbd-40bc-83cc-dbf43ef7e643"}}, {"head": {"id": "5c7c478c-b391-40e2-92d0-4725f82322ea", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878648810500, "endTime": 312878651908300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "eafe3dc2-c55f-457e-aae4-edf87a3c1469", "logId": "8b4687ce-d4e1-4d47-9d0c-fb994f6c93dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eafe3dc2-c55f-457e-aae4-edf87a3c1469", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878647309800}, "additional": {"logType": "detail", "children": [], "durationId": "5c7c478c-b391-40e2-92d0-4725f82322ea"}}, {"head": {"id": "315f8f96-43c5-4216-9566-5bb1ed89ddb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878647907800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60eaa799-0cea-4aed-b167-8b1f980b3217", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878648005900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a7762d6-e33b-4738-b77e-57d559e157de", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878648821300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce2b6c6-3e27-4d52-ace6-23a77b4843bb", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878650749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75deb2a8-900e-49a3-9203-a5b552f0d723", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878650864700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2b3187e-bec6-47d2-90db-0490d3f4b04f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878650952700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae09581-7b00-443e-bca5-77d4bb4d1692", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878651011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4802a516-c2cf-477f-9144-92bfcc0d82f2", "name": "entry : default@PreviewUpdateAssets cost memory 0.1359710693359375", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878651702500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d0d96fa-f57f-4a3c-98b4-184a0af63e4d", "name": "runTaskFromQueue task cost before running: 764 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878651817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4687ce-d4e1-4d47-9d0c-fb994f6c93dd", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878648810500, "endTime": 312878651908300, "totalTime": 2985700}, "additional": {"logType": "info", "children": [], "durationId": "5c7c478c-b391-40e2-92d0-4725f82322ea"}}, {"head": {"id": "9f4a13a7-a865-4a67-aed9-13749dd782b5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878665165500, "endTime": 312897914661500}, "additional": {"children": ["fa78200a-1505-4c54-bb54-5debc3292a1a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6e2808ba-cb4d-47be-bddc-76a5da07811a", "logId": "bf6b0e69-6724-4107-947f-9dd98c173d7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e2808ba-cb4d-47be-bddc-76a5da07811a", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878654319700}, "additional": {"logType": "detail", "children": [], "durationId": "9f4a13a7-a865-4a67-aed9-13749dd782b5"}}, {"head": {"id": "a0b232c4-2e14-47bd-9434-b03d64af53b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878654933700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628fe948-9bdd-4a8c-8d56-d834<PERSON><PERSON><PERSON><PERSON>", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878655030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42d4f66f-7b66-43d1-9938-00dd980b8adc", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878665184200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa78200a-1505-4c54-bb54-5debc3292a1a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312878696942100, "endTime": 312897911670200}, "additional": {"children": ["7d161830-2109-4074-a54f-e0ebe64acad4", "f7646bf1-da64-4afb-97e7-151b213e1ce8", "fa3b15b2-2998-4f9d-957e-ff19b805ec82", "6c2f3558-b172-47cf-9dcd-61fa0d7f9ee2", "5b246bdc-d032-4ddb-9690-c9a4f4db9291"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9f4a13a7-a865-4a67-aed9-13749dd782b5", "logId": "b1c723db-d58f-45da-9962-ebadc9f74d50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69b1ab66-1c1d-4a80-a0d3-93a70f760f28", "name": "entry : default@PreviewArkTS cost memory -0.7402725219726562", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878701239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e15db63-35cc-4038-ad3a-c2d57467dbf4", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312883795310300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d161830-2109-4074-a54f-e0ebe64acad4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312883796376800, "endTime": 312883796395800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa78200a-1505-4c54-bb54-5debc3292a1a", "logId": "b4dd4a6f-b045-49b2-9da8-44e31cb88a37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4dd4a6f-b045-49b2-9da8-44e31cb88a37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312883796376800, "endTime": 312883796395800}, "additional": {"logType": "info", "children": [], "durationId": "7d161830-2109-4074-a54f-e0ebe64acad4", "parent": "b1c723db-d58f-45da-9962-ebadc9f74d50"}}, {"head": {"id": "99ccbe9e-20fd-4028-a5f9-273708d21b1c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897910267500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7646bf1-da64-4afb-97e7-151b213e1ce8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312897911260800, "endTime": 312897911278500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa78200a-1505-4c54-bb54-5debc3292a1a", "logId": "2a59d37e-cb02-4e9c-b905-72731d55c9a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a59d37e-cb02-4e9c-b905-72731d55c9a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897911260800, "endTime": 312897911278500}, "additional": {"logType": "info", "children": [], "durationId": "f7646bf1-da64-4afb-97e7-151b213e1ce8", "parent": "b1c723db-d58f-45da-9962-ebadc9f74d50"}}, {"head": {"id": "b1c723db-d58f-45da-9962-ebadc9f74d50", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312878696942100, "endTime": 312897911670200}, "additional": {"logType": "info", "children": ["b4dd4a6f-b045-49b2-9da8-44e31cb88a37", "2a59d37e-cb02-4e9c-b905-72731d55c9a9", "f3cf1cc0-bdbc-4040-8f71-ecc9aac7d9da", "ab3c455a-e5a9-4b84-a79c-b9be426a75cf", "ee6c6ec6-63c4-4205-b491-13d015ba76a0"], "durationId": "fa78200a-1505-4c54-bb54-5debc3292a1a", "parent": "bf6b0e69-6724-4107-947f-9dd98c173d7b"}}, {"head": {"id": "fa3b15b2-2998-4f9d-957e-ff19b805ec82", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312882441725200, "endTime": 312883752811900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa78200a-1505-4c54-bb54-5debc3292a1a", "logId": "f3cf1cc0-bdbc-4040-8f71-ecc9aac7d9da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3cf1cc0-bdbc-4040-8f71-ecc9aac7d9da", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312882441725200, "endTime": 312883752811900}, "additional": {"logType": "info", "children": [], "durationId": "fa3b15b2-2998-4f9d-957e-ff19b805ec82", "parent": "b1c723db-d58f-45da-9962-ebadc9f74d50"}}, {"head": {"id": "6c2f3558-b172-47cf-9dcd-61fa0d7f9ee2", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312883753021600, "endTime": 312883753166400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa78200a-1505-4c54-bb54-5debc3292a1a", "logId": "ab3c455a-e5a9-4b84-a79c-b9be426a75cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab3c455a-e5a9-4b84-a79c-b9be426a75cf", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312883753021600, "endTime": 312883753166400}, "additional": {"logType": "info", "children": [], "durationId": "6c2f3558-b172-47cf-9dcd-61fa0d7f9ee2", "parent": "b1c723db-d58f-45da-9962-ebadc9f74d50"}}, {"head": {"id": "5b246bdc-d032-4ddb-9690-c9a4f4db9291", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker5", "startTime": 312883753316800, "endTime": 312897910333900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa78200a-1505-4c54-bb54-5debc3292a1a", "logId": "ee6c6ec6-63c4-4205-b491-13d015ba76a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee6c6ec6-63c4-4205-b491-13d015ba76a0", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312883753316800, "endTime": 312897910333900}, "additional": {"logType": "info", "children": [], "durationId": "5b246bdc-d032-4ddb-9690-c9a4f4db9291", "parent": "b1c723db-d58f-45da-9962-ebadc9f74d50"}}, {"head": {"id": "bf6b0e69-6724-4107-947f-9dd98c173d7b", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312878665165500, "endTime": 312897914661500, "totalTime": 19249481800}, "additional": {"logType": "info", "children": ["b1c723db-d58f-45da-9962-ebadc9f74d50"], "durationId": "9f4a13a7-a865-4a67-aed9-13749dd782b5"}}, {"head": {"id": "e3273efb-8bcf-4302-9e1d-ee58c2673010", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897923919600, "endTime": 312897924400800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "16577b47-1c01-410c-8d29-d22263b43a41", "logId": "f70d2559-6d10-4468-a920-1d94b67d3dd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16577b47-1c01-410c-8d29-d22263b43a41", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897923827400}, "additional": {"logType": "detail", "children": [], "durationId": "e3273efb-8bcf-4302-9e1d-ee58c2673010"}}, {"head": {"id": "c0bc3206-4d00-4581-9787-fa9abb2b9908", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897923938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6318ac-7d93-4b4c-bc24-07fc13288b70", "name": "entry : PreviewBuild cost memory 0.01174163818359375", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897924146200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7031d7a-354b-45a6-9b4f-2fde9844bed1", "name": "runTaskFromQueue task cost before running: 20 s 36 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897924302600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70d2559-6d10-4468-a920-1d94b67d3dd7", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897923919600, "endTime": 312897924400800, "totalTime": 349600}, "additional": {"logType": "info", "children": [], "durationId": "e3273efb-8bcf-4302-9e1d-ee58c2673010"}}, {"head": {"id": "7d44a535-75aa-47d4-afdf-31b019b45098", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897933772800, "endTime": 312897933797500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fb6e6172-87b0-4806-9eca-fec3c0b46535", "logId": "9d01883b-959f-46eb-a84a-c76f7812cee2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d01883b-959f-46eb-a84a-c76f7812cee2", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897933772800, "endTime": 312897933797500}, "additional": {"logType": "info", "children": [], "durationId": "7d44a535-75aa-47d4-afdf-31b019b45098"}}, {"head": {"id": "92147cd1-4461-4f34-b6d7-4cdd12ef6bb5", "name": "BUILD SUCCESSFUL in 20 s 46 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897933865100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "dd4cec89-ef51-444b-a40a-8178cf2ba3ca", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312877888771900, "endTime": 312897934218600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 42}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "41ee50b0-5e6f-4596-a14e-48c282e7969b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05703227-12c6-47a4-9a00-50138ce33bf1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934352400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "019d4f74-8389-4e46-86ec-1dc5a4ca41bd", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934423100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16bf560e-bd43-4f53-b198-8af1d3ef8203", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934516900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9b33c4-f8c2-4028-be02-574afdce5c48", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad343b2-4504-4541-9502-432314aee078", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934746500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62e1231-e4ae-47cc-87dc-2497a562077d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897934849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad6e99a-0c13-4656-aada-ab21519fffa9", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897937051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e15b777-a738-468d-bbda-22b919f16091", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897949641500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a23cff-1a46-4079-a771-7cf5362c1644", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897950241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489ab896-0f8e-4391-8ba7-2f1da1ac8eac", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897966587700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad09a57-4d8d-476f-8c54-85a26599907d", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897967649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9780d517-adf2-441a-9e77-16e5bfa4c32a", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897968016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729214f5-68e1-47ea-be57-7734d1ea61ba", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897969420100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9ab8d5f-b682-4103-aad9-6b4376d9720b", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897970714000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b835fb-8984-4625-bc2a-ec9e4aa43d15", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897971420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc66721-882f-42f9-ac34-f435967c8ac3", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897972030000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e67061e4-b359-4833-8744-e98dd8faf7e9", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897972575200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f027711-87a8-4d6b-9be2-bd77558317c6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897978009400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4689c81b-b516-4236-beef-1c331356a534", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897979501000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8d2302-a991-4df6-b87b-e0c1638a13ac", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897980048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed377553-07f9-4139-a1b2-31970c7a67cb", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897980566800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01fd3814-ca0d-49ab-b256-9a37e6c5b210", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897982011600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5b9c5b-f3c1-4f80-ae7d-6bda1b2d6971", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312897999494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c1c6774-99a8-47ec-8f42-03dbd09d1acc", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312898000027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b31e2b49-f193-4dd7-856d-0d6f4ee39ff0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312898000501500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42a51492-137b-4688-baf2-0ec7a946585d", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312898001129300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e54c0ee-650b-4f5f-a1d6-32b01fe457b1", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:29 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312898001557400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}