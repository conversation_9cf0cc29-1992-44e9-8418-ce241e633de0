import http from '@ohos.net.http';
import { BusinessError } from '@ohos.base';
import { ApiResponse, ErrorType, ApiError } from '../types/index';
import { errorHandler } from '../utils/ErrorHandler';

/**
 * HTTP客户端工具类
 * 封装网络请求，提供统一的API调用接口
 */
export class HttpClient {
  private static instance: HttpClient;
  private baseUrl: string = 'http://10.0.2.2:8080'; // 模拟器访问宿主机的Spring Boot服务
  private timeout: number = 10000;
  private maxRetries: number = 3;
  private retryDelay: number = 1000;

  private constructor() {}

  public static getInstance(): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient();
    }
    return HttpClient.instance;
  }

  /**
   * 设置基础URL
   */
  public setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * 设置超时时间
   */
  public setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  /**
   * 设置重试次数
   */
  public setMaxRetries(maxRetries: number): void {
    this.maxRetries = maxRetries;
  }

  /**
   * 设置重试延迟
   */
  public setRetryDelay(retryDelay: number): void {
    this.retryDelay = retryDelay;
  }

  /**
   * GET请求
   */
  public async get<T>(url: string, params?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(http.RequestMethod.GET, url, undefined, params);
  }

  /**
   * POST请求
   */
  public async post<T>(url: string, data?: object): Promise<ApiResponse<T>> {
    return this.request<T>(http.RequestMethod.POST, url, data);
  }

  /**
   * POST请求（form格式）
   */
  public async postForm<T>(url: string, data?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.requestForm<T>(http.RequestMethod.POST, url, data);
  }

  /**
   * PUT请求
   */
  public async put<T>(url: string, data?: object): Promise<ApiResponse<T>> {
    return this.request<T>(http.RequestMethod.PUT, url, data);
  }

  /**
   * DELETE请求
   */
  public async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(http.RequestMethod.DELETE, url);
  }

  /**
   * 通用请求方法（带重试机制）
   */
  private async request<T>(
    method: http.RequestMethod,
    url: string,
    data?: object,
    params?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>(method, url, 0, data, params);
  }

  /**
   * Form请求方法（带重试机制）
   */
  private async requestForm<T>(
    method: http.RequestMethod,
    url: string,
    data?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.requestFormWithRetry<T>(method, url, 0, data);
  }

  /**
   * 带重试机制的请求方法
   */
  private async requestWithRetry<T>(
    method: http.RequestMethod,
    url: string,
    retryCount: number,
    data?: object,
    params?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      const httpRequest = http.createHttp();
      
      // 构建完整URL
      let fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
      
      // 添加查询参数
      if (params) {
        const queryString = Object.keys(params)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
          .join('&');
        fullUrl += `?${queryString}`;
      }

      // 请求选项
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // 添加认证头
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const options: http.HttpRequestOptions = {
        method: method,
        header: headers,
        readTimeout: this.timeout,
        connectTimeout: this.timeout
      };

      // 添加请求体
      if (data && (method === http.RequestMethod.POST || method === http.RequestMethod.PUT)) {
        options.extraData = JSON.stringify(data);
      }

      httpRequest.request(fullUrl, options, (err: BusinessError, data: http.HttpResponse) => {
        if (err) {
          console.error(`HTTP请求失败 (尝试 ${retryCount + 1}/${this.maxRetries + 1}):`, err);

          // 判断是否需要重试
          if (retryCount < this.maxRetries && this.shouldRetry(err.code)) {
            setTimeout(() => {
              this.requestWithRetry<T>(method, url, retryCount + 1, data, params)
                .then(resolve)
                .catch(reject);
            }, this.retryDelay * (retryCount + 1));
            return;
          }

          // 创建网络错误
          const networkError = new ApiError(err.code, err.message, ErrorType.NETWORK_ERROR);
          reject(networkError);
          return;
        }

        try {
          // 检查HTTP状态码
          if (data.responseCode !== 200) {
            const httpError = new ApiError(
              data.responseCode,
              `HTTP ${data.responseCode}`,
              ErrorType.API_ERROR
            );
            reject(httpError);
            return;
          }

          const springBootResponse: {code: number, msg: string, data: T} = JSON.parse(data.result as string);

          // 检查Spring Boot的业务状态码
          if (springBootResponse.code === 200) {
            // 转换为标准ApiResponse格式
            const response: ApiResponse<T> = {
              code: springBootResponse.code,
              message: springBootResponse.msg,
              data: springBootResponse.data,
              timestamp: Date.now()
            };
            resolve(response);
          } else {
            // 根据业务错误码判断错误类型
            const errorType = springBootResponse.code === 401 ? ErrorType.AUTH_ERROR : ErrorType.BUSINESS_ERROR;
            const businessError = new ApiError(springBootResponse.code, springBootResponse.msg, errorType);
            reject(businessError);
          }
        } catch (parseError) {
          console.error('响应解析失败:', parseError);
          const parseErr = new ApiError(-1, '响应解析失败', ErrorType.API_ERROR);
          reject(parseErr);
        } finally {
          httpRequest.destroy();
        }
      });
    });
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(errorCode: number): boolean {
    // 网络相关错误码，可以重试
    const retryableCodes = [
      2300001, // 网络连接失败
      2300002, // 网络连接超时
      2300003, // 网络连接中断
    ];
    return retryableCodes.includes(errorCode);
  }

  /**
   * 获取认证Token
   */
  private getAuthToken(): string | null {
    // 注意：这里需要同步获取，实际使用时需要在应用启动时预加载token
    // 在实际项目中，建议在应用启动时将token加载到内存中
    return this.cachedToken;
  }

  private cachedToken: string | null = null;

  /**
   * 设置认证Token（缓存到内存）
   */
  public setAuthToken(token: string): void {
    this.cachedToken = token;
  }

  /**
   * 清除认证Token
   */
  public clearAuthToken(): void {
    this.cachedToken = null;
  }

  /**
   * 检查网络连接状态
   */
  public async checkConnection(): Promise<boolean> {
    try {
      const response = await this.get<string>('/health');
      return response.code === 200;
    } catch (error) {
      console.error('网络连接检查失败:', error);
      return false;
    }
  }

  /**
   * 带重试机制的Form请求方法
   */
  private async requestFormWithRetry<T>(
    method: http.RequestMethod,
    url: string,
    retryCount: number,
    data?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      const httpRequest = http.createHttp();

      // 构建完整URL
      let fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;

      // 请求选项
      const headers: Record<string, string> = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      };

      // 添加认证头
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 构建form数据
      let formData = '';
      if (data) {
        formData = Object.keys(data)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
          .join('&');
      }

      const options: http.HttpRequestOptions = {
        method: method,
        header: headers,
        extraData: formData,
        expectDataType: http.HttpDataType.STRING,
        usingCache: false,
        priority: 1,
        connectTimeout: this.timeout,
        readTimeout: this.timeout,
        usingProtocol: http.HttpProtocol.HTTP1_1
      };

      console.log(`发送Form请求: ${method} ${fullUrl}`);
      console.log('Form数据:', formData);

      httpRequest.request(fullUrl, options, (err: BusinessError, data: http.HttpResponse) => {
        if (err) {
          console.error('Form请求失败:', err);

          // 检查是否需要重试
          if (retryCount < this.maxRetries && this.shouldRetry(err.code)) {
            console.log(`Form请求重试 ${retryCount + 1}/${this.maxRetries}`);
            setTimeout(() => {
              this.requestFormWithRetry<T>(method, url, retryCount + 1, data)
                .then(resolve)
                .catch(reject);
            }, this.retryDelay);
            return;
          }

          const networkError = new ApiError(0, `网络请求失败: ${err.message}`, ErrorType.NETWORK_ERROR);
          reject(networkError);
          return;
        }

        try {
          const springBootResponse: {code: number, msg: string, data: T} = JSON.parse(data.result as string);

          // 检查Spring Boot的业务状态码
          if (springBootResponse.code === 200) {
            // 转换为标准ApiResponse格式
            const response: ApiResponse<T> = {
              code: springBootResponse.code,
              message: springBootResponse.msg,
              data: springBootResponse.data,
              timestamp: Date.now()
            };
            resolve(response);
          } else {
            // 根据业务错误码判断错误类型
            const errorType = springBootResponse.code === 401 ? ErrorType.AUTH_ERROR : ErrorType.BUSINESS_ERROR;
            const businessError = new ApiError(springBootResponse.code, springBootResponse.msg, errorType);
            reject(businessError);
          }
        } catch (parseError) {
          console.error('Form响应解析失败:', parseError);
          const parseErr = new ApiError(0, '响应数据解析失败', ErrorType.NETWORK_ERROR);
          reject(parseErr);
        }
      });
    });
  }

  /**
   * 获取请求配置信息
   */
  public getConfig(): HttpConfig {
    return new HttpConfig(this.baseUrl, this.timeout, this.maxRetries);
  }
}

/**
 * HTTP配置类
 */
export class HttpConfig {
  public baseUrl: string;
  public timeout: number;
  public maxRetries: number;

  constructor(baseUrl: string, timeout: number, maxRetries: number) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.maxRetries = maxRetries;
  }
}

/**
 * 导出单例实例
 */
export const httpClient = HttpClient.getInstance();
