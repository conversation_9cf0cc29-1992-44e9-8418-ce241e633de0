package com.icss.spring.controller;

import com.icss.spring.result.ApiException;
import com.icss.spring.result.Result;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
@CrossOrigin
@RestControllerAdvice
public class ExceptionController {
    @ExceptionHandler(ApiException.class)
    public Result exceptionHandler(ApiException e){
        return Result.error(e.getMessage());
    }
}
