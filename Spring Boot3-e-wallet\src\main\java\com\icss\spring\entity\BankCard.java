package com.icss.spring.entity;

import java.math.BigDecimal;

public class BankCard {
    private Long id;
    private Long userId;
    private String cardNumber;
    private String cardType; // DEBIT/CREDIT
    private String bankName;
    private BigDecimal balance;
    private BigDecimal creditLimit;
    private Integer bindStatus; // 1:绑定 0:解绑
    private String cardPassword; // 银行卡密码

    // 构造方法
    public BankCard() {}

    public BankCard(Long id, Long userId, String cardNumber, String cardType,
                    String bankName, BigDecimal balance, BigDecimal creditLimit, Integer bindStatus) {
        this.id = id;
        this.userId = userId;
        this.cardNumber = cardNumber;
        this.cardType = cardType;
        this.bankName = bankName;
        this.balance = balance;
        this.creditLimit = creditLimit;
        this.bindStatus = bindStatus;
        this.cardPassword = "123456"; // 默认密码
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public Integer getBindStatus() {
        return bindStatus;
    }

    public void setBindStatus(Integer bindStatus) {
        this.bindStatus = bindStatus;
    }

    public String getCardPassword() {
        return cardPassword;
    }

    public void setCardPassword(String cardPassword) {
        this.cardPassword = cardPassword;
    }
}