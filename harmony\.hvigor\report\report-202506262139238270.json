{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "455f896d-aa2e-4ed2-b39d-a9783bae8c9f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20896011627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e67f38db-8d58-4065-a3a7-70ddce8d4639", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20896019933100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d290b51-a94e-43eb-96e0-51767c429a6e", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20896020125200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e20567-1ee6-4b78-a0f0-767de7005bad", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20922029822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005cb181-c696-47ba-aa27-49140ec149d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20922030926600, "endTime": 20922030946000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "208481ab-cd7a-454e-bf8a-30ce5b69d35b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "208481ab-cd7a-454e-bf8a-30ce5b69d35b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20922030926600, "endTime": 20922030946000}, "additional": {"logType": "info", "children": [], "durationId": "005cb181-c696-47ba-aa27-49140ec149d5"}}, {"head": {"id": "c6d9fb2b-3aac-45c4-b67b-279aaa5feb39", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20924079682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dec2188d-5690-4bc0-b037-3444bc229167", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20924081161700, "endTime": 20924081206800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "17aef75b-b064-4594-ba1c-f583f0b35da1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17aef75b-b064-4594-ba1c-f583f0b35da1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20924081161700, "endTime": 20924081206800}, "additional": {"logType": "info", "children": [], "durationId": "dec2188d-5690-4bc0-b037-3444bc229167"}}, {"head": {"id": "459cb8ce-48af-481b-b8bd-e4a9f40489c4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952508730200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b22a369-4a75-4aae-a416-94fd617a8127", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952518598000, "endTime": 20952700478900}, "additional": {"children": ["9f9fa083-4fc9-444a-b2c4-b18036df1dd3", "60ff820e-15af-40e3-b395-14c470bb9c6e", "aa44c3c2-658b-4986-89a6-04bbc3e344d0", "053ef986-d1ae-4ec0-9451-c8d79f8810ea", "cd70b12f-722c-473d-8c71-a384a93aa90a", "1209db87-20e6-4ec7-ae9b-68c38b616cfe", "65b995c0-e580-462d-b2fd-c03390d30309"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f9fa083-4fc9-444a-b2c4-b18036df1dd3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952518599500, "endTime": 20952531067900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "13750eb9-649f-4e28-bdb9-d227285e2024"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60ff820e-15af-40e3-b395-14c470bb9c6e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952531084500, "endTime": 20952699355400}, "additional": {"children": ["c0f2a1f4-1b79-430c-b15d-0d08434e6811", "9a9964fb-6d82-44ce-bed2-f9477169c40a", "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "2b37a3fb-f8ec-4afd-ba33-816553fb50fb", "3dfab9b3-3985-4b8c-a497-a2ecb29dc32c", "afd1c2f4-8178-4a70-b37a-fdd9518d787a", "605a7620-254c-4145-923d-39cb98dc7a61", "fda1c8fe-f23a-471b-9f86-d4b2f3e2deba", "bda57c9b-a771-4217-81a4-721025d851c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa44c3c2-658b-4986-89a6-04bbc3e344d0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952699381000, "endTime": 20952700471400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "f2e97ff5-4207-4a4d-ade2-7067d13cec05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "053ef986-d1ae-4ec0-9451-c8d79f8810ea", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952700475100, "endTime": 20952700476300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "26426c50-dbc9-45be-bf03-c0ea23f95091"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd70b12f-722c-473d-8c71-a384a93aa90a", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952521966900, "endTime": 20952521993800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "3b9f193d-0384-47ad-be33-dd4901cc578b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b9f193d-0384-47ad-be33-dd4901cc578b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952521966900, "endTime": 20952521993800}, "additional": {"logType": "info", "children": [], "durationId": "cd70b12f-722c-473d-8c71-a384a93aa90a", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "1209db87-20e6-4ec7-ae9b-68c38b616cfe", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952527364700, "endTime": 20952527379000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "dd324eb2-9b8e-4388-abc1-5971787d2815"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd324eb2-9b8e-4388-abc1-5971787d2815", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952527364700, "endTime": 20952527379000}, "additional": {"logType": "info", "children": [], "durationId": "1209db87-20e6-4ec7-ae9b-68c38b616cfe", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "a89d2be2-69b6-4b40-b605-ea243ef38a32", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952527423400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7ff446-45e7-4494-b2bd-5f0d4a1fa384", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952530960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13750eb9-649f-4e28-bdb9-d227285e2024", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952518599500, "endTime": 20952531067900}, "additional": {"logType": "info", "children": [], "durationId": "9f9fa083-4fc9-444a-b2c4-b18036df1dd3", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "c0f2a1f4-1b79-430c-b15d-0d08434e6811", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952537743500, "endTime": 20952537753900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "47550d55-f5ef-4077-8270-3234edea2aa7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a9964fb-6d82-44ce-bed2-f9477169c40a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952537772600, "endTime": 20952543386600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "421a4d12-55e0-49b6-baab-9f43d6bbfc8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952543399700, "endTime": 20952622524700}, "additional": {"children": ["20405fbd-7927-43ec-81d0-ae6081df94fb", "864b6be4-175e-40aa-8e88-89877675b779", "301300a0-37de-4c72-9e03-a027b1a5bf59"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "4b068cfb-b480-4c68-8d5a-a4700650ffae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b37a3fb-f8ec-4afd-ba33-816553fb50fb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952622538100, "endTime": 20952644272700}, "additional": {"children": ["ac5c877a-8aec-43d8-94e1-32301e5c8f96"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "f70d1ca9-70db-42e3-a63c-9a8c2d318681"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dfab9b3-3985-4b8c-a497-a2ecb29dc32c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952644279000, "endTime": 20952671040300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "370db8d3-807c-4a3d-85fe-411a1ac298f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afd1c2f4-8178-4a70-b37a-fdd9518d787a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952672470800, "endTime": 20952683966200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "1182a35e-b141-4dd1-81e4-20c976d4901f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "605a7620-254c-4145-923d-39cb98dc7a61", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952683991500, "endTime": 20952699217300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "52f557e5-2901-48d6-8a84-619ecadbc855"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda1c8fe-f23a-471b-9f86-d4b2f3e2deba", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952699234200, "endTime": 20952699345500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "893898d7-033e-4c9a-a1a0-02cb2cff88b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47550d55-f5ef-4077-8270-3234edea2aa7", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952537743500, "endTime": 20952537753900}, "additional": {"logType": "info", "children": [], "durationId": "c0f2a1f4-1b79-430c-b15d-0d08434e6811", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "421a4d12-55e0-49b6-baab-9f43d6bbfc8b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952537772600, "endTime": 20952543386600}, "additional": {"logType": "info", "children": [], "durationId": "9a9964fb-6d82-44ce-bed2-f9477169c40a", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "20405fbd-7927-43ec-81d0-ae6081df94fb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952544103400, "endTime": 20952544118900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "logId": "e345f81f-468c-4a15-bc43-7e9c94b6c077"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e345f81f-468c-4a15-bc43-7e9c94b6c077", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952544103400, "endTime": 20952544118900}, "additional": {"logType": "info", "children": [], "durationId": "20405fbd-7927-43ec-81d0-ae6081df94fb", "parent": "4b068cfb-b480-4c68-8d5a-a4700650ffae"}}, {"head": {"id": "864b6be4-175e-40aa-8e88-89877675b779", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952546402300, "endTime": 20952621786600}, "additional": {"children": ["e991121d-b9f0-473c-9f14-6b214c0e3036", "e3e28440-6540-4d9f-9a45-6ba3a58fb939"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "logId": "c74dfb85-4a05-4dd4-8aff-ed303c99d224"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e991121d-b9f0-473c-9f14-6b214c0e3036", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952546403400, "endTime": 20952550298800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "864b6be4-175e-40aa-8e88-89877675b779", "logId": "ac51126f-29ca-4c47-a927-a05a6b82d95d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3e28440-6540-4d9f-9a45-6ba3a58fb939", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952550316600, "endTime": 20952621772200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "864b6be4-175e-40aa-8e88-89877675b779", "logId": "91a9f62f-03b4-4f37-aa8e-d90e7ee3aa30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8de03478-c271-49c2-ac85-152f3243a55e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952546407600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3018ce43-76f6-4829-a741-0e8f7e15e899", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952550154100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac51126f-29ca-4c47-a927-a05a6b82d95d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952546403400, "endTime": 20952550298800}, "additional": {"logType": "info", "children": [], "durationId": "e991121d-b9f0-473c-9f14-6b214c0e3036", "parent": "c74dfb85-4a05-4dd4-8aff-ed303c99d224"}}, {"head": {"id": "099ea576-73a7-450b-92cd-545bd3b59d06", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952550329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09c0146-34e2-4742-bb9d-2b75b64c2814", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952557567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e0535a-198b-4e95-b977-a8577b83a821", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952557669600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97201e75-6c13-44a0-b2ed-b6972feff469", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952557794000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa491ffd-3df8-4e32-8898-1e24fdd9804e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952557882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e234e77-f729-49c4-8898-aceb54245ca8", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952559617100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91aac44b-ce0e-4dd1-ae58-909ff54bb7b9", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952563721900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b0a59af-db71-4659-ad79-b8a910f8a2ff", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952572838900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1504b0ed-0b0f-479b-8f77-abb641aebef2", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952599308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af2a53e9-5088-4556-b438-93c7fb63faf1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952599423000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 39}, "markType": "other"}}, {"head": {"id": "d2f79667-a852-418f-ad62-a74a5ae125b7", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952599436600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 39}, "markType": "other"}}, {"head": {"id": "f55ec592-b85e-4db9-a22c-0906c0a6040d", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952621517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38735a3-28d1-4390-9c73-8ecc427c7591", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952621631900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbc3310-ee97-4154-83f6-6a6cb1cb633d", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952621687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc01cf00-a24b-4832-805c-6fec076c8262", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952621732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a9f62f-03b4-4f37-aa8e-d90e7ee3aa30", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952550316600, "endTime": 20952621772200}, "additional": {"logType": "info", "children": [], "durationId": "e3e28440-6540-4d9f-9a45-6ba3a58fb939", "parent": "c74dfb85-4a05-4dd4-8aff-ed303c99d224"}}, {"head": {"id": "c74dfb85-4a05-4dd4-8aff-ed303c99d224", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952546402300, "endTime": 20952621786600}, "additional": {"logType": "info", "children": ["ac51126f-29ca-4c47-a927-a05a6b82d95d", "91a9f62f-03b4-4f37-aa8e-d90e7ee3aa30"], "durationId": "864b6be4-175e-40aa-8e88-89877675b779", "parent": "4b068cfb-b480-4c68-8d5a-a4700650ffae"}}, {"head": {"id": "301300a0-37de-4c72-9e03-a027b1a5bf59", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952622499100, "endTime": 20952622513300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "logId": "6643f121-7e74-4fc3-8b86-f4667c3f0c59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6643f121-7e74-4fc3-8b86-f4667c3f0c59", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952622499100, "endTime": 20952622513300}, "additional": {"logType": "info", "children": [], "durationId": "301300a0-37de-4c72-9e03-a027b1a5bf59", "parent": "4b068cfb-b480-4c68-8d5a-a4700650ffae"}}, {"head": {"id": "4b068cfb-b480-4c68-8d5a-a4700650ffae", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952543399700, "endTime": 20952622524700}, "additional": {"logType": "info", "children": ["e345f81f-468c-4a15-bc43-7e9c94b6c077", "c74dfb85-4a05-4dd4-8aff-ed303c99d224", "6643f121-7e74-4fc3-8b86-f4667c3f0c59"], "durationId": "88331cd4-5638-40b5-b1f4-c8e5f06e1663", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "ac5c877a-8aec-43d8-94e1-32301e5c8f96", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952623182400, "endTime": 20952644261600}, "additional": {"children": ["192b9388-0d1e-4ac0-b740-d79f51be6dea", "f7f656a1-2c0d-460b-961e-2be322e7ae0e", "5e469675-4ae8-49c6-953c-4ca0631d23b4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b37a3fb-f8ec-4afd-ba33-816553fb50fb", "logId": "1214eb5d-9f78-462d-a152-13036a7d19fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "192b9388-0d1e-4ac0-b740-d79f51be6dea", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952626409300, "endTime": 20952626424600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac5c877a-8aec-43d8-94e1-32301e5c8f96", "logId": "4bd4e1f7-e847-4b45-b94c-f4fb7b51ea00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd4e1f7-e847-4b45-b94c-f4fb7b51ea00", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952626409300, "endTime": 20952626424600}, "additional": {"logType": "info", "children": [], "durationId": "192b9388-0d1e-4ac0-b740-d79f51be6dea", "parent": "1214eb5d-9f78-462d-a152-13036a7d19fa"}}, {"head": {"id": "f7f656a1-2c0d-460b-961e-2be322e7ae0e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952628443100, "endTime": 20952642823900}, "additional": {"children": ["65bbe8fb-d83f-4fc5-86c5-6acae0ca191f", "89913d05-53bd-4d04-b741-a4d439b6adc5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac5c877a-8aec-43d8-94e1-32301e5c8f96", "logId": "2d82b839-770f-4700-8462-c45fecd41802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65bbe8fb-d83f-4fc5-86c5-6acae0ca191f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952628444200, "endTime": 20952631481100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7f656a1-2c0d-460b-961e-2be322e7ae0e", "logId": "1b29a626-18fd-4e97-a59b-725857a00801"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89913d05-53bd-4d04-b741-a4d439b6adc5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952631495600, "endTime": 20952642810700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f7f656a1-2c0d-460b-961e-2be322e7ae0e", "logId": "27fb384b-1071-4019-993a-5eca593c0574"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d9941a-5444-4a73-8c14-994bac577732", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952628448600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "091badd7-1ea7-4680-b64f-29a32e437436", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952631379800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b29a626-18fd-4e97-a59b-725857a00801", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952628444200, "endTime": 20952631481100}, "additional": {"logType": "info", "children": [], "durationId": "65bbe8fb-d83f-4fc5-86c5-6acae0ca191f", "parent": "2d82b839-770f-4700-8462-c45fecd41802"}}, {"head": {"id": "87f598b2-a410-4281-b4a1-520c6c4a2c98", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952631505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983f1010-28f4-4122-b86b-90eea1598a61", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952638446000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2858ae71-41a5-4fcb-a49f-9a79af9f6a1c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952638556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06acec38-8aaf-45c7-a572-925065d0fb80", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952638727100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62154a66-68ea-4dd5-a75a-529bfaac7f47", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952639508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbddab71-0ba0-4638-a548-dddb4b3cd57e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952639572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50813188-cfca-4a62-93a9-b66f4bb2c6ee", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952639616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c64a3e0c-455d-4894-b449-cf9557245bc1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952639667300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4c9c86c-6943-4669-a275-a8e5b4894747", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952642537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45860b18-d3e3-44a9-885b-cec61d9d0ae1", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952642644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4b4cd8-bd82-4ad7-b460-6870a22679e4", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952642696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b2c1ad0-189a-4a04-983b-661b94cde926", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952642753600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fb384b-1071-4019-993a-5eca593c0574", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952631495600, "endTime": 20952642810700}, "additional": {"logType": "info", "children": [], "durationId": "89913d05-53bd-4d04-b741-a4d439b6adc5", "parent": "2d82b839-770f-4700-8462-c45fecd41802"}}, {"head": {"id": "2d82b839-770f-4700-8462-c45fecd41802", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952628443100, "endTime": 20952642823900}, "additional": {"logType": "info", "children": ["1b29a626-18fd-4e97-a59b-725857a00801", "27fb384b-1071-4019-993a-5eca593c0574"], "durationId": "f7f656a1-2c0d-460b-961e-2be322e7ae0e", "parent": "1214eb5d-9f78-462d-a152-13036a7d19fa"}}, {"head": {"id": "5e469675-4ae8-49c6-953c-4ca0631d23b4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952644239500, "endTime": 20952644251100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac5c877a-8aec-43d8-94e1-32301e5c8f96", "logId": "bb276250-abcb-4e65-a040-7c45aed366b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb276250-abcb-4e65-a040-7c45aed366b3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952644239500, "endTime": 20952644251100}, "additional": {"logType": "info", "children": [], "durationId": "5e469675-4ae8-49c6-953c-4ca0631d23b4", "parent": "1214eb5d-9f78-462d-a152-13036a7d19fa"}}, {"head": {"id": "1214eb5d-9f78-462d-a152-13036a7d19fa", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952623182400, "endTime": 20952644261600}, "additional": {"logType": "info", "children": ["4bd4e1f7-e847-4b45-b94c-f4fb7b51ea00", "2d82b839-770f-4700-8462-c45fecd41802", "bb276250-abcb-4e65-a040-7c45aed366b3"], "durationId": "ac5c877a-8aec-43d8-94e1-32301e5c8f96", "parent": "f70d1ca9-70db-42e3-a63c-9a8c2d318681"}}, {"head": {"id": "f70d1ca9-70db-42e3-a63c-9a8c2d318681", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952622538100, "endTime": 20952644272700}, "additional": {"logType": "info", "children": ["1214eb5d-9f78-462d-a152-13036a7d19fa"], "durationId": "2b37a3fb-f8ec-4afd-ba33-816553fb50fb", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "18c9c28b-9118-415f-a8dc-9b7d77fe32cc", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952670517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de6836c3-0233-455e-9764-985994fba47e", "name": "hvigorfile, resolve hvigorfile dependencies in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952670897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370db8d3-807c-4a3d-85fe-411a1ac298f8", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952644279000, "endTime": 20952671040300}, "additional": {"logType": "info", "children": [], "durationId": "3dfab9b3-3985-4b8c-a497-a2ecb29dc32c", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "bda57c9b-a771-4217-81a4-721025d851c5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952672142800, "endTime": 20952672444000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60ff820e-15af-40e3-b395-14c470bb9c6e", "logId": "3b72058d-ad14-4fad-9455-342d34ec1617"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5f933e2-df79-46e0-b09d-de4001939946", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952672171800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b72058d-ad14-4fad-9455-342d34ec1617", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952672142800, "endTime": 20952672444000}, "additional": {"logType": "info", "children": [], "durationId": "bda57c9b-a771-4217-81a4-721025d851c5", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "292e456e-066b-40ef-97fa-9bca988368a0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952674670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83f060ea-fce8-42d4-8011-0c7956fe9956", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952682829600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1182a35e-b141-4dd1-81e4-20c976d4901f", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952672470800, "endTime": 20952683966200}, "additional": {"logType": "info", "children": [], "durationId": "afd1c2f4-8178-4a70-b37a-fdd9518d787a", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "cd7273fd-cbbe-4f73-9ec9-4bca0e98779e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952684005000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61496e4f-898f-42e3-923f-f0de42e4c5d7", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952692416600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415cf549-407a-4372-8bb5-2d0ecdcc0941", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952692557600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e48e570b-fb78-4aa1-99ed-dee18f4e9bcc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952692867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f546b7e-76c9-439f-ad69-7aa48c33fcd9", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952696183900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36750e2c-34f7-4136-94a7-b486160006d5", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952696302200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f557e5-2901-48d6-8a84-619ecadbc855", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952683991500, "endTime": 20952699217300}, "additional": {"logType": "info", "children": [], "durationId": "605a7620-254c-4145-923d-39cb98dc7a61", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "1a513cd9-2f03-4a17-8222-9f40ca7ba695", "name": "Configuration phase cost:162 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952699254600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893898d7-033e-4c9a-a1a0-02cb2cff88b2", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952699234200, "endTime": 20952699345500}, "additional": {"logType": "info", "children": [], "durationId": "fda1c8fe-f23a-471b-9f86-d4b2f3e2deba", "parent": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e"}}, {"head": {"id": "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952531084500, "endTime": 20952699355400}, "additional": {"logType": "info", "children": ["47550d55-f5ef-4077-8270-3234edea2aa7", "421a4d12-55e0-49b6-baab-9f43d6bbfc8b", "4b068cfb-b480-4c68-8d5a-a4700650ffae", "f70d1ca9-70db-42e3-a63c-9a8c2d318681", "370db8d3-807c-4a3d-85fe-411a1ac298f8", "1182a35e-b141-4dd1-81e4-20c976d4901f", "52f557e5-2901-48d6-8a84-619ecadbc855", "893898d7-033e-4c9a-a1a0-02cb2cff88b2", "3b72058d-ad14-4fad-9455-342d34ec1617"], "durationId": "60ff820e-15af-40e3-b395-14c470bb9c6e", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "65b995c0-e580-462d-b2fd-c03390d30309", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952700449700, "endTime": 20952700463400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b22a369-4a75-4aae-a416-94fd617a8127", "logId": "f91302ea-cdc7-4cad-a8f0-92a90dd1119c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f91302ea-cdc7-4cad-a8f0-92a90dd1119c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952700449700, "endTime": 20952700463400}, "additional": {"logType": "info", "children": [], "durationId": "65b995c0-e580-462d-b2fd-c03390d30309", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "f2e97ff5-4207-4a4d-ade2-7067d13cec05", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952699381000, "endTime": 20952700471400}, "additional": {"logType": "info", "children": [], "durationId": "aa44c3c2-658b-4986-89a6-04bbc3e344d0", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "26426c50-dbc9-45be-bf03-c0ea23f95091", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952700475100, "endTime": 20952700476300}, "additional": {"logType": "info", "children": [], "durationId": "053ef986-d1ae-4ec0-9451-c8d79f8810ea", "parent": "d762b205-78ed-47fa-a2ee-61e62eaf4991"}}, {"head": {"id": "d762b205-78ed-47fa-a2ee-61e62eaf4991", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952518598000, "endTime": 20952700478900}, "additional": {"logType": "info", "children": ["13750eb9-649f-4e28-bdb9-d227285e2024", "ea42c7c7-ab38-4a50-ad31-21dc03e8e22e", "f2e97ff5-4207-4a4d-ade2-7067d13cec05", "26426c50-dbc9-45be-bf03-c0ea23f95091", "3b9f193d-0384-47ad-be33-dd4901cc578b", "dd324eb2-9b8e-4388-abc1-5971787d2815", "f91302ea-cdc7-4cad-a8f0-92a90dd1119c"], "durationId": "5b22a369-4a75-4aae-a416-94fd617a8127"}}, {"head": {"id": "2e4ecff3-51c1-4f87-9e1e-a0ab5f8d76a9", "name": "Configuration task cost before running: 187 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952700578100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b3747d1-eae7-43da-8f5c-579692812f89", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952707243900, "endTime": 20952717049900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "926cbf55-8d32-4c31-99c2-a744a24ba3d2", "logId": "2af43755-3365-4c27-934c-bea6f694cd26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "926cbf55-8d32-4c31-99c2-a744a24ba3d2", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952702032200}, "additional": {"logType": "detail", "children": [], "durationId": "5b3747d1-eae7-43da-8f5c-579692812f89"}}, {"head": {"id": "8dd7dde6-cbc9-47b9-aea0-33cde61beceb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952702538400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eedbda19-69d6-474a-87e5-3c1ff0966eda", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952702973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2314bbe-8ba8-4983-814f-9feb49d5c124", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952707262000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3912ea-b667-4bb1-a80f-df46df7aa442", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952716775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b794c8c-5e0a-4057-a5e8-d0a1cd45c9c2", "name": "entry : default@PreBuild cost memory 0.25982666015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952716946100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af43755-3365-4c27-934c-bea6f694cd26", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952707243900, "endTime": 20952717049900}, "additional": {"logType": "info", "children": [], "durationId": "5b3747d1-eae7-43da-8f5c-579692812f89"}}, {"head": {"id": "d2cf86a6-6dee-41bf-8853-b7e8e395a7fe", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952724162800, "endTime": 20952726482700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "042b5621-b0b3-4d6b-a9d2-00ae78e9bd29", "logId": "cc53497e-0be3-4425-b61c-df90be4c98d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "042b5621-b0b3-4d6b-a9d2-00ae78e9bd29", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952722718400}, "additional": {"logType": "detail", "children": [], "durationId": "d2cf86a6-6dee-41bf-8853-b7e8e395a7fe"}}, {"head": {"id": "2c4664e9-c343-4c20-bc4f-a62e7815dd22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952723348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9eed4f-3ba4-4713-940d-735100610701", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952723448600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f048b09-8fae-47ab-8809-ee54c5963827", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952724172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbfb590-6137-4fe0-9120-8e8a539de4af", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952726288400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a40ad4-1f18-4005-a5f7-934715b6b997", "name": "entry : default@MergeProfile cost memory 0.10587310791015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952726408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc53497e-0be3-4425-b61c-df90be4c98d5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952724162800, "endTime": 20952726482700}, "additional": {"logType": "info", "children": [], "durationId": "d2cf86a6-6dee-41bf-8853-b7e8e395a7fe"}}, {"head": {"id": "2a53bf50-4bf1-45a0-a38b-2f39e358f403", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952729720200, "endTime": 20952732363400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2e86fc86-3ab0-4beb-a6c8-059eede413da", "logId": "33181aa5-8dc8-4fb9-a99f-574d7ff89c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e86fc86-3ab0-4beb-a6c8-059eede413da", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952728305400}, "additional": {"logType": "detail", "children": [], "durationId": "2a53bf50-4bf1-45a0-a38b-2f39e358f403"}}, {"head": {"id": "72a8b3c6-77a1-45b1-a72c-11dcc84b8bea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952728828700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c61512d7-cbad-43a9-ad3b-fd423addcb59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952728922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d1a876-ab2b-4e5f-905b-761745958447", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952729731200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f4f0651-8c21-4e28-86b7-9f2f6024a17d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952730657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eabed502-f870-41a8-9387-87935f2a589a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952732137700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b27f8d8-d92e-4ec5-be5e-86e3b5dd1eed", "name": "entry : default@CreateBuildProfile cost memory 0.09383392333984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952732278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33181aa5-8dc8-4fb9-a99f-574d7ff89c1b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952729720200, "endTime": 20952732363400}, "additional": {"logType": "info", "children": [], "durationId": "2a53bf50-4bf1-45a0-a38b-2f39e358f403"}}, {"head": {"id": "e56d2d65-5e4d-4435-b360-7b796f8db519", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736363500, "endTime": 20952737090300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9ff95e78-3270-4023-a2d5-94084e54acc4", "logId": "6a34a0b1-054f-45a4-903b-d0bf5dcf0d8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ff95e78-3270-4023-a2d5-94084e54acc4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952734603300}, "additional": {"logType": "detail", "children": [], "durationId": "e56d2d65-5e4d-4435-b360-7b796f8db519"}}, {"head": {"id": "28d0dff5-4e28-4a0c-9584-56d9e0e7e51a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952735220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d4ed21a-0fa9-4bb9-a77a-b804b921b9a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952735337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e87d09a-ec70-4773-a5e2-f0c9083ccc45", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736381900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1222b2ba-157c-4fce-8485-69c547ef2cd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736567500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c23e4c9-63f2-4fa1-8d79-095234b64482", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736639800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f6fef4-69f5-4d22-a92c-ffa1e790b2e2", "name": "entry : default@PreCheckSyscap cost memory 0.03679656982421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042c515c-c9ae-4cd7-8e82-35f20ef45525", "name": "runTaskFromQueue task cost before running: 223 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736921400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a34a0b1-054f-45a4-903b-d0bf5dcf0d8f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952736363500, "endTime": 20952737090300, "totalTime": 525600}, "additional": {"logType": "info", "children": [], "durationId": "e56d2d65-5e4d-4435-b360-7b796f8db519"}}, {"head": {"id": "aabe0ba0-a6c5-4b39-b4af-5369d1c1bd88", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952748703200, "endTime": 20952749662500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ec13ccce-d1af-429a-aac2-52ebc8c9b317", "logId": "2c976fdb-d440-4b04-bf5f-48ef5f8985df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec13ccce-d1af-429a-aac2-52ebc8c9b317", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952739939200}, "additional": {"logType": "detail", "children": [], "durationId": "aabe0ba0-a6c5-4b39-b4af-5369d1c1bd88"}}, {"head": {"id": "e7a843b1-b0ab-47a9-bd67-b9864806d6e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952740651800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "644a3f07-1091-4dbc-a5c6-3e6b3000c406", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952740774600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ef406fe-a5f7-4986-af42-0b44ce234d51", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952748713100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70c4c26-9139-46d6-b71b-c68afbba5872", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952748891500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f642e1ba-5bd7-477a-8cbf-5f9834ba7a9f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952749512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca25cf6-34d1-4007-9cba-b99c16d3b493", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06329345703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952749595800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c976fdb-d440-4b04-bf5f-48ef5f8985df", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952748703200, "endTime": 20952749662500}, "additional": {"logType": "info", "children": [], "durationId": "aabe0ba0-a6c5-4b39-b4af-5369d1c1bd88"}}, {"head": {"id": "40713cc9-140f-4bb6-8970-328acc378d73", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952753388400, "endTime": 20952754652100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1dfc3d96-f9b9-4e0a-bdf2-68da53db541d", "logId": "5475f4cd-088e-4d19-b606-3de80df23e22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dfc3d96-f9b9-4e0a-bdf2-68da53db541d", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952751600000}, "additional": {"logType": "detail", "children": [], "durationId": "40713cc9-140f-4bb6-8970-328acc378d73"}}, {"head": {"id": "5abe369d-94e5-4355-a34d-17d242f6ccb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952752137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77411b71-3355-4418-8a9e-669ddbce74d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952752245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f36e678a-8f2c-4c8d-93ff-95f8335669f7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952753398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f970d7-6059-4758-b3de-320a1bb7f3ba", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952754416300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d8406b-d00c-430c-aab7-b941cf724081", "name": "entry : default@ProcessProfile cost memory 0.053924560546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952754562700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5475f4cd-088e-4d19-b606-3de80df23e22", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952753388400, "endTime": 20952754652100}, "additional": {"logType": "info", "children": [], "durationId": "40713cc9-140f-4bb6-8970-328acc378d73"}}, {"head": {"id": "ff863bb4-c0be-49e7-a003-7678bb4b8eff", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952759405600, "endTime": 20952765726500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f566f883-45ac-4174-85c6-351c6e0add70", "logId": "712ea376-2718-4db9-8d96-34f4b318a2a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f566f883-45ac-4174-85c6-351c6e0add70", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952757028200}, "additional": {"logType": "detail", "children": [], "durationId": "ff863bb4-c0be-49e7-a003-7678bb4b8eff"}}, {"head": {"id": "97287a32-1945-4f9b-8023-11a32dd72add", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952757538000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b260f84-4acb-45f2-9f47-140d72b32ca4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952757631300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b111cc5-b8cb-4128-bd5b-cc0e9f98fa50", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952759418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fa29bd-6db0-4ad0-9795-195725ca7f27", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952765521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e9a976b-f9da-46a1-b120-910fdceecd08", "name": "entry : default@ProcessRouterMap cost memory 0.18445587158203125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952765646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712ea376-2718-4db9-8d96-34f4b318a2a7", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952759405600, "endTime": 20952765726500}, "additional": {"logType": "info", "children": [], "durationId": "ff863bb4-c0be-49e7-a003-7678bb4b8eff"}}, {"head": {"id": "ae13c1ed-5ccc-4fd3-af64-7a201fb0f2b4", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952773283600, "endTime": 20952775985600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "54535f97-0df2-4fc8-a0ce-9a12c58dba02", "logId": "e742d7cb-74af-403e-b556-2e0c18f2b5fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54535f97-0df2-4fc8-a0ce-9a12c58dba02", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952769021000}, "additional": {"logType": "detail", "children": [], "durationId": "ae13c1ed-5ccc-4fd3-af64-7a201fb0f2b4"}}, {"head": {"id": "ac0b2f01-a19d-4bb5-9fb6-25804a06a41b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952769533800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3312f3-ee93-4336-ba76-8614267f8e19", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952769641800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281e2d4d-c94b-4a8a-a5a3-d1539e9bed1b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952770589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c86e22-2a87-45f2-878f-3edfa70466a3", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952774413500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a444949-bc52-44f5-80f2-2355822cedfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952774548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7adc9f12-dd12-4aff-bc72-06f5e4e5ff3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952774605400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147efee3-9ecd-4ec7-8b40-044a1481c244", "name": "entry : default@PreviewProcessResource cost memory 0.06742095947265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952774678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0196cf0-7696-4b7b-b558-43221feb5198", "name": "runTaskFromQueue task cost before running: 262 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952775891900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e742d7cb-74af-403e-b556-2e0c18f2b5fe", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952773283600, "endTime": 20952775985600, "totalTime": 1455600}, "additional": {"logType": "info", "children": [], "durationId": "ae13c1ed-5ccc-4fd3-af64-7a201fb0f2b4"}}, {"head": {"id": "6d2e7a0c-e272-40a7-b505-42de00014f4a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952782874400, "endTime": 20952807358800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c522dde-cbee-45f2-9fc1-493842210005", "logId": "f82bccea-ba85-4981-8945-884066fe9668"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c522dde-cbee-45f2-9fc1-493842210005", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952778883400}, "additional": {"logType": "detail", "children": [], "durationId": "6d2e7a0c-e272-40a7-b505-42de00014f4a"}}, {"head": {"id": "d8398894-0a68-46f9-9ebf-c86c82b2bd4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952779433000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaff780d-a4fa-4bf7-827e-e91f25798ad5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952779527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3bc40f3-01f2-4788-b8c4-9ca0689552c4", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952782887900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad78427d-3dd8-4bb5-a629-dac8702a02b3", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952807143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b827ea3-2757-474a-b456-374d8163c65d", "name": "entry : default@GenerateLoaderJson cost memory -1.022491455078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952807285000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f82bccea-ba85-4981-8945-884066fe9668", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952782874400, "endTime": 20952807358800}, "additional": {"logType": "info", "children": [], "durationId": "6d2e7a0c-e272-40a7-b505-42de00014f4a"}}, {"head": {"id": "d3f796e6-c5d5-4f14-84f2-f8cc52502315", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952819940300, "endTime": 20952842611600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "684d4ef2-d894-4e3f-8f87-be285b4c0b39", "logId": "bee92ebb-ed5b-4123-a007-f26a7603eede"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "684d4ef2-d894-4e3f-8f87-be285b4c0b39", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952816109400}, "additional": {"logType": "detail", "children": [], "durationId": "d3f796e6-c5d5-4f14-84f2-f8cc52502315"}}, {"head": {"id": "9eca9afd-7c6f-4476-bbf6-14ae31857043", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952816662700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32562c0-6bf2-4da2-bf98-c92faadf2ba1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952816759100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42f060f-386f-460a-b1ba-a8f597495adc", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952817734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d6cc88-878c-4b31-a726-d88f17fc5deb", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952819966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b8bcef-80bb-41fa-882c-d940a1b98da2", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952842331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b66204-ffc0-45f5-8c41-18e6b18ff45f", "name": "entry : default@PreviewCompileResource cost memory 0.6918716430664062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952842479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee92ebb-ed5b-4123-a007-f26a7603eede", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952819940300, "endTime": 20952842611600}, "additional": {"logType": "info", "children": [], "durationId": "d3f796e6-c5d5-4f14-84f2-f8cc52502315"}}, {"head": {"id": "23488f32-2569-43c7-8900-4ec0edd9c4cd", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845864700, "endTime": 20952846962100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1950f5d9-0a43-4870-ae6f-c79f7c69378f", "logId": "1f2128b5-0712-4213-b1f1-7b6c194573cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1950f5d9-0a43-4870-ae6f-c79f7c69378f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845192100}, "additional": {"logType": "detail", "children": [], "durationId": "23488f32-2569-43c7-8900-4ec0edd9c4cd"}}, {"head": {"id": "a5b04679-4c2a-459a-98ab-575414075d4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845691700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "476375ee-a8ab-4389-8d9e-b01b4fb02ecb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01c4c6b-2077-47eb-8255-ff3143302949", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845872000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f293223-add4-4d1e-a982-161e83071d16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952846696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa1769e-c5e1-4b47-990f-c036d4afe27b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952846765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01bb85e3-835e-4509-b6b6-9587edcf7504", "name": "entry : default@PreviewHookCompileResource cost memory -1.7368011474609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952846839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89257deb-182b-4350-9b39-4fb11fda07f4", "name": "runTaskFromQueue task cost before running: 333 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952846914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f2128b5-0712-4213-b1f1-7b6c194573cf", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952845864700, "endTime": 20952846962100, "totalTime": 1033200}, "additional": {"logType": "info", "children": [], "durationId": "23488f32-2569-43c7-8900-4ec0edd9c4cd"}}, {"head": {"id": "b4c5b763-cc9e-4091-975a-aba45faf0cdf", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952850173700, "endTime": 20952852758200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "82d45fa1-c693-48f3-b58d-56626c89e2e5", "logId": "fe9852ee-8a71-4c65-97ce-d2b8f7a98ea7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82d45fa1-c693-48f3-b58d-56626c89e2e5", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952848866300}, "additional": {"logType": "detail", "children": [], "durationId": "b4c5b763-cc9e-4091-975a-aba45faf0cdf"}}, {"head": {"id": "e06ae4c3-1917-41f6-a150-9cae290b895a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952849422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13117d6-4f61-4545-b23e-8134c737751b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952849526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1cfa71f-3d75-49ef-ab58-92603748b8b6", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952850196400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b3d27b-954d-4854-b50c-704719ea5132", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952852524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f4a9d8-8f41-4ff3-be77-4f7306372856", "name": "entry : default@CopyPreviewProfile cost memory 0.09345245361328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952852651800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe9852ee-8a71-4c65-97ce-d2b8f7a98ea7", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952850173700, "endTime": 20952852758200}, "additional": {"logType": "info", "children": [], "durationId": "b4c5b763-cc9e-4091-975a-aba45faf0cdf"}}, {"head": {"id": "067ad7cd-cb7a-4cc9-b24b-56f057514d11", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856327100, "endTime": 20952856908000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "de192a01-add2-4afe-970a-76c372493fa7", "logId": "9ea7671b-07c7-4dde-a49e-3edb7ac7d6c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de192a01-add2-4afe-970a-76c372493fa7", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952854955200}, "additional": {"logType": "detail", "children": [], "durationId": "067ad7cd-cb7a-4cc9-b24b-56f057514d11"}}, {"head": {"id": "aedeffe1-f74c-4d3d-80b0-d48c333693b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952855477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "646fb598-6f7c-472c-8eef-1f1195eaf1d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952855574000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "184b4bff-b5b4-4d96-95bc-d3bffdde03e8", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856335000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "185b8622-f746-4d5c-b84b-10479b032218", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856455000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616f9a4b-19fb-4d36-8ef3-20f7fe3bbdf3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b904e32-b811-465a-8779-44e5e4e36b89", "name": "entry : default@ReplacePreviewerPage cost memory 0.0515594482421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856701000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bccbc0e-d375-4adb-a97b-24f31b1c11b0", "name": "runTaskFromQueue task cost before running: 343 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea7671b-07c7-4dde-a49e-3edb7ac7d6c9", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952856327100, "endTime": 20952856908000, "totalTime": 473400}, "additional": {"logType": "info", "children": [], "durationId": "067ad7cd-cb7a-4cc9-b24b-56f057514d11"}}, {"head": {"id": "23488424-b21a-4764-a078-b411a91fa730", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858731300, "endTime": 20952858965100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d922b369-f0c7-4c33-8028-aedfd0bb0e34", "logId": "c16d87d5-3434-4623-911d-15d5bd41b4ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d922b369-f0c7-4c33-8028-aedfd0bb0e34", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858692200}, "additional": {"logType": "detail", "children": [], "durationId": "23488424-b21a-4764-a078-b411a91fa730"}}, {"head": {"id": "f33abcea-3296-4e41-8349-84ba41225713", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858738300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756a96e9-e345-4186-ae60-43a61644c35b", "name": "entry : buildPreviewerResource cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858843900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2b4a846-c16f-4e28-941d-c1f2b3be1b82", "name": "runTaskFromQueue task cost before running: 345 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16d87d5-3434-4623-911d-15d5bd41b4ee", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952858731300, "endTime": 20952858965100, "totalTime": 172500}, "additional": {"logType": "info", "children": [], "durationId": "23488424-b21a-4764-a078-b411a91fa730"}}, {"head": {"id": "e2e6bdc2-31a4-4386-8701-2005b18fa94e", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952862037800, "endTime": 20952864530800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "24ea060b-6635-4d8a-accb-f9db53c9bebf", "logId": "fd92bc2e-8383-4b2d-adaa-a8008b7b1d8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24ea060b-6635-4d8a-accb-f9db53c9bebf", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952860627400}, "additional": {"logType": "detail", "children": [], "durationId": "e2e6bdc2-31a4-4386-8701-2005b18fa94e"}}, {"head": {"id": "c00fddc0-1f42-411b-b0a4-9192e4ad224c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952861179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5edda47d-7ce6-4982-a143-3f47f00abe32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952861278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "311211ee-fdcd-4a9a-8a2c-562dae6aaf97", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952862046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb257c01-7ffa-4b33-836b-7816cb0aed37", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952864313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f93f9ef3-53e5-420e-97ba-55900a4bcc2f", "name": "entry : default@PreviewUpdateAssets cost memory 0.1104583740234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952864433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd92bc2e-8383-4b2d-adaa-a8008b7b1d8d", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952862037800, "endTime": 20952864530800}, "additional": {"logType": "info", "children": [], "durationId": "e2e6bdc2-31a4-4386-8701-2005b18fa94e"}}, {"head": {"id": "81d62532-dbd7-4c6f-9628-74fa0f504145", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952873672400, "endTime": 20972895847800}, "additional": {"children": ["26dd83ef-1c73-4f9c-8f38-40b9ff85a563"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5bcdb316-79c9-4216-8507-bd8e97511294", "logId": "5757663b-d77a-40cc-b545-14d61ce6761f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bcdb316-79c9-4216-8507-bd8e97511294", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952866961000}, "additional": {"logType": "detail", "children": [], "durationId": "81d62532-dbd7-4c6f-9628-74fa0f504145"}}, {"head": {"id": "dd695451-defa-404f-aca9-ff833ff140dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952867473400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe35a5d-8629-43cd-9730-8ee2709c954c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952867559900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb37b63f-5513-401a-9103-4c62d2a0b84b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952873691200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20952899116300, "endTime": 20972893242100}, "additional": {"children": ["00b7ea7e-3fd0-4483-92da-cc5c699ae03b", "23530bcd-4694-4476-b396-01581a4d35e1", "2f7b96b0-02fa-4499-a7cf-c2fcba45fe80", "b8f216bc-b670-44cc-8e90-8509b5270034", "40c2eb1c-92c3-490c-bac8-86779ce7b747"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "81d62532-dbd7-4c6f-9628-74fa0f504145", "logId": "33ec784a-1288-4b0b-b31a-f10642317038"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cb15cd4-8dbc-470f-901d-ad75627459b9", "name": "entry : default@PreviewArkTS cost memory -0.825897216796875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952901351100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a262f44-445c-4c70-9fc9-95f3853b5c33", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20959536852100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b7ea7e-3fd0-4483-92da-cc5c699ae03b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20959537913600, "endTime": 20959537931900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "5ab6a922-08c8-445e-b416-8cbe3f7e092c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ab6a922-08c8-445e-b416-8cbe3f7e092c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20959537913600, "endTime": 20959537931900}, "additional": {"logType": "info", "children": [], "durationId": "00b7ea7e-3fd0-4483-92da-cc5c699ae03b", "parent": "33ec784a-1288-4b0b-b31a-f10642317038"}}, {"head": {"id": "7b0db021-7cdb-49f6-8ac4-24f3d396a8ed", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972891613600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23530bcd-4694-4476-b396-01581a4d35e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20972892659400, "endTime": 20972892678200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "dbfd2069-f19f-48c6-9fe7-f6cfd1f12acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbfd2069-f19f-48c6-9fe7-f6cfd1f12acf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972892659400, "endTime": 20972892678200}, "additional": {"logType": "info", "children": [], "durationId": "23530bcd-4694-4476-b396-01581a4d35e1", "parent": "33ec784a-1288-4b0b-b31a-f10642317038"}}, {"head": {"id": "33ec784a-1288-4b0b-b31a-f10642317038", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20952899116300, "endTime": 20972893242100}, "additional": {"logType": "info", "children": ["5ab6a922-08c8-445e-b416-8cbe3f7e092c", "dbfd2069-f19f-48c6-9fe7-f6cfd1f12acf", "3c1007d1-a1ef-464d-a541-f4025a2a32cf", "9f5043a7-8013-49f6-9d53-e959b9fc38c9", "458749da-fe8e-45ca-8da3-4867d4cc469e"], "durationId": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "parent": "5757663b-d77a-40cc-b545-14d61ce6761f"}}, {"head": {"id": "2f7b96b0-02fa-4499-a7cf-c2fcba45fe80", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20956576322000, "endTime": 20959513971700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "3c1007d1-a1ef-464d-a541-f4025a2a32cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c1007d1-a1ef-464d-a541-f4025a2a32cf", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20956576322000, "endTime": 20959513971700}, "additional": {"logType": "info", "children": [], "durationId": "2f7b96b0-02fa-4499-a7cf-c2fcba45fe80", "parent": "33ec784a-1288-4b0b-b31a-f10642317038"}}, {"head": {"id": "b8f216bc-b670-44cc-8e90-8509b5270034", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20959514177800, "endTime": 20959514316400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "9f5043a7-8013-49f6-9d53-e959b9fc38c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f5043a7-8013-49f6-9d53-e959b9fc38c9", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20959514177800, "endTime": 20959514316400}, "additional": {"logType": "info", "children": [], "durationId": "b8f216bc-b670-44cc-8e90-8509b5270034", "parent": "33ec784a-1288-4b0b-b31a-f10642317038"}}, {"head": {"id": "40c2eb1c-92c3-490c-bac8-86779ce7b747", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker19", "startTime": 20959514440000, "endTime": 20972891715300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "26dd83ef-1c73-4f9c-8f38-40b9ff85a563", "logId": "458749da-fe8e-45ca-8da3-4867d4cc469e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "458749da-fe8e-45ca-8da3-4867d4cc469e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20959514440000, "endTime": 20972891715300}, "additional": {"logType": "info", "children": [], "durationId": "40c2eb1c-92c3-490c-bac8-86779ce7b747", "parent": "33ec784a-1288-4b0b-b31a-f10642317038"}}, {"head": {"id": "5757663b-d77a-40cc-b545-14d61ce6761f", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952873672400, "endTime": 20972895847800, "totalTime": 20022167100}, "additional": {"logType": "info", "children": ["33ec784a-1288-4b0b-b31a-f10642317038"], "durationId": "81d62532-dbd7-4c6f-9628-74fa0f504145"}}, {"head": {"id": "b6a525d0-28d4-4c23-b6ff-de900961241c", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900494400, "endTime": 20972900734000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fe6590cc-7cf2-47fb-9586-974356015d7e", "logId": "5de07623-6eba-476f-bbac-0779fcfb90a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe6590cc-7cf2-47fb-9586-974356015d7e", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900448200}, "additional": {"logType": "detail", "children": [], "durationId": "b6a525d0-28d4-4c23-b6ff-de900961241c"}}, {"head": {"id": "915eee1d-5632-4888-beac-f568f6af4f73", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900505100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707db035-4c84-4ad4-b775-3c18e0fdee2c", "name": "entry : PreviewBuild cost memory 0.01151275634765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900607200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9640b9e8-a4a6-438c-a301-869184000e82", "name": "runTaskFromQueue task cost before running: 20 s 387 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de07623-6eba-476f-bbac-0779fcfb90a5", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972900494400, "endTime": 20972900734000, "totalTime": 172600}, "additional": {"logType": "info", "children": [], "durationId": "b6a525d0-28d4-4c23-b6ff-de900961241c"}}, {"head": {"id": "952a5bba-308b-4d8a-a22a-2ca42dedade5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906009600, "endTime": 20972906159200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b19907c-223e-4ed8-8574-5d5cea272ef8", "logId": "5ad7a0ba-1a12-4b4a-b7a0-010606c7fb7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ad7a0ba-1a12-4b4a-b7a0-010606c7fb7b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906009600, "endTime": 20972906159200}, "additional": {"logType": "info", "children": [], "durationId": "952a5bba-308b-4d8a-a22a-2ca42dedade5"}}, {"head": {"id": "7bd1402b-d90b-44da-b9cc-d9261056d7c6", "name": "BUILD SUCCESSFUL in 20 s 392 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906203700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "85ebbd2e-17a2-4254-87dc-171eb78d8f53", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20952514508000, "endTime": 20972906435800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 39}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "68a9d91f-2e73-4231-937a-44aaa130cb6a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd69e79e-7093-4cf0-a24a-fad74f97072a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e292a64-829f-45e8-be1f-70d9ed1043cb", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906630500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423f108c-5c3a-4c43-8683-54f52f1190de", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f6f8b1c-32ae-4934-ac22-f5b563b953ac", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "476c84ed-f0c5-4b1d-a459-53c270320655", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906848700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cd8417-2cd9-43df-a893-1b83fe4ae0b0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda4ee13-3ca2-4ec1-b8f9-e36dac0f7bee", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972906990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ff1304-ab17-46fc-a06c-5b60cbd1d830", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972907061000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62cb4cf-1083-4ec4-831f-198ffa0adda0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972907132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8d1ec7-e424-4889-b629-2f3063fb62b1", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972910308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6fb1e4a-89ab-47f5-8ec6-7f113fae39b0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972911127100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e271c5-ab7d-40d5-8409-034d62209add", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972911415400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5e6fa4-7c21-415d-8606-d6c74a780a88", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972911688100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68331c22-4b56-4ef7-af39-1069386e9c6f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972912516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c785a4-d5cc-41ce-8648-d2418983bed7", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972925028200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c062fb-74da-4c77-b3b1-3c64ed638ccd", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972925360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fd554a-f201-43c6-b4d2-da9e659847b0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972925632500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "019f503d-d53b-49f7-9704-148fa051e864", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972925929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899fb021-1dee-46c0-ba5b-3ad5e05a2bf3", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20972926192000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}