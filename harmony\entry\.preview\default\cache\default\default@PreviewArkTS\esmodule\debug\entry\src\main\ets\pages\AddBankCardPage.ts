if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AddBankCardPage_Params {
    cardNo?: string;
    holderName?: string;
    bankName?: string;
    cardType?: string;
    isLoading?: boolean;
    userInfoLoaded?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import type { BankCardBindRequest } from '../common/types/index';
import { tempDataManager, TempDataKeys } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
import type { FormData } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
class AddBankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cardNo = new ObservedPropertySimplePU('', this, "cardNo");
        this.__holderName = new ObservedPropertySimplePU('', this, "holderName");
        this.__bankName = new ObservedPropertySimplePU('', this, "bankName");
        this.__cardType = new ObservedPropertySimplePU('储蓄卡', this, "cardType");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__userInfoLoaded = new ObservedPropertySimplePU(false, this, "userInfoLoaded");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AddBankCardPage_Params) {
        if (params.cardNo !== undefined) {
            this.cardNo = params.cardNo;
        }
        if (params.holderName !== undefined) {
            this.holderName = params.holderName;
        }
        if (params.bankName !== undefined) {
            this.bankName = params.bankName;
        }
        if (params.cardType !== undefined) {
            this.cardType = params.cardType;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.userInfoLoaded !== undefined) {
            this.userInfoLoaded = params.userInfoLoaded;
        }
    }
    updateStateVars(params: AddBankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardNo.purgeDependencyOnElmtId(rmElmtId);
        this.__holderName.purgeDependencyOnElmtId(rmElmtId);
        this.__bankName.purgeDependencyOnElmtId(rmElmtId);
        this.__cardType.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__userInfoLoaded.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardNo.aboutToBeDeleted();
        this.__holderName.aboutToBeDeleted();
        this.__bankName.aboutToBeDeleted();
        this.__cardType.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__userInfoLoaded.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cardNo: ObservedPropertySimplePU<string>;
    get cardNo() {
        return this.__cardNo.get();
    }
    set cardNo(newValue: string) {
        this.__cardNo.set(newValue);
    }
    private __holderName: ObservedPropertySimplePU<string>;
    get holderName() {
        return this.__holderName.get();
    }
    set holderName(newValue: string) {
        this.__holderName.set(newValue);
    }
    private __bankName: ObservedPropertySimplePU<string>;
    get bankName() {
        return this.__bankName.get();
    }
    set bankName(newValue: string) {
        this.__bankName.set(newValue);
    }
    private __cardType: ObservedPropertySimplePU<string>;
    get cardType() {
        return this.__cardType.get();
    }
    set cardType(newValue: string) {
        this.__cardType.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __userInfoLoaded: ObservedPropertySimplePU<boolean>; // 用于强制界面更新
    get userInfoLoaded() {
        return this.__userInfoLoaded.get();
    }
    set userInfoLoaded(newValue: boolean) {
        this.__userInfoLoaded.set(newValue);
    }
    aboutToAppear() {
        // 获取路由参数（用于初始化）
        const params = router.getParams() as Record<string, string>;
        if (params?.selectedBank) {
            this.bankName = params.selectedBank;
        }
        if (params?.selectedType) {
            this.cardType = params.selectedType;
        }
        // 加载用户信息，设置持卡人姓名
        this.loadUserInfo();
    }
    onPageShow() {
        this.handlePageParams();
    }
    /**
     * 获取持卡人姓名显示文本
     */
    getHolderNameDisplay(): string {
        console.log('getHolderNameDisplay 被调用，holderName:', this.holderName, 'userInfoLoaded:', this.userInfoLoaded);
        if (!this.userInfoLoaded) {
            return '加载中...';
        }
        if (this.holderName && this.holderName.length > 0) {
            return this.holderName;
        }
        return '未知用户';
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            const userInfo = await storageManager.getUserInfo();
            if (userInfo && userInfo.realName) {
                this.holderName = userInfo.realName;
                this.userInfoLoaded = true;
                console.log('用户信息加载成功，姓名:', this.holderName);
            }
            else {
                this.holderName = '未设置真实姓名';
                this.userInfoLoaded = true;
                console.log('用户信息中没有真实姓名');
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            this.holderName = '加载失败';
            this.userInfoLoaded = true;
        }
    }
    private handlePageParams() {
        // 检查是否有选择器返回的数据
        const selectedBank: string | null = tempDataManager.getAndRemoveData(TempDataKeys.SELECTED_BANK) as string | null;
        const selectedCardType: string | null = tempDataManager.getAndRemoveData(TempDataKeys.SELECTED_CARD_TYPE) as string | null;
        // 恢复表单数据
        const formData: FormData | null = tempDataManager.getData(TempDataKeys.ADD_BANK_CARD_FORM) as FormData | null;
        if (formData) {
            this.cardNo = formData.cardNo || '';
            // 不恢复持卡人姓名，保持用户真实姓名
            // this.holderName = formData.holderName || '';
            // 只有在没有新选择的情况下才恢复原来的值
            if (!selectedBank) {
                this.bankName = formData.bankName || '';
            }
            if (!selectedCardType) {
                this.cardType = formData.cardType || '储蓄卡';
            }
        }
        // 更新选择的银行（优先级最高）
        if (selectedBank) {
            this.bankName = selectedBank;
        }
        // 更新选择的卡片类型（优先级最高）
        if (selectedCardType) {
            this.cardType = selectedCardType;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(115:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(117:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(118:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(119:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(132:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(139:9)", "entry");
            Row.width(40);
            Row.height(40);
        }, Row);
        Row.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(144:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(145:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡信息表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(147:11)", "entry");
            // 银行卡信息表单
            Column.width('100%');
            // 银行卡信息表单
            Column.padding({ left: 24, right: 24, top: 20, bottom: 40 });
        }, Column);
        // 银行卡号
        this.InputField.bind(this)('银行卡号', '请输入银行卡号', this.cardNo, (value: string) => {
            this.cardNo = value;
        }, InputType.Number, 19);
        // 持卡人姓名（可输入）
        this.InputField.bind(this)('持卡人姓名', '请输入持卡人姓名', this.holderName, (value: string) => {
            this.holderName = value;
        }, InputType.Normal, 20);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行名称选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(159:13)", "entry");
            // 银行名称选择
            Column.alignItems(HorizontalAlign.Start);
            // 银行名称选择
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('开户银行');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(160:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(166:15)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor('#F8F9FA');
            Row.borderRadius(8);
            Row.border({ width: 1, color: '#E0E0E0' });
            Row.onClick(() => {
                // 简单的银行选择逻辑
                if (this.bankName === '中国银行') {
                    this.bankName = '工商银行';
                }
                else if (this.bankName === '工商银行') {
                    this.bankName = '建设银行';
                }
                else if (this.bankName === '建设银行') {
                    this.bankName = '农业银行';
                }
                else {
                    this.bankName = '中国银行';
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.bankName && this.bankName.length > 0 ? this.bankName : '请选择开户银行');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(167:17)", "entry");
            Text.fontSize(16);
            Text.fontColor((this.bankName && this.bankName.length > 0 ? this.bankName : '请选择开户银行').includes('请选择') ? '#999999' : '#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(172:17)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#999999');
        }, Image);
        Row.pop();
        // 银行名称选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片类型选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(200:13)", "entry");
            // 卡片类型选择
            Column.alignItems(HorizontalAlign.Start);
            // 卡片类型选择
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡片类型');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(201:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(207:15)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor('#F8F9FA');
            Row.borderRadius(8);
            Row.border({ width: 1, color: '#E0E0E0' });
            Row.onClick(() => {
                // 简单的卡片类型切换
                this.cardType = this.cardType === '储蓄卡' ? '信用卡' : '储蓄卡';
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardType);
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(208:17)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(213:17)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#999999');
        }, Image);
        Row.pop();
        // 卡片类型选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加按钮
            Button.createWithLabel('添加银行卡');
            Button.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(233:13)", "entry");
            // 添加按钮
            Button.width('100%');
            // 添加按钮
            Button.height(48);
            // 添加按钮
            Button.fontSize(16);
            // 添加按钮
            Button.fontColor(Color.White);
            // 添加按钮
            Button.backgroundColor('#1976D2');
            // 添加按钮
            Button.borderRadius(8);
            // 添加按钮
            Button.margin({ top: 30 });
            // 添加按钮
            Button.enabled(!this.isLoading && this.isFormValid());
            // 添加按钮
            Button.opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5);
            // 添加按钮
            Button.onClick(() => {
                this.handleAddCard();
            });
        }, Button);
        // 添加按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 安全提示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(248:13)", "entry");
            // 安全提示
            Column.width('100%');
            // 安全提示
            Column.padding(16);
            // 安全提示
            Column.backgroundColor('#F8F9FA');
            // 安全提示
            Column.borderRadius(8);
            // 安全提示
            Column.margin({ top: 20 });
            // 安全提示
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全提示');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(249:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 请确保银行卡信息准确无误\n• 仅支持本人实名银行卡\n• 银行卡信息将加密保存');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(256:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.lineHeight(18);
        }, Text);
        Text.pop();
        // 安全提示
        Column.pop();
        // 银行卡信息表单
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    InputField(label: string, placeholder: string, value: string, onChange: (value: string) => void, inputType: InputType = InputType.Normal, maxLength?: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(290:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(291:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder, text: value });
            TextInput.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(297:7)", "entry");
            TextInput.type(inputType);
            TextInput.maxLength(maxLength);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    ReadOnlyField(label: string, value: string, hint?: string, forceUpdate?: boolean, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(313:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(314:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(320:7)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor('#F5F5F5');
            Row.borderRadius(8);
            Row.border({ width: 1, color: '#E0E0E0' });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(321:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔒');
            Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(326:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (hint) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(hint);
                        Text.debugLine("entry/src/main/ets/pages/AddBankCardPage.ets(338:9)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999999');
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    /**
     * 表单验证
     */
    isFormValid(): boolean {
        return this.cardNo.length >= 16 &&
            this.holderName.length >= 2 &&
            this.bankName.length > 0 &&
            this.cardType.length > 0;
    }
    /**
     * 处理添加银行卡
     */
    async handleAddCard() {
        if (this.isLoading)
            return;
        // 表单验证
        if (!this.validateForm())
            return;
        this.isLoading = true;
        try {
            const cardData: BankCardBindRequest = {
                userId: 1,
                cardNumber: this.cardNo,
                cardType: this.cardType,
                bankName: this.bankName,
                balance: 0,
                creditLimit: 0
            };
            await BankCardApi.bindCard(cardData);
            promptAction.showToast({ message: '添加银行卡成功' });
            // 通知银行卡列表页面刷新
            console.log('AddBankCardPage - 设置银行卡添加事件标志');
            tempDataManager.setData('BANK_CARD_ADDED', true);
            // 返回银行卡列表页
            router.back();
        }
        catch (error) {
            console.error('添加银行卡失败:', error);
            promptAction.showToast({ message: '添加银行卡失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 表单验证
     */
    validateForm(): boolean {
        if (this.cardNo.length < 16) {
            promptAction.showToast({ message: '请输入正确的银行卡号' });
            return false;
        }
        if (this.holderName.length < 2) {
            promptAction.showToast({ message: '用户信息加载失败，请重新进入页面' });
            return false;
        }
        if (this.bankName.length === 0) {
            promptAction.showToast({ message: '请选择开户银行' });
            return false;
        }
        return true;
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AddBankCardPage";
    }
}
registerNamedRoute(() => new AddBankCardPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/AddBankCardPage", pageFullPath: "entry/src/main/ets/pages/AddBankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
