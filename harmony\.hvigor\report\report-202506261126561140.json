{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "600ae18e-38ec-4bf0-9a16-a64102c2ed0c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315054423899300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54957524-75d1-4cf0-bf14-2bf80a69d93f", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315150442203600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460ff81e-6808-46fe-8654-a344abf936db", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315150442632700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0c5232-a2b9-4e0e-9cf4-866a7c915e58", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582418478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582430116200, "endTime": 315582657999400}, "additional": {"children": ["2b3750ab-40dd-4b5b-9064-a3b96c600ce7", "dbabd0db-de89-4e1d-a034-20f62335f359", "d4b8798c-b1ad-4142-99ec-16944e6b3de0", "da38eb41-6cf6-43cc-b35f-c75d50b3ee85", "bf3ec43d-bf48-40b2-9aa9-7d5902c139c7", "fca7de1c-558c-4983-9e80-6ab21cde9715", "98351eff-462d-40be-8656-6d71a7463057"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b3750ab-40dd-4b5b-9064-a3b96c600ce7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582430118200, "endTime": 315582447461100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "ca00692e-d731-4d96-bcc4-298b6352f49b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbabd0db-de89-4e1d-a034-20f62335f359", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582447478500, "endTime": 315582655983400}, "additional": {"children": ["a742ec92-f79e-421e-9e3b-ceb77a30f034", "73be244b-4a28-4a31-a3a7-4d65c211a099", "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "8b978a8f-ae7c-4042-91bd-e44523590387", "5893587d-8b1a-4511-a585-97d63d0a3897", "d7cb6aee-3a93-43f1-9a69-f324d94745f2", "ea5bd358-c3c3-4167-8f31-7f0474d9cce7", "0e59b6d1-7483-4f2a-a5b8-1175a54a17c5", "b1326200-93bb-4e42-9de0-c40c06fee8c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "569c760f-659e-48f9-9521-115911b978b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4b8798c-b1ad-4142-99ec-16944e6b3de0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582656026900, "endTime": 315582657975400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "f8252a7e-3892-449b-b5f8-575f1d721458"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da38eb41-6cf6-43cc-b35f-c75d50b3ee85", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582657984500, "endTime": 315582657991900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "94908f33-e38c-4766-af7b-6439dde012b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf3ec43d-bf48-40b2-9aa9-7d5902c139c7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582436013300, "endTime": 315582436058600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "53c70f5a-7305-43f6-ab8f-9ef4e5d5553f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53c70f5a-7305-43f6-ab8f-9ef4e5d5553f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582436013300, "endTime": 315582436058600}, "additional": {"logType": "info", "children": [], "durationId": "bf3ec43d-bf48-40b2-9aa9-7d5902c139c7", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "fca7de1c-558c-4983-9e80-6ab21cde9715", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582443014400, "endTime": 315582443036300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "97ebe05e-3541-4e30-879b-998610c4b287"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97ebe05e-3541-4e30-879b-998610c4b287", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582443014400, "endTime": 315582443036300}, "additional": {"logType": "info", "children": [], "durationId": "fca7de1c-558c-4983-9e80-6ab21cde9715", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "087c70c4-da3a-46e5-bddf-a2c14ff51bbb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582443095100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6764ccb7-9966-4c60-ba30-3a9cb912e70b", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582447312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca00692e-d731-4d96-bcc4-298b6352f49b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582430118200, "endTime": 315582447461100}, "additional": {"logType": "info", "children": [], "durationId": "2b3750ab-40dd-4b5b-9064-a3b96c600ce7", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "a742ec92-f79e-421e-9e3b-ceb77a30f034", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582456069500, "endTime": 315582456082600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "ab1f6f69-ef83-45a0-90dc-ed03323fd44d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73be244b-4a28-4a31-a3a7-4d65c211a099", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582456100200, "endTime": 315582461397500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "eb1dfb05-2d5a-4c67-845c-2d440b5422ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582461411400, "endTime": 315582567928100}, "additional": {"children": ["41555764-a834-4e44-ae2d-06b9bd94eb9f", "acb2ca8e-6b43-4639-b420-402fc44220ce", "c0298f41-d421-4706-bcc5-9c6612b59d34"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "2e9918b3-abeb-40f0-9ab7-091681c36203"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b978a8f-ae7c-4042-91bd-e44523590387", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582567946300, "endTime": 315582596389700}, "additional": {"children": ["11fb0846-b013-430c-90c5-04e4f153d521"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "6907584e-3772-493c-a1db-8ffe3078ef86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5893587d-8b1a-4511-a585-97d63d0a3897", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582596409600, "endTime": 315582621352400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "5add1112-88f3-43a7-8c9a-fb5325421fd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7cb6aee-3a93-43f1-9a69-f324d94745f2", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582622496600, "endTime": 315582632085300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "e026eb9a-0198-4e7e-b0fc-09fb499b903a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea5bd358-c3c3-4167-8f31-7f0474d9cce7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582632121800, "endTime": 315582655744800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "bfc1bab3-6ba0-4902-ad6a-74b6bf704e3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e59b6d1-7483-4f2a-a5b8-1175a54a17c5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582655793200, "endTime": 315582655965600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "9047c310-c03d-414b-86c6-3855f6988550"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab1f6f69-ef83-45a0-90dc-ed03323fd44d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582456069500, "endTime": 315582456082600}, "additional": {"logType": "info", "children": [], "durationId": "a742ec92-f79e-421e-9e3b-ceb77a30f034", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "eb1dfb05-2d5a-4c67-845c-2d440b5422ac", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582456100200, "endTime": 315582461397500}, "additional": {"logType": "info", "children": [], "durationId": "73be244b-4a28-4a31-a3a7-4d65c211a099", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "41555764-a834-4e44-ae2d-06b9bd94eb9f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582462194400, "endTime": 315582462216900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "logId": "0bab661e-4fdd-449c-b193-4473bbdaa3f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bab661e-4fdd-449c-b193-4473bbdaa3f9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582462194400, "endTime": 315582462216900}, "additional": {"logType": "info", "children": [], "durationId": "41555764-a834-4e44-ae2d-06b9bd94eb9f", "parent": "2e9918b3-abeb-40f0-9ab7-091681c36203"}}, {"head": {"id": "acb2ca8e-6b43-4639-b420-402fc44220ce", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582465417400, "endTime": 315582566914700}, "additional": {"children": ["24bf9871-afb0-4f1a-ac9d-33f7aafcfe26", "053ac59d-b9af-42fe-b0ef-3bf117e3f7f2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "logId": "9d02248e-6a94-4d6e-924a-368787e5c137"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24bf9871-afb0-4f1a-ac9d-33f7aafcfe26", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582465427400, "endTime": 315582471918900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acb2ca8e-6b43-4639-b420-402fc44220ce", "logId": "7de02b3f-54a8-45ee-918d-6920ce447bdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "053ac59d-b9af-42fe-b0ef-3bf117e3f7f2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582471939900, "endTime": 315582566899600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acb2ca8e-6b43-4639-b420-402fc44220ce", "logId": "a2a14959-e8ba-4917-bada-220743fd3473"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "126946ab-e326-4116-9acf-2b1ba75d525e", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582465436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0690b524-b58e-4906-afb3-de3caffb429e", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582471776000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7de02b3f-54a8-45ee-918d-6920ce447bdc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582465427400, "endTime": 315582471918900}, "additional": {"logType": "info", "children": [], "durationId": "24bf9871-afb0-4f1a-ac9d-33f7aafcfe26", "parent": "9d02248e-6a94-4d6e-924a-368787e5c137"}}, {"head": {"id": "d05d60af-a696-4b91-a229-fa9ee963b458", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582471954600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726a6d4f-4cc0-4a31-9c3e-f2f674bcfd03", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582479062500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7281c08c-5af4-4d07-9f29-663b0ad8e9ae", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582479972200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e1ef1c-a1a5-4b6f-9384-80feb61aa651", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582480146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a96f27-a174-4b7c-aa57-ae04073c8453", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582480252100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1efefeef-d04a-4acc-8151-b4a310681aa4", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582482273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ef4c2e-b12d-4eee-a2ff-f204e0c06b68", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582487239600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2937e5b-33cc-4274-9f12-943314cd4b19", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582499992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386c9563-6a82-45d5-b6ff-3ba4bc97fd79", "name": "Sdk init in 51 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582539468800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61f2c28-8ffb-4328-ad0d-ad1508773379", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582539618200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 26}, "markType": "other"}}, {"head": {"id": "833036d4-c065-4ec3-8baa-a646afee4ab5", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582539630700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 26}, "markType": "other"}}, {"head": {"id": "73c30ccb-1a16-4743-a92b-5249eb0a0e69", "name": "Project task initialization takes 26 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582566567400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0f9eee-2423-42c1-b302-70b29c39cedd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582566708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78ea7c9-8f5e-4b47-b523-96862ee075e7", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582566783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f58429-1310-44ed-8a0d-a6e8769c3268", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582566841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a14959-e8ba-4917-bada-220743fd3473", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582471939900, "endTime": 315582566899600}, "additional": {"logType": "info", "children": [], "durationId": "053ac59d-b9af-42fe-b0ef-3bf117e3f7f2", "parent": "9d02248e-6a94-4d6e-924a-368787e5c137"}}, {"head": {"id": "9d02248e-6a94-4d6e-924a-368787e5c137", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582465417400, "endTime": 315582566914700}, "additional": {"logType": "info", "children": ["7de02b3f-54a8-45ee-918d-6920ce447bdc", "a2a14959-e8ba-4917-bada-220743fd3473"], "durationId": "acb2ca8e-6b43-4639-b420-402fc44220ce", "parent": "2e9918b3-abeb-40f0-9ab7-091681c36203"}}, {"head": {"id": "c0298f41-d421-4706-bcc5-9c6612b59d34", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582567890500, "endTime": 315582567908000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "logId": "29354422-566d-4b8c-9b8a-b6528734416d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29354422-566d-4b8c-9b8a-b6528734416d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582567890500, "endTime": 315582567908000}, "additional": {"logType": "info", "children": [], "durationId": "c0298f41-d421-4706-bcc5-9c6612b59d34", "parent": "2e9918b3-abeb-40f0-9ab7-091681c36203"}}, {"head": {"id": "2e9918b3-abeb-40f0-9ab7-091681c36203", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582461411400, "endTime": 315582567928100}, "additional": {"logType": "info", "children": ["0bab661e-4fdd-449c-b193-4473bbdaa3f9", "9d02248e-6a94-4d6e-924a-368787e5c137", "29354422-566d-4b8c-9b8a-b6528734416d"], "durationId": "a28bf3ea-8fa6-4ced-89ca-c8ce03ddce29", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "11fb0846-b013-430c-90c5-04e4f153d521", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582568724400, "endTime": 315582595671100}, "additional": {"children": ["d4f37318-d435-4582-ba4a-62a38c2d7b60", "7e864964-69bf-4431-b2ee-61f339ee59ca", "d342ddc2-d3e4-41b1-8a1d-faf40ddfe947"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b978a8f-ae7c-4042-91bd-e44523590387", "logId": "10b81c4b-8496-4635-8312-347f7c9033dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4f37318-d435-4582-ba4a-62a38c2d7b60", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582573311300, "endTime": 315582573328600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11fb0846-b013-430c-90c5-04e4f153d521", "logId": "05af63fc-e414-4178-948e-4ebdc525c672"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05af63fc-e414-4178-948e-4ebdc525c672", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582573311300, "endTime": 315582573328600}, "additional": {"logType": "info", "children": [], "durationId": "d4f37318-d435-4582-ba4a-62a38c2d7b60", "parent": "10b81c4b-8496-4635-8312-347f7c9033dc"}}, {"head": {"id": "7e864964-69bf-4431-b2ee-61f339ee59ca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582575544600, "endTime": 315582593706200}, "additional": {"children": ["29cc9e2b-8f39-4799-aeba-744382d6a879", "402328aa-7924-4f77-a457-f19fab89b473"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11fb0846-b013-430c-90c5-04e4f153d521", "logId": "406dcc0a-17cf-466a-b2a6-3d3c3e5c0df7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29cc9e2b-8f39-4799-aeba-744382d6a879", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582575545700, "endTime": 315582579190100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e864964-69bf-4431-b2ee-61f339ee59ca", "logId": "e4482d13-98b9-4a99-9391-446376d5ed77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "402328aa-7924-4f77-a457-f19fab89b473", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582579236000, "endTime": 315582593687900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e864964-69bf-4431-b2ee-61f339ee59ca", "logId": "7f327348-cbf8-47de-b1cb-83268449b0ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48fa5b4f-6964-4741-8a48-fbde5e22f955", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582575551400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "228155b4-b2d5-41ba-bcb1-1fd9e65c5fca", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582579029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4482d13-98b9-4a99-9391-446376d5ed77", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582575545700, "endTime": 315582579190100}, "additional": {"logType": "info", "children": [], "durationId": "29cc9e2b-8f39-4799-aeba-744382d6a879", "parent": "406dcc0a-17cf-466a-b2a6-3d3c3e5c0df7"}}, {"head": {"id": "b9807382-8e52-45da-a639-2e5c69a1dfce", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582579277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3839ada-0200-4929-aba3-2b50af0fe3fd", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582588403000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ead3f2-b229-49a9-8332-023d123df4e7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582588560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feb614bb-faa5-4fe7-be27-09393897a701", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582588787300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07282cd4-ec76-4340-8de6-3dd48cc97b77", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582588948200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1897888-1943-4ba7-a24c-a34e47d49c7a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582589025100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc15ef4a-4b8f-43ec-af1b-0bf74200b93e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582589086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f463e1ff-07de-4356-9328-1e5bb3f85b94", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582589157900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66df90d-b124-4398-80a7-353120658702", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582593259900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a82210-cc8b-4d50-9e6d-a36e41d717bb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582593435900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d55604d-6f9a-4dfe-9c0f-b0950fbc3c62", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582593536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba33ac1-4d12-4061-9600-4dcb3994e827", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582593604700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f327348-cbf8-47de-b1cb-83268449b0ff", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582579236000, "endTime": 315582593687900}, "additional": {"logType": "info", "children": [], "durationId": "402328aa-7924-4f77-a457-f19fab89b473", "parent": "406dcc0a-17cf-466a-b2a6-3d3c3e5c0df7"}}, {"head": {"id": "406dcc0a-17cf-466a-b2a6-3d3c3e5c0df7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582575544600, "endTime": 315582593706200}, "additional": {"logType": "info", "children": ["e4482d13-98b9-4a99-9391-446376d5ed77", "7f327348-cbf8-47de-b1cb-83268449b0ff"], "durationId": "7e864964-69bf-4431-b2ee-61f339ee59ca", "parent": "10b81c4b-8496-4635-8312-347f7c9033dc"}}, {"head": {"id": "d342ddc2-d3e4-41b1-8a1d-faf40ddfe947", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582595525700, "endTime": 315582595540400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11fb0846-b013-430c-90c5-04e4f153d521", "logId": "d8c1474b-17f8-483e-a4bb-f9b11405098b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8c1474b-17f8-483e-a4bb-f9b11405098b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582595525700, "endTime": 315582595540400}, "additional": {"logType": "info", "children": [], "durationId": "d342ddc2-d3e4-41b1-8a1d-faf40ddfe947", "parent": "10b81c4b-8496-4635-8312-347f7c9033dc"}}, {"head": {"id": "10b81c4b-8496-4635-8312-347f7c9033dc", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582568724400, "endTime": 315582595671100}, "additional": {"logType": "info", "children": ["05af63fc-e414-4178-948e-4ebdc525c672", "406dcc0a-17cf-466a-b2a6-3d3c3e5c0df7", "d8c1474b-17f8-483e-a4bb-f9b11405098b"], "durationId": "11fb0846-b013-430c-90c5-04e4f153d521", "parent": "6907584e-3772-493c-a1db-8ffe3078ef86"}}, {"head": {"id": "6907584e-3772-493c-a1db-8ffe3078ef86", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582567946300, "endTime": 315582596389700}, "additional": {"logType": "info", "children": ["10b81c4b-8496-4635-8312-347f7c9033dc"], "durationId": "8b978a8f-ae7c-4042-91bd-e44523590387", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "d013bfa2-9966-45e5-8bf2-b0e9f1a5b4a0", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582620991700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb5a8ec-ae0b-4729-bf30-b2c95183e0c2", "name": "hvigorfile, resolve hvigorfile dependencies in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582621277700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5add1112-88f3-43a7-8c9a-fb5325421fd1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582596409600, "endTime": 315582621352400}, "additional": {"logType": "info", "children": [], "durationId": "5893587d-8b1a-4511-a585-97d63d0a3897", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "b1326200-93bb-4e42-9de0-c40c06fee8c5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582622255500, "endTime": 315582622480700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dbabd0db-de89-4e1d-a034-20f62335f359", "logId": "66f9f37b-118f-4de1-a8f1-fabf8b3269bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75952c45-acd8-405e-accc-154f8f2d4ce2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582622285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f9f37b-118f-4de1-a8f1-fabf8b3269bb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582622255500, "endTime": 315582622480700}, "additional": {"logType": "info", "children": [], "durationId": "b1326200-93bb-4e42-9de0-c40c06fee8c5", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "a7dee8f5-47f5-486a-b1b1-cb029a380f7d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582624322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9c35e33-2fde-48fa-81fa-df8159e2199b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582630883000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e026eb9a-0198-4e7e-b0fc-09fb499b903a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582622496600, "endTime": 315582632085300}, "additional": {"logType": "info", "children": [], "durationId": "d7cb6aee-3a93-43f1-9a69-f324d94745f2", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "d567ad72-75ab-46fc-b17a-f899687f9473", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582632146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0032c15e-ec4b-4397-a096-520841562455", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582648300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dfdb890-6f5a-49cc-aa19-0a54bd31a611", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582648428400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f26f0d-8776-47a4-8ad1-20d1c11ae4a5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582648653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e892121-6118-4da6-90b9-de6e95ceb540", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582651455300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0705e88-299e-4f97-be1b-7c2bb2253530", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582651579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc1bab3-6ba0-4902-ad6a-74b6bf704e3e", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582632121800, "endTime": 315582655744800}, "additional": {"logType": "info", "children": [], "durationId": "ea5bd358-c3c3-4167-8f31-7f0474d9cce7", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "533da995-aab9-4235-9958-3273a7edd921", "name": "Configuration phase cost:200 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582655827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9047c310-c03d-414b-86c6-3855f6988550", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582655793200, "endTime": 315582655965600}, "additional": {"logType": "info", "children": [], "durationId": "0e59b6d1-7483-4f2a-a5b8-1175a54a17c5", "parent": "569c760f-659e-48f9-9521-115911b978b8"}}, {"head": {"id": "569c760f-659e-48f9-9521-115911b978b8", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582447478500, "endTime": 315582655983400}, "additional": {"logType": "info", "children": ["ab1f6f69-ef83-45a0-90dc-ed03323fd44d", "eb1dfb05-2d5a-4c67-845c-2d440b5422ac", "2e9918b3-abeb-40f0-9ab7-091681c36203", "6907584e-3772-493c-a1db-8ffe3078ef86", "5add1112-88f3-43a7-8c9a-fb5325421fd1", "e026eb9a-0198-4e7e-b0fc-09fb499b903a", "bfc1bab3-6ba0-4902-ad6a-74b6bf704e3e", "9047c310-c03d-414b-86c6-3855f6988550", "66f9f37b-118f-4de1-a8f1-fabf8b3269bb"], "durationId": "dbabd0db-de89-4e1d-a034-20f62335f359", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "98351eff-462d-40be-8656-6d71a7463057", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582657942500, "endTime": 315582657959200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079", "logId": "bf316a4a-9109-4c6b-ade5-cfae4280b336"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf316a4a-9109-4c6b-ade5-cfae4280b336", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582657942500, "endTime": 315582657959200}, "additional": {"logType": "info", "children": [], "durationId": "98351eff-462d-40be-8656-6d71a7463057", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "f8252a7e-3892-449b-b5f8-575f1d721458", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582656026900, "endTime": 315582657975400}, "additional": {"logType": "info", "children": [], "durationId": "d4b8798c-b1ad-4142-99ec-16944e6b3de0", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "94908f33-e38c-4766-af7b-6439dde012b0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582657984500, "endTime": 315582657991900}, "additional": {"logType": "info", "children": [], "durationId": "da38eb41-6cf6-43cc-b35f-c75d50b3ee85", "parent": "018e44fe-2550-49c8-a4a3-b6180e936bf6"}}, {"head": {"id": "018e44fe-2550-49c8-a4a3-b6180e936bf6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582430116200, "endTime": 315582657999400}, "additional": {"logType": "info", "children": ["ca00692e-d731-4d96-bcc4-298b6352f49b", "569c760f-659e-48f9-9521-115911b978b8", "f8252a7e-3892-449b-b5f8-575f1d721458", "94908f33-e38c-4766-af7b-6439dde012b0", "53c70f5a-7305-43f6-ab8f-9ef4e5d5553f", "97ebe05e-3541-4e30-879b-998610c4b287", "bf316a4a-9109-4c6b-ade5-cfae4280b336"], "durationId": "7b735eb9-e4b4-46cc-b4cd-102f6d1d6079"}}, {"head": {"id": "cb8c89ce-69d6-42d7-a85b-0453453b7f6d", "name": "Configuration task cost before running: 234 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582658131700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266c9686-e833-4ab3-b8c3-9a4e569c1225", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582665875300, "endTime": 315582678601400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d9a6bdc9-efd6-466b-b45f-de7e86c23dee", "logId": "c220c157-2940-40ca-8925-961a077b1ef5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9a6bdc9-efd6-466b-b45f-de7e86c23dee", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582660298300}, "additional": {"logType": "detail", "children": [], "durationId": "266c9686-e833-4ab3-b8c3-9a4e569c1225"}}, {"head": {"id": "39421d9d-ce92-4b5d-9828-e753cf780142", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582661042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "285d6ea9-6982-43c0-ae9b-3855561020a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582661195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280e4572-a3c6-4a00-9218-11c5df0b4174", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582665900500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a34e2da-0ef6-4086-be57-0a2d7da5202a", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582678354500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f75305-888c-4d1f-a739-8105a55eff03", "name": "entry : default@PreBuild cost memory -1.5355606079101562", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582678516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c220c157-2940-40ca-8925-961a077b1ef5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582665875300, "endTime": 315582678601400}, "additional": {"logType": "info", "children": [], "durationId": "266c9686-e833-4ab3-b8c3-9a4e569c1225"}}, {"head": {"id": "d707dbe2-ea91-48f5-843b-3b22bff6778b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582685100200, "endTime": 315582687681900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "868a8440-5c11-46b8-a917-e9835a102501", "logId": "bf1b8d4f-4937-46ee-9341-7ea9d52de569"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "868a8440-5c11-46b8-a917-e9835a102501", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582683172800}, "additional": {"logType": "detail", "children": [], "durationId": "d707dbe2-ea91-48f5-843b-3b22bff6778b"}}, {"head": {"id": "c77286d9-f3aa-4795-9583-4ec00277bb3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582683908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ead8c54b-ae76-4a48-9dbd-361cb5d16dd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582684075200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f693977-ed37-4247-8a5d-f93125d8a33e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582685112700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "641c212d-8cdd-46b4-b167-8d3dd934b391", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582687428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6560c437-bf9f-42eb-a5ad-af794c5964e7", "name": "entry : default@MergeProfile cost memory 0.1116180419921875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582687600100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1b8d4f-4937-46ee-9341-7ea9d52de569", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582685100200, "endTime": 315582687681900}, "additional": {"logType": "info", "children": [], "durationId": "d707dbe2-ea91-48f5-843b-3b22bff6778b"}}, {"head": {"id": "1acfcf34-a939-4273-91e1-29626113a23a", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582692423900, "endTime": 315582696435000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7b97af7f-9d31-4a2b-a3b5-ebab8a2e0844", "logId": "975451bd-17e5-49de-8a99-d15bf24ba619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b97af7f-9d31-4a2b-a3b5-ebab8a2e0844", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582690040500}, "additional": {"logType": "detail", "children": [], "durationId": "1acfcf34-a939-4273-91e1-29626113a23a"}}, {"head": {"id": "339f8821-38c3-44c9-9740-c7ee7fe10c72", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582690732200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac811d6-8fb6-4755-95cd-c0a1475eede2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582690871400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b08ece4-ee52-4e4f-ad7e-ea1198977c8a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582692444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6320e7-c85e-4052-a72a-6275d2c6e08a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582693928200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2314f916-99d3-4da0-9194-37d4b28fdb37", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582696119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4436aae-8d97-4e25-94f3-ef2096548557", "name": "entry : default@CreateBuildProfile cost memory 0.0985107421875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582696308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975451bd-17e5-49de-8a99-d15bf24ba619", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582692423900, "endTime": 315582696435000}, "additional": {"logType": "info", "children": [], "durationId": "1acfcf34-a939-4273-91e1-29626113a23a"}}, {"head": {"id": "8ed1ea0e-6eb7-4b6b-a3b0-2f69aae3ba11", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582701798100, "endTime": 315582702491200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a1715648-901c-4378-99d7-ba06cd13d5de", "logId": "0d3b74e2-4b47-403c-a011-c5a3c5e93f99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1715648-901c-4378-99d7-ba06cd13d5de", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582699390200}, "additional": {"logType": "detail", "children": [], "durationId": "8ed1ea0e-6eb7-4b6b-a3b0-2f69aae3ba11"}}, {"head": {"id": "c2c037bf-8437-4598-b2ba-094dd865db02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582700322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bb4b22-e59a-4988-9efa-9b488527a6fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582700496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51cd42c-c00d-497d-93d4-40bc5ecf54e9", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582701816300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252586d6-db1b-4ba9-8c0f-ea6e24aa821d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582702000600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0858db1c-07f1-4884-96ec-cd265efef1ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582702114300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5a3543-018d-4648-9990-f7657b4c7f65", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582702246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da2d859-5141-4b5d-b32c-37d3a0ce1fbc", "name": "runTaskFromQueue task cost before running: 278 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582702368300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3b74e2-4b47-403c-a011-c5a3c5e93f99", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582701798100, "endTime": 315582702491200, "totalTime": 540800}, "additional": {"logType": "info", "children": [], "durationId": "8ed1ea0e-6eb7-4b6b-a3b0-2f69aae3ba11"}}, {"head": {"id": "cb5e4253-a09b-47f3-8173-63f371f4d1e2", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582713013100, "endTime": 315582714281600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a640c8eb-82c9-47cb-9042-d6d40794322c", "logId": "8f0d2669-a323-49d1-85e2-12bf2534bf3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a640c8eb-82c9-47cb-9042-d6d40794322c", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582704788000}, "additional": {"logType": "detail", "children": [], "durationId": "cb5e4253-a09b-47f3-8173-63f371f4d1e2"}}, {"head": {"id": "af0b6432-5ef2-4510-b946-78e188e0679a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582705348500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "565df920-ce1a-44a3-97bc-cae62ecdf3e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582705453900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4eddd56-95e3-42da-b6a9-a06629851e00", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582713030700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46aebe2-0389-4ab7-bb40-157f35b9ac6c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582713293900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691baefa-b974-4d15-88d6-77c3c9b1002c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582714048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50f3fabe-be17-4e18-8d26-676983681a4b", "name": "entry : default@GeneratePkgContextInfo cost memory 0.066070556640625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582714173000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0d2669-a323-49d1-85e2-12bf2534bf3b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582713013100, "endTime": 315582714281600}, "additional": {"logType": "info", "children": [], "durationId": "cb5e4253-a09b-47f3-8173-63f371f4d1e2"}}, {"head": {"id": "5762a722-2e1e-4a4b-b308-5e5d44adb7ec", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582720781200, "endTime": 315582722742000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a0d79c21-ab88-4eb3-8dd8-09f1e33f6314", "logId": "11554987-67d8-44d3-b207-48249a203d5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0d79c21-ab88-4eb3-8dd8-09f1e33f6314", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582717920700}, "additional": {"logType": "detail", "children": [], "durationId": "5762a722-2e1e-4a4b-b308-5e5d44adb7ec"}}, {"head": {"id": "279a66d5-e00c-4e01-927d-aadffd814377", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582718697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ad251d-7816-440a-a213-54430a4d27f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582718879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f5c4a81-4a0f-495a-9cca-64a45ac66bb0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582720797600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d4fd58-f803-4b9d-b221-3c07f65ee75b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582722467800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffb1d79-177b-449b-8031-5e487528cc75", "name": "entry : default@ProcessProfile cost memory 0.05745697021484375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582722633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11554987-67d8-44d3-b207-48249a203d5b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582720781200, "endTime": 315582722742000}, "additional": {"logType": "info", "children": [], "durationId": "5762a722-2e1e-4a4b-b308-5e5d44adb7ec"}}, {"head": {"id": "fe36d5fa-df1c-4b6d-ba68-a67d6ef78e55", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582728779600, "endTime": 315582735683800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "211668bb-1ac8-4d4b-b719-055906b0c136", "logId": "56fa1b98-aa6c-4180-8a2b-0a844fac7088"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "211668bb-1ac8-4d4b-b719-055906b0c136", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582725212900}, "additional": {"logType": "detail", "children": [], "durationId": "fe36d5fa-df1c-4b6d-ba68-a67d6ef78e55"}}, {"head": {"id": "27e7927b-92e8-410a-8a45-8a82b4f6502b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582725849500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe2a078-de19-40ce-93fc-3ff849ecdd03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582726050600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ddd193-2756-4d5f-bbf7-3038055ecad5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582728794500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347acfe6-91b7-4f20-aea7-287a2487e73f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582735464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd809b4e-0793-44df-bedb-cac078b6620a", "name": "entry : default@ProcessRouterMap cost memory 0.19051361083984375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582735610600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56fa1b98-aa6c-4180-8a2b-0a844fac7088", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582728779600, "endTime": 315582735683800}, "additional": {"logType": "info", "children": [], "durationId": "fe36d5fa-df1c-4b6d-ba68-a67d6ef78e55"}}, {"head": {"id": "c28a913d-9432-487c-a866-d0b64adb800a", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582744274400, "endTime": 315582747947600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7576b44f-b009-49f7-9822-4b3b5adbfce4", "logId": "4ea47503-ad59-421c-a229-3ce57b586e7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7576b44f-b009-49f7-9822-4b3b5adbfce4", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582739445300}, "additional": {"logType": "detail", "children": [], "durationId": "c28a913d-9432-487c-a866-d0b64adb800a"}}, {"head": {"id": "d4bce327-b0fc-4d42-9fd7-7686780b0f31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582740062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590c3dba-993b-4c8a-a0f1-23f8c599fb20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582740183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8d7bff-5885-49c8-a846-3b2eef915338", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582741315300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7145d3-298f-404a-84b3-cc8d56ed5196", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582745605900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38049553-65b6-49fc-9ebb-63091092a4a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582745802600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6baab19c-c4fa-4068-beac-c504d61afaa1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582745903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22152a1d-02d9-4280-898b-d62305609590", "name": "entry : default@PreviewProcessResource cost memory 0.0683135986328125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582746039600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1753de6b-5194-4c00-b095-70c8765aa4cc", "name": "runTaskFromQueue task cost before running: 324 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582747806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea47503-ad59-421c-a229-3ce57b586e7e", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582744274400, "endTime": 315582747947600, "totalTime": 1870400}, "additional": {"logType": "info", "children": [], "durationId": "c28a913d-9432-487c-a866-d0b64adb800a"}}, {"head": {"id": "c9ff6df9-db52-465c-b764-db326d7e25ad", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582756904600, "endTime": 315582781385100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "34ac9963-7690-4e59-9f96-01677a0a98a0", "logId": "85241c1b-073e-459a-8908-502319c86713"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34ac9963-7690-4e59-9f96-01677a0a98a0", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582752082300}, "additional": {"logType": "detail", "children": [], "durationId": "c9ff6df9-db52-465c-b764-db326d7e25ad"}}, {"head": {"id": "a8b28c22-21b1-43f4-b6c6-bb48e35b8cff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582752615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83ef8fe1-bdc6-429c-9b11-cdb495ee732e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582752735300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9f46a8-b336-49d7-aaa9-37a20f7136dc", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582756920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5642ce55-b798-4c4d-97b8-766e25f39bc5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582781104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf017463-cc03-4b5c-8a0b-dca1c6d56465", "name": "entry : default@GenerateLoaderJson cost memory -0.97784423828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582781290200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85241c1b-073e-459a-8908-502319c86713", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582756904600, "endTime": 315582781385100}, "additional": {"logType": "info", "children": [], "durationId": "c9ff6df9-db52-465c-b764-db326d7e25ad"}}, {"head": {"id": "ece58c92-d43f-4496-9fa6-63683d11a6a7", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582796420600, "endTime": 315582826443900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "59233292-c86f-46f8-88e3-e2d94bc7eb2f", "logId": "cf13f88a-6298-4d18-a6e2-495100974324"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59233292-c86f-46f8-88e3-e2d94bc7eb2f", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582791637100}, "additional": {"logType": "detail", "children": [], "durationId": "ece58c92-d43f-4496-9fa6-63683d11a6a7"}}, {"head": {"id": "c12ec45d-a643-40b6-93d9-1f20c28bfac4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582792334900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5adb5b9d-230e-43e0-816e-ba72e35d5ae9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582792474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0fe5f7-d28c-40db-86d5-ba12779c4e61", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582793591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c12c94-28cb-4a6b-bdc4-0bf9e0f70fe8", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582796449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8601f62c-7b5a-4070-9720-178aac09d1dc", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582826179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf8d232-0233-4276-be6b-c909ebf4de93", "name": "entry : default@PreviewCompileResource cost memory 0.7175750732421875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582826335800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf13f88a-6298-4d18-a6e2-495100974324", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582796420600, "endTime": 315582826443900}, "additional": {"logType": "info", "children": [], "durationId": "ece58c92-d43f-4496-9fa6-63683d11a6a7"}}, {"head": {"id": "e04593ea-594d-4a96-8510-c2bbdc71c584", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829854100, "endTime": 315582830987900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ace59476-9439-4534-af71-f7dfec04e7e3", "logId": "bf3586f1-45c0-4864-b5d0-56b87911b365"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ace59476-9439-4534-af71-f7dfec04e7e3", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829052700}, "additional": {"logType": "detail", "children": [], "durationId": "e04593ea-594d-4a96-8510-c2bbdc71c584"}}, {"head": {"id": "66114fcd-12fc-4b68-9c8a-2ef1ac0acd68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829618000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf224739-59f6-4ce1-99de-7250b0df877a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829730300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c918bd2d-ae91-4fe3-9ad4-3e0bd53f7dcc", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c957074-913b-4569-a5ca-1edf981081b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ef3dd91-2983-40a6-9005-b9ed95671b62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582830033800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd13bc19-3fbd-4196-a15c-d73f99a86269", "name": "entry : default@PreviewHookCompileResource cost memory -1.7276535034179688", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582830766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f0131a1-e5c0-4f91-8451-d923dacb0bbc", "name": "runTaskFromQueue task cost before running: 407 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582830910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf3586f1-45c0-4864-b5d0-56b87911b365", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582829854100, "endTime": 315582830987900, "totalTime": 1027700}, "additional": {"logType": "info", "children": [], "durationId": "e04593ea-594d-4a96-8510-c2bbdc71c584"}}, {"head": {"id": "bfe96da0-1f3b-43db-8329-5bb16d3793ce", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582834736200, "endTime": 315582837644500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c43d9b56-f008-4f39-8bb9-83ddbc1767e1", "logId": "c2ae668c-fc6d-4584-be8b-715e0580895b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c43d9b56-f008-4f39-8bb9-83ddbc1767e1", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582833159900}, "additional": {"logType": "detail", "children": [], "durationId": "bfe96da0-1f3b-43db-8329-5bb16d3793ce"}}, {"head": {"id": "00d6eccf-eaad-428f-b229-1858d7b14855", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582833763100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dcb8a2f-edea-47a1-a5c2-cf60d75181bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582833877100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074e2512-16e2-4bc2-b59b-83fd3e1f0fe5", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582834748100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08eba612-2130-4f2f-a105-2b799ba7ae9a", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582837393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb8d603-61f9-48ca-a3c6-7a3074a1ba5c", "name": "entry : default@CopyPreviewProfile cost memory 0.0965728759765625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582837559900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ae668c-fc6d-4584-be8b-715e0580895b", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582834736200, "endTime": 315582837644500}, "additional": {"logType": "info", "children": [], "durationId": "bfe96da0-1f3b-43db-8329-5bb16d3793ce"}}, {"head": {"id": "23a5afbf-dd9f-410d-bff2-8fef1f2c5f80", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841413500, "endTime": 315582841975600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "479cf08e-8b25-42bc-a43d-5c0fb4ad3991", "logId": "bf198b3d-5d28-47fa-bd41-d53fa9832650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "479cf08e-8b25-42bc-a43d-5c0fb4ad3991", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582839917800}, "additional": {"logType": "detail", "children": [], "durationId": "23a5afbf-dd9f-410d-bff2-8fef1f2c5f80"}}, {"head": {"id": "6bef3815-c0ae-4308-b653-0f12d17fcfda", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582840519600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3841c19a-3994-45ac-a2e1-253a4337ec95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582840624600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b2e430-300b-4d8c-bcac-ea055ac5a8cf", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841421000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886136a9-574e-494f-9247-764464434e3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566131b8-714e-41f0-b036-538bdf360275", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841625200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7500501-1428-463d-b1b6-eb170594bcad", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd3d88fa-b4a8-49c9-a8ab-fcbfc7426ce9", "name": "runTaskFromQueue task cost before running: 418 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf198b3d-5d28-47fa-bd41-d53fa9832650", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582841413500, "endTime": 315582841975600, "totalTime": 433900}, "additional": {"logType": "info", "children": [], "durationId": "23a5afbf-dd9f-410d-bff2-8fef1f2c5f80"}}, {"head": {"id": "21397e74-bcbe-4205-afa2-57bbb1f989c1", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844315500, "endTime": 315582844712700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "accc9342-f3cd-432e-b265-dcb42053e762", "logId": "fd7e55a0-a4d2-4406-b87f-931ae1b1ac22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "accc9342-f3cd-432e-b265-dcb42053e762", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844253700}, "additional": {"logType": "detail", "children": [], "durationId": "21397e74-bcbe-4205-afa2-57bbb1f989c1"}}, {"head": {"id": "385a11bd-1809-4ebc-b6d2-bde12535b25c", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4331ed07-8e3c-4eeb-9a6a-7f04edd437ad", "name": "entry : buildPreviewerResource cost memory 0.05065155029296875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844540600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b5f5ee-7775-4fae-aa54-5d73f2dfa39e", "name": "runTaskFromQueue task cost before running: 421 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7e55a0-a4d2-4406-b87f-931ae1b1ac22", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582844315500, "endTime": 315582844712700, "totalTime": 303100}, "additional": {"logType": "info", "children": [], "durationId": "21397e74-bcbe-4205-afa2-57bbb1f989c1"}}, {"head": {"id": "b06a80b5-403d-4d47-8dd8-9afcbf19ed5f", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582848929800, "endTime": 315582852753000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "fbe3ae30-09b1-4a52-9ddd-4a7416fb80e4", "logId": "c0a9a927-fc64-48f2-b00c-d20e7efcd638"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbe3ae30-09b1-4a52-9ddd-4a7416fb80e4", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582846847500}, "additional": {"logType": "detail", "children": [], "durationId": "b06a80b5-403d-4d47-8dd8-9afcbf19ed5f"}}, {"head": {"id": "eb3646ec-dd27-420d-8719-3b9d41553b0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582847476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37de195f-cf0d-4e27-877d-03a0114f856b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582847653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "132491c1-f271-47e2-a7b2-d943767918e3", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582848944400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ebd84e-5f30-4edc-b117-00a45a90bec5", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582852532200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35887e9a-eae4-4e03-934f-31045988032d", "name": "entry : default@PreviewUpdateAssets cost memory 0.104522705078125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582852675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a9a927-fc64-48f2-b00c-d20e7efcd638", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582848929800, "endTime": 315582852753000}, "additional": {"logType": "info", "children": [], "durationId": "b06a80b5-403d-4d47-8dd8-9afcbf19ed5f"}}, {"head": {"id": "db46cbde-b604-436d-a6a7-e7cc946dafd0", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582862865100, "endTime": 315605798748700}, "additional": {"children": ["a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed."], "detailId": "9700ba0d-bb53-4c52-89af-d7bbb4a6a834", "logId": "c11a919d-9308-4d70-8f6a-d8120344b8dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9700ba0d-bb53-4c52-89af-d7bbb4a6a834", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582855817000}, "additional": {"logType": "detail", "children": [], "durationId": "db46cbde-b604-436d-a6a7-e7cc946dafd0"}}, {"head": {"id": "c46b1c46-06f5-4ff0-99e3-4f8c38ec86b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582856371300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1717cb20-1689-46a7-bbee-f3c0759506ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582856489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1710964a-d0e4-41f9-9aec-b2b418e8f369", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582862882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d17648-2438-4b97-9db0-759cbf1aa15d", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582906378100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73197c0-6f2f-4ebf-b34a-a3019c2d7f19", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 37 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582906552400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker6", "startTime": 315582921687300, "endTime": 315605798554800}, "additional": {"children": ["62f9a433-d31c-4fc9-8e3a-167ae2f7ed44", "d5358c2f-b89c-4445-ad35-164f7e53ac26", "070f90e6-4c52-4cdd-968a-3dc8c297f21f", "75ace3b9-7624-439b-8cc2-d5e0b1ad5ac6"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "db46cbde-b604-436d-a6a7-e7cc946dafd0", "logId": "b744865a-c812-428b-8f13-27f657011654"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "606380d3-a200-4c37-ac4b-3b1380c81f5e", "name": "entry : default@PreviewArkTS cost memory 0.2474822998046875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582924877400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ceaee20-aab9-4311-8887-2255d9d167b6", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315592665204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f9a433-d31c-4fc9-8e3a-167ae2f7ed44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315592666396700, "endTime": 315592666418400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "logId": "c57e6b47-c867-4a31-80ce-59d29d6f050b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c57e6b47-c867-4a31-80ce-59d29d6f050b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315592666396700, "endTime": 315592666418400}, "additional": {"logType": "info", "children": [], "durationId": "62f9a433-d31c-4fc9-8e3a-167ae2f7ed44", "parent": "b744865a-c812-428b-8f13-27f657011654"}}, {"head": {"id": "7ef21cd6-ed4b-4f6c-b998-afc9d883370d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602902751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5358c2f-b89c-4445-ad35-164f7e53ac26", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602905816400, "endTime": 315602905868000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "logId": "851b62e0-cc8b-4c10-b236-96465fcd758f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "851b62e0-cc8b-4c10-b236-96465fcd758f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602905816400, "endTime": 315602905868000}, "additional": {"logType": "info", "children": [], "durationId": "d5358c2f-b89c-4445-ad35-164f7e53ac26", "parent": "b744865a-c812-428b-8f13-27f657011654"}}, {"head": {"id": "6ad70976-b4b6-4a97-b738-6530df040f98", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602906158200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070f90e6-4c52-4cdd-968a-3dc8c297f21f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602910818500, "endTime": 315602910872900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "logId": "656fa896-bdd8-4b99-9905-072d16636e14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "656fa896-bdd8-4b99-9905-072d16636e14", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315602910818500, "endTime": 315602910872900}, "additional": {"logType": "info", "children": [], "durationId": "070f90e6-4c52-4cdd-968a-3dc8c297f21f", "parent": "b744865a-c812-428b-8f13-27f657011654"}}, {"head": {"id": "2f7df92e-af64-4cbf-88e4-6735ba803efb", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605797252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ace3b9-7624-439b-8cc2-d5e0b1ad5ac6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605798451300, "endTime": 315605798470800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "logId": "b761b75a-a6a5-4c08-951c-9e13e747359b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b761b75a-a6a5-4c08-951c-9e13e747359b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605798451300, "endTime": 315605798470800}, "additional": {"logType": "info", "children": [], "durationId": "75ace3b9-7624-439b-8cc2-d5e0b1ad5ac6", "parent": "b744865a-c812-428b-8f13-27f657011654"}}, {"head": {"id": "b744865a-c812-428b-8f13-27f657011654", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Worker6", "startTime": 315582921687300, "endTime": 315605798554800}, "additional": {"logType": "error", "children": ["c57e6b47-c867-4a31-80ce-59d29d6f050b", "851b62e0-cc8b-4c10-b236-96465fcd758f", "656fa896-bdd8-4b99-9905-072d16636e14", "b761b75a-a6a5-4c08-951c-9e13e747359b"], "durationId": "a7750df6-5516-4ff9-b5e6-7b3ae1ceaff8", "parent": "c11a919d-9308-4d70-8f6a-d8120344b8dd"}}, {"head": {"id": "b7e94690-42ea-48fb-afb4-b9603b644c68", "name": "default@PreviewArkTS watch work[6] failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605798596900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11a919d-9308-4d70-8f6a-d8120344b8dd", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582862865100, "endTime": 315605798748700}, "additional": {"logType": "error", "children": ["b744865a-c812-428b-8f13-27f657011654"], "durationId": "db46cbde-b604-436d-a6a7-e7cc946dafd0"}}, {"head": {"id": "c1b5e2bf-b5a5-4cc8-a9bf-6e22f40f83b3", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605798880100}, "additional": {"logType": "debug", "children": [], "durationId": "db46cbde-b604-436d-a6a7-e7cc946dafd0"}}, {"head": {"id": "b61e4905-edbd-4641-a608-f54175b1384a", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:165:22\n Unknown resource name 'ic_transaction_active'.\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:192:22\n Unknown resource name 'ic_payment'.\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605829279400}, "additional": {"logType": "debug", "children": [], "durationId": "db46cbde-b604-436d-a6a7-e7cc946dafd0"}}, {"head": {"id": "14045688-359b-48a0-af2f-8902b2d0aaa0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836204300, "endTime": 315605836276100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6d081f5-d2c7-4fad-a16d-ceb8a598a309", "logId": "fa497c2a-15c7-4b5b-b399-22f91b4eb50b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa497c2a-15c7-4b5b-b399-22f91b4eb50b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836204300, "endTime": 315605836276100}, "additional": {"logType": "info", "children": [], "durationId": "14045688-359b-48a0-af2f-8902b2d0aaa0"}}, {"head": {"id": "e7f0e73e-aeb9-4058-8540-6871b8bf5cea", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315582424583200, "endTime": 315605836404600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 27}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "fabd9a1e-5e3d-43c6-be86-67339aaede1e", "name": "BUILD FAILED in 23 s 412 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836431900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "342c7157-ef7f-4122-8d39-898fee12ab04", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a64ba0-8fee-4b29-bd55-5292cfc3d3db", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836665500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7753f59e-d12f-487f-ba6d-5dfaa1649c7c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250ab1a3-70d9-434c-8017-f52fd05f821e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19277e03-e42e-4b72-b4ca-79f7b45c65a0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f93205-1568-475c-95f5-22dae45ca8c0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836890400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d58d8eb-ad81-4c48-8371-829fbd729d3d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836947800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eef30803-e81d-4c3e-8afd-a6f5a0258793", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605836995800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff91c6f-f99e-4102-be94-9748ef95cb90", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605837062200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaaa190f-f708-4fbc-8c3d-2a250d0f78a7", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605837140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6968b4c5-8fdc-42d5-80ee-7b5dd37354c4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605840611900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af080979-a6a2-4a7f-b197-7aa16274bc6d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605841586700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c215f7-5cea-4a24-9d15-8ced75a68852", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605841963200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "812ca1af-91b6-442a-89ff-7d3044eaf8d0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605842286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb8c0e4-1cf9-4ec7-869c-9b8a532234a4", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605843187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558419bb-98fa-4c76-bc79-1588dbe3b5e3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605843272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a134f47-6d01-4a85-8845-6e7c6adf199d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605843697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064cb92e-f828-4cd8-ac2c-752ac4401834", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605844021600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6cb7cd-0d65-4345-b6b0-59e1821c924e", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605844388800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8a6bfe-8016-48d7-9968-81f3410f57ce", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 315605844675500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}