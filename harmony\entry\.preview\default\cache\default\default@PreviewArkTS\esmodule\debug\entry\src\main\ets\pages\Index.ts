if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    isLoggedIn?: boolean;
    isLoading?: boolean;
}
import router from "@ohos:router";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isLoggedIn = new ObservedPropertySimplePU(false, this, "isLoggedIn");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.isLoggedIn !== undefined) {
            this.isLoggedIn = params.isLoggedIn;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isLoggedIn.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isLoggedIn.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isLoggedIn: ObservedPropertySimplePU<boolean>;
    get isLoggedIn() {
        return this.__isLoggedIn.get();
    }
    set isLoggedIn(newValue: boolean) {
        this.__isLoggedIn.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        console.log('=== Index页面启动 ===');
        // 为了测试，直接跳转到MyBankCardPage
        setTimeout(() => {
            console.log('开始测试跳转到MyBankCardPage');
            router.replaceUrl({
                url: 'pages/MyBankCardPage'
            }).then(() => {
                console.log('Index -> MyBankCardPage 跳转成功');
            }).catch((error: Error) => {
                console.error('Index -> MyBankCardPage 跳转失败:', error.message);
                // 如果跳转失败，回退到原来的逻辑
                this.checkLoginStatus();
            });
        }, 1000);
    }
    async checkLoginStatus() {
        try {
            // 检查本地存储的登录状态
            const isLoggedIn = await storageManager.isLoggedIn();
            if (isLoggedIn) {
                // 已登录，获取token并设置到HTTP客户端
                const token = await storageManager.getUserToken();
                if (token) {
                    httpClient.setAuthToken(token);
                }
                // 跳转到我的银行卡页面
                router.replaceUrl({
                    url: 'pages/MyBankCardPage'
                });
            }
            else {
                // 未登录，跳转到登录页
                router.replaceUrl({
                    url: 'pages/LoginPage'
                });
            }
        }
        catch (error) {
            console.error('检查登录状态失败:', error);
            // 出错时跳转到登录页
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(60:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777262, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/Index.ets(61:7)", "entry");
            Image.width(120);
            Image.height(120);
            Image.margin({ bottom: 20 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('E-Wallet');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(66:7)", "entry");
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1976D2');
            Text.margin({ bottom: 10 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('电子钱包');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(72:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/Index.ets(78:9)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正在加载...');
                        Text.debugLine("entry/src/main/ets/pages/Index.ets(83:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 20 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('进入应用');
                        Button.debugLine("entry/src/main/ets/pages/Index.ets(88:9)", "entry");
                        Button.width(200);
                        Button.height(48);
                        Button.fontSize(16);
                        Button.fontColor(Color.White);
                        Button.backgroundColor('#1976D2');
                        Button.borderRadius(8);
                        Button.onClick(() => {
                            router.pushUrl({
                                url: 'pages/LoginPage'
                            });
                        });
                    }, Button);
                    Button.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
