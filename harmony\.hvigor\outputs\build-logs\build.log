[2025-06-26T11:17:41.986] [DEBUG] debug-file - session manager: set active socket. socketId=YNhRdK7W_B_JTEtvAAAL
[2025-06-26T11:17:41.994] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:17:42.014] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:17:42.015] [DEBUG] debug-file - Since current hvigor version 5.15.3 differs from last hvigor version 
      undefined, delete file-cache.json and task-cache.json.
[2025-06-26T11:17:42.016] [DEBUG] debug-file - Cache service initialization finished in 2 ms 
[2025-06-26T11:17:42.032] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:17:42.036] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:17:42.036] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:17:42.042] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:17:42.043] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:17:42.044] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:17:42.044] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:17:42.048] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:17:42.053] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:17:42.070] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:17:42.116] [DEBUG] debug-file - Sdk init in 62 ms 
[2025-06-26T11:17:42.140] [DEBUG] debug-file - Project task initialization takes 24 ms 
[2025-06-26T11:17:42.141] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:17:42.141] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:17:42.141] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:17:42.150] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:17:42.153] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:17:42.153] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:17:42.162] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:17:42.162] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:17:42.163] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:17:42.163] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:17:42.163] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:17:42.163] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:17:42.163] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:17:42.169] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-26T11:17:42.169] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:17:42.170] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:17:42.170] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:17:42.247] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 37 ms 
[2025-06-26T11:17:42.248] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:17:42.250] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:17:42.255] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:17:42.256] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:17:42.264] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:17:42.264] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:17:42.264] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:17:42.268] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:17:42.268] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:17:42.272] [DEBUG] debug-file - Configuration phase cost:250 ms 
[2025-06-26T11:17:42.274] [DEBUG] debug-file - Configuration task cost before running: 275 ms 
[2025-06-26T11:17:42.277] [DEBUG] debug-file - Executing task :entry:clean
[2025-06-26T11:17:42.278] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-26T11:17:45.451] [DEBUG] debug-file - entry : clean cost memory 0.7977523803710938
[2025-06-26T11:17:45.452] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 453 ms 
[2025-06-26T11:17:45.453] [INFO] debug-file - Finished :entry:clean... after 3 s 175 ms 
[2025-06-26T11:17:45.465] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:45.465] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:45.482] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:17:45.491] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:45.491] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.109] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-26T11:17:46.111] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'E:\\hongmeng\\HM\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-26T11:17:46.111] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'E:\\hongmeng\\HM\\DevEco Studio\\tools\\node' } ]
[2025-06-26T11:17:46.113] [DEBUG] debug-file - entry : default@PreBuild cost memory 13.067970275878906
[2025-06-26T11:17:46.113] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 114 ms 
[2025-06-26T11:17:46.117] [INFO] debug-file - Finished :entry:default@PreBuild... after 632 ms 
[2025-06-26T11:17:46.120] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.121] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.122] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:17:46.122] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.122] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.123] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-06-26T11:17:46.123] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-06-26T11:17:46.123] [DEBUG] debug-file - Change app target API version with '50003015'
[2025-06-26T11:17:46.123] [DEBUG] debug-file - Change app minimum API version with '50003015'
[2025-06-26T11:17:46.124] [DEBUG] debug-file - Use cli appEnvironment
[2025-06-26T11:17:46.128] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.22540283203125
[2025-06-26T11:17:46.128] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 129 ms 
[2025-06-26T11:17:46.128] [INFO] debug-file - Finished :entry:default@MergeProfile... after 6 ms 
[2025-06-26T11:17:46.131] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.131] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.132] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:17:46.133] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:17:46.133] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.133] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.135] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1128997802734375
[2025-06-26T11:17:46.135] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 136 ms 
[2025-06-26T11:17:46.135] [INFO] debug-file - Finished :entry:default@CreateBuildProfile... after 4 ms 
[2025-06-26T11:17:46.139] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.139] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.140] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:17:46.140] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.140] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.140] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:17:46.141] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 142 ms 
[2025-06-26T11:17:46.141] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:17:46.143] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.143] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.152] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:17:46.152] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:17:46.153] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.05225372314453125
[2025-06-26T11:17:46.153] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 154 ms 
[2025-06-26T11:17:46.153] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 1 ms 
[2025-06-26T11:17:46.155] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.155] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.157] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:17:46.157] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.157] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.176] [DEBUG] debug-file - [
  'E:\\hongmeng\\HM\\DevEco Studio\\tools\\node\\node.exe',
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\ts2abc.js',
  '--target-api-version',
  '15'
]
[2025-06-26T11:17:46.297] [DEBUG] debug-file - ********
[2025-06-26T11:17:46.301] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.3201446533203125
[2025-06-26T11:17:46.301] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 302 ms 
[2025-06-26T11:17:46.301] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 145 ms 
[2025-06-26T11:17:46.303] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.303] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.305] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:17:46.308] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.308] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.310] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.14151763916015625
[2025-06-26T11:17:46.311] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 312 ms 
[2025-06-26T11:17:46.312] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 6 ms 
[2025-06-26T11:17:46.322] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.322] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.324] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:17:46.328] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:17:46.328] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.328] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.328] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.07866668701171875
[2025-06-26T11:17:46.330] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 331 ms 
[2025-06-26T11:17:46.331] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:17:46.333] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.333] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.337] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:17:46.360] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7815933227539062
[2025-06-26T11:17:46.360] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 361 ms 
[2025-06-26T11:17:46.366] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 23 ms 
[2025-06-26T11:17:46.368] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.368] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.369] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:17:46.372] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:17:46.378] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\AppScope\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:17:46.380] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 127205376,
  heapTotal: 135094272,
  heapUsed: 118042576,
  external: 3215816,
  arrayBuffers: 209681
} os memoryUsage :5.5611724853515625
[2025-06-26T11:17:46.407] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:17:46.410] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\entry\\src\\main\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:17:46.413] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 127225856,
  heapTotal: 135094272,
  heapUsed: 118311464,
  external: 3215942,
  arrayBuffers: 209822
} os memoryUsage :5.561187744140625
[2025-06-26T11:17:46.472] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:17:46.474] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***y',
  '-r',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:17:46.477] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 127242240,
  heapTotal: 135094272,
  heapUsed: 118696904,
  external: 3216068,
  arrayBuffers: 210603
} os memoryUsage :5.561119079589844
[2025-06-26T11:17:46.553] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:17:46.557] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.1096725463867188
[2025-06-26T11:17:46.557] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 558 ms 
[2025-06-26T11:17:46.558] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 186 ms 
[2025-06-26T11:17:46.561] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.561] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.561] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:17:46.561] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.561] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.561] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-26T11:17:46.561] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 562 ms 
[2025-06-26T11:17:46.561] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:17:46.563] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.563] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.564] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:17:46.564] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.564] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.593] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.154876708984375
[2025-06-26T11:17:46.593] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 595 ms 
[2025-06-26T11:17:46.594] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 30 ms 
[2025-06-26T11:17:46.600] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.600] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.602] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:17:46.602] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.602] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.602] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-26T11:17:46.603] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 604 ms 
[2025-06-26T11:17:46.603] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:17:46.607] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:17:46.607] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:17:46.607] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 609 ms 
[2025-06-26T11:17:46.608] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:17:46.613] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.613] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.615] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:17:46.618] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.618] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.620] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.09586334228515625
[2025-06-26T11:17:46.620] [DEBUG] debug-file - runTaskFromQueue task cost before running: 4 s 621 ms 
[2025-06-26T11:17:46.622] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 5 ms 
[2025-06-26T11:17:46.625] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:17:46.626] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:17:46.633] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:17:46.667] [DEBUG] debug-file - session manager: binding session. socketId=YNhRdK7W_B_JTEtvAAAL, threadId=1@5.
[2025-06-26T11:17:46.669] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -5.379920959472656
[2025-06-26T11:17:51.441] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:17:51.442] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:18:07.810] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:18:07.825] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:18:07.858] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:18:07.843] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 21 s 204 ms 
[2025-06-26T11:18:07.847] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:18:07.847] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.011688232421875
[2025-06-26T11:18:07.848] [DEBUG] debug-file - runTaskFromQueue task cost before running: 25 s 849 ms 
[2025-06-26T11:18:07.848] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:18:07.860] [DEBUG] debug-file - BUILD SUCCESSFUL in 25 s 861 ms 
[2025-06-26T11:18:07.862] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-26T11:18:07.863] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-26T11:18:07.864] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-26T11:18:07.865] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\entry\build-profile.json5 cache by regenerate.
[2025-06-26T11:18:07.866] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\entry\src\main\resources\base\profile\main_pages.json cache by regenerate.
[2025-06-26T11:18:07.866] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-26T11:18:07.867] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:18:07.868] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\entry\oh-package.json5 cache by regenerate.
[2025-06-26T11:18:07.868] [DEBUG] debug-file - Update task entry:default@PreBuild input file:F:\e-wallet\harmony\oh-package.json5 cache by regenerate.
[2025-06-26T11:18:07.869] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:9 ms .
[2025-06-26T11:18:07.869] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:F:\e-wallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-26T11:18:07.870] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:F:\e-wallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-26T11:18:07.871] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:F:\e-wallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-26T11:18:07.872] [DEBUG] debug-file - Update task entry:default@MergeProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache.
[2025-06-26T11:18:07.873] [DEBUG] debug-file - Incremental task entry:default@MergeProfile post-execution cost:4 ms .
[2025-06-26T11:18:07.873] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:F:\e-wallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-26T11:18:07.873] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:F:\e-wallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-26T11:18:07.874] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile output file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache.
[2025-06-26T11:18:07.875] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile post-execution cost:3 ms .
[2025-06-26T11:18:07.875] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-26T11:18:07.876] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-26T11:18:07.876] [DEBUG] debug-file - Update task entry:default@ProcessProfile input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:18:07.877] [DEBUG] debug-file - Update task entry:default@ProcessProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache.
[2025-06-26T11:18:07.878] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile post-execution cost:2 ms .
[2025-06-26T11:18:07.881] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:F:\e-wallet\harmony\entry\oh-package.json5 cache by regenerate.
[2025-06-26T11:18:07.882] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:F:\e-wallet\harmony\oh-package.json5 cache by regenerate.
[2025-06-26T11:18:07.882] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:F:\e-wallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-26T11:18:07.883] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:18:07.883] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache.
[2025-06-26T11:18:07.884] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\router_map\default\loader-router-map.json cache.
[2025-06-26T11:18:07.885] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap post-execution cost:7 ms .
[2025-06-26T11:18:07.910] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:18:07.911] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:18:07.912] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache by regenerate.
[2025-06-26T11:18:07.914] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\loader.json cache.
[2025-06-26T11:18:07.918] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson post-execution cost:34 ms .
[2025-06-26T11:18:07.920] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\src\main\resources cache by regenerate.
[2025-06-26T11:18:07.942] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:18:07.942] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:18:07.968] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:18:07.969] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:51 ms .
[2025-06-26T11:18:07.970] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:F:\e-wallet\harmony\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:18:07.972] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:18:07.973] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:5 ms .
[2025-06-26T11:18:07.974] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:18:07.975] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:18:07.976] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:3 ms .
[2025-06-26T11:18:07.982] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:18:07.984] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:18:07.985] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:18:07.985] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:18:07.988] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:18:08.013] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:18:08.014] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:18:08.015] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:18:08.015] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:18:08.016] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:40 ms .
[2025-06-26T11:18:08.113] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:18:08.115] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:19:43.941] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:19:43.945] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:19:44.018] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T11:19:44.133] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:19:44.134] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:26:56.101] [DEBUG] debug-file - session manager: set active socket. socketId=VTobUzdjNNGleXFEAAAN
[2025-06-26T11:26:56.109] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:26:56.134] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:26:56.138] [DEBUG] debug-file - Cache service initialization finished in 5 ms 
[2025-06-26T11:26:56.156] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:26:56.162] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:26:56.162] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:26:56.170] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:26:56.170] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:26:56.171] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:26:56.171] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:26:56.173] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:26:56.178] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:26:56.190] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:26:56.230] [DEBUG] debug-file - Sdk init in 51 ms 
[2025-06-26T11:26:56.257] [DEBUG] debug-file - Project task initialization takes 26 ms 
[2025-06-26T11:26:56.257] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:26:56.257] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:26:56.257] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:26:56.266] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:26:56.269] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:26:56.270] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:26:56.279] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:26:56.279] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:26:56.279] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:26:56.279] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:26:56.279] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:26:56.280] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:26:56.280] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:26:56.284] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:26:56.284] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:26:56.284] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:26:56.284] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:26:56.312] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 25 ms 
[2025-06-26T11:26:56.313] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:26:56.315] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:26:56.321] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:26:56.323] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:26:56.339] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:26:56.339] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:26:56.339] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:26:56.342] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:26:56.342] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:26:56.346] [DEBUG] debug-file - Configuration phase cost:200 ms 
[2025-06-26T11:26:56.349] [DEBUG] debug-file - Configuration task cost before running: 234 ms 
[2025-06-26T11:26:56.352] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.352] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.356] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:26:56.369] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 9 ms .
[2025-06-26T11:26:56.369] [DEBUG] debug-file - entry : default@PreBuild cost memory -1.5355606079101562
[2025-06-26T11:26:56.372] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:26:56.374] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.375] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.376] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:26:56.378] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:26:56.378] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1116180419921875
[2025-06-26T11:26:56.378] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:26:56.381] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.381] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.383] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:26:56.384] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-26T11:26:56.387] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:26:56.387] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.0985107421875
[2025-06-26T11:26:56.387] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:26:56.391] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.391] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.392] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:26:56.392] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.393] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.393] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:26:56.393] [DEBUG] debug-file - runTaskFromQueue task cost before running: 278 ms 
[2025-06-26T11:26:56.393] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:26:56.396] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.396] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.403] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:26:56.404] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:26:56.405] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:26:56.405] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.066070556640625
[2025-06-26T11:26:56.405] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:26:56.409] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.409] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.411] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:26:56.413] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:26:56.413] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05745697021484375
[2025-06-26T11:26:56.413] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:26:56.416] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.417] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.419] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:26:56.426] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .
[2025-06-26T11:26:56.426] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.19051361083984375
[2025-06-26T11:26:56.428] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:26:56.431] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.431] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.432] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:26:56.436] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:26:56.436] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.436] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.436] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.0683135986328125
[2025-06-26T11:26:56.438] [DEBUG] debug-file - runTaskFromQueue task cost before running: 324 ms 
[2025-06-26T11:26:56.440] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:26:56.443] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.443] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.447] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:26:56.472] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .
[2025-06-26T11:26:56.472] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.97784423828125
[2025-06-26T11:26:56.480] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:26:56.483] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.483] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.484] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:26:56.487] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:26:56.517] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 29 ms .
[2025-06-26T11:26:56.517] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.7175750732421875
[2025-06-26T11:26:56.518] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-26T11:26:56.520] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.520] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.520] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:26:56.520] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.520] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.521] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory -1.7276535034179688
[2025-06-26T11:26:56.521] [DEBUG] debug-file - runTaskFromQueue task cost before running: 407 ms 
[2025-06-26T11:26:56.522] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 2 ms 
[2025-06-26T11:26:56.524] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.524] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.525] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:26:56.528] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .
[2025-06-26T11:26:56.528] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.0965728759765625
[2025-06-26T11:26:56.528] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-26T11:26:56.531] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.531] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.532] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:26:56.532] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.532] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.532] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-26T11:26:56.532] [DEBUG] debug-file - runTaskFromQueue task cost before running: 418 ms 
[2025-06-26T11:26:56.533] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:26:56.535] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:26:56.535] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.05065155029296875
[2025-06-26T11:26:56.535] [DEBUG] debug-file - runTaskFromQueue task cost before running: 421 ms 
[2025-06-26T11:26:56.535] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:26:56.538] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.538] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.539] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:26:56.543] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .
[2025-06-26T11:26:56.543] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.104522705078125
[2025-06-26T11:26:56.544] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-26T11:26:56.547] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:26:56.547] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:26:56.553] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:26:56.597] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\e-wallet\harmony\entry\src\main\ets' has been changed.
[2025-06-26T11:26:56.597] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 37 ms .
[2025-06-26T11:26:56.613] [DEBUG] debug-file - session manager: binding session. socketId=VTobUzdjNNGleXFEAAAN, threadId=1@6.
[2025-06-26T11:26:56.615] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.2474822998046875
[2025-06-26T11:27:06.356] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:27:06.358] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:27:16.593] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:27:16.598] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:27:16.597] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:27:16.603] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:27:19.488] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:27:19.490] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:27:19.489] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:27:19.489] [DEBUG] debug-file - default@PreviewArkTS watch work[6] failed.
[2025-06-26T11:27:19.489] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-26T11:27:19.489] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-26T11:27:19.520] [DEBUG] debug-file - ERROR: stacktrace = Error: [31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:165:22
 Unknown resource name 'ic_transaction_active'.[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:192:22
 Unknown resource name 'ic_payment'.
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:27:19.520] [ERROR] debug-file - Error: [31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:165:22
 Unknown resource name 'ic_transaction_active'.[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:192:22
 Unknown resource name 'ic_payment'.
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:27:19.527] [WARN] debug-file - BUILD FAILED in 23 s 412 ms 
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:27:19.527] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-26T11:27:19.528] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-26T11:27:19.528] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-26T11:27:19.531] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:27:19.532] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:27:19.532] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:27:19.533] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:27:19.534] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache from map.
[2025-06-26T11:27:19.534] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:27:19.534] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:27:19.534] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:27:19.535] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:27:19.535] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:8 ms .
[2025-06-26T11:27:19.547] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:27:19.548] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:27:19.554] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:27:19.556] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:27:19.619] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:27:19.619] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:32:29.259] [DEBUG] debug-file - session manager: set active socket. socketId=daJ0QMQfJvn2wudZAAAP
[2025-06-26T11:32:29.266] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:32:29.292] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:32:29.298] [DEBUG] debug-file - Cache service initialization finished in 7 ms 
[2025-06-26T11:32:29.317] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:32:29.322] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:32:29.322] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:32:29.330] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:32:29.330] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:32:29.331] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:32:29.331] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:32:29.333] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:32:29.336] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:32:29.348] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:32:29.378] [DEBUG] debug-file - Sdk init in 40 ms 
[2025-06-26T11:32:29.406] [DEBUG] debug-file - Project task initialization takes 27 ms 
[2025-06-26T11:32:29.406] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:32:29.407] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:32:29.407] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:32:29.415] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:32:29.419] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:32:29.420] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:32:29.428] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:32:29.428] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:32:29.428] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:32:29.428] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:32:29.428] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:32:29.429] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:32:29.429] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:32:29.434] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:32:29.434] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:32:29.434] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:32:29.434] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:32:29.459] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 24 ms 
[2025-06-26T11:32:29.460] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:32:29.462] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:32:29.468] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:32:29.469] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:32:29.476] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:32:29.476] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:32:29.476] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:32:29.479] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:32:29.479] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:32:29.482] [DEBUG] debug-file - Configuration phase cost:176 ms 
[2025-06-26T11:32:29.484] [DEBUG] debug-file - Configuration task cost before running: 213 ms 
[2025-06-26T11:32:29.486] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.486] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.489] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:32:29.498] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 7 ms .
[2025-06-26T11:32:29.498] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.26850128173828125
[2025-06-26T11:32:29.501] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:32:29.504] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.504] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.505] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:32:29.507] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:32:29.507] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1113739013671875
[2025-06-26T11:32:29.507] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:32:29.510] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.510] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.511] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:32:29.512] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-26T11:32:29.514] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:32:29.514] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09807586669921875
[2025-06-26T11:32:29.514] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:32:29.518] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.518] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.519] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:32:29.520] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.520] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.520] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:32:29.520] [DEBUG] debug-file - runTaskFromQueue task cost before running: 250 ms 
[2025-06-26T11:32:29.520] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:32:29.523] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.523] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.533] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:32:29.533] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:32:29.533] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:32:29.534] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06620025634765625
[2025-06-26T11:32:29.534] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:32:29.536] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.536] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.538] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:32:29.539] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:32:29.539] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05743408203125
[2025-06-26T11:32:29.539] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:32:29.542] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.542] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.544] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:32:29.550] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T11:32:29.550] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.19033050537109375
[2025-06-26T11:32:29.552] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:32:29.554] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.554] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.555] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:32:29.562] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:32:29.563] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.563] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.563] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory -1.5597381591796875
[2025-06-26T11:32:29.564] [DEBUG] debug-file - runTaskFromQueue task cost before running: 294 ms 
[2025-06-26T11:32:29.566] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 4 ms 
[2025-06-26T11:32:29.570] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.570] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.575] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:32:29.607] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 19 ms .
[2025-06-26T11:32:29.607] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7255096435546875
[2025-06-26T11:32:29.620] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:32:29.624] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.624] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.625] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:32:29.628] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:32:29.655] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T11:32:29.655] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 27 ms .
[2025-06-26T11:32:29.719] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\AppScope\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:32:29.721] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118652928,
  heapTotal: 129327104,
  heapUsed: 123049784,
  external: 3142217,
  arrayBuffers: 136082
} os memoryUsage :6.155727386474609
[2025-06-26T11:32:29.750] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:32:29.753] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\entry\\src\\main\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:32:29.756] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118681600,
  heapTotal: 129327104,
  heapUsed: 123303408,
  external: 3142343,
  arrayBuffers: 136223
} os memoryUsage :6.163848876953125
[2025-06-26T11:32:29.798] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:32:29.800] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***y',
  '-r',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:32:29.803] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118706176,
  heapTotal: 129327104,
  heapUsed: 123584544,
  external: 3142469,
  arrayBuffers: 137163
} os memoryUsage :6.180522918701172
[2025-06-26T11:32:29.898] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:32:29.902] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.00682830810546875
[2025-06-26T11:32:29.903] [DEBUG] debug-file - runTaskFromQueue task cost before running: 632 ms 
[2025-06-26T11:32:29.904] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 275 ms 
[2025-06-26T11:32:29.906] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.906] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.907] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:32:29.907] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.907] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.907] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory -1.6985931396484375
[2025-06-26T11:32:29.907] [DEBUG] debug-file - runTaskFromQueue task cost before running: 637 ms 
[2025-06-26T11:32:29.908] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:32:29.910] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.910] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.911] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:32:29.912] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:32:29.912] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:32:29.912] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.912] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.921] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.2053070068359375
[2025-06-26T11:32:29.921] [DEBUG] debug-file - runTaskFromQueue task cost before running: 651 ms 
[2025-06-26T11:32:29.922] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 11 ms 
[2025-06-26T11:32:29.924] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.924] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.925] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:32:29.925] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.925] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.925] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-26T11:32:29.925] [DEBUG] debug-file - runTaskFromQueue task cost before running: 655 ms 
[2025-06-26T11:32:29.925] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:32:29.927] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:32:29.927] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:32:29.927] [DEBUG] debug-file - runTaskFromQueue task cost before running: 657 ms 
[2025-06-26T11:32:29.927] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:32:29.929] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.929] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.930] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:32:29.933] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:32:29.933] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .
[2025-06-26T11:32:29.933] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.933] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.935] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.13039398193359375
[2025-06-26T11:32:29.935] [DEBUG] debug-file - runTaskFromQueue task cost before running: 665 ms 
[2025-06-26T11:32:29.936] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 5 ms 
[2025-06-26T11:32:29.940] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:32:29.940] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:32:29.950] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:32:29.986] [DEBUG] debug-file - session manager: binding session. socketId=daJ0QMQfJvn2wudZAAAP, threadId=1@7.
[2025-06-26T11:32:29.990] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -0.2169036865234375
[2025-06-26T11:32:35.269] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:32:35.270] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:32:44.077] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.252] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.079] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.261] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.079] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.263] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.080] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.265] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.081] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.267] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.082] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.270] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.084] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.273] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.220] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.275] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.223] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.277] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.225] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.280] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.227] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.282] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.228] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.284] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.230] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.287] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.233] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.290] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.235] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.293] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.237] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.295] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.239] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.318] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.241] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.321] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.243] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.324] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.245] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.326] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.248] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.328] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.262] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.330] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.264] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.332] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.267] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.334] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.270] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.336] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.274] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.338] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.277] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.340] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.281] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.342] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.284] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.344] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.286] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.346] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.293] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.348] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.297] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.350] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.299] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.352] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.301] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.377] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.303] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.379] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.306] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.381] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.308] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.383] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.310] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.386] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.312] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.387] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.317] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.389] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.319] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.392] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.322] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.400] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.325] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.403] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.327] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.405] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.329] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.407] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.330] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.409] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.333] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.411] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.335] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.413] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.336] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.414] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.338] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.416] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.340] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.417] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.342] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.418] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.344] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.419] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.346] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.421] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.348] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.422] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.350] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.424] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.352] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.425] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.355] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.426] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.356] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.428] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.358] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.429] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.360] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.432] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.361] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.434] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.363] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.435] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.365] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.437] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.367] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.439] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.368] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.441] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.370] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.444] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.627] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:32:44.629] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:32:44.664] [WARN] debug-file - Error [RollupError]: 'return' outside of function (Note that you need plugins to import files that are not JavaScript)
    at error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:210:30)
    at Module.error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:13962:16)
    at Module.tryParse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14728:25)
    at Module.setSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14257:37)
    at ModuleLoader.addModuleSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:24000:20) {
  cause: SyntaxError: 'return' outside of function (402:8)
      at pp$4.raise (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:21395:13)
      at pp$8.parseReturnStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19008:12)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18844:37)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseIfStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19001:26)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18843:33)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseTopLevel (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18753:21) {
    pos: 15812,
    loc: Position { line: 402, column: 8 },
    raisedAt: 15818
  },
  code: 'PARSE_ERROR',
  id: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
  pos: 15812,
  loc: {
    column: 6,
    file: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    line: 230
  },
  frame: '228:   isLoginEnabled(): boolean {\n' +
    '229:     if (this.isLoading) {\n' +
    '230:       return false;\n' +
    '           ^\n' +
    '231:     }\n' +
    '232:     // 简化条件：只需要用户名和密码',
  watchFiles: [
    'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt',
    'F:\\e-wallet\\harmony\\entry\\oh-package.json5',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\MyBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransferPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\RechargePage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\EditBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WithdrawPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\PaymentCenterPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\NFCPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\QRCodePaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TestPaymentPage.ets'
  ]
}
[2025-06-26T11:32:44.665] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:32:44.706] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:32:44.667] [DEBUG] debug-file - default@PreviewArkTS watch work[7] failed.
[2025-06-26T11:32:44.667] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-26T11:32:44.667] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-26T11:32:44.668] [DEBUG] debug-file - ERROR: stacktrace = Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:32:44.668] [ERROR] debug-file - Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:32:44.677] [WARN] debug-file - BUILD FAILED in 15 s 407 ms 
[2025-06-26T11:32:44.677] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:32:44.677] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:32:44.677] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:32:44.678] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:32:44.678] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:32:44.678] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:32:44.678] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:32:44.679] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\src\main\resources cache by regenerate.
[2025-06-26T11:32:44.690] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:32:44.691] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:32:44.713] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:32:44.714] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:37 ms .
[2025-06-26T11:32:44.714] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:F:\e-wallet\harmony\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:32:44.716] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:32:44.717] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .
[2025-06-26T11:32:44.718] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:32:44.718] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:32:44.719] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-26T11:32:44.723] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:32:44.725] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:32:44.726] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:32:44.726] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:32:44.728] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:32:44.748] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:32:44.748] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:32:44.749] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:32:44.750] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:32:44.750] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:32 ms .
[2025-06-26T11:32:44.778] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:32:44.779] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:32:44.791] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:32:44.791] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:32:44.666] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:32:46.252] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:32:46.253] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:33:10.807] [DEBUG] debug-file - session manager: set active socket. socketId=dD0Wgl2EYUtCdWVoAAAR
[2025-06-26T11:33:10.831] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:33:10.860] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:33:10.869] [DEBUG] debug-file - Cache service initialization finished in 9 ms 
[2025-06-26T11:33:10.896] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:33:10.904] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:33:10.905] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:33:10.919] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:33:10.919] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:33:10.919] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:33:10.919] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:33:10.923] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:33:10.933] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:33:10.950] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:33:10.991] [DEBUG] debug-file - Sdk init in 56 ms 
[2025-06-26T11:33:11.024] [DEBUG] debug-file - Project task initialization takes 31 ms 
[2025-06-26T11:33:11.024] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:33:11.024] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:33:11.025] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:33:11.039] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:33:11.044] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:33:11.045] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:33:11.057] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:33:11.057] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:33:11.059] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:33:11.059] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:33:11.059] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:33:11.059] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:33:11.060] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:33:11.067] [DEBUG] debug-file - Module entry task initialization takes 4 ms 
[2025-06-26T11:33:11.067] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:33:11.068] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:33:11.068] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:33:11.110] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 40 ms 
[2025-06-26T11:33:11.112] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:33:11.115] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:33:11.123] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:33:11.124] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:33:11.133] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:33:11.133] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:33:11.133] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:33:11.137] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:33:11.137] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:33:11.142] [DEBUG] debug-file - Configuration phase cost:260 ms 
[2025-06-26T11:33:11.145] [DEBUG] debug-file - Configuration task cost before running: 307 ms 
[2025-06-26T11:33:11.148] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.148] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.154] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:33:11.168] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 10 ms .
[2025-06-26T11:33:11.168] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.26880645751953125
[2025-06-26T11:33:11.172] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:33:11.175] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.175] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.176] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:33:11.179] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:33:11.179] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1116180419921875
[2025-06-26T11:33:11.179] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:33:11.183] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.183] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.185] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:33:11.186] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-26T11:33:11.188] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:33:11.188] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09807586669921875
[2025-06-26T11:33:11.188] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:33:11.192] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.192] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.193] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:33:11.193] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.193] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.193] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:33:11.193] [DEBUG] debug-file - runTaskFromQueue task cost before running: 356 ms 
[2025-06-26T11:33:11.194] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:33:11.198] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.198] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.209] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:33:11.209] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:33:11.209] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:33:11.210] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0662994384765625
[2025-06-26T11:33:11.210] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:33:11.213] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.213] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.214] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:33:11.215] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-26T11:33:11.215] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.056732177734375
[2025-06-26T11:33:11.215] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:33:11.218] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.218] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.220] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:33:11.226] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T11:33:11.226] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.18952178955078125
[2025-06-26T11:33:11.228] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:33:11.230] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.230] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.232] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:33:11.237] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:33:11.237] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.237] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.237] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.06855010986328125
[2025-06-26T11:33:11.239] [DEBUG] debug-file - runTaskFromQueue task cost before running: 401 ms 
[2025-06-26T11:33:11.240] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:33:11.243] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.243] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.247] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:33:11.280] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .
[2025-06-26T11:33:11.280] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.9506072998046875
[2025-06-26T11:33:11.294] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:33:11.298] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.298] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.301] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:33:11.306] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:33:11.337] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 30 ms .
[2025-06-26T11:33:11.337] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -1.0441665649414062
[2025-06-26T11:33:11.338] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-26T11:33:11.341] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.341] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.341] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:33:11.342] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.342] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.342] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-26T11:33:11.342] [DEBUG] debug-file - runTaskFromQueue task cost before running: 504 ms 
[2025-06-26T11:33:11.342] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:33:11.345] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.345] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.346] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:33:11.349] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .
[2025-06-26T11:33:11.349] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.0952606201171875
[2025-06-26T11:33:11.349] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-26T11:33:11.352] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.352] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.353] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:33:11.353] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.353] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.353] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03858184814453125
[2025-06-26T11:33:11.353] [DEBUG] debug-file - runTaskFromQueue task cost before running: 515 ms 
[2025-06-26T11:33:11.353] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:33:11.355] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:33:11.355] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.181427001953125
[2025-06-26T11:33:11.355] [DEBUG] debug-file - runTaskFromQueue task cost before running: 518 ms 
[2025-06-26T11:33:11.355] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:33:11.360] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.360] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.362] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:33:11.366] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 4 ms .
[2025-06-26T11:33:11.366] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.10277557373046875
[2025-06-26T11:33:11.368] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-26T11:33:11.372] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:33:11.372] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:33:11.395] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:33:11.426] [DEBUG] debug-file - session manager: binding session. socketId=dD0Wgl2EYUtCdWVoAAAR, threadId=1@8.
[2025-06-26T11:33:11.429] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 1.1741180419921875
[2025-06-26T11:33:19.393] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:33:19.394] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:33:29.801] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.805] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.804] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.808] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.807] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.812] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.811] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.816] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.814] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.819] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.818] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.822] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.821] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.826] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.825] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.830] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.829] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.841] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.840] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.858] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.860] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.867] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.868] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.874] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.873] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.880] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.881] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.886] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.885] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.889] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.888] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.892] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.891] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.895] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.894] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.900] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.900] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.905] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.903] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.908] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.907] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.912] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.910] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.916] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.915] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.927] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.921] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.930] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.924] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.933] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.928] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.936] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.932] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.940] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.939] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.944] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.944] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.952] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.954] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.962] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.960] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.967] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.967] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.972] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.971] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.975] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.974] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.983] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.984] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.989] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.988] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.992] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.991] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:29.996] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:29.996] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.002] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.001] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.005] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.004] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.008] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.007] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.014] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.014] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.019] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.020] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.025] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.024] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.029] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.031] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.038] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.037] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.043] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.042] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.047] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.048] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.053] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.054] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.059] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.061] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.066] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.065] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.071] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.069] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.075] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.075] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.083] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.082] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.086] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.085] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.090] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.090] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.097] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.097] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.103] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.101] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.107] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.107] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.119] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.110] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.121] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.113] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.124] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.117] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.128] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.120] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.130] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.123] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.134] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.136] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.143] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.154] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.160] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.161] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.168] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.587] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.590] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.594] [WARN] debug-file - Error [RollupError]: 'return' outside of function (Note that you need plugins to import files that are not JavaScript)
    at error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:210:30)
    at Module.error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:13962:16)
    at Module.tryParse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14728:25)
    at Module.setSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14257:37)
    at ModuleLoader.addModuleSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:24000:20) {
  cause: SyntaxError: 'return' outside of function (402:8)
      at pp$4.raise (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:21395:13)
      at pp$8.parseReturnStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19008:12)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18844:37)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseIfStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19001:26)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18843:33)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseTopLevel (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18753:21) {
    pos: 15812,
    loc: Position { line: 402, column: 8 },
    raisedAt: 15818
  },
  code: 'PARSE_ERROR',
  id: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
  pos: 15812,
  loc: {
    column: 6,
    file: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    line: 230
  },
  frame: '228:   isLoginEnabled(): boolean {\n' +
    '229:     if (this.isLoading) {\n' +
    '230:       return false;\n' +
    '           ^\n' +
    '231:     }\n' +
    '232:     // 简化条件：只需要用户名和密码',
  watchFiles: [
    'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt',
    'F:\\e-wallet\\harmony\\entry\\oh-package.json5',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\MyBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransferPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WithdrawPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\RechargePage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\EditBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\PaymentCenterPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\QRCodePaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\NFCPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TestPaymentPage.ets'
  ]
}
[2025-06-26T11:33:30.595] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:33:30.599] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:33:30.598] [DEBUG] debug-file - default@PreviewArkTS watch work[8] failed.
[2025-06-26T11:33:30.598] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-26T11:33:30.598] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-26T11:33:30.600] [DEBUG] debug-file - ERROR: stacktrace = Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:33:30.600] [ERROR] debug-file - Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:33:30.613] [WARN] debug-file - BUILD FAILED in 19 s 776 ms 
[2025-06-26T11:33:30.613] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:33:30.613] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:33:30.613] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-26T11:33:30.614] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-26T11:33:30.621] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:33:30.622] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:33:30.623] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:33:30.624] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:33:30.625] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:33:30.641] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:33:30.641] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:33:30.642] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:33:30.643] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:33:30.644] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:30 ms .
[2025-06-26T11:33:30.668] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:33:30.670] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:33:30.693] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:33:30.694] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:33:30.910] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.913] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.913] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:33:30.912] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:33:30.914] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:33:30.914] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:33:30.596] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:33:32.065] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:33:32.065] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:36:11.207] [DEBUG] debug-file - session manager: set active socket. socketId=-rENyyQbKFZjeFxeAAAT
[2025-06-26T11:36:11.215] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:36:11.235] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:36:11.240] [DEBUG] debug-file - Cache service initialization finished in 5 ms 
[2025-06-26T11:36:11.263] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:11.271] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:36:11.271] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:36:11.283] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:36:11.284] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:36:11.284] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:36:11.285] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:36:11.289] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:36:11.298] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:36:11.313] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:36:11.348] [DEBUG] debug-file - Sdk init in 48 ms 
[2025-06-26T11:36:11.377] [DEBUG] debug-file - Project task initialization takes 27 ms 
[2025-06-26T11:36:11.377] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:36:11.377] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:11.377] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:11.388] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:11.391] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:36:11.391] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:36:11.401] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:36:11.401] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:36:11.402] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:36:11.402] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:36:11.402] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:36:11.402] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:36:11.402] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:36:11.407] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-26T11:36:11.407] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:36:11.407] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:11.407] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:11.435] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 26 ms 
[2025-06-26T11:36:11.436] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:36:11.438] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:36:11.444] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:36:11.445] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:36:11.452] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:36:11.452] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:36:11.452] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:36:11.456] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:36:11.457] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:36:11.462] [DEBUG] debug-file - Configuration phase cost:213 ms 
[2025-06-26T11:36:11.463] [DEBUG] debug-file - Configuration task cost before running: 244 ms 
[2025-06-26T11:36:11.466] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.466] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.470] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:36:11.479] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 7 ms .
[2025-06-26T11:36:11.479] [DEBUG] debug-file - entry : default@PreBuild cost memory -1.5127639770507812
[2025-06-26T11:36:11.482] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:36:11.485] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.485] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.486] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:36:11.488] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-26T11:36:11.488] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1110992431640625
[2025-06-26T11:36:11.488] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:36:11.491] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.491] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.493] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:36:11.494] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:36:11.495] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:36:11.495] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.097259521484375
[2025-06-26T11:36:11.495] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:36:11.498] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.498] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.499] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:36:11.499] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.499] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.499] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03742218017578125
[2025-06-26T11:36:11.499] [DEBUG] debug-file - runTaskFromQueue task cost before running: 280 ms 
[2025-06-26T11:36:11.499] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:36:11.502] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.502] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.514] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:36:11.514] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:36:11.515] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .
[2025-06-26T11:36:11.515] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06694793701171875
[2025-06-26T11:36:11.516] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:36:11.520] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.520] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.522] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:36:11.524] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:36:11.524] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05695343017578125
[2025-06-26T11:36:11.524] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:36:11.527] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.527] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.530] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:36:11.537] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T11:36:11.537] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.18972015380859375
[2025-06-26T11:36:11.539] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:36:11.543] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.543] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.545] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:36:11.549] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:36:11.549] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.549] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.549] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.06856536865234375
[2025-06-26T11:36:11.551] [DEBUG] debug-file - runTaskFromQueue task cost before running: 331 ms 
[2025-06-26T11:36:11.552] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:36:11.555] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.555] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.560] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:36:11.586] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .
[2025-06-26T11:36:11.586] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.9531021118164062
[2025-06-26T11:36:11.593] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:36:11.595] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.595] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.596] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:36:11.599] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:36:11.625] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T11:36:11.625] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 25 ms .
[2025-06-26T11:36:11.645] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\AppScope\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:36:11.647] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 104898560,
  heapTotal: 118317056,
  heapUsed: 110350584,
  external: 3108429,
  arrayBuffers: 102294
} os memoryUsage :6.297901153564453
[2025-06-26T11:36:11.679] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:36:11.682] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\entry\\src\\main\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:36:11.684] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 104914944,
  heapTotal: 118317056,
  heapUsed: 110602576,
  external: 3108555,
  arrayBuffers: 102435
} os memoryUsage :6.314262390136719
[2025-06-26T11:36:11.749] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:36:11.753] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***y',
  '-r',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:36:11.757] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 104964096,
  heapTotal: 118317056,
  heapUsed: 110960496,
  external: 3108681,
  arrayBuffers: 103375
} os memoryUsage :6.287006378173828
[2025-06-26T11:36:11.846] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:36:11.851] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.21660614013671875
[2025-06-26T11:36:11.851] [DEBUG] debug-file - runTaskFromQueue task cost before running: 631 ms 
[2025-06-26T11:36:11.852] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 252 ms 
[2025-06-26T11:36:11.854] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.855] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.855] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:36:11.855] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.855] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.855] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384368896484375
[2025-06-26T11:36:11.855] [DEBUG] debug-file - runTaskFromQueue task cost before running: 635 ms 
[2025-06-26T11:36:11.855] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:36:11.858] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.858] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.858] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:36:11.860] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:36:11.860] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:36:11.860] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.860] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.865] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.2083740234375
[2025-06-26T11:36:11.865] [DEBUG] debug-file - runTaskFromQueue task cost before running: 646 ms 
[2025-06-26T11:36:11.866] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 7 ms 
[2025-06-26T11:36:11.868] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.868] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.869] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:36:11.869] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.870] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.870] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory -1.70318603515625
[2025-06-26T11:36:11.870] [DEBUG] debug-file - runTaskFromQueue task cost before running: 651 ms 
[2025-06-26T11:36:11.871] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 2 ms 
[2025-06-26T11:36:11.873] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:36:11.873] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:36:11.873] [DEBUG] debug-file - runTaskFromQueue task cost before running: 654 ms 
[2025-06-26T11:36:11.873] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:36:11.876] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.876] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.877] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:36:11.880] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:36:11.880] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:36:11.880] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.880] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.881] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.13399505615234375
[2025-06-26T11:36:11.882] [DEBUG] debug-file - runTaskFromQueue task cost before running: 662 ms 
[2025-06-26T11:36:11.883] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 5 ms 
[2025-06-26T11:36:11.886] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:11.887] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:11.896] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:36:11.934] [DEBUG] debug-file - session manager: binding session. socketId=-rENyyQbKFZjeFxeAAAT, threadId=1@9.
[2025-06-26T11:36:11.937] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -0.48946380615234375
[2025-06-26T11:36:13.379] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:36:13.380] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:36:13.391] [WARN] debug-file - BUILD FAILED in 2 s 172 ms 
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:36:13.392] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:36:13.394] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\src\main\resources cache by regenerate.
[2025-06-26T11:36:13.422] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:36:13.423] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:36:13.456] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:36:13.459] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:67 ms .
[2025-06-26T11:36:13.460] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:F:\e-wallet\harmony\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:36:13.463] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:36:13.466] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:7 ms .
[2025-06-26T11:36:13.467] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:36:13.471] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:36:13.473] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:7 ms .
[2025-06-26T11:36:13.481] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:36:13.484] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:36:13.489] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:36:13.490] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:36:13.492] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:36:13.519] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:36:13.521] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:36:13.522] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:36:13.523] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:36:13.523] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:51 ms .
[2025-06-26T11:36:13.554] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:36:13.556] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:36:14.555] [DEBUG] debug-file - session manager: set active socket. socketId=7M95ZsLDtAqacuKMAAAV
[2025-06-26T11:36:14.563] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:36:14.588] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:36:14.594] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-06-26T11:36:14.614] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:14.617] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:36:14.617] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:36:14.627] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:36:14.627] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:36:14.627] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:36:14.627] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:36:14.631] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:36:14.637] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:36:14.651] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:36:14.688] [DEBUG] debug-file - Sdk init in 49 ms 
[2025-06-26T11:36:14.728] [DEBUG] debug-file - Project task initialization takes 38 ms 
[2025-06-26T11:36:14.728] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:36:14.728] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:14.728] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:36:14.738] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:14.741] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:36:14.742] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:36:14.753] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:36:14.753] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:36:14.753] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:36:14.754] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:36:14.754] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:36:14.754] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:36:14.754] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:36:14.759] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:36:14.759] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:36:14.759] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:14.759] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:36:14.802] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 40 ms 
[2025-06-26T11:36:14.803] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:36:14.805] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:36:14.815] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:36:14.816] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:36:14.825] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:36:14.825] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:36:14.826] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:36:14.829] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:36:14.830] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:36:14.834] [DEBUG] debug-file - Configuration phase cost:230 ms 
[2025-06-26T11:36:14.837] [DEBUG] debug-file - Configuration task cost before running: 269 ms 
[2025-06-26T11:36:14.840] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.840] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.845] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:36:14.859] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 10 ms .
[2025-06-26T11:36:14.859] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.26876068115234375
[2025-06-26T11:36:14.863] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:36:14.867] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.867] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.868] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:36:14.871] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:36:14.871] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.11086273193359375
[2025-06-26T11:36:14.871] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:36:14.875] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.875] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.876] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:36:14.878] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-26T11:36:14.880] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .
[2025-06-26T11:36:14.880] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.097900390625
[2025-06-26T11:36:14.881] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:36:14.884] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.884] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.885] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:36:14.886] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.886] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.886] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:36:14.886] [DEBUG] debug-file - runTaskFromQueue task cost before running: 318 ms 
[2025-06-26T11:36:14.887] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 2 ms 
[2025-06-26T11:36:14.890] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.890] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.898] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:36:14.899] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:36:14.900] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:36:14.900] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0661773681640625
[2025-06-26T11:36:14.900] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:36:14.904] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.904] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.905] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:36:14.907] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:36:14.907] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05709075927734375
[2025-06-26T11:36:14.907] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:36:14.910] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.910] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.912] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:36:14.920] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .
[2025-06-26T11:36:14.920] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.189361572265625
[2025-06-26T11:36:14.922] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:36:14.924] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.925] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.926] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:36:14.930] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:36:14.931] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.931] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.931] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.069000244140625
[2025-06-26T11:36:14.932] [DEBUG] debug-file - runTaskFromQueue task cost before running: 364 ms 
[2025-06-26T11:36:14.934] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 3 ms 
[2025-06-26T11:36:14.937] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.937] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.943] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:36:14.978] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 17 ms .
[2025-06-26T11:36:14.979] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -2.7658538818359375
[2025-06-26T11:36:14.989] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:36:14.993] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:14.993] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:14.995] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:36:14.999] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:36:15.031] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 31 ms .
[2025-06-26T11:36:15.031] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.700714111328125
[2025-06-26T11:36:15.033] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-26T11:36:15.037] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.037] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.037] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:36:15.038] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.038] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.038] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384521484375
[2025-06-26T11:36:15.038] [DEBUG] debug-file - runTaskFromQueue task cost before running: 470 ms 
[2025-06-26T11:36:15.039] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:36:15.043] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.043] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.045] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:36:15.048] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .
[2025-06-26T11:36:15.048] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.095550537109375
[2025-06-26T11:36:15.049] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-26T11:36:15.053] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.053] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.055] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:36:15.056] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.056] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.056] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-26T11:36:15.056] [DEBUG] debug-file - runTaskFromQueue task cost before running: 488 ms 
[2025-06-26T11:36:15.057] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 2 ms 
[2025-06-26T11:36:15.060] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:36:15.060] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:36:15.060] [DEBUG] debug-file - runTaskFromQueue task cost before running: 492 ms 
[2025-06-26T11:36:15.061] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:36:15.064] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.064] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.066] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:36:15.070] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .
[2025-06-26T11:36:15.070] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.1045074462890625
[2025-06-26T11:36:15.072] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-26T11:36:15.076] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:36:15.077] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:36:15.086] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:36:15.115] [DEBUG] debug-file - session manager: binding session. socketId=7M95ZsLDtAqacuKMAAAV, threadId=1@10.
[2025-06-26T11:36:15.118] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.92779541015625
[2025-06-26T11:36:18.938] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:36:18.940] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:36:18.940] [DEBUG] debug-file - session manager: undefined socket. Type: WatchStart
[2025-06-26T11:36:21.467] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:36:21.470] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:36:29.724] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.726] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.726] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.725] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.727] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.728] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.727] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.729] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.730] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.728] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.731] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.732] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.730] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.733] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.733] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.732] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.734] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.735] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.734] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.736] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.737] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.736] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.738] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.738] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.738] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.740] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.740] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.739] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.744] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.745] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.744] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.749] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.750] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.749] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.751] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.752] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.751] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.753] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.754] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.753] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.755] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.756] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.755] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.757] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.758] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.756] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.759] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.760] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.761] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.762] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.759] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.763] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.764] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.761] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.765] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.766] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.763] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.767] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.768] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.764] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.769] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.770] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.766] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.771] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.771] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.768] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.773] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.773] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.770] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.775] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.775] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.772] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.777] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.777] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.773] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.778] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.779] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.775] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.781] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.781] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.777] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.783] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.783] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.779] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.785] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.785] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.781] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.787] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.787] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.783] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.788] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.789] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.784] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.790] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.791] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.786] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.792] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.793] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.787] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.794] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.795] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.788] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.796] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.796] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.790] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.798] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.798] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.791] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.800] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.800] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.793] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.802] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.802] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.796] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.803] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.804] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.798] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.806] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.806] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.799] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.807] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.808] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.801] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.810] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.810] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.803] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.813] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.813] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.805] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.815] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.816] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.806] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.817] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.818] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.808] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.844] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.844] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.810] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.845] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.845] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.812] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.846] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.846] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.815] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.888] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.889] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.817] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.890] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.891] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.819] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.892] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.893] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.821] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.894] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.895] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.823] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.896] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.896] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.825] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.905] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.905] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.826] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.906] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.906] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.828] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.907] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.907] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.830] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.908] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.908] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.832] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.909] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.909] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.834] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.910] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.911] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.836] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.912] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.912] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.837] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.913] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.913] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.839] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.914] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.915] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.841] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.915] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.916] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.842] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.916] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.917] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.844] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.918] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.918] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.846] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.919] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.919] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.847] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:29.921] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:29.921] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:36:29.849] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:36:29.873] [DEBUG] debug-file - Server currently has 1 watch-worker
[2025-06-26T11:36:32.476] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.479] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.478] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.482] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.482] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.486] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.486] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.489] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.489] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.492] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.492] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.496] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.495] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.499] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.498] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.502] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.502] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.505] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.505] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.511] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.511] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.516] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.516] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.522] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.522] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.528] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.528] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.531] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.531] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.534] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.534] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.537] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.537] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.540] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.539] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.543] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.543] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.546] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.545] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.549] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.548] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.551] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.551] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.554] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.554] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.558] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.557] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.561] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.561] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.565] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.564] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.570] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.570] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.574] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.573] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.577] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.576] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.580] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.579] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.583] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.582] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.586] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.586] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.589] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.589] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.593] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.592] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.596] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.595] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.602] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.602] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.605] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.605] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.609] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.609] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.612] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.612] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.615] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.615] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.619] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.619] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.624] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.623] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.629] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.627] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.633] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.633] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.638] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.637] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.641] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.640] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.645] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.645] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.649] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.649] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.652] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.652] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.656] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.655] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.659] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.659] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.662] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.662] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.666] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.665] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.669] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.669] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.673] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.673] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.677] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.677] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.682] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.681] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.686] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.686] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.689] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.689] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.692] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.692] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.696] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.695] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.699] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.699] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.703] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.703] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.706] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.706] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.710] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.709] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.713] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.713] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.719] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:32.718] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:32.723] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:33.119] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:36:33.122] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:36:33.133] [WARN] debug-file - Error [RollupError]: 'return' outside of function (Note that you need plugins to import files that are not JavaScript)
    at error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:210:30)
    at Module.error (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:13962:16)
    at Module.tryParse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14728:25)
    at Module.setSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14257:37)
    at ModuleLoader.addModuleSource (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:24000:20) {
  cause: SyntaxError: 'return' outside of function (402:8)
      at pp$4.raise (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:21395:13)
      at pp$8.parseReturnStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19008:12)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18844:37)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseIfStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19001:26)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18843:33)
      at pp$8.parseBlock (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseTopLevel (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18753:21) {
    pos: 15812,
    loc: Position { line: 402, column: 8 },
    raisedAt: 15818
  },
  code: 'PARSE_ERROR',
  id: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
  pos: 15812,
  loc: {
    column: 6,
    file: 'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    line: 230
  },
  frame: '228:   isLoginEnabled(): boolean {\n' +
    '229:     if (this.isLoading) {\n' +
    '230:       return false;\n' +
    '           ^\n' +
    '231:     }\n' +
    '232:     // 简化条件：只需要用户名和密码',
  watchFiles: [
    'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt',
    'F:\\e-wallet\\harmony\\entry\\oh-package.json5',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\MyBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransferPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\EditBankCardPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\RechargePage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WithdrawPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\PaymentCenterPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\QRCodePaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\NFCPaymentPage.ets',
    'F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TestPaymentPage.ets'
  ]
}
[2025-06-26T11:36:33.135] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:36:33.138] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:36:33.139] [DEBUG] debug-file - default@PreviewArkTS watch work[10] failed.
[2025-06-26T11:36:33.139] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-26T11:36:33.139] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-26T11:36:33.142] [DEBUG] debug-file - ERROR: stacktrace = Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:36:33.143] [ERROR] debug-file - Error: Compilation failed
    at handleResponse (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (E:\hongmeng\HM\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-26T11:36:33.160] [WARN] debug-file - BUILD FAILED in 18 s 590 ms 
[2025-06-26T11:36:33.160] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:36:33.161] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:36:33.161] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:36:33.161] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:36:33.161] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:36:33.162] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:36:33.162] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:36:33.162] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-26T11:36:33.162] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-26T11:36:33.163] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-26T11:36:33.173] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:36:33.176] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:36:33.177] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:36:33.178] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:36:33.180] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:36:33.210] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:36:33.211] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:36:33.212] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:36:33.213] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:36:33.214] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:52 ms .
[2025-06-26T11:36:33.292] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:36:33.294] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:36:33.305] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:36:33.306] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:36:33.137] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:36:36.125] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:36:36.125] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:37:07.976] [DEBUG] debug-file - session manager: set active socket. socketId=eQ18EBV7hbwjUX3WAAAX
[2025-06-26T11:37:07.983] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:37:08.003] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:37:08.007] [DEBUG] debug-file - Cache service initialization finished in 5 ms 
[2025-06-26T11:37:08.025] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:37:08.032] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:37:08.032] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:37:08.040] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:37:08.040] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:37:08.040] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:37:08.041] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:37:08.042] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:37:08.048] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:37:08.058] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:37:08.088] [DEBUG] debug-file - Sdk init in 40 ms 
[2025-06-26T11:37:08.120] [DEBUG] debug-file - Project task initialization takes 29 ms 
[2025-06-26T11:37:08.120] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:37:08.120] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:37:08.120] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\hvigorfile.ts
[2025-06-26T11:37:08.131] [DEBUG] debug-file - hvigorfile, resolving F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:37:08.135] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:37:08.136] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:37:08.144] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:37:08.144] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:37:08.145] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:37:08.145] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:37:08.145] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:37:08.145] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:37:08.145] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:37:08.149] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:37:08.149] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:37:08.149] [DEBUG] debug-file - hvigorfile, no custom plugins were found in F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:37:08.150] [DEBUG] debug-file - hvigorfile, resolve finished F:\e-wallet\harmony\entry\hvigorfile.ts
[2025-06-26T11:37:08.177] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 25 ms 
[2025-06-26T11:37:08.178] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:37:08.179] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:37:08.185] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:37:08.186] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:37:08.193] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-26T11:37:08.193] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-26T11:37:08.193] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:37:08.196] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-26T11:37:08.196] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-26T11:37:08.199] [DEBUG] debug-file - Configuration phase cost:185 ms 
[2025-06-26T11:37:08.200] [DEBUG] debug-file - Configuration task cost before running: 213 ms 
[2025-06-26T11:37:08.203] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.203] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.206] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:37:08.216] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 7 ms .
[2025-06-26T11:37:08.216] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.2727203369140625
[2025-06-26T11:37:08.219] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:37:08.221] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.221] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.222] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:37:08.224] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-26T11:37:08.224] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.11063385009765625
[2025-06-26T11:37:08.224] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:37:08.227] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.227] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.228] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:37:08.229] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:37:08.230] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:37:08.230] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09618377685546875
[2025-06-26T11:37:08.231] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:37:08.233] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.233] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.234] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:37:08.234] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.234] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.235] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.037322998046875
[2025-06-26T11:37:08.235] [DEBUG] debug-file - runTaskFromQueue task cost before running: 247 ms 
[2025-06-26T11:37:08.235] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:37:08.238] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.238] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.249] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:37:08.249] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:37:08.250] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:37:08.251] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06561279296875
[2025-06-26T11:37:08.251] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:37:08.255] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.255] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.257] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:37:08.259] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:37:08.259] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05654144287109375
[2025-06-26T11:37:08.259] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:37:08.264] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.264] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.268] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:37:08.278] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .
[2025-06-26T11:37:08.278] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.188079833984375
[2025-06-26T11:37:08.280] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:37:08.284] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.284] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.286] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:37:08.292] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:37:08.292] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.292] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.292] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.0684051513671875
[2025-06-26T11:37:08.294] [DEBUG] debug-file - runTaskFromQueue task cost before running: 306 ms 
[2025-06-26T11:37:08.296] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 3 ms 
[2025-06-26T11:37:08.299] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.300] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.304] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:37:08.327] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .
[2025-06-26T11:37:08.327] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -1.0028839111328125
[2025-06-26T11:37:08.333] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:37:08.336] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.336] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.337] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:37:08.340] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:37:08.368] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T11:37:08.368] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 28 ms .
[2025-06-26T11:37:08.389] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\AppScope\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:37:08.391] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 110772224,
  heapTotal: 122249216,
  heapUsed: 111356336,
  external: 3108429,
  arrayBuffers: 102294
} os memoryUsage :6.328525543212891
[2025-06-26T11:37:08.417] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:37:08.420] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'F:\\e-wallet\\harmony\\entry\\src\\main\\resources',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:37:08.423] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 110776320,
  heapTotal: 122249216,
  heapUsed: 111620096,
  external: 3108555,
  arrayBuffers: 102435
} os memoryUsage :6.338932037353516
[2025-06-26T11:37:08.475] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:37:08.479] [DEBUG] debug-file - Use tool [E:\hongmeng\HM\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***y',
  '-r',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:37:08.482] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 110780416,
  heapTotal: 122249216,
  heapUsed: 111908000,
  external: 3108681,
  arrayBuffers: 103375
} os memoryUsage :6.307151794433594
[2025-06-26T11:37:08.581] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:37:08.586] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.0105133056640625
[2025-06-26T11:37:08.586] [DEBUG] debug-file - runTaskFromQueue task cost before running: 599 ms 
[2025-06-26T11:37:08.588] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 247 ms 
[2025-06-26T11:37:08.591] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.591] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.591] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:37:08.591] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.591] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.592] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.0384521484375
[2025-06-26T11:37:08.592] [DEBUG] debug-file - runTaskFromQueue task cost before running: 604 ms 
[2025-06-26T11:37:08.592] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:37:08.595] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.595] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.596] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:37:08.598] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:37:08.598] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:37:08.598] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.598] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.605] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.20832061767578125
[2025-06-26T11:37:08.606] [DEBUG] debug-file - runTaskFromQueue task cost before running: 618 ms 
[2025-06-26T11:37:08.606] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 10 ms 
[2025-06-26T11:37:08.610] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.610] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.612] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:37:08.612] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.612] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.612] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03838348388671875
[2025-06-26T11:37:08.613] [DEBUG] debug-file - runTaskFromQueue task cost before running: 626 ms 
[2025-06-26T11:37:08.613] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 2 ms 
[2025-06-26T11:37:08.617] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:37:08.617] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:37:08.617] [DEBUG] debug-file - runTaskFromQueue task cost before running: 629 ms 
[2025-06-26T11:37:08.617] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:37:08.622] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.622] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.623] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:37:08.627] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:37:08.627] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .
[2025-06-26T11:37:08.627] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.628] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.629] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.13263702392578125
[2025-06-26T11:37:08.629] [DEBUG] debug-file - runTaskFromQueue task cost before running: 642 ms 
[2025-06-26T11:37:08.631] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 7 ms 
[2025-06-26T11:37:08.635] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-26T11:37:08.635] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-26T11:37:08.647] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:37:08.690] [DEBUG] debug-file - session manager: binding session. socketId=eQ18EBV7hbwjUX3WAAAX, threadId=1@11.
[2025-06-26T11:37:08.693] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -0.552825927734375
[2025-06-26T11:37:14.295] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:37:14.298] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:37:27.073] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:37:27.074] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:37:27.075] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:37:27.097] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 18 s 447 ms 
[2025-06-26T11:37:27.099] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:37:27.099] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.0116729736328125
[2025-06-26T11:37:27.099] [DEBUG] debug-file - runTaskFromQueue task cost before running: 19 s 112 ms 
[2025-06-26T11:37:27.099] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:37:27.107] [DEBUG] debug-file - BUILD SUCCESSFUL in 19 s 119 ms 
[2025-06-26T11:37:27.107] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:37:27.107] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:37:27.107] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:37:27.107] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:37:27.107] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:37:27.108] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:37:27.108] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:37:27.108] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\src\main\resources cache by regenerate.
[2025-06-26T11:37:27.117] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:37:27.117] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:37:27.129] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:F:\e-wallet\harmony\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:37:27.130] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .
[2025-06-26T11:37:27.130] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:F:\e-wallet\harmony\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:37:27.131] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:37:27.132] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-26T11:37:27.132] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:37:27.133] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:37:27.133] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .
[2025-06-26T11:37:27.136] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:37:27.137] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:37:27.137] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:37:27.137] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:37:27.138] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-26T11:37:27.150] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:37:27.150] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:37:27.151] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:F:\e-wallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:37:27.152] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:F:\e-wallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:37:27.152] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .
[2025-06-26T11:37:27.166] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:37:27.166] [DEBUG] debug-file - hvigor build process will be closed.
