<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="7baab544-cb14-4501-8daf-fd9376a64183" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="ArkTS File" />
      </list>
    </option>
  </component>
  <component name="HiLogStateProjectLevelPreference">
    <panelStates>
      <hilogPanelState>
        <option name="logPanelType" value="ONLINE" />
        <option name="processName" value="com.example.harmony" />
      </hilogPanelState>
    </panelStates>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/entry/src/main/ets/pages/LoginPage.ets" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/entry/src/main/ets/pages/MyBankCardPage.ets" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2ycjEl7bg2CSEOQQjUf9K5qn0Yc" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;127.0.0.1:5555&quot;: &quot;5369592461750591014496&quot;,
    &quot;Application.entry.executor&quot;: &quot;Run&quot;,
    &quot;MODULE_HAP_TIME&quot;: &quot;{\&quot;127.0.0.1:5555entry\&quot;:\&quot;2025-06-20T09:26:20.111649100Z\&quot;}&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;ace.nodejs.version&quot;: &quot;18.20.1&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/e-wallet/harmony&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\e-wallet\harmony\entry\src\main\resources\base\media" />
    </key>
  </component>
  <component name="RunManager" selected="Application.entry">
    <configuration name="entry" type="HotReLoadTask" factoryName="Hot Reload">
      <MODULE_NAME />
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <HOT_RELOAD_MODULE_NAME>entry</HOT_RELOAD_MODULE_NAME>
      <method v="2">
        <option name="Build.Hvigor.HotReloadBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="entry" type="OhosDebugTask" factoryName="OpenHarmony App">
      <MODULE_NAME>entry</MODULE_NAME>
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <method v="2">
        <option name="Build.Hvigor.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用级" UseSingleDictionary="true" transferred="true" />
</project>