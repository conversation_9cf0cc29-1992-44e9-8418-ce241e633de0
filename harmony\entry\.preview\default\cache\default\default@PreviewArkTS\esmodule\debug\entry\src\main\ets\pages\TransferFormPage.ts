if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransferFormPage_Params {
    amount?: string;
    targetAccount?: string;
    selectedCardId?: number;
    password?: string;
    bankCards?: BankCard[];
    isLoading?: boolean;
    showCardSelector?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { BankCard } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class TransferFormPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__targetAccount = new ObservedPropertySimplePU('', this, "targetAccount");
        this.__selectedCardId = new ObservedPropertySimplePU(0, this, "selectedCardId");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransferFormPage_Params) {
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.targetAccount !== undefined) {
            this.targetAccount = params.targetAccount;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
    }
    updateStateVars(params: TransferFormPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__targetAccount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__amount.aboutToBeDeleted();
        this.__targetAccount.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __targetAccount: ObservedPropertySimplePU<string>;
    get targetAccount() {
        return this.__targetAccount.get();
    }
    set targetAccount(newValue: string) {
        this.__targetAccount.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    aboutToAppear() {
        this.loadBankCards();
    }
    async loadBankCards() {
        try {
            const userId = 1; // 临时使用固定用户ID
            this.bankCards = await BankCardApi.getCardList(userId, true);
        }
        catch (error) {
            console.error('加载银行卡失败:', error);
            promptAction.showToast({ message: '加载银行卡失败' });
            // 添加测试数据
            this.bankCards = [
                {
                    cardId: 1,
                    userId: 1,
                    cardNo: '6217000010001234567',
                    bankName: '中国银行',
                    cardType: BankCardType.DEBIT,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard,
                {
                    cardId: 2,
                    userId: 1,
                    cardNo: '6228480010001234567',
                    bankName: '工商银行',
                    cardType: BankCardType.DEBIT,
                    holderName: '张三',
                    isBound: BankCardStatus.BOUND,
                    createTime: '2024-01-01 00:00:00',
                    updateTime: '2024-01-01 00:00:00'
                } as BankCard
            ];
        }
    }
    async handleTransfer() {
        if (!this.amount || !this.targetAccount || !this.selectedCardId || !this.password) {
            promptAction.showToast({ message: '请填写完整信息' });
            return;
        }
        const transferAmount = parseFloat(this.amount);
        if (transferAmount <= 0) {
            promptAction.showToast({ message: '转账金额必须大于0' });
            return;
        }
        try {
            this.isLoading = true;
            // 调用转账API
            const userId = 1;
            await TransactionApi.transfer({
                fromUserId: userId,
                targetAccount: this.targetAccount,
                amount: transferAmount,
                paymentMethod: 'BANK_CARD',
                cardId: this.selectedCardId
            });
            promptAction.showToast({ message: '转账成功' });
            // 重置表单
            this.amount = '';
            this.targetAccount = '';
            this.selectedCardId = 0;
            this.password = '';
            // 跳转到交易记录页面查看新的交易记录
            router.replaceUrl({
                url: 'pages/TransactionListPage'
            });
        }
        catch (error) {
            console.error('转账失败:', error);
            promptAction.showToast({ message: '转账失败' });
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(104:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
            Column.bindSheet({ value: this.showCardSelector, changeEvent: newValue => { this.showCardSelector = newValue; } }, { builder: () => {
                    this.BankCardSelector.call(this);
                } }, {
                height: 400,
                dragBar: true
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(106:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(60);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(107:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#667EEA');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => router.back());
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(113:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#1A1A1A');
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(120:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(128:7)", "entry");
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(129:9)", "entry");
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(131:11)", "entry");
            // 转账金额
            Column.width('100%');
            // 转账金额
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账金额');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(132:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转账金额' });
            TextInput.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(139:13)", "entry");
            TextInput.fontSize(16);
            TextInput.height(50);
            TextInput.borderRadius(12);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E7EB' });
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        // 转账金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收款账户
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(153:11)", "entry");
            // 收款账户
            Column.width('100%');
            // 收款账户
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收款账户');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(154:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收款银行卡号' });
            TextInput.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(161:13)", "entry");
            TextInput.fontSize(16);
            TextInput.height(50);
            TextInput.borderRadius(12);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E7EB' });
            TextInput.onChange((value: string) => {
                this.targetAccount = value;
            });
        }, TextInput);
        // 收款账户
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 付款银行卡选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(175:11)", "entry");
            // 付款银行卡选择
            Column.width('100%');
            // 付款银行卡选择
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('付款银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(176:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.getSelectedCardText());
            Button.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(183:13)", "entry");
            Button.fontSize(16);
            Button.fontColor('#333333');
            Button.backgroundColor('#F8F9FA');
            Button.border({ width: 1, color: '#E5E7EB' });
            Button.borderRadius(12);
            Button.height(50);
            Button.width('100%');
            Button.onClick(() => {
                this.showCardSelector = true;
            });
        }, Button);
        Button.pop();
        // 付款银行卡选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(199:11)", "entry");
            // 支付密码
            Column.width('100%');
            // 支付密码
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(200:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(207:13)", "entry");
            TextInput.fontSize(16);
            TextInput.height(50);
            TextInput.borderRadius(12);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E5E7EB' });
            TextInput.type(InputType.Password);
            TextInput.onChange((value: string) => {
                this.password = value;
            });
        }, TextInput);
        // 支付密码
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账按钮
            Button.createWithLabel('确认转账');
            Button.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(222:11)", "entry");
            // 转账按钮
            Button.fontSize(18);
            // 转账按钮
            Button.fontColor('#FFFFFF');
            // 转账按钮
            Button.backgroundColor('#667EEA');
            // 转账按钮
            Button.borderRadius(12);
            // 转账按钮
            Button.height(50);
            // 转账按钮
            Button.width('100%');
            // 转账按钮
            Button.enabled(!this.isLoading);
            // 转账按钮
            Button.onClick(() => {
                this.handleTransfer();
            });
        }, Button);
        // 转账按钮
        Button.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    BankCardSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(249:5)", "entry");
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择付款银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(250:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const card = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(256:9)", "entry");
                    Row.width('100%');
                    Row.padding(16);
                    Row.backgroundColor(this.selectedCardId === card.cardId ? '#667EEA15' : '#FFFFFF');
                    Row.borderRadius(12);
                    Row.margin({ bottom: 8 });
                    Row.onClick(() => {
                        this.selectedCardId = card.cardId;
                        this.showCardSelector = false;
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(257:11)", "entry");
                    Column.layoutWeight(1);
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(card.bankName);
                    Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(258:13)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#333333');
                    Text.fontWeight(FontWeight.Medium);
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(`****${card.cardNo.slice(-4)}`);
                    Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(264:13)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#666666');
                    Text.margin({ top: 4 });
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.selectedCardId === card.cardId) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('✓');
                                Text.debugLine("entry/src/main/ets/pages/TransferFormPage.ets(274:13)", "entry");
                                Text.fontSize(20);
                                Text.fontColor('#667EEA');
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Column.pop();
    }
    private getSelectedCardText(): string {
        if (this.selectedCardId === 0) {
            return '请选择付款银行卡';
        }
        const selectedCard = this.bankCards.find(card => card.cardId === this.selectedCardId);
        if (selectedCard) {
            return `${selectedCard.bankName} ****${selectedCard.cardNo.slice(-4)}`;
        }
        return '请选择付款银行卡';
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransferFormPage";
    }
}
registerNamedRoute(() => new TransferFormPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransferFormPage", pageFullPath: "entry/src/main/ets/pages/TransferFormPage", integratedHsp: "false", moduleType: "followWithHap" });
