{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "5c4c3ad7-9522-4edc-bc36-7ab4b3de434c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14109889416100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad975ab-d407-4f28-b1d6-86b5e57ae5ce", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14296065551700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c95854d-f562-44db-97cf-ff7c477bde93", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14296149905600, "endTime": 14296149931200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "1b4f41e3-75c6-4055-b1ff-fb5230b88381"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b4f41e3-75c6-4055-b1ff-fb5230b88381", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14296149905600, "endTime": 14296149931200}, "additional": {"logType": "info", "children": [], "durationId": "1c95854d-f562-44db-97cf-ff7c477bde93"}}, {"head": {"id": "4401b484-9ed3-45fc-9edd-ff234bedc186", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300430722600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1594d445-0a9e-4c30-a93b-8724e6dd62d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300432064200, "endTime": 14300432084600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "0db6498b-8ad8-4644-85f6-a95b25c77bff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0db6498b-8ad8-4644-85f6-a95b25c77bff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300432064200, "endTime": 14300432084600}, "additional": {"logType": "info", "children": [], "durationId": "1594d445-0a9e-4c30-a93b-8724e6dd62d8"}}, {"head": {"id": "7ed6437a-a5f1-4f17-aeaf-8964f88ec5b1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300432192500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19908c5-479b-49eb-b0f4-c8498d5fcb4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300433379900, "endTime": 14300433405100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "edcaf60b-aad4-42a3-82a3-aa1f8d22aa58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edcaf60b-aad4-42a3-82a3-aa1f8d22aa58", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300433379900, "endTime": 14300433405100}, "additional": {"logType": "info", "children": [], "durationId": "c19908c5-479b-49eb-b0f4-c8498d5fcb4d"}}, {"head": {"id": "49f86cd2-7b44-477f-a20a-fef753fc8747", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300433533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41288ab0-6b52-4724-a355-aa0b36011964", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300435244000, "endTime": 14300435282700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "f448afd5-31ff-44e0-918c-0097b6bfaccd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f448afd5-31ff-44e0-918c-0097b6bfaccd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300435244000, "endTime": 14300435282700}, "additional": {"logType": "info", "children": [], "durationId": "41288ab0-6b52-4724-a355-aa0b36011964"}}, {"head": {"id": "18309aea-7155-4423-b734-d11a254cea92", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300435452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0995464-d067-4a2f-b7af-3f1541595892", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300437088700, "endTime": 14300437121100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "bc6d620b-c3e6-4ec8-9140-89963a283228"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc6d620b-c3e6-4ec8-9140-89963a283228", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300437088700, "endTime": 14300437121100}, "additional": {"logType": "info", "children": [], "durationId": "b0995464-d067-4a2f-b7af-3f1541595892"}}, {"head": {"id": "bc78951e-1ff7-4482-afdf-37fabcbd91f0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300437903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487cbb02-a5f9-45a4-97d6-c7f2cf0a509b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300441643700, "endTime": 14300441686600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "ed8aec26-74ec-4390-9db1-0314d1e8a464"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed8aec26-74ec-4390-9db1-0314d1e8a464", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14300441643700, "endTime": 14300441686600}, "additional": {"logType": "info", "children": [], "durationId": "487cbb02-a5f9-45a4-97d6-c7f2cf0a509b"}}, {"head": {"id": "be9b2d21-3e6c-43c3-b4ae-48be8222c2d5", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14302475671500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bb6adb-ccc6-49c1-bdb9-db3d35ac5c65", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14302476880300, "endTime": 14302476905700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "69ed27dc-8679-4789-8d38-0d23601fd406"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69ed27dc-8679-4789-8d38-0d23601fd406", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14302476880300, "endTime": 14302476905700}, "additional": {"logType": "info", "children": [], "durationId": "b4bb6adb-ccc6-49c1-bdb9-db3d35ac5c65"}}, {"head": {"id": "9b984609-6802-4a0c-a147-5c7009eda214", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14337595508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c71dee4-3895-4fe9-ba49-42efb9364dcd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14337597238400, "endTime": 14337597278500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "87b30310-cb6b-4fd6-8bd6-ca1f7994e76b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87b30310-cb6b-4fd6-8bd6-ca1f7994e76b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14337597238400, "endTime": 14337597278500}, "additional": {"logType": "info", "children": [], "durationId": "3c71dee4-3895-4fe9-ba49-42efb9364dcd"}}, {"head": {"id": "0eed9103-2d81-4c12-842e-171a9ae1483f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338829385000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb20ac1-a282-4623-aa91-b3c9de492307", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338831157500, "endTime": 14338831180400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d08ef7ca-4a11-4a5b-adc7-ca07ca619b62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d08ef7ca-4a11-4a5b-adc7-ca07ca619b62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338831157500, "endTime": 14338831180400}, "additional": {"logType": "info", "children": [], "durationId": "cdb20ac1-a282-4623-aa91-b3c9de492307"}}, {"head": {"id": "f85daa0a-198a-4ecb-9476-9ac7cf12eda6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338831306900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e982cdf-988f-4d3b-b578-fb918764fccf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338832707600, "endTime": 14338832732900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "245eb409-615c-4773-862d-86799786a423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "245eb409-615c-4773-862d-86799786a423", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338832707600, "endTime": 14338832732900}, "additional": {"logType": "info", "children": [], "durationId": "6e982cdf-988f-4d3b-b578-fb918764fccf"}}, {"head": {"id": "6e1762d0-cacd-4b39-b68e-f344421d8dca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338832874100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7f8752-0c7c-4f28-93cd-b4bca7484d75", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338834595200, "endTime": 14338834630500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "66f79857-19a9-48cd-b740-a0cbc400bdbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66f79857-19a9-48cd-b740-a0cbc400bdbd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14338834595200, "endTime": 14338834630500}, "additional": {"logType": "info", "children": [], "durationId": "9c7f8752-0c7c-4f28-93cd-b4bca7484d75"}}, {"head": {"id": "6760eae4-ac87-4fb9-8ec1-49ff92de4f5a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14339406038400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75341d69-e2ef-4c6d-b66d-824c5b4eea4e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14339407430500, "endTime": 14339407453100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "23f86371-f3a7-4571-9e69-acbf45a24e0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23f86371-f3a7-4571-9e69-acbf45a24e0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14339407430500, "endTime": 14339407453100}, "additional": {"logType": "info", "children": [], "durationId": "75341d69-e2ef-4c6d-b66d-824c5b4eea4e"}}, {"head": {"id": "6d98b01e-47a3-49cc-a365-8f29031da196", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14355043414200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "771fd8ba-70ae-45c5-9e6d-f7b3b811ad14", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14355044952600, "endTime": 14355044972700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "11463c61-a90f-4e8e-b142-5d0164a67753"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11463c61-a90f-4e8e-b142-5d0164a67753", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14355044952600, "endTime": 14355044972700}, "additional": {"logType": "info", "children": [], "durationId": "771fd8ba-70ae-45c5-9e6d-f7b3b811ad14"}}, {"head": {"id": "33b0a75a-00fa-4f8f-94d8-7ce9a49ef64b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356408895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed67749-276e-429b-ae73-90f3466c59c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356410020000, "endTime": 14356410038500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "46079420-c242-4964-be92-e3bc1796b24f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46079420-c242-4964-be92-e3bc1796b24f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356410020000, "endTime": 14356410038500}, "additional": {"logType": "info", "children": [], "durationId": "fed67749-276e-429b-ae73-90f3466c59c8"}}, {"head": {"id": "67a2a6c4-83cf-4cd5-879a-8aab0814a0e4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356410127900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16dd3f46-dd77-43f2-923d-48d5cac630fa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356411806100, "endTime": 14356411827600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "3c754a75-0859-466d-9deb-98e0bf8f2664"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c754a75-0859-466d-9deb-98e0bf8f2664", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356411806100, "endTime": 14356411827600}, "additional": {"logType": "info", "children": [], "durationId": "16dd3f46-dd77-43f2-923d-48d5cac630fa"}}, {"head": {"id": "60e37766-4821-4e40-85ba-aaf160869b4b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356411964700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3520c82e-2280-4324-bacb-766380a4535e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356413421100, "endTime": 14356413449100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "491e3db9-48d8-44b3-9d4d-8b9c3050e63b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "491e3db9-48d8-44b3-9d4d-8b9c3050e63b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356413421100, "endTime": 14356413449100}, "additional": {"logType": "info", "children": [], "durationId": "3520c82e-2280-4324-bacb-766380a4535e"}}, {"head": {"id": "2d0711e0-7e27-445c-b6fc-36d6975592c5", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356808288500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c1789f-6598-4a32-8d5e-4a51b294cbcd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356809604500, "endTime": 14356809627600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "a8a99a27-bb55-4bf0-bb14-1f7dd0bd973d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8a99a27-bb55-4bf0-bb14-1f7dd0bd973d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14356809604500, "endTime": 14356809627600}, "additional": {"logType": "info", "children": [], "durationId": "78c1789f-6598-4a32-8d5e-4a51b294cbcd"}}, {"head": {"id": "ad5726e2-041c-40cb-b33b-83c076845dad", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14385526936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8bc920d-06a1-4704-8328-d77a6c602be1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14385529122900, "endTime": 14385529152100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "5611da6e-7adb-43ec-80de-78e30c7adee8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5611da6e-7adb-43ec-80de-78e30c7adee8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14385529122900, "endTime": 14385529152100}, "additional": {"logType": "info", "children": [], "durationId": "c8bc920d-06a1-4704-8328-d77a6c602be1"}}, {"head": {"id": "b8866e9b-e5fd-49b7-98d2-f95ed1e3e8ee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386684906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d4df0d-cfa3-4f50-aae9-fee8406e8d40", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386686364200, "endTime": 14386686391900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "3ff7a874-d7b3-4449-a44e-3eb9fb8db734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ff7a874-d7b3-4449-a44e-3eb9fb8db734", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386686364200, "endTime": 14386686391900}, "additional": {"logType": "info", "children": [], "durationId": "b9d4df0d-cfa3-4f50-aae9-fee8406e8d40"}}, {"head": {"id": "0a34ee32-1c22-441f-b8fd-ca76fc70f46b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386686531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c2786c-54e2-49f0-9be0-d1152ef49648", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386688077800, "endTime": 14386688102600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "cb395787-76ad-4a34-8a56-15ec0813b3b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb395787-76ad-4a34-8a56-15ec0813b3b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386688077800, "endTime": 14386688102600}, "additional": {"logType": "info", "children": [], "durationId": "31c2786c-54e2-49f0-9be0-d1152ef49648"}}, {"head": {"id": "0070c06a-9b83-46fc-ae6c-f3f12be87b35", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386688260300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b41e17d-d03c-483b-a1fb-bbd375caf318", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386690966800, "endTime": 14386691026800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "bd4098fb-f05d-4f0b-ae0f-032911b22ab0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd4098fb-f05d-4f0b-ae0f-032911b22ab0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14386690966800, "endTime": 14386691026800}, "additional": {"logType": "info", "children": [], "durationId": "0b41e17d-d03c-483b-a1fb-bbd375caf318"}}, {"head": {"id": "7bcb2375-825e-457b-87ce-a9a2bc7cf13e", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14387083479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a011d2c8-20c3-4a98-ba56-fbb83d54945c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14387084734300, "endTime": 14387084754300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "8a2fe928-6813-422e-adcc-8d60eccfe358"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a2fe928-6813-422e-adcc-8d60eccfe358", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14387084734300, "endTime": 14387084754300}, "additional": {"logType": "info", "children": [], "durationId": "a011d2c8-20c3-4a98-ba56-fbb83d54945c"}}, {"head": {"id": "cdc8c170-9929-4ba0-91c8-473b5f4842cd", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14512063506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9530b63b-7a2a-4599-8276-f6d1a21e9b2f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14512064596300, "endTime": 14512064616300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "db32c0f8-cf53-4e05-b701-9f1e6a6a4434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db32c0f8-cf53-4e05-b701-9f1e6a6a4434", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14512064596300, "endTime": 14512064616300}, "additional": {"logType": "info", "children": [], "durationId": "9530b63b-7a2a-4599-8276-f6d1a21e9b2f"}}, {"head": {"id": "0f2aca31-c0fb-4188-98b8-bd0fcac7dedf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513089670600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a4ae5b-a1ea-48de-a4ef-c948e2921d21", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513090758900, "endTime": 14513090776400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "16ce5a78-cd3f-48b7-a750-52dfaebc596e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16ce5a78-cd3f-48b7-a750-52dfaebc596e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513090758900, "endTime": 14513090776400}, "additional": {"logType": "info", "children": [], "durationId": "20a4ae5b-a1ea-48de-a4ef-c948e2921d21"}}, {"head": {"id": "d3cecc2a-615e-42a8-8698-e1b968590222", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513090858000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316d1d4f-e3f4-41a4-a91f-0566ac0a01ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513092342600, "endTime": 14513092368300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "ffc74119-c809-449c-88da-c0a591e931b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffc74119-c809-449c-88da-c0a591e931b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513092342600, "endTime": 14513092368300}, "additional": {"logType": "info", "children": [], "durationId": "316d1d4f-e3f4-41a4-a91f-0566ac0a01ad"}}, {"head": {"id": "a1bdb972-36e5-4ba1-86d3-0b3d1a26aec3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513092504100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7c7b20-284d-46a1-929a-50f7b444fe75", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513094105600, "endTime": 14513094140700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "604ce5e1-4825-4b37-8d17-1e32fc779747"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "604ce5e1-4825-4b37-8d17-1e32fc779747", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513094105600, "endTime": 14513094140700}, "additional": {"logType": "info", "children": [], "durationId": "2a7c7b20-284d-46a1-929a-50f7b444fe75"}}, {"head": {"id": "b462ded1-f770-48b6-823d-32c046ca3ad7", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513497955900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ba3087-1a9b-413b-9129-1677100f0240", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513499147700, "endTime": 14513499169000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "6cf4ea6c-f2ed-4dc6-bbab-c91929517c91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cf4ea6c-f2ed-4dc6-bbab-c91929517c91", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14513499147700, "endTime": 14513499169000}, "additional": {"logType": "info", "children": [], "durationId": "54ba3087-1a9b-413b-9129-1677100f0240"}}, {"head": {"id": "a0956a42-a1a1-4c28-972c-c788e4b41522", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14599760354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4af0d61-1858-4458-84b7-3b939b199780", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14599762111400, "endTime": 14599762140500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "1fc8130d-269e-4a85-b37b-1f697316d13b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fc8130d-269e-4a85-b37b-1f697316d13b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14599762111400, "endTime": 14599762140500}, "additional": {"logType": "info", "children": [], "durationId": "e4af0d61-1858-4458-84b7-3b939b199780"}}, {"head": {"id": "62304aae-46a6-4972-83e2-bc980d87e938", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600813516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9886f7c-f3ab-4bfb-b3e0-37174481c5d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600815488600, "endTime": 14600815510000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "df6c3e73-1f05-4c2c-b684-0ac7a6f1b316"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df6c3e73-1f05-4c2c-b684-0ac7a6f1b316", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600815488600, "endTime": 14600815510000}, "additional": {"logType": "info", "children": [], "durationId": "e9886f7c-f3ab-4bfb-b3e0-37174481c5d9"}}, {"head": {"id": "72019002-6488-491d-ab73-567e1c37b7ae", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600839886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e3c502-ac3c-40c1-b883-9f15ed4a7e2a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600841748300, "endTime": 14600841773800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "8234afff-7f68-4aa2-90b3-90cb786c9958"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8234afff-7f68-4aa2-90b3-90cb786c9958", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600841748300, "endTime": 14600841773800}, "additional": {"logType": "info", "children": [], "durationId": "77e3c502-ac3c-40c1-b883-9f15ed4a7e2a"}}, {"head": {"id": "26038c35-19ec-4657-9eb5-a25e88fd5b46", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600841901900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c7ebdeb-0184-47aa-a202-d8f365386dbb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600843509700, "endTime": 14600843541700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "773942f6-ac0d-4423-bbbd-65caa7ad5969"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "773942f6-ac0d-4423-bbbd-65caa7ad5969", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14600843509700, "endTime": 14600843541700}, "additional": {"logType": "info", "children": [], "durationId": "8c7ebdeb-0184-47aa-a202-d8f365386dbb"}}, {"head": {"id": "69125d06-2441-4e21-a67e-ca57507bcf7f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14601222842300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61949a75-199d-45fd-980e-d11f90056b10", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14601224342300, "endTime": 14601224364800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "43b429aa-46df-459f-9520-89477c415894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43b429aa-46df-459f-9520-89477c415894", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14601224342300, "endTime": 14601224364800}, "additional": {"logType": "info", "children": [], "durationId": "61949a75-199d-45fd-980e-d11f90056b10"}}, {"head": {"id": "9266dba5-57da-48e4-9e14-d310d1392f6e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14647570406600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c88bc86-5fba-4b30-9f28-668ecd0b5e46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14647572192200, "endTime": 14647572230400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "55a4e319-9c21-4629-9987-e0e81af2a1ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55a4e319-9c21-4629-9987-e0e81af2a1ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14647572192200, "endTime": 14647572230400}, "additional": {"logType": "info", "children": [], "durationId": "7c88bc86-5fba-4b30-9f28-668ecd0b5e46"}}, {"head": {"id": "5c9e7d88-c638-46f3-8c6a-df803edef54f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649065634700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e7fea8-6ea2-421b-8b6e-90f9548a3e38", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649066753700, "endTime": 14649066777500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "e3a838ee-6f81-49fa-867d-6063caeeeecb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3a838ee-6f81-49fa-867d-6063caeeeecb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649066753700, "endTime": 14649066777500}, "additional": {"logType": "info", "children": [], "durationId": "31e7fea8-6ea2-421b-8b6e-90f9548a3e38"}}, {"head": {"id": "f4ba17e9-85ad-464f-a920-fbf247f565b8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649066871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635b7610-5a3e-4541-bfdb-6d155f6e6710", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649067755200, "endTime": 14649067777700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d78c7afb-92dc-4931-b0b1-e29d50d6c88b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d78c7afb-92dc-4931-b0b1-e29d50d6c88b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649067755200, "endTime": 14649067777700}, "additional": {"logType": "info", "children": [], "durationId": "635b7610-5a3e-4541-bfdb-6d155f6e6710"}}, {"head": {"id": "506157e3-6dd8-4a73-a37c-f4b50c887c3a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649067882700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "997a3f74-449e-4ea8-9985-a47f6869d9ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649069868800, "endTime": 14649069915500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "14eaf2c8-7236-41f8-af7c-27223e1ea220"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14eaf2c8-7236-41f8-af7c-27223e1ea220", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649069868800, "endTime": 14649069915500}, "additional": {"logType": "info", "children": [], "durationId": "997a3f74-449e-4ea8-9985-a47f6869d9ec"}}, {"head": {"id": "2b5efa3f-e8cc-4d80-b50c-7f917e5939b4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649466573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9011bd6e-c7f0-427a-b70c-3c98a031b93b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649467851100, "endTime": 14649467882000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "0a8e5550-20a9-4a10-ae1b-ed9b2333b7b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a8e5550-20a9-4a10-ae1b-ed9b2333b7b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14649467851100, "endTime": 14649467882000}, "additional": {"logType": "info", "children": [], "durationId": "9011bd6e-c7f0-427a-b70c-3c98a031b93b"}}, {"head": {"id": "c1483700-7e21-4bb6-af5a-746fb5d53490", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14667572559700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fb3bc6-cdd5-48f9-970a-a23349f99096", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14667574824900, "endTime": 14667574856600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "38993ba2-9350-421e-a082-9ddb7c90d10e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38993ba2-9350-421e-a082-9ddb7c90d10e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14667574824900, "endTime": 14667574856600}, "additional": {"logType": "info", "children": [], "durationId": "a8fb3bc6-cdd5-48f9-970a-a23349f99096"}}, {"head": {"id": "df381a12-f6f6-454c-9ae8-a981058fd214", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668541805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0bd0759-cf0f-4cb1-a8fa-1a838f51b9fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668543040600, "endTime": 14668543064700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "de0c0608-55de-4a4c-8bc0-af7a5654957d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de0c0608-55de-4a4c-8bc0-af7a5654957d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668543040600, "endTime": 14668543064700}, "additional": {"logType": "info", "children": [], "durationId": "b0bd0759-cf0f-4cb1-a8fa-1a838f51b9fd"}}, {"head": {"id": "49d7540a-f8ea-440b-9889-4c2bfb678852", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668543180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfdb1a3-28a8-4604-b534-af1121f76a45", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668544495700, "endTime": 14668544519400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "e50ba25a-d267-40ed-97b8-f5152d3979d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e50ba25a-d267-40ed-97b8-f5152d3979d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668544495700, "endTime": 14668544519400}, "additional": {"logType": "info", "children": [], "durationId": "bbfdb1a3-28a8-4604-b534-af1121f76a45"}}, {"head": {"id": "dc37338d-e183-4c17-a804-d6c823829d24", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668544639800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd2274f-0bb7-4d3c-aa0a-2864cef0fafe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668554959900, "endTime": 14668555004200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d186d654-2b5c-47c5-baee-cc019c1fc411"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d186d654-2b5c-47c5-baee-cc019c1fc411", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668554959900, "endTime": 14668555004200}, "additional": {"logType": "info", "children": [], "durationId": "2dd2274f-0bb7-4d3c-aa0a-2864cef0fafe"}}, {"head": {"id": "97581ee8-c8fa-4069-b9cf-fead0e248f50", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668965892300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "246d3ae8-e1b3-4626-84b4-93eb4902f9c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668967961700, "endTime": 14668967984500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "b2f36dc7-11f1-4ba1-949b-6f88ac3d36c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2f36dc7-11f1-4ba1-949b-6f88ac3d36c8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14668967961700, "endTime": 14668967984500}, "additional": {"logType": "info", "children": [], "durationId": "246d3ae8-e1b3-4626-84b4-93eb4902f9c2"}}, {"head": {"id": "85b51cef-ab82-4824-9284-ed36210194f0", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14686863858000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33acec7-87c4-48ea-b564-191eb6fdae02", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14686865021200, "endTime": 14686865039000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "dad6d278-7fcb-4690-80d9-56c954afd749"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dad6d278-7fcb-4690-80d9-56c954afd749", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14686865021200, "endTime": 14686865039000}, "additional": {"logType": "info", "children": [], "durationId": "b33acec7-87c4-48ea-b564-191eb6fdae02"}}, {"head": {"id": "5cf2e7ff-0832-4bf2-815e-9806ee1cc4a1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687828472900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baad3da7-9605-4cce-bf90-bd569c314188", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687830052200, "endTime": 14687830073600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d0bba3d3-e30d-4d2d-9e76-a53341ce9f73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0bba3d3-e30d-4d2d-9e76-a53341ce9f73", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687830052200, "endTime": 14687830073600}, "additional": {"logType": "info", "children": [], "durationId": "baad3da7-9605-4cce-bf90-bd569c314188"}}, {"head": {"id": "c89d7c05-b29d-47f8-9233-412ee847e243", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687830179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "379d08ae-90b2-4a1c-90af-4c5abd09758b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687831105200, "endTime": 14687831136700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "4b5f797e-b353-4959-a5aa-947379375f0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b5f797e-b353-4959-a5aa-947379375f0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687831105200, "endTime": 14687831136700}, "additional": {"logType": "info", "children": [], "durationId": "379d08ae-90b2-4a1c-90af-4c5abd09758b"}}, {"head": {"id": "218a4584-f970-4ed7-b798-97ac8b37f35a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687831227900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39cbf2a1-fb6b-4654-b3cd-89c1ecf0a6ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687833972400, "endTime": 14687834010000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "3ba5390d-c0c1-46d5-807f-6e2a0bcb9b83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ba5390d-c0c1-46d5-807f-6e2a0bcb9b83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14687833972400, "endTime": 14687834010000}, "additional": {"logType": "info", "children": [], "durationId": "39cbf2a1-fb6b-4654-b3cd-89c1ecf0a6ed"}}, {"head": {"id": "4d36fc7f-1569-4e67-8475-22779c7c53fb", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14688157292800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aa3fa31-6096-4aeb-80e0-89116809ef9a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14688158958900, "endTime": 14688159013200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "31844342-4cd2-45d3-812c-e29eebb5dad4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31844342-4cd2-45d3-812c-e29eebb5dad4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14688158958900, "endTime": 14688159013200}, "additional": {"logType": "info", "children": [], "durationId": "4aa3fa31-6096-4aeb-80e0-89116809ef9a"}}, {"head": {"id": "0be5a8d6-2fb4-4c2a-a127-3c1010fe3d64", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14705753714600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc6a6a8-f4a6-4c83-873c-41d5d48612d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14705755527700, "endTime": 14705755549800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "1219c785-d53f-471f-b8f4-af6352bb854d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1219c785-d53f-471f-b8f4-af6352bb854d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14705755527700, "endTime": 14705755549800}, "additional": {"logType": "info", "children": [], "durationId": "ebc6a6a8-f4a6-4c83-873c-41d5d48612d4"}}, {"head": {"id": "6b4a1413-fc01-4132-a6c4-48f41bf5f640", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706954890600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab226fe-796c-49df-b1bb-797d6b0da1ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706956033600, "endTime": 14706956056200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "2f7e68d7-7a36-4803-ac43-0e9a5c12eda2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f7e68d7-7a36-4803-ac43-0e9a5c12eda2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706956033600, "endTime": 14706956056200}, "additional": {"logType": "info", "children": [], "durationId": "1ab226fe-796c-49df-b1bb-797d6b0da1ed"}}, {"head": {"id": "a3588839-507e-49c8-9648-a72fa1a8d61f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706956138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0aad90-76ca-4c2e-8b78-c24f37093cdc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706957037700, "endTime": 14706957060200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "daf238ad-cdcb-4cc0-9a07-46fb7a33bad3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daf238ad-cdcb-4cc0-9a07-46fb7a33bad3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706957037700, "endTime": 14706957060200}, "additional": {"logType": "info", "children": [], "durationId": "1f0aad90-76ca-4c2e-8b78-c24f37093cdc"}}, {"head": {"id": "87811282-a404-4f91-879a-87245d0bc732", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706957222500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eacb5ca2-b81b-4c23-ac79-93543c2bee58", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706959351200, "endTime": 14706959407000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "044a0bea-4389-4fae-bf15-0d4d81051801"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "044a0bea-4389-4fae-bf15-0d4d81051801", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14706959351200, "endTime": 14706959407000}, "additional": {"logType": "info", "children": [], "durationId": "eacb5ca2-b81b-4c23-ac79-93543c2bee58"}}, {"head": {"id": "e116618a-6e6e-40a9-8df0-a9297c4c1bc0", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14707328504200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9871c1d-c576-4532-a775-aab621023169", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14707330018700, "endTime": 14707330043600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "51e2c5df-84a1-4c66-9ba0-5b40af6c2c77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51e2c5df-84a1-4c66-9ba0-5b40af6c2c77", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14707330018700, "endTime": 14707330043600}, "additional": {"logType": "info", "children": [], "durationId": "d9871c1d-c576-4532-a775-aab621023169"}}, {"head": {"id": "d6f439fa-8a1a-4def-b735-f84fd59b631a", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14775538060200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad629917-a57d-41cf-b5cd-83cd8e428852", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14775541270800, "endTime": 14775541317600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "fcc26abf-b7bb-410f-85ad-3acadb3ed9fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcc26abf-b7bb-410f-85ad-3acadb3ed9fb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14775541270800, "endTime": 14775541317600}, "additional": {"logType": "info", "children": [], "durationId": "ad629917-a57d-41cf-b5cd-83cd8e428852"}}, {"head": {"id": "0a400bf7-54ba-498a-9614-82a435f3b06f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778693842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d3502a-20f1-4e0f-906c-ec2f5642af64", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778696311600, "endTime": 14778696339300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "baa55fce-864d-4780-912e-79cb7f4794fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa55fce-864d-4780-912e-79cb7f4794fb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778696311600, "endTime": 14778696339300}, "additional": {"logType": "info", "children": [], "durationId": "c9d3502a-20f1-4e0f-906c-ec2f5642af64"}}, {"head": {"id": "c379563e-8848-4a2d-8f6b-b727bad4e7e6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778696461500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "422f997a-8683-4786-95b7-72c3d466858d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778697526200, "endTime": 14778697544300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "055888c7-5a2d-4cf1-946c-da61c0ed400f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "055888c7-5a2d-4cf1-946c-da61c0ed400f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778697526200, "endTime": 14778697544300}, "additional": {"logType": "info", "children": [], "durationId": "422f997a-8683-4786-95b7-72c3d466858d"}}, {"head": {"id": "ff060c65-8314-40e7-b4ce-48667a500313", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778697633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3562edda-c342-446e-91eb-3dd5749cd966", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778699496700, "endTime": 14778699524500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "b4bc9699-abb8-44e4-9b76-12b431d59bb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4bc9699-abb8-44e4-9b76-12b431d59bb8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14778699496700, "endTime": 14778699524500}, "additional": {"logType": "info", "children": [], "durationId": "3562edda-c342-446e-91eb-3dd5749cd966"}}, {"head": {"id": "0b6a7053-cad1-490c-83e2-99bd7dfc580a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14780432543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "facc80c1-be9c-4a3b-bdce-4272c0c62bb4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14780433650500, "endTime": 14780433670000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "8c081478-fe38-453a-9112-cf1ef4786c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c081478-fe38-453a-9112-cf1ef4786c3f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14780433650500, "endTime": 14780433670000}, "additional": {"logType": "info", "children": [], "durationId": "facc80c1-be9c-4a3b-bdce-4272c0c62bb4"}}, {"head": {"id": "f27c4bed-c3c0-4189-8807-622af3b0ccbd", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14789273629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a79c13a-902a-4dfa-a8cc-db0fc795b883", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14789275707500, "endTime": 14789275731600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "54104298-65f0-4f3a-9666-9eac54343919"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54104298-65f0-4f3a-9666-9eac54343919", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14789275707500, "endTime": 14789275731600}, "additional": {"logType": "info", "children": [], "durationId": "8a79c13a-902a-4dfa-a8cc-db0fc795b883"}}, {"head": {"id": "f20cc323-bb6a-4569-ba48-e421fd6c6a1d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790370068100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db0597b-3f5e-41c9-83e1-caea7ac63103", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790371118000, "endTime": 14790371137500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "4155db2b-4abf-48c1-8b79-38601e595464"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4155db2b-4abf-48c1-8b79-38601e595464", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790371118000, "endTime": 14790371137500}, "additional": {"logType": "info", "children": [], "durationId": "0db0597b-3f5e-41c9-83e1-caea7ac63103"}}, {"head": {"id": "f32423e9-7cf8-4831-bfb2-bd09ec1c7de8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790371213700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdd68fa-1aab-452e-9c74-d65667eb9e1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790372109700, "endTime": 14790372133000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "1d9ab568-6719-44c7-a2fb-2ff5dc3fbcfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d9ab568-6719-44c7-a2fb-2ff5dc3fbcfc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790372109700, "endTime": 14790372133000}, "additional": {"logType": "info", "children": [], "durationId": "3bdd68fa-1aab-452e-9c74-d65667eb9e1f"}}, {"head": {"id": "1528355f-83f0-427d-820b-0480644f2159", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790372225400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38aeac2-e6f8-4774-a29c-e39a4870183e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790375961600, "endTime": 14790376071500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d4145dac-b61f-4d1a-b715-06882cf83370"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4145dac-b61f-4d1a-b715-06882cf83370", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790375961600, "endTime": 14790376071500}, "additional": {"logType": "info", "children": [], "durationId": "f38aeac2-e6f8-4774-a29c-e39a4870183e"}}, {"head": {"id": "ae906732-c17f-4f7b-89d1-30cd1aa063ee", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790723047400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a11112f5-408e-4667-8878-c95aa69a81bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790727359400, "endTime": 14790727396400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "778f3f6c-b923-4ba7-a5e8-ea128e48c191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "778f3f6c-b923-4ba7-a5e8-ea128e48c191", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14790727359400, "endTime": 14790727396400}, "additional": {"logType": "info", "children": [], "durationId": "a11112f5-408e-4667-8878-c95aa69a81bd"}}, {"head": {"id": "5a29b97b-047c-4927-abda-cc041ce997c8", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14803587444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "020fbc13-f034-4767-a49c-7e88d5cbae82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14803588735900, "endTime": 14803588761200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "9fddb8c0-c28a-490f-8cad-9f865f8ba318"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fddb8c0-c28a-490f-8cad-9f865f8ba318", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14803588735900, "endTime": 14803588761200}, "additional": {"logType": "info", "children": [], "durationId": "020fbc13-f034-4767-a49c-7e88d5cbae82"}}, {"head": {"id": "cee14d24-a56f-446a-848b-4236907cc3e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804530774800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c8e4666-4256-46df-897e-807a3eba4034", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804531853300, "endTime": 14804531872000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "aee3d7aa-b01b-40dd-a95f-b7ab0a9c304d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aee3d7aa-b01b-40dd-a95f-b7ab0a9c304d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804531853300, "endTime": 14804531872000}, "additional": {"logType": "info", "children": [], "durationId": "4c8e4666-4256-46df-897e-807a3eba4034"}}, {"head": {"id": "2931b42e-e48a-4d89-bfa9-828f00141cf9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804531953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35cc742a-6698-458e-b10e-b97bfaefb3a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804533011700, "endTime": 14804533035900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "6bd9ccc6-7f82-4018-ae4c-20c547aff341"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bd9ccc6-7f82-4018-ae4c-20c547aff341", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804533011700, "endTime": 14804533035900}, "additional": {"logType": "info", "children": [], "durationId": "35cc742a-6698-458e-b10e-b97bfaefb3a8"}}, {"head": {"id": "e75e9908-81ca-4161-bc5a-128f1c5ab004", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804533532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243f4e83-c75c-4a8f-b009-0190dae3baf9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804546938500, "endTime": 14804546993700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "976a6845-fe8b-4a92-9617-24228fb2a50c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "976a6845-fe8b-4a92-9617-24228fb2a50c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14804546938500, "endTime": 14804546993700}, "additional": {"logType": "info", "children": [], "durationId": "243f4e83-c75c-4a8f-b009-0190dae3baf9"}}, {"head": {"id": "845b79a2-9649-4ca8-bf9a-8d35eca479a4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14805028719700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d389bf5-ac47-49e1-bad1-591ecbe7053f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14805030197800, "endTime": 14805030239100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "d018c244-dde3-4cfe-b400-2a0a45672e57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d018c244-dde3-4cfe-b400-2a0a45672e57", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14805030197800, "endTime": 14805030239100}, "additional": {"logType": "info", "children": [], "durationId": "5d389bf5-ac47-49e1-bad1-591ecbe7053f"}}, {"head": {"id": "d0092551-dde5-48cb-94f7-d7ad6f615e33", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14816694668300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d520af-caf0-46cc-a630-c0415b7198a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14816695758100, "endTime": 14816695776600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "5364a5c3-b7e3-4060-890c-25ca592b6e89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5364a5c3-b7e3-4060-890c-25ca592b6e89", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14816695758100, "endTime": 14816695776600}, "additional": {"logType": "info", "children": [], "durationId": "44d520af-caf0-46cc-a630-c0415b7198a5"}}, {"head": {"id": "4d9ac017-b79e-43a0-a553-608f37f1aca2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817651549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd8e7d4a-c64d-4c1d-bc07-0173452baf6d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817653287500, "endTime": 14817653322900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "297a3b8f-cbc8-40d8-9b87-5be13cf4ce47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "297a3b8f-cbc8-40d8-9b87-5be13cf4ce47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817653287500, "endTime": 14817653322900}, "additional": {"logType": "info", "children": [], "durationId": "fd8e7d4a-c64d-4c1d-bc07-0173452baf6d"}}, {"head": {"id": "0edd66fe-a8bd-49f8-826b-cff33e3637a6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817654519300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "829a4de3-82c5-47ad-8895-f15dda45939e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817656324700, "endTime": 14817656362400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "eb8c96a9-5354-40a9-a235-a7ef64822bbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb8c96a9-5354-40a9-a235-a7ef64822bbd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817656324700, "endTime": 14817656362400}, "additional": {"logType": "info", "children": [], "durationId": "829a4de3-82c5-47ad-8895-f15dda45939e"}}, {"head": {"id": "6c03ab65-5bd4-4280-bb5e-bcc57987dcd3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817656576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e063df-8e14-4897-a2d4-7f65122b373c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817658685900, "endTime": 14817658718000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "cabbb65a-a2ed-4c8b-89d3-48217e922018"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cabbb65a-a2ed-4c8b-89d3-48217e922018", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14817658685900, "endTime": 14817658718000}, "additional": {"logType": "info", "children": [], "durationId": "49e063df-8e14-4897-a2d4-7f65122b373c"}}, {"head": {"id": "f73a5b42-d717-4f04-b0a4-178a6f971871", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14818194470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc05eae-e1cb-4e01-b68e-62e201b6fedf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14818195543700, "endTime": 14818195561100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f99dedf6-d756-4f31-9066-4cbee3467df3", "logId": "73e84d61-71e6-498f-a59c-29fa3ccbd5b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73e84d61-71e6-498f-a59c-29fa3ccbd5b7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 14818195543700, "endTime": 14818195561100}, "additional": {"logType": "info", "children": [], "durationId": "cfc05eae-e1cb-4e01-b68e-62e201b6fedf"}}, {"head": {"id": "9b6fbb97-dc8e-4e7d-96e7-7c43440d9aa1", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15003101950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89bb69b5-7c7f-4815-9dc0-1e35a3b61db2", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15003102299400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0723ac9-dbe8-4b8e-be20-00aa8a38b07b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006048423000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006056861700, "endTime": 15006613477900}, "additional": {"children": ["0cf863b4-a500-434b-b4c0-4a865531c73d", "2806cd2d-9787-49b3-ab5a-d0a92469f182", "8dc980cf-a4e7-4dad-a27c-e2bfee4080f1", "105760e3-fdd9-43a4-bc83-626e35e7558d", "ff347d18-0707-4510-8300-8d0f5311b7cd", "bdec612b-c471-453e-bf66-61d83f0870e9", "7e504327-7622-4608-b09a-27eef9ffdcf8"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cf863b4-a500-434b-b4c0-4a865531c73d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006056863900, "endTime": 15006132245900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "8d197050-79be-4768-9640-deb9683d7516"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006132284400, "endTime": 15006610909700}, "additional": {"children": ["63eb8420-82ae-46bd-a1ca-daf69b643033", "b3c15a73-8668-4cf6-8c20-d75c2e0fc7ff", "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "445ce2c3-d188-4f52-b952-8593662d0001", "a9949d5e-a2b0-4b22-9af8-e69222f2d4f3", "3fecea69-7495-4911-a49f-fa5e5122f0ab", "ec333b1c-b433-4939-92bb-36ef1ceb01b7", "341f9ca3-fe3a-444b-a26d-3f96e404796a", "ae7a213f-9151-40b5-bf89-a57baeb1194b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "ad02ae05-0785-4cd0-bab6-7669b7195219"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dc980cf-a4e7-4dad-a27c-e2bfee4080f1", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006610956500, "endTime": 15006613411700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "6047dc66-4961-43bf-ae70-5daaf793c031"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "105760e3-fdd9-43a4-bc83-626e35e7558d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006613421500, "endTime": 15006613464100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "51e61d52-a5a7-4470-bf2b-f9d713ccadb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff347d18-0707-4510-8300-8d0f5311b7cd", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006060661600, "endTime": 15006060698100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "5235c246-f0a0-446e-9aac-393a4ece5bb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5235c246-f0a0-446e-9aac-393a4ece5bb5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006060661600, "endTime": 15006060698100}, "additional": {"logType": "info", "children": [], "durationId": "ff347d18-0707-4510-8300-8d0f5311b7cd", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "bdec612b-c471-453e-bf66-61d83f0870e9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006065708900, "endTime": 15006065723000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "417fae03-43af-4906-b026-380e0a0e61c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "417fae03-43af-4906-b026-380e0a0e61c4", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006065708900, "endTime": 15006065723000}, "additional": {"logType": "info", "children": [], "durationId": "bdec612b-c471-453e-bf66-61d83f0870e9", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "0efd92c4-16ee-4417-8f6f-8cd236304c70", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006065778200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff28356-16b1-4f55-ab84-a343b182c73f", "name": "Cache service initialization finished in 67 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006132104300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d197050-79be-4768-9640-deb9683d7516", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006056863900, "endTime": 15006132245900}, "additional": {"logType": "info", "children": [], "durationId": "0cf863b4-a500-434b-b4c0-4a865531c73d", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "63eb8420-82ae-46bd-a1ca-daf69b643033", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006139160900, "endTime": 15006139175400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "4c3a4997-ec37-4c5e-b795-ad8bfd64dd13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c15a73-8668-4cf6-8c20-d75c2e0fc7ff", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006139192400, "endTime": 15006143354400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "1af7f4ba-b465-4bf6-95e2-b5beab0ebe18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006143370800, "endTime": 15006422056900}, "additional": {"children": ["c203e6ab-0f77-4af5-82cf-6aaaae631dae", "879e1fe5-a6e7-4576-9aa3-28efb4af6ff9", "7c5f9294-4c73-49a8-8e23-751035a668ef"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "918a4e3d-3a4e-41e5-ba29-948a735abd83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "445ce2c3-d188-4f52-b952-8593662d0001", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006422071400, "endTime": 15006445886100}, "additional": {"children": ["d0542d76-8008-4f4f-8470-dd326329f63e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "c031ad68-5869-4314-b585-daa174c0314e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9949d5e-a2b0-4b22-9af8-e69222f2d4f3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006446055600, "endTime": 15006469396000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "2fa007c2-e40b-431b-991d-b2d2c178a547"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fecea69-7495-4911-a49f-fa5e5122f0ab", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006470523800, "endTime": 15006502795800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "b7fd6d70-b7ab-4c20-8d8a-437d30529fd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec333b1c-b433-4939-92bb-36ef1ceb01b7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006502852200, "endTime": 15006610564100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "cc7f77c7-e544-4c21-9c9f-e6b50dad5812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "341f9ca3-fe3a-444b-a26d-3f96e404796a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006610650000, "endTime": 15006610877600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "d2b77146-5e5b-45b7-9367-bb40013cdd7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c3a4997-ec37-4c5e-b795-ad8bfd64dd13", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006139160900, "endTime": 15006139175400}, "additional": {"logType": "info", "children": [], "durationId": "63eb8420-82ae-46bd-a1ca-daf69b643033", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "1af7f4ba-b465-4bf6-95e2-b5beab0ebe18", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006139192400, "endTime": 15006143354400}, "additional": {"logType": "info", "children": [], "durationId": "b3c15a73-8668-4cf6-8c20-d75c2e0fc7ff", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "c203e6ab-0f77-4af5-82cf-6aaaae631dae", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006144036200, "endTime": 15006144053900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "logId": "dd00a065-6ef1-49d4-b267-0138329b5fb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd00a065-6ef1-49d4-b267-0138329b5fb3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006144036200, "endTime": 15006144053900}, "additional": {"logType": "info", "children": [], "durationId": "c203e6ab-0f77-4af5-82cf-6aaaae631dae", "parent": "918a4e3d-3a4e-41e5-ba29-948a735abd83"}}, {"head": {"id": "879e1fe5-a6e7-4576-9aa3-28efb4af6ff9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006146865500, "endTime": 15006421330100}, "additional": {"children": ["ed00e425-7261-4fb5-b6cc-d7c6b7a88082", "e5c7ee23-e9e4-487d-9dc4-464238c211e0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "logId": "a9687f29-c493-4cdd-a8e8-95815157bac4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed00e425-7261-4fb5-b6cc-d7c6b7a88082", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006146866600, "endTime": 15006152127800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "879e1fe5-a6e7-4576-9aa3-28efb4af6ff9", "logId": "57f9f893-7746-42c6-b862-92574f908e17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5c7ee23-e9e4-487d-9dc4-464238c211e0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006152143700, "endTime": 15006421316800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "879e1fe5-a6e7-4576-9aa3-28efb4af6ff9", "logId": "546b4411-8e5e-4084-8962-d40e797adf88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1294273a-e88f-4ae7-9aaa-4644c1133e0a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006146871200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f5e9c86-1caa-436b-8735-c6d8ddc70a50", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006151990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f9f893-7746-42c6-b862-92574f908e17", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006146866600, "endTime": 15006152127800}, "additional": {"logType": "info", "children": [], "durationId": "ed00e425-7261-4fb5-b6cc-d7c6b7a88082", "parent": "a9687f29-c493-4cdd-a8e8-95815157bac4"}}, {"head": {"id": "b9aff72b-167a-494c-8910-ba9166ef7ed5", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006152161000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba532ad-1ef9-4a00-8b99-f6e317de86ab", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006160261500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d02b153-ce11-4567-a273-bd88bd85af83", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006160396600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77265e6d-c920-4650-b7a7-040cb3fe3da7", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006160551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1de1b67-bfc7-4c88-a160-33e30c4862bc", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006160805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1146edd5-8afe-4467-8a57-a614c567239d", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006162681000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcd110b-3ad2-4fa3-a532-f92cd39d867e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006167820600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e320ce2d-a3f1-4e70-a961-6904f3d83c8f", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006235226200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3129bfb9-814d-445a-8be0-25bc56e7e7e9", "name": "Sdk init in 219 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006387731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab9cb223-89a8-44e5-8d9c-396fd66ba489", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006388064500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 0}, "markType": "other"}}, {"head": {"id": "73338803-b2b0-4e57-a155-ee33a086df74", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006388156000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 0}, "markType": "other"}}, {"head": {"id": "3b29cc9f-15d5-4ddc-a6ba-51cb249e83de", "name": "Project task initialization takes 30 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006421027600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "544fc9b9-f071-49cc-8ea8-ea07964b5b50", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006421150200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8eef6cd-9d2c-4877-bba1-3ab2e7b25053", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006421214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45488ab9-691e-4d3a-9fa9-e971687260d9", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006421268000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546b4411-8e5e-4084-8962-d40e797adf88", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006152143700, "endTime": 15006421316800}, "additional": {"logType": "info", "children": [], "durationId": "e5c7ee23-e9e4-487d-9dc4-464238c211e0", "parent": "a9687f29-c493-4cdd-a8e8-95815157bac4"}}, {"head": {"id": "a9687f29-c493-4cdd-a8e8-95815157bac4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006146865500, "endTime": 15006421330100}, "additional": {"logType": "info", "children": ["57f9f893-7746-42c6-b862-92574f908e17", "546b4411-8e5e-4084-8962-d40e797adf88"], "durationId": "879e1fe5-a6e7-4576-9aa3-28efb4af6ff9", "parent": "918a4e3d-3a4e-41e5-ba29-948a735abd83"}}, {"head": {"id": "7c5f9294-4c73-49a8-8e23-751035a668ef", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006422030300, "endTime": 15006422042500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "logId": "4f2e31fb-cae2-4f52-aa33-55fb3d0b68ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f2e31fb-cae2-4f52-aa33-55fb3d0b68ba", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006422030300, "endTime": 15006422042500}, "additional": {"logType": "info", "children": [], "durationId": "7c5f9294-4c73-49a8-8e23-751035a668ef", "parent": "918a4e3d-3a4e-41e5-ba29-948a735abd83"}}, {"head": {"id": "918a4e3d-3a4e-41e5-ba29-948a735abd83", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006143370800, "endTime": 15006422056900}, "additional": {"logType": "info", "children": ["dd00a065-6ef1-49d4-b267-0138329b5fb3", "a9687f29-c493-4cdd-a8e8-95815157bac4", "4f2e31fb-cae2-4f52-aa33-55fb3d0b68ba"], "durationId": "ef1bd9da-7d31-4d11-88ab-16a3976e6b7f", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "d0542d76-8008-4f4f-8470-dd326329f63e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006423334800, "endTime": 15006445873800}, "additional": {"children": ["6146dac3-d0bf-4b3e-96c5-eebd6922b1ff", "92d98edb-e14d-45fc-af42-b5760aa012da", "6528ae48-0a36-4c38-9c30-7246810e7a1c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "445ce2c3-d188-4f52-b952-8593662d0001", "logId": "2210e870-ccbf-425b-b5f8-1ec5489859f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6146dac3-d0bf-4b3e-96c5-eebd6922b1ff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006426625500, "endTime": 15006426639500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d0542d76-8008-4f4f-8470-dd326329f63e", "logId": "e068fd39-50a7-4395-b1eb-2e810f1a970f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e068fd39-50a7-4395-b1eb-2e810f1a970f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006426625500, "endTime": 15006426639500}, "additional": {"logType": "info", "children": [], "durationId": "6146dac3-d0bf-4b3e-96c5-eebd6922b1ff", "parent": "2210e870-ccbf-425b-b5f8-1ec5489859f8"}}, {"head": {"id": "92d98edb-e14d-45fc-af42-b5760aa012da", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006428693700, "endTime": 15006443785900}, "additional": {"children": ["fdea142b-eef7-40e7-a605-9541108ebf5c", "aba12d9c-216a-4b37-9b8a-9fb2a1b133a3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d0542d76-8008-4f4f-8470-dd326329f63e", "logId": "78eaae09-9b02-4dc4-8e13-8333935ed4fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdea142b-eef7-40e7-a605-9541108ebf5c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006428694800, "endTime": 15006431692200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92d98edb-e14d-45fc-af42-b5760aa012da", "logId": "649733ad-b855-4d50-bc85-733cc6648b87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aba12d9c-216a-4b37-9b8a-9fb2a1b133a3", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006431711000, "endTime": 15006443772100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92d98edb-e14d-45fc-af42-b5760aa012da", "logId": "364966bd-ad94-43ae-b59b-6523e23398bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4f6c1bf-7c2f-495c-b5b1-fb839f794505", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006428700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac28182-00d2-4725-951c-58170cb78c95", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006431554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649733ad-b855-4d50-bc85-733cc6648b87", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006428694800, "endTime": 15006431692200}, "additional": {"logType": "info", "children": [], "durationId": "fdea142b-eef7-40e7-a605-9541108ebf5c", "parent": "78eaae09-9b02-4dc4-8e13-8333935ed4fb"}}, {"head": {"id": "77210848-1b9b-4d1a-8059-f81a89e0855a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006431724500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7fdcf5b-bcbb-4398-b10b-1fcb3a6f06ca", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006439276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d859fcd-1a41-4537-88d0-6042e3633430", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006439411600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59962f95-2783-4f40-b8a4-0fdfbc1f3f97", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006439621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb6d8d6-8339-42ab-9d38-ddb533ff03d6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006439904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1252b1-f9c7-40ec-aa97-a4ad52d5688b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006439994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93812cf6-1d61-44f8-a510-ab0dc927b8c0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006440061000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aefc5dc-0ab4-48d7-b96d-96146f116479", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006440153300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a058ba-5ec9-4ad9-a051-270b7532a12f", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006443465300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8384ade3-aebb-404e-bb45-bd7e6bb43b4e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006443587300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306b25cb-0004-4ae0-bd62-baba1574e515", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006443659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1700eace-c7ba-4a9d-9b1c-349396b2b765", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006443719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364966bd-ad94-43ae-b59b-6523e23398bc", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006431711000, "endTime": 15006443772100}, "additional": {"logType": "info", "children": [], "durationId": "aba12d9c-216a-4b37-9b8a-9fb2a1b133a3", "parent": "78eaae09-9b02-4dc4-8e13-8333935ed4fb"}}, {"head": {"id": "78eaae09-9b02-4dc4-8e13-8333935ed4fb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006428693700, "endTime": 15006443785900}, "additional": {"logType": "info", "children": ["649733ad-b855-4d50-bc85-733cc6648b87", "364966bd-ad94-43ae-b59b-6523e23398bc"], "durationId": "92d98edb-e14d-45fc-af42-b5760aa012da", "parent": "2210e870-ccbf-425b-b5f8-1ec5489859f8"}}, {"head": {"id": "6528ae48-0a36-4c38-9c30-7246810e7a1c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006445847600, "endTime": 15006445859500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d0542d76-8008-4f4f-8470-dd326329f63e", "logId": "fba1cc3a-82b6-495e-ab81-26651e72ecca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fba1cc3a-82b6-495e-ab81-26651e72ecca", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006445847600, "endTime": 15006445859500}, "additional": {"logType": "info", "children": [], "durationId": "6528ae48-0a36-4c38-9c30-7246810e7a1c", "parent": "2210e870-ccbf-425b-b5f8-1ec5489859f8"}}, {"head": {"id": "2210e870-ccbf-425b-b5f8-1ec5489859f8", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006423334800, "endTime": 15006445873800}, "additional": {"logType": "info", "children": ["e068fd39-50a7-4395-b1eb-2e810f1a970f", "78eaae09-9b02-4dc4-8e13-8333935ed4fb", "fba1cc3a-82b6-495e-ab81-26651e72ecca"], "durationId": "d0542d76-8008-4f4f-8470-dd326329f63e", "parent": "c031ad68-5869-4314-b585-daa174c0314e"}}, {"head": {"id": "c031ad68-5869-4314-b585-daa174c0314e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006422071400, "endTime": 15006445886100}, "additional": {"logType": "info", "children": ["2210e870-ccbf-425b-b5f8-1ec5489859f8"], "durationId": "445ce2c3-d188-4f52-b952-8593662d0001", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "e35e5813-0cd7-41a9-9ffc-0a8d679d0c51", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006468713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270819c4-6c7b-4b1c-8b3f-4c68d04054c2", "name": "hvigorfile, resolve hvigorfile dependencies in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006469307000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa007c2-e40b-431b-991d-b2d2c178a547", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006446055600, "endTime": 15006469396000}, "additional": {"logType": "info", "children": [], "durationId": "a9949d5e-a2b0-4b22-9af8-e69222f2d4f3", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "ae7a213f-9151-40b5-bf89-a57baeb1194b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006470306300, "endTime": 15006470511900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "logId": "6255fd66-01e8-405a-b5d2-93751ab33574"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4530ee4e-74a7-4bbf-8a17-cfc6f1d4b27e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006470369000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6255fd66-01e8-405a-b5d2-93751ab33574", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006470306300, "endTime": 15006470511900}, "additional": {"logType": "info", "children": [], "durationId": "ae7a213f-9151-40b5-bf89-a57baeb1194b", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "210b1a25-c6e0-4d20-b18a-2dd02c2acd9c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006471915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e377e71-2957-4c97-a8f9-459a38842c8a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006501582400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7fd6d70-b7ab-4c20-8d8a-437d30529fd4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006470523800, "endTime": 15006502795800}, "additional": {"logType": "info", "children": [], "durationId": "3fecea69-7495-4911-a49f-fa5e5122f0ab", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "ec871150-1f21-4de2-8355-da8184b3921e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006502878200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "624fa9a4-1750-4313-aeda-34f749cd0824", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006597631600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131edd63-1540-4eb8-830c-0c59145c7e22", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006597873900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44f71ca-ed7b-4a8e-acbf-82ccfc55be32", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006598306700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "310c7190-9039-4588-81d5-e657c5c7a8b0", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006603900500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47785a3c-511f-4c7d-bf1b-0f6f6de42989", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006604118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc7f77c7-e544-4c21-9c9f-e6b50dad5812", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006502852200, "endTime": 15006610564100}, "additional": {"logType": "info", "children": [], "durationId": "ec333b1c-b433-4939-92bb-36ef1ceb01b7", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "962822a8-bd34-44b6-b41f-036117a7956c", "name": "Configuration phase cost:472 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006610692600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2b77146-5e5b-45b7-9367-bb40013cdd7c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006610650000, "endTime": 15006610877600}, "additional": {"logType": "info", "children": [], "durationId": "341f9ca3-fe3a-444b-a26d-3f96e404796a", "parent": "ad02ae05-0785-4cd0-bab6-7669b7195219"}}, {"head": {"id": "ad02ae05-0785-4cd0-bab6-7669b7195219", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006132284400, "endTime": 15006610909700}, "additional": {"logType": "info", "children": ["4c3a4997-ec37-4c5e-b795-ad8bfd64dd13", "1af7f4ba-b465-4bf6-95e2-b5beab0ebe18", "918a4e3d-3a4e-41e5-ba29-948a735abd83", "c031ad68-5869-4314-b585-daa174c0314e", "2fa007c2-e40b-431b-991d-b2d2c178a547", "b7fd6d70-b7ab-4c20-8d8a-437d30529fd4", "cc7f77c7-e544-4c21-9c9f-e6b50dad5812", "d2b77146-5e5b-45b7-9367-bb40013cdd7c", "6255fd66-01e8-405a-b5d2-93751ab33574"], "durationId": "2806cd2d-9787-49b3-ab5a-d0a92469f182", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "7e504327-7622-4608-b09a-27eef9ffdcf8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006613362800, "endTime": 15006613389500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2", "logId": "0ede053a-4799-4ea7-9e90-dec393b627a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ede053a-4799-4ea7-9e90-dec393b627a8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006613362800, "endTime": 15006613389500}, "additional": {"logType": "info", "children": [], "durationId": "7e504327-7622-4608-b09a-27eef9ffdcf8", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "6047dc66-4961-43bf-ae70-5daaf793c031", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006610956500, "endTime": 15006613411700}, "additional": {"logType": "info", "children": [], "durationId": "8dc980cf-a4e7-4dad-a27c-e2bfee4080f1", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "51e61d52-a5a7-4470-bf2b-f9d713ccadb7", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006613421500, "endTime": 15006613464100}, "additional": {"logType": "info", "children": [], "durationId": "105760e3-fdd9-43a4-bc83-626e35e7558d", "parent": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c"}}, {"head": {"id": "c18f2913-6b97-4a5b-9b8d-8ec2fc95d35c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006056861700, "endTime": 15006613477900}, "additional": {"logType": "info", "children": ["8d197050-79be-4768-9640-deb9683d7516", "ad02ae05-0785-4cd0-bab6-7669b7195219", "6047dc66-4961-43bf-ae70-5daaf793c031", "51e61d52-a5a7-4470-bf2b-f9d713ccadb7", "5235c246-f0a0-446e-9aac-393a4ece5bb5", "417fae03-43af-4906-b026-380e0a0e61c4", "0ede053a-4799-4ea7-9e90-dec393b627a8"], "durationId": "2bcd3898-ced5-467d-b6b3-9afcbe95f2e2"}}, {"head": {"id": "36026c1e-ee35-45cb-9075-32357e1ae2c6", "name": "Configuration task cost before running: 561 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006613776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd627fa-8507-4582-84ff-1c8859cc0da5", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006624301800, "endTime": 15006636419500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "44c8f897-15e3-44c6-8751-09e6c95eeb5f", "logId": "b35b9461-8b0f-4fb1-8265-4c59645aee15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c8f897-15e3-44c6-8751-09e6c95eeb5f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006617079000}, "additional": {"logType": "detail", "children": [], "durationId": "cfd627fa-8507-4582-84ff-1c8859cc0da5"}}, {"head": {"id": "b30c3b11-b4ed-4963-b10f-6b774ef8190d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006618249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6580f786-54a3-4046-9698-0a4633fc5455", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006618418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e608fd-9d50-45bf-ae23-335270130351", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006624325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996c3e52-5621-41c1-8683-d0c18d8f05e0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006636204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243ff8a5-5ca0-470e-a89c-05a1917f141f", "name": "entry : default@PreBuild cost memory 0.2809600830078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006636339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35b9461-8b0f-4fb1-8265-4c59645aee15", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006624301800, "endTime": 15006636419500}, "additional": {"logType": "info", "children": [], "durationId": "cfd627fa-8507-4582-84ff-1c8859cc0da5"}}, {"head": {"id": "aa8e0e22-fd95-40f4-ada1-be34f111f1ba", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006641723600, "endTime": 15006644157100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "25b4d0af-0574-4bfa-8bd5-b832943024b1", "logId": "007c5a0e-ee51-4d5d-842e-b8b0c1d1cf09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25b4d0af-0574-4bfa-8bd5-b832943024b1", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006640269500}, "additional": {"logType": "detail", "children": [], "durationId": "aa8e0e22-fd95-40f4-ada1-be34f111f1ba"}}, {"head": {"id": "7748bd4e-3a26-4d61-8c43-373bbd918df5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006640791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea9d0c73-044e-4939-9fe6-b941eb920d24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006640875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c20732-de28-4cb7-bc11-f585495cd3e6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006641734300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27a089b-72f6-4fde-99e6-3fe8052fa32c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006643959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2596609c-49dc-47eb-aca5-8ac216e9c077", "name": "entry : default@MergeProfile cost memory 0.1108551025390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006644080300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007c5a0e-ee51-4d5d-842e-b8b0c1d1cf09", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006641723600, "endTime": 15006644157100}, "additional": {"logType": "info", "children": [], "durationId": "aa8e0e22-fd95-40f4-ada1-be34f111f1ba"}}, {"head": {"id": "54bc44a5-e898-43a7-81bd-71001abd6f3b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006647280800, "endTime": 15006649812400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "099d0c32-7d70-42ff-a0b5-e55f0d9b3a10", "logId": "9fef4f05-a263-4b72-b0c5-f7fdfc080bd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "099d0c32-7d70-42ff-a0b5-e55f0d9b3a10", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006645863000}, "additional": {"logType": "detail", "children": [], "durationId": "54bc44a5-e898-43a7-81bd-71001abd6f3b"}}, {"head": {"id": "3f78fcfa-25aa-436d-b9e4-6bd1773bf448", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006646388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa4625f-fd13-45cf-82eb-139ed10957e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006646486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47f917b-7e5d-4644-a697-43b120bc864c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006647291300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934d656e-25c2-4356-ae89-529fa6f637bb", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006648232500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20706d43-a74d-4242-819a-f02af78b28bb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006649614400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37367320-218d-43b2-8aa8-b58d39de03fc", "name": "entry : default@CreateBuildProfile cost memory 0.0967864990234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006649732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fef4f05-a263-4b72-b0c5-f7fdfc080bd6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006647280800, "endTime": 15006649812400}, "additional": {"logType": "info", "children": [], "durationId": "54bc44a5-e898-43a7-81bd-71001abd6f3b"}}, {"head": {"id": "beec071c-7498-4e47-bce1-6f7a698a7969", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676575300, "endTime": 15006677087100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "62073101-096c-40bd-9016-ea05a4aa2778", "logId": "55cf5576-4bf6-44ae-aa3d-937925e33465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62073101-096c-40bd-9016-ea05a4aa2778", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006651992900}, "additional": {"logType": "detail", "children": [], "durationId": "beec071c-7498-4e47-bce1-6f7a698a7969"}}, {"head": {"id": "0b267efe-ba47-4bf9-bfab-5fb08d7d3b7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006652624000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a420231-e2fc-40de-8762-df74fed5d985", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006652765000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66455fc-c70f-4030-869e-06d735fa08db", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676600200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df6217e-726b-4d07-8885-1499f465f329", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676771100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fe844eb-a956-4fd0-b542-114a8dad4648", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676839100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9d569c-06a7-4a01-89e7-43a2067a1257", "name": "entry : default@PreCheckSyscap cost memory 0.03752899169921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676924400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6617a8-1182-4a47-b942-9e635ae2359c", "name": "runTaskFromQueue task cost before running: 624 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006677011500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55cf5576-4bf6-44ae-aa3d-937925e33465", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006676575300, "endTime": 15006677087100, "totalTime": 418800}, "additional": {"logType": "info", "children": [], "durationId": "beec071c-7498-4e47-bce1-6f7a698a7969"}}, {"head": {"id": "bdd2d278-2dc0-4755-a325-62f3a1582aa4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006688663200, "endTime": 15006691153900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cc3d0ef0-bb61-47bc-aa1e-7486799d03a8", "logId": "d8ab7521-faea-4674-bcc4-4b02e5e84754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc3d0ef0-bb61-47bc-aa1e-7486799d03a8", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006678748000}, "additional": {"logType": "detail", "children": [], "durationId": "bdd2d278-2dc0-4755-a325-62f3a1582aa4"}}, {"head": {"id": "6d6a38f5-56cd-4f15-a5ce-d05313285889", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006679250400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4cd36c6-a2ec-4229-99ae-dfe04ad307e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006679335500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "749efa72-b89a-46a6-ae22-f559a7f86136", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006688688400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9475fd6-c87a-46d7-8f94-9802ca68d7b4", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006688960200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a003a08a-0b18-4efa-8eab-a7868365c0d7", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006690834500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f09846-42e1-4dc3-b1e5-4a4f1d5a4d0c", "name": "entry : default@GeneratePkgContextInfo cost memory -1.6030120849609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006691029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ab7521-faea-4674-bcc4-4b02e5e84754", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006688663200, "endTime": 15006691153900}, "additional": {"logType": "info", "children": [], "durationId": "bdd2d278-2dc0-4755-a325-62f3a1582aa4"}}, {"head": {"id": "6df44cf7-aec1-4610-ac74-81621386afb0", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006694984500, "endTime": 15006696220300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a572e6e9-63bb-4522-8eb9-89b9d4ba0336", "logId": "b7a5efbd-5060-45ef-957a-10b07f90a57e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a572e6e9-63bb-4522-8eb9-89b9d4ba0336", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006693205800}, "additional": {"logType": "detail", "children": [], "durationId": "6df44cf7-aec1-4610-ac74-81621386afb0"}}, {"head": {"id": "e8dd339b-86a0-4618-a83d-4337333db65e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006693721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1437e4-dc57-4bc1-83a5-67755b6d84b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006693822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3df102a-3608-4491-8231-4eaa23b1d060", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006694995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d82d3de-4186-4ba8-9b6a-acc4af483739", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006696053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5cf468-76b7-4149-847b-74c99ba9abd3", "name": "entry : default@ProcessProfile cost memory 0.05638885498046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006696149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a5efbd-5060-45ef-957a-10b07f90a57e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006694984500, "endTime": 15006696220300}, "additional": {"logType": "info", "children": [], "durationId": "6df44cf7-aec1-4610-ac74-81621386afb0"}}, {"head": {"id": "43d297e7-c218-4bd8-812d-e8f9e8bce7b3", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006700604100, "endTime": 15006707832700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33a3bb05-34d1-40e9-bec6-9cdfe5f378c1", "logId": "121b2e97-36b1-448b-8cf9-20879ba573e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33a3bb05-34d1-40e9-bec6-9cdfe5f378c1", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006697768500}, "additional": {"logType": "detail", "children": [], "durationId": "43d297e7-c218-4bd8-812d-e8f9e8bce7b3"}}, {"head": {"id": "1fa5eee6-92d8-457c-a1f7-ae303f7849df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006698265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d4788f-417d-4c65-98ea-036384878e24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006698394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2db733c-675d-4001-83f5-788cb47b0e46", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006700631400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea29580a-dc8f-480d-a4e8-eb06f3a6015d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006707626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e97af4b8-f922-4c37-882a-f928bc5acd9c", "name": "entry : default@ProcessRouterMap cost memory 0.18837738037109375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006707758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "121b2e97-36b1-448b-8cf9-20879ba573e6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006700604100, "endTime": 15006707832700}, "additional": {"logType": "info", "children": [], "durationId": "43d297e7-c218-4bd8-812d-e8f9e8bce7b3"}}, {"head": {"id": "fce0bcaa-6a35-4569-bb10-4a2f45e40a02", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006714627000, "endTime": 15006737988700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0853f2cf-215a-446e-a302-090f36fb0711", "logId": "494ff4ea-7305-4f39-ac0f-093d2a6fc9d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0853f2cf-215a-446e-a302-090f36fb0711", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006710734600}, "additional": {"logType": "detail", "children": [], "durationId": "fce0bcaa-6a35-4569-bb10-4a2f45e40a02"}}, {"head": {"id": "65cb1766-9a52-4d7e-959e-6d1486f3bf42", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006711246800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f0c8d87-d823-448f-a450-78ed024e9345", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006711334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c5e427-5be9-4508-a804-eb44b8131292", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006712443200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "985e7c6c-aaa9-4548-8071-781aaa9ed5ee", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006735984300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59220ef2-76bb-4313-890d-0ad31d8c6083", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006736254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72416fe-e5c3-4d0d-8dae-65a9e6e6cb91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006736363900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab582c0-99a3-4604-bc35-78d2aefbbcb2", "name": "entry : default@PreviewProcessResource cost memory 0.0684051513671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006736495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90313aaf-1ebe-48bf-a8d9-5ee1ba65773f", "name": "runTaskFromQueue task cost before running: 685 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006737888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "494ff4ea-7305-4f39-ac0f-093d2a6fc9d3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006714627000, "endTime": 15006737988700, "totalTime": 21941800}, "additional": {"logType": "info", "children": [], "durationId": "fce0bcaa-6a35-4569-bb10-4a2f45e40a02"}}, {"head": {"id": "315c99a1-23cd-4e57-9d94-9cd862eb037b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006745358100, "endTime": 15006772117000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "73125a4b-0095-4400-a100-cfe64644ee1b", "logId": "b53cb199-162d-4cf1-b906-a583dfada8b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73125a4b-0095-4400-a100-cfe64644ee1b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006740889100}, "additional": {"logType": "detail", "children": [], "durationId": "315c99a1-23cd-4e57-9d94-9cd862eb037b"}}, {"head": {"id": "dd098fd5-347e-47f6-b14d-62189c715a4c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006741406600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24026f7-5c28-4610-a90b-ccc4f18f158f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006741496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e48b5c-b8f9-4a8a-a3b5-06751a67826a", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006745383700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb27c31-1c49-4651-9e17-eecbd7d929a5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006771882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f97b28e-b1aa-4609-85a1-a6ed10072050", "name": "entry : default@GenerateLoaderJson cost memory -0.9869613647460938", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006772031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b53cb199-162d-4cf1-b906-a583dfada8b5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006745358100, "endTime": 15006772117000}, "additional": {"logType": "info", "children": [], "durationId": "315c99a1-23cd-4e57-9d94-9cd862eb037b"}}, {"head": {"id": "90626045-64ce-4744-8c9b-f4c46df96eae", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006789400700, "endTime": 15007895246000}, "additional": {"children": ["7eb12c1c-564e-419e-9962-c00d10d4c959", "9003b0a8-e1cc-4fa1-8fee-776358a08551", "bc85b108-685d-4ee2-8dc5-ff79cdf077dc", "6ca32f00-1859-47a6-a2da-d98f91f56292"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "561a50a3-ccdd-45dc-b67f-450d30367f17", "logId": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "561a50a3-ccdd-45dc-b67f-450d30367f17", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006783517100}, "additional": {"logType": "detail", "children": [], "durationId": "90626045-64ce-4744-8c9b-f4c46df96eae"}}, {"head": {"id": "0a3a2e74-e829-4123-a723-65dadc81791d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006784245700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da403552-53b4-4590-b2df-20286af2b950", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006784394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78767122-db2d-46f9-92db-76eb2480fdd2", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006785823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b5a014-fc1e-4851-be5d-741c33f451f7", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006789441400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80fe7e58-e2ef-460a-b027-d46ea9954511", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006811879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ba21a1-cdb2-4018-9f1f-c5a47aa5e43a", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006812035000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb12c1c-564e-419e-9962-c00d10d4c959", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006813215200, "endTime": 15006878816400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90626045-64ce-4744-8c9b-f4c46df96eae", "logId": "50201b53-ef7f-4681-b329-f5bc5c9d23b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50201b53-ef7f-4681-b329-f5bc5c9d23b6", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006813215200, "endTime": 15006878816400}, "additional": {"logType": "info", "children": [], "durationId": "7eb12c1c-564e-419e-9962-c00d10d4c959", "parent": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e"}}, {"head": {"id": "29e24a21-7af4-4b6d-b3f7-2c741485153e", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006879166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9003b0a8-e1cc-4fa1-8fee-776358a08551", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006880172800, "endTime": 15007582881000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90626045-64ce-4744-8c9b-f4c46df96eae", "logId": "91854f23-65be-4909-b7ab-9363ab4b72fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cba7fb8e-4175-4745-997d-490aa61bde44", "name": "current process  memoryUsage: {\n  rss: 99721216,\n  heapTotal: 116162560,\n  heapUsed: 108362288,\n  external: 3100237,\n  arrayBuffers: 94102\n} os memoryUsage :6.924388885498047", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006881091800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d384a3d5-96da-4f48-a706-0c6181cba65b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007580364000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91854f23-65be-4909-b7ab-9363ab4b72fc", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006880172800, "endTime": 15007582881000}, "additional": {"logType": "info", "children": [], "durationId": "9003b0a8-e1cc-4fa1-8fee-776358a08551", "parent": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e"}}, {"head": {"id": "153f2c67-3a62-4643-a68b-ce73cba20dd8", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007583031500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc85b108-685d-4ee2-8dc5-ff79cdf077dc", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007584520600, "endTime": 15007673063700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90626045-64ce-4744-8c9b-f4c46df96eae", "logId": "93048580-b56c-4b88-8946-80056ab1a00d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca5a5ea8-fe4f-4e11-b9b4-dc97b22b5257", "name": "current process  memoryUsage: {\n  rss: 99766272,\n  heapTotal: 116162560,\n  heapUsed: 108623168,\n  external: 3100363,\n  arrayBuffers: 94243\n} os memoryUsage :6.582653045654297", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007585954500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639eeddc-bec2-4af7-8315-dd8c421bc7bd", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007670614100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93048580-b56c-4b88-8946-80056ab1a00d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007584520600, "endTime": 15007673063700}, "additional": {"logType": "info", "children": [], "durationId": "bc85b108-685d-4ee2-8dc5-ff79cdf077dc", "parent": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e"}}, {"head": {"id": "774f8c11-99a9-4178-9c7e-7cdf2c80882f", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007673716900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca32f00-1859-47a6-a2da-d98f91f56292", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007675368300, "endTime": 15007893523000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90626045-64ce-4744-8c9b-f4c46df96eae", "logId": "d2db4a30-0422-47fb-b08e-1c07c1dce146"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e74f287d-75f0-400e-9c04-d42a2236c511", "name": "current process  memoryUsage: {\n  rss: 99803136,\n  heapTotal: 116162560,\n  heapUsed: 108898824,\n  external: 3100489,\n  arrayBuffers: 95183\n} os memoryUsage :6.590869903564453", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007676523400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee149233-aca7-498b-b9b4-ad442a28d033", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007889463000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2db4a30-0422-47fb-b08e-1c07c1dce146", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007675368300, "endTime": 15007893523000}, "additional": {"logType": "info", "children": [], "durationId": "6ca32f00-1859-47a6-a2da-d98f91f56292", "parent": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e"}}, {"head": {"id": "8091ac92-678c-479e-b890-d6e66b7a5bda", "name": "entry : default@PreviewCompileResource cost memory 0.012451171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007894792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7501a6-e895-4e94-a2ed-9350194334ec", "name": "runTaskFromQueue task cost before running: 1 s 842 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007895095400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbbd482-73cd-45a6-982f-b5d3e39c3d9e", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006789400700, "endTime": 15007895246000, "totalTime": 1105601300}, "additional": {"logType": "info", "children": ["50201b53-ef7f-4681-b329-f5bc5c9d23b6", "91854f23-65be-4909-b7ab-9363ab4b72fc", "93048580-b56c-4b88-8946-80056ab1a00d", "d2db4a30-0422-47fb-b08e-1c07c1dce146"], "durationId": "90626045-64ce-4744-8c9b-f4c46df96eae"}}, {"head": {"id": "04fdecf1-bb1c-4a03-931a-61a492a91e91", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899503800, "endTime": 15007899963700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d961afae-5c54-4f78-9fee-6f118188560a", "logId": "0c746899-453e-4e5b-a5c2-0c613d138936"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d961afae-5c54-4f78-9fee-6f118188560a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007898709400}, "additional": {"logType": "detail", "children": [], "durationId": "04fdecf1-bb1c-4a03-931a-61a492a91e91"}}, {"head": {"id": "d548970a-ef09-4d26-926a-7986ef078047", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899277900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e65a6e-90d1-4bfa-842c-d5a40cd4d9c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899392500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd32537-9b6d-45bf-b132-9d94a67277df", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899512400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5486ec73-fa78-4f3a-a225-ed050652b9e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899610200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10cb6063-bcb3-4035-9dfd-9cebdb2febc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c6f8fe-ae74-4c94-9334-7af3c405a15a", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899743900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83be67f8-87bc-43dc-908e-73137032cc98", "name": "runTaskFromQueue task cost before running: 1 s 847 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899867700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c746899-453e-4e5b-a5c2-0c613d138936", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007899503800, "endTime": 15007899963700, "totalTime": 333300}, "additional": {"logType": "info", "children": [], "durationId": "04fdecf1-bb1c-4a03-931a-61a492a91e91"}}, {"head": {"id": "d616dde3-fa9d-4685-a4e2-2a69d9559588", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007904394700, "endTime": 15007913668900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "a125cc8a-d9bc-4593-9123-2cf997b9220b", "logId": "c77bd11a-141e-46a7-a3c2-66cacd67db99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a125cc8a-d9bc-4593-9123-2cf997b9220b", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007902490500}, "additional": {"logType": "detail", "children": [], "durationId": "d616dde3-fa9d-4685-a4e2-2a69d9559588"}}, {"head": {"id": "53c00a53-31b9-4b95-b2cb-7124fe9079db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007903089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a5cbc8-0e90-489c-9937-f164252f0e12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007903245600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84004b44-e1cf-4486-91f5-2ff629b4bed2", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007904412100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05528235-39fb-4921-9d23-2c4e5ebc5321", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007906191300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6175f5d-1aae-4681-bc64-9c894f6c7bdf", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007906338800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ff85e0-13f8-4eab-bd1c-2200598fb544", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007906440600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33953fc9-e8f6-4d83-8588-aa74211fd29a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007906505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "672e4328-0ed3-4621-839b-75c3d8bd482a", "name": "entry : default@CopyPreviewProfile cost memory 0.2133026123046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007913290700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea41c47-cc52-4ae0-9aaf-14f91724506c", "name": "runTaskFromQueue task cost before running: 1 s 861 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007913542400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c77bd11a-141e-46a7-a3c2-66cacd67db99", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007904394700, "endTime": 15007913668900, "totalTime": 9100600}, "additional": {"logType": "info", "children": [], "durationId": "d616dde3-fa9d-4685-a4e2-2a69d9559588"}}, {"head": {"id": "4ed5d201-19a8-4272-bca4-812a56df2056", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007918302600, "endTime": 15007919512800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "dff0afe5-2e4e-4594-989d-c952b93b54f9", "logId": "f194177c-2e75-4c9f-a9ff-2ef1f29456e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dff0afe5-2e4e-4594-989d-c952b93b54f9", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007916441800}, "additional": {"logType": "detail", "children": [], "durationId": "4ed5d201-19a8-4272-bca4-812a56df2056"}}, {"head": {"id": "e2870d3c-e46b-4b0b-9ca6-92d9f0d6bdd2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007917080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ea967ab-b978-40f0-b7ad-50c6ffefa888", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007917227900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c00b8c5e-c49f-44c5-a939-53aebd70c8e9", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007918317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce349ff-c681-4cf0-a296-23631665e075", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007918469900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42acdd5-9e22-434e-ba4b-334ce00e4c76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007919177100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e286a7d1-539a-4994-b147-19889d5b6f23", "name": "entry : default@ReplacePreviewerPage cost memory -1.7140274047851562", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007919326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74615e39-e12a-4196-a1df-2b2b48d0c770", "name": "runTaskFromQueue task cost before running: 1 s 867 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007919440900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f194177c-2e75-4c9f-a9ff-2ef1f29456e5", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007918302600, "endTime": 15007919512800, "totalTime": 1109300}, "additional": {"logType": "info", "children": [], "durationId": "4ed5d201-19a8-4272-bca4-812a56df2056"}}, {"head": {"id": "a0db2f67-0280-495f-88b5-4ca3bb52c6f2", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922274800, "endTime": 15007922687700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "13f96b68-d107-4da0-8da5-e61ac53b2958", "logId": "b94748c2-97c2-44e3-9aa9-f0369b3ce48b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13f96b68-d107-4da0-8da5-e61ac53b2958", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922143200}, "additional": {"logType": "detail", "children": [], "durationId": "a0db2f67-0280-495f-88b5-4ca3bb52c6f2"}}, {"head": {"id": "b175c749-2512-4490-be0e-de8325ec3748", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922290300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1fd5fc-1ade-4054-b620-a9d08de99919", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfa2200-fe1c-4a07-9eb5-e978dfbd3297", "name": "runTaskFromQueue task cost before running: 1 s 870 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94748c2-97c2-44e3-9aa9-f0369b3ce48b", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007922274800, "endTime": 15007922687700, "totalTime": 289000}, "additional": {"logType": "info", "children": [], "durationId": "a0db2f67-0280-495f-88b5-4ca3bb52c6f2"}}, {"head": {"id": "f4959bac-f6ba-4df5-a63e-050c0b72cc21", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007926809500, "endTime": 15007931565600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "0990e963-233c-4120-9892-cb7468589e03", "logId": "cfa6335c-c39e-4059-b8e7-2f9e817eb461"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0990e963-233c-4120-9892-cb7468589e03", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007924899600}, "additional": {"logType": "detail", "children": [], "durationId": "f4959bac-f6ba-4df5-a63e-050c0b72cc21"}}, {"head": {"id": "63017898-4762-4647-a8da-58f0e0e6fd90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007925570600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e179944b-a987-42c6-83df-4ddb73f17c5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007925701400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c75380-328a-4cfb-94f8-0c08e59962fc", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007926827100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4d76f4-2285-466b-80c5-e6fe6ae0ffaa", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007929761200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ca97d2-37b0-4a45-a23a-e361baa5d722", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007929936400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd851f1-6669-4669-bf4a-e2c2d711dddc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007930084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9939aa7-4fe8-46ed-8240-5eb6217d4b46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007930185000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f1f8bd-8be4-43be-93eb-26005e4315da", "name": "entry : default@PreviewUpdateAssets cost memory 0.13339996337890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007931258300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e0d26c-178b-4261-8a30-620f97bfc6da", "name": "runTaskFromQueue task cost before running: 1 s 879 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007931449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa6335c-c39e-4059-b8e7-2f9e817eb461", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007926809500, "endTime": 15007931565600, "totalTime": 4599700}, "additional": {"logType": "info", "children": [], "durationId": "f4959bac-f6ba-4df5-a63e-050c0b72cc21"}}, {"head": {"id": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007944662200, "endTime": 15060977888100}, "additional": {"children": ["64534b83-8326-480f-800d-b3e5e7536bb4"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "858df95e-d42b-4c37-8630-11202668ae82", "logId": "714bd6fe-f0cf-44a7-99ca-58064e68c0c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "858df95e-d42b-4c37-8630-11202668ae82", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007935064000}, "additional": {"logType": "detail", "children": [], "durationId": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6"}}, {"head": {"id": "46a08c53-fa1d-41fb-9739-34b023b184b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007935864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f6af00-c1d5-4200-9c05-106d0c0ef34b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007936000500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b63e5a5-4478-4138-817f-c2f53781738e", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007944680300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd97d40d-a6b2-431e-b0a3-1592f1c3059a", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007957872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8190f667-99b5-4b2c-a385-d4cda66d540b", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007958051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64534b83-8326-480f-800d-b3e5e7536bb4", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker9", "startTime": 15008096346000, "endTime": 15060977584200}, "additional": {"children": ["fb37fe1a-6430-4258-af5d-4bb49d45befd", "070e1122-fdd7-4a1e-8854-3670319e96d4", "fd2f83d9-f512-44f5-a4f1-f3e5c63cb38c", "07fb7078-f51f-4fed-89e0-d1385cf90daf", "f67ec88d-dffd-4b69-98ed-564ad8774e61"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6", "logId": "f001724c-606c-432b-a875-2a194d09287d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3149cee-74c0-4c68-8148-14d038b7e233", "name": "entry : default@PreviewArkTS cost memory -0.5725173950195312", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15008101547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c059ea5-5a34-4ee1-b571-8f2b24c9dbb2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15049283230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb37fe1a-6430-4258-af5d-4bb49d45befd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15049284406700, "endTime": 15049284426700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64534b83-8326-480f-800d-b3e5e7536bb4", "logId": "a5b78d1e-264e-46b5-a971-68f4771009f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5b78d1e-264e-46b5-a971-68f4771009f6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15049284406700, "endTime": 15049284426700}, "additional": {"logType": "info", "children": [], "durationId": "fb37fe1a-6430-4258-af5d-4bb49d45befd", "parent": "f001724c-606c-432b-a875-2a194d09287d"}}, {"head": {"id": "176766f8-db7b-4399-9671-262aa939b9d5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055610740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070e1122-fdd7-4a1e-8854-3670319e96d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055612455800, "endTime": 15055612483400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64534b83-8326-480f-800d-b3e5e7536bb4", "logId": "d75d56eb-0248-4c8d-96a9-2030fe053957"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d75d56eb-0248-4c8d-96a9-2030fe053957", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055612455800, "endTime": 15055612483400}, "additional": {"logType": "info", "children": [], "durationId": "070e1122-fdd7-4a1e-8854-3670319e96d4", "parent": "f001724c-606c-432b-a875-2a194d09287d"}}, {"head": {"id": "8588f218-be1f-4545-9812-e1c09f65d861", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055612615100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2f83d9-f512-44f5-a4f1-f3e5c63cb38c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055614042400, "endTime": 15055614072200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64534b83-8326-480f-800d-b3e5e7536bb4", "logId": "9240bd43-8174-4920-96ed-eb113e6c1c82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9240bd43-8174-4920-96ed-eb113e6c1c82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055614042400, "endTime": 15055614072200}, "additional": {"logType": "info", "children": [], "durationId": "fd2f83d9-f512-44f5-a4f1-f3e5c63cb38c", "parent": "f001724c-606c-432b-a875-2a194d09287d"}}, {"head": {"id": "b83a15c3-8fa0-41bb-8669-a25aac2074a5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055614210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07fb7078-f51f-4fed-89e0-d1385cf90daf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055616056700, "endTime": 15055616108400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64534b83-8326-480f-800d-b3e5e7536bb4", "logId": "8a353e7d-194a-4c03-98f6-ded2bb4d0a08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a353e7d-194a-4c03-98f6-ded2bb4d0a08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15055616056700, "endTime": 15055616108400}, "additional": {"logType": "info", "children": [], "durationId": "07fb7078-f51f-4fed-89e0-d1385cf90daf", "parent": "f001724c-606c-432b-a875-2a194d09287d"}}, {"head": {"id": "7faad959-b579-4110-8a01-5a8bd4a28f3a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15060976496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f67ec88d-dffd-4b69-98ed-564ad8774e61", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15060977485200, "endTime": 15060977503200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64534b83-8326-480f-800d-b3e5e7536bb4", "logId": "446e4bfc-bc64-41a5-a230-4c49ffe3d37d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "446e4bfc-bc64-41a5-a230-4c49ffe3d37d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15060977485200, "endTime": 15060977503200}, "additional": {"logType": "info", "children": [], "durationId": "f67ec88d-dffd-4b69-98ed-564ad8774e61", "parent": "f001724c-606c-432b-a875-2a194d09287d"}}, {"head": {"id": "f001724c-606c-432b-a875-2a194d09287d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker9", "startTime": 15008096346000, "endTime": 15060977584200}, "additional": {"logType": "error", "children": ["a5b78d1e-264e-46b5-a971-68f4771009f6", "d75d56eb-0248-4c8d-96a9-2030fe053957", "9240bd43-8174-4920-96ed-eb113e6c1c82", "8a353e7d-194a-4c03-98f6-ded2bb4d0a08", "446e4bfc-bc64-41a5-a230-4c49ffe3d37d"], "durationId": "64534b83-8326-480f-800d-b3e5e7536bb4", "parent": "714bd6fe-f0cf-44a7-99ca-58064e68c0c1"}}, {"head": {"id": "f4a60857-dc6b-42db-8ff4-135d563df578", "name": "default@PreviewArkTS watch work[9] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15060977635700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "714bd6fe-f0cf-44a7-99ca-58064e68c0c1", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15007944662200, "endTime": 15060977888100}, "additional": {"logType": "error", "children": ["f001724c-606c-432b-a875-2a194d09287d"], "durationId": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6"}}, {"head": {"id": "dc049d44-ca06-4a38-862a-b78e4268976a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15060978113700}, "additional": {"logType": "debug", "children": [], "durationId": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6"}}, {"head": {"id": "8a6717ba-8e5f-4b95-94aa-268a0edb3dff", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:245:5\n Declaration or statement expected.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:166:5\n Cannot find name 'Card'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:245:6\n Cannot find name 'width'. Did you mean the instance member 'this.width'?\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061055876300}, "additional": {"logType": "debug", "children": [], "durationId": "2bec77c9-3b61-4a2c-ab1a-2e1490f3f9a6"}}, {"head": {"id": "70e99626-ec15-450f-b6ee-96b35a843084", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061079212700, "endTime": 15061084542600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67bc90e3-c9b6-4966-972e-3bf64748e113", "logId": "58212383-7b66-4b3a-bafa-84421ad96ec8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58212383-7b66-4b3a-bafa-84421ad96ec8", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061079212700, "endTime": 15061084542600}, "additional": {"logType": "info", "children": [], "durationId": "70e99626-ec15-450f-b6ee-96b35a843084"}}, {"head": {"id": "bf70c257-4dd0-426d-8b79-bd09207c6a28", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15006053240700, "endTime": 15061084898800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 1}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "b7d9b110-3362-4b8b-ba22-617a202fdb4f", "name": "BUILD FAILED in 55 s 32 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061084999700}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "940a19bc-148f-434d-a629-d232806af0c1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96dda05e-dec3-42c3-bf40-bf46de158a82", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6001ec70-518a-4fbc-8f77-6a5e72bc9e5d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca86e44-a256-496d-8cf7-b7a14473d4b4", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085547300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4d51a25-c399-4a91-9599-6db04a261cb3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085629400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b01d357e-0405-4b98-84ca-ecf78f9b571c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085707100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3e3623-7b3e-42b8-99b7-9d1aa0872a32", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061085796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5494cb70-5801-42bb-98d3-4a1582884716", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061088347000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81708c34-c9fa-4c21-94c9-1ab9e62dbdba", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061114529200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4c1faf1-64fe-4a73-9d1f-644b158e3583", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061115973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4374cdc-02d3-41b9-b037-66b434f246ab", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061133475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0455112-2fc3-4790-8d97-bf9d498128a6", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:49 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061134391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "669a1b8f-7ac6-4a67-a527-1beb874afa90", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061134657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6d6580-ecab-42d3-8729-74f68f0caff2", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061135647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0999dd-a75c-460d-98b7-990f739280c9", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061136589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e6c1aa-5232-46c1-982b-8f47d106b7a9", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061137007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49cb096-44be-4bad-a00e-5ee98aa02155", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061137350600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f57562a-2841-4548-b5f9-d3805fab72fb", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061137728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ad05c1-b7dd-427e-a3ca-f23497360d30", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061140949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ac66eb-d5c2-488d-8ad1-23cac182c456", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061141909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f8cd63d-7b7d-4086-93ae-76f876e410d7", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061142001200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47790c6-683b-418b-8323-3d8545033fdc", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061142328300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "467fbaff-40b1-474c-9912-58d23eda2598", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061143308300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f49a70c-58ef-43ab-ae93-afd2662cbe88", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061155975900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029c9e9e-2a6c-4ff3-a524-2de2db0e864b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061156335900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71162b9-8738-4e64-9ea2-dfc2f7af4c25", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061156644100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d09b8d-0c54-42a5-87f1-12ef676f8239", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061156963600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e79f2b7a-0607-40d5-ba8e-c8115ad0d102", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:20 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15061157262300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}