{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "a9421c61-a131-43df-8bc6-ed2ed0edca18", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15527882411000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b47e9bc-4230-4a63-9387-98e92079f24d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15643398118700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f338ef2-3bfe-4272-bf2a-fed971abd169", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15643398430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cc4246-a372-4e20-8d19-0148e7660b68", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645539903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645548265900, "endTime": 15646272431000}, "additional": {"children": ["6abc05fe-bb38-492d-a6a3-ba8b8276b0d3", "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "b97f80c8-e81c-4cf5-9fb2-4d81ded9095d", "fd7e3871-4e04-4bb1-b419-84bd605b0488", "9b81d594-c2dd-4998-9db0-08ba87705d3d", "aacb30d7-3402-4b5e-a1c4-b7d2d7a31431", "27380b99-4e04-418c-806e-b1145d6fae5e"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6abc05fe-bb38-492d-a6a3-ba8b8276b0d3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645548267600, "endTime": 15646012931500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "5110dac5-e444-4975-8920-f0e82393b2f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646012962700, "endTime": 15646270731300}, "additional": {"children": ["a32d0b5a-d91b-455f-ad66-57fff41fdf58", "5fdd6a46-d3ff-444f-9438-11f83da87d64", "11aad75e-ac6f-43ff-9939-258df03517c5", "3623f0b1-15ee-404d-b40b-2741c6e72488", "973f0c73-742b-493a-bb68-4a080d6186af", "4666bb5d-b125-4837-a46b-682b49fea33a", "cfe28257-9fa0-4c25-9bcc-a1b8e60632a5", "041014f8-6866-4354-8ac1-e910fc57e065", "545a9a1e-8b80-49f2-800f-c76a4f121494"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "f702e35d-0b74-412b-a358-25072ffa961c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b97f80c8-e81c-4cf5-9fb2-4d81ded9095d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646270756700, "endTime": 15646272418400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "dcee648d-2ed6-4c21-9256-9935af120678"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd7e3871-4e04-4bb1-b419-84bd605b0488", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646272424900, "endTime": 15646272426500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "47adb700-62ca-49f7-acaf-ed74f0dceb42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b81d594-c2dd-4998-9db0-08ba87705d3d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645910761200, "endTime": 15645910801100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "a6fd870a-264e-45f9-a904-0a9e304bce93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6fd870a-264e-45f9-a904-0a9e304bce93", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645910761200, "endTime": 15645910801100}, "additional": {"logType": "info", "children": [], "durationId": "9b81d594-c2dd-4998-9db0-08ba87705d3d", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "aacb30d7-3402-4b5e-a1c4-b7d2d7a31431", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645915469400, "endTime": 15645915490400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "3652f13b-d387-4579-a467-735942062e07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3652f13b-d387-4579-a467-735942062e07", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645915469400, "endTime": 15645915490400}, "additional": {"logType": "info", "children": [], "durationId": "aacb30d7-3402-4b5e-a1c4-b7d2d7a31431", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "1a0a4e45-ee77-4dad-94d5-e4c40d0c47ce", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645915538300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a01100-7925-4fce-9ff4-72a98d654543", "name": "Cache service initialization finished in 97 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646012680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5110dac5-e444-4975-8920-f0e82393b2f5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645548267600, "endTime": 15646012931500}, "additional": {"logType": "info", "children": [], "durationId": "6abc05fe-bb38-492d-a6a3-ba8b8276b0d3", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "a32d0b5a-d91b-455f-ad66-57fff41fdf58", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646022851000, "endTime": 15646022859400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "ff7f4bfe-e9c6-4d55-9ca4-c089b49ad750"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fdd6a46-d3ff-444f-9438-11f83da87d64", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646022874400, "endTime": 15646030436600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "37538ae4-7445-4671-9ce3-af30a2777c82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11aad75e-ac6f-43ff-9939-258df03517c5", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646030469800, "endTime": 15646166268900}, "additional": {"children": ["92dae990-86e5-43cd-8eb0-ed958df4f39c", "c4642b38-0696-4106-908e-6bd7bec7a59b", "37151e5d-47bb-4fba-911f-ded4e5fc74ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "00f06d72-e2ae-4b61-a433-34c60309237d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3623f0b1-15ee-404d-b40b-2741c6e72488", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646166310100, "endTime": 15646203337500}, "additional": {"children": ["51177750-2fbd-43e5-b451-c663ba9afa4b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "76ad5398-2ed0-499b-b9c8-775295205384"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "973f0c73-742b-493a-bb68-4a080d6186af", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646203354100, "endTime": 15646233327000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "98081f16-7c0a-4a39-a7f4-7bfa95a85c91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4666bb5d-b125-4837-a46b-682b49fea33a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646234957800, "endTime": 15646257960300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "fe918a29-cceb-4781-91ce-8e60014d8d56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfe28257-9fa0-4c25-9bcc-a1b8e60632a5", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646257985300, "endTime": 15646270523600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "72ba1a5b-d88a-4f32-b0b3-d6550f50dae8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "041014f8-6866-4354-8ac1-e910fc57e065", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646270560500, "endTime": 15646270717900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "db858b53-2a1b-4e3e-9bbd-2bb1c34b09d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff7f4bfe-e9c6-4d55-9ca4-c089b49ad750", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646022851000, "endTime": 15646022859400}, "additional": {"logType": "info", "children": [], "durationId": "a32d0b5a-d91b-455f-ad66-57fff41fdf58", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "37538ae4-7445-4671-9ce3-af30a2777c82", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646022874400, "endTime": 15646030436600}, "additional": {"logType": "info", "children": [], "durationId": "5fdd6a46-d3ff-444f-9438-11f83da87d64", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "92dae990-86e5-43cd-8eb0-ed958df4f39c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646032426100, "endTime": 15646032461900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11aad75e-ac6f-43ff-9939-258df03517c5", "logId": "03636491-4627-4f68-9b1d-ea27036e363d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03636491-4627-4f68-9b1d-ea27036e363d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646032426100, "endTime": 15646032461900}, "additional": {"logType": "info", "children": [], "durationId": "92dae990-86e5-43cd-8eb0-ed958df4f39c", "parent": "00f06d72-e2ae-4b61-a433-34c60309237d"}}, {"head": {"id": "c4642b38-0696-4106-908e-6bd7bec7a59b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646039720800, "endTime": 15646164253300}, "additional": {"children": ["f2f4aefb-cc43-4a9b-a205-78d9d3ffd30c", "9aa0dfc8-7a20-4ce3-9f1b-5149aa381117"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11aad75e-ac6f-43ff-9939-258df03517c5", "logId": "d0de272a-77df-484c-bade-362cd91cfa58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2f4aefb-cc43-4a9b-a205-78d9d3ffd30c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646039724900, "endTime": 15646049398700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c4642b38-0696-4106-908e-6bd7bec7a59b", "logId": "f80db20f-aee6-446f-9b4a-c3590797def3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aa0dfc8-7a20-4ce3-9f1b-5149aa381117", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646049451200, "endTime": 15646164223300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c4642b38-0696-4106-908e-6bd7bec7a59b", "logId": "87653dcb-8314-454c-be74-e264c4dc1d30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6293b374-6535-420e-9f57-d14355e1fbf8", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646039744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49fb83b2-ece0-4282-b66f-0709bc37096a", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646049045200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80db20f-aee6-446f-9b4a-c3590797def3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646039724900, "endTime": 15646049398700}, "additional": {"logType": "info", "children": [], "durationId": "f2f4aefb-cc43-4a9b-a205-78d9d3ffd30c", "parent": "d0de272a-77df-484c-bade-362cd91cfa58"}}, {"head": {"id": "89416658-1e00-4329-86c6-b0eddb654715", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646049492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232c9e65-b683-4c34-acae-dcb6db288278", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646064172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9b35cd-6585-4015-8af0-95daccc3b70a", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646064338400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884af2c0-cf87-4762-ba22-4914a12192be", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646064518100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0457ce40-fcbf-4845-9cd7-16f0e18a1be5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646064668900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e2d08d6-b15d-4582-8070-8dc1bbfd9e4c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646067407200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a91b05-b0c8-4e41-9643-18ca382b2ef3", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646072679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebbc159-4d7b-44cc-b9fe-18270e4a0e8e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646083750000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a56aca-b182-40be-a45d-239bf75a7621", "name": "Sdk init in 51 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646125807200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4766e2-6960-4a8e-9484-9ca225feaa1b", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646125959600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 10}, "markType": "other"}}, {"head": {"id": "30e97b10-c44f-4a1b-835d-94db2627bc34", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646125976200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 10}, "markType": "other"}}, {"head": {"id": "574f72e6-b5b9-4ff0-af77-972fbf4fba9e", "name": "Project task initialization takes 37 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646163581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeaf9c43-3926-4731-92e9-f55039eac3a4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646163795000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ad7cb9-ebbb-4f14-a27a-fb5ff0a0c99c", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646163945100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba17f5b-aab9-43ce-b166-ced1ae6102fb", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646164063800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87653dcb-8314-454c-be74-e264c4dc1d30", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646049451200, "endTime": 15646164223300}, "additional": {"logType": "info", "children": [], "durationId": "9aa0dfc8-7a20-4ce3-9f1b-5149aa381117", "parent": "d0de272a-77df-484c-bade-362cd91cfa58"}}, {"head": {"id": "d0de272a-77df-484c-bade-362cd91cfa58", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646039720800, "endTime": 15646164253300}, "additional": {"logType": "info", "children": ["f80db20f-aee6-446f-9b4a-c3590797def3", "87653dcb-8314-454c-be74-e264c4dc1d30"], "durationId": "c4642b38-0696-4106-908e-6bd7bec7a59b", "parent": "00f06d72-e2ae-4b61-a433-34c60309237d"}}, {"head": {"id": "37151e5d-47bb-4fba-911f-ded4e5fc74ed", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646166202100, "endTime": 15646166226900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11aad75e-ac6f-43ff-9939-258df03517c5", "logId": "523523de-45f1-480b-ae1c-4e7873211a54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "523523de-45f1-480b-ae1c-4e7873211a54", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646166202100, "endTime": 15646166226900}, "additional": {"logType": "info", "children": [], "durationId": "37151e5d-47bb-4fba-911f-ded4e5fc74ed", "parent": "00f06d72-e2ae-4b61-a433-34c60309237d"}}, {"head": {"id": "00f06d72-e2ae-4b61-a433-34c60309237d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646030469800, "endTime": 15646166268900}, "additional": {"logType": "info", "children": ["03636491-4627-4f68-9b1d-ea27036e363d", "d0de272a-77df-484c-bade-362cd91cfa58", "523523de-45f1-480b-ae1c-4e7873211a54"], "durationId": "11aad75e-ac6f-43ff-9939-258df03517c5", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "51177750-2fbd-43e5-b451-c663ba9afa4b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646169664800, "endTime": 15646203311000}, "additional": {"children": ["8644ed30-87fa-409c-9b9c-5e1b11c0ac80", "36bf2a95-f9e6-41b3-bfc7-da41a5e6505e", "2c36fcd1-90af-47f0-9692-d0a40eab14ff"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3623f0b1-15ee-404d-b40b-2741c6e72488", "logId": "607bbe9d-26d8-47bd-a4b5-d607db8617b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8644ed30-87fa-409c-9b9c-5e1b11c0ac80", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646176392900, "endTime": 15646176414000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51177750-2fbd-43e5-b451-c663ba9afa4b", "logId": "f81d3b7e-28fb-4527-8142-1ae7a47cc814"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f81d3b7e-28fb-4527-8142-1ae7a47cc814", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646176392900, "endTime": 15646176414000}, "additional": {"logType": "info", "children": [], "durationId": "8644ed30-87fa-409c-9b9c-5e1b11c0ac80", "parent": "607bbe9d-26d8-47bd-a4b5-d607db8617b5"}}, {"head": {"id": "36bf2a95-f9e6-41b3-bfc7-da41a5e6505e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646179371700, "endTime": 15646200161000}, "additional": {"children": ["82daf272-cc0e-43d1-bbe1-acab15a31260", "ea2c75cb-3656-468d-808b-caaf69a17040"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51177750-2fbd-43e5-b451-c663ba9afa4b", "logId": "d2749a6c-a7de-4fab-a448-4285c5b98f4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82daf272-cc0e-43d1-bbe1-acab15a31260", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646179373900, "endTime": 15646184508100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36bf2a95-f9e6-41b3-bfc7-da41a5e6505e", "logId": "360d4c41-67b7-476d-84e6-9af28fe84e65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea2c75cb-3656-468d-808b-caaf69a17040", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646184640000, "endTime": 15646200143800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36bf2a95-f9e6-41b3-bfc7-da41a5e6505e", "logId": "7613ad74-ceaa-4155-973b-a97eb4428450"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bde29f8-fff3-449d-be06-3eaba6ba0bd1", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646179382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59999ae8-b4e3-4970-8ce3-369159e5f5c9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646184189800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360d4c41-67b7-476d-84e6-9af28fe84e65", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646179373900, "endTime": 15646184508100}, "additional": {"logType": "info", "children": [], "durationId": "82daf272-cc0e-43d1-bbe1-acab15a31260", "parent": "d2749a6c-a7de-4fab-a448-4285c5b98f4e"}}, {"head": {"id": "b52652ae-9d19-4c90-b8da-33cb00b55d8b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646184677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "033fa83c-c5af-4ac1-a404-be40d101130f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646193919300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef01b9d2-0253-46ca-bb5d-c36c0d88d6f0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97fe946-328f-4047-8057-ba0fe888bdc1", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194285800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f008ae1-baca-4cb8-83e3-7450615675d2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fab328f0-4d50-4fd0-9cbb-4aa1867108f4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8480207a-f9f2-46b6-a6bd-cc44f66516e1", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194603000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf0b528-d158-4c98-b2b2-daed72816303", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646194671300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ca2d21-d87f-4975-98ef-919e00b7a0e5", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646199629100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c3c7dcf-bbca-426f-868d-48ad8fb46744", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646199871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ef1e30-b4a1-49b2-8a4f-1d6caf13adc6", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646199979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c097096-679a-4f0c-9f35-1aa6d15d4deb", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646200063800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7613ad74-ceaa-4155-973b-a97eb4428450", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646184640000, "endTime": 15646200143800}, "additional": {"logType": "info", "children": [], "durationId": "ea2c75cb-3656-468d-808b-caaf69a17040", "parent": "d2749a6c-a7de-4fab-a448-4285c5b98f4e"}}, {"head": {"id": "d2749a6c-a7de-4fab-a448-4285c5b98f4e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646179371700, "endTime": 15646200161000}, "additional": {"logType": "info", "children": ["360d4c41-67b7-476d-84e6-9af28fe84e65", "7613ad74-ceaa-4155-973b-a97eb4428450"], "durationId": "36bf2a95-f9e6-41b3-bfc7-da41a5e6505e", "parent": "607bbe9d-26d8-47bd-a4b5-d607db8617b5"}}, {"head": {"id": "2c36fcd1-90af-47f0-9692-d0a40eab14ff", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646203251700, "endTime": 15646203279800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51177750-2fbd-43e5-b451-c663ba9afa4b", "logId": "7c24f0ff-c90d-49c6-8f89-95b494f9d0e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c24f0ff-c90d-49c6-8f89-95b494f9d0e4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646203251700, "endTime": 15646203279800}, "additional": {"logType": "info", "children": [], "durationId": "2c36fcd1-90af-47f0-9692-d0a40eab14ff", "parent": "607bbe9d-26d8-47bd-a4b5-d607db8617b5"}}, {"head": {"id": "607bbe9d-26d8-47bd-a4b5-d607db8617b5", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646169664800, "endTime": 15646203311000}, "additional": {"logType": "info", "children": ["f81d3b7e-28fb-4527-8142-1ae7a47cc814", "d2749a6c-a7de-4fab-a448-4285c5b98f4e", "7c24f0ff-c90d-49c6-8f89-95b494f9d0e4"], "durationId": "51177750-2fbd-43e5-b451-c663ba9afa4b", "parent": "76ad5398-2ed0-499b-b9c8-775295205384"}}, {"head": {"id": "76ad5398-2ed0-499b-b9c8-775295205384", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646166310100, "endTime": 15646203337500}, "additional": {"logType": "info", "children": ["607bbe9d-26d8-47bd-a4b5-d607db8617b5"], "durationId": "3623f0b1-15ee-404d-b40b-2741c6e72488", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "71b91e35-a448-4908-8b96-c3922b12d1d0", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646232948400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342fbc37-7001-48c7-8494-cdb58cf5c67d", "name": "hvigorfile, resolve hvigorfile dependencies in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646233235200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98081f16-7c0a-4a39-a7f4-7bfa95a85c91", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646203354100, "endTime": 15646233327000}, "additional": {"logType": "info", "children": [], "durationId": "973f0c73-742b-493a-bb68-4a080d6186af", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "545a9a1e-8b80-49f2-800f-c76a4f121494", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646234568200, "endTime": 15646234929400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "logId": "d1d1b7cf-e200-4a41-bef4-417be7d87cb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e432635-2bb0-40c8-8110-120bb7ec7883", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646234620100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d1b7cf-e200-4a41-bef4-417be7d87cb2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646234568200, "endTime": 15646234929400}, "additional": {"logType": "info", "children": [], "durationId": "545a9a1e-8b80-49f2-800f-c76a4f121494", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "4453aab0-3046-4296-9f8b-7d9282cab416", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646236810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4600da-d4cb-4052-8b01-296ed632b834", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646257088700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe918a29-cceb-4781-91ce-8e60014d8d56", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646234957800, "endTime": 15646257960300}, "additional": {"logType": "info", "children": [], "durationId": "4666bb5d-b125-4837-a46b-682b49fea33a", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "e29f92da-cf6b-489c-bb8a-0825dc3a62e2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646257997700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa569d2-2657-4286-8aa3-cfd593748385", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646263567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf99fa8-7a6d-4835-85cf-420dd75a60d9", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646263682300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62a9d36-b2fa-42c2-b463-0369e486615d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646263970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38cea15d-b054-47f3-b5a6-1a4502a16906", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646266235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5880853-2778-4944-b7d4-193339571f91", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646266327300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ba1a5b-d88a-4f32-b0b3-d6550f50dae8", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646257985300, "endTime": 15646270523600}, "additional": {"logType": "info", "children": [], "durationId": "cfe28257-9fa0-4c25-9bcc-a1b8e60632a5", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "5ae8fa91-8da6-4322-96d5-fc127bf41a67", "name": "Configuration phase cost:248 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646270586200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db858b53-2a1b-4e3e-9bbd-2bb1c34b09d2", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646270560500, "endTime": 15646270717900}, "additional": {"logType": "info", "children": [], "durationId": "041014f8-6866-4354-8ac1-e910fc57e065", "parent": "f702e35d-0b74-412b-a358-25072ffa961c"}}, {"head": {"id": "f702e35d-0b74-412b-a358-25072ffa961c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646012962700, "endTime": 15646270731300}, "additional": {"logType": "info", "children": ["ff7f4bfe-e9c6-4d55-9ca4-c089b49ad750", "37538ae4-7445-4671-9ce3-af30a2777c82", "00f06d72-e2ae-4b61-a433-34c60309237d", "76ad5398-2ed0-499b-b9c8-775295205384", "98081f16-7c0a-4a39-a7f4-7bfa95a85c91", "fe918a29-cceb-4781-91ce-8e60014d8d56", "72ba1a5b-d88a-4f32-b0b3-d6550f50dae8", "db858b53-2a1b-4e3e-9bbd-2bb1c34b09d2", "d1d1b7cf-e200-4a41-bef4-417be7d87cb2"], "durationId": "9c2a8470-c2d0-42fd-ad41-790b4e65a99d", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "27380b99-4e04-418c-806e-b1145d6fae5e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646272380600, "endTime": 15646272400900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "099c383a-1a21-45a8-8e37-0d5533f1ea76", "logId": "d24b9195-2ca5-4bc6-a51f-636d47be1b54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d24b9195-2ca5-4bc6-a51f-636d47be1b54", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646272380600, "endTime": 15646272400900}, "additional": {"logType": "info", "children": [], "durationId": "27380b99-4e04-418c-806e-b1145d6fae5e", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "dcee648d-2ed6-4c21-9256-9935af120678", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646270756700, "endTime": 15646272418400}, "additional": {"logType": "info", "children": [], "durationId": "b97f80c8-e81c-4cf5-9fb2-4d81ded9095d", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "47adb700-62ca-49f7-acaf-ed74f0dceb42", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646272424900, "endTime": 15646272426500}, "additional": {"logType": "info", "children": [], "durationId": "fd7e3871-4e04-4bb1-b419-84bd605b0488", "parent": "82ea3fb7-aa07-433c-b963-93c3c973f3d0"}}, {"head": {"id": "82ea3fb7-aa07-433c-b963-93c3c973f3d0", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645548265900, "endTime": 15646272431000}, "additional": {"logType": "info", "children": ["5110dac5-e444-4975-8920-f0e82393b2f5", "f702e35d-0b74-412b-a358-25072ffa961c", "dcee648d-2ed6-4c21-9256-9935af120678", "47adb700-62ca-49f7-acaf-ed74f0dceb42", "a6fd870a-264e-45f9-a904-0a9e304bce93", "3652f13b-d387-4579-a467-735942062e07", "d24b9195-2ca5-4bc6-a51f-636d47be1b54"], "durationId": "099c383a-1a21-45a8-8e37-0d5533f1ea76"}}, {"head": {"id": "b8158398-939b-437a-85fd-fd89cea02830", "name": "Configuration task cost before running: 729 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646272580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fef109e-02de-4532-b68e-45c9a7a42137", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646278372000, "endTime": 15646288102800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a3e5b581-0b4c-44c4-9093-a40773eeed34", "logId": "2ff6536e-23d3-4df6-ad2d-f6c9260ed3b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3e5b581-0b4c-44c4-9093-a40773eeed34", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646274263800}, "additional": {"logType": "detail", "children": [], "durationId": "5fef109e-02de-4532-b68e-45c9a7a42137"}}, {"head": {"id": "d5155711-44a7-4f52-80b9-7573f366a89e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646274831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a5039c-e985-48cd-bcce-462b0b8da633", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646274936200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f115aa98-3b8d-44e5-bd82-8492f3ce41c2", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646278386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba67ec9-067b-4efb-afd1-a4dfb93a035a", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646287883200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2c5a7f7-2069-46f8-bcc5-d790a29f83da", "name": "entry : default@PreBuild cost memory -1.5120697021484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646288026500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ff6536e-23d3-4df6-ad2d-f6c9260ed3b3", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646278372000, "endTime": 15646288102800}, "additional": {"logType": "info", "children": [], "durationId": "5fef109e-02de-4532-b68e-45c9a7a42137"}}, {"head": {"id": "072dafee-fd7c-46a6-854f-2a7987258e9c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646293731000, "endTime": 15646295969100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "77718313-d689-421d-8201-f4d10e4cae50", "logId": "930a1a35-b3bf-49e1-98de-ddd697a5052c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77718313-d689-421d-8201-f4d10e4cae50", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646292251400}, "additional": {"logType": "detail", "children": [], "durationId": "072dafee-fd7c-46a6-854f-2a7987258e9c"}}, {"head": {"id": "b4ff0981-7aea-46d0-ac03-55e6d38e1223", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646292855000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c366b5d-88b7-4be4-86fe-21bbb6aefa3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646292971400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32c9184-0d9f-4ed5-b5c7-a0e261abc27b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646293741400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c2cf92d-9df9-4fc1-b7dd-4a14fc6e3a2a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646295794300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27da827a-82a7-4046-a7d6-55dad08fc37d", "name": "entry : default@MergeProfile cost memory 0.11026763916015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646295899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "930a1a35-b3bf-49e1-98de-ddd697a5052c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646293731000, "endTime": 15646295969100}, "additional": {"logType": "info", "children": [], "durationId": "072dafee-fd7c-46a6-854f-2a7987258e9c"}}, {"head": {"id": "2edc798d-450e-4521-8488-576406587518", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646299478600, "endTime": 15646303091300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "15e74b58-62cb-4cdf-b8fa-624e5443af6a", "logId": "22c6fd41-26ec-48ec-ba1a-46636449315d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15e74b58-62cb-4cdf-b8fa-624e5443af6a", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646297911700}, "additional": {"logType": "detail", "children": [], "durationId": "2edc798d-450e-4521-8488-576406587518"}}, {"head": {"id": "3db1e9e2-9b6e-41f8-acdd-68f42be98054", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646298498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa714c27-f106-4e06-a01d-9536278ae718", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646298610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3cddb7d-c553-412c-be9b-c04bcdca0434", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646299489700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80d8807-41e6-4cbd-b644-5e7dd9b7b226", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646300716600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44830b1c-4065-408a-8c06-80ad10aba9cd", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646302869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91bcf6e5-182c-48ee-96da-7ca4c1d33673", "name": "entry : default@CreateBuildProfile cost memory 0.09685516357421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646303005000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c6fd41-26ec-48ec-ba1a-46636449315d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646299478600, "endTime": 15646303091300}, "additional": {"logType": "info", "children": [], "durationId": "2edc798d-450e-4521-8488-576406587518"}}, {"head": {"id": "5ecd8ab0-086a-4caf-ad28-4ee9580df8e2", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306599700, "endTime": 15646307034500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b3c6516a-0ea6-402a-8f2c-f043aa672341", "logId": "0987a640-1180-49e7-89f6-55019eb752a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c6516a-0ea6-402a-8f2c-f043aa672341", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646305014900}, "additional": {"logType": "detail", "children": [], "durationId": "5ecd8ab0-086a-4caf-ad28-4ee9580df8e2"}}, {"head": {"id": "b7c1205c-e2ae-4ff7-a202-7768c9a3dea7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646305647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "815f24e7-17be-4fa3-ac0e-e5ea04b0f6f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646305779700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca42070c-b2ac-4737-bba6-c97f78804c42", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306610900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2e47e1e-3d20-4d70-85c6-a254f4a3d533", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c1e568-d0fd-4bff-b21a-c3f0aa038f83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5701868-a21b-446f-a299-312de3399177", "name": "entry : default@PreCheckSyscap cost memory 0.037322998046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c046c47-f41e-4c9e-84b6-17ddc1060736", "name": "runTaskFromQueue task cost before running: 764 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306972900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0987a640-1180-49e7-89f6-55019eb752a7", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646306599700, "endTime": 15646307034500, "totalTime": 353700}, "additional": {"logType": "info", "children": [], "durationId": "5ecd8ab0-086a-4caf-ad28-4ee9580df8e2"}}, {"head": {"id": "2595cf0d-553a-407e-a76d-a1ac34707beb", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646316541800, "endTime": 15646317877900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1d969e79-f370-498d-9ed6-f1c7552e8604", "logId": "fd6ffdcf-edbf-495c-8cc4-6d63d2be5901"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d969e79-f370-498d-9ed6-f1c7552e8604", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646308936600}, "additional": {"logType": "detail", "children": [], "durationId": "2595cf0d-553a-407e-a76d-a1ac34707beb"}}, {"head": {"id": "5e66d77f-3f41-4da3-bc53-495548f0953a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646309549300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3e89af-8b6b-4392-afaf-a0b071c4cba7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646309665700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb917b83-831a-4a26-ab07-14b86c162608", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646316556600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7371c5-5d18-4e23-b0b5-9c18895f5f40", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646316761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c0a3e9-b6a5-4c26-9931-c3903dc538d1", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646317605600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4888ff-c836-4035-8fd2-a524503d0e4c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06549072265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646317768900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6ffdcf-edbf-495c-8cc4-6d63d2be5901", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646316541800, "endTime": 15646317877900}, "additional": {"logType": "info", "children": [], "durationId": "2595cf0d-553a-407e-a76d-a1ac34707beb"}}, {"head": {"id": "a15228e2-38de-4d33-bf48-44f5787fa0fd", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646323925400, "endTime": 15646373781700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "209a54b4-20ab-4481-a2ce-30089842f752", "logId": "e751793a-9682-451c-804f-a6e5664edbc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "209a54b4-20ab-4481-a2ce-30089842f752", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646321825000}, "additional": {"logType": "detail", "children": [], "durationId": "a15228e2-38de-4d33-bf48-44f5787fa0fd"}}, {"head": {"id": "6083ec77-a426-4e22-b0f9-ae5f41ff754f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646322442000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0bf75f3-e419-4845-b156-28c8bbf15f9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646322570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b164c9-bc30-443b-a0fa-ee840cb3ed12", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646323940800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea019cf3-5c78-4602-a391-844031028d8e", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 50 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646373557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6630283c-55f7-44b0-b166-050b5ad36c04", "name": "entry : default@ProcessProfile cost memory 0.05663299560546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646373705600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e751793a-9682-451c-804f-a6e5664edbc7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646323925400, "endTime": 15646373781700}, "additional": {"logType": "info", "children": [], "durationId": "a15228e2-38de-4d33-bf48-44f5787fa0fd"}}, {"head": {"id": "b931e32a-734e-4dba-bedc-4fffea40b865", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646377880800, "endTime": 15646383936700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cbc201c9-af80-45b5-8057-66ee09dcda9a", "logId": "27aa73f0-4f98-42f5-8507-442ff196b837"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbc201c9-af80-45b5-8057-66ee09dcda9a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646375501200}, "additional": {"logType": "detail", "children": [], "durationId": "b931e32a-734e-4dba-bedc-4fffea40b865"}}, {"head": {"id": "1b05fd18-4e4a-4838-ac1a-975b3b51c9b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646376020200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090baa69-e011-4cb6-b5e7-1f9511084549", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646376124800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2a8a3ea-3358-4f3a-9b52-c4fe45857c52", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646377889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14f728b-a04d-43cb-ad15-5bd9d772ccb6", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646383661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c674be-6f3f-45d7-9f6d-fd6daaebe443", "name": "entry : default@ProcessRouterMap cost memory 0.18732452392578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646383822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27aa73f0-4f98-42f5-8507-442ff196b837", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646377880800, "endTime": 15646383936700}, "additional": {"logType": "info", "children": [], "durationId": "b931e32a-734e-4dba-bedc-4fffea40b865"}}, {"head": {"id": "a7b5a353-9cc0-41a2-a95f-78fd668342a8", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646390589800, "endTime": 15646393246400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c9d6cc61-78f6-402a-9707-726b18343b16", "logId": "cfc86997-64a2-4180-84ce-421adc01b597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9d6cc61-78f6-402a-9707-726b18343b16", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646386975600}, "additional": {"logType": "detail", "children": [], "durationId": "a7b5a353-9cc0-41a2-a95f-78fd668342a8"}}, {"head": {"id": "4b6e31eb-88d0-47cf-9144-96e6de908959", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646387472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb2839d-313d-43b4-87b9-1e809c07b88d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646387561900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e06866b-bf8a-4a57-8967-e1d0f3f6bf0e", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646388503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53b60fc-cf63-44ae-815f-d79b89ac6bd1", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646391701500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba4a277-5915-49f5-855e-b59d40f26b20", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646391832100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50684241-5b64-4180-8684-174eb1e6770e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646391896100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb5a5f0-9a44-4855-aa89-3603b1c01f92", "name": "entry : default@PreviewProcessResource cost memory 0.06819915771484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646391974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b941b190-4284-4940-bcd6-3c35a8c471dd", "name": "runTaskFromQueue task cost before running: 850 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646393158100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc86997-64a2-4180-84ce-421adc01b597", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646390589800, "endTime": 15646393246400, "totalTime": 1449400}, "additional": {"logType": "info", "children": [], "durationId": "a7b5a353-9cc0-41a2-a95f-78fd668342a8"}}, {"head": {"id": "a36c2d6d-3b91-4c5d-8f2b-34d78fa1f9b1", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646399910000, "endTime": 15646423873300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5c7e7ea8-d920-4464-bf66-00a1b72fc038", "logId": "57e284d9-3a8f-40c4-b421-7a0a7973b610"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c7e7ea8-d920-4464-bf66-00a1b72fc038", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646396093600}, "additional": {"logType": "detail", "children": [], "durationId": "a36c2d6d-3b91-4c5d-8f2b-34d78fa1f9b1"}}, {"head": {"id": "bb008724-33e9-415d-ab56-317958a2798d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646396571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234924d6-b668-4622-bf47-5a0856fdd785", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646396665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211ad674-c6fd-424b-93a5-0bd7d3b18253", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646399931400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5437a7b2-c268-4579-92a0-edfb4a787fd8", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646423547000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd00db9-163f-4ac6-8dc8-dd8d57cd061c", "name": "entry : default@GenerateLoaderJson cost memory -0.9911346435546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646423759200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e284d9-3a8f-40c4-b421-7a0a7973b610", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646399910000, "endTime": 15646423873300}, "additional": {"logType": "info", "children": [], "durationId": "a36c2d6d-3b91-4c5d-8f2b-34d78fa1f9b1"}}, {"head": {"id": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646440219600, "endTime": 15647111262400}, "additional": {"children": ["ae4fbe4c-6f45-4c7e-b0af-f9c0b0f69a09", "15c650e7-6301-4139-a866-f520e82f3cbc", "ff36ce00-93bb-401e-a6b6-4d1486ccea86", "fc17ab3d-f78b-438c-a468-88f1afc7a354"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "961754b6-feb8-4a61-a098-c8720555431d", "logId": "57f44c4d-8f16-4417-9ea0-7403a9e6636d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "961754b6-feb8-4a61-a098-c8720555431d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646435747500}, "additional": {"logType": "detail", "children": [], "durationId": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4"}}, {"head": {"id": "cda854cf-b459-4980-b8e0-11dfab4e3825", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646436532000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba07e47-e33a-406f-9613-b12c205dd7d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646436671000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "561a6b38-bfe2-4d4d-8105-f7d5fc20e395", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646437831900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d55983-91cd-4243-970d-2f3a5af0405b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646440248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3beb830-e8b1-4a79-a448-5c3627bed92d", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646465009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "501aedb8-56f5-4468-8955-f6163c63351e", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646465203800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae4fbe4c-6f45-4c7e-b0af-f9c0b0f69a09", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646466446900, "endTime": 15646525581800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4", "logId": "be83f7e3-1d7e-410f-b19c-c26100f4f510"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be83f7e3-1d7e-410f-b19c-c26100f4f510", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646466446900, "endTime": 15646525581800}, "additional": {"logType": "info", "children": [], "durationId": "ae4fbe4c-6f45-4c7e-b0af-f9c0b0f69a09", "parent": "57f44c4d-8f16-4417-9ea0-7403a9e6636d"}}, {"head": {"id": "1aa3603d-9c2d-4b5c-bd89-a99208f15df6", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646525954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c650e7-6301-4139-a866-f520e82f3cbc", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646527192300, "endTime": 15646764646400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4", "logId": "1968b11b-6a7a-4146-90f6-8463e0623c10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "340d9979-0db9-4964-af39-f4ef3efcff31", "name": "current process  memoryUsage: {\n  rss: 108220416,\n  heapTotal: 123240448,\n  heapUsed: 105416880,\n  external: 3075151,\n  arrayBuffers: 69016\n} os memoryUsage :6.282917022705078", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646528311900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f372c0-3b3f-4a86-b8d7-d16347666a79", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646761933900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1968b11b-6a7a-4146-90f6-8463e0623c10", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646527192300, "endTime": 15646764646400}, "additional": {"logType": "info", "children": [], "durationId": "15c650e7-6301-4139-a866-f520e82f3cbc", "parent": "57f44c4d-8f16-4417-9ea0-7403a9e6636d"}}, {"head": {"id": "4251bbce-dad7-4433-8a43-ac1e5c074f77", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646764790300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff36ce00-93bb-401e-a6b6-4d1486ccea86", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646765974200, "endTime": 15646874046400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4", "logId": "462cf2a3-cb5a-4699-90a7-3859bd6f36a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2c58fed-767a-49a8-94ff-c6b2d3b7f589", "name": "current process  memoryUsage: {\n  rss: 108232704,\n  heapTotal: 123240448,\n  heapUsed: 105677288,\n  external: 3075277,\n  arrayBuffers: 69157\n} os memoryUsage :6.2886810302734375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646766979600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15e6e918-e342-4264-90eb-636580892009", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646871436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462cf2a3-cb5a-4699-90a7-3859bd6f36a8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646765974200, "endTime": 15646874046400}, "additional": {"logType": "info", "children": [], "durationId": "ff36ce00-93bb-401e-a6b6-4d1486ccea86", "parent": "57f44c4d-8f16-4417-9ea0-7403a9e6636d"}}, {"head": {"id": "5d58688d-c9b4-4ef8-b0e3-583d08a40b57", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646874276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc17ab3d-f78b-438c-a468-88f1afc7a354", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646875562300, "endTime": 15647110038900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4", "logId": "0465db7f-84a2-4c36-882d-f9748ccc5502"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42f7c721-e2be-4134-893f-000e5cc6ec58", "name": "current process  memoryUsage: {\n  rss: 108244992,\n  heapTotal: 123240448,\n  heapUsed: 105970112,\n  external: 3083595,\n  arrayBuffers: 78289\n} os memoryUsage :6.281658172607422", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646876626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a1293a3-18d8-4689-809e-098a210ece83", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647107119000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0465db7f-84a2-4c36-882d-f9748ccc5502", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646875562300, "endTime": 15647110038900}, "additional": {"logType": "info", "children": [], "durationId": "fc17ab3d-f78b-438c-a468-88f1afc7a354", "parent": "57f44c4d-8f16-4417-9ea0-7403a9e6636d"}}, {"head": {"id": "8b7a221f-9e73-4348-8104-4d40a0bf77cf", "name": "entry : default@PreviewCompileResource cost memory -9.220207214355469", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647110966500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e05e52b-c2f8-4816-8ffa-5563fb8e7d31", "name": "runTaskFromQueue task cost before running: 1 s 568 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647111171100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f44c4d-8f16-4417-9ea0-7403a9e6636d", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15646440219600, "endTime": 15647111262400, "totalTime": 670890900}, "additional": {"logType": "info", "children": ["be83f7e3-1d7e-410f-b19c-c26100f4f510", "1968b11b-6a7a-4146-90f6-8463e0623c10", "462cf2a3-cb5a-4699-90a7-3859bd6f36a8", "0465db7f-84a2-4c36-882d-f9748ccc5502"], "durationId": "7a31a264-ed6e-4ce8-8f26-de202a77c2e4"}}, {"head": {"id": "bac39d0d-cf2d-48da-ae83-189eb5ffc5d3", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115070400, "endTime": 15647115442000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "47609fc5-5be2-4974-a2a0-44d0d3ff7f91", "logId": "7565f839-0a64-418d-b33b-601745e2a439"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47609fc5-5be2-4974-a2a0-44d0d3ff7f91", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647114271800}, "additional": {"logType": "detail", "children": [], "durationId": "bac39d0d-cf2d-48da-ae83-189eb5ffc5d3"}}, {"head": {"id": "8d9c0607-7e0c-48c2-a808-c63873a08eba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647114845500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c2acc1f-d87a-4aa4-b446-ef09d39cef43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647114964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e00b12-2086-4080-8c75-fe0d72402953", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115080000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d0a056-c01c-45d2-99a2-1782a3526346", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115173200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18be352c-1ed5-4763-b4e6-73bc4ae14975", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e1418a-4ccf-4452-b52f-92d6a7d9ec90", "name": "entry : default@PreviewHookCompileResource cost memory 0.0384368896484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44736ffe-9617-4ad4-9602-b734349cea2e", "name": "runTaskFromQueue task cost before running: 1 s 572 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7565f839-0a64-418d-b33b-601745e2a439", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647115070400, "endTime": 15647115442000, "totalTime": 297500}, "additional": {"logType": "info", "children": [], "durationId": "bac39d0d-cf2d-48da-ae83-189eb5ffc5d3"}}, {"head": {"id": "7160c757-1153-452b-8973-95697975e1db", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647118677400, "endTime": 15647125147400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "d2d85ae8-65e0-43c5-bd3d-185be7f1cc5d", "logId": "3ee6a9a1-eaf7-45ee-84cd-9c6486295d2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2d85ae8-65e0-43c5-bd3d-185be7f1cc5d", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647117221600}, "additional": {"logType": "detail", "children": [], "durationId": "7160c757-1153-452b-8973-95697975e1db"}}, {"head": {"id": "e4acef7f-22a9-40d4-a60c-81d465ebf472", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647117858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3271d7-fcb2-4275-ae47-4b66f47f4b23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647117984900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7edb44bc-9bd3-4e40-94f2-cb683c597ac1", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647118689500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0945a26-8e25-4684-9db0-e98683f0c153", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647120144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60efe888-c962-48c8-9b72-168e816ab61b", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647120253800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e36ea8-1a92-493b-abfb-1f5f8b21cb05", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647120342800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1049c3-39ca-4f4e-bc70-efdb4e925809", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647120401600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbc61bb-2841-4d11-a07e-60f3322fb875", "name": "entry : default@CopyPreviewProfile cost memory 0.21202850341796875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647124912200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ddca6b-4c8c-483c-bf60-95cbdfbe5fa0", "name": "runTaskFromQueue task cost before running: 1 s 582 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647125072700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee6a9a1-eaf7-45ee-84cd-9c6486295d2c", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647118677400, "endTime": 15647125147400, "totalTime": 6367500}, "additional": {"logType": "info", "children": [], "durationId": "7160c757-1153-452b-8973-95697975e1db"}}, {"head": {"id": "4504c79c-18a6-4589-8e3a-967c18ef5034", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128794300, "endTime": 15647129228600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7d155dfe-08b9-41bd-b321-df50558c374c", "logId": "1352ec0c-01bd-466a-a8bd-f2295a3e440a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d155dfe-08b9-41bd-b321-df50558c374c", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647127329900}, "additional": {"logType": "detail", "children": [], "durationId": "4504c79c-18a6-4589-8e3a-967c18ef5034"}}, {"head": {"id": "9f5c4d6b-1ad7-40db-95ca-5c1e5c03a344", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647127920100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba2a8eb-99ad-44f0-8191-43d7b72d7cc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128029200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f72dbc70-ed53-41e7-825f-c8532b1b33cb", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128803800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9dfac2d-915a-40ee-9af8-5870b654b83e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128923500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f756a6d7-f29c-45d0-b6a9-9818d521c28e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128986700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c446b0-c00d-4c00-b683-e00f0c56640b", "name": "entry : default@ReplacePreviewerPage cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647129084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f97aba-dbd6-4423-b86e-b181fa535e15", "name": "runTaskFromQueue task cost before running: 1 s 586 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647129169500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1352ec0c-01bd-466a-a8bd-f2295a3e440a", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647128794300, "endTime": 15647129228600, "totalTime": 356500}, "additional": {"logType": "info", "children": [], "durationId": "4504c79c-18a6-4589-8e3a-967c18ef5034"}}, {"head": {"id": "a1f8a057-5bb0-44ed-b957-86c1043dff3c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131638000, "endTime": 15647131953800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "dfc58c88-077d-4b35-80ce-89672e6dbc08", "logId": "b97febf8-73c1-4b0d-baf9-7a0c87c413ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfc58c88-077d-4b35-80ce-89672e6dbc08", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131575400}, "additional": {"logType": "detail", "children": [], "durationId": "a1f8a057-5bb0-44ed-b957-86c1043dff3c"}}, {"head": {"id": "40c0e9c0-cd9c-4218-9b21-2e4f198d9ed5", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb423b7-fd13-455c-ba2b-b472ff8bf12d", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131781100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f3b37fe-4e59-4837-8873-d2b65d81b8a4", "name": "runTaskFromQueue task cost before running: 1 s 588 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97febf8-73c1-4b0d-baf9-7a0c87c413ae", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647131638000, "endTime": 15647131953800, "totalTime": 216700}, "additional": {"logType": "info", "children": [], "durationId": "a1f8a057-5bb0-44ed-b957-86c1043dff3c"}}, {"head": {"id": "f67ca938-e05a-41f7-b21a-98424d45fb27", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647135821700, "endTime": 15647138935500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "a6f7b3fd-f651-4977-8e13-e0fc099b0bea", "logId": "9e3b67be-ebc9-4e4d-8eae-b90093e384db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6f7b3fd-f651-4977-8e13-e0fc099b0bea", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647134052900}, "additional": {"logType": "detail", "children": [], "durationId": "f67ca938-e05a-41f7-b21a-98424d45fb27"}}, {"head": {"id": "6e34899c-5df8-4ca1-985a-69a3675ee0f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647134770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa51b3d4-5120-44b6-8b5f-ee9034b9f5e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647134897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c2a8803-60c1-4a45-92d6-82de92d5d0fc", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647135834300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb156281-aed6-47f5-be10-556e219ba2d3", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647137729300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c35bc8-8e4d-4e4d-8556-8bb7ad2fda79", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647137843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ecf59e-eda1-40b1-837e-ac791289471e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647137951500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "256e1141-79d0-4daa-849f-96fd64369b59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647138016700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1516bb9d-4443-4ebc-8765-827276716c4c", "name": "entry : default@PreviewUpdateAssets cost memory 0.13339996337890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647138741500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c27decd-699e-4b11-9cf5-57b4cfbaa485", "name": "runTaskFromQueue task cost before running: 1 s 595 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647138861500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e3b67be-ebc9-4e4d-8eae-b90093e384db", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647135821700, "endTime": 15647138935500, "totalTime": 3011800}, "additional": {"logType": "info", "children": [], "durationId": "f67ca938-e05a-41f7-b21a-98424d45fb27"}}, {"head": {"id": "d87a946a-6662-46d9-b6ac-2c7878fde7cf", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647147431000, "endTime": 15675291763400}, "additional": {"children": ["d00c5600-27f2-4da2-8408-a1fb17027c05"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "6ea2c4b3-a291-46ce-9984-e460f2547c26", "logId": "c07fd995-2517-4e9d-8c58-c73c80d41d9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ea2c4b3-a291-46ce-9984-e460f2547c26", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647141223400}, "additional": {"logType": "detail", "children": [], "durationId": "d87a946a-6662-46d9-b6ac-2c7878fde7cf"}}, {"head": {"id": "6f60b54c-8f3b-49ea-992c-7a0f49e4377b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647141744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "891eb8d4-4d0f-47e9-ba59-ddbf540b348b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647141832300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad22812d-10a7-4481-b31e-b48e3a45ad4b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647147445200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b188a4e-472b-4777-bb16-5e5ecb3f736b", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647160035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b90209-f4a6-42f0-9f30-2920a4ee8754", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647160183900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d00c5600-27f2-4da2-8408-a1fb17027c05", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15647175970800, "endTime": 15675288463900}, "additional": {"children": ["9308c681-2585-4477-95a3-1963267e3eac", "717af5cc-0468-444f-b248-bccb5e08de3d", "940310f1-9e32-407e-b437-8a46d844ffc8", "f2a0e6c0-763a-482a-a59b-93b5a8fe2f82", "25c5fce7-df4e-4b3b-8e8d-2805231ac282", "92f218a7-3c78-407b-9d66-c45f0ef79087"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d87a946a-6662-46d9-b6ac-2c7878fde7cf", "logId": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee01b620-e5ab-4c1f-9d32-4965abaedf1b", "name": "entry : default@PreviewArkTS cost memory -0.43762969970703125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647178236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b02a11-4c6a-45b4-832b-a869d765a429", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15663329476700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9308c681-2585-4477-95a3-1963267e3eac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15663330556100, "endTime": 15663330576900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "a14eb770-87ab-4114-acbb-a4a0d86420f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a14eb770-87ab-4114-acbb-a4a0d86420f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15663330556100, "endTime": 15663330576900}, "additional": {"logType": "info", "children": [], "durationId": "9308c681-2585-4477-95a3-1963267e3eac", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "7551e4b8-6227-4074-b2c6-1850055760d8", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675287022500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717af5cc-0468-444f-b248-bccb5e08de3d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15675288040200, "endTime": 15675288058000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "f2ca6528-0b42-466b-9602-605a376b6f73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2ca6528-0b42-466b-9602-605a376b6f73", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675288040200, "endTime": 15675288058000}, "additional": {"logType": "info", "children": [], "durationId": "717af5cc-0468-444f-b248-bccb5e08de3d", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "2f4f211c-852c-4e2d-aff6-51685d6aaad5", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15647175970800, "endTime": 15675288463900}, "additional": {"logType": "info", "children": ["a14eb770-87ab-4114-acbb-a4a0d86420f7", "f2ca6528-0b42-466b-9602-605a376b6f73", "74ecc04b-79f0-4b02-9cc6-71d01d40d3c3", "508fccb0-7ec6-4594-bb82-fbf3340670c1", "d461ccd6-e541-4297-9e52-ae0fd3d834d8", "077edb13-b76a-4070-901b-4b683690785e"], "durationId": "d00c5600-27f2-4da2-8408-a1fb17027c05", "parent": "c07fd995-2517-4e9d-8c58-c73c80d41d9c"}}, {"head": {"id": "940310f1-9e32-407e-b437-8a46d844ffc8", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15659342761000, "endTime": 15663128704200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "74ecc04b-79f0-4b02-9cc6-71d01d40d3c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74ecc04b-79f0-4b02-9cc6-71d01d40d3c3", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15659342761000, "endTime": 15663128704200}, "additional": {"logType": "info", "children": [], "durationId": "940310f1-9e32-407e-b437-8a46d844ffc8", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "f2a0e6c0-763a-482a-a59b-93b5a8fe2f82", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15663128940900, "endTime": 15663174669300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "508fccb0-7ec6-4594-bb82-fbf3340670c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "508fccb0-7ec6-4594-bb82-fbf3340670c1", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15663128940900, "endTime": 15663174669300}, "additional": {"logType": "info", "children": [], "durationId": "f2a0e6c0-763a-482a-a59b-93b5a8fe2f82", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "25c5fce7-df4e-4b3b-8e8d-2805231ac282", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15663174814200, "endTime": 15663175031000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "d461ccd6-e541-4297-9e52-ae0fd3d834d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d461ccd6-e541-4297-9e52-ae0fd3d834d8", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15663174814200, "endTime": 15663175031000}, "additional": {"logType": "info", "children": [], "durationId": "25c5fce7-df4e-4b3b-8e8d-2805231ac282", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "92f218a7-3c78-407b-9d66-c45f0ef79087", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker11", "startTime": 15663175144700, "endTime": 15675287104100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d00c5600-27f2-4da2-8408-a1fb17027c05", "logId": "077edb13-b76a-4070-901b-4b683690785e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "077edb13-b76a-4070-901b-4b683690785e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15663175144700, "endTime": 15675287104100}, "additional": {"logType": "info", "children": [], "durationId": "92f218a7-3c78-407b-9d66-c45f0ef79087", "parent": "2f4f211c-852c-4e2d-aff6-51685d6aaad5"}}, {"head": {"id": "c07fd995-2517-4e9d-8c58-c73c80d41d9c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15647147431000, "endTime": 15675291763400, "totalTime": 28144313500}, "additional": {"logType": "info", "children": ["2f4f211c-852c-4e2d-aff6-51685d6aaad5"], "durationId": "d87a946a-6662-46d9-b6ac-2c7878fde7cf"}}, {"head": {"id": "720d025b-876f-4010-a65f-8a9879e13c2e", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296602300, "endTime": 15675296893300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8b49b577-da88-467d-bd2f-b88051af9c43", "logId": "955d74a8-2323-4b0d-971f-d4cab3e4e88c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b49b577-da88-467d-bd2f-b88051af9c43", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296541200}, "additional": {"logType": "detail", "children": [], "durationId": "720d025b-876f-4010-a65f-8a9879e13c2e"}}, {"head": {"id": "13bbcabd-3217-48a1-b94e-6bb002a8f6bd", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72c1c78-2da7-4fd7-aab2-156be2f92f49", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296740900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd89b100-7e24-45df-952a-9de7b3d8305d", "name": "runTaskFromQueue task cost before running: 29 s 753 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296831300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955d74a8-2323-4b0d-971f-d4cab3e4e88c", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675296602300, "endTime": 15675296893300, "totalTime": 208100}, "additional": {"logType": "info", "children": [], "durationId": "720d025b-876f-4010-a65f-8a9879e13c2e"}}, {"head": {"id": "0017bede-ec7f-4445-8d10-029922c25e9b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675302620400, "endTime": 15675302642600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ce329d5-136c-4542-b355-09d4af73fdfd", "logId": "44c7c7f8-6a44-46b3-a2f7-bcbe877755b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c7c7f8-6a44-46b3-a2f7-bcbe877755b6", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675302620400, "endTime": 15675302642600}, "additional": {"logType": "info", "children": [], "durationId": "0017bede-ec7f-4445-8d10-029922c25e9b"}}, {"head": {"id": "1c6af6b0-108d-4ef7-a2f9-dfb019922db5", "name": "BUILD SUCCESSFUL in 29 s 759 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675302684100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "c90c885a-9584-4424-b56f-8d89ea529dd0", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15645543935000, "endTime": 15675302960800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 11}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "946329a8-cb0a-44b0-adb2-02d34cba8886", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675302986900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23bc3ee6-ca7d-41b7-8bdd-33170e8cd7ce", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303061800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e3864a-fa66-4d74-a4fd-979d229b70e9", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00237ce2-ae55-4c6e-9950-7a56664f2050", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303175500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70879d3a-111f-42a7-b5cc-4970c15a511d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e34b0ed9-7946-4336-8e0b-3ece6fff9c39", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303285800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e553df43-226e-4e65-9517-559c6c1cf98f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675303342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2850b1-ee41-42c9-a966-d623f249ffa6", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675304089900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9caeb442-def6-4e98-90f0-29db990c6341", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675311962900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d063adb0-7a48-4d13-b0aa-37b90dccb7da", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675312318900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19e5d35-c0d1-4c82-bb44-9377cccfcc6f", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675322905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953d8482-afca-4a10-81cf-63b4e25df722", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:21 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675323582500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dadd619c-f9fb-4a5c-86ea-d16395312a5d", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675323812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8620477-0e7d-4278-97ad-694a80b7eb10", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675324704600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cf94a13-323c-4e8f-80cb-2e288826e52c", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675325583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a638df68-b8a9-4eb3-8b23-64bf71f95099", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675325978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f844db6-0090-44fe-ba34-5b3f0c202f00", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675326317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1cfa5c2-c386-498b-a1df-d6e71c2f0203", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675326676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b94a4a-ba69-4068-9b98-57caf6206195", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675329499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9aab257-a9e9-4912-b259-94567867dbff", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675330357600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16fe52f-5085-4937-b13c-13ebe7726f61", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675330437400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719df8b7-2b7f-4eeb-a393-8f9d1104bb35", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675330712400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de1bceaa-592a-4364-bf90-dd536c2b567c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675331521600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78e2b13d-aedf-4c7f-940e-1c1922abed03", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675343098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f59de7-095c-41d6-928a-90e09c8fc9f2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675343458600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f194144-68c0-4b45-bfc8-46605244bd1d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675343753600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d5db3d-a3ed-4015-97bd-c3c254f4edde", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675344088300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d682e36b-9f48-4450-855a-f03395c5f7cd", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 15675344359100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}