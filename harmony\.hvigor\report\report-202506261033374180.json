{"version": "2.0", "ppid": 10700, "events": [{"head": {"id": "3b8a7fe7-a825-462b-b0b5-6c02779d638e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 311817687048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841145c5-8792-40ed-a6ec-664f79229abd", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 311817786814800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d544494-c1c1-4bb2-a32f-2a044f638b24", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 311817787242100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcfb160-b9a3-46df-aa8e-d0ee8ecee81d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383722101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffa2257-54ff-4e4e-a325-e190be527159", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383740569800, "endTime": 312384080516600}, "additional": {"children": ["5a9918a3-d88b-4ebe-adb0-4fce07e1cb16", "4a2889d2-b4d4-4095-a64d-54903bbe250c", "2ddb627e-ea2b-49b5-8e06-530dd5f3e012", "52f04d30-bde6-4061-ae8b-178ddf05a913", "2d3e2555-a833-44ad-998b-8b79937ff2e0", "1479ca92-dfb3-432e-ac7f-bf39bbde33c5", "8f493e7b-7bd6-43e9-a71b-d049992c856d"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a9918a3-d88b-4ebe-adb0-4fce07e1cb16", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383740572500, "endTime": 312383758634100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "96b8ac1d-55b8-4a96-ba01-8d45f0dc38f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383758650200, "endTime": 312384079235200}, "additional": {"children": ["e112a098-195f-43b8-934e-16bef6308625", "57dad349-4fe9-4e98-b923-9af4d084e5e5", "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "59f49b54-53c9-46a3-a74b-e399ab52215f", "cbcbf20c-2709-4ac2-a61f-db8f95b98e88", "2c0e6606-93c6-4f17-8826-6ca7d9022c20", "5763362b-c142-4128-9d2f-60f8f6b58cf8", "dd4e242b-cbc3-4532-a36f-310162ca26e8", "a41bf5ff-a47c-44fa-ac49-b52fa6a2915b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ddb627e-ea2b-49b5-8e06-530dd5f3e012", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384079259700, "endTime": 312384080500400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "06fa7902-d28a-44ad-9baa-5a09cc2adfd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52f04d30-bde6-4061-ae8b-178ddf05a913", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384080505500, "endTime": 312384080512400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "3044ff4d-66bc-4705-98ba-13df77b46245"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d3e2555-a833-44ad-998b-8b79937ff2e0", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383746233800, "endTime": 312383746273300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "47bb0883-22b7-46fa-a505-3b892a018c68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47bb0883-22b7-46fa-a505-3b892a018c68", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383746233800, "endTime": 312383746273300}, "additional": {"logType": "info", "children": [], "durationId": "2d3e2555-a833-44ad-998b-8b79937ff2e0", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "1479ca92-dfb3-432e-ac7f-bf39bbde33c5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383752991600, "endTime": 312383753009100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "9e262e7a-459e-44bf-9b59-e38e3dd326d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e262e7a-459e-44bf-9b59-e38e3dd326d1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383752991600, "endTime": 312383753009100}, "additional": {"logType": "info", "children": [], "durationId": "1479ca92-dfb3-432e-ac7f-bf39bbde33c5", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "954c5260-dfe3-435c-bb20-1874203a5a8a", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383753069600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5f84ba-b9d9-4490-bf16-fa51804da1b8", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383758524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b8ac1d-55b8-4a96-ba01-8d45f0dc38f6", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383740572500, "endTime": 312383758634100}, "additional": {"logType": "info", "children": [], "durationId": "5a9918a3-d88b-4ebe-adb0-4fce07e1cb16", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "e112a098-195f-43b8-934e-16bef6308625", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383764670100, "endTime": 312383764680800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "60fc5dfa-889d-483e-a69a-dc2ef506923d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57dad349-4fe9-4e98-b923-9af4d084e5e5", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383764694800, "endTime": 312383769588400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "729fc5b8-1370-4af3-8823-e1233f933eba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383769602700, "endTime": 312383934245700}, "additional": {"children": ["599c4dc5-2b0c-4dec-8776-c2349f3497d0", "b552dfcf-3671-4d2c-8525-5a6db349c48d", "32e64b93-77c7-4332-a350-5aa9d31bf928"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "189e4772-40d0-4672-a970-a0a5455e818e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59f49b54-53c9-46a3-a74b-e399ab52215f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934259700, "endTime": 312383972367000}, "additional": {"children": ["f80b3c7e-6929-479e-a4cd-da7199cdec08"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "7a2cd228-f63b-40ed-b6b6-12d8c6091d3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbcbf20c-2709-4ac2-a61f-db8f95b98e88", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383972375700, "endTime": 312384055256500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "34d34f02-d7ed-436b-a870-ec4253fde887"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c0e6606-93c6-4f17-8826-6ca7d9022c20", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384056288200, "endTime": 312384065376600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "65e303df-94f7-434b-b9da-c05859b05283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5763362b-c142-4128-9d2f-60f8f6b58cf8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384065399400, "endTime": 312384078288000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "048a2228-7002-46bf-ae76-83988d3ecb2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd4e242b-cbc3-4532-a36f-310162ca26e8", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384079095400, "endTime": 312384079221900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "b43066cf-7edb-43b2-ad81-730ebded4862"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60fc5dfa-889d-483e-a69a-dc2ef506923d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383764670100, "endTime": 312383764680800}, "additional": {"logType": "info", "children": [], "durationId": "e112a098-195f-43b8-934e-16bef6308625", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "729fc5b8-1370-4af3-8823-e1233f933eba", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383764694800, "endTime": 312383769588400}, "additional": {"logType": "info", "children": [], "durationId": "57dad349-4fe9-4e98-b923-9af4d084e5e5", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "599c4dc5-2b0c-4dec-8776-c2349f3497d0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383770287300, "endTime": 312383770303500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "logId": "10538bbb-215e-480a-91b1-eebcb8b18411"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10538bbb-215e-480a-91b1-eebcb8b18411", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383770287300, "endTime": 312383770303500}, "additional": {"logType": "info", "children": [], "durationId": "599c4dc5-2b0c-4dec-8776-c2349f3497d0", "parent": "189e4772-40d0-4672-a970-a0a5455e818e"}}, {"head": {"id": "b552dfcf-3671-4d2c-8525-5a6db349c48d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383772459300, "endTime": 312383933524500}, "additional": {"children": ["9915651d-ec72-4524-981c-a667a4d0834a", "0a8c20e0-ec28-4cab-b57c-29e118274f92"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "logId": "91e10ace-5628-4823-a5b9-b5361699c32d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9915651d-ec72-4524-981c-a667a4d0834a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383772460500, "endTime": 312383805173700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b552dfcf-3671-4d2c-8525-5a6db349c48d", "logId": "d3f5da05-250a-431f-82dd-358f1b67f150"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a8c20e0-ec28-4cab-b57c-29e118274f92", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383805189700, "endTime": 312383933512000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b552dfcf-3671-4d2c-8525-5a6db349c48d", "logId": "85ae264f-4716-448b-b197-246587cf23cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e80f94a-778b-43aa-b6b1-f3a424f65384", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383772466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e494559e-321e-40e9-915e-95364cfc4889", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383805043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f5da05-250a-431f-82dd-358f1b67f150", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383772460500, "endTime": 312383805173700}, "additional": {"logType": "info", "children": [], "durationId": "9915651d-ec72-4524-981c-a667a4d0834a", "parent": "91e10ace-5628-4823-a5b9-b5361699c32d"}}, {"head": {"id": "530af718-ad6a-4525-86f1-bceaa5a1056b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383805204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7069c5-ceb3-4843-85bc-c4f328b4db84", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383812434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "864a6559-d3d3-4b32-aa68-2e9a2b8c211d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383812556700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1191653-dbb0-46b9-bd2b-3fc512b4164e", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383812698400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f5ffbb-db56-48df-92a6-4ed0d352b1c9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383812806200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bfd103-c199-4a95-aeff-524160af54da", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383823698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b086b94-30c7-4a56-ac6a-e5fbb899cd6a", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383828097200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd723de-9187-4c4c-88d4-da32dd2e675c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383860996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbbb0ff7-d8e5-4d5a-bd96-cdac96fcfd33", "name": "Sdk init in 79 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383908015400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79332c2d-de68-45bc-b666-a8e5a4b6897f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383908146100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 33}, "markType": "other"}}, {"head": {"id": "8868b4ce-2762-4b2a-9e9c-f1814446b0ea", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383908163800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 33}, "markType": "other"}}, {"head": {"id": "7c963f4c-689c-4c9c-858a-ea96c59b14f0", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383933225800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b852556a-1105-492e-b56a-8216da96f503", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383933352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51734f62-b70e-4c01-b04f-bfd0a80461b2", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383933413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e87aaa-3b28-4c6d-b926-8aea453ce936", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383933465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ae264f-4716-448b-b197-246587cf23cf", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383805189700, "endTime": 312383933512000}, "additional": {"logType": "info", "children": [], "durationId": "0a8c20e0-ec28-4cab-b57c-29e118274f92", "parent": "91e10ace-5628-4823-a5b9-b5361699c32d"}}, {"head": {"id": "91e10ace-5628-4823-a5b9-b5361699c32d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383772459300, "endTime": 312383933524500}, "additional": {"logType": "info", "children": ["d3f5da05-250a-431f-82dd-358f1b67f150", "85ae264f-4716-448b-b197-246587cf23cf"], "durationId": "b552dfcf-3671-4d2c-8525-5a6db349c48d", "parent": "189e4772-40d0-4672-a970-a0a5455e818e"}}, {"head": {"id": "32e64b93-77c7-4332-a350-5aa9d31bf928", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934219600, "endTime": 312383934232500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "logId": "ab211af5-815d-42a3-89f0-f6610371f508"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab211af5-815d-42a3-89f0-f6610371f508", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934219600, "endTime": 312383934232500}, "additional": {"logType": "info", "children": [], "durationId": "32e64b93-77c7-4332-a350-5aa9d31bf928", "parent": "189e4772-40d0-4672-a970-a0a5455e818e"}}, {"head": {"id": "189e4772-40d0-4672-a970-a0a5455e818e", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383769602700, "endTime": 312383934245700}, "additional": {"logType": "info", "children": ["10538bbb-215e-480a-91b1-eebcb8b18411", "91e10ace-5628-4823-a5b9-b5361699c32d", "ab211af5-815d-42a3-89f0-f6610371f508"], "durationId": "a9aafb52-ca6e-4d45-87d6-3e514bb65525", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "f80b3c7e-6929-479e-a4cd-da7199cdec08", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934931800, "endTime": 312383972315300}, "additional": {"children": ["014d2342-583d-462c-aa85-b3b05bb89a18", "9473279b-47b5-4c97-b8cd-2ed101ec8bae", "39e44531-93ef-4830-9b93-015fe01e41aa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "59f49b54-53c9-46a3-a74b-e399ab52215f", "logId": "8ed7d982-f298-471e-91bb-0529efce5696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "014d2342-583d-462c-aa85-b3b05bb89a18", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383938071100, "endTime": 312383938083800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f80b3c7e-6929-479e-a4cd-da7199cdec08", "logId": "6841b7d8-6448-49a7-b19d-a9817aba01c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6841b7d8-6448-49a7-b19d-a9817aba01c9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383938071100, "endTime": 312383938083800}, "additional": {"logType": "info", "children": [], "durationId": "014d2342-583d-462c-aa85-b3b05bb89a18", "parent": "8ed7d982-f298-471e-91bb-0529efce5696"}}, {"head": {"id": "9473279b-47b5-4c97-b8cd-2ed101ec8bae", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383940119100, "endTime": 312383970612800}, "additional": {"children": ["55452beb-c682-4de4-88bc-f79b0c23ae29", "3463dc0f-0d7b-4828-9045-b54498505362"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f80b3c7e-6929-479e-a4cd-da7199cdec08", "logId": "ff5be0a3-f0e6-48ee-8681-32583ffee292"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55452beb-c682-4de4-88bc-f79b0c23ae29", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383940120100, "endTime": 312383945663300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9473279b-47b5-4c97-b8cd-2ed101ec8bae", "logId": "bf02a53a-d75b-4374-a772-92e079f2a473"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3463dc0f-0d7b-4828-9045-b54498505362", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383945679300, "endTime": 312383970597100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9473279b-47b5-4c97-b8cd-2ed101ec8bae", "logId": "6f4eed0a-5f55-4191-8720-a70bd092ee9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffe52301-76b9-4b4d-a084-c165098d561f", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383940125800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2dfc7e7-d694-439c-a645-a8ce9815fbef", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383945547100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf02a53a-d75b-4374-a772-92e079f2a473", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383940120100, "endTime": 312383945663300}, "additional": {"logType": "info", "children": [], "durationId": "55452beb-c682-4de4-88bc-f79b0c23ae29", "parent": "ff5be0a3-f0e6-48ee-8681-32583ffee292"}}, {"head": {"id": "558ec34e-4c14-4b87-94ba-a5e4b1fd16ec", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383945692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344995a6-f550-4600-a8e6-f08766155951", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383956489000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f78fbca-f6fe-4a62-b277-e3273547ab33", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383956625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db3303d-2a98-4b2a-9a1e-59d6e9632e75", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383957059400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e7f026b-b1e3-48bc-8d8e-fbb6504037b4", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383957356400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c7e9db-165f-47b1-9eba-937913774459", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383957439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983fb097-9500-4d13-befa-507d11e63af7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383957492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d76082-4ce9-4039-afda-2d910cc32d00", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383957554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe47fe1-f295-4c90-85f9-27f4859799f9", "name": "Module entry task initialization takes 10 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383970207300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254859df-4f03-41ed-8287-208b9fa09f29", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383970430200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f952fd-72d4-4daf-a7a3-e7671b0a0c9e", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383970497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5ef2e95-7582-4bb7-8bea-975e775dc6cd", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383970548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4eed0a-5f55-4191-8720-a70bd092ee9c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383945679300, "endTime": 312383970597100}, "additional": {"logType": "info", "children": [], "durationId": "3463dc0f-0d7b-4828-9045-b54498505362", "parent": "ff5be0a3-f0e6-48ee-8681-32583ffee292"}}, {"head": {"id": "ff5be0a3-f0e6-48ee-8681-32583ffee292", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383940119100, "endTime": 312383970612800}, "additional": {"logType": "info", "children": ["bf02a53a-d75b-4374-a772-92e079f2a473", "6f4eed0a-5f55-4191-8720-a70bd092ee9c"], "durationId": "9473279b-47b5-4c97-b8cd-2ed101ec8bae", "parent": "8ed7d982-f298-471e-91bb-0529efce5696"}}, {"head": {"id": "39e44531-93ef-4830-9b93-015fe01e41aa", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383972287200, "endTime": 312383972300000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f80b3c7e-6929-479e-a4cd-da7199cdec08", "logId": "15a4a7c9-e2eb-4421-bd65-f5e1e67fadfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15a4a7c9-e2eb-4421-bd65-f5e1e67fadfa", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383972287200, "endTime": 312383972300000}, "additional": {"logType": "info", "children": [], "durationId": "39e44531-93ef-4830-9b93-015fe01e41aa", "parent": "8ed7d982-f298-471e-91bb-0529efce5696"}}, {"head": {"id": "8ed7d982-f298-471e-91bb-0529efce5696", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934931800, "endTime": 312383972315300}, "additional": {"logType": "info", "children": ["6841b7d8-6448-49a7-b19d-a9817aba01c9", "ff5be0a3-f0e6-48ee-8681-32583ffee292", "15a4a7c9-e2eb-4421-bd65-f5e1e67fadfa"], "durationId": "f80b3c7e-6929-479e-a4cd-da7199cdec08", "parent": "7a2cd228-f63b-40ed-b6b6-12d8c6091d3b"}}, {"head": {"id": "7a2cd228-f63b-40ed-b6b6-12d8c6091d3b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383934259700, "endTime": 312383972367000}, "additional": {"logType": "info", "children": ["8ed7d982-f298-471e-91bb-0529efce5696"], "durationId": "59f49b54-53c9-46a3-a74b-e399ab52215f", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "90de7d7d-ab10-41cd-bb7b-5a7dce73c2b4", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384024937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968d07c0-a2cc-4e06-88ba-31ea1f3d1800", "name": "hvigorfile, resolve hvigorfile dependencies in 83 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384055121100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d34f02-d7ed-436b-a870-ec4253fde887", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383972375700, "endTime": 312384055256500}, "additional": {"logType": "info", "children": [], "durationId": "cbcbf20c-2709-4ac2-a61f-db8f95b98e88", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "a41bf5ff-a47c-44fa-ac49-b52fa6a2915b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384056083700, "endTime": 312384056276900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "logId": "229e3c4b-dcfa-4886-8782-43581f2af9d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f69c033d-d79b-4112-80f8-df5ca23ba41c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384056108000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "229e3c4b-dcfa-4886-8782-43581f2af9d4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384056083700, "endTime": 312384056276900}, "additional": {"logType": "info", "children": [], "durationId": "a41bf5ff-a47c-44fa-ac49-b52fa6a2915b", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "3dc2b6d7-d899-4e68-9293-4abb734efb78", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384057718600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4acd7ff3-a96d-45d9-a3a1-b112e1842df6", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384064515600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e303df-94f7-434b-b9da-c05859b05283", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384056288200, "endTime": 312384065376600}, "additional": {"logType": "info", "children": [], "durationId": "2c0e6606-93c6-4f17-8826-6ca7d9022c20", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "999c1264-56f0-471f-bc8b-02a07e24b7f8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384065416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715610df-c7a8-46bc-871d-880ebf88e3d6", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384071625400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b82a41f-2ed9-455f-8607-c298b170a0b9", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384071744700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005aae7d-b413-4b7c-bb69-23e648b5ecf0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384072177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6393e310-770e-48d0-be37-748fb4b63946", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384074585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1327fb35-4a57-4511-9986-57e13e7b20cb", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384074665200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048a2228-7002-46bf-ae76-83988d3ecb2c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384065399400, "endTime": 312384078288000}, "additional": {"logType": "info", "children": [], "durationId": "5763362b-c142-4128-9d2f-60f8f6b58cf8", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "46b142b2-3076-4c31-9b98-2cea79b78075", "name": "Configuration phase cost:315 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384079124700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b43066cf-7edb-43b2-ad81-730ebded4862", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384079095400, "endTime": 312384079221900}, "additional": {"logType": "info", "children": [], "durationId": "dd4e242b-cbc3-4532-a36f-310162ca26e8", "parent": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b"}}, {"head": {"id": "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383758650200, "endTime": 312384079235200}, "additional": {"logType": "info", "children": ["60fc5dfa-889d-483e-a69a-dc2ef506923d", "729fc5b8-1370-4af3-8823-e1233f933eba", "189e4772-40d0-4672-a970-a0a5455e818e", "7a2cd228-f63b-40ed-b6b6-12d8c6091d3b", "34d34f02-d7ed-436b-a870-ec4253fde887", "65e303df-94f7-434b-b9da-c05859b05283", "048a2228-7002-46bf-ae76-83988d3ecb2c", "b43066cf-7edb-43b2-ad81-730ebded4862", "229e3c4b-dcfa-4886-8782-43581f2af9d4"], "durationId": "4a2889d2-b4d4-4095-a64d-54903bbe250c", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "8f493e7b-7bd6-43e9-a71b-d049992c856d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384080476400, "endTime": 312384080490200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cffa2257-54ff-4e4e-a325-e190be527159", "logId": "a0628657-0e5b-4469-b52e-edcf3b55a108"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0628657-0e5b-4469-b52e-edcf3b55a108", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384080476400, "endTime": 312384080490200}, "additional": {"logType": "info", "children": [], "durationId": "8f493e7b-7bd6-43e9-a71b-d049992c856d", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "06fa7902-d28a-44ad-9baa-5a09cc2adfd7", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384079259700, "endTime": 312384080500400}, "additional": {"logType": "info", "children": [], "durationId": "2ddb627e-ea2b-49b5-8e06-530dd5f3e012", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "3044ff4d-66bc-4705-98ba-13df77b46245", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384080505500, "endTime": 312384080512400}, "additional": {"logType": "info", "children": [], "durationId": "52f04d30-bde6-4061-ae8b-178ddf05a913", "parent": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19"}}, {"head": {"id": "96a0db1d-d3cc-47f3-95aa-7ad23a739f19", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383740569800, "endTime": 312384080516600}, "additional": {"logType": "info", "children": ["96b8ac1d-55b8-4a96-ba01-8d45f0dc38f6", "fc4dd3dd-5d26-4f50-8c55-28c8b06b634b", "06fa7902-d28a-44ad-9baa-5a09cc2adfd7", "3044ff4d-66bc-4705-98ba-13df77b46245", "47bb0883-22b7-46fa-a505-3b892a018c68", "9e262e7a-459e-44bf-9b59-e38e3dd326d1", "a0628657-0e5b-4469-b52e-edcf3b55a108"], "durationId": "cffa2257-54ff-4e4e-a325-e190be527159"}}, {"head": {"id": "3d433a66-4feb-412f-b2bf-3de2bb5b9043", "name": "Configuration task cost before running: 353 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384080646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52987219-2936-4954-bd2b-d49eb0cdfc6c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384101678500, "endTime": 312384114473300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7b9e6e65-a9f0-48c7-8789-2152663fbcdf", "logId": "0cef7236-79f5-4eb5-9374-4ef29734b930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b9e6e65-a9f0-48c7-8789-2152663fbcdf", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384097343600}, "additional": {"logType": "detail", "children": [], "durationId": "52987219-2936-4954-bd2b-d49eb0cdfc6c"}}, {"head": {"id": "81372fa0-da76-4da4-b3eb-a5738bb0a9f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384097998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e6192b0-be04-4665-a5ab-2f079d415e15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384098125200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e9ccd3-8e23-471a-9deb-ccb606e05b0f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384101698100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5422daa-e537-4622-b653-406dff2df483", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384114173500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff34a2c-6456-4d02-8f88-cd8182f82c95", "name": "entry : default@PreBuild cost memory 0.2709503173828125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384114364400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cef7236-79f5-4eb5-9374-4ef29734b930", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384101678500, "endTime": 312384114473300}, "additional": {"logType": "info", "children": [], "durationId": "52987219-2936-4954-bd2b-d49eb0cdfc6c"}}, {"head": {"id": "6828c2aa-adfc-4e99-9e7a-46c60e19d6f9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384134318500, "endTime": 312384137145400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "09b487f6-5e14-42be-a61f-692e39ee41df", "logId": "90a656a5-5b27-4599-8cfd-ab2270a4d4b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09b487f6-5e14-42be-a61f-692e39ee41df", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384121578800}, "additional": {"logType": "detail", "children": [], "durationId": "6828c2aa-adfc-4e99-9e7a-46c60e19d6f9"}}, {"head": {"id": "1f4c0d07-eedb-4b42-9d5c-c621df3f5cf9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384122641300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1163a681-3f6a-4f62-b725-f82800225b33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384122777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae093a1-6a8a-4a65-8830-7ab02d998ebf", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384134338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f74038a-79ec-4d45-89b6-2f2e900f30b0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384136918600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e28f36-9581-4ee9-b3df-ab0db9dd9c64", "name": "entry : default@MergeProfile cost memory 0.11115264892578125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384137068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a656a5-5b27-4599-8cfd-ab2270a4d4b4", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384134318500, "endTime": 312384137145400}, "additional": {"logType": "info", "children": [], "durationId": "6828c2aa-adfc-4e99-9e7a-46c60e19d6f9"}}, {"head": {"id": "0e296110-6352-4220-a0ea-4706ff149743", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384141180700, "endTime": 312384144237800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7ab59909-9820-42a0-a39e-8fc87babe312", "logId": "887da7d8-bf7c-4842-a051-89f2107f5662"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ab59909-9820-42a0-a39e-8fc87babe312", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384139627200}, "additional": {"logType": "detail", "children": [], "durationId": "0e296110-6352-4220-a0ea-4706ff149743"}}, {"head": {"id": "25b44e52-edb3-4e7d-8a83-594d49ae0c07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384140268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e8ac11-0498-4c9d-98df-ed0ae94f0bc2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384140361600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395462f3-5185-452d-bb41-d2417da9a18e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384141190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f2d170-ff64-4996-a615-4db6770159f9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384142306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b38fecab-c0b4-4073-a225-29441b6c7b5f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384144011700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a17e5d-1c7b-4213-baae-703f7d88ab87", "name": "entry : default@CreateBuildProfile cost memory 0.0977783203125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384144141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "887da7d8-bf7c-4842-a051-89f2107f5662", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384141180700, "endTime": 312384144237800}, "additional": {"logType": "info", "children": [], "durationId": "0e296110-6352-4220-a0ea-4706ff149743"}}, {"head": {"id": "57ae0932-0a75-455a-a40f-4bcb6d1d960f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147554800, "endTime": 312384147956100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b4fbf42d-4319-4c00-bbe0-e5a63b0ca65e", "logId": "4ec964b3-e407-4eb9-9114-f73b1866f645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4fbf42d-4319-4c00-bbe0-e5a63b0ca65e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384146113700}, "additional": {"logType": "detail", "children": [], "durationId": "57ae0932-0a75-455a-a40f-4bcb6d1d960f"}}, {"head": {"id": "c1c67e61-cc43-4880-b9d8-897daa422f6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384146664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eeb57c9-f681-44e2-9088-618238836820", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384146775800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a0b02d-ef4b-4a32-b8c2-aee74aff17aa", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147565000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4338992-307c-4a37-8514-c035e4153364", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86796f30-d834-4b6b-802a-cd8602cd045d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "674b934f-4a04-410e-b2da-fe5b0bef7ca0", "name": "entry : default@PreCheckSyscap cost memory 0.03755950927734375", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d92d74d-d995-4d20-8e30-70dff40c85cc", "name": "runTaskFromQueue task cost before running: 421 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec964b3-e407-4eb9-9114-f73b1866f645", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384147554800, "endTime": 312384147956100, "totalTime": 325900}, "additional": {"logType": "info", "children": [], "durationId": "57ae0932-0a75-455a-a40f-4bcb6d1d960f"}}, {"head": {"id": "95de9c57-a80f-4227-9050-9c8b778a23ba", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384157534000, "endTime": 312384158642100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "50b6f545-4847-405d-8e50-8323ac446757", "logId": "d3b5814b-fc91-48b0-8434-e5baa0a5a2e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50b6f545-4847-405d-8e50-8323ac446757", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384149916100}, "additional": {"logType": "detail", "children": [], "durationId": "95de9c57-a80f-4227-9050-9c8b778a23ba"}}, {"head": {"id": "f6863fba-e01f-4927-91be-944005bfce8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384150496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0477893-e0d5-464e-9f8e-55e702ff969b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384150596300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "184582ae-b1ab-4d68-8096-94bec4239b57", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384157553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24b0105-dcb7-4bf6-bdfc-fb03b0e9ae1e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384157752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283faf60-469d-472f-8007-bb647fd0f847", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384158458400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d391b312-87b9-40d5-9e21-616bd4a30d22", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07054901123046875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384158572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b5814b-fc91-48b0-8434-e5baa0a5a2e3", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384157534000, "endTime": 312384158642100}, "additional": {"logType": "info", "children": [], "durationId": "95de9c57-a80f-4227-9050-9c8b778a23ba"}}, {"head": {"id": "b54dbb43-e464-4428-8ea3-5cdd715db70b", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384162366200, "endTime": 312384163671000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9b5eca1a-6c74-43ad-b31d-ebf36d67f10c", "logId": "eb15369f-ca18-49b6-a37b-a9503df7196f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b5eca1a-6c74-43ad-b31d-ebf36d67f10c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384160486000}, "additional": {"logType": "detail", "children": [], "durationId": "b54dbb43-e464-4428-8ea3-5cdd715db70b"}}, {"head": {"id": "a1543a97-3a2a-4ba4-beaf-010ebf285878", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384161055900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8addd654-03cc-4fd7-958f-763341559d9b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384161154600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013e64eb-4ca9-4a54-818c-aeb70d4d58b0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384162379200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b361888c-f77e-4017-99d9-6262f16e0f31", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384163488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e81151-30ac-4f9e-b050-88bb96e002a1", "name": "entry : default@ProcessProfile cost memory 0.05664825439453125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384163595000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb15369f-ca18-49b6-a37b-a9503df7196f", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384162366200, "endTime": 312384163671000}, "additional": {"logType": "info", "children": [], "durationId": "b54dbb43-e464-4428-8ea3-5cdd715db70b"}}, {"head": {"id": "2e2fb0fe-becb-40ba-aa33-57f53814a11d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384189464300, "endTime": 312384195853600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6eb2f699-fe9b-444f-8b83-87080add2df7", "logId": "5853a946-bd3b-431c-8f7d-6ae9cc56ed59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eb2f699-fe9b-444f-8b83-87080add2df7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384165410800}, "additional": {"logType": "detail", "children": [], "durationId": "2e2fb0fe-becb-40ba-aa33-57f53814a11d"}}, {"head": {"id": "050dc219-8d7d-49f5-bfee-28e2b1e7a2aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384165988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917a57da-f6cf-4237-a707-cdf976c429a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384166100500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90302665-4da8-4a52-ab62-5f671b2a05a1", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384189479300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d38ab5a2-5bc7-44a8-9b77-4c7912c1553a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384195626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d65a1f0-2c0f-4d98-82a5-84af1129038a", "name": "entry : default@ProcessRouterMap cost memory 0.1889190673828125", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384195778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5853a946-bd3b-431c-8f7d-6ae9cc56ed59", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384189464300, "endTime": 312384195853600}, "additional": {"logType": "info", "children": [], "durationId": "2e2fb0fe-becb-40ba-aa33-57f53814a11d"}}, {"head": {"id": "04fe0edc-3259-496a-b140-ea3612824868", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384213439900, "endTime": 312384218484600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9a954610-d190-408c-ae1c-3ee3f97c1e7b", "logId": "2ffe7772-93c0-4705-a9b0-7bc9d3bfa439"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a954610-d190-408c-ae1c-3ee3f97c1e7b", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384206390700}, "additional": {"logType": "detail", "children": [], "durationId": "04fe0edc-3259-496a-b140-ea3612824868"}}, {"head": {"id": "97dc69c4-4dee-4226-87f8-8a7a7a2bf9a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384207462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9324c7d-685f-4c8a-9081-3eb9bab5c4ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384207640600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41ce4b7-295b-49ea-9e91-40cb92f98eb9", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384209445500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb69ea6c-afd9-4e63-99f9-c5ca381745ea", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384215738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d90ec51-dac0-4b31-b8cd-f9941bd274f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384215987000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec664fa-27a1-4db3-b5aa-a0a7c74e6271", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384216102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c85237c-6c99-4f32-9dab-690a36d19daa", "name": "entry : default@PreviewProcessResource cost memory 0.06835174560546875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384216257000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f682cd-82f4-4038-9f5f-74a6f4e0aef1", "name": "runTaskFromQueue task cost before running: 491 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384218358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffe7772-93c0-4705-a9b0-7bc9d3bfa439", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384213439900, "endTime": 312384218484600, "totalTime": 2919800}, "additional": {"logType": "info", "children": [], "durationId": "04fe0edc-3259-496a-b140-ea3612824868"}}, {"head": {"id": "9d2f90a0-a63c-429f-a188-a1d9f57767c6", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384232219700, "endTime": 312384258013100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b68d457a-188c-4462-ab36-d07ff6673657", "logId": "50a75bd1-9d1d-4c93-b68c-03269f781954"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b68d457a-188c-4462-ab36-d07ff6673657", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384224332700}, "additional": {"logType": "detail", "children": [], "durationId": "9d2f90a0-a63c-429f-a188-a1d9f57767c6"}}, {"head": {"id": "633e3583-61b2-4715-9ed8-3c0c3cadeba0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384225131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a86d9f-d34a-4027-9e76-0619b52bf015", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384225378200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283cb296-1837-4755-986c-a8797cb21688", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384232246200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9f506b-56c6-4560-a161-0a13f09c813f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384257800700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21246c6-d43e-4c51-9464-47df94ec1411", "name": "entry : default@GenerateLoaderJson cost memory 0.7082672119140625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384257939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a75bd1-9d1d-4c93-b68c-03269f781954", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384232219700, "endTime": 312384258013100}, "additional": {"logType": "info", "children": [], "durationId": "9d2f90a0-a63c-429f-a188-a1d9f57767c6"}}, {"head": {"id": "ae796fd5-b8da-446b-aaf5-a796252779b2", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384272431000, "endTime": 312384294789900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ab9f4a23-2679-448e-ad3c-ef51fa8366a2", "logId": "ae29d451-bc41-4ee0-87b6-3f5de91d7efe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab9f4a23-2679-448e-ad3c-ef51fa8366a2", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384268351400}, "additional": {"logType": "detail", "children": [], "durationId": "ae796fd5-b8da-446b-aaf5-a796252779b2"}}, {"head": {"id": "28372be0-09e6-472f-b0b0-3a12c1da37de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384268888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9243ab-ee0f-4594-995b-6883e2cfb32e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384268986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ab0775-bb8c-40de-a8e5-50743bf98dec", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384270052400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1393f0f5-ca43-465b-ae47-558ba904e39f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384272459300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1545d2a-e29b-4256-83cb-9af2e880f092", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384294548100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0845ed4e-808e-4654-847f-82cb13e73ef5", "name": "entry : default@PreviewCompileResource cost memory 0.6750717163085938", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384294691100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae29d451-bc41-4ee0-87b6-3f5de91d7efe", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384272431000, "endTime": 312384294789900}, "additional": {"logType": "info", "children": [], "durationId": "ae796fd5-b8da-446b-aaf5-a796252779b2"}}, {"head": {"id": "aa0e58b2-9072-490d-9d5e-69c8c3ce89e3", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298328300, "endTime": 312384298938800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1af0163e-f274-4acd-af9f-2d7ca905618f", "logId": "70ef6973-d848-41d4-b844-9bb47a787a66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1af0163e-f274-4acd-af9f-2d7ca905618f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384297172100}, "additional": {"logType": "detail", "children": [], "durationId": "aa0e58b2-9072-490d-9d5e-69c8c3ce89e3"}}, {"head": {"id": "cb52b876-4a77-4282-bba3-4000f05f503b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384297920700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919ca000-96cd-4024-98f9-30cf923107cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298135100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be05851d-4bb9-4af1-b9b8-a37e2e3669dd", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07abd7bc-558b-4515-8777-5fff04121c82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e8b1f24-8208-405c-a009-eb1a27b3002a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa6a10a-0aa9-4987-af15-dd0efc539a75", "name": "entry : default@PreviewHookCompileResource cost memory 0.03863525390625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298717300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c3c66a-c0d7-4abf-ade8-3c43d7b10498", "name": "runTaskFromQueue task cost before running: 572 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298856100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ef6973-d848-41d4-b844-9bb47a787a66", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384298328300, "endTime": 312384298938800, "totalTime": 489000}, "additional": {"logType": "info", "children": [], "durationId": "aa0e58b2-9072-490d-9d5e-69c8c3ce89e3"}}, {"head": {"id": "d8145cbf-9c2b-403b-bdd9-bd07163315c2", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384304272500, "endTime": 312384307109500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0ba164fb-5682-4a5c-a6f9-2ba7d1d254cf", "logId": "eaf50968-cf3e-4188-99d1-630c0f78f1fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ba164fb-5682-4a5c-a6f9-2ba7d1d254cf", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384302773500}, "additional": {"logType": "detail", "children": [], "durationId": "d8145cbf-9c2b-403b-bdd9-bd07163315c2"}}, {"head": {"id": "119d3dad-4c62-4f18-b72c-a58351c25d2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384303381500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fecb433-2f8b-446b-b02e-f52c28c7a439", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384303526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d56697f-fb26-4b9e-8eb2-29773b3c5679", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384304283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55abb97d-06ed-4c7e-9670-76b28320b047", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384306933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ce9e19-456f-47e9-83d6-4a9575966683", "name": "entry : default@CopyPreviewProfile cost memory 0.096405029296875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384307041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf50968-cf3e-4188-99d1-630c0f78f1fc", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384304272500, "endTime": 312384307109500}, "additional": {"logType": "info", "children": [], "durationId": "d8145cbf-9c2b-403b-bdd9-bd07163315c2"}}, {"head": {"id": "640fc0b9-945a-4c18-b278-bc41534a1f7b", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384318919000, "endTime": 312384319325000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "4bff6b88-bd14-476a-8166-0d3bf42ab2e7", "logId": "b9a9e81b-e21d-4348-8273-d2b9d28fb663"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bff6b88-bd14-476a-8166-0d3bf42ab2e7", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384308985000}, "additional": {"logType": "detail", "children": [], "durationId": "640fc0b9-945a-4c18-b278-bc41534a1f7b"}}, {"head": {"id": "dd3d5f1f-c0c6-4404-add4-2778663c5204", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384317913300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1521d240-7027-4953-ae34-396cd640990a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384318043500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45ae934-721d-4265-9834-7d600bcf99b2", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384318928300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b26964-d2f8-4f01-824f-62f60f5df35b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384319046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e4d810c-3b70-4a90-8cc6-ff1197585c9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384319104000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34fe59ff-f794-42ec-bd8f-e89e3408b1c3", "name": "entry : default@ReplacePreviewerPage cost memory 0.038543701171875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384319189200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad391dee-9d3e-4508-b50f-dab59f81c360", "name": "runTaskFromQueue task cost before running: 592 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384319273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a9e81b-e21d-4348-8273-d2b9d28fb663", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384318919000, "endTime": 312384319325000, "totalTime": 334000}, "additional": {"logType": "info", "children": [], "durationId": "640fc0b9-945a-4c18-b278-bc41534a1f7b"}}, {"head": {"id": "42455fb2-c433-4a49-a258-cbb5d469e45c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384321963300, "endTime": 312384329833700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "40260e1c-365d-4242-bc6a-df7765cac130", "logId": "89ea1c4e-d8c7-4364-a55c-42d259a7e91f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40260e1c-365d-4242-bc6a-df7765cac130", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384321894000}, "additional": {"logType": "detail", "children": [], "durationId": "42455fb2-c433-4a49-a258-cbb5d469e45c"}}, {"head": {"id": "0f690430-bfe3-49a6-adc9-865216a09564", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384321974900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac1bb86-e706-45d7-a772-ba45c4a51407", "name": "entry : buildPreviewerResource cost memory 0.03008270263671875", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384329428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f85614-ebc8-4da1-af7c-0985e543683c", "name": "runTaskFromQueue task cost before running: 602 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384329689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ea1c4e-d8c7-4364-a55c-42d259a7e91f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384321963300, "endTime": 312384329833700, "totalTime": 7653400}, "additional": {"logType": "info", "children": [], "durationId": "42455fb2-c433-4a49-a258-cbb5d469e45c"}}, {"head": {"id": "637ed244-2e35-4589-afb3-74b498318a33", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384336669100, "endTime": 312384340952100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "39f8b502-9b97-4c74-8d75-d9d2722addea", "logId": "68ca3817-6e52-49e6-9df9-613910b89048"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39f8b502-9b97-4c74-8d75-d9d2722addea", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384334012800}, "additional": {"logType": "detail", "children": [], "durationId": "637ed244-2e35-4589-afb3-74b498318a33"}}, {"head": {"id": "a2fe294d-3494-4f3e-bccb-366f25d5bf04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384334860300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cda91135-f2c4-4a7c-a36c-996b74b610fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384335033800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe5c2da-1536-40bf-a457-7b822eba6655", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384336693700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999932d6-c894-4cee-87d3-ccc72cf8778a", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384340647700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94abb73f-f014-4ee0-a8c4-9f5287d24030", "name": "entry : default@PreviewUpdateAssets cost memory 0.10642242431640625", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384340844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ca3817-6e52-49e6-9df9-613910b89048", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384336669100, "endTime": 312384340952100}, "additional": {"logType": "info", "children": [], "durationId": "637ed244-2e35-4589-afb3-74b498318a33"}}, {"head": {"id": "044a8e0a-601f-4220-a767-f83cc3af8f1a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384352780800, "endTime": 312401195810800}, "additional": {"children": ["c296f29b-d714-4895-be55-7c73b7f89ac0"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0f1960b8-12c0-4e51-83d0-30e78b7286f3", "logId": "bec08c88-4084-4b2a-84cc-ea80e95c909f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f1960b8-12c0-4e51-83d0-30e78b7286f3", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384345592900}, "additional": {"logType": "detail", "children": [], "durationId": "044a8e0a-601f-4220-a767-f83cc3af8f1a"}}, {"head": {"id": "9c2531dc-c86e-4751-937e-cc2d1c7e4072", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384346213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8eb6d6f-5016-4398-b7bc-5fadbcdd32e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384346322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8098170-8545-453d-9225-a7cecca58ee4", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384352800100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c296f29b-d714-4895-be55-7c73b7f89ac0", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13488, "tid": "Worker4", "startTime": 312384379957600, "endTime": 312401195540900}, "additional": {"children": ["bf4af549-7f21-4290-99e0-1c37ea7c7ca0", "fa50353b-294a-4e57-8eb5-47f285e2e132", "f474a78f-7326-4d72-8dd2-1d6b683a8f2c", "b5fbee04-10a9-4a15-b925-2c7e33859b70", "201d4a1a-4224-426a-9420-d191bc431ae4", "49fd88b7-3e75-4f0a-b612-8d4c0b5e5eda", "bcf3ed8c-a214-4ecf-ad25-8b20a58ad9b2", "f1a14656-9704-4db5-a856-8cea73e5eda3", "bfdb793e-da93-40c4-bcf9-89c861b6aa84", "883baaec-7b71-4fdf-a5f9-a65bd7154502", "3e3fccd8-3c43-4c1d-bdfd-6276f8676c23", "fc754fae-0e3a-409f-8cdc-25c7d287ece4", "b03f4a44-a70f-4c1c-bf59-6aacb30f4b0b", "02d39c77-02a9-40d9-8d13-fe16078b6711", "703465d6-4838-49cc-9304-66163b7f77b0", "280ecad7-4b5d-4e72-a313-f1b8576f5f4a", "5ecccc10-a319-46e4-a5d5-602f3558a04b", "4fe0ecf8-2b46-4a7d-a3f0-d2259df91ce2", "9e98df6f-d479-49b6-bd14-20fedbdff000", "9d5782f1-66f6-494d-994e-855405ebffd7", "843e4ee3-f4f8-4da9-9eeb-67318a02603f", "7e01f016-d3eb-4264-a838-524b5ad71cc0", "7f3d35e1-a107-4e25-8a78-426bb74a569b", "0ec476c3-a2c5-4cd9-9aa5-1f74032a7506", "3a84847a-9421-40af-91b4-53bc42feff4d", "508a3af2-6365-4736-b7db-3008a598073c", "c847de2e-cf29-4996-bdc2-15f608e022e3", "f169223f-4e6f-4db6-acde-1f932c682773", "ba774b0a-89d0-4c17-a92e-c953f0cf634b", "a494afe1-a139-4b2b-954d-be2056640151", "a769a9d1-6e55-44c3-a71e-cc3f055009f9", "982956d4-fdcd-4b9f-9bd8-8d0db7ec1e60", "cac9a483-568d-4a1c-8409-dea24fa4cf55", "7807a14c-4df2-4ab0-85b1-0c40497504d1", "7e323a92-c7e0-4e62-8b28-7a91a80511cd", "99579f5f-2995-4cf7-89b2-0024690e8bea", "dd0a387f-d40d-4874-bbb9-ce8a17cfc486", "3a0bc6d1-3a59-4e13-aab2-b5a151934c6e", "4c5a4c1e-0bfb-48d7-8758-3b3e3478cb15", "7949aa14-07ae-4a2c-be8c-0f83bc2b0be3", "9dab4c1a-2787-4c1b-9c86-16c3aa56c6fa", "fa036315-92ed-4ea2-a9c0-9e36ce44d7a0"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "044a8e0a-601f-4220-a767-f83cc3af8f1a", "logId": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f67931b-2428-4b20-ae60-ce8936285992", "name": "entry : default@PreviewArkTS cost memory -0.8453903198242188", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384384249300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd7a658-b216-48e2-a07a-f439f7051f8c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312388766688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf4af549-7f21-4290-99e0-1c37ea7c7ca0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312388767746200, "endTime": 312388767763900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "bd90f8bb-a202-48f9-9c12-d377ace86d73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd90f8bb-a202-48f9-9c12-d377ace86d73", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312388767746200, "endTime": 312388767763900}, "additional": {"logType": "info", "children": [], "durationId": "bf4af549-7f21-4290-99e0-1c37ea7c7ca0", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "9f8b0b39-24e7-4cfe-9efc-8e0c4362cc04", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396971044100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa50353b-294a-4e57-8eb5-47f285e2e132", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396972694800, "endTime": 312396972722300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "7ca24e57-3824-4ead-9263-85bf924c6bc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ca24e57-3824-4ead-9263-85bf924c6bc2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396972694800, "endTime": 312396972722300}, "additional": {"logType": "info", "children": [], "durationId": "fa50353b-294a-4e57-8eb5-47f285e2e132", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "df6d0a2f-e82b-4ffb-9026-03ea28105423", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396972828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f474a78f-7326-4d72-8dd2-1d6b683a8f2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396973957700, "endTime": 312396973978000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "4a4e8cd5-0f25-44f7-904a-27d3dac6aff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a4e8cd5-0f25-44f7-904a-27d3dac6aff2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396973957700, "endTime": 312396973978000}, "additional": {"logType": "info", "children": [], "durationId": "f474a78f-7326-4d72-8dd2-1d6b683a8f2c", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "4614977f-703b-4828-99ab-7b8ca978b984", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396974063800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fbee04-10a9-4a15-b925-2c7e33859b70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396975454300, "endTime": 312396975484000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "afb232ea-f35f-460e-aa80-a3a43e245732"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afb232ea-f35f-460e-aa80-a3a43e245732", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396975454300, "endTime": 312396975484000}, "additional": {"logType": "info", "children": [], "durationId": "b5fbee04-10a9-4a15-b925-2c7e33859b70", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "bdc4078a-adb7-40e9-874f-55b28628aff4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396975621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "201d4a1a-4224-426a-9420-d191bc431ae4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396977209300, "endTime": 312396977252800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "bbba28ea-30e6-48ce-a4bd-ab86806d7999"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbba28ea-30e6-48ce-a4bd-ab86806d7999", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396977209300, "endTime": 312396977252800}, "additional": {"logType": "info", "children": [], "durationId": "201d4a1a-4224-426a-9420-d191bc431ae4", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "ca1f68ec-62f3-4c98-89ff-5a4ed0949f66", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396977367500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49fd88b7-3e75-4f0a-b612-8d4c0b5e5eda", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396979037100, "endTime": 312396979083000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "685fb860-e090-4d7d-85ae-f0d23fdb64c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "685fb860-e090-4d7d-85ae-f0d23fdb64c1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396979037100, "endTime": 312396979083000}, "additional": {"logType": "info", "children": [], "durationId": "49fd88b7-3e75-4f0a-b612-8d4c0b5e5eda", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "e79adcb5-8e7f-40a0-8fd7-b2876af638d9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396979238100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcf3ed8c-a214-4ecf-ad25-8b20a58ad9b2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396981237000, "endTime": 312396981268800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "9dccb37d-0a19-4dba-b50c-f849a8c1bc88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dccb37d-0a19-4dba-b50c-f849a8c1bc88", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396981237000, "endTime": 312396981268800}, "additional": {"logType": "info", "children": [], "durationId": "bcf3ed8c-a214-4ecf-ad25-8b20a58ad9b2", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "d2b9eb53-f252-487a-93f4-e9b1297c14cc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396981416600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a14656-9704-4db5-a856-8cea73e5eda3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396983199300, "endTime": 312396983230800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "c15300a5-868c-40af-9939-fd1fc021db5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c15300a5-868c-40af-9939-fd1fc021db5f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396983199300, "endTime": 312396983230800}, "additional": {"logType": "info", "children": [], "durationId": "f1a14656-9704-4db5-a856-8cea73e5eda3", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "b2bc6df2-965b-43ce-a223-c83d4731fe30", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396983378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdb793e-da93-40c4-bcf9-89c861b6aa84", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396986480200, "endTime": 312396986516000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "dc8445e3-791e-4cfc-a3b0-ca25558dbcbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc8445e3-791e-4cfc-a3b0-ca25558dbcbd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396986480200, "endTime": 312396986516000}, "additional": {"logType": "info", "children": [], "durationId": "bfdb793e-da93-40c4-bcf9-89c861b6aa84", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "78a03d7f-2af9-47e5-99d2-998e8f90e658", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396986737800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883baaec-7b71-4fdf-a5f9-a65bd7154502", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396990212300, "endTime": 312396990247200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "1c34a3ee-e8d0-4bc2-bd89-ed0889c4c243"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c34a3ee-e8d0-4bc2-bd89-ed0889c4c243", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396990212300, "endTime": 312396990247200}, "additional": {"logType": "info", "children": [], "durationId": "883baaec-7b71-4fdf-a5f9-a65bd7154502", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "62ff2d05-a843-40d2-ad06-727e085e470d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396990401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3fccd8-3c43-4c1d-bdfd-6276f8676c23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396995949400, "endTime": 312396995983600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "8179dfed-24d3-472b-b53d-a88739833e3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8179dfed-24d3-472b-b53d-a88739833e3d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396995949400, "endTime": 312396995983600}, "additional": {"logType": "info", "children": [], "durationId": "3e3fccd8-3c43-4c1d-bdfd-6276f8676c23", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "e8501496-7d9a-4da0-a1bc-79a386cb548f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396996134200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc754fae-0e3a-409f-8cdc-25c7d287ece4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396997710800, "endTime": 312396997743600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "75996e5f-b28c-423b-8042-53631986a1e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75996e5f-b28c-423b-8042-53631986a1e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396997710800, "endTime": 312396997743600}, "additional": {"logType": "info", "children": [], "durationId": "fc754fae-0e3a-409f-8cdc-25c7d287ece4", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "ef457556-7330-46c8-a9b5-fef24732cc1e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396997881700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03f4a44-a70f-4c1c-bf59-6aacb30f4b0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396999546500, "endTime": 312396999585600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "a4dfcc25-4eda-49ed-a424-8416e6b27a3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4dfcc25-4eda-49ed-a424-8416e6b27a3f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396999546500, "endTime": 312396999585600}, "additional": {"logType": "info", "children": [], "durationId": "b03f4a44-a70f-4c1c-bf59-6aacb30f4b0b", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "66e8ad1e-e61a-4bb3-b330-c3da81d33dba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312396999842400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d39c77-02a9-40d9-8d13-fe16078b6711", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397002082200, "endTime": 312397002116900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "381abeaf-5263-447e-a20d-8230e1f8888a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "381abeaf-5263-447e-a20d-8230e1f8888a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397002082200, "endTime": 312397002116900}, "additional": {"logType": "info", "children": [], "durationId": "02d39c77-02a9-40d9-8d13-fe16078b6711", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "8961fdf8-5a54-4b37-944d-d7e7dc634531", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397002288700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703465d6-4838-49cc-9304-66163b7f77b0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397004131700, "endTime": 312397004172500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "090f3dbb-e060-4b43-aa09-371eb74bc963"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "090f3dbb-e060-4b43-aa09-371eb74bc963", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397004131700, "endTime": 312397004172500}, "additional": {"logType": "info", "children": [], "durationId": "703465d6-4838-49cc-9304-66163b7f77b0", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "b4fe9483-f354-470d-b5ff-ad4aef9d7c16", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397004342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280ecad7-4b5d-4e72-a313-f1b8576f5f4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397006009900, "endTime": 312397006044500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "c5e01546-d7d2-4616-bae1-0c4d1181cbf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5e01546-d7d2-4616-bae1-0c4d1181cbf7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397006009900, "endTime": 312397006044500}, "additional": {"logType": "info", "children": [], "durationId": "280ecad7-4b5d-4e72-a313-f1b8576f5f4a", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "076b889a-2a1b-4878-bb8e-83d54213557a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397006237300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ecccc10-a319-46e4-a5d5-602f3558a04b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397008224700, "endTime": 312397008274600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "2ac2f06c-57e1-4e60-a4ed-ed85cfa75f01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ac2f06c-57e1-4e60-a4ed-ed85cfa75f01", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397008224700, "endTime": 312397008274600}, "additional": {"logType": "info", "children": [], "durationId": "5ecccc10-a319-46e4-a5d5-602f3558a04b", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "d594f7f6-04bc-43b8-bce8-54522ae9af3d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397008513000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe0ecf8-2b46-4a7d-a3f0-d2259df91ce2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397010321900, "endTime": 312397010350800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "9f4120fc-77b6-46f2-878c-a7b8db799494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f4120fc-77b6-46f2-878c-a7b8db799494", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397010321900, "endTime": 312397010350800}, "additional": {"logType": "info", "children": [], "durationId": "4fe0ecf8-2b46-4a7d-a3f0-d2259df91ce2", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "90745b5d-2b87-4634-9fef-56731232dd0d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397010495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e98df6f-d479-49b6-bd14-20fedbdff000", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397012167700, "endTime": 312397012195800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "b18c5bb7-de29-427d-b8dd-1feac9e9ebfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b18c5bb7-de29-427d-b8dd-1feac9e9ebfd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397012167700, "endTime": 312397012195800}, "additional": {"logType": "info", "children": [], "durationId": "9e98df6f-d479-49b6-bd14-20fedbdff000", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "3d73e381-a825-40a8-8286-2036aeecf4c9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397012312000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5782f1-66f6-494d-994e-855405ebffd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397028548600, "endTime": 312397028571000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "6bf553ac-63b8-4a99-b5a3-25bc8ddc5a82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bf553ac-63b8-4a99-b5a3-25bc8ddc5a82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397028548600, "endTime": 312397028571000}, "additional": {"logType": "info", "children": [], "durationId": "9d5782f1-66f6-494d-994e-855405ebffd7", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "4ac5a58f-8a17-4a69-b801-f64c884516d0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397028683300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843e4ee3-f4f8-4da9-9eeb-67318a02603f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397031374100, "endTime": 312397031401100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "aea84235-80cc-4f4d-b6fa-b7f02d8d9420"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aea84235-80cc-4f4d-b6fa-b7f02d8d9420", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397031374100, "endTime": 312397031401100}, "additional": {"logType": "info", "children": [], "durationId": "843e4ee3-f4f8-4da9-9eeb-67318a02603f", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "b03f9c1b-966b-454d-8d44-f6c2e9993f5c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397031511600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e01f016-d3eb-4264-a838-524b5ad71cc0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397032866300, "endTime": 312397032901000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "98469c5b-a267-4c76-9ded-d8fb6f7254d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98469c5b-a267-4c76-9ded-d8fb6f7254d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397032866300, "endTime": 312397032901000}, "additional": {"logType": "info", "children": [], "durationId": "7e01f016-d3eb-4264-a838-524b5ad71cc0", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "1bf24727-dfa6-47f2-8acf-b791a5625359", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397033031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f3d35e1-a107-4e25-8a78-426bb74a569b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397034771900, "endTime": 312397034817300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "e625677e-0130-4a78-9677-7c20b4fdc8b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e625677e-0130-4a78-9677-7c20b4fdc8b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397034771900, "endTime": 312397034817300}, "additional": {"logType": "info", "children": [], "durationId": "7f3d35e1-a107-4e25-8a78-426bb74a569b", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "425ef6d8-75a0-45d9-859d-60e459b24977", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397034974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ec476c3-a2c5-4cd9-9aa5-1f74032a7506", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397036584700, "endTime": 312397036627800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "bbe19a75-1b64-4973-be00-88c75bbf267f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbe19a75-1b64-4973-be00-88c75bbf267f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397036584700, "endTime": 312397036627800}, "additional": {"logType": "info", "children": [], "durationId": "0ec476c3-a2c5-4cd9-9aa5-1f74032a7506", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "a072a7aa-4d3d-4318-a9bc-5e8690480881", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397036817300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a84847a-9421-40af-91b4-53bc42feff4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397038712700, "endTime": 312397038752300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "480335b1-5f5a-48fd-b9f6-4499d5a1d5ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "480335b1-5f5a-48fd-b9f6-4499d5a1d5ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397038712700, "endTime": 312397038752300}, "additional": {"logType": "info", "children": [], "durationId": "3a84847a-9421-40af-91b4-53bc42feff4d", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "84eeb26b-81e6-45f9-bbf2-e1441f1e723a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397038906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508a3af2-6365-4736-b7db-3008a598073c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397046341800, "endTime": 312397046377300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "d7e75d7c-c132-41a3-a894-1132859c718c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7e75d7c-c132-41a3-a894-1132859c718c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397046341800, "endTime": 312397046377300}, "additional": {"logType": "info", "children": [], "durationId": "508a3af2-6365-4736-b7db-3008a598073c", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "8896ee21-aa83-4e16-b3ac-e9a9bb2cbfc8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397046536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c847de2e-cf29-4996-bdc2-15f608e022e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397048724900, "endTime": 312397048758400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "7dc49ab1-eeb9-4f6a-973d-2332ca33ebb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dc49ab1-eeb9-4f6a-973d-2332ca33ebb1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397048724900, "endTime": 312397048758400}, "additional": {"logType": "info", "children": [], "durationId": "c847de2e-cf29-4996-bdc2-15f608e022e3", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "702cd5ec-4787-4f22-99cc-3a6562ee3db6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397048903900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f169223f-4e6f-4db6-acde-1f932c682773", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397050925100, "endTime": 312397050961700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "1381c302-a8f9-4b43-9ac1-a6c182415898"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1381c302-a8f9-4b43-9ac1-a6c182415898", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397050925100, "endTime": 312397050961700}, "additional": {"logType": "info", "children": [], "durationId": "f169223f-4e6f-4db6-acde-1f932c682773", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "0407b7da-eb86-4c86-b013-b11a48317443", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397051138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba774b0a-89d0-4c17-a92e-c953f0cf634b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397052743700, "endTime": 312397052785700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "c111fd25-3369-418c-9b28-7a5bab60df48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c111fd25-3369-418c-9b28-7a5bab60df48", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397052743700, "endTime": 312397052785700}, "additional": {"logType": "info", "children": [], "durationId": "ba774b0a-89d0-4c17-a92e-c953f0cf634b", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "4cfb8fcd-1a46-48ba-9ed8-1b9aaf9da2a4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397052957600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a494afe1-a139-4b2b-954d-be2056640151", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397054804700, "endTime": 312397054847100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "e1738cef-dbb2-44e0-bc58-40b989905257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1738cef-dbb2-44e0-bc58-40b989905257", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397054804700, "endTime": 312397054847100}, "additional": {"logType": "info", "children": [], "durationId": "a494afe1-a139-4b2b-954d-be2056640151", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "5a44b814-94ff-409a-9ca0-395f33043fa7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397055213700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a769a9d1-6e55-44c3-a71e-cc3f055009f9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397067589300, "endTime": 312397067617800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "27981084-dd14-4abc-a6f7-743a98a7aaf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27981084-dd14-4abc-a6f7-743a98a7aaf5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397067589300, "endTime": 312397067617800}, "additional": {"logType": "info", "children": [], "durationId": "a769a9d1-6e55-44c3-a71e-cc3f055009f9", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "0d5f3770-cfd8-43a5-930b-5776185f5b41", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397067788000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982956d4-fdcd-4b9f-9bd8-8d0db7ec1e60", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397069144000, "endTime": 312397069174300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "0c23e1dc-9bdb-4539-b05b-963ed2b26ace"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c23e1dc-9bdb-4539-b05b-963ed2b26ace", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397069144000, "endTime": 312397069174300}, "additional": {"logType": "info", "children": [], "durationId": "982956d4-fdcd-4b9f-9bd8-8d0db7ec1e60", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "5b20296f-0ab8-45c7-9270-e72b9f6e7a73", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397069323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac9a483-568d-4a1c-8409-dea24fa4cf55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397071042300, "endTime": 312397071073500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "290f9040-c3f6-4726-a4b2-804120118518"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "290f9040-c3f6-4726-a4b2-804120118518", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397071042300, "endTime": 312397071073500}, "additional": {"logType": "info", "children": [], "durationId": "cac9a483-568d-4a1c-8409-dea24fa4cf55", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "d3978b7a-95a4-4ace-8bf1-36b2b5635f13", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397071222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7807a14c-4df2-4ab0-85b1-0c40497504d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397073129400, "endTime": 312397073204400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "c95ccca0-cc20-4403-ac50-577490aec6d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c95ccca0-cc20-4403-ac50-577490aec6d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397073129400, "endTime": 312397073204400}, "additional": {"logType": "info", "children": [], "durationId": "7807a14c-4df2-4ab0-85b1-0c40497504d1", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "a3b5a9ce-e94b-4c71-8055-3ecc8704cc0d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397073461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e323a92-c7e0-4e62-8b28-7a91a80511cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397075583000, "endTime": 312397075680600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "e2a5947f-0077-4a44-a60f-323df9e3993f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2a5947f-0077-4a44-a60f-323df9e3993f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397075583000, "endTime": 312397075680600}, "additional": {"logType": "info", "children": [], "durationId": "7e323a92-c7e0-4e62-8b28-7a91a80511cd", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "4c1a4485-9254-4ce2-a3e1-4b472bdfd65e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397075850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99579f5f-2995-4cf7-89b2-0024690e8bea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397077668600, "endTime": 312397077705300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "50a5f51a-1cfb-4244-9ab5-68aab825ba40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50a5f51a-1cfb-4244-9ab5-68aab825ba40", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397077668600, "endTime": 312397077705300}, "additional": {"logType": "info", "children": [], "durationId": "99579f5f-2995-4cf7-89b2-0024690e8bea", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "540a6a36-3531-4e2b-9b8f-fad6fd9925ad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397077867900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd0a387f-d40d-4874-bbb9-ce8a17cfc486", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397079684600, "endTime": 312397079720100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "27ba9f5a-a197-4ba3-abaf-f2a1c59e1951"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27ba9f5a-a197-4ba3-abaf-f2a1c59e1951", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397079684600, "endTime": 312397079720100}, "additional": {"logType": "info", "children": [], "durationId": "dd0a387f-d40d-4874-bbb9-ce8a17cfc486", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "f9235182-570b-4d18-a385-bcf66180a8c0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397079897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0bc6d1-3a59-4e13-aab2-b5a151934c6e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397081666100, "endTime": 312397081700400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "aa717a09-bb32-4803-a11b-b546fd5658ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa717a09-bb32-4803-a11b-b546fd5658ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397081666100, "endTime": 312397081700400}, "additional": {"logType": "info", "children": [], "durationId": "3a0bc6d1-3a59-4e13-aab2-b5a151934c6e", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "ba469389-9ca8-4668-8af1-50885c6938f4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397081851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5a4c1e-0bfb-48d7-8758-3b3e3478cb15", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397083540100, "endTime": 312397083672800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "a48c1883-d8eb-4f08-a12c-a13942d6356c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a48c1883-d8eb-4f08-a12c-a13942d6356c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397083540100, "endTime": 312397083672800}, "additional": {"logType": "info", "children": [], "durationId": "4c5a4c1e-0bfb-48d7-8758-3b3e3478cb15", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "22ec7282-45e8-40b3-859a-42ee71e67264", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397083876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7949aa14-07ae-4a2c-be8c-0f83bc2b0be3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397085909000, "endTime": 312397085953500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "911063b3-db72-4ddf-9086-60811d4d261e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "911063b3-db72-4ddf-9086-60811d4d261e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397085909000, "endTime": 312397085953500}, "additional": {"logType": "info", "children": [], "durationId": "7949aa14-07ae-4a2c-be8c-0f83bc2b0be3", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "f8314509-cac0-4050-855b-5cc04e9827b0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397086124800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dab4c1a-2787-4c1b-9c86-16c3aa56c6fa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397088686200, "endTime": 312397088757100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "43d0858d-222c-45c9-bd87-1921f5d99cdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43d0858d-222c-45c9-bd87-1921f5d99cdc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312397088686200, "endTime": 312397088757100}, "additional": {"logType": "info", "children": [], "durationId": "9dab4c1a-2787-4c1b-9c86-16c3aa56c6fa", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "bb48d628-99ed-4683-a559-1711b184bc3f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401194372800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa036315-92ed-4ea2-a9c0-9e36ce44d7a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401195459700, "endTime": 312401195476700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c296f29b-d714-4895-be55-7c73b7f89ac0", "logId": "f133a3a6-131a-4947-8ce9-7a1f3eb15ab5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f133a3a6-131a-4947-8ce9-7a1f3eb15ab5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401195459700, "endTime": 312401195476700}, "additional": {"logType": "info", "children": [], "durationId": "fa036315-92ed-4ea2-a9c0-9e36ce44d7a0", "parent": "71a8ada8-f441-44c4-913c-ac6fe9e3432c"}}, {"head": {"id": "71a8ada8-f441-44c4-913c-ac6fe9e3432c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Worker4", "startTime": 312384379957600, "endTime": 312401195540900}, "additional": {"logType": "error", "children": ["bd90f8bb-a202-48f9-9c12-d377ace86d73", "7ca24e57-3824-4ead-9263-85bf924c6bc2", "4a4e8cd5-0f25-44f7-904a-27d3dac6aff2", "afb232ea-f35f-460e-aa80-a3a43e245732", "bbba28ea-30e6-48ce-a4bd-ab86806d7999", "685fb860-e090-4d7d-85ae-f0d23fdb64c1", "9dccb37d-0a19-4dba-b50c-f849a8c1bc88", "c15300a5-868c-40af-9939-fd1fc021db5f", "dc8445e3-791e-4cfc-a3b0-ca25558dbcbd", "1c34a3ee-e8d0-4bc2-bd89-ed0889c4c243", "8179dfed-24d3-472b-b53d-a88739833e3d", "75996e5f-b28c-423b-8042-53631986a1e1", "a4dfcc25-4eda-49ed-a424-8416e6b27a3f", "381abeaf-5263-447e-a20d-8230e1f8888a", "090f3dbb-e060-4b43-aa09-371eb74bc963", "c5e01546-d7d2-4616-bae1-0c4d1181cbf7", "2ac2f06c-57e1-4e60-a4ed-ed85cfa75f01", "9f4120fc-77b6-46f2-878c-a7b8db799494", "b18c5bb7-de29-427d-b8dd-1feac9e9ebfd", "6bf553ac-63b8-4a99-b5a3-25bc8ddc5a82", "aea84235-80cc-4f4d-b6fa-b7f02d8d9420", "98469c5b-a267-4c76-9ded-d8fb6f7254d8", "e625677e-0130-4a78-9677-7c20b4fdc8b3", "bbe19a75-1b64-4973-be00-88c75bbf267f", "480335b1-5f5a-48fd-b9f6-4499d5a1d5ce", "d7e75d7c-c132-41a3-a894-1132859c718c", "7dc49ab1-eeb9-4f6a-973d-2332ca33ebb1", "1381c302-a8f9-4b43-9ac1-a6c182415898", "c111fd25-3369-418c-9b28-7a5bab60df48", "e1738cef-dbb2-44e0-bc58-40b989905257", "27981084-dd14-4abc-a6f7-743a98a7aaf5", "0c23e1dc-9bdb-4539-b05b-963ed2b26ace", "290f9040-c3f6-4726-a4b2-804120118518", "c95ccca0-cc20-4403-ac50-577490aec6d8", "e2a5947f-0077-4a44-a60f-323df9e3993f", "50a5f51a-1cfb-4244-9ab5-68aab825ba40", "27ba9f5a-a197-4ba3-abaf-f2a1c59e1951", "aa717a09-bb32-4803-a11b-b546fd5658ee", "a48c1883-d8eb-4f08-a12c-a13942d6356c", "911063b3-db72-4ddf-9086-60811d4d261e", "43d0858d-222c-45c9-bd87-1921f5d99cdc", "f133a3a6-131a-4947-8ce9-7a1f3eb15ab5"], "durationId": "c296f29b-d714-4895-be55-7c73b7f89ac0", "parent": "bec08c88-4084-4b2a-84cc-ea80e95c909f"}}, {"head": {"id": "69282b14-2ec3-4e76-90d9-e485392c930c", "name": "default@PreviewArkTS watch work[4] failed.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401195666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec08c88-4084-4b2a-84cc-ea80e95c909f", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312384352780800, "endTime": 312401195810800}, "additional": {"logType": "error", "children": ["71a8ada8-f441-44c4-913c-ac6fe9e3432c"], "durationId": "044a8e0a-601f-4220-a767-f83cc3af8f1a"}}, {"head": {"id": "0d88553e-aa2b-4a0c-818e-86a1976f4342", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401195946200}, "additional": {"logType": "debug", "children": [], "durationId": "044a8e0a-601f-4220-a767-f83cc3af8f1a"}}, {"head": {"id": "3189cf09-68a4-4962-8205-51bca2a21884", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:41:12\n Array literals must contain elements of only inferrable types (arkts-no-noninferrable-arr-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:42:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:50:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:58:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:66:7\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:48:35\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:51:9\n Conversion of type '{ id: number; type: any; amount: number; target: string; time: string; status: any; }[]' to type 'Transaction[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n  Type '{ id: number; type: any; amount: number; target: string; time: string; status: any; }' is not comparable to type 'Transaction'.\n    Object literal may only specify known properties, and 'id' does not exist in type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:52:31\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:56:35\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:60:31\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:64:35\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:72:35\n Property 'PROCESSING' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:142:38\n Property 'type' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:147:49\n Property 'type' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:155:40\n Property 'type' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:166:21\n Property 'target' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:173:23\n Property 'time' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:197:28\n Property 'id' does not exist on type 'Transaction'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:205:28\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:207:28\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:215:28\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:217:28\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:225:28\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:227:28\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:234:30\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:235:30\n Property 'PROCESSING' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:237:30\n Property 'CANCELED' does not exist on type 'typeof TransactionStatus'. Did you mean 'CANCELLED'?\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:244:30\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:245:30\n Property 'PROCESSING' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransferPage.ets:247:30\n Property 'CANCELED' does not exist on type 'typeof TransactionStatus'. Did you mean 'CANCELLED'?\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:348:28\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:350:28\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:359:28\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:361:28\n Property 'CONSUME' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:369:68\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:375:60\n Property 'DEPOSIT' does not exist on type 'typeof TransactionType'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:381:30\n Property 'COMPLETED' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:382:30\n Property 'PROCESSING' does not exist on type 'typeof TransactionStatus'.\n\u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/pages/TransactionListPage.ets:384:30\n Property 'CANCELED' does not exist on type 'typeof TransactionStatus'. Did you mean 'CANCELLED'?\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401196950000}, "additional": {"logType": "debug", "children": [], "durationId": "044a8e0a-601f-4220-a767-f83cc3af8f1a"}}, {"head": {"id": "ca64ef16-9718-4d6c-9d4d-4f153aba429a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401203728700, "endTime": 312401203773700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff7e7006-f63f-41c7-8a00-a5f6b0e47c3e", "logId": "057151c8-2d6f-4334-841a-97436a8ac1b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "057151c8-2d6f-4334-841a-97436a8ac1b9", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401203728700, "endTime": 312401203773700}, "additional": {"logType": "info", "children": [], "durationId": "ca64ef16-9718-4d6c-9d4d-4f153aba429a"}}, {"head": {"id": "8c8bba86-96f6-4b84-83c3-c0814479c6b9", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312383727764500, "endTime": 312401203879000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 10, "minute": 33}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "02714795-aaf7-4f82-b7a3-22b6aaf01bec", "name": "BUILD FAILED in 17 s 477 ms ", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401203907700}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "254813eb-5d94-4d52-ba29-36d8050f0dfc", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b902aa4f-f77e-4877-8530-b641a194d192", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0513e2c-90ee-4add-9790-5eecf2146d8e", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af35c35d-ea1c-4b99-980f-176bed6545da", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204255500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9b8512-129a-438c-919b-f1abbab5d3bf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beae29e2-6daf-454a-93ed-9501ebf4e18b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be55d14-2cdf-4943-842b-a4b0adb03a7f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204457700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8898c782-f9c1-4389-b107-34b37253c37d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204501500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b6b2208-affb-4c0b-b717-b5b4db3ad7a7", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204546400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adae0712-3811-43ca-a3fd-e555a65567aa", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401204592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7719df4c-e2d4-4fcb-a7ac-3335504b0c0d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401208153500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3255f99-f045-463d-9820-dda93456b794", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401227807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9acfba-7bfa-44c0-ab60-fa0a60f724c3", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401228648800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9971c0f2-8f00-4500-a9e2-7ae2cc54770f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401229328200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01162761-e86c-458b-9a9b-36a7d78cda59", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401231211700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab78479c-ad43-410d-bf16-fde98dd215ef", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401251960000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69cd2639-1b20-4be0-9478-66b19bb84de6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401252413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c86f995-362b-4d0e-a108-6ce021395083", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401252798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958aeafe-e340-4f04-a60a-a124f06ef51a", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401253268000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ca3355-7197-4668-b66f-b46b970271d1", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:49 ms .", "description": "", "type": "log"}, "body": {"pid": 13488, "tid": "Main Thread", "startTime": 312401253661000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}