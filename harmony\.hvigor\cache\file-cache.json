{"C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\app.json5": {"hashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c", "name": "app.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1750214348664}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\module.json5": {"hashValue": "7090176ec3048fba8580cdf55d1daaa5", "name": "module.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1629, "lastModifiedTime": 1750209969129}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\build-profile.json5": {"hashValue": "2610b30be322d40e665ac40d75ee654a", "name": "build-profile.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 756, "lastModifiedTime": 1750140648715}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build-profile.json5": {"hashValue": "3f5b9f9d6a98660c37307ee634fe1eb7", "name": "build-profile.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 449, "lastModifiedTime": 1750140648742}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json": {"hashValue": "7ccc8cdb9c580d27c1247548424846f7", "name": "main_pages.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750339019477}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\hvigor\\hvigor-config.json5": {"hashValue": "b3c28932150c2f93f31ec74602c77fd6", "name": "hvigor-config.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\hvigor\\hvigor-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1323, "lastModifiedTime": 1750140645609}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\mock\\mock-config.json5": {"hashValue": "3c7125ff64a1f5cb04652ed4b904e0a3", "name": "mock-config.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\mock\\mock-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3, "lastModifiedTime": 1750140646029}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\oh-package.json5": {"hashValue": "752cb6a7b1a256aa66ddaaec6cca37cd", "name": "oh-package.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 183, "lastModifiedTime": 1750140645791}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\oh-package.json5": {"hashValue": "a2b58636854e0cff5afa91c959f75e0c", "name": "oh-package.json5", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 210, "lastModifiedTime": 1750140648724}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json": {"hashValue": "8f140dc29188172e089e8a7d585068de", "name": "output_metadata.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 100, "lastModifiedTime": 1750210566878}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "4f558f1183c62653664d4985d7952db3", "name": "module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1854, "lastModifiedTime": 1750214406712}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "10b0015f894c6dcd11eb9b672be4026b", "name": "BuildProfile.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 862, "lastModifiedTime": 1750214406725}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json": {"hashValue": "7d1ccda73c6d9f519a5a0712072f7f0e", "name": "pkgContextInfo.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 444, "lastModifiedTime": 1750209914705}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json": {"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\pack.info": {"hashValue": "ab1932c5c4377e81d8c00ecae774f55a", "name": "pack.info", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 636, "lastModifiedTime": 1750214406805}}, "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe": {"hashValue": "ac8dfe8c3f0133dec4f1fc35ef7cc6d8", "name": "syscap_tool.exe", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 347648, "lastModifiedTime": 1741834954000}}, "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define": {"hashValue": "b39a35ea4b0883a45e39815d85495855", "name": "device-define", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "db996466002a1bf08c8347d5b4ce8219", "name": "default.json", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define\\default.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12477, "lastModifiedTime": 1741834954000}}, {"hashValue": "33557eb3df32fee80c99c78bf790b24f", "name": "liteWearable.json", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define\\liteWearable.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 883, "lastModifiedTime": 1741834954000}}, {"hashValue": "ddd35e180c5fc093a85b9b24a94de54f", "name": "tablet.json", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define\\tablet.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11990, "lastModifiedTime": 1741834954000}}, {"hashValue": "9d10b5277aac4982b5c75918a18b0bee", "name": "wearable.json", "path": "D:\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define\\wearable.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12130, "lastModifiedTime": 1741834954000}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "c33aeeff8408c5f00531dfbfda7f1082", "name": "module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1940, "lastModifiedTime": 1750214407008}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\router_map\\default\\temp-router-map.json": {"hashValue": "********************************", "name": "temp-router-map.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\router_map\\default\\temp-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1750210567154}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json": {"hashValue": "67103dfa54df1fc8c00be4d2a9503504", "name": "loader-router-map.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1750210567155}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json": {"hashValue": "56b7e96c5dae23843286e0d05e30b17d", "name": "resConfig.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1277, "lastModifiedTime": 1750214407063}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json": {"hashValue": "********************************", "name": "opt-compression.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 180, "lastModifiedTime": 1750214407061}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "e1296b8e8dccf7b3d89f8aa0696a26dd", "name": "loader.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1066, "lastModifiedTime": 1750210567245}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default": {"hashValue": "", "name": "default", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\libs\\default", "type": "directory", "isSymbolicLink": false, "children": []}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources": {"hashValue": "8d846a6faa2900b9311afd87737e33ed", "name": "resources", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "02c2b0e63c13d9c4d48c1ad4cc1cef82", "name": "base", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "00a1e7563c1d4721a1f9bc9455a541ef", "name": "element", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c84be398aee391643d3704723cb4731e", "name": "color.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 105, "lastModifiedTime": 1750140646072}}, {"hashValue": "15c21f3ec940e021bec7450ebf817bc8", "name": "float.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\float.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 100, "lastModifiedTime": 1750140646098}}, {"hashValue": "f0164c2aa444198c92fd7d2c03050a97", "name": "string.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 570, "lastModifiedTime": 1750209981815}}]}, {"hashValue": "873febe6f91bcfb0acc2957a6268dba3", "name": "media", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ffd640e8a1e49aa1f6e31c3141412f90", "name": "app_icon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5247, "lastModifiedTime": 1750209677825}}, {"hashValue": "0a90e50c6e19b5879caa848d8917df7c", "name": "background.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750140646124}}, {"hashValue": "b522112fceb1ff1ec46ee6dc21354f7c", "name": "foreground.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750140646141}}, {"hashValue": "9615b0f7827d0626b8548c3c68946cd1", "name": "ic_add.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_add.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2421, "lastModifiedTime": 1750211852881}}, {"hashValue": "36c1d3b5fcf5574874fe9e90b8b76ed1", "name": "ic_arrow_right.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_arrow_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2398, "lastModifiedTime": *************}}, {"hashValue": "9ae925f89e25f11debd6119163a72935", "name": "ic_back.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_back.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2649, "lastModifiedTime": *************}}, {"hashValue": "0de05ca4985fc2a2621c3f750e1c8d3e", "name": "ic_bank_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_bank_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3170, "lastModifiedTime": *************}}, {"hashValue": "5b9389aa4f9b992ce76835dd3d313047", "name": "ic_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2497, "lastModifiedTime": *************}}, {"hashValue": "9aa1c66326f376fc7867675318f15abf", "name": "ic_empty_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_empty_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3072, "lastModifiedTime": *************}}, {"hashValue": "e2e5c96813df13770e053585d09660e7", "name": "ic_profile.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_profile.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4146, "lastModifiedTime": *************}}, {"hashValue": "948e7b708a6c765b306cd7a3aa6ac17f", "name": "ic_recharge.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_recharge.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4377, "lastModifiedTime": 1750211680067}}, {"hashValue": "e4cbc8d8a5f2a4324585c327d4f1adf2", "name": "ic_settings.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_settings.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5233, "lastModifiedTime": 1750211649869}}, {"hashValue": "964bd9acd6c4498374897aef0ab35c92", "name": "ic_transaction.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_transaction.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4378, "lastModifiedTime": 1750211815475}}, {"hashValue": "cb315e4c456888a42a72c51ec660dcda", "name": "ic_transfer.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_transfer.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2261, "lastModifiedTime": 1750212194626}}, {"hashValue": "3245f8600b625faa013a82050abd4b7e", "name": "ic_wallet.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_wallet.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3578, "lastModifiedTime": 1750211776680}}, {"hashValue": "dafef4680b150d69c64473c416d5711b", "name": "ic_withdraw.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_withdraw.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4027, "lastModifiedTime": 1750211696378}}, {"hashValue": "edf1ac45fbca0d134ada8c8c1087d38b", "name": "layered_image.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750140646157}}, {"hashValue": "3206f4b053137849137d330b02549fe6", "name": "startIcon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750140646190}}]}, {"hashValue": "e1a09519f0ea6d6dd19af7d6ee26adca", "name": "profile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "7ccc8cdb9c580d27c1247548424846f7", "name": "main_pages.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750339019477}}]}]}, {"hashValue": "7c8379239da2c6592b5bdf8bcd245cb3", "name": "dark", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\dark", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "da22853fc3d1479ade71d3f28db2e8ac", "name": "element", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\dark\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2d96dc1178019b924854b12ebb966231", "name": "color.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\dark\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 105, "lastModifiedTime": 1750140646310}}]}]}, {"hashValue": "", "name": "rawfile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\resources\\rawfile", "type": "directory", "isSymbolicLink": false, "children": []}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources": {"hashValue": "1fb3d502133f4592d97e99fa98b24b09", "name": "resources", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b99487e9bcc0c2c12fbe826e8858cc7f", "name": "base", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4a1ca550be97dd8537f70be6df422cb0", "name": "element", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "681b6d7668e7e60f5eefcac70450a394", "name": "string.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 93, "lastModifiedTime": 1750140645720}}]}, {"hashValue": "19a8734404035756bf2b84cb2bdc14a1", "name": "media", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "49392f47ff504aa51b7127ebf43e9284", "name": "background.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750140645668}}, {"hashValue": "928cf9c25e42c5e81bdda44525062b8d", "name": "foreground.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750140645682}}, {"hashValue": "66bb11a96e5b85a8aeb3e4481a8a9a84", "name": "layered_image.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\AppScope\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750140645695}}]}]}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default": {"hashValue": "7623d585cdbb7e1268b06db16dd39808", "name": "default", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6ea8c956dd60d6a5fca966354d94109f", "name": ".caches", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\.caches", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "74df7ef39e9ceed39be6f2a4eea9dca3", "name": "base", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\.caches\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "cf7d2a2447fd1e6cb165608130ff1161", "name": "media", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\.caches\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4dbe76abe03f83212618b591b6bd242d", "name": "background.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\.caches\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1750339247287}}, {"hashValue": "7bd6140111c2c554c7079b54ef5c8805", "name": "foreground.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\.caches\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1750339247330}}]}]}]}, {"hashValue": "555a7f8a01c9772bf4c385970303b9cf", "name": "ark_module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1526, "lastModifiedTime": 1750214407006}}, {"hashValue": "9b10b26274c8102002afcba9ee48e171", "name": "ids_map", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f916fa9f1f8d70b4a8c488612bcd9ca7", "name": "id_defined.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2163, "lastModifiedTime": 1750339247335}}]}, {"hashValue": "5158e6239cb92ff7efb0db9ae76ddca0", "name": "module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2155, "lastModifiedTime": 1750339247242}}, {"hashValue": "********************************", "name": "opt-compression.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 180, "lastModifiedTime": 1750214407061}}, {"hashValue": "56b7e96c5dae23843286e0d05e30b17d", "name": "resConfig.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1277, "lastModifiedTime": 1750214407063}}, {"hashValue": "a9e74228925f88829e27333c4020c70f", "name": "resources", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0e100f7934b41015f7c9c5ad765acf15", "name": "base", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "d9e437b4fb42f7ece7b1ce08e0b93f31", "name": "media", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ffd640e8a1e49aa1f6e31c3141412f90", "name": "app_icon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5247, "lastModifiedTime": 1750209677825}}, {"hashValue": "4dbe76abe03f83212618b591b6bd242d", "name": "background.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1750339247287}}, {"hashValue": "7bd6140111c2c554c7079b54ef5c8805", "name": "foreground.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1750339247330}}, {"hashValue": "9615b0f7827d0626b8548c3c68946cd1", "name": "ic_add.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_add.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2421, "lastModifiedTime": 1750211852881}}, {"hashValue": "36c1d3b5fcf5574874fe9e90b8b76ed1", "name": "ic_arrow_right.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_arrow_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2398, "lastModifiedTime": *************}}, {"hashValue": "9ae925f89e25f11debd6119163a72935", "name": "ic_back.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_back.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2649, "lastModifiedTime": *************}}, {"hashValue": "0de05ca4985fc2a2621c3f750e1c8d3e", "name": "ic_bank_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_bank_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3170, "lastModifiedTime": *************}}, {"hashValue": "5b9389aa4f9b992ce76835dd3d313047", "name": "ic_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2497, "lastModifiedTime": *************}}, {"hashValue": "9aa1c66326f376fc7867675318f15abf", "name": "ic_empty_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_empty_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3072, "lastModifiedTime": *************}}, {"hashValue": "e2e5c96813df13770e053585d09660e7", "name": "ic_profile.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_profile.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4146, "lastModifiedTime": *************}}, {"hashValue": "948e7b708a6c765b306cd7a3aa6ac17f", "name": "ic_recharge.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_recharge.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4377, "lastModifiedTime": 1750211680067}}, {"hashValue": "e4cbc8d8a5f2a4324585c327d4f1adf2", "name": "ic_settings.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_settings.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5233, "lastModifiedTime": 1750211649869}}, {"hashValue": "964bd9acd6c4498374897aef0ab35c92", "name": "ic_transaction.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_transaction.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4378, "lastModifiedTime": 1750211815475}}, {"hashValue": "cb315e4c456888a42a72c51ec660dcda", "name": "ic_transfer.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_transfer.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2261, "lastModifiedTime": 1750212194626}}, {"hashValue": "3245f8600b625faa013a82050abd4b7e", "name": "ic_wallet.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_wallet.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3578, "lastModifiedTime": 1750211776680}}, {"hashValue": "dafef4680b150d69c64473c416d5711b", "name": "ic_withdraw.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_withdraw.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4027, "lastModifiedTime": 1750211696378}}, {"hashValue": "4f5b4d1e99c27bfad43abdc48e31736e", "name": "layered_image.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1750339247232}}, {"hashValue": "3206f4b053137849137d330b02549fe6", "name": "startIcon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750140646190}}]}, {"hashValue": "e1a09519f0ea6d6dd19af7d6ee26adca", "name": "profile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "7ccc8cdb9c580d27c1247548424846f7", "name": "main_pages.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750339019477}}]}]}, {"hashValue": "", "name": "rawfile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "19ede55d1705bdf56dfa47c111014234", "name": "resources.index", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2321, "lastModifiedTime": 1750339247334}}, {"hashValue": "cf4fe6ac71a701e9bba77f4ea7cbbdbc", "name": "ResourceTable.txt", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 859, "lastModifiedTime": 1750339247236}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h": {"hashValue": "393cd803dd9c52d4aba73fe86ac69ddc", "name": "ResourceTable.h", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2041, "lastModifiedTime": 1750339247235}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default": {"hashValue": "5ab46794d8a0b98e0b9f1378cdd68641", "name": "default", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "393cd803dd9c52d4aba73fe86ac69ddc", "name": "ResourceTable.h", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2041, "lastModifiedTime": 1750339247235}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default": {"hashValue": "a15891a7280ce43fb61efcd2351f40a1", "name": "default", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e1296b8e8dccf7b3d89f8aa0696a26dd", "name": "loader.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1066, "lastModifiedTime": 1750210567245}}, {"hashValue": "7d1ccda73c6d9f519a5a0712072f7f0e", "name": "pkgContextInfo.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 444, "lastModifiedTime": 1750209914705}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile": {"hashValue": "", "name": "rawfile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", "type": "directory", "isSymbolicLink": false, "children": []}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "cf4fe6ac71a701e9bba77f4ea7cbbdbc", "name": "ResourceTable.txt", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 859, "lastModifiedTime": 1750339247236}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json": {"hashValue": "555a7f8a01c9772bf4c385970303b9cf", "name": "ark_module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1526, "lastModifiedTime": 1750214407006}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "e1a09519f0ea6d6dd19af7d6ee26adca", "name": "profile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "7ccc8cdb9c580d27c1247548424846f7", "name": "main_pages.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750339019477}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets": {"hashValue": "3603cb9eb67c86baff6a2f681cee76e2", "name": "ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ced5d60c428ec209f35b45d082121c23", "name": "api", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\api", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "546f4232abc88bb6ab84e2b9db6c1d7a", "name": "BankCardApi.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\api\\BankCardApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1531, "lastModifiedTime": *************}}, {"hashValue": "1539e70689a1a2c19626d05830d4b1a6", "name": "TransactionApi.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\api\\TransactionApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 805, "lastModifiedTime": *************}}, {"hashValue": "f87d511ea87fed57e4363ef8711a9c4a", "name": "UserApi.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\api\\UserApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1282, "lastModifiedTime": *************}}, {"hashValue": "e16022909aaea446818414847d4f065d", "name": "WalletApi.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\api\\WalletApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 983, "lastModifiedTime": *************}}]}, {"hashValue": "5e6b416117d734a9166e856a1d1b5416", "name": "common", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "452f6a5a1cde73c0d94f98804d042018", "name": "http", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\http", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "04a7c2c42ee5c53d886eb4d9dcac2baf", "name": "HttpClient.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\http\\HttpClient.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7245, "lastModifiedTime": 1750216520546}}]}, {"hashValue": "a02c21c00eb8324cbdf5af0f912a8520", "name": "storage", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\storage", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "62c4f4c255328beda7f647d5179018ad", "name": "StorageManager.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\storage\\StorageManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6317, "lastModifiedTime": 1750217692561}}, {"hashValue": "274278c88d973801b2c07d3160b4193e", "name": "TempDataManager.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\storage\\TempDataManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1750299036429}}]}, {"hashValue": "2d844a99da7242f72ea4ab50372c4519", "name": "types", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\types", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2dce590f3d6c2bea83d28792c7e9c922", "name": "index.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\types\\index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7014, "lastModifiedTime": 1750342843633}}]}, {"hashValue": "f65eba83963ba6f86b8a439cf69421e9", "name": "utils", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "695ff7cf1499a22ceadc44a7290ab1d8", "name": "DataFormatter.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\DataFormatter.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8344, "lastModifiedTime": 1750216629200}}, {"hashValue": "6cde28fad176f4bdda92d10778b27e5e", "name": "ErrorHandler.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\ErrorHandler.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6722, "lastModifiedTime": 1750217368712}}, {"hashValue": "86edd24bd76f30b8dc3d3b50a0204971", "name": "EventManager.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\EventManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1640, "lastModifiedTime": 1750219504452}}, {"hashValue": "d90d247aab9ca1b9a9a325be836c66d3", "name": "FormValidator.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\FormValidator.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6180, "lastModifiedTime": 1750215419458}}, {"hashValue": "04e120d55f21f0326e4091936b2a3372", "name": "LoadingManager.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\LoadingManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4755, "lastModifiedTime": 1750216579049}}]}]}, {"hashValue": "99666a08add9cb0c69ae6f1254514a35", "name": "entryability", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bfdeda62649bc65b5b680899a6d1f2f9", "name": "EntryAbility.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2105, "lastModifiedTime": 1750210855553}}]}, {"hashValue": "fd24ce9eef2afd00b9a7028484909117", "name": "entrybackupability", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "********************************", "name": "EntryBackupAbility.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 522, "lastModifiedTime": *************}}]}, {"hashValue": "5193dffccdbbd6cd6959f59847b82c2c", "name": "pages", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0401cbc46c8ad5df2d3a60e6de4de675", "name": "AboutPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\AboutPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5747, "lastModifiedTime": *************}}, {"hashValue": "fee83fe26dc9a11ca8c5b6e947b7a7ee", "name": "AddBankCardPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13939, "lastModifiedTime": *************}}, {"hashValue": "afe3a4c86198353372ba54d87f4d8ad1", "name": "BankCardDetailPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8049, "lastModifiedTime": *************}}, {"hashValue": "2677d05e2a1917ac2f6e53334155c824", "name": "BankCardPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8626, "lastModifiedTime": *************}}, {"hashValue": "68b14848c731db9a11aa2ad041a856ed", "name": "BankSelectorPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankSelectorPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3769, "lastModifiedTime": *************}}, {"hashValue": "4b2b65ef530fd8d3041fa6996d43e7fc", "name": "CardTypeSelectorPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\CardTypeSelectorPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3917, "lastModifiedTime": *************}}, {"hashValue": "0ffa1bcc4bd6ddc6d5008214f2861564", "name": "ChangePayPasswordPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\ChangePayPasswordPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9134, "lastModifiedTime": *************}}, {"hashValue": "57d97f9edb0c7622e4e33abf11bd8eea", "name": "ForgotPasswordPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\ForgotPasswordPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11079, "lastModifiedTime": 1750335290329}}, {"hashValue": "ebf3984b50424acbb7ccb81d8ef91299", "name": "HelpCenterPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\HelpCenterPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4968, "lastModifiedTime": 1750339338058}}, {"hashValue": "03d1af837232da72e7a1fce2aed11118", "name": "Index.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2360, "lastModifiedTime": 1750214588970}}, {"hashValue": "0c6e4e6f5accff37b5f708813ef04a8d", "name": "LoginPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11502, "lastModifiedTime": 1750333352123}}, {"hashValue": "2bd4cf936d73d7624313e0e8521b14e3", "name": "MainPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\MainPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 33477, "lastModifiedTime": 1750409109893}}, {"hashValue": "700c811d0239684d5179a22bb15c8e3d", "name": "PayLimitSettingPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\PayLimitSettingPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7755, "lastModifiedTime": 1750232076284}}, {"hashValue": "c9a72058ade751cede34a4ab28d06b2b", "name": "RechargePage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\RechargePage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10235, "lastModifiedTime": 1750409134396}}, {"hashValue": "fc69ec93b918c627c786be3c36b1b0a4", "name": "RegisterPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\RegisterPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6549, "lastModifiedTime": 1750333579474}}, {"hashValue": "46257360cf948c5bd67aaef1cb7c624b", "name": "SettingsPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8684, "lastModifiedTime": 1750340017704}}, {"hashValue": "c2a4129a9d8f86abb4febd7379241af6", "name": "TransactionDetailPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionDetailPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8270, "lastModifiedTime": 1750342556196}}, {"hashValue": "39b605c69037d7854b72bc66020d3266", "name": "TransactionListPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9920, "lastModifiedTime": 1750343025161}}, {"hashValue": "fdf24495bc40b7139eba2f7d08a16864", "name": "TransferPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransferPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7403, "lastModifiedTime": 1750336768106}}, {"hashValue": "f0768ba26354650b9c4c3d274f419f5a", "name": "WithdrawPage.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\pages\\WithdrawPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11141, "lastModifiedTime": 1750337216208}}]}, {"hashValue": "4110317c1f795db44e4fa444982d9383", "name": "test", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\test", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a5f9b5cf4d5b513c985e800cf55e146d", "name": "TypeCheck.ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\src\\main\\ets\\test\\TypeCheck.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3920, "lastModifiedTime": *************}}]}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets": {"hashValue": "91c7d7ef10cc9d6c59e4e812e47b4806", "name": "ets", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8d9349348aa072566152ae203146bf39", "name": "modules.abc", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 606068, "lastModifiedTime": 1750411579141}}, {"hashValue": "43facd534529c7742d32faedbba523e3", "name": "sourceMaps.map", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 234845, "lastModifiedTime": 1750411578662}}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\stripped_native_libs\\default": {"hashValue": "", "name": "default", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", "type": "directory", "isSymbolicLink": false, "children": []}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json": {"hashValue": "8fdedd36f9f20d01ed3e6c8aed9cdc5a", "name": "base_native_libs.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 25, "lastModifiedTime": 1750209919802}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\module.json": {"hashValue": "5158e6239cb92ff7efb0db9ae76ddca0", "name": "module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2155, "lastModifiedTime": 1750339247242}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\package\\default\\module.json": {"hashValue": "5158e6239cb92ff7efb0db9ae76ddca0", "name": "module.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\package\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2155, "lastModifiedTime": 1750339247242}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources": {"hashValue": "a9e74228925f88829e27333c4020c70f", "name": "resources", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0e100f7934b41015f7c9c5ad765acf15", "name": "base", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "d9e437b4fb42f7ece7b1ce08e0b93f31", "name": "media", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ffd640e8a1e49aa1f6e31c3141412f90", "name": "app_icon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5247, "lastModifiedTime": 1750209677825}}, {"hashValue": "4dbe76abe03f83212618b591b6bd242d", "name": "background.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1750339247287}}, {"hashValue": "7bd6140111c2c554c7079b54ef5c8805", "name": "foreground.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1750339247330}}, {"hashValue": "9615b0f7827d0626b8548c3c68946cd1", "name": "ic_add.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_add.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2421, "lastModifiedTime": 1750211852881}}, {"hashValue": "36c1d3b5fcf5574874fe9e90b8b76ed1", "name": "ic_arrow_right.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_arrow_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2398, "lastModifiedTime": *************}}, {"hashValue": "9ae925f89e25f11debd6119163a72935", "name": "ic_back.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_back.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2649, "lastModifiedTime": *************}}, {"hashValue": "0de05ca4985fc2a2621c3f750e1c8d3e", "name": "ic_bank_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_bank_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3170, "lastModifiedTime": *************}}, {"hashValue": "5b9389aa4f9b992ce76835dd3d313047", "name": "ic_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2497, "lastModifiedTime": *************}}, {"hashValue": "9aa1c66326f376fc7867675318f15abf", "name": "ic_empty_card.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_empty_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3072, "lastModifiedTime": *************}}, {"hashValue": "e2e5c96813df13770e053585d09660e7", "name": "ic_profile.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_profile.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4146, "lastModifiedTime": *************}}, {"hashValue": "948e7b708a6c765b306cd7a3aa6ac17f", "name": "ic_recharge.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_recharge.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4377, "lastModifiedTime": 1750211680067}}, {"hashValue": "e4cbc8d8a5f2a4324585c327d4f1adf2", "name": "ic_settings.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_settings.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5233, "lastModifiedTime": 1750211649869}}, {"hashValue": "964bd9acd6c4498374897aef0ab35c92", "name": "ic_transaction.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_transaction.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4378, "lastModifiedTime": 1750211815475}}, {"hashValue": "cb315e4c456888a42a72c51ec660dcda", "name": "ic_transfer.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_transfer.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2261, "lastModifiedTime": 1750212194626}}, {"hashValue": "3245f8600b625faa013a82050abd4b7e", "name": "ic_wallet.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_wallet.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3578, "lastModifiedTime": 1750211776680}}, {"hashValue": "dafef4680b150d69c64473c416d5711b", "name": "ic_withdraw.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_withdraw.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4027, "lastModifiedTime": 1750211696378}}, {"hashValue": "4f5b4d1e99c27bfad43abdc48e31736e", "name": "layered_image.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1750339247232}}, {"hashValue": "3206f4b053137849137d330b02549fe6", "name": "startIcon.png", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750140646190}}]}, {"hashValue": "e1a09519f0ea6d6dd19af7d6ee26adca", "name": "profile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "7ccc8cdb9c580d27c1247548424846f7", "name": "main_pages.json", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750339019477}}]}]}, {"hashValue": "", "name": "rawfile", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", "type": "directory", "isSymbolicLink": false, "children": []}]}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources.index": {"hashValue": "19ede55d1705bdf56dfa47c111014234", "name": "resources.index", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2321, "lastModifiedTime": 1750339247334}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map": {"hashValue": "43facd534529c7742d32faedbba523e3", "name": "sourceMaps.map", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 234845, "lastModifiedTime": 1750411578662}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap": {"hashValue": "5f95c814e52f7be24072b631271fa481", "name": "entry-default-unsigned.hap", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 999437, "lastModifiedTime": 1750411580112}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map": {"hashValue": "43facd534529c7742d32faedbba523e3", "name": "sourceMaps.map", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 234845, "lastModifiedTime": 1750411578662}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map": {"hashValue": "43facd534529c7742d32faedbba523e3", "name": "sourceMaps.map", "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\Wallet\\harmony\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 234845, "lastModifiedTime": 1750411578662}}, "F:\\e-wallet\\harmony\\AppScope\\app.json5": {"hashValue": "11e2198ed76cddffdfd1e8db0a1d9c8c", "name": "app.json5", "path": "F:\\e-wallet\\harmony\\AppScope\\app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1750214348664}}, "F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5": {"hashValue": "e28058952addff9802af04d2667e51ae", "name": "module.json5", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1628, "lastModifiedTime": 1750843663268}}, "F:\\e-wallet\\harmony\\build-profile.json5": {"hashValue": "97b51a6d29cdc809c9b54239e4db53a2", "name": "build-profile.json5", "path": "F:\\e-wallet\\harmony\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 755, "lastModifiedTime": 1750843527404}}, "F:\\e-wallet\\harmony\\entry\\build-profile.json5": {"hashValue": "9da6945291df8ef3ba4da44e85efec71", "name": "build-profile.json5", "path": "F:\\e-wallet\\harmony\\entry\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 447, "lastModifiedTime": 1750843616204}}, "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json": {"hashValue": "0fa30755b804d6cbee77ee403eaf52e5", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 523, "lastModifiedTime": 1750862172779}}, "F:\\e-wallet\\harmony\\hvigor\\hvigor-config.json5": {"hashValue": "b3c28932150c2f93f31ec74602c77fd6", "name": "hvigor-config.json5", "path": "F:\\e-wallet\\harmony\\hvigor\\hvigor-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1323, "lastModifiedTime": 1750140645609}}, "F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5": {"hashValue": "3c7125ff64a1f5cb04652ed4b904e0a3", "name": "mock-config.json5", "path": "F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3, "lastModifiedTime": 1750140646029}}, "F:\\e-wallet\\harmony\\entry\\oh-package.json5": {"hashValue": "752cb6a7b1a256aa66ddaaec6cca37cd", "name": "oh-package.json5", "path": "F:\\e-wallet\\harmony\\entry\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 183, "lastModifiedTime": 1750140645791}}, "F:\\e-wallet\\harmony\\oh-package.json5": {"hashValue": "a2b58636854e0cff5afa91c959f75e0c", "name": "oh-package.json5", "path": "F:\\e-wallet\\harmony\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 210, "lastModifiedTime": 1750140648724}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "d8ce18749004845350512f169dc630d2", "name": "module.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1854, "lastModifiedTime": 1750843701359}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "19fe28f08dbb8d1a45bcd92dc633a693", "name": "BuildProfile.ets", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 862, "lastModifiedTime": 1750843701396}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json": {"hashValue": "dbddfdce2fcf8cdffa2cb3c256479126", "name": "pkgContextInfo.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 444, "lastModifiedTime": 1750748452073}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "aa217ab8c68551b15dfa0795730e3e52", "name": "module.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1940, "lastModifiedTime": 1750843701595}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json": {"hashValue": "1a6fa0677bc5a7283410c4e5b7c35f2c", "name": "temp-router-map.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1750843701629}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json": {"hashValue": "5c909708794ec0ea7aaa5f369cc62746", "name": "loader-router-map.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1750843701630}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "bc7b67707dd5770d834c21831afbc2dd", "name": "loader.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 779, "lastModifiedTime": 1750843701728}}, "F:\\e-wallet\\harmony\\entry\\src\\main\\resources": {"hashValue": "56a4984cedc2ba528d09dc76e841d08a", "name": "resources", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a9461b1ff1bcf00316062dc9e54994e3", "name": "base", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4cad66068212e11f00ac41c6418d0eb4", "name": "element", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b6bd5770e592e73d5e2ef29a1fe6d2c8", "name": "color.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1517, "lastModifiedTime": 1750903697497}}, {"hashValue": "15c21f3ec940e021bec7450ebf817bc8", "name": "float.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\float.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 100, "lastModifiedTime": 1750140646098}}, {"hashValue": "f0164c2aa444198c92fd7d2c03050a97", "name": "string.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 570, "lastModifiedTime": 1750209981815}}]}, {"hashValue": "873febe6f91bcfb0acc2957a6268dba3", "name": "media", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ffd640e8a1e49aa1f6e31c3141412f90", "name": "app_icon.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5247, "lastModifiedTime": 1750209677825}}, {"hashValue": "0a90e50c6e19b5879caa848d8917df7c", "name": "background.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750140646124}}, {"hashValue": "b522112fceb1ff1ec46ee6dc21354f7c", "name": "foreground.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750140646141}}, {"hashValue": "9615b0f7827d0626b8548c3c68946cd1", "name": "ic_add.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_add.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2421, "lastModifiedTime": 1750211852881}}, {"hashValue": "36c1d3b5fcf5574874fe9e90b8b76ed1", "name": "ic_arrow_right.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_arrow_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2398, "lastModifiedTime": *************}}, {"hashValue": "9ae925f89e25f11debd6119163a72935", "name": "ic_back.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_back.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2649, "lastModifiedTime": *************}}, {"hashValue": "0de05ca4985fc2a2621c3f750e1c8d3e", "name": "ic_bank_card.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_bank_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3170, "lastModifiedTime": *************}}, {"hashValue": "5b9389aa4f9b992ce76835dd3d313047", "name": "ic_card.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2497, "lastModifiedTime": *************}}, {"hashValue": "9aa1c66326f376fc7867675318f15abf", "name": "ic_empty_card.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_empty_card.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3072, "lastModifiedTime": *************}}, {"hashValue": "e2e5c96813df13770e053585d09660e7", "name": "ic_profile.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_profile.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4146, "lastModifiedTime": *************}}, {"hashValue": "948e7b708a6c765b306cd7a3aa6ac17f", "name": "ic_recharge.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_recharge.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4377, "lastModifiedTime": 1750211680067}}, {"hashValue": "e4cbc8d8a5f2a4324585c327d4f1adf2", "name": "ic_settings.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_settings.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5233, "lastModifiedTime": 1750211649869}}, {"hashValue": "964bd9acd6c4498374897aef0ab35c92", "name": "ic_transaction.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_transaction.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4378, "lastModifiedTime": 1750211815475}}, {"hashValue": "cb315e4c456888a42a72c51ec660dcda", "name": "ic_transfer.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_transfer.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2261, "lastModifiedTime": 1750212194626}}, {"hashValue": "3245f8600b625faa013a82050abd4b7e", "name": "ic_wallet.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_wallet.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3578, "lastModifiedTime": 1750211776680}}, {"hashValue": "dafef4680b150d69c64473c416d5711b", "name": "ic_withdraw.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\ic_withdraw.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4027, "lastModifiedTime": 1750211696378}}, {"hashValue": "edf1ac45fbca0d134ada8c8c1087d38b", "name": "layered_image.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750140646157}}, {"hashValue": "3206f4b053137849137d330b02549fe6", "name": "startIcon.png", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\media\\startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750140646190}}]}, {"hashValue": "93886f22bf2b9435cee6bb90f06dbe86", "name": "profile", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "0fa30755b804d6cbee77ee403eaf52e5", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 523, "lastModifiedTime": 1750862172779}}]}]}, {"hashValue": "7c8379239da2c6592b5bdf8bcd245cb3", "name": "dark", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\dark", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "da22853fc3d1479ade71d3f28db2e8ac", "name": "element", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\dark\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2d96dc1178019b924854b12ebb966231", "name": "color.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\dark\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 105, "lastModifiedTime": 1750140646310}}]}]}, {"hashValue": "", "name": "rawfile", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\rawfile", "type": "directory", "isSymbolicLink": false, "children": []}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default": {"hashValue": "29f036533cf6cf109a406defb09448a3", "name": "default", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "792621d9839719af81355dff012f52a2", "name": "app_compiled", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "07725787e0ec8d3089bae043ad705389", "name": "14313738803726136247", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\14313738803726136247", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 182, "lastModifiedTime": 1750904598655}}, {"hashValue": "c2dfcf88d8214d6b4ca31e4f338ee656", "name": "5208698531366892702", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\5208698531366892702", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 170, "lastModifiedTime": 1750904598654}}, {"hashValue": "e8131811904488ed3861720de45e0aed", "name": "5519234272796725947", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\5519234272796725947", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 170, "lastModifiedTime": 1750904598653}}, {"hashValue": "682de8b7602fe3cbae4f753f7ca225b6", "name": "6888221184290573101", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\6888221184290573101", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 106, "lastModifiedTime": 1750904598650}}]}, {"hashValue": "7fd3a79e02e7810c6509aba838ed2ef4", "name": "ids_map", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ea8632973960febe12982c2bd81370e7", "name": "id_defined.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3365, "lastModifiedTime": 1750904599071}}]}, {"hashValue": "e6c2afef0eda31daf2ad6ee42a9737b9", "name": "mock-config.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\mock-config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2, "lastModifiedTime": 1750748473996}}, {"hashValue": "d44f20744f5d53bd1f2710e168bb357b", "name": "module_compiled", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e27a023f460c3a5e29fbe236ec060067", "name": "10155173318796291048", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10155173318796291048", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 179, "lastModifiedTime": 1750904598775}}, {"hashValue": "a46810254593f6d7f714dcc3db5830fe", "name": "10573305396831069031", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10573305396831069031", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 176, "lastModifiedTime": 1750904598745}}, {"hashValue": "0419a6f1568f35989043f2c8a2f1ed5a", "name": "1059596538993773055", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1059596538993773055", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1960, "lastModifiedTime": 1750904598742}}, {"hashValue": "ae44754ecab67f6567964dd503b9b1d2", "name": "11535843107945332162", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11535843107945332162", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 182, "lastModifiedTime": 1750904598746}}, {"hashValue": "fd3808553d3baa063b77bd1402973048", "name": "11987588426876728347", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11987588426876728347", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 119, "lastModifiedTime": 1750904598743}}, {"hashValue": "a8051d4e4207f9b2b20f2b9a463bc907", "name": "13044827658901311901", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13044827658901311901", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 170, "lastModifiedTime": 1750904598747}}, {"hashValue": "e87aff9d243aea3583c9d298f2d9a7c3", "name": "1419940438123835170", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1419940438123835170", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 194, "lastModifiedTime": 1750904598777}}, {"hashValue": "7edc5fc8b79709df9a8a4bdfcc4919d0", "name": "14854721179249859686", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14854721179249859686", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 191, "lastModifiedTime": 1750904598748}}, {"hashValue": "594516c46a080edad33b7f61db464708", "name": "15696879621082164891", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15696879621082164891", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 134, "lastModifiedTime": 1750904598783}}, {"hashValue": "2d138fe1a0f48384ab0f14452dba1b55", "name": "16026393350230392090", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16026393350230392090", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 185, "lastModifiedTime": 1750904598749}}, {"hashValue": "d184d2dbfcfa3d413e8da955b80404f7", "name": "16156685455898414318", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16156685455898414318", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 189, "lastModifiedTime": 1750904598781}}, {"hashValue": "717b82a4265c15d48e1f02fd9b29e990", "name": "16815063392308634730", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16815063392308634730", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 194, "lastModifiedTime": 1750904598764}}, {"hashValue": "c525eadc43b3a91a598f51409e9c8c6f", "name": "17940939251724784133", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\17940939251724784133", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 173, "lastModifiedTime": 1750904598748}}, {"hashValue": "********************************", "name": "2288225003065155795", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\2288225003065155795", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 188, "lastModifiedTime": 1750904598748}}, {"hashValue": "2c5c7569b0a55dd7fd0944eb179d5b43", "name": "4657871382367435850", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4657871382367435850", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 198, "lastModifiedTime": 1750904598780}}, {"hashValue": "3f26a6889581ac66e1b0e6dee4967f48", "name": "5925503086895065022", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5925503086895065022", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 194, "lastModifiedTime": 1750904598747}}, {"hashValue": "0bbbf7edcf307a0c97a6fcdb295e718f", "name": "6310693927697278264", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6310693927697278264", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 185, "lastModifiedTime": 1750904598774}}, {"hashValue": "7dd78b7c775a673d9f8a6e19f622198e", "name": "6451743672581054173", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6451743672581054173", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 173, "lastModifiedTime": 1750904598747}}, {"hashValue": "4d0a6aa6ecba716f0e127abee30329e9", "name": "7141810853738255148", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7141810853738255148", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 185, "lastModifiedTime": 1750904598776}}, {"hashValue": "6fec1ce4b3b202c0dd780a52449ebd58", "name": "8039879029102770823", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8039879029102770823", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 182, "lastModifiedTime": 1750904598745}}, {"hashValue": "28dcd7b9353c06de065ce9f9b99bea9a", "name": "833600868796640338", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\833600868796640338", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 838, "lastModifiedTime": 1750904598744}}, {"hashValue": "b4f7bbb424045442f6acd07e101eb3f7", "name": "8406138580903408602", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8406138580903408602", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 185, "lastModifiedTime": 1750904598749}}, {"hashValue": "f8c9b0aa5af3c47fd56389f71765ce06", "name": "8549205034635708545", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8549205034635708545", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 179, "lastModifiedTime": 1750904598778}}, {"hashValue": "a25cb83cccb68f04fab77b3eac3deaa8", "name": "9415492319005012351", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9415492319005012351", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 182, "lastModifiedTime": 1750904598748}}]}, {"hashValue": "e4d7c7adfcc2da118cf1221d82695242", "name": "module.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1854, "lastModifiedTime": 1750904599173}}, {"hashValue": "b937f1ec54f461ccc3f181a2275f7132", "name": "resources", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b60c6fdab95682a6e8048c292abdc3fc", "name": "base", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "********************************", "name": "media", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "139481ab213877c7df4148f47d80255b", "name": "layered_image.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\media\\layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1750904599070}}]}, {"hashValue": "a3627da62a9d5e89a7547fc7544869ce", "name": "profile", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "********************************", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 425, "lastModifiedTime": 1750904599106}}]}]}]}, {"hashValue": "ec64a2f469b4c0a2eed3885455296464", "name": "resources.index", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3637, "lastModifiedTime": 1750904599071}}, {"hashValue": "3c3f603a22f8abb5b7b35f148fd1381d", "name": "ResourceTable.txt", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1325, "lastModifiedTime": *************}}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default": {"hashValue": "ac64a29371ea4b682637b1016313671c", "name": "default", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c3597d757c73eaf30402bb582008f820", "name": "ResourceTable.h", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2779, "lastModifiedTime": *************}}]}, "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile": {"hashValue": "93886f22bf2b9435cee6bb90f06dbe86", "name": "profile", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "0fa30755b804d6cbee77ee403eaf52e5", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 523, "lastModifiedTime": 1750862172779}}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "a3627da62a9d5e89a7547fc7544869ce", "name": "profile", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1ce8b6393e056b44bd83668e22dcc7a8", "name": "backup_config.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\backup_config.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 36, "lastModifiedTime": 1750140646367}}, {"hashValue": "********************************", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 425, "lastModifiedTime": 1750904599106}}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json": {"hashValue": "e4d7c7adfcc2da118cf1221d82695242", "name": "module.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1854, "lastModifiedTime": 1750904599173}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json": {"hashValue": "********************************", "name": "main_pages.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 425, "lastModifiedTime": 1750904599106}}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default": {"hashValue": "50c080ce70d893b0435a009ae981d5ed", "name": "default", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bc7b67707dd5770d834c21831afbc2dd", "name": "loader.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 779, "lastModifiedTime": 1750843701728}}, {"hashValue": "dbddfdce2fcf8cdffa2cb3c256479126", "name": "pkgContextInfo.json", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 444, "lastModifiedTime": 1750748452073}}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "3c3f603a22f8abb5b7b35f148fd1381d", "name": "ResourceTable.txt", "path": "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1325, "lastModifiedTime": *************}}, "F:\\e-wallet\\harmony\\entry\\src\\main\\ets": {"hashValue": "feeaba2d175c4bc9d936781fd3571bb0", "name": "ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bdbc298adc6806f3f6975723d5a15ddb", "name": "api", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\api", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "751a31ae95bff93defb13a23cf6ac375", "name": "BankCardApi.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\api\\BankCardApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2009, "lastModifiedTime": *************}}, {"hashValue": "3ee407f6738cde7136c0d840ce698831", "name": "TransactionApi.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\api\\TransactionApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1602, "lastModifiedTime": *************}}, {"hashValue": "f87d511ea87fed57e4363ef8711a9c4a", "name": "UserApi.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\api\\UserApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1282, "lastModifiedTime": *************}}, {"hashValue": "e16022909aaea446818414847d4f065d", "name": "WalletApi.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\api\\WalletApi.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 983, "lastModifiedTime": *************}}]}, {"hashValue": "23c2e6f015eaeabc6586326f6ffe2c0d", "name": "common", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0dac021b6a294ad0ee3b02a7a8638262", "name": "http", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\http", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6b053e95488a45641940726fa40dee52", "name": "HttpClient.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\http\\HttpClient.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7245, "lastModifiedTime": 1750641766367}}]}, {"hashValue": "3faa76baaa70a8ae1c04881d0c76e0e6", "name": "storage", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\storage", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "62c4f4c255328beda7f647d5179018ad", "name": "StorageManager.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\storage\\StorageManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6317, "lastModifiedTime": 1750217692561}}, {"hashValue": "cb9c0de769ace789096a7274c82c15c7", "name": "TempDataManager.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\storage\\TempDataManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1803, "lastModifiedTime": 1750770500383}}]}, {"hashValue": "b87cc48da0a3e157488442cf6a14ccfe", "name": "types", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\types", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "d974beb58c431b4062191a1317da9fce", "name": "index.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\types\\index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7714, "lastModifiedTime": 1750827766213}}, {"hashValue": "66f8c84a262d5fe6ac23a31e9c33eea4", "name": "PageResult.ts", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\types\\PageResult.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 125, "lastModifiedTime": 1750904001254}}, {"hashValue": "b9992f516079d300ad99cb4066ca23f7", "name": "Transaction.ts", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\types\\Transaction.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 437, "lastModifiedTime": 1750903994011}}]}, {"hashValue": "f65eba83963ba6f86b8a439cf69421e9", "name": "utils", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "695ff7cf1499a22ceadc44a7290ab1d8", "name": "DataFormatter.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\DataFormatter.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8344, "lastModifiedTime": 1750216629200}}, {"hashValue": "6cde28fad176f4bdda92d10778b27e5e", "name": "ErrorHandler.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\ErrorHandler.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6722, "lastModifiedTime": 1750217368712}}, {"hashValue": "86edd24bd76f30b8dc3d3b50a0204971", "name": "EventManager.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\EventManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1640, "lastModifiedTime": 1750219504452}}, {"hashValue": "d90d247aab9ca1b9a9a325be836c66d3", "name": "FormValidator.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\FormValidator.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6180, "lastModifiedTime": 1750215419458}}, {"hashValue": "04e120d55f21f0326e4091936b2a3372", "name": "LoadingManager.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\common\\utils\\LoadingManager.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4755, "lastModifiedTime": 1750216579049}}]}]}, {"hashValue": "99666a08add9cb0c69ae6f1254514a35", "name": "entryability", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bfdeda62649bc65b5b680899a6d1f2f9", "name": "EntryAbility.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2105, "lastModifiedTime": 1750210855553}}]}, {"hashValue": "fd24ce9eef2afd00b9a7028484909117", "name": "entrybackupability", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "********************************", "name": "EntryBackupAbility.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 522, "lastModifiedTime": *************}}]}, {"hashValue": "22a75ebaefc754eb17dad10e7c5eede6", "name": "pages", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f8e6aa81cf605b82c683d461254d8483", "name": "AddBankCardPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13016, "lastModifiedTime": *************}}, {"hashValue": "f8f6e70ffa70f9c47da363a5229bc6ee", "name": "BankCardDetailPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8663, "lastModifiedTime": *************}}, {"hashValue": "910663cc696c7f582c6659a470e974b6", "name": "BankCardPaymentPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPaymentPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8267, "lastModifiedTime": *************}}, {"hashValue": "8c4596aa5ff953fa98bfce5dd8a73a94", "name": "EditBankCardPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\EditBankCardPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9356, "lastModifiedTime": *************}}, {"hashValue": "cfed808dabfe2e205ca1864d1cded2d7", "name": "Index.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2800, "lastModifiedTime": *************}}, {"hashValue": "0547f852a83c8c1a6f9a01b50d084562", "name": "LoginPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10834, "lastModifiedTime": *************}}, {"hashValue": "b6537139c1ae707eb21898ea4eca331b", "name": "MyBankCardPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\MyBankCardPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20346, "lastModifiedTime": *************}}, {"hashValue": "c6f82045f0a8082839b7eb045d1beaa0", "name": "NFCPaymentPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\NFCPaymentPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5160, "lastModifiedTime": *************}}, {"hashValue": "284a88d96b81b04b312bd9240f253c62", "name": "PaymentCenterPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\PaymentCenterPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9746, "lastModifiedTime": *************}}, {"hashValue": "77dafa29e173393ce76ecbf889c89c15", "name": "QRCodePaymentPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\QRCodePaymentPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4750, "lastModifiedTime": *************}}, {"hashValue": "1c84ee0061b389398efefd67c48343c7", "name": "RechargePage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\RechargePage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5497, "lastModifiedTime": 1750827495489}}, {"hashValue": "776edcf876d7931fc19e52d5a3eb31c0", "name": "SettingsPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11428, "lastModifiedTime": 1750902803686}}, {"hashValue": "ecec8e43d5455eb3c0712556ada6ebaa", "name": "TestPaymentPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TestPaymentPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 882, "lastModifiedTime": 1750859892744}}, {"hashValue": "ce4ffd9324fd2bde6566c60130bae2de", "name": "TransactionListPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12751, "lastModifiedTime": 1750904937932}}, {"hashValue": "7f31f6413cafbeffbc049478be04fc00", "name": "TransferPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\TransferPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9473, "lastModifiedTime": 1750904824428}}, {"hashValue": "fef6bd4b0973d82e30ec7120c2a06ebf", "name": "WalletPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9249, "lastModifiedTime": 1750901767339}}, {"hashValue": "2defe9c391f276226db4ed5b017fec02", "name": "WalletPaymentPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletPaymentPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4883, "lastModifiedTime": 1750862079436}}, {"hashValue": "fee4b8f0a0002f8e4506ee68b5b62c77", "name": "WithdrawPage.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\pages\\WithdrawPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8445, "lastModifiedTime": *************}}]}, {"hashValue": "02f6e98581bdea1df33c9fb9cda1ac40", "name": "test", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\test", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "582bfd59d0fe55507cfaaa6acade5681", "name": "BankCardTest.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\test\\BankCardTest.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2611, "lastModifiedTime": *************}}, {"hashValue": "a5f9b5cf4d5b513c985e800cf55e146d", "name": "TypeCheck.ets", "path": "F:\\e-wallet\\harmony\\entry\\src\\main\\ets\\test\\TypeCheck.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3920, "lastModifiedTime": *************}}]}]}, "F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}}