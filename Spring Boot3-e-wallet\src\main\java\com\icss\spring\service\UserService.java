package com.icss.spring.service;

import com.icss.spring.entity.User;
import com.icss.spring.mapper.UserMapper;
import com.icss.spring.result.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
public class UserService {
    @Autowired
    private UserMapper userMapper;

    // 用户登录验证
    public User login(String username, String password) {
        // 根据用户名查询用户
        User user = userMapper.findUserByUsername(username);

        // 验证用户是否存在
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证密码是否正确
        if (!user.getPassword().equals(password)) {
            throw new ApiException("密码错误");
        }

        return user;
    }

    // 修改登录密码
    @Transactional
    public boolean updateLoginPassword(Long userId, String oldPassword, String newPassword) {
        // 先验证原密码
        User user = userMapper.findUserById(userId);
        if (user == null || !user.getPassword().equals(oldPassword)) {
            return false; // 原密码错误
        }

        // 更新密码
        int updateCount = userMapper.updateLoginPassword(userId, newPassword);
        return updateCount > 0;
    }

    // 设置支付密码
    @Transactional
    public boolean updatePaymentPassword(Long userId, String oldPassword, String newPassword) {
        // 先验证原支付密码
        int count = userMapper.verifyPaymentPassword(userId, oldPassword);
        if (count == 0) {
            return false; // 原密码错误
        }

        // 更新支付密码
        int updateCount = userMapper.updatePaymentPassword(userId, newPassword);
        return updateCount > 0;
    }

    // 验证支付密码
    public boolean verifyPaymentPassword(Long userId, String password) {
        int count = userMapper.verifyPaymentPassword(userId, password);
        return count > 0;
    }

    // 更新用户信息
    @Transactional
    public boolean updateUserInfo(User user) {
        int updateCount = userMapper.updateUserInfo(user);
        return updateCount > 0;
    }

    // 设置支付限额
    @Transactional
    public int setPaymentLimit(Long userId, BigDecimal newLimit) {
        return userMapper.updatePaymentLimit(userId, newLimit);
    }

    // 根据用户ID查询用户
    public User getUserById(Long userId) {
        return userMapper.findUserById(userId);
    }
}