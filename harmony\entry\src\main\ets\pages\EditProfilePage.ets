import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';

@Entry
@Component
struct EditProfilePage {
  @State username: string = '';
  @State email: string = '';
  @State phone: string = '';
  @State isLoading: boolean = false;

  aboutToAppear() {
    this.loadUserInfo();
  }

  async loadUserInfo() {
    try {
      // 临时使用固定数据
      this.username = '张三';
      this.email = '<EMAIL>';
      this.phone = '13800138000';
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  }

  async handleSave() {
    if (!this.username.trim()) {
      promptAction.showToast({ message: '请输入用户名' });
      return;
    }

    if (!this.email.trim()) {
      promptAction.showToast({ message: '请输入邮箱' });
      return;
    }

    if (!this.phone.trim()) {
      promptAction.showToast({ message: '请输入手机号' });
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.email)) {
      promptAction.showToast({ message: '邮箱格式不正确' });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.phone)) {
      promptAction.showToast({ message: '手机号格式不正确' });
      return;
    }

    this.isLoading = true;
    try {
      // 调用API保存用户信息
      await UserApi.updateProfile({
        username: this.username,
        email: this.email,
        phone: this.phone
      });

      promptAction.showToast({ message: '保存成功' });
      router.back();
    } catch (error) {
      console.error('保存失败:', error);
      promptAction.showToast({ message: '保存失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  handleCancel() {
    router.back();
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            this.handleCancel();
          })

        Text('修改基本资料')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button('保存')
          .fontSize(16)
          .fontColor('#007AFF')
          .backgroundColor(Color.Transparent)
          .enabled(!this.isLoading)
          .onClick(() => {
            this.handleSave();
          })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .border({ width: { bottom: 1 }, color: '#E5E7EB' })

      // 表单内容
      Column() {
        // 用户名
        Column() {
          Row() {
            Text('用户名')
              .fontSize(16)
              .fontColor('#333333')
              .width(80)

            TextInput({ placeholder: '请输入用户名', text: this.username })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .onChange((value: string) => {
                this.username = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 邮箱
        Column() {
          Row() {
            Text('邮箱')
              .fontSize(16)
              .fontColor('#333333')
              .width(80)

            TextInput({ placeholder: '请输入邮箱', text: this.email })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.Email)
              .onChange((value: string) => {
                this.email = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
        .margin({ bottom: 16 })

        // 手机号
        Column() {
          Row() {
            Text('手机')
              .fontSize(16)
              .fontColor('#333333')
              .width(80)

            TextInput({ placeholder: '请输入手机号', text: this.phone })
              .fontSize(16)
              .backgroundColor(Color.Transparent)
              .border({ width: 0 })
              .layoutWeight(1)
              .type(InputType.PhoneNumber)
              .onChange((value: string) => {
                this.phone = value;
              })
          }
          .width('100%')
          .height(50)
          .alignItems(VerticalAlign.Center)

          Divider()
            .color('#E5E7EB')
            .strokeWidth(1)
        }
        .width('100%')
      }
      .padding(20)
      .backgroundColor('#FFFFFF')
      .margin({ top: 16, left: 16, right: 16 })
      .borderRadius(12)

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
