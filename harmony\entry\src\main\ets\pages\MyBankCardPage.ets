import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { BankCard, BankCardType, BankCardStatus } from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';

@Entry
@Component
struct MyBankCardPage {
  @State cardList: BankCard[] = [];
  @State isLoading: boolean = true;

  aboutToAppear() {
    console.log('MyBankCardPage - aboutToAppear 开始');
    // 直接加载测试数据，确保界面有内容显示
    this.addTestData();
    this.isLoading = false;
    console.log('MyBankCardPage - aboutToAppear 完成，银行卡数量:', this.cardList.length);
    console.log('MyBankCardPage - 银行卡数据:', JSON.stringify(this.cardList));
  }

  onPageShow() {
    console.log('MyBankCardPage onPageShow - 页面显示');

    // 检查是否有银行卡添加事件
    const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
    console.log('MyBankCardPage onPageShow - 检查银行卡添加事件:', cardAdded);
    if (cardAdded) {
      console.log('MyBankCardPage - 检测到银行卡添加，重新加载银行卡列表');

      // 检查是否有新添加的银行卡数据
      const newCard = tempDataManager.getData('NEW_BANK_CARD') as BankCard;
      if (newCard) {
        console.log('MyBankCardPage - 发现新添加的银行卡:', JSON.stringify(newCard));
        // 将新卡添加到当前列表
        this.cardList.push(newCard);
        // 清除临时数据
        tempDataManager.setData('NEW_BANK_CARD', null);
      }

      // 重新加载银行卡列表
      this.loadBankCards();
      // 清除添加事件标志
      tempDataManager.setData('BANK_CARD_ADDED', false);
    }

    // 检查是否有银行卡更新事件
    const cardUpdated = tempDataManager.getData('BANK_CARD_UPDATED');
    console.log('MyBankCardPage onPageShow - 检查银行卡更新事件:', cardUpdated);
    if (cardUpdated) {
      console.log('MyBankCardPage - 检测到银行卡更新，重新加载银行卡列表');

      // 检查是否有更新的银行卡数据
      const updatedCard = tempDataManager.getData('UPDATED_BANK_CARD') as BankCard;
      if (updatedCard) {
        console.log('MyBankCardPage - 发现更新的银行卡:', JSON.stringify(updatedCard));
        // 在当前列表中更新对应的银行卡
        const cardIndex = this.cardList.findIndex(card => card.cardId === updatedCard.cardId);
        if (cardIndex !== -1) {
          this.cardList[cardIndex] = updatedCard;
          console.log('MyBankCardPage - 银行卡列表已更新');
        }
        // 清除临时数据
        tempDataManager.setData('UPDATED_BANK_CARD', null);
      }

      // 重新加载银行卡列表
      this.loadBankCards();
      // 清除更新事件标志
      tempDataManager.setData('BANK_CARD_UPDATED', false);
    }

    // 检查是否有银行卡解绑事件
    const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
    console.log('MyBankCardPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
    if (cardUnbound) {
      console.log('MyBankCardPage - 检测到银行卡解绑，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 清除解绑事件标志
      tempDataManager.setData('BANK_CARD_UNBOUND', false);
    }

    // 检查是否有银行卡删除事件
    const cardDeleted = tempDataManager.getData('BANK_CARD_DELETED');
    console.log('MyBankCardPage onPageShow - 检查银行卡删除事件:', cardDeleted);
    if (cardDeleted) {
      console.log('MyBankCardPage - 检测到银行卡删除，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 清除删除事件标志
      tempDataManager.setData('BANK_CARD_DELETED', false);
    }
  }

  async loadBankCards() {
    try {
      this.isLoading = true;
      console.log('开始加载银行卡列表...');

      // 先加载测试数据
      this.addTestData();

      try {
        // 尝试从API获取银行卡
        const apiCards = await BankCardApi.getCardList();
        console.log('API银行卡列表加载成功，数量:', apiCards.length);
        if (apiCards.length > 0) {
          this.cardList = apiCards;
        }
      } catch (apiError) {
        console.log('API调用失败，使用本地数据:', apiError);
      }

      // 加载本地存储的银行卡
      const localCards = (tempDataManager.getData('LOCAL_BANK_CARDS') || []) as BankCard[];
      console.log('本地银行卡数量:', localCards.length);

      if (localCards.length > 0) {
        // 合并本地银行卡到列表中（避免重复）
        localCards.forEach((localCard: BankCard) => {
          const exists = this.cardList.find(card => card.cardId === localCard.cardId);
          if (!exists) {
            this.cardList.push(localCard);
          }
        });
      }

      console.log('最终银行卡列表数量:', this.cardList.length);
      console.log('银行卡数据:', JSON.stringify(this.cardList));
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
      console.log('使用测试数据');
      this.addTestData();
      promptAction.showToast({ message: '加载银行卡列表失败，显示测试数据' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 添加测试数据
   */
  addTestData() {
    this.cardList = [
      {
        cardId: 1,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.DEBIT,
        bankName: '工商银行',
        holderName: '张三',
        isBound: BankCardStatus.BOUND,
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 10:00:00'
      },
      {
        cardId: 2,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.DEBIT,
        bankName: '中国银行',
        holderName: '张三',
        isBound: BankCardStatus.BOUND,
        createTime: '2024-01-02 10:00:00',
        updateTime: '2024-01-02 10:00:00'
      },
      {
        cardId: 3,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.CREDIT,
        bankName: '平安银行',
        holderName: '张三',
        isBound: BankCardStatus.UNBOUND,
        createTime: '2024-01-03 10:00:00',
        updateTime: '2024-01-03 10:00:00'
      },
      {
        cardId: 4,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.DEBIT,
        bankName: '工商银行',
        holderName: '张三',
        isBound: BankCardStatus.UNBOUND,
        createTime: '2024-01-04 10:00:00',
        updateTime: '2024-01-04 10:00:00'
      },
      {
        cardId: 5,
        userId: 1,
        cardNo: '****************',
        cardType: BankCardType.CREDIT,
        bankName: '农业银行',
        holderName: '张三',
        isBound: BankCardStatus.BOUND,
        createTime: '2024-01-05 10:00:00',
        updateTime: '2024-01-05 10:00:00'
      }
    ];
    console.log('测试数据添加完成，银行卡数量:', this.cardList.length);
  }

  build() {
    Column() {
      // 修改后的顶部导航栏（淡粉色卡片式）
      Column() {
        Row() {
          Text('我的银行卡')
            .fontSize(18)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
          Button('+')
            .fontSize(28)
            .fontColor('#FFFFFF')
            .backgroundColor('#FF4444')
            .borderRadius(25)
            .width(50)
            .height(50)
            .margin({ left: 8 })
            .onClick(() => {
              router.pushUrl({ url: 'pages/AddBankCardPage' });
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      }
      .width('90%')
      .margin({ top: 20 })
      .borderRadius(16)
      .backgroundColor('#ff3785f5')
      .shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 })
      // 主要内容区域
      if (this.isLoading) {
        this.LoadingView()
      } else {
        this.CardListView()
      }

      // 底部导航栏
      this.BottomNavigation()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color('#1976D2')

      Text('加载中...')
        .fontSize(14)
        .fontColor('#999999')
        .margin({ top: 16 })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  CardListView() {
    if (this.cardList.length === 0) {
      // 空状态提示
      Column() {
        Text('暂无银行卡')
          .fontSize(16)
          .fontColor('#999999')
          .margin({ bottom: 8 })

        Text('点击右上角"+"添加银行卡')
          .fontSize(14)
          .fontColor('#CCCCCC')
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      // 银行卡列表
      List({ space: 0 }) {
        ForEach(this.cardList, (card: BankCard, index: number) => {
          ListItem() {
            this.BankCardItem(card, index)
          }
          .swipeAction({ end: this.SwipeActionBuilder(card) })
        })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
      .padding({ left: 16, right: 16, top: 16, bottom: 20 })
    }
  }

  @Builder
  BankCardItem(card: BankCard, index: number) {
    // 银行卡主体内容
    Column() {
      // 顶部：银行名称和卡片类型
      Row() {
        Text(card.bankName)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)

        // 银行卡图标
        Row() {
          Image($r('app.media.ic_bank_card'))
            .width(24)
            .height(24)
            .fillColor('#FFFFFF')
        }
        .width(40)
        .height(24)
        .justifyContent(FlexAlign.Center)
        .backgroundColor('rgba(255, 255, 255, 0.2)')
        .borderRadius(4)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 中间：卡片类型
      Text(card.cardType === BankCardType.DEBIT ? '储蓄卡' : '信用卡')
        .fontSize(12)
        .fontColor('rgba(255, 255, 255, 0.8)')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      // 银行卡号
      Text(`**** **** **** ${card.cardNo.slice(-4)}`)
        .fontSize(18)
        .fontColor('#FFFFFF')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      // 底部：持卡人姓名和绑定状态
      Row() {
        Text(card.holderName)
          .fontSize(14)
          .fontColor('rgba(255, 255, 255, 0.9)')
          .layoutWeight(1)

        // 绑定状态
        Text(card.isBound === BankCardStatus.BOUND ? '已绑定' : '未绑定')
          .fontSize(12)
          .fontColor('#FFFFFF')
          .backgroundColor(card.isBound === BankCardStatus.BOUND ? 'rgba(76, 175, 80, 0.8)' : 'rgba(255, 152, 0, 0.8)')
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .borderRadius(4)
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .linearGradient(this.getCardGradient(index))
    .borderRadius(12)
    .margin({ bottom: 12 })
    .shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetX: 0, offsetY: 2 })
    .gesture(
      // 双击手势 - 查看详情
      TapGesture({ count: 2 })
        .onAction(() => {
          router.pushUrl({
            url: 'pages/BankCardDetailPage',
            params: { cardId: card.cardId }
          });
        })
    )
    .gesture(
      // 单击手势 - 防止与双击冲突
      TapGesture({ count: 1 })
        .onAction(() => {
          // 单击暂时不做处理，避免与双击冲突
        })
    )
  }

  @Builder
  SwipeActionBuilder(card: BankCard) {
    Row() {
      // 修改按钮 - 跳转到银行卡编辑页面
      Button('修改')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor('#FF9800')
        .borderRadius(8)
        .width(60)
        .height(40)
        .onClick(() => {
          console.log('点击修改按钮，银行卡ID:', card.cardId);
          router.pushUrl({
            url: 'pages/EditBankCardPage',
            params: {
              cardId: card.cardId,
              editMode: true,
              cardData: card
            }
          }).then(() => {
            console.log('跳转到编辑页面成功');
          }).catch((error: Error) => {
            console.error('跳转到编辑页面失败:', error.message);
            promptAction.showToast({ message: '跳转失败，请重试' });
          });
        })

      // 删除按钮
      Button('删除')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor('#F44336')
        .borderRadius(8)
        .width(60)
        .height(40)
        .margin({ left: 8 })
        .onClick(() => {
          this.confirmDeleteCard(card);
        })

      // 解绑按钮
      Button('解绑')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor('#9E9E9E')
        .borderRadius(8)
        .width(60)
        .height(40)
        .margin({ left: 8 })
        .onClick(() => {
          this.confirmUnbindCard(card);
        })
    }
    .padding({ left: 16, right: 16 })
  }

  /**
   * 获取银行卡渐变色
   */
  getCardGradient(index: number): LinearGradient {
    const gradients: LinearGradient[] = [
      // 蓝色渐变
      {
        angle: 135,
        colors: [['#667eea', 0], ['#764ba2', 1]]
      },
      // 紫色渐变
      {
        angle: 135,
        colors: [['#f093fb', 0], ['#f5576c', 1]]
      },
      // 绿色渐变
      {
        angle: 135,
        colors: [['#4facfe', 0], ['#00f2fe', 1]]
      },
      // 橙色渐变
      {
        angle: 135,
        colors: [['#ff9a9e', 0], ['#fecfef', 1]]
      },
      // 深蓝渐变
      {
        angle: 135,
        colors: [['#a8edea', 0], ['#fed6e3', 1]]
      }
    ];

    return gradients[index % gradients.length];
  }

  /**
   * 确认删除银行卡
   */
  async confirmDeleteCard(card: BankCard) {
    try {
      const result = await promptAction.showDialog({
        title: '确认删除',
        message: `确定要删除银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？\n删除后无法恢复！`,
        buttons: [
          { text: '确定删除', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.deleteCard(card);
      }
    } catch (error) {
      console.error('显示删除确认对话框失败:', error);
    }
  }

  /**
   * 删除银行卡
   */
  async deleteCard(card: BankCard) {
    try {
      console.log('开始删除银行卡:', card.cardId);

      // 从本地列表中删除
      const cardIndex = this.cardList.findIndex(c => c.cardId === card.cardId);
      if (cardIndex !== -1) {
        this.cardList.splice(cardIndex, 1);
        console.log('从本地列表删除成功，剩余银行卡数量:', this.cardList.length);
      }

      // 从本地存储中删除
      const localCards = (tempDataManager.getData('LOCAL_BANK_CARDS') || []) as BankCard[];
      const localIndex = localCards.findIndex(c => c.cardId === card.cardId);
      if (localIndex !== -1) {
        localCards.splice(localIndex, 1);
        tempDataManager.setData('LOCAL_BANK_CARDS', localCards);
        console.log('从本地存储删除成功');
      }

      // 尝试调用API删除（如果API可用）
      try {
        await BankCardApi.deleteCard(card.cardId);
        console.log('API删除成功');
      } catch (apiError) {
        console.log('API删除失败，但本地删除已完成:', apiError);
      }

      promptAction.showToast({ message: '删除成功' });

      // 通知其他页面银行卡已删除
      tempDataManager.setData('BANK_CARD_DELETED', true);

    } catch (error) {
      console.error('删除银行卡失败:', error);
      promptAction.showToast({ message: '删除失败，请重试' });
    }
  }

  /**
   * 确认解绑银行卡
   */
  async confirmUnbindCard(card: BankCard) {
    try {
      const result = await promptAction.showDialog({
        title: '确认解绑',
        message: `确定要解绑银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
        buttons: [
          { text: '确定', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.unbindCard(card);
      }
    } catch (error) {
      console.error('显示确认对话框失败:', error);
    }
  }

  /**
   * 解绑银行卡
   */
  async unbindCard(card: BankCard) {
    try {
      await BankCardApi.unbindCard(card.cardId);
      promptAction.showToast({ message: '解绑成功' });

      // 通知其他页面银行卡已解绑
      console.log('MyBankCardPage - 设置银行卡解绑事件标志');
      tempDataManager.setData('BANK_CARD_UNBOUND', true);

      // 重新加载银行卡列表
      await this.loadBankCards();
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      promptAction.showToast({ message: '解绑失败，请重试' });
    }
  }

  @Builder
  BottomNavigation() {
    Row() {
      // 银行卡
      Column() {
        Text('💳')
          .fontSize(20)
        Text('银行卡')
          .fontSize(12)
          .fontColor('#1976D2')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        // 当前页面，不需要跳转
      })

      // 交易
      Column() {
        Text('📊')
          .fontSize(20)
        Text('交易')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/TransferPage'
        });
      })

      // 钱包
      Column() {
        Text('👛')
          .fontSize(20)
        Text('钱包')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/WalletPage'
        });
      })

      // 支付中心
      Column() {
        Text('💰')
          .fontSize(20)
        Text('支付中心')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        console.log('底部导航：点击支付中心按钮');
        promptAction.showToast({
          message: '正在跳转到支付中心...',
          duration: 1000
        });
        router.pushUrl({
          url: 'pages/PaymentCenterPage'
        }).then(() => {
          console.log('底部导航：跳转支付中心成功');
        }).catch((error: Error) => {
          console.error('底部导航：跳转支付中心失败:', error.message);
          promptAction.showToast({
            message: '跳转失败: ' + error.message,
            duration: 3000
          });
        });
      })

      // 我的
      Column() {
        Text('👤')
          .fontSize(20)
        Text('我的')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/SettingsPage'
        });
      })
    }
    .width('100%')
    .height(60)
    .backgroundColor('#FFFFFF')
    .border({ width: { top: 1 }, color: '#E0E0E0' })
  }
}
