import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { UserInfo, UserLoginRequest, UserLoginResponse, UserRegisterRequest, UpdatePayPasswordRequest, UpdatePayLimitRequest, UserLoginFormData } from '../common/types/index';
// 新增接口定义
export interface UpdateProfileRequest {
    username: string;
    email: string;
    phone: string;
}
export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}
export interface ChangePayPasswordRequest {
    currentPayPassword: string;
    newPayPassword: string;
}
/**
 * 用户相关API接口
 */
export class UserApi {
    /**
     * 用户登录
     */
    static async login(loginData: UserLoginRequest): Promise<UserLoginResponse> {
        // Spring Boot使用form参数而不是JSON body
        const formData: UserLoginFormData = {
            username: loginData.username,
            password: loginData.password,
            captcha: loginData.captcha
        };
        const response = await httpClient.postForm<UserInfo>('/api/user/login', formData);
        const loginResponse: UserLoginResponse = {
            token: 'mock-token',
            userInfo: response.data
        };
        return loginResponse;
    }
    /**
     * 获取验证码
     */
    static async getCaptcha(): Promise<string> {
        const response = await httpClient.get<string>('/api/user/captcha');
        return response.data;
    }
    /**
     * 用户注册
     */
    static async register(registerData: UserRegisterRequest): Promise<void> {
        await httpClient.post<void>('/user/register', registerData);
    }
    /**
     * 获取用户信息
     */
    static async getUserInfo(): Promise<UserInfo> {
        const response = await httpClient.get<UserInfo>('/user/info');
        return response.data;
    }
    /**
     * 修改支付密码
     */
    static async updatePayPassword(data: UpdatePayPasswordRequest): Promise<void> {
        await httpClient.put<void>('/user/pay-password', data);
    }
    /**
     * 设置支付限额
     */
    static async updatePayLimit(data: UpdatePayLimitRequest): Promise<void> {
        await httpClient.put<void>('/user/pay-limit', data);
    }
    /**
     * 修改用户基本信息
     */
    static async updateProfile(data: UpdateProfileRequest): Promise<void> {
        await httpClient.put<void>('/user/profile', data);
    }
    /**
     * 修改登录密码
     */
    static async changePassword(data: ChangePasswordRequest): Promise<void> {
        await httpClient.put<void>('/user/password', data);
    }
    /**
     * 修改支付密码
     */
    static async changePayPassword(data: ChangePayPasswordRequest): Promise<void> {
        await httpClient.put<void>('/user/pay-password-change', data);
    }
}
