import router from '@ohos.router';

@Entry
@Component
struct TestPaymentPage {
  aboutToAppear() {
    console.log('TestPaymentPage 页面加载成功');
  }

  build() {
    Column() {
      Text('✅ 路由跳转成功！')
        .fontSize(28)
        .fontWeight(FontWeight.Bold)
        .fontColor('#4CAF50')
        .margin({ bottom: 20 })

      Text('这是测试支付页面')
        .fontSize(18)
        .fontColor('#333333')
        .margin({ bottom: 40 })

      Button('返回支付中心')
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#4A90E2')
        .borderRadius(8)
        .width(150)
        .height(44)
        .onClick(() => {
          router.back();
        })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor('#F0F8FF')
  }
}
