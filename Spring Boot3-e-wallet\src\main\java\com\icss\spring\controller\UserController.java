package com.icss.spring.controller;


import com.icss.spring.entity.User;
import com.icss.spring.mapper.UserMapper;
import com.icss.spring.result.Result;
import com.icss.spring.service.UserService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Random;
@CrossOrigin
@RestController
@RequestMapping("/api/user")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    // 用户登录
    @PostMapping("/login")
    public Result login(@RequestParam("username") String username,
                        @RequestParam("password") String password,
                        @RequestParam("captcha") String captcha,
                        HttpSession session) {
        // 获取会话中的验证码
        String sessionCaptcha = (String) session.getAttribute("captcha");

        // 验证验证码
        if (sessionCaptcha == null || !sessionCaptcha.equalsIgnoreCase(captcha)) {
            return Result.error("验证码错误");
        }

        // 验证用户名和密码
        User user = userService.login(username, password);
        if (user == null) {
            return Result.error("用户名或密码错误");
        }

        // 登录成功，将用户信息存入session
        session.setAttribute("currentUser", user);

        // 返回用户信息（不包含密码）
        user.setPassword(null);
        return Result.OK("登录成功", user);
    }

    // 生成验证码
    @GetMapping("/captcha")
    public Result generateCaptcha(HttpSession session) {
        // 生成4位随机数字验证码
        Random random = new Random();
        int captchaValue = 1000 + random.nextInt(9000);
        String captcha = String.valueOf(captchaValue);

        // 将验证码存入session
        session.setAttribute("captcha", captcha);

        // 返回验证码给前端
        return Result.OK("SUCCESS", captcha);
    }

    // 设置支付密码（首次设置，不需要验证原密码）
    @PostMapping("/setPaymentPassword")
    public Result setPaymentPassword(@RequestParam("userId") Long userId,
                                     @RequestParam("newPassword") String newPassword,
                                     HttpSession session) {
        try {
            // 检查用户是否登录
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getId().equals(userId)) {
                return Result.error("用户未登录");
            }

            // 直接设置支付密码（首次设置）
            int result = userMapper.updatePaymentPassword(userId, newPassword);
            if (result > 0) {
                return Result.OK("支付密码设置成功");
            }
            return Result.error("支付密码设置失败");
        } catch (Exception e) {
            return Result.error("支付密码设置失败");
        }
    }

    // 修改支付密码
    @PostMapping("/updatePaymentPassword")
    public Result updatePaymentPassword(@RequestParam("userId") Long userId,
                                        @RequestParam("oldPassword") String oldPassword,
                                        @RequestParam("newPassword") String newPassword,
                                        HttpSession session) {
        try {
            // 检查用户是否登录
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getId().equals(userId)) {
                return Result.error("用户未登录");
            }

            // 使用新的支付密码更新方法
            boolean success = userService.updatePaymentPassword(userId, oldPassword, newPassword);
            if (success) {
                // 更新session中的用户信息
                currentUser.setPaymentPassword(newPassword);
                session.setAttribute("currentUser", currentUser);
                return Result.OK("支付密码修改成功");
            } else {
                return Result.error("原支付密码错误");
            }
        } catch (Exception e) {
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    // 修改登录密码
    @PostMapping("/updateLoginPassword")
    public Result updateLoginPassword(@RequestParam("userId") Long userId,
                                      @RequestParam("oldPassword") String oldPassword,
                                      @RequestParam("newPassword") String newPassword,
                                      HttpSession session) {
        try {
            // 检查用户是否登录
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getId().equals(userId)) {
                return Result.error("用户未登录");
            }

            // 使用新的登录密码更新方法
            boolean success = userService.updateLoginPassword(userId, oldPassword, newPassword);
            if (success) {
                // 更新session中的用户信息
                currentUser.setPassword(newPassword);
                session.setAttribute("currentUser", currentUser);
                return Result.OK("登录密码修改成功");
            } else {
                return Result.error("原密码错误");
            }
        } catch (Exception e) {
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    // 设置支付限额
    @PostMapping("/setPaymentLimit")
    public Result setPaymentLimit(@RequestParam("userId") Long userId,
                                  @RequestParam("newLimit") BigDecimal newLimit,
                                  HttpSession session) {
        // 检查用户是否登录
        User currentUser = (User) session.getAttribute("currentUser");
        if (currentUser == null || !currentUser.getId().equals(userId)) {
            return Result.error("用户未登录");
        }

        // 更新支付限额
        int result = userService.setPaymentLimit(userId, newLimit);
        if (result > 0) {
            return Result.OK("支付限额设置成功");
        }
        return Result.error("支付限额设置失败");
    }

    // 银行卡密码统一管理 - 根据选择的银行卡更新密码
    @PostMapping("/updateCardPassword")
    public Result updateCardPassword(@RequestParam("userId") Long userId,
                                     @RequestParam("cardId") Long cardId,
                                     @RequestParam("oldPassword") String oldPassword,
                                     @RequestParam("newPassword") String newPassword,
                                     HttpSession session) {
        try {
            // 检查用户是否登录
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getId().equals(userId)) {
                return Result.error("用户未登录");
            }

            // 验证旧密码（使用用户的支付密码）
            User user = userService.getUserById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            if (!oldPassword.equals(user.getPaymentPassword())) {
                return Result.error("原支付密码错误");
            }

            // 验证银行卡是否属于该用户
            // 这里可以添加银行卡验证逻辑

            // 更新用户支付密码（银行卡密码与支付密码统一管理）
            int result = userMapper.updatePaymentPassword(userId, newPassword);
            if (result > 0) {
                // 更新session中的用户信息
                user.setPaymentPassword(newPassword);
                session.setAttribute("currentUser", user);

                return Result.OK("银行卡密码修改成功，支付密码已同步更新");
            }
            return Result.error("银行卡密码修改失败");
        } catch (Exception e) {
            return Result.error("修改失败：" + e.getMessage());
        }
    }

    // 获取用户信息
    @GetMapping("/info")
    public Result getUserInfo(HttpSession session) {
        try {
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null) {
                return Result.error("用户未登录");
            }

            // 重新从数据库获取最新用户信息
            User user = userService.getUserById(currentUser.getId());
            if (user != null) {
                // 不返回密码信息
                user.setPassword(null);
                user.setPaymentPassword(null);
                return Result.OK("查询成功", user);
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    // 更新用户信息（不包括真实姓名）
    @PostMapping("/updateInfo")
    public Result updateUserInfo(@RequestParam("userId") Long userId,
                                 @RequestParam("username") String username,
                                 @RequestParam(value = "mobile", required = false) String mobile,
                                 @RequestParam(value = "email", required = false) String email,
                                 HttpSession session) {
        try {
            // 检查用户是否登录
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser == null || !currentUser.getId().equals(userId)) {
                return Result.error("用户未登录");
            }

            // 获取现有用户信息
            User existingUser = userService.getUserById(userId);
            if (existingUser == null) {
                return Result.error("用户不存在");
            }

            // 更新用户信息，保持真实姓名不变
            existingUser.setUsername(username);
            existingUser.setMobile(mobile);
            existingUser.setEmail(email);

            boolean success = userService.updateUserInfo(existingUser);
            if (success) {
                // 更新session中的用户信息
                currentUser.setUsername(username);
                currentUser.setMobile(mobile);
                currentUser.setEmail(email);
                session.setAttribute("currentUser", currentUser);
                return Result.OK("用户信息更新成功");
            } else {
                return Result.error("用户信息更新失败");
            }
        } catch (Exception e) {
            return Result.error("用户信息更新失败：" + e.getMessage());
        }
    }

    // 用户登出
    @GetMapping("/logout")
    public Result logout(HttpSession session) {
        // 清除session中的用户信息
        session.removeAttribute("currentUser");
        session.removeAttribute("captcha");
        return Result.OK("退出成功");
    }
}
