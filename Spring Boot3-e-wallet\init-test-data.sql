-- 初始化测试数据
-- 清空现有数据
DELETE FROM transaction;
DELETE FROM bank_card;
DELETE FROM user;

-- 重置自增ID
ALTER TABLE user AUTO_INCREMENT = 1;
ALTER TABLE bank_card AUTO_INCREMENT = 1;
ALTER TABLE transaction AUTO_INCREMENT = 1;

-- 插入测试用户
INSERT INTO user (username, password, real_name, phone, email, account, payment_password) VALUES
('testuser', 'password123', '测试用户', '***********', '<EMAIL>', 1000.00, 'pay123'),
('user2', 'password123', '用户二', '***********', '<EMAIL>', 500.00, 'pay123');

-- 插入测试银行卡
INSERT INTO bank_card (user_id, bank_name, card_number, card_type, balance, credit_limit, bind_status) VALUES
(1, '中国银行', '6217001234567890123', 'DEBIT', 5000.00, 0.00, 1),
(1, '建设银行', '6227001234567890124', 'DEBIT', 3000.00, 0.00, 1),
(1, '招商银行', '6225001234567890125', 'CREDIT', -500.00, 10000.00, 1),
(2, '工商银行', '6222001234567890126', 'DEBIT', 2000.00, 0.00, 1);

-- 插入一些测试交易记录
INSERT INTO transaction (user_id, transaction_type, amount, transaction_time, payment_method, payment_channel, target_account, card_id, status, description) VALUES
(1, 'DEPOSIT', 500.00, NOW(), 'BANK_CARD', 'MERCHANT', '钱包充值', 1, 'SUCCESS', '从中国银行充值到钱包'),
(1, 'WITHDRAW', 200.00, NOW(), 'BANK_CARD', 'MERCHANT', '中国银行(6217001234567890123)', 1, 'SUCCESS', '提现到中国银行'),
(1, 'PAYMENT', 100.00, NOW(), 'WALLET', 'QR_CODE', '测试商户', NULL, 'SUCCESS', '扫码支付'),
(1, 'TRANSFER', 150.00, NOW(), 'WALLET', NULL, '用户二', NULL, 'SUCCESS', '转账给用户二'),
(2, 'RECEIVE', 150.00, NOW(), 'WALLET', NULL, '来自用户1', NULL, 'SUCCESS', '收到转账');

-- 查询验证数据
SELECT '用户数据:' as info;
SELECT * FROM user;

SELECT '银行卡数据:' as info;
SELECT * FROM bank_card;

SELECT '交易记录数据:' as info;
SELECT * FROM transaction ORDER BY transaction_time DESC;
