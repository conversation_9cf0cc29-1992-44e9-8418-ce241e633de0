if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionListPage_Params {
    transactions?: Transaction[];
    isLoading?: boolean;
    isRefreshing?: boolean;
    hasMore?: boolean;
    currentPage?: number;
    showDetailDialog?: boolean;
    selectedTransaction?: Transaction | null;
    pageSize?: number;
    selectedType?: TransactionType | '';
    showFilterDialog?: boolean;
    filterStartDate?: string;
    filterEndDate?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { TransactionType, TransactionStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction, TransactionQueryParams } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class TransactionListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isRefreshing = new ObservedPropertySimplePU(false, this, "isRefreshing");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__showDetailDialog = new ObservedPropertySimplePU(false, this, "showDetailDialog");
        this.__selectedTransaction = new ObservedPropertyObjectPU(null, this, "selectedTransaction");
        this.__pageSize = new ObservedPropertySimplePU(20, this, "pageSize");
        this.__selectedType = new ObservedPropertySimplePU('', this, "selectedType");
        this.__showFilterDialog = new ObservedPropertySimplePU(false, this, "showFilterDialog");
        this.__filterStartDate = new ObservedPropertySimplePU('', this, "filterStartDate");
        this.__filterEndDate = new ObservedPropertySimplePU('', this, "filterEndDate");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionListPage_Params) {
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isRefreshing !== undefined) {
            this.isRefreshing = params.isRefreshing;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.showDetailDialog !== undefined) {
            this.showDetailDialog = params.showDetailDialog;
        }
        if (params.selectedTransaction !== undefined) {
            this.selectedTransaction = params.selectedTransaction;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.showFilterDialog !== undefined) {
            this.showFilterDialog = params.showFilterDialog;
        }
        if (params.filterStartDate !== undefined) {
            this.filterStartDate = params.filterStartDate;
        }
        if (params.filterEndDate !== undefined) {
            this.filterEndDate = params.filterEndDate;
        }
    }
    updateStateVars(params: TransactionListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isRefreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__showDetailDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTransaction.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilterDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__filterStartDate.purgeDependencyOnElmtId(rmElmtId);
        this.__filterEndDate.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isRefreshing.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__showDetailDialog.aboutToBeDeleted();
        this.__selectedTransaction.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__selectedType.aboutToBeDeleted();
        this.__showFilterDialog.aboutToBeDeleted();
        this.__filterStartDate.aboutToBeDeleted();
        this.__filterEndDate.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isRefreshing: ObservedPropertySimplePU<boolean>;
    get isRefreshing() {
        return this.__isRefreshing.get();
    }
    set isRefreshing(newValue: boolean) {
        this.__isRefreshing.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __showDetailDialog: ObservedPropertySimplePU<boolean>;
    get showDetailDialog() {
        return this.__showDetailDialog.get();
    }
    set showDetailDialog(newValue: boolean) {
        this.__showDetailDialog.set(newValue);
    }
    private __selectedTransaction: ObservedPropertyObjectPU<Transaction | null>;
    get selectedTransaction() {
        return this.__selectedTransaction.get();
    }
    set selectedTransaction(newValue: Transaction | null) {
        this.__selectedTransaction.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __selectedType: ObservedPropertySimplePU<TransactionType | ''>;
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: TransactionType | '') {
        this.__selectedType.set(newValue);
    }
    private __showFilterDialog: ObservedPropertySimplePU<boolean>;
    get showFilterDialog() {
        return this.__showFilterDialog.get();
    }
    set showFilterDialog(newValue: boolean) {
        this.__showFilterDialog.set(newValue);
    }
    // 筛选条件
    private __filterStartDate: ObservedPropertySimplePU<string>;
    get filterStartDate() {
        return this.__filterStartDate.get();
    }
    set filterStartDate(newValue: string) {
        this.__filterStartDate.set(newValue);
    }
    private __filterEndDate: ObservedPropertySimplePU<string>;
    get filterEndDate() {
        return this.__filterEndDate.get();
    }
    set filterEndDate(newValue: string) {
        this.__filterEndDate.set(newValue);
    }
    aboutToAppear() {
        this.loadTransactions(true);
    }
    onPageShow() {
        // 每次显示页面时刷新交易记录
        this.loadTransactions(true);
    }
    async loadTransactions(refresh: boolean = false) {
        if (this.isLoading)
            return;
        this.isLoading = true;
        if (refresh) {
            this.isRefreshing = true;
            this.currentPage = 1;
            this.hasMore = true;
        }
        try {
            const params: TransactionQueryParams = {
                page: this.currentPage.toString(),
                size: this.pageSize.toString(),
                type: this.selectedType || undefined,
                startDate: this.filterStartDate,
                endDate: this.filterEndDate
            };
            const userId = 1; // 临时使用固定用户ID
            const result: Transaction[] = await TransactionApi.getTransactionList(userId);
            if (refresh) {
                this.transactions = result;
            }
            else {
                this.transactions = [...this.transactions, ...result];
            }
            this.hasMore = false; // 简化分页逻辑，暂时不支持分页
            this.currentPage++;
        }
        catch (error) {
            console.error('加载交易记录失败:', error);
            promptAction.showToast({ message: '加载交易记录失败' });
        }
        finally {
            this.isLoading = false;
            this.isRefreshing = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(80:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏 - 更美观的设计
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(82:7)", "entry");
            // 顶部导航栏 - 更美观的设计
            Column.width('94%');
            // 顶部导航栏 - 更美观的设计
            Column.margin({ top: 20, bottom: 8 });
            // 顶部导航栏 - 更美观的设计
            Column.borderRadius(24);
            // 顶部导航栏 - 更美观的设计
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: [['#667EEA', 0.0], ['#764BA2', 1.0]]
            });
            // 顶部导航栏 - 更美观的设计
            Column.shadow({
                radius: 16,
                color: '#667EEA40',
                offsetX: 0,
                offsetY: 6
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(83:9)", "entry");
            Row.width('100%');
            Row.height(70);
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('全部交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(84:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.letterSpacing(1);
        }, Text);
        Text.pop();
        Row.pop();
        // 顶部导航栏 - 更美观的设计
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型筛选
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(110:7)", "entry");
            // 交易类型筛选
            Scroll.scrollable(ScrollDirection.Horizontal);
            // 交易类型筛选
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(111:9)", "entry");
            Row.padding(12);
        }, Row);
        this.TypeFilterButton.bind(this)('全部', '');
        this.TypeFilterButton.bind(this)('支付', TransactionType.PAYMENT);
        this.TypeFilterButton.bind(this)('充值', TransactionType.RECHARGE);
        this.TypeFilterButton.bind(this)('提现', TransactionType.WITHDRAW);
        this.TypeFilterButton.bind(this)('转账', TransactionType.TRANSFER);
        Row.pop();
        // 交易类型筛选
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易记录列表
            if (this.transactions.length === 0 && !this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.EmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(127:9)", "entry");
                        List.width('100%');
                        List.layoutWeight(1);
                        List.divider({
                            strokeWidth: 1,
                            color: '#F0F0F0',
                            startMargin: 16,
                            endMargin: 16
                        });
                        List.onReachEnd(() => {
                            if (!this.isLoading && this.hasMore) {
                                this.loadTransactions();
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => this.viewTransactionDetail(item));
                                    ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(129:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.TransactionCard.bind(this)(item);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.hasMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(136:13)", "entry");
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        this.LoadMoreView.bind(this)();
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易详情弹窗
            if (this.showDetailDialog && this.selectedTransaction) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.TransactionDetailDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TransactionCard(item: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(171:5)", "entry");
            Column.width('100%');
            Column.margin({ left: 16, right: 16, bottom: 12 });
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(16);
            Column.shadow({
                radius: 12,
                color: '#1A1A1A10',
                offsetX: 0,
                offsetY: 4
            });
            Column.onClick(() => {
                this.viewTransactionDetail(item);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(172:7)", "entry");
            Row.width('100%');
            Row.padding(20);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标 - 更大更美观的图标
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(174:9)", "entry");
            // 交易类型图标 - 更大更美观的图标
            Column.width(50);
            // 交易类型图标 - 更大更美观的图标
            Column.height(50);
            // 交易类型图标 - 更大更美观的图标
            Column.backgroundColor(this.getTypeColor(item.transactionType));
            // 交易类型图标 - 更大更美观的图标
            Column.borderRadius(25);
            // 交易类型图标 - 更大更美观的图标
            Column.justifyContent(FlexAlign.Center);
            // 交易类型图标 - 更大更美观的图标
            Column.alignItems(HorizontalAlign.Center);
            // 交易类型图标 - 更大更美观的图标
            Column.margin({ right: 16 });
            // 交易类型图标 - 更大更美观的图标
            Column.shadow({
                radius: 8,
                color: this.getTypeColor(item.transactionType) + '40',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionIcon(item.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(175:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 交易类型图标 - 更大更美观的图标
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(195:9)", "entry");
            // 交易信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(196:11)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(197:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTypeDisplayName(item.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(198:15)", "entry");
            Text.fontSize(17);
            Text.fontColor('#1A1A1A');
            Text.fontWeight(FontWeight.Medium);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.description);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(204:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(213:13)", "entry");
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatAmount(item));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(214:15)", "entry");
            Text.fontSize(18);
            Text.fontColor(this.getAmountColor(item));
            Text.fontWeight(FontWeight.Medium);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getStatusDisplayName(item.status));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(220:15)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(item.status));
            Text.backgroundColor(this.getStatusColor(item.status) + '15');
            Text.padding({ left: 8, right: 8, top: 2, bottom: 2 });
            Text.borderRadius(10);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(232:11)", "entry");
            Row.width('100%');
            Row.margin({ top: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.createTime);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(233:13)", "entry");
            Text.fontSize(13);
            Text.fontColor('#999999');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`交易号: ${item.transactionNo}`);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(238:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
        // 交易信息
        Column.pop();
        Row.pop();
        Column.pop();
    }
    TypeFilterButton(title: string, type: TransactionType | '', parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(title);
            Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(267:5)", "entry");
            Button.fontSize(15);
            Button.fontColor(this.selectedType === type ? '#FFFFFF' : '#667EEA');
            Button.backgroundColor(this.selectedType === type ? '#667EEA' : '#F8F9FA');
            Button.borderRadius(20);
            Button.height(36);
            Button.padding({ left: 20, right: 20 });
            Button.margin({ right: 10 });
            Button.border({
                width: this.selectedType === type ? 0 : 1,
                color: '#E5E7EB'
            });
            Button.shadow(this.selectedType === type ? {
                radius: 8,
                color: '#667EEA40',
                offsetX: 0,
                offsetY: 3
            } : undefined);
            Button.onClick(() => {
                this.selectedType = type;
                this.loadTransactions(true);
            });
        }, Button);
        Button.pop();
    }
    EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(293:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📄');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(294:7)", "entry");
            Text.fontSize(60);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(298:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#999999');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('去充值');
            Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(303:7)", "entry");
            Button.type(ButtonType.Capsule);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#6366F1');
            Button.onClick(() => router.pushUrl({ url: 'pages/RechargePage' }));
        }, Button);
        Button.pop();
        Column.pop();
    }
    LoadMoreView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(317:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.justifyContent(FlexAlign.Center);
            Row.onClick(() => {
                if (!this.isLoading) {
                    this.loadTransactions();
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(319:9)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.isLoading ? '加载中...' : '上拉加载更多');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(324:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Row.pop();
    }
    TransactionDetailDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(340:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('rgba(0,0,0,0.5)');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.onClick(() => this.showDetailDialog = false);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(341:7)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(342:9)", "entry");
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易详情');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(343:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('✕');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(348:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#666666');
            Text.width(24);
            Text.height(24);
            Text.textAlign(TextAlign.Center);
            Text.onClick(() => this.showDetailDialog = false);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(358:9)", "entry");
        }, Column);
        this.DetailItem.bind(this)('交易编号', this.selectedTransaction!.transactionNo);
        this.DetailItem.bind(this)('交易类型', this.selectedTransaction!.transactionType);
        this.DetailItem.bind(this)('交易金额', `¥${this.selectedTransaction!.amount.toFixed(2)}`);
        this.DetailItem.bind(this)('支付方式', this.selectedTransaction!.paymentMethod);
        this.DetailItem.bind(this)('交易状态', this.selectedTransaction!.status);
        this.DetailItem.bind(this)('交易时间', this.selectedTransaction!.createTime);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedTransaction!.cardNo) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItem.bind(this)('银行卡', this.selectedTransaction!.cardNo);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.DetailItem.bind(this)('交易描述', this.selectedTransaction!.description);
        Column.pop();
        Column.pop();
        Column.pop();
    }
    DetailItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(388:5)", "entry");
            Row.height(40);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(389:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width('30%');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(394:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
    }
    // 查看交易详情
    viewTransactionDetail(transaction: Transaction) {
        this.selectedTransaction = transaction;
        this.showDetailDialog = true;
    }
    // 获取交易图标
    getTransactionIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '💸';
            case TransactionType.RECHARGE: return '💰';
            case TransactionType.WITHDRAW: return '🏧';
            case TransactionType.PAYMENT: return '🛒';
            case TransactionType.RECEIVE: return '📥';
            case TransactionType.REFUND: return '↩️';
            default: return '📄';
        }
    }
    // 获取交易类型显示名称
    getTypeDisplayName(type: TransactionType): string {
        switch (type) {
            case TransactionType.PAYMENT:
                return '支付';
            case TransactionType.TRANSFER:
                return '转账';
            case TransactionType.RECHARGE:
                return '充值';
            case TransactionType.WITHDRAW:
                return '提现';
            case TransactionType.RECEIVE:
                return '收款';
            case TransactionType.REFUND:
                return '退款';
            default:
                return '其他';
        }
    }
    // 获取状态显示名称
    getStatusDisplayName(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS:
                return '成功';
            case TransactionStatus.PENDING:
                return '处理中';
            case TransactionStatus.FAILED:
                return '失败';
            default:
                return '未知';
        }
    }
    // 获取类型颜色
    getTypeColor(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '#6366F1';
            case TransactionType.RECHARGE: return '#10B981';
            case TransactionType.WITHDRAW: return '#F59E0B';
            case TransactionType.PAYMENT: return '#4338CA';
            case TransactionType.RECEIVE: return '#059669';
            case TransactionType.REFUND: return '#7C3AED';
            default: return '#6B7280';
        }
    }
    // 格式化金额
    formatAmount(transaction: Transaction): string {
        const amount = transaction.amount;
        // 充值和收钱显示正数，其他显示负数
        if (transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE) {
            return `+¥${amount.toFixed(2)}`;
        }
        return `¥${amount.toFixed(2)}`;
    }
    // 获取金额颜色
    getAmountColor(transaction: Transaction): string {
        // 充值和收钱显示绿色，其他显示红色
        if (transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE) {
            return '#10B981';
        }
        return '#EF4444';
    }
    // 获取状态颜色
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS: return '#10B981';
            case TransactionStatus.PENDING: return '#F59E0B';
            case TransactionStatus.FAILED: return '#EF4444';
            case TransactionStatus.CANCELLED: return '#9CA3AF';
            default: return '#6B7280';
        }
    }
    // 底部导航栏
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(504:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({
                width: { top: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(506:7)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.alignItems(HorizontalAlign.Center);
            // 银行卡
            Column.padding({ top: 8, bottom: 8 });
            // 银行卡
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/MyBankCardPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(507:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(509:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易 (当前页面)
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(524:7)", "entry");
            // 交易 (当前页面)
            Column.layoutWeight(1);
            // 交易 (当前页面)
            Column.alignItems(HorizontalAlign.Center);
            // 交易 (当前页面)
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(525:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(527:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#6366F1');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易 (当前页面)
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(537:7)", "entry");
            // 钱包
            Column.layoutWeight(1);
            // 钱包
            Column.alignItems(HorizontalAlign.Center);
            // 钱包
            Column.padding({ top: 8, bottom: 8 });
            // 钱包
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/WalletPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(538:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(540:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 钱包
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付中心
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(555:7)", "entry");
            // 支付中心
            Column.layoutWeight(1);
            // 支付中心
            Column.alignItems(HorizontalAlign.Center);
            // 支付中心
            Column.padding({ top: 8, bottom: 8 });
            // 支付中心
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/PaymentCenterPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(556:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(558:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 支付中心
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(573:7)", "entry");
            // 我的
            Column.layoutWeight(1);
            // 我的
            Column.alignItems(HorizontalAlign.Center);
            // 我的
            Column.padding({ top: 8, bottom: 8 });
            // 我的
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(574:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(576:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 我的
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionListPage";
    }
}
registerNamedRoute(() => new TransactionListPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransactionListPage", pageFullPath: "entry/src/main/ets/pages/TransactionListPage", integratedHsp: "false", moduleType: "followWithHap" });
