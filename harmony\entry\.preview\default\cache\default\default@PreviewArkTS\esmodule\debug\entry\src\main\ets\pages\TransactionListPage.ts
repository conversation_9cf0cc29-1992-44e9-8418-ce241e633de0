if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionListPage_Params {
    transactions?: Transaction[];
    isLoading?: boolean;
    isRefreshing?: boolean;
    hasMore?: boolean;
    currentPage?: number;
    showDetailDialog?: boolean;
    selectedTransaction?: Transaction | null;
    pageSize?: number;
    selectedType?: TransactionType | '';
    showFilterDialog?: boolean;
    filterStartDate?: string;
    filterEndDate?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import { TransactionType, TransactionStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction, TransactionQueryParams, PageResult } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
class TransactionListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isRefreshing = new ObservedPropertySimplePU(false, this, "isRefreshing");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__showDetailDialog = new ObservedPropertySimplePU(false, this, "showDetailDialog");
        this.__selectedTransaction = new ObservedPropertyObjectPU(null, this, "selectedTransaction");
        this.__pageSize = new ObservedPropertySimplePU(20, this, "pageSize");
        this.__selectedType = new ObservedPropertySimplePU('', this, "selectedType");
        this.__showFilterDialog = new ObservedPropertySimplePU(false, this, "showFilterDialog");
        this.__filterStartDate = new ObservedPropertySimplePU('', this, "filterStartDate");
        this.__filterEndDate = new ObservedPropertySimplePU('', this, "filterEndDate");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionListPage_Params) {
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isRefreshing !== undefined) {
            this.isRefreshing = params.isRefreshing;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.showDetailDialog !== undefined) {
            this.showDetailDialog = params.showDetailDialog;
        }
        if (params.selectedTransaction !== undefined) {
            this.selectedTransaction = params.selectedTransaction;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.showFilterDialog !== undefined) {
            this.showFilterDialog = params.showFilterDialog;
        }
        if (params.filterStartDate !== undefined) {
            this.filterStartDate = params.filterStartDate;
        }
        if (params.filterEndDate !== undefined) {
            this.filterEndDate = params.filterEndDate;
        }
    }
    updateStateVars(params: TransactionListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isRefreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__showDetailDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTransaction.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilterDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__filterStartDate.purgeDependencyOnElmtId(rmElmtId);
        this.__filterEndDate.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isRefreshing.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__showDetailDialog.aboutToBeDeleted();
        this.__selectedTransaction.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__selectedType.aboutToBeDeleted();
        this.__showFilterDialog.aboutToBeDeleted();
        this.__filterStartDate.aboutToBeDeleted();
        this.__filterEndDate.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isRefreshing: ObservedPropertySimplePU<boolean>;
    get isRefreshing() {
        return this.__isRefreshing.get();
    }
    set isRefreshing(newValue: boolean) {
        this.__isRefreshing.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __showDetailDialog: ObservedPropertySimplePU<boolean>;
    get showDetailDialog() {
        return this.__showDetailDialog.get();
    }
    set showDetailDialog(newValue: boolean) {
        this.__showDetailDialog.set(newValue);
    }
    private __selectedTransaction: ObservedPropertyObjectPU<Transaction | null>;
    get selectedTransaction() {
        return this.__selectedTransaction.get();
    }
    set selectedTransaction(newValue: Transaction | null) {
        this.__selectedTransaction.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __selectedType: ObservedPropertySimplePU<TransactionType | ''>;
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: TransactionType | '') {
        this.__selectedType.set(newValue);
    }
    private __showFilterDialog: ObservedPropertySimplePU<boolean>;
    get showFilterDialog() {
        return this.__showFilterDialog.get();
    }
    set showFilterDialog(newValue: boolean) {
        this.__showFilterDialog.set(newValue);
    }
    // 筛选条件
    private __filterStartDate: ObservedPropertySimplePU<string>;
    get filterStartDate() {
        return this.__filterStartDate.get();
    }
    set filterStartDate(newValue: string) {
        this.__filterStartDate.set(newValue);
    }
    private __filterEndDate: ObservedPropertySimplePU<string>;
    get filterEndDate() {
        return this.__filterEndDate.get();
    }
    set filterEndDate(newValue: string) {
        this.__filterEndDate.set(newValue);
    }
    aboutToAppear() {
        this.loadTransactions(true);
    }
    async loadTransactions(refresh: boolean = false) {
        if (this.isLoading)
            return;
        this.isLoading = true;
        if (refresh) {
            this.isRefreshing = true;
            this.currentPage = 1;
            this.hasMore = true;
        }
        try {
            const params: TransactionQueryParams = {
                page: this.currentPage.toString(),
                size: this.pageSize.toString(),
                type: this.selectedType || undefined,
                startDate: this.filterStartDate,
                endDate: this.filterEndDate
            };
            const result: PageResult<Transaction> = await TransactionApi.getTransactionList(params);
            if (refresh) {
                this.transactions = result.records;
            }
            else {
                this.transactions = [...this.transactions, ...result.records];
            }
            this.hasMore = this.currentPage < result.pages;
            this.currentPage++;
        }
        catch (error) {
            console.error('加载交易记录失败:', error);
            promptAction.showToast({ message: '加载交易记录失败' });
        }
        finally {
            this.isLoading = false;
            this.isRefreshing = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(74:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(76:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(60);
            // 顶部导航栏
            Row.justifyContent(FlexAlign.Center);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.linearGradient({
                direction: GradientDirection.Right,
                colors: [['#6366F1', 0.0], ['#8B5CF6', 1.0]]
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(77:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型筛选
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(92:7)", "entry");
            // 交易类型筛选
            Scroll.scrollable(ScrollDirection.Horizontal);
            // 交易类型筛选
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(93:9)", "entry");
            Row.padding(12);
        }, Row);
        this.TypeFilterButton.bind(this)('全部', '');
        this.TypeFilterButton.bind(this)('支付', TransactionType.PAYMENT);
        this.TypeFilterButton.bind(this)('充值', TransactionType.RECHARGE);
        this.TypeFilterButton.bind(this)('提现', TransactionType.WITHDRAW);
        this.TypeFilterButton.bind(this)('转账', TransactionType.TRANSFER);
        Row.pop();
        // 交易类型筛选
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易记录列表
            if (this.transactions.length === 0 && !this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.EmptyView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(109:9)", "entry");
                        List.width('100%');
                        List.layoutWeight(1);
                        List.divider({
                            strokeWidth: 1,
                            color: '#F0F0F0',
                            startMargin: 16,
                            endMargin: 16
                        });
                        List.onReachEnd(() => {
                            if (!this.isLoading && this.hasMore) {
                                this.loadTransactions();
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const item = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => this.viewTransactionDetail(item));
                                    ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(111:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.TransactionCard.bind(this)(item);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.hasMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(118:13)", "entry");
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        this.LoadMoreView.bind(this)();
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        // 底部导航栏
        this.BottomNavigation.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易详情弹窗
            if (this.showDetailDialog && this.selectedTransaction) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.TransactionDetailDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TransactionCard(item: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(153:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(154:7)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(156:9)", "entry");
            // 交易类型图标
            Column.width(40);
            // 交易类型图标
            Column.height(40);
            // 交易类型图标
            Column.backgroundColor(this.getTypeColor(item.transactionType) + '20');
            // 交易类型图标
            Column.borderRadius(20);
            // 交易类型图标
            Column.justifyContent(FlexAlign.Center);
            // 交易类型图标
            Column.alignItems(HorizontalAlign.Center);
            // 交易类型图标
            Column.margin({ right: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionIcon(item.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(157:11)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        // 交易类型图标
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(169:9)", "entry");
            // 交易信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(170:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.transactionType);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(171:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatAmount(item));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(177:13)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.getAmountColor(item));
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.description);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(182:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(189:11)", "entry");
            Row.margin({ top: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.createTime);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(190:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.status);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(194:13)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(item.status));
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        // 交易信息
        Column.pop();
        Row.pop();
        Column.pop();
    }
    TypeFilterButton(title: string, type: TransactionType | '', parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(title);
            Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(212:5)", "entry");
            Button.fontSize(14);
            Button.fontColor(this.selectedType === type ? '#FFFFFF' : '#6366F1');
            Button.backgroundColor(this.selectedType === type ? '#6366F1' : '#F5F5F5');
            Button.borderRadius(16);
            Button.height(32);
            Button.padding({ left: 16, right: 16 });
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.selectedType = type;
                this.loadTransactions(true);
            });
        }, Button);
        Button.pop();
    }
    EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(228:5)", "entry");
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📄');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(229:7)", "entry");
            Text.fontSize(60);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('暂无交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(233:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#999999');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('去充值');
            Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(238:7)", "entry");
            Button.type(ButtonType.Capsule);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#6366F1');
            Button.onClick(() => router.pushUrl({ url: 'pages/RechargePage' }));
        }, Button);
        Button.pop();
        Column.pop();
    }
    LoadMoreView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(252:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.justifyContent(FlexAlign.Center);
            Row.onClick(() => {
                if (!this.isLoading) {
                    this.loadTransactions();
                }
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(254:9)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.isLoading ? '加载中...' : '上拉加载更多');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(259:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Row.pop();
    }
    TransactionDetailDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(275:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('rgba(0,0,0,0.5)');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.onClick(() => this.showDetailDialog = false);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(276:7)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#FFFFFF');
            Column.borderRadius(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(277:9)", "entry");
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易详情');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(278:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('✕');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(283:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#666666');
            Text.width(24);
            Text.height(24);
            Text.textAlign(TextAlign.Center);
            Text.onClick(() => this.showDetailDialog = false);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(293:9)", "entry");
        }, Column);
        this.DetailItem.bind(this)('交易编号', this.selectedTransaction!.transactionNo);
        this.DetailItem.bind(this)('交易类型', this.selectedTransaction!.transactionType);
        this.DetailItem.bind(this)('交易金额', `¥${this.selectedTransaction!.amount.toFixed(2)}`);
        this.DetailItem.bind(this)('支付方式', this.selectedTransaction!.paymentMethod);
        this.DetailItem.bind(this)('交易状态', this.selectedTransaction!.status);
        this.DetailItem.bind(this)('交易时间', this.selectedTransaction!.createTime);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedTransaction!.cardNo) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.DetailItem.bind(this)('银行卡', this.selectedTransaction!.cardNo);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.DetailItem.bind(this)('交易描述', this.selectedTransaction!.description);
        Column.pop();
        Column.pop();
        Column.pop();
    }
    DetailItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(323:5)", "entry");
            Row.height(40);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(324:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width('30%');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(329:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
    }
    // 查看交易详情
    viewTransactionDetail(transaction: Transaction) {
        this.selectedTransaction = transaction;
        this.showDetailDialog = true;
    }
    // 获取交易图标
    getTransactionIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '💸';
            case TransactionType.DEPOSIT: return '💰';
            case TransactionType.WITHDRAW: return '🏧';
            case TransactionType.CONSUME: return '🛒';
            default: return '📄';
        }
    }
    // 获取类型颜色
    getTypeColor(type: TransactionType): string {
        switch (type) {
            case TransactionType.TRANSFER: return '#6366F1';
            case TransactionType.DEPOSIT: return '#10B981';
            case TransactionType.WITHDRAW: return '#F59E0B';
            case TransactionType.CONSUME: return '#4338CA';
            default: return '#6B7280';
        }
    }
    // 格式化金额
    formatAmount(transaction: Transaction): string {
        const amount = transaction.amount;
        const prefix = transaction.transactionType === TransactionType.DEPOSIT ? '+' : '-';
        return `${prefix}¥${amount.toFixed(2)}`;
    }
    // 获取金额颜色
    getAmountColor(transaction: Transaction): string {
        return transaction.transactionType === TransactionType.DEPOSIT ? '#10B981' : '#EF4444';
    }
    // 获取状态颜色
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.COMPLETED: return '#10B981';
            case TransactionStatus.PROCESSING: return '#F59E0B';
            case TransactionStatus.FAILED: return '#EF4444';
            case TransactionStatus.CANCELED: return '#9CA3AF';
            default: return '#6B7280';
        }
    }
    // 底部导航栏
    BottomNavigation(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(392:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.backgroundColor('#FFFFFF');
            Row.border({
                width: { top: 1 },
                color: '#E5E5E5'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(394:7)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.alignItems(HorizontalAlign.Center);
            // 银行卡
            Column.padding({ top: 8, bottom: 8 });
            // 银行卡
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/MyBankCardPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(395:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(397:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易 (当前页面)
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(412:7)", "entry");
            // 交易 (当前页面)
            Column.layoutWeight(1);
            // 交易 (当前页面)
            Column.alignItems(HorizontalAlign.Center);
            // 交易 (当前页面)
            Column.padding({ top: 8, bottom: 8 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📊');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(413:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(415:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#6366F1');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 交易 (当前页面)
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(425:7)", "entry");
            // 钱包
            Column.layoutWeight(1);
            // 钱包
            Column.alignItems(HorizontalAlign.Center);
            // 钱包
            Column.padding({ top: 8, bottom: 8 });
            // 钱包
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/WalletPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👛');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(426:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(428:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 钱包
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付中心
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(443:7)", "entry");
            // 支付中心
            Column.layoutWeight(1);
            // 支付中心
            Column.alignItems(HorizontalAlign.Center);
            // 支付中心
            Column.padding({ top: 8, bottom: 8 });
            // 支付中心
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/PaymentCenterPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💰');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(444:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付中心');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(446:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 支付中心
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(461:7)", "entry");
            // 我的
            Column.layoutWeight(1);
            // 我的
            Column.alignItems(HorizontalAlign.Center);
            // 我的
            Column.padding({ top: 8, bottom: 8 });
            // 我的
            Column.onClick(() => {
                router.pushUrl({
                    url: 'pages/SettingsPage'
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('👤');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(462:9)", "entry");
            Text.fontSize(20);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(464:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 我的
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionListPage";
    }
}
registerNamedRoute(() => new TransactionListPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransactionListPage", pageFullPath: "entry/src/main/ets/pages/TransactionListPage", integratedHsp: "false", moduleType: "followWithHap" });
