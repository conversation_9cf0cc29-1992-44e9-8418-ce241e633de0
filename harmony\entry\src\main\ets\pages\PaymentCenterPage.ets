import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct PaymentCenterPage {
  aboutToAppear() {
    console.log('PaymentCenterPage 页面加载');
  }

  // 支付方式点击处理（保持不变）
  private handlePaymentMethod(method: string) {
    console.log(`选择支付方式: ${method}`);

    promptAction.showToast({
      message: `正在跳转到${method}...`,
      duration: 1000
    });

    switch (method) {
      case '钱包支付':
        router.pushUrl({
          url: 'pages/WalletPaymentPage'
        }).then(() => {
          console.log('跳转到钱包支付页面成功');
        }).catch((error: Error) => {
          console.error('跳转到钱包支付页面失败:', error.message);
          promptAction.showToast({
            message: '跳转失败: ' + error.message,
            duration: 3000
          });
        });
        break;
      case '银行卡支付':
        router.pushUrl({
          url: 'pages/BankCardPaymentPage'
        }).then(() => {
          console.log('跳转到银行卡支付页面成功');
        }).catch((error: Error) => {
          console.error('跳转到银行卡支付页面失败:', error.message);
          promptAction.showToast({
            message: '跳转失败: ' + error.message,
            duration: 3000
          });
        });
        break;
      case '扫码支付':
        router.pushUrl({
          url: 'pages/QRCodePaymentPage'
        }).then(() => {
          console.log('跳转到扫码支付页面成功');
        }).catch((error: Error) => {
          console.error('跳转到扫码支付页面失败:', error.message);
          promptAction.showToast({
            message: '跳转失败: ' + error.message,
            duration: 3000
          });
        });
        break;
      case 'NFC支付':
        router.pushUrl({
          url: 'pages/NFCPaymentPage'
        }).then(() => {
          console.log('跳转到NFC支付页面成功');
        }).catch((error: Error) => {
          console.error('跳转到NFC支付页面失败:', error.message);
          promptAction.showToast({
            message: '跳转失败: ' + error.message,
            duration: 3000
          });
        });
        break;
      default:
        promptAction.showToast({
          message: `选择了${method}`,
          duration: 2000
        });
    }
  }

  build() {
    Column() {
      // 顶部标题栏（淡粉色卡片式）
      Column() {
        Row() {
          Text('支付中心')
            .fontSize(20)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
        }
        .width('100%')
        .height(60)
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
      }
      .width('90%')
      .margin({ top: 20 })
      .borderRadius(16)
      .backgroundColor('#ff3785f5')
      .shadow({ radius: 6, color: '#ff4ce4e9', offsetX: 0, offsetY: 2 })

      // 内容区域
      Column() {
        // 选择支付方式标题
        Text('选择支付方式')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .margin({ top: 20, bottom: 20 })
          .alignSelf(ItemAlign.Start)

        // 钱包支付
        Row() {
          Text('💳')
            .fontSize(24)
            .margin({ right: 12 })

          Column() {
            Text('钱包支付')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 2 })

            Text('使用钱包余额支付')
              .fontSize(12)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .alignItems(VerticalAlign.Center)
        .onClick(() => {
          this.handlePaymentMethod('钱包支付');
        })

        // 银行卡支付
        Row() {
          Text('🏦')
            .fontSize(24)
            .margin({ right: 12 })

          Column() {
            Text('银行卡支付')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 2 })

            Text('使用银行卡支付')
              .fontSize(12)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .alignItems(VerticalAlign.Center)
        .onClick(() => {
          this.handlePaymentMethod('银行卡支付');
        })

        // 扫码支付
        Row() {
          Text('📱')
            .fontSize(24)
            .margin({ right: 12 })

          Column() {
            Text('扫码支付')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 2 })

            Text('扫描二维码支付')
              .fontSize(12)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .alignItems(VerticalAlign.Center)
        .onClick(() => {
          this.handlePaymentMethod('扫码支付');
        })

        // NFC支付
        Row() {
          Text('📡')
            .fontSize(24)
            .margin({ right: 12 })

          Column() {
            Text('NFC支付')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 2 })

            Text('近场通信支付')
              .fontSize(12)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Text('>')
            .fontSize(16)
            .fontColor('#CCCCCC')
        }
        .width('100%')
        .padding({ top: 16, bottom: 16, left: 16, right: 16 })
        .backgroundColor('#FFFFFF')
        .borderRadius(12)
        .margin({ bottom: 12 })
        .alignItems(VerticalAlign.Center)
        .onClick(() => {
          this.handlePaymentMethod('NFC支付');
        })
      }
      .layoutWeight(1)
      .padding({ left: 16, right: 16, bottom: 10 })
      .alignItems(HorizontalAlign.Start)
      .justifyContent(FlexAlign.Start)

      // 底部导航栏
      this.BottomNavigation()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }

  // 底部导航栏
  @Builder
  BottomNavigation() {
    Row() {
      // 银行卡
      Column() {
        Text('💳')
          .fontSize(20)
        Text('银行卡')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/MyBankCardPage'
        });
      })

      // 交易
      Column() {
        Text('📊')
          .fontSize(20)
        Text('交易')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/TransactionListPage'
        });
      })

      // 钱包
      Column() {
        Text('👛')
          .fontSize(20)
        Text('钱包')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/WalletPage'
        });
      })

      // 支付中心 (当前页面)
      Column() {
        Text('💰')
          .fontSize(20)
        Text('支付中心')
          .fontSize(12)
          .fontColor('#6366F1')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })

      // 我的
      Column() {
        Text('👤')
          .fontSize(20)
        Text('我的')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Center)
      .padding({ top: 8, bottom: 8 })
      .onClick(() => {
        router.pushUrl({
          url: 'pages/SettingsPage'
        });
      })
    }
    .width('100%')
    .height(60)
    .backgroundColor('#FFFFFF')
    .border({
      width: { top: 1 },
      color: '#E5E5E5'
    })
  }
}