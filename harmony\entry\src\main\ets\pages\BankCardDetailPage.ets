import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { BankCard, BankCardType } from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';

@Entry
@Component
struct BankCardDetailPage {
  @State cardDetail: BankCard | null = null;
  @State isLoading: boolean = true;
  @State cardId: number = 0;

  aboutToAppear() {
    // 获取传入的银行卡ID
    const params = router.getParams() as Record<string, Object>;
    this.cardId = (params && params.cardId) ? params.cardId as number : 0;
    
    if (this.cardId > 0) {
      this.loadCardDetail();
    } else {
      promptAction.showToast({ message: '银行卡信息错误' });
      router.back();
    }
  }

  async loadCardDetail() {
    try {
      this.isLoading = true;
      this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
    } catch (error) {
      console.error('获取银行卡详情失败:', error);
      promptAction.showToast({ message: '获取银行卡详情失败' });
      router.back();
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .onClick(() => {
            router.back();
          })

        Text('银行卡详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .alignItems(VerticalAlign.Center)

      if (this.isLoading) {
        // 加载状态
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#F5F5F5')
      } else if (this.cardDetail) {
        // 银行卡详情内容
        Scroll() {
          Column() {
            // 银行卡卡片
            this.BankCardDisplay()

            // 详细信息
            this.CardDetailInfo()

            // 操作按钮
            this.ActionButtons()
          }
          .padding({ left: 16, right: 16, top: 16, bottom: 20 })
        }
        .layoutWeight(1)
        .backgroundColor('#F5F5F5')
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardDisplay() {
    Column() {
      // 银行卡样式展示
      Column() {
        // 第一行：银行名称和卡片类型
        Row() {
          Text(this.cardDetail?.bankName || '中国工商银行')
            .fontSize(18)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
            .layoutWeight(1)

          Text(this.getCardTypeText())
            .fontSize(14)
            .fontColor('#E3F2FD')
        }
        .width('100%')
        .margin({ bottom: 30 })

        // 第二行：卡号
        Text(`**** **** **** ${this.cardDetail?.cardNo.slice(-4) || '8888'}`)
          .fontSize(22)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
          .letterSpacing(2)
          .margin({ bottom: 20 })

        // 第三行：持卡人姓名和绑定状态
        Row() {
          Text(this.cardDetail?.holderName || '罗曼勾')
            .fontSize(16)
            .fontColor('#E3F2FD')
            .layoutWeight(1)

          Text(this.getCardStatus())
            .fontSize(12)
            .fontColor('#FFFFFF')
            .backgroundColor('rgba(255, 255, 255, 0.2)')
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .borderRadius(12)
        }
        .width('100%')
      }
      .width('100%')
      .height(180)
      .padding(24)
      .borderRadius(16)
      .backgroundColor('#1976D2')
    }
    .width('100%')
    .margin({ bottom: 24 })
  }

  @Builder
  CardDetailInfo() {
    Column() {
      Text('银行卡信息')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      this.DetailItem('银行名称', this.cardDetail?.bankName || '中国工商银行')
      this.DetailItem('卡片类型', this.getCardTypeText())
      this.DetailItem('持卡人姓名', this.cardDetail?.holderName || '罗曼勾')
      this.DetailItem('卡号', this.formatCardNumber(this.cardDetail?.cardNo || '****************'))
      this.DetailItem('绑定状态', this.getCardStatus())
      this.DetailItem('绑定时间', this.formatDateTime(this.cardDetail?.createTime || '2025/06/23 14:30'))
    }
    .width('100%')
    .padding(20)
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .margin({ bottom: 16 })
  }

  @Builder
  DetailItem(label: string, value: string) {
    Column() {
      Row() {
        Text(label)
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(value)
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.End)
      }
      .width('100%')
      .height(50)
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .padding({ left: 0, right: 0 })

      // 分隔线
      Divider()
        .color('#F0F0F0')
        .strokeWidth(0.5)
        .margin({ top: 8 })
    }
    .width('100%')
  }

  @Builder
  ActionButtons() {
    Column() {
      Button('解绑银行卡')
        .width('100%')
        .height(50)
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#F44336')
        .borderRadius(25)
        .onClick(() => {
          this.confirmUnbindCard();
        })
    }
    .width('100%')
    .padding({ left: 20, right: 20, top: 30, bottom: 50 })
  }

  // 工具方法
  getCardStatus(): string {
    return this.cardDetail?.isBound === 1 ? '已绑定' : '未绑定';
  }

  getCardStatusColor(): string {
    return this.cardDetail?.isBound === 1 ? '#4CAF50' : '#F44336';
  }

  getCardStatusBgColor(): string {
    return this.cardDetail?.isBound === 1 ? '#E8F5E8' : '#FFEBEE';
  }

  getCardTypeText(): string {
    if (!this.cardDetail?.cardType) return '储蓄卡';
    return this.cardDetail.cardType;
  }

  formatCardNumber(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) {
      return cardNo;
    }

    // 根据图片显示格式：8888 ********** 8888
    const start = cardNo.substring(0, 4);
    const end = cardNo.substring(cardNo.length - 4);
    const middle = '*'.repeat(10); // 固定10个星号

    return `${start} ${middle} ${end}`;
  }

  formatDateTime(dateTime: string): string {
    if (!dateTime) return '2025/06/23 14:30';

    // 如果已经是正确格式，直接返回
    if (dateTime.includes('/')) {
      return dateTime;
    }

    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hour}:${minute}`;
  }

  async confirmUnbindCard() {
    try {
      const result = await promptAction.showDialog({
        title: '确认解绑',
        message: `确定要解绑银行卡 ${this.cardDetail?.bankName}(${this.cardDetail?.cardNo.slice(-4)}) 吗？`,
        buttons: [
          { text: '确定', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.unbindCard();
      }
    } catch (error) {
      console.error('显示确认对话框失败:', error);
    }
  }

  async unbindCard() {
    try {
      await BankCardApi.unbindCard(this.cardId);
      promptAction.showToast({ message: '解绑成功' });

      // 通知银行卡列表页面刷新
      console.log('BankCardDetailPage - 设置银行卡解绑事件标志');
      tempDataManager.setData('BANK_CARD_UNBOUND', true);

      // 返回银行卡列表页
      router.back();
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      promptAction.showToast({ message: '解绑失败，请重试' });
    }
  }
}
