{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "49497c67-64bd-4167-b600-46004e3a553b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19732057829000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38b679a1-9f10-46a6-b7bc-9c63225d9133", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19732065104000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e9eb843-8703-4f2a-9322-a2ea7890988c", "name": "Server currently has 1 watch-worker", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 19732065395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ad1253-15d2-4499-9d6f-224b5bee435c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20627053902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce8fb25a-368d-469c-992a-e97c51879847", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20627099128300, "endTime": 20627099183300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "38c86204-c7d3-4a97-af05-47d927708ad6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38c86204-c7d3-4a97-af05-47d927708ad6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20627099128300, "endTime": 20627099183300}, "additional": {"logType": "info", "children": [], "durationId": "ce8fb25a-368d-469c-992a-e97c51879847"}}, {"head": {"id": "ac6407cd-929b-4089-8347-fc1fa1a93df0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635563413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98304c71-2833-4e27-af61-3ae1375b5af9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635564629500, "endTime": 20635564650500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "69f91bef-8c59-4c3e-ab09-e50c41141617"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69f91bef-8c59-4c3e-ab09-e50c41141617", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635564629500, "endTime": 20635564650500}, "additional": {"logType": "info", "children": [], "durationId": "98304c71-2833-4e27-af61-3ae1375b5af9"}}, {"head": {"id": "1f94287e-6956-45a5-88e1-f9e64755923c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635564733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0058b9b1-5f7b-41dc-b65d-b30fcfe167a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635565574200, "endTime": 20635565587500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f0b9fa47-d21e-470a-816b-848462fc71f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0b9fa47-d21e-470a-816b-848462fc71f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20635565574200, "endTime": 20635565587500}, "additional": {"logType": "info", "children": [], "durationId": "0058b9b1-5f7b-41dc-b65d-b30fcfe167a0"}}, {"head": {"id": "f4be5a43-9b1a-4833-9847-74ba60b33722", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20638787138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ba8c41-6684-4c42-ae69-ac9670e46a2e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20638788388700, "endTime": 20638788407300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "fcb9ddca-cd62-4d8f-8d94-0f172c07ef1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcb9ddca-cd62-4d8f-8d94-0f172c07ef1b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20638788388700, "endTime": 20638788407300}, "additional": {"logType": "info", "children": [], "durationId": "49ba8c41-6684-4c42-ae69-ac9670e46a2e"}}, {"head": {"id": "d921f149-d8c1-4a3f-bc8c-e02a64db775b", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20642084456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34216baa-7ad3-466e-9a38-2aad1389522b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20642085738800, "endTime": 20642085767000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f8b22264-5d9c-4533-8d02-0a5425cf4457"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8b22264-5d9c-4533-8d02-0a5425cf4457", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20642085738800, "endTime": 20642085767000}, "additional": {"logType": "info", "children": [], "durationId": "34216baa-7ad3-466e-9a38-2aad1389522b"}}, {"head": {"id": "d940511f-1948-4e36-a3ee-a9e6a7122558", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643558748500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1bd189-51f3-411e-9359-dd64d8a8d273", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643560813900, "endTime": 20643560840000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "297ea3ba-9c0b-4b6e-aafb-c02b5edd4a4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "297ea3ba-9c0b-4b6e-aafb-c02b5edd4a4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643560813900, "endTime": 20643560840000}, "additional": {"logType": "info", "children": [], "durationId": "df1bd189-51f3-411e-9359-dd64d8a8d273"}}, {"head": {"id": "6da89538-af5e-45f1-b3a7-350846e0a787", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643808595200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af303175-9473-492e-976d-56e15b1001ea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643810423300, "endTime": 20643810468000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f916caf1-23ae-4f18-8fb1-8b2df0213418"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f916caf1-23ae-4f18-8fb1-8b2df0213418", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20643810423300, "endTime": 20643810468000}, "additional": {"logType": "info", "children": [], "durationId": "af303175-9473-492e-976d-56e15b1001ea"}}, {"head": {"id": "cb5bda78-bb83-4727-b02a-b5209dcaa727", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867396723400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "711ed035-1926-433b-b6b5-495b93870681", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867403780600, "endTime": 20867893738400}, "additional": {"children": ["7316d935-afb2-44b2-bba7-e02f6187d650", "9c744481-0b10-400b-ba21-4c2e8ffd557b", "dedaf0da-4302-4e61-9222-8fabd37cb684", "3740a7be-39bb-42cf-9661-000d982b05ff", "348e45e7-528d-499a-9864-27b7d19349cc", "5f83f5e7-b7fc-429f-af45-a863c40a4849", "09fc0d26-7721-4391-8b8b-179f68605205"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7316d935-afb2-44b2-bba7-e02f6187d650", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867403782300, "endTime": 20867618988800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "6b144727-0390-409d-b964-c120b06ceecb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867619057000, "endTime": 20867884920100}, "additional": {"children": ["084a30fa-416e-4865-a026-3bacf9ac78ae", "0f256eeb-72d8-460e-aa2a-ba9d322bc08d", "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "1baf0227-7b3a-4461-945c-593688ecb3aa", "11eed542-359c-4a68-92ef-5009f000bfc0", "7a01ff79-a809-4a6e-85ed-193094007485", "30a3f129-8714-4dad-9077-b934e9fa4387", "4ce7e6e4-f727-4958-9bda-d4780f5496a3", "154680a3-bf5e-4ee6-9612-3b9a298c1d63"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "29d597c6-dbc3-4cc8-8b21-76782014767f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dedaf0da-4302-4e61-9222-8fabd37cb684", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867884942900, "endTime": 20867893729300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "88ad4e3c-9453-4f6d-aa8f-3c62f21b65e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3740a7be-39bb-42cf-9661-000d982b05ff", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867893733800, "endTime": 20867893735300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "b286e674-67e0-4c1a-9638-d811a1e0c5f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "348e45e7-528d-499a-9864-27b7d19349cc", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867407525100, "endTime": 20867407562500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "077041bc-734e-44a5-bb74-33691179a9e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "077041bc-734e-44a5-bb74-33691179a9e7", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867407525100, "endTime": 20867407562500}, "additional": {"logType": "info", "children": [], "durationId": "348e45e7-528d-499a-9864-27b7d19349cc", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "5f83f5e7-b7fc-429f-af45-a863c40a4849", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867412447300, "endTime": 20867412460900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "7f124802-6c10-4b2a-94be-92908e332a78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f124802-6c10-4b2a-94be-92908e332a78", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867412447300, "endTime": 20867412460900}, "additional": {"logType": "info", "children": [], "durationId": "5f83f5e7-b7fc-429f-af45-a863c40a4849", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "71885b22-374c-483e-b4c0-232049add894", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867412506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0535994-c511-4d12-bd5d-735774fdf813", "name": "Cache service initialization finished in 207 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867618858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b144727-0390-409d-b964-c120b06ceecb", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867403782300, "endTime": 20867618988800}, "additional": {"logType": "info", "children": [], "durationId": "7316d935-afb2-44b2-bba7-e02f6187d650", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "084a30fa-416e-4865-a026-3bacf9ac78ae", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867625039200, "endTime": 20867625068200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "29982ad3-d2cc-4212-be6f-5d55e9eb92b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f256eeb-72d8-460e-aa2a-ba9d322bc08d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867625082900, "endTime": 20867629564600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "2a552973-6782-465b-ac79-28291341f6ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867629574900, "endTime": 20867799346700}, "additional": {"children": ["ff2cef0c-cc43-4547-b3a0-148b1c25d70a", "4a619fd2-e504-4c30-bbd3-623b5b76f6cb", "44a8f8e0-c757-4bf8-8bd1-d7e71c74972a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "672b1dab-a8bd-4247-a2e5-2fd14577d322"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1baf0227-7b3a-4461-945c-593688ecb3aa", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867799359500, "endTime": 20867824332500}, "additional": {"children": ["e60d0799-5a92-42fe-b24a-d3ee3eb91eb9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "07cf29f4-b643-4d73-a85c-08ff01673c5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11eed542-359c-4a68-92ef-5009f000bfc0", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867824338900, "endTime": 20867863013900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "ece628ad-1265-4e99-9a6d-9bc0d9482711"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a01ff79-a809-4a6e-85ed-193094007485", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867864038900, "endTime": 20867872888700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "43a484b7-381f-4397-b0d6-2f278207fd2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30a3f129-8714-4dad-9077-b934e9fa4387", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867872909000, "endTime": 20867884758400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "bdec0f66-3402-47ec-8d66-e87d499384b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ce7e6e4-f727-4958-9bda-d4780f5496a3", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867884777500, "endTime": 20867884909900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "94b2a942-ddd6-4bcd-b2c7-0b94ec75ac33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29982ad3-d2cc-4212-be6f-5d55e9eb92b4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867625039200, "endTime": 20867625068200}, "additional": {"logType": "info", "children": [], "durationId": "084a30fa-416e-4865-a026-3bacf9ac78ae", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "2a552973-6782-465b-ac79-28291341f6ed", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867625082900, "endTime": 20867629564600}, "additional": {"logType": "info", "children": [], "durationId": "0f256eeb-72d8-460e-aa2a-ba9d322bc08d", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "ff2cef0c-cc43-4547-b3a0-148b1c25d70a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867630229300, "endTime": 20867630245700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "logId": "7369244b-1644-45cc-8a52-217326370ece"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7369244b-1644-45cc-8a52-217326370ece", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867630229300, "endTime": 20867630245700}, "additional": {"logType": "info", "children": [], "durationId": "ff2cef0c-cc43-4547-b3a0-148b1c25d70a", "parent": "672b1dab-a8bd-4247-a2e5-2fd14577d322"}}, {"head": {"id": "4a619fd2-e504-4c30-bbd3-623b5b76f6cb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867632728600, "endTime": 20867798620000}, "additional": {"children": ["013ec56d-41bc-4c46-bab0-00b363be66af", "6e979607-e5bc-4fc1-9196-827b2c78b979"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "logId": "0bf6c4c7-da2e-4a9f-8e22-8fcb662a8cde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "013ec56d-41bc-4c46-bab0-00b363be66af", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867632729500, "endTime": 20867637700700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a619fd2-e504-4c30-bbd3-623b5b76f6cb", "logId": "0c4362f9-61ab-4ed3-97f7-844aa38c6635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e979607-e5bc-4fc1-9196-827b2c78b979", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867637729600, "endTime": 20867798605900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a619fd2-e504-4c30-bbd3-623b5b76f6cb", "logId": "8e53cd5a-f9f1-451d-b800-56ff441dfd12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57d43f4c-2081-43dd-ad92-168b359076d7", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867632732800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb7cc19-f64a-49aa-a346-e14a09cd6b87", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867637485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c4362f9-61ab-4ed3-97f7-844aa38c6635", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867632729500, "endTime": 20867637700700}, "additional": {"logType": "info", "children": [], "durationId": "013ec56d-41bc-4c46-bab0-00b363be66af", "parent": "0bf6c4c7-da2e-4a9f-8e22-8fcb662a8cde"}}, {"head": {"id": "1325d2e7-b5f6-4d0b-a2cd-9c5a64b91e86", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867637747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3313dba-5c2e-4daa-b75b-a179e5560ba7", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867646113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235f35c1-85fc-48d3-ba43-d92fb620e851", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867646222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a9a7ce-87a3-4e8a-a936-a986cd68aeee", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867646342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "753e86f2-55ab-4ca8-9140-c17fa7f76367", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867646442000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d7f39f-5ae7-437d-b3ec-9f8bdbb00950", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867647972600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "075fb3d2-9b2e-459e-97b6-5f7718436fea", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867652779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf86711-3b8f-4e7f-ac9b-e4c62e4ebb60", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867662579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81aeede5-fd33-4c6f-9c4d-531ba82538ee", "name": "Sdk init in 122 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867775410900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d938fe-9d52-4cd0-be67-5e495ea8b946", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867775532700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 37}, "markType": "other"}}, {"head": {"id": "d21554a9-8481-4851-bcff-566f4118bd4a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867775547600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 37}, "markType": "other"}}, {"head": {"id": "06f4aeeb-d9e5-471f-b8a3-93d816336a1a", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867798337700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af36e71c-9e0d-4d9b-b375-dd066c67a44c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867798453700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3145d384-5dd8-4234-82cc-f9a1c05bda50", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867798512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7604cb1e-c6d5-4667-8338-e6eea027efc6", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867798561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e53cd5a-f9f1-451d-b800-56ff441dfd12", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867637729600, "endTime": 20867798605900}, "additional": {"logType": "info", "children": [], "durationId": "6e979607-e5bc-4fc1-9196-827b2c78b979", "parent": "0bf6c4c7-da2e-4a9f-8e22-8fcb662a8cde"}}, {"head": {"id": "0bf6c4c7-da2e-4a9f-8e22-8fcb662a8cde", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867632728600, "endTime": 20867798620000}, "additional": {"logType": "info", "children": ["0c4362f9-61ab-4ed3-97f7-844aa38c6635", "8e53cd5a-f9f1-451d-b800-56ff441dfd12"], "durationId": "4a619fd2-e504-4c30-bbd3-623b5b76f6cb", "parent": "672b1dab-a8bd-4247-a2e5-2fd14577d322"}}, {"head": {"id": "44a8f8e0-c757-4bf8-8bd1-d7e71c74972a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867799323500, "endTime": 20867799336300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "logId": "4df3466c-8c04-4512-98e5-dc394711da7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df3466c-8c04-4512-98e5-dc394711da7b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867799323500, "endTime": 20867799336300}, "additional": {"logType": "info", "children": [], "durationId": "44a8f8e0-c757-4bf8-8bd1-d7e71c74972a", "parent": "672b1dab-a8bd-4247-a2e5-2fd14577d322"}}, {"head": {"id": "672b1dab-a8bd-4247-a2e5-2fd14577d322", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867629574900, "endTime": 20867799346700}, "additional": {"logType": "info", "children": ["7369244b-1644-45cc-8a52-217326370ece", "0bf6c4c7-da2e-4a9f-8e22-8fcb662a8cde", "4df3466c-8c04-4512-98e5-dc394711da7b"], "durationId": "401a61e8-e4bb-4ae7-b0d2-1b6f9e8ad23b", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "e60d0799-5a92-42fe-b24a-d3ee3eb91eb9", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867800571500, "endTime": 20867824322000}, "additional": {"children": ["90f9fe83-5ff7-43c6-bfc8-b79744569f59", "44a16541-f14d-4cd8-8b31-82492b16f35d", "766fa4e4-c9f7-4215-8a15-e8a1a730da3f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1baf0227-7b3a-4461-945c-593688ecb3aa", "logId": "f4e70145-4e34-422c-bbc2-e8b204a57685"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90f9fe83-5ff7-43c6-bfc8-b79744569f59", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867804184800, "endTime": 20867804205000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e60d0799-5a92-42fe-b24a-d3ee3eb91eb9", "logId": "e6985aec-1518-459d-bd8c-e8b79e736159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6985aec-1518-459d-bd8c-e8b79e736159", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867804184800, "endTime": 20867804205000}, "additional": {"logType": "info", "children": [], "durationId": "90f9fe83-5ff7-43c6-bfc8-b79744569f59", "parent": "f4e70145-4e34-422c-bbc2-e8b204a57685"}}, {"head": {"id": "44a16541-f14d-4cd8-8b31-82492b16f35d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867806371500, "endTime": 20867822944200}, "additional": {"children": ["d7f681be-085a-434a-bc17-f876acd9992d", "5cb33adf-df72-4fc6-9906-4a0afbf7c204"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e60d0799-5a92-42fe-b24a-d3ee3eb91eb9", "logId": "7b472f38-0e7f-4015-8022-d375bcfe71a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7f681be-085a-434a-bc17-f876acd9992d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867806372900, "endTime": 20867809456600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44a16541-f14d-4cd8-8b31-82492b16f35d", "logId": "a04dd85a-6593-41dc-9bfe-4a31a3a7e2cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cb33adf-df72-4fc6-9906-4a0afbf7c204", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867809471900, "endTime": 20867822921800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44a16541-f14d-4cd8-8b31-82492b16f35d", "logId": "b0ab5513-57ef-4884-b316-1c8172cd6773"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c0628e4-d12b-4f61-bedf-1a0105d0eb7a", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867806377800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ba260e-9482-4487-97ab-fe94edc0e856", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867809339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04dd85a-6593-41dc-9bfe-4a31a3a7e2cc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867806372900, "endTime": 20867809456600}, "additional": {"logType": "info", "children": [], "durationId": "d7f681be-085a-434a-bc17-f876acd9992d", "parent": "7b472f38-0e7f-4015-8022-d375bcfe71a6"}}, {"head": {"id": "248db24b-5357-463f-b65c-509468197cec", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867809481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bf1e0c-9029-4335-9a0c-1fcbaa94d3d4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867817367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44684b12-559f-46ff-a991-011b4e36db25", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867817600300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a627c0-faf8-4df2-b9af-9fe21e9eeb6a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867817906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c094412c-2e5e-41cc-bf28-10781d0262ef", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867818143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "871132ac-8929-4eeb-83e4-d997050b8580", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867818253900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7fe8069-68bc-429a-924e-43db345fac6a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867818355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa68c6d-6586-442c-9b30-35f4921095e9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867818449200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02350af8-01b3-424e-bc46-0676a45362bf", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867822646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ab0a4d-b59d-4354-8d8b-45ead5aba64f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867822771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e19d83-dbaa-4a17-a15f-4c8fe5e4cdf1", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867822830100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ef9bfc-b243-4933-b154-404a990045be", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867822877700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0ab5513-57ef-4884-b316-1c8172cd6773", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867809471900, "endTime": 20867822921800}, "additional": {"logType": "info", "children": [], "durationId": "5cb33adf-df72-4fc6-9906-4a0afbf7c204", "parent": "7b472f38-0e7f-4015-8022-d375bcfe71a6"}}, {"head": {"id": "7b472f38-0e7f-4015-8022-d375bcfe71a6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867806371500, "endTime": 20867822944200}, "additional": {"logType": "info", "children": ["a04dd85a-6593-41dc-9bfe-4a31a3a7e2cc", "b0ab5513-57ef-4884-b316-1c8172cd6773"], "durationId": "44a16541-f14d-4cd8-8b31-82492b16f35d", "parent": "f4e70145-4e34-422c-bbc2-e8b204a57685"}}, {"head": {"id": "766fa4e4-c9f7-4215-8a15-e8a1a730da3f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867824298900, "endTime": 20867824310100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e60d0799-5a92-42fe-b24a-d3ee3eb91eb9", "logId": "876e533a-f6b9-40af-abed-6f8778e6e42a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "876e533a-f6b9-40af-abed-6f8778e6e42a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867824298900, "endTime": 20867824310100}, "additional": {"logType": "info", "children": [], "durationId": "766fa4e4-c9f7-4215-8a15-e8a1a730da3f", "parent": "f4e70145-4e34-422c-bbc2-e8b204a57685"}}, {"head": {"id": "f4e70145-4e34-422c-bbc2-e8b204a57685", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867800571500, "endTime": 20867824322000}, "additional": {"logType": "info", "children": ["e6985aec-1518-459d-bd8c-e8b79e736159", "7b472f38-0e7f-4015-8022-d375bcfe71a6", "876e533a-f6b9-40af-abed-6f8778e6e42a"], "durationId": "e60d0799-5a92-42fe-b24a-d3ee3eb91eb9", "parent": "07cf29f4-b643-4d73-a85c-08ff01673c5d"}}, {"head": {"id": "07cf29f4-b643-4d73-a85c-08ff01673c5d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867799359500, "endTime": 20867824332500}, "additional": {"logType": "info", "children": ["f4e70145-4e34-422c-bbc2-e8b204a57685"], "durationId": "1baf0227-7b3a-4461-945c-593688ecb3aa", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "a0d500de-d433-43c4-83d1-ac5524536f86", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867862603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "011d5f4a-92f6-4383-8894-5bece4ee3396", "name": "hvigorfile, resolve hvigorfile dependencies in 39 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867862937800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece628ad-1265-4e99-9a6d-9bc0d9482711", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867824338900, "endTime": 20867863013900}, "additional": {"logType": "info", "children": [], "durationId": "11eed542-359c-4a68-92ef-5009f000bfc0", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "154680a3-bf5e-4ee6-9612-3b9a298c1d63", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867863855400, "endTime": 20867864027600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "logId": "e01f58e1-dd7a-4696-a5b1-c572e8b83070"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1ef44ed-98ac-4a04-bf56-d75e5174d3cc", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867863875700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e01f58e1-dd7a-4696-a5b1-c572e8b83070", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867863855400, "endTime": 20867864027600}, "additional": {"logType": "info", "children": [], "durationId": "154680a3-bf5e-4ee6-9612-3b9a298c1d63", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "519feb09-37a7-4380-a5e4-83f76eca95ca", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867865522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd535877-f974-4c2a-86a7-8cc98a147717", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867872056800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a484b7-381f-4397-b0d6-2f278207fd2a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867864038900, "endTime": 20867872888700}, "additional": {"logType": "info", "children": [], "durationId": "7a01ff79-a809-4a6e-85ed-193094007485", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "f5d8a80d-ea27-4afc-be17-807d556f2c24", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867872920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f5935ad-0fde-4e21-8111-18867f5fcebc", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867878616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88dd319-194d-43a9-b0f6-6cd3bfc583e7", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867878718700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efee8b9e-2a9f-4a59-9393-9ab51be053a5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867878912400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9db9f5-5061-4d74-a831-b7f116e4477c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867881237700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c952389b-b36e-4861-888a-0cbfd06dfae0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867881324200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdec0f66-3402-47ec-8d66-e87d499384b9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867872909000, "endTime": 20867884758400}, "additional": {"logType": "info", "children": [], "durationId": "30a3f129-8714-4dad-9077-b934e9fa4387", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "8cc1a9c6-fac2-42bf-a576-fe596eda262e", "name": "Configuration phase cost:260 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867884801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b2a942-ddd6-4bcd-b2c7-0b94ec75ac33", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867884777500, "endTime": 20867884909900}, "additional": {"logType": "info", "children": [], "durationId": "4ce7e6e4-f727-4958-9bda-d4780f5496a3", "parent": "29d597c6-dbc3-4cc8-8b21-76782014767f"}}, {"head": {"id": "29d597c6-dbc3-4cc8-8b21-76782014767f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867619057000, "endTime": 20867884920100}, "additional": {"logType": "info", "children": ["29982ad3-d2cc-4212-be6f-5d55e9eb92b4", "2a552973-6782-465b-ac79-28291341f6ed", "672b1dab-a8bd-4247-a2e5-2fd14577d322", "07cf29f4-b643-4d73-a85c-08ff01673c5d", "ece628ad-1265-4e99-9a6d-9bc0d9482711", "43a484b7-381f-4397-b0d6-2f278207fd2a", "bdec0f66-3402-47ec-8d66-e87d499384b9", "94b2a942-ddd6-4bcd-b2c7-0b94ec75ac33", "e01f58e1-dd7a-4696-a5b1-c572e8b83070"], "durationId": "9c744481-0b10-400b-ba21-4c2e8ffd557b", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "09fc0d26-7721-4391-8b8b-179f68605205", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867893702700, "endTime": 20867893717700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711ed035-1926-433b-b6b5-495b93870681", "logId": "d2254f1e-a2df-43c7-ab17-8e2216d11302"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2254f1e-a2df-43c7-ab17-8e2216d11302", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867893702700, "endTime": 20867893717700}, "additional": {"logType": "info", "children": [], "durationId": "09fc0d26-7721-4391-8b8b-179f68605205", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "88ad4e3c-9453-4f6d-aa8f-3c62f21b65e7", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867884942900, "endTime": 20867893729300}, "additional": {"logType": "info", "children": [], "durationId": "dedaf0da-4302-4e61-9222-8fabd37cb684", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "b286e674-67e0-4c1a-9638-d811a1e0c5f0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867893733800, "endTime": 20867893735300}, "additional": {"logType": "info", "children": [], "durationId": "3740a7be-39bb-42cf-9661-000d982b05ff", "parent": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8"}}, {"head": {"id": "bf7c6e9d-c3a7-45f0-99a4-f596773ebdc8", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867403780600, "endTime": 20867893738400}, "additional": {"logType": "info", "children": ["6b144727-0390-409d-b964-c120b06ceecb", "29d597c6-dbc3-4cc8-8b21-76782014767f", "88ad4e3c-9453-4f6d-aa8f-3c62f21b65e7", "b286e674-67e0-4c1a-9638-d811a1e0c5f0", "077041bc-734e-44a5-bb74-33691179a9e7", "7f124802-6c10-4b2a-94be-92908e332a78", "d2254f1e-a2df-43c7-ab17-8e2216d11302"], "durationId": "711ed035-1926-433b-b6b5-495b93870681"}}, {"head": {"id": "3a79efb7-20b5-4b6f-9f5c-80ae74d6b7da", "name": "Configuration task cost before running: 494 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867893874300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10915cb1-ada1-4be5-b1a9-c2a4dc795025", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867905797300, "endTime": 20867914565600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c4cfbc77-9dbc-4834-8b5c-8b73b52d28e6", "logId": "2ffb0523-edd9-4483-a453-b7d2b2b1e631"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4cfbc77-9dbc-4834-8b5c-8b73b52d28e6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867895324300}, "additional": {"logType": "detail", "children": [], "durationId": "10915cb1-ada1-4be5-b1a9-c2a4dc795025"}}, {"head": {"id": "5b8543f5-0c5a-43fc-b8af-f061663ae8fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867895880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87413c18-e8e6-4959-9c34-a401b5b91221", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867896003500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f46a59-01a9-4ac5-abc6-b1dd1c2ca2b0", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867905812000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49868e8-f750-4274-a288-038d35a96211", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867914307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91bbf7e-69f7-40e8-9f89-e82210dd2e96", "name": "entry : default@PreBuild cost memory 0.2706146240234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867914492500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffb0523-edd9-4483-a453-b7d2b2b1e631", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867905797300, "endTime": 20867914565600}, "additional": {"logType": "info", "children": [], "durationId": "10915cb1-ada1-4be5-b1a9-c2a4dc795025"}}, {"head": {"id": "169d684d-5241-4fc8-a1d6-b79b6a086f50", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867921341100, "endTime": 20867935295400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "84d0e64f-3cd4-4761-a6ca-4d6cb80f0c81", "logId": "d451f9ad-28ac-473c-9ad7-99d0a19c73be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84d0e64f-3cd4-4761-a6ca-4d6cb80f0c81", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867919427500}, "additional": {"logType": "detail", "children": [], "durationId": "169d684d-5241-4fc8-a1d6-b79b6a086f50"}}, {"head": {"id": "64b9a988-bdb8-4067-8fcf-9d944299be2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867920164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b290f3b9-edf9-45dc-9e38-cc30809b9480", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867920286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68892ba8-7114-4739-8341-4a32ba9031c0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867921352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59f6cb8-0a43-453a-951d-9bef5db1bed9", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867924223000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307be40b-91a4-4f9d-80a4-aa8b38722233", "name": "entry : default@MergeProfile cost memory 0.1058197021484375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867924403700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d451f9ad-28ac-473c-9ad7-99d0a19c73be", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867921341100, "endTime": 20867935295400}, "additional": {"logType": "info", "children": [], "durationId": "169d684d-5241-4fc8-a1d6-b79b6a086f50"}}, {"head": {"id": "08c62a22-3364-4296-91e3-1386e35b9d0b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867938611000, "endTime": 20867941113400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "525e2ca2-c6d3-4b15-90ff-3a31fdac969d", "logId": "07ad29f5-3a18-4dc0-b696-9f8af25721c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "525e2ca2-c6d3-4b15-90ff-3a31fdac969d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867937189900}, "additional": {"logType": "detail", "children": [], "durationId": "08c62a22-3364-4296-91e3-1386e35b9d0b"}}, {"head": {"id": "09e175b7-1c67-48c4-80d0-cbdd27ef570b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867937735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be42b5c9-0680-4378-a5ee-38c183f19be5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867937832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41429da-a113-4d0b-912e-308819456e5c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867938620800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d94a3dad-e56b-4fdc-b790-10c2a0f5a874", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867939556600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f5000d-02b7-49be-920c-2dea03f144ca", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867940933000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8fbaa5f-7d62-4c6f-be85-3117fc92c444", "name": "entry : default@CreateBuildProfile cost memory 0.0936737060546875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867941036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07ad29f5-3a18-4dc0-b696-9f8af25721c9", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867938611000, "endTime": 20867941113400}, "additional": {"logType": "info", "children": [], "durationId": "08c62a22-3364-4296-91e3-1386e35b9d0b"}}, {"head": {"id": "1d879404-eaa8-4ac1-a6b3-af98f0689151", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944806100, "endTime": 20867945188700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1bd4c093-a0f7-4873-a01f-3e22002e3e9f", "logId": "ad42dab6-aa3a-4f5e-a7f9-236ad3ac0e20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bd4c093-a0f7-4873-a01f-3e22002e3e9f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867943419400}, "additional": {"logType": "detail", "children": [], "durationId": "1d879404-eaa8-4ac1-a6b3-af98f0689151"}}, {"head": {"id": "c71381df-da0a-4603-95b5-5cf4baccdef0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867943939600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a2b5a2-e582-4e06-837f-441279a5513e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944034300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b752e192-1f3f-4ca5-8462-7d1761ba627f", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944815800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b092c167-dd4b-4eb4-8a2d-408e9b498d99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6b6fad-ee58-4fde-a3a9-81661bbed91b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c10016-ef32-43fb-907d-92cf68e26def", "name": "entry : default@PreCheckSyscap cost memory 0.0366363525390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867945057800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b8a8e6-74b0-4420-9a3d-26fab350ded6", "name": "runTaskFromQueue task cost before running: 545 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867945136200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad42dab6-aa3a-4f5e-a7f9-236ad3ac0e20", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867944806100, "endTime": 20867945188700, "totalTime": 314800}, "additional": {"logType": "info", "children": [], "durationId": "1d879404-eaa8-4ac1-a6b3-af98f0689151"}}, {"head": {"id": "72cb36ab-9016-493d-b0b4-291a5dbe8a3a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867957277600, "endTime": 20867958285800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e87d3b56-4c2f-4867-b073-c5985c01245e", "logId": "d9a30c1c-fa06-47bf-94c6-f266782f18bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e87d3b56-4c2f-4867-b073-c5985c01245e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867947893600}, "additional": {"logType": "detail", "children": [], "durationId": "72cb36ab-9016-493d-b0b4-291a5dbe8a3a"}}, {"head": {"id": "d854277f-9c40-4edc-9d0d-617fda2d2100", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867948651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861c3d38-5e27-4728-8d38-31682f60e7d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867948784000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "256d2a1b-e7cf-496f-baa0-64ab23ccc59b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867957292300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ce72c7-6cb6-407d-856a-a92d90bcdd87", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867957482900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80bc9cd-f90f-47b9-bd4a-5508f1ccfef5", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867958098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e54608-b945-4482-9219-ae2685b5a24d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.063140869140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867958189300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a30c1c-fa06-47bf-94c6-f266782f18bb", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867957277600, "endTime": 20867958285800}, "additional": {"logType": "info", "children": [], "durationId": "72cb36ab-9016-493d-b0b4-291a5dbe8a3a"}}, {"head": {"id": "3ce1522f-8780-42ea-bed5-19c147bfafa3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867961965800, "endTime": 20867963104500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8934fa78-e4b7-4f6d-911a-13fc6eacebb6", "logId": "04f7f55e-fca1-4df5-81fc-4bf9c0c68764"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8934fa78-e4b7-4f6d-911a-13fc6eacebb6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867960213300}, "additional": {"logType": "detail", "children": [], "durationId": "3ce1522f-8780-42ea-bed5-19c147bfafa3"}}, {"head": {"id": "3ad39da0-3431-4911-9d95-b57d1f97576e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867960744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7477851e-fdce-41b1-85b6-7d550c30a0be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867960840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "725dc579-d557-4f81-a575-a26878cc4ff0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867961975000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9411b0a-d016-4d18-a2e6-b5568d0e405d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867962949200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17e6271f-e8b6-4fa9-823a-ccac43739c48", "name": "entry : default@ProcessProfile cost memory 0.05385589599609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867963037400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f7f55e-fca1-4df5-81fc-4bf9c0c68764", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867961965800, "endTime": 20867963104500}, "additional": {"logType": "info", "children": [], "durationId": "3ce1522f-8780-42ea-bed5-19c147bfafa3"}}, {"head": {"id": "d02cdc5b-d88f-4270-88c9-ec73d06da8ea", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867967494700, "endTime": 20867974310400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e91fb608-6e01-447d-9392-a3c244512a2f", "logId": "bcdd4f77-837e-4104-ad5b-be050213001c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e91fb608-6e01-447d-9392-a3c244512a2f", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867964707100}, "additional": {"logType": "detail", "children": [], "durationId": "d02cdc5b-d88f-4270-88c9-ec73d06da8ea"}}, {"head": {"id": "2525993f-3a78-496f-ad41-3000437c149e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867965223400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd148bd3-5ed4-4a3b-8aba-1debfa0864fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867965326000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec8d127-6465-45cd-8f38-95573f8aad26", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867967509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "191c6b42-a931-48af-a59e-2321cb1fb679", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867974108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4d5503-09f1-4847-a85e-cc472c82453f", "name": "entry : default@ProcessRouterMap cost memory 0.18434906005859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867974237500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdd4f77-837e-4104-ad5b-be050213001c", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867967494700, "endTime": 20867974310400}, "additional": {"logType": "info", "children": [], "durationId": "d02cdc5b-d88f-4270-88c9-ec73d06da8ea"}}, {"head": {"id": "aea5a643-0d92-41aa-a1bc-0844533c5490", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867981816400, "endTime": 20867985325000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3ebab3e5-a35c-4717-8c12-e04dbdbdcf78", "logId": "9675d0a3-d087-493a-9a20-47d5c9aa2488"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ebab3e5-a35c-4717-8c12-e04dbdbdcf78", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867977392800}, "additional": {"logType": "detail", "children": [], "durationId": "aea5a643-0d92-41aa-a1bc-0844533c5490"}}, {"head": {"id": "ad334e58-13a7-4ed9-91cd-87ac689f4a35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867977975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fbec4f-1866-4f53-9a49-4b0e03e92e74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867978081400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f17ee7-4914-4e01-a0af-d7e64dbe233b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867979048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5d2707-c8c4-46ba-9c0a-5983b887e8ed", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867982928700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f060f895-47fb-4859-a95f-46cfc121b198", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867983086600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84305ec2-02ac-4004-8c51-c290f3749109", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867983175500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac1861f-14cc-4abb-ab76-d5f432da1414", "name": "entry : default@PreviewProcessResource cost memory 0.06742095947265625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867983311500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2219d544-c436-4f4a-a57e-2b6f7ac15d25", "name": "runTaskFromQueue task cost before running: 585 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867985204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9675d0a3-d087-493a-9a20-47d5c9aa2488", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867981816400, "endTime": 20867985325000, "totalTime": 1596600}, "additional": {"logType": "info", "children": [], "durationId": "aea5a643-0d92-41aa-a1bc-0844533c5490"}}, {"head": {"id": "164608b9-ab93-4869-893b-1e8af7fa7f33", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867995552300, "endTime": 20868020053900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2f01ceb8-e377-4457-b0dd-a4b398ba47f2", "logId": "729ba47a-f1b5-4343-ac52-0dbfe8e7eb54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f01ceb8-e377-4457-b0dd-a4b398ba47f2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867989595100}, "additional": {"logType": "detail", "children": [], "durationId": "164608b9-ab93-4869-893b-1e8af7fa7f33"}}, {"head": {"id": "7c76d611-ab70-485e-91fc-105573f655cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867990382400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067b23d1-fe66-4003-bd92-a96d8f066802", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867990521500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b279a3c-4603-4c9b-8560-c4298582f23b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867995569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e84a464-05c3-4259-bf68-4f675a6e2fb1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868019833600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f561dfac-8ed1-4122-8a82-46f2cee6a2d3", "name": "entry : default@GenerateLoaderJson cost memory 0.7065582275390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868019975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729ba47a-f1b5-4343-ac52-0dbfe8e7eb54", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867995552300, "endTime": 20868020053900}, "additional": {"logType": "info", "children": [], "durationId": "164608b9-ab93-4869-893b-1e8af7fa7f33"}}, {"head": {"id": "5af5d377-7a05-414e-a461-e45940db37b2", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868031436700, "endTime": 20868668450400}, "additional": {"children": ["57eb431c-6e76-4933-a214-614c4a2e312c", "b400ead7-f9f2-481d-af74-92b8b63361a1", "b164cd63-3332-4dfb-bc0b-e463d154b5a5", "b93b6667-932f-4431-aea7-4d63f687e163"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "b8d255a1-790b-45a8-bb7b-c5110964d684", "logId": "ff63d3f2-a7e5-468c-b638-6d7963701c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d255a1-790b-45a8-bb7b-c5110964d684", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868027650100}, "additional": {"logType": "detail", "children": [], "durationId": "5af5d377-7a05-414e-a461-e45940db37b2"}}, {"head": {"id": "5e2d4b75-6919-4690-b7e9-452f07c55a69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868028214800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef130099-bb7d-4a82-b606-55dddb79999f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868028300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287e8d02-4fd4-4ed4-9c07-58f0ffae343e", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868029255700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e143bad-b948-4aec-84ae-42d7aef9c932", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868031461300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433aae64-9dcf-43e7-b7e4-5bf8e958ff93", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868072674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116b39ea-33ce-4031-b2e8-bb56f6da03f0", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 41 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868072821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57eb431c-6e76-4933-a214-614c4a2e312c", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868073898000, "endTime": 20868155868800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5af5d377-7a05-414e-a461-e45940db37b2", "logId": "8f053dc5-c68d-4f2a-944c-ebb56b2c5c7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f053dc5-c68d-4f2a-944c-ebb56b2c5c7a", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868073898000, "endTime": 20868155868800}, "additional": {"logType": "info", "children": [], "durationId": "57eb431c-6e76-4933-a214-614c4a2e312c", "parent": "ff63d3f2-a7e5-468c-b638-6d7963701c3f"}}, {"head": {"id": "943f6a21-9c6c-40fb-b76e-481e11da5f62", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868156326200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b400ead7-f9f2-481d-af74-92b8b63361a1", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868157731000, "endTime": 20868338560900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5af5d377-7a05-414e-a461-e45940db37b2", "logId": "940c4362-284a-419f-8e49-20b6de4d379f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3228a87-48c8-486d-a463-4b2d4cdf131f", "name": "current process  memoryUsage: {\n  rss: 645292032,\n  heapTotal: 126181376,\n  heapUsed: 111572264,\n  external: 3108429,\n  arrayBuffers: 102294\n} os memoryUsage :7.311977386474609", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868158698100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80016c96-1eb3-49b9-b1d0-e2f1e1494788", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868277077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db845adf-d17c-4903-aeac-a01a2b1ed4ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868278231000, "endTime": 20868278252800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "7683f1d4-0c3d-4319-a9a8-11db7738e9df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7683f1d4-0c3d-4319-a9a8-11db7738e9df", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868278231000, "endTime": 20868278252800}, "additional": {"logType": "info", "children": [], "durationId": "db845adf-d17c-4903-aeac-a01a2b1ed4ce"}}, {"head": {"id": "f81d9ae5-0f3c-457a-9edd-9b61ad70c98c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868335745700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940c4362-284a-419f-8e49-20b6de4d379f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868157731000, "endTime": 20868338560900}, "additional": {"logType": "info", "children": [], "durationId": "b400ead7-f9f2-481d-af74-92b8b63361a1", "parent": "ff63d3f2-a7e5-468c-b638-6d7963701c3f"}}, {"head": {"id": "163458de-6d87-4e84-92ee-11face5484a6", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868338688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b164cd63-3332-4dfb-bc0b-e463d154b5a5", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868339883000, "endTime": 20868431668900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5af5d377-7a05-414e-a461-e45940db37b2", "logId": "4344bed0-300a-4e00-a2a0-fb8d6bca5ba2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f228d2e-bb11-4dd3-8719-75c9e98f24b9", "name": "current process  memoryUsage: {\n  rss: 645419008,\n  heapTotal: 126181376,\n  heapUsed: 111936152,\n  external: 3116747,\n  arrayBuffers: 110627\n} os memoryUsage :7.354305267333984", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868340948100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04eec8de-5894-4dd9-95b9-d26e9a5b0660", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868428478400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4344bed0-300a-4e00-a2a0-fb8d6bca5ba2", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868339883000, "endTime": 20868431668900}, "additional": {"logType": "info", "children": [], "durationId": "b164cd63-3332-4dfb-bc0b-e463d154b5a5", "parent": "ff63d3f2-a7e5-468c-b638-6d7963701c3f"}}, {"head": {"id": "ecfb89cf-5696-438c-b8b4-cd834900b3e8", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868432115700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b93b6667-932f-4431-aea7-4d63f687e163", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868434108100, "endTime": 20868667095900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5af5d377-7a05-414e-a461-e45940db37b2", "logId": "67cfe7da-7d97-4b3b-8e97-f768ec6aa2ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99b26ce0-e24d-4f5f-9e70-51b4057cfc8a", "name": "current process  memoryUsage: {\n  rss: 647974912,\n  heapTotal: 126181376,\n  heapUsed: 112226736,\n  external: 3116873,\n  arrayBuffers: 111567\n} os memoryUsage :7.340717315673828", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868435830700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9384b1-c009-44fa-9b0a-c905160ae573", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868663232500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67cfe7da-7d97-4b3b-8e97-f768ec6aa2ae", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868434108100, "endTime": 20868667095900}, "additional": {"logType": "info", "children": [], "durationId": "b93b6667-932f-4431-aea7-4d63f687e163", "parent": "ff63d3f2-a7e5-468c-b638-6d7963701c3f"}}, {"head": {"id": "c145d3a4-f53e-4b0f-949e-085163f555a9", "name": "entry : default@PreviewCompileResource cost memory 0.0835418701171875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868668166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc1b721d-c8ef-43e3-894b-05512a66cfb8", "name": "runTaskFromQueue task cost before running: 1 s 268 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868668373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff63d3f2-a7e5-468c-b638-6d7963701c3f", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868031436700, "endTime": 20868668450400, "totalTime": 636879000}, "additional": {"logType": "info", "children": ["8f053dc5-c68d-4f2a-944c-ebb56b2c5c7a", "940c4362-284a-419f-8e49-20b6de4d379f", "4344bed0-300a-4e00-a2a0-fb8d6bca5ba2", "67cfe7da-7d97-4b3b-8e97-f768ec6aa2ae"], "durationId": "5af5d377-7a05-414e-a461-e45940db37b2"}}, {"head": {"id": "1d4fcb5f-cdc7-4d51-a628-26d663aa66fe", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672584800, "endTime": 20868672943000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "17f1ce66-62af-428e-8f15-abbe677d9683", "logId": "8c0e19a0-0092-471d-b625-715200006052"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17f1ce66-62af-428e-8f15-abbe677d9683", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868671801100}, "additional": {"logType": "detail", "children": [], "durationId": "1d4fcb5f-cdc7-4d51-a628-26d663aa66fe"}}, {"head": {"id": "1963d88e-84e7-4b8f-babd-bc40cad2b4ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672388900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0724e0-cc90-43db-af62-34c4643bb3bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672492800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e1292d-fec7-4dc9-bf93-126117b27bc3", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c6f12a-0c25-454d-b5d7-6683b1bbfc5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672685300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05058b1e-ab73-47f8-b394-ddb66d52caec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0232f778-b37b-4d73-81db-84509302c591", "name": "entry : default@PreviewHookCompileResource cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672805600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a148bcd7-a762-405a-b605-d5e87140af2a", "name": "runTaskFromQueue task cost before running: 1 s 273 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0e19a0-0092-471d-b625-715200006052", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868672584800, "endTime": 20868672943000, "totalTime": 285400}, "additional": {"logType": "info", "children": [], "durationId": "1d4fcb5f-cdc7-4d51-a628-26d663aa66fe"}}, {"head": {"id": "4b45ddf5-5c07-4126-af15-f27b3b31ba29", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868676484600, "endTime": 20868684459100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "6ded698e-b8c8-4b7c-842b-a294561b44c9", "logId": "1d53b701-48e9-4423-ada5-0d8129d1bb1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ded698e-b8c8-4b7c-842b-a294561b44c9", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868675022700}, "additional": {"logType": "detail", "children": [], "durationId": "4b45ddf5-5c07-4126-af15-f27b3b31ba29"}}, {"head": {"id": "c236bfd1-8ceb-4436-bc52-4a7b31fd9d95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868675642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef77a9b0-4eea-439b-8ddb-43e0a8c68041", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868675756300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae9c87e-e360-45b9-bc15-6e65302609c3", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868676496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a2bbd4-acf1-4eee-bad5-37b8e9c63ea4", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868678048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a012df1-623c-4d74-ae22-d0b53d22ed0e", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868678159300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8643d33d-cf12-4156-b09b-320e2c78e1c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868678251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e6424e-7fba-45d8-a95d-f9f46b052a13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868678306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98107aa0-4f1a-499b-81f2-42d1a317d933", "name": "entry : default@CopyPreviewProfile cost memory 0.210723876953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868684118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20467ff6-a33c-4dfd-9a06-45982ee5f829", "name": "runTaskFromQueue task cost before running: 1 s 284 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868684333700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d53b701-48e9-4423-ada5-0d8129d1bb1c", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868676484600, "endTime": 20868684459100, "totalTime": 7805700}, "additional": {"logType": "info", "children": [], "durationId": "4b45ddf5-5c07-4126-af15-f27b3b31ba29"}}, {"head": {"id": "ecc34950-dafa-4332-8460-b279c8bb6e92", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690010200, "endTime": 20868690822500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "05df3f02-e5ed-48c1-b0e6-88d0c3827de3", "logId": "a97fd9d2-dcf2-436d-9de7-3abd9ce1c80e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05df3f02-e5ed-48c1-b0e6-88d0c3827de3", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868687733200}, "additional": {"logType": "detail", "children": [], "durationId": "ecc34950-dafa-4332-8460-b279c8bb6e92"}}, {"head": {"id": "2421111a-aa45-4795-bb62-a1158e3307be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868688437700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df34b144-c603-4c38-b96a-d0ce044a8151", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868688577800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5135adbf-748f-496e-ab60-ec2c25f2afc3", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690026500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f606e06b-f670-4d40-8277-e9a60c3f93b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690241100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedf9eb9-8649-4044-ae94-1ea0c9f15d1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690366000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2129ccc8-45b3-447c-8249-c7997a25521c", "name": "entry : default@ReplacePreviewerPage cost memory 0.0412139892578125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f713936-b516-4f0e-8542-117b12d99b7a", "name": "runTaskFromQueue task cost before running: 1 s 291 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97fd9d2-dcf2-436d-9de7-3abd9ce1c80e", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868690010200, "endTime": 20868690822500, "totalTime": 661800}, "additional": {"logType": "info", "children": [], "durationId": "ecc34950-dafa-4332-8460-b279c8bb6e92"}}, {"head": {"id": "1a057550-4129-48ab-bd8d-c0fe88ec10e8", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693655900, "endTime": 20868693998300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b38476fe-2ecb-4891-a89d-5130ea49b1ab", "logId": "6d8ac7d2-ce06-46d8-92de-86830d12248b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b38476fe-2ecb-4891-a89d-5130ea49b1ab", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693598400}, "additional": {"logType": "detail", "children": [], "durationId": "1a057550-4129-48ab-bd8d-c0fe88ec10e8"}}, {"head": {"id": "cd3ea21f-b758-48f8-babe-16acbcefe9c9", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693666700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08fbe0da-90bb-47e6-b0f7-588481142a2d", "name": "entry : buildPreviewerResource cost memory 0.01158905029296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693822500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1904ee9-9495-40ef-aa1b-a3ee5f8401e6", "name": "runTaskFromQueue task cost before running: 1 s 294 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693929700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8ac7d2-ce06-46d8-92de-86830d12248b", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868693655900, "endTime": 20868693998300, "totalTime": 250200}, "additional": {"logType": "info", "children": [], "durationId": "1a057550-4129-48ab-bd8d-c0fe88ec10e8"}}, {"head": {"id": "b94b5c40-d774-4f75-ba16-d21b52b01d42", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868698239300, "endTime": 20868702013700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "a948cde1-92ab-40b5-ab65-7d6caf73ae74", "logId": "1e77b818-7fcb-459d-8f85-b0bf2628b35f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a948cde1-92ab-40b5-ab65-7d6caf73ae74", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868696563300}, "additional": {"logType": "detail", "children": [], "durationId": "b94b5c40-d774-4f75-ba16-d21b52b01d42"}}, {"head": {"id": "4e9bd9ff-0cd4-4103-a23b-14757b5b010d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868697216800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff94ff4-6eb0-4cb2-bb31-a046636a6cec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868697335700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c404732-5d90-4b1c-9406-9ac2acf447fa", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868698267000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "100791af-336d-49bf-85d8-3117ce0fa16f", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868700572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42badb3b-01bb-4aa2-8bf6-3d5107dfc814", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868700723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf303601-20b8-44b3-b8e7-783fbb37fb7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868700825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d22d7e5-0937-4df1-baeb-afade9d3fc24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868700888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8520e2-6219-46c6-8283-830ddfb52b93", "name": "entry : default@PreviewUpdateAssets cost memory 0.14331817626953125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868701804300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd40c8a-f607-4883-97a0-fb92765fc8ce", "name": "runTaskFromQueue task cost before running: 1 s 302 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868701941700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e77b818-7fcb-459d-8f85-b0bf2628b35f", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868698239300, "endTime": 20868702013700, "totalTime": 3678300}, "additional": {"logType": "info", "children": [], "durationId": "b94b5c40-d774-4f75-ba16-d21b52b01d42"}}, {"head": {"id": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868711710700, "endTime": 20895870324300}, "additional": {"children": ["db45e966-42b7-477b-a3a7-af6e05d79f4b"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "244afbeb-f05c-4004-82f8-19ed4b5052eb", "logId": "073aeafd-bfce-4fc3-804f-3b1199022974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "244afbeb-f05c-4004-82f8-19ed4b5052eb", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868704968400}, "additional": {"logType": "detail", "children": [], "durationId": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d"}}, {"head": {"id": "4a01cdd4-7764-4d80-8706-1c27c5dc2edb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868705610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ed4d8c-5bf9-43de-91e4-012152628032", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868705723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9009aec0-1b53-4329-bdbe-0fe25de22ed7", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868711726100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db45e966-42b7-477b-a3a7-af6e05d79f4b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker18", "startTime": 20868735146900, "endTime": 20895870159300}, "additional": {"children": ["7fd0e62e-b27e-4431-9282-2172c8d4cdc7", "b5303300-fe98-479c-b4ed-38aa374c0ce7", "906f8f23-eef6-4cd7-ad93-edf81ff65012"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d", "logId": "ed444334-a6d1-4e77-9c2f-ed897242f59d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a631f1ad-60a9-4b61-8566-f294a2f8c9b0", "name": "entry : default@PreviewArkTS cost memory -0.7099990844726562", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868738456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8ff72f-5611-47b3-8d2a-bb1b921f582f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868803966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1be76d-c38f-4269-a360-9bcbe300f198", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868804937900, "endTime": 20868804956200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "dbcfe3f5-99e1-41f1-b921-ffe14fe018e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbcfe3f5-99e1-41f1-b921-ffe14fe018e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868804937900, "endTime": 20868804956200}, "additional": {"logType": "info", "children": [], "durationId": "0f1be76d-c38f-4269-a360-9bcbe300f198"}}, {"head": {"id": "451359ed-f2a4-4d11-ae04-97e6f9ac4f9c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868968841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ccedc1-e526-42a4-bbd8-fd67a57cf921", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868970777300, "endTime": 20868970809600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "4397ecea-74a4-49ce-8582-ba059a56a4b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4397ecea-74a4-49ce-8582-ba059a56a4b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868970777300, "endTime": 20868970809600}, "additional": {"logType": "info", "children": [], "durationId": "e4ccedc1-e526-42a4-bbd8-fd67a57cf921"}}, {"head": {"id": "c12654a3-574d-4f32-b1d8-81f40830304f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869014498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a587f3-1262-49b1-bc64-9cc6f00552b9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869017391700, "endTime": 20869017425900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "110608ed-4c5d-41aa-a905-f45c452162d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "110608ed-4c5d-41aa-a905-f45c452162d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869017391700, "endTime": 20869017425900}, "additional": {"logType": "info", "children": [], "durationId": "d3a587f3-1262-49b1-bc64-9cc6f00552b9"}}, {"head": {"id": "723b5469-992b-4b3a-977b-1d2a42d32926", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869140440500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20073298-55db-4607-94a2-76330c0f0f6f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869142188400, "endTime": 20869142214900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "697a4f00-af53-4804-9057-5efe3558bc85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "697a4f00-af53-4804-9057-5efe3558bc85", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869142188400, "endTime": 20869142214900}, "additional": {"logType": "info", "children": [], "durationId": "20073298-55db-4607-94a2-76330c0f0f6f"}}, {"head": {"id": "c1287d7a-5895-4fdf-a550-e63085876782", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869245619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8acebd0c-1c2b-4345-8bb3-23b8b0a04b8f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869247566400, "endTime": 20869247600400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f0291fe3-c47d-4b8a-b97e-e2583fa9bf8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0291fe3-c47d-4b8a-b97e-e2583fa9bf8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869247566400, "endTime": 20869247600400}, "additional": {"logType": "info", "children": [], "durationId": "8acebd0c-1c2b-4345-8bb3-23b8b0a04b8f"}}, {"head": {"id": "e3e88734-e4ba-47fc-9c32-c4c25ab0ba31", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869247749100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53b080a-4eb0-45f7-8fea-605ec2196c55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869250598000, "endTime": 20869250631800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5e7d8e75-e8ee-48d8-b3bd-fbd334ef29dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e7d8e75-e8ee-48d8-b3bd-fbd334ef29dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869250598000, "endTime": 20869250631800}, "additional": {"logType": "info", "children": [], "durationId": "a53b080a-4eb0-45f7-8fea-605ec2196c55"}}, {"head": {"id": "9eaa672a-e487-4c7f-b6b1-b1ad7b781f6a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869250792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c958f68-f504-449f-8c38-057895326d7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869252691600, "endTime": 20869252721800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a3754f1c-dbde-467d-9f56-486b802403a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3754f1c-dbde-467d-9f56-486b802403a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869252691600, "endTime": 20869252721800}, "additional": {"logType": "info", "children": [], "durationId": "4c958f68-f504-449f-8c38-057895326d7b"}}, {"head": {"id": "41056f54-9043-4eb5-b386-efcdd1b3f01d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869252908500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddc4146-e378-4127-a2cd-3b5f00f713af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869254546600, "endTime": 20869254575200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "6ecde7fd-eb67-46fd-baee-149bb9b710d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ecde7fd-eb67-46fd-baee-149bb9b710d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869254546600, "endTime": 20869254575200}, "additional": {"logType": "info", "children": [], "durationId": "fddc4146-e378-4127-a2cd-3b5f00f713af"}}, {"head": {"id": "ddfae841-f118-4881-81e3-d6fda6de9361", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869254710300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43491226-648b-4774-b68a-20e7b419cdea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869256260200, "endTime": 20869256287200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "fe4b9bab-92fe-4d87-b277-c3bd267345e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe4b9bab-92fe-4d87-b277-c3bd267345e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869256260200, "endTime": 20869256287200}, "additional": {"logType": "info", "children": [], "durationId": "43491226-648b-4774-b68a-20e7b419cdea"}}, {"head": {"id": "6028cc3b-587d-4269-8652-f1e6f272c9c7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869256408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430e07b8-2f86-409a-91e3-34684d8b6d7c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869258048000, "endTime": 20869258078300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "a84e89c7-151b-490e-9e50-41fed864ad62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a84e89c7-151b-490e-9e50-41fed864ad62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869258048000, "endTime": 20869258078300}, "additional": {"logType": "info", "children": [], "durationId": "430e07b8-2f86-409a-91e3-34684d8b6d7c"}}, {"head": {"id": "7ef1b650-e89d-4fe7-8598-9c5e01c2c6ef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869258220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e4c96fe-9987-4cba-bd8d-a1e40f7d222b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869260140500, "endTime": 20869260162000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "5e68002b-f5b0-4408-8594-1ec4271d944c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e68002b-f5b0-4408-8594-1ec4271d944c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869260140500, "endTime": 20869260162000}, "additional": {"logType": "info", "children": [], "durationId": "2e4c96fe-9987-4cba-bd8d-a1e40f7d222b"}}, {"head": {"id": "c9207978-ebf9-4a13-b813-8a201a2759e9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869260274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcbfa43a-1ba2-4d47-8843-747b20d0ea57", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869261489200, "endTime": 20869261510600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "1b90f221-2fab-4950-bcb6-9eafa0777a66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b90f221-2fab-4950-bcb6-9eafa0777a66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869261489200, "endTime": 20869261510600}, "additional": {"logType": "info", "children": [], "durationId": "bcbfa43a-1ba2-4d47-8843-747b20d0ea57"}}, {"head": {"id": "77149f35-4098-4ba0-8a89-d8b8a44ca8d7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869261611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89996cc1-387d-42bd-901e-5bfb56607d5f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869263019700, "endTime": 20869263046400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "be5de6fa-6d96-4e08-a631-3509a1ffb8c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5de6fa-6d96-4e08-a631-3509a1ffb8c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869263019700, "endTime": 20869263046400}, "additional": {"logType": "info", "children": [], "durationId": "89996cc1-387d-42bd-901e-5bfb56607d5f"}}, {"head": {"id": "4eb812f2-beff-4ea5-a8e6-2d442f0c9826", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869263167000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e701d0df-5ab2-4e9e-afbc-397049cd101d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869264746200, "endTime": 20869264775100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "ac64a66d-5608-4ad8-9898-c9e0b3624cb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac64a66d-5608-4ad8-9898-c9e0b3624cb9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869264746200, "endTime": 20869264775100}, "additional": {"logType": "info", "children": [], "durationId": "e701d0df-5ab2-4e9e-afbc-397049cd101d"}}, {"head": {"id": "e3717300-b509-4573-b710-5a3764781730", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869264897500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b53e01-1f6f-45b4-b54d-469fec824eff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869266873200, "endTime": 20869266904500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "bde90575-8185-4333-899b-a67cd2523353"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bde90575-8185-4333-899b-a67cd2523353", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869266873200, "endTime": 20869266904500}, "additional": {"logType": "info", "children": [], "durationId": "01b53e01-1f6f-45b4-b54d-469fec824eff"}}, {"head": {"id": "825df854-bd24-4fe0-9122-60d4cbc5cb91", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869267049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01347d0f-0ceb-4503-83ee-0e61b4e6b4e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869268612900, "endTime": 20869268637000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "45977834-8da2-4b79-9824-ceaa117d21c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45977834-8da2-4b79-9824-ceaa117d21c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869268612900, "endTime": 20869268637000}, "additional": {"logType": "info", "children": [], "durationId": "01347d0f-0ceb-4503-83ee-0e61b4e6b4e2"}}, {"head": {"id": "c1f0a890-8e11-44f8-80d3-493e9e829ce0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869268760200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490ce16d-ca0c-47db-98c8-219f8d94bc75", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869269852400, "endTime": 20869269873700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9ba496b1-aa01-49e4-9bda-e9b4defa1b23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ba496b1-aa01-49e4-9bda-e9b4defa1b23", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869269852400, "endTime": 20869269873700}, "additional": {"logType": "info", "children": [], "durationId": "490ce16d-ca0c-47db-98c8-219f8d94bc75"}}, {"head": {"id": "720da5d9-2e48-4f7c-b921-0c6b8e152f11", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869269966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958ad319-d9cb-4b1f-8cdb-29e2ebf8f780", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869271398000, "endTime": 20869271426000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f4363579-4ecc-4313-9bee-75678833d110"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4363579-4ecc-4313-9bee-75678833d110", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869271398000, "endTime": 20869271426000}, "additional": {"logType": "info", "children": [], "durationId": "958ad319-d9cb-4b1f-8cdb-29e2ebf8f780"}}, {"head": {"id": "9615fba8-6073-4adb-923f-5e900677cd7a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869271562900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7fb4a0f-7fdb-4cd9-90e9-dd0122fc7459", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869273064100, "endTime": 20869273095000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "2b38576b-d29d-4145-add2-969467b610e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b38576b-d29d-4145-add2-969467b610e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869273064100, "endTime": 20869273095000}, "additional": {"logType": "info", "children": [], "durationId": "b7fb4a0f-7fdb-4cd9-90e9-dd0122fc7459"}}, {"head": {"id": "387ad5a8-8477-4a14-b43d-99fa9f3b4848", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869352029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b811062-5a22-4b6a-b9b8-7609f00a6ec2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869353443300, "endTime": 20869353467600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9d9f304c-2211-4a74-b796-6cd3f904900b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d9f304c-2211-4a74-b796-6cd3f904900b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869353443300, "endTime": 20869353467600}, "additional": {"logType": "info", "children": [], "durationId": "4b811062-5a22-4b6a-b9b8-7609f00a6ec2"}}, {"head": {"id": "49fdf6a1-5062-41f5-937b-e0ec47667b8e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869456102500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429f62ae-930c-417f-abfc-7f70621c0ab1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869457586000, "endTime": 20869457610100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "9807bce0-358e-44e8-a1f5-52cc32192a2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9807bce0-358e-44e8-a1f5-52cc32192a2f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869457586000, "endTime": 20869457610100}, "additional": {"logType": "info", "children": [], "durationId": "429f62ae-930c-417f-abfc-7f70621c0ab1"}}, {"head": {"id": "68166ed9-4f4c-4545-a7d9-335d0639d0f1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869457723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb2204f-f4d1-4e41-a4f1-def684b1b33d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869459266300, "endTime": 20869459294200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "d70279e7-6bb9-41f7-be0a-138d3725a13c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d70279e7-6bb9-41f7-be0a-138d3725a13c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869459266300, "endTime": 20869459294200}, "additional": {"logType": "info", "children": [], "durationId": "ceb2204f-f4d1-4e41-a4f1-def684b1b33d"}}, {"head": {"id": "e29a270a-0d85-4d73-8346-aeaa97ec1d73", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869459416800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9c1f9f-1a4e-4c2b-92f8-13f790e5aa39", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869460732900, "endTime": 20869460775900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "c9216c3e-83c4-4085-b3c7-f0423b2de8ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9216c3e-83c4-4085-b3c7-f0423b2de8ba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20869460732900, "endTime": 20869460775900}, "additional": {"logType": "info", "children": [], "durationId": "2e9c1f9f-1a4e-4c2b-92f8-13f790e5aa39"}}, {"head": {"id": "0be94d17-2056-4290-844f-706d10231b2f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870004084800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e1de16-7fbb-42a7-aa5f-5949a20febf0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870006285200, "endTime": 20870006316900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "6f68c09e-b255-4e73-a9d8-99bf8c6c67b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f68c09e-b255-4e73-a9d8-99bf8c6c67b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870006285200, "endTime": 20870006316900}, "additional": {"logType": "info", "children": [], "durationId": "66e1de16-7fbb-42a7-aa5f-5949a20febf0"}}, {"head": {"id": "9b5562b7-c05c-4e1b-8048-6d54c54cee6e", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870105898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9228c575-56d0-4c9d-9d2b-fd51b83243b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870107092800, "endTime": 20870107112400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0143a11e-a6b6-4fd1-a0b7-a64309fdc2c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0143a11e-a6b6-4fd1-a0b7-a64309fdc2c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870107092800, "endTime": 20870107112400}, "additional": {"logType": "info", "children": [], "durationId": "9228c575-56d0-4c9d-9d2b-fd51b83243b3"}}, {"head": {"id": "2e794d3f-24ad-4fff-a3eb-ced747eb28c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870732249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54624565-729f-41a3-a3a4-deb532112f7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870733802000, "endTime": 20870733827200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "553aea45-8498-4807-bf78-3dd9a8899412"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "553aea45-8498-4807-bf78-3dd9a8899412", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20870733802000, "endTime": 20870733827200}, "additional": {"logType": "info", "children": [], "durationId": "54624565-729f-41a3-a3a4-deb532112f7b"}}, {"head": {"id": "08807686-dabe-48e5-984f-3ca63b0e8d39", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20871480021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283307b1-1197-4a6a-91ae-a752906c62bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20871481069600, "endTime": 20871481100800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "3b8ac137-5c47-484b-b617-c7c684279357"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b8ac137-5c47-484b-b617-c7c684279357", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20871481069600, "endTime": 20871481100800}, "additional": {"logType": "info", "children": [], "durationId": "283307b1-1197-4a6a-91ae-a752906c62bc"}}, {"head": {"id": "77efd1fe-99e0-4e9b-831c-fb3ca1170b53", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20882617767000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fd0e62e-b27e-4431-9282-2172c8d4cdc7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20882620176200, "endTime": 20882620214600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db45e966-42b7-477b-a3a7-af6e05d79f4b", "logId": "293fcd4f-337a-4c69-a5af-0dc0c7eb77d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "293fcd4f-337a-4c69-a5af-0dc0c7eb77d2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20882620176200, "endTime": 20882620214600}, "additional": {"logType": "info", "children": [], "durationId": "7fd0e62e-b27e-4431-9282-2172c8d4cdc7", "parent": "ed444334-a6d1-4e77-9c2f-ed897242f59d"}}, {"head": {"id": "fe0be7c6-bddb-4486-89b1-6e485d43ef40", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20890791016100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5303300-fe98-479c-b4ed-38aa374c0ce7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20890794419900, "endTime": 20890794470100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db45e966-42b7-477b-a3a7-af6e05d79f4b", "logId": "b92284af-bac2-41d1-a5ef-20cc16825006"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b92284af-bac2-41d1-a5ef-20cc16825006", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20890794419900, "endTime": 20890794470100}, "additional": {"logType": "info", "children": [], "durationId": "b5303300-fe98-479c-b4ed-38aa374c0ce7", "parent": "ed444334-a6d1-4e77-9c2f-ed897242f59d"}}, {"head": {"id": "dc696a3b-2e92-46c5-959c-a39741dec7e9", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895869088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "906f8f23-eef6-4cd7-ad93-edf81ff65012", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895870078700, "endTime": 20895870097200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db45e966-42b7-477b-a3a7-af6e05d79f4b", "logId": "6ec369a6-fb8b-4fb7-8e4b-55299f855c03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ec369a6-fb8b-4fb7-8e4b-55299f855c03", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895870078700, "endTime": 20895870097200}, "additional": {"logType": "info", "children": [], "durationId": "906f8f23-eef6-4cd7-ad93-edf81ff65012", "parent": "ed444334-a6d1-4e77-9c2f-ed897242f59d"}}, {"head": {"id": "ed444334-a6d1-4e77-9c2f-ed897242f59d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker18", "startTime": 20868735146900, "endTime": 20895870159300}, "additional": {"logType": "error", "children": ["293fcd4f-337a-4c69-a5af-0dc0c7eb77d2", "b92284af-bac2-41d1-a5ef-20cc16825006", "6ec369a6-fb8b-4fb7-8e4b-55299f855c03"], "durationId": "db45e966-42b7-477b-a3a7-af6e05d79f4b", "parent": "073aeafd-bfce-4fc3-804f-3b1199022974"}}, {"head": {"id": "91ddd2f0-1def-4440-a21b-e4be4dd18c82", "name": "default@PreviewArkTS watch work[18] failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895870187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "073aeafd-bfce-4fc3-804f-3b1199022974", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20868711710700, "endTime": 20895870324300}, "additional": {"logType": "error", "children": ["ed444334-a6d1-4e77-9c2f-ed897242f59d"], "durationId": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d"}}, {"head": {"id": "7928b218-b045-493d-9345-243c0ad09f68", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895870432500}, "additional": {"logType": "debug", "children": [], "durationId": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d"}}, {"head": {"id": "e46d4d6c-a10a-462d-8e16-8ebedbab0e93", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: F:/e-wallet/harmony/entry/src/main/ets/api/TransactionApi.ets:118:54\n Argument of type 'PaymentFormData' is not assignable to parameter of type 'Record<string, string>'.\n  Index signature for type 'string' is missing in type 'PaymentFormData'.\n\n    at handleResponse (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (E:\\hongmeng\\HM\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895890653400}, "additional": {"logType": "debug", "children": [], "durationId": "83d205ff-0e2b-4d86-b245-2d65c18b3d5d"}}, {"head": {"id": "734e51e9-0bc5-405a-93a6-6acabe9dc994", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895924325500, "endTime": 20895924412100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a5ebbd2d-76ba-4cdb-96e1-0fa0a8ba5b83", "logId": "ef59a21c-4cbd-4181-afc6-1926683d1aba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef59a21c-4cbd-4181-afc6-1926683d1aba", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895924325500, "endTime": 20895924412100}, "additional": {"logType": "info", "children": [], "durationId": "734e51e9-0bc5-405a-93a6-6acabe9dc994"}}, {"head": {"id": "885f63e8-8677-41c6-ae2c-2d1049ea1756", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20867400473200, "endTime": 20895924597000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 21, "minute": 38}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "f8c10dd2-c6d6-4a62-a598-65d86d5702d5", "name": "BUILD FAILED in 28 s 524 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895924642500}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "e4b4e101-cb2f-45d6-b2e1-a3695619787e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895924966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ecb217-c870-45b0-94be-2ee18fb7894c", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df67970e-38d6-43c2-b026-34043e163bef", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925172800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93e2767-7843-4034-a2d5-1e445ea9f727", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ecd5a67-01bb-469f-aeba-6ad74a791a33", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79db0554-cde9-4e25-90a5-db9a8b4585ca", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca18fea7-b2ba-4e83-96aa-ac18ffb014a6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895925553200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b116dbd2-2e61-4791-b292-674f7d32eebf", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895927334100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f129d12-27ae-4a79-aac6-fe06ea2654a8", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895943012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc60941-c7dc-4e81-86d8-8f466727605a", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895943478200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "651ba612-90a3-497c-a654-6ab2f9c17448", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895956470200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e62848-cf81-4717-a46d-22108172c405", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:32 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895957359200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d323fe-9b83-4c4c-ab09-514c6cb54b7d", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895957616800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3da018-5991-40cc-8d84-3b4b79e0e4d1", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895958492800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53d6c1f-a8f7-4dc8-9ab3-6dfdfacb3898", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895959336700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63435c56-9757-453a-8f7a-793d897a2170", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895959702100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d2fb498-cb3f-4618-8313-f4670074e8d1", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895960057900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f90383e-b0f9-4e69-9836-b56de9c31bbb", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895960379700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45649e95-937e-4e36-8008-3844c5e7ac59", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895963260800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2144d89-6488-4861-a4e1-5ce36b7fcd80", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895964062700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707b6ff5-8b67-485a-a450-6a77f8d80d61", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895964352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e90ba7-b3f1-46de-ba36-f4d7cd291a22", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895964630000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b9af29c-817e-4b10-92bb-bf1cc7429600", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895965434900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0ca4a7e-e0d6-489d-8410-69a66eebc168", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895977232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc862ff-0e51-4f02-8baf-8244ffe72048", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895977798600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b77e8f6-ec16-426b-96dc-e6a7e488b237", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895978169100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488f676b-3452-4ff8-a8c2-ed31e7259ee6", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895978523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98c1189-d6d9-4f7b-a68f-39c6eb533cc8", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 20895978901300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}