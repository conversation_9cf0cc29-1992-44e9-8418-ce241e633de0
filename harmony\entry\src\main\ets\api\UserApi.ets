import { httpClient } from '../common/http/HttpClient';
import {
  UserInfo,
  UserLoginRequest,
  UserLoginResponse,
  UserRegisterRequest,
  UpdatePayPasswordRequest,
  UpdatePayLimitRequest
} from '../common/types/index';

/**
 * 用户相关API接口
 */
export class UserApi {
  
  /**
   * 用户登录
   */
  static async login(loginData: UserLoginRequest): Promise<UserLoginResponse> {
    const response = await httpClient.post<UserLoginResponse>('/user/login', loginData);
    return response.data;
  }

  /**
   * 用户注册
   */
  static async register(registerData: UserRegisterRequest): Promise<void> {
    await httpClient.post<void>('/user/register', registerData);
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo(): Promise<UserInfo> {
    const response = await httpClient.get<UserInfo>('/user/info');
    return response.data;
  }

  /**
   * 修改支付密码
   */
  static async updatePayPassword(data: UpdatePayPasswordRequest): Promise<void> {
    await httpClient.put<void>('/user/pay-password', data);
  }

  /**
   * 设置支付限额
   */
  static async updatePayLimit(data: UpdatePayLimitRequest): Promise<void> {
    await httpClient.put<void>('/user/pay-limit', data);
  }
}

// 类型定义已移至 common/types/index.ets
