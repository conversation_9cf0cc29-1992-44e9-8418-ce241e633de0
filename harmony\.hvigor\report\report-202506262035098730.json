{"version": "2.0", "ppid": 13336, "events": [{"head": {"id": "72dd6ce1-12fd-4cb6-afc8-3634e896e803", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097875524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7847d782-da8a-47d0-a113-5d870c45d446", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098556159700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098564726400, "endTime": 17098828962200}, "additional": {"children": ["99713f0e-672b-4643-94d9-0569f5ddd22b", "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "1c456a42-ceaf-43fa-9a69-64cffbb41bdc", "1e66827b-e99c-4b3e-aac1-46920928f7bd", "1edf2012-a465-4ef3-aaad-d1a2fc5bc88e", "cd597d95-a6fe-47c3-aaa6-eb0122c8e283", "8d93a200-d5a6-4aa9-86ca-089e54444e26"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3794fd26-cd72-4e77-a82d-15363595d49f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99713f0e-672b-4643-94d9-0569f5ddd22b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098564728000, "endTime": 17098579092100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "554da15d-6579-417c-85f8-f056bb4767ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098579116500, "endTime": 17098826699500}, "additional": {"children": ["213e5f7c-ce3e-430c-8357-1d5615259074", "28d00a7a-271a-45c8-9fcd-f2bd8f85c514", "01bdcf69-de8a-4816-99ae-95446cb5fd55", "0d91b906-eaa5-4e4a-bb91-ee2595d26f76", "da3835ed-3cdb-4433-a08d-1e15610121df", "bf9b0735-a4e2-4047-a19b-04e9f6736ab1", "37ac12b1-0ed4-4bb2-8f9c-66658c48b54a", "14d03049-4ad2-48ff-8fce-f15b929f65ed", "af8681b6-f4ba-412f-8ff0-f1550f9ac7b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "16881683-4915-4af7-84df-3a6afaa93c1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c456a42-ceaf-43fa-9a69-64cffbb41bdc", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098826747800, "endTime": 17098828944600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "ef4ff9a1-09a3-47ed-8e1d-9cb327ffa12e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e66827b-e99c-4b3e-aac1-46920928f7bd", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098828953400, "endTime": 17098828956000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "87830977-ec8c-48b0-9b77-90eb3d74c6b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1edf2012-a465-4ef3-aaad-d1a2fc5bc88e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098567827100, "endTime": 17098567854200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "c1b3457a-ffad-4882-82e2-d45d3252328b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1b3457a-ffad-4882-82e2-d45d3252328b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098567827100, "endTime": 17098567854200}, "additional": {"logType": "info", "children": [], "durationId": "1edf2012-a465-4ef3-aaad-d1a2fc5bc88e", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "cd597d95-a6fe-47c3-aaa6-eb0122c8e283", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098573004500, "endTime": 17098573021700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "b1c02721-f7ff-425a-8788-a8a670084ce1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1c02721-f7ff-425a-8788-a8a670084ce1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098573004500, "endTime": 17098573021700}, "additional": {"logType": "info", "children": [], "durationId": "cd597d95-a6fe-47c3-aaa6-eb0122c8e283", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "df1020f5-f8ed-4b79-9def-bdf576e3063a", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098573065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc27dd04-ef1b-44a9-be13-46da8dab59f3", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098578920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "554da15d-6579-417c-85f8-f056bb4767ab", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098564728000, "endTime": 17098579092100}, "additional": {"logType": "info", "children": [], "durationId": "99713f0e-672b-4643-94d9-0569f5ddd22b", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "213e5f7c-ce3e-430c-8357-1d5615259074", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098588729800, "endTime": 17098588743500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "7688a0e5-50ec-45b6-be74-e50e1a614a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28d00a7a-271a-45c8-9fcd-f2bd8f85c514", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098588768400, "endTime": 17098596613900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "993ad7d1-f0c8-421f-97c7-9a87ac744982"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01bdcf69-de8a-4816-99ae-95446cb5fd55", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098596635400, "endTime": 17098728697100}, "additional": {"children": ["10fb04a8-d246-45a0-8ca1-a9653c73ee96", "d88c69ae-f3ea-4449-afdd-b5485535ed7e", "71165e90-5b7f-4b8b-a4c7-d8fc10f5e9ad"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "d9837f7f-e874-4c68-a602-cf81e4fdcb51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d91b906-eaa5-4e4a-bb91-ee2595d26f76", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098728713900, "endTime": 17098760516800}, "additional": {"children": ["224d5a63-d9e1-4074-ac13-2b2603d59544"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "1d9b8ce1-f33b-4f4c-a676-a135d20beff1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da3835ed-3cdb-4433-a08d-1e15610121df", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098760530600, "endTime": 17098794662600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "b68cd1e2-e76d-4da4-8011-585b7b6bea0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf9b0735-a4e2-4047-a19b-04e9f6736ab1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098796884200, "endTime": 17098811290600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "cf404181-bf5d-478b-bfe3-d447af8cd856"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37ac12b1-0ed4-4bb2-8f9c-66658c48b54a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098811325400, "endTime": 17098826364800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "002403c9-3956-4098-bf49-90a89949219b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14d03049-4ad2-48ff-8fce-f15b929f65ed", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098826396700, "endTime": 17098826623900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "5ce94574-e6fc-4f51-9a7c-8188a347c623"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7688a0e5-50ec-45b6-be74-e50e1a614a29", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098588729800, "endTime": 17098588743500}, "additional": {"logType": "info", "children": [], "durationId": "213e5f7c-ce3e-430c-8357-1d5615259074", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "993ad7d1-f0c8-421f-97c7-9a87ac744982", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098588768400, "endTime": 17098596613900}, "additional": {"logType": "info", "children": [], "durationId": "28d00a7a-271a-45c8-9fcd-f2bd8f85c514", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "10fb04a8-d246-45a0-8ca1-a9653c73ee96", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098597981600, "endTime": 17098598007400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01bdcf69-de8a-4816-99ae-95446cb5fd55", "logId": "694b47b5-c2c8-47f9-93dd-7d00ba2c743d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "694b47b5-c2c8-47f9-93dd-7d00ba2c743d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098597981600, "endTime": 17098598007400}, "additional": {"logType": "info", "children": [], "durationId": "10fb04a8-d246-45a0-8ca1-a9653c73ee96", "parent": "d9837f7f-e874-4c68-a602-cf81e4fdcb51"}}, {"head": {"id": "d88c69ae-f3ea-4449-afdd-b5485535ed7e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098602399800, "endTime": 17098727467500}, "additional": {"children": ["951df523-6be4-4955-9f34-8da98ff0ac7e", "0262b730-ac3d-4f8a-a22e-e1350c964d27"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01bdcf69-de8a-4816-99ae-95446cb5fd55", "logId": "d47e69e3-65d3-4e92-8d8b-31d1e0f92079"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "951df523-6be4-4955-9f34-8da98ff0ac7e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098602402400, "endTime": 17098606607100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d88c69ae-f3ea-4449-afdd-b5485535ed7e", "logId": "718d8c59-5eaa-4b99-9809-13a8b051ed65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0262b730-ac3d-4f8a-a22e-e1350c964d27", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098606634300, "endTime": 17098727441700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d88c69ae-f3ea-4449-afdd-b5485535ed7e", "logId": "c11d9783-6d0a-49a5-a4c2-68f1e2d6de8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66415042-9411-4dfc-b9e3-74626e90e866", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098602408800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0434c6b-61c5-449f-a9df-2bc5b5fe4581", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098606448900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718d8c59-5eaa-4b99-9809-13a8b051ed65", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098602402400, "endTime": 17098606607100}, "additional": {"logType": "info", "children": [], "durationId": "951df523-6be4-4955-9f34-8da98ff0ac7e", "parent": "d47e69e3-65d3-4e92-8d8b-31d1e0f92079"}}, {"head": {"id": "d649c071-b8bf-4a2e-9e2e-ee56b51fcc9b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098606654300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db361efd-3e67-4294-91d5-7f1c43f0f790", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098617342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626d9c13-8a6d-4a8e-9488-1d33c2825dd1", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098617463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877d957d-6e50-4209-80c2-d71ba2722373", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098617593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "846f5989-10a8-4e7f-8382-5f320931e58f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098618399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dba36f6-8c1b-408d-9fc7-bf3726f848ad", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098620378400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "713adad6-08e8-499c-b55c-1bd921e2525e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098624385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f4091e-015c-496c-bc89-31497f9c0906", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098649629100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f4a50b-0307-43cb-b9bd-7db657cb3735", "name": "Sdk init in 66 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098692072400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d33501-a466-497a-b14b-8856a0edea95", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098692291700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "markType": "other"}}, {"head": {"id": "ad618ccb-98dc-44fd-ba77-b64f1978b416", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098692314500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "markType": "other"}}, {"head": {"id": "2ae2fbcc-6800-4e23-a7af-d134470ac355", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098726884700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e67d4beb-9515-4784-9074-d05f7f472f1a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098727084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6a385d-9a02-4826-9591-7a8d7e78f4ef", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098727239400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44b61ec-2c2a-4a49-9ac3-1030a7a2d11d", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098727345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11d9783-6d0a-49a5-a4c2-68f1e2d6de8a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098606634300, "endTime": 17098727441700}, "additional": {"logType": "info", "children": [], "durationId": "0262b730-ac3d-4f8a-a22e-e1350c964d27", "parent": "d47e69e3-65d3-4e92-8d8b-31d1e0f92079"}}, {"head": {"id": "d47e69e3-65d3-4e92-8d8b-31d1e0f92079", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098602399800, "endTime": 17098727467500}, "additional": {"logType": "info", "children": ["718d8c59-5eaa-4b99-9809-13a8b051ed65", "c11d9783-6d0a-49a5-a4c2-68f1e2d6de8a"], "durationId": "d88c69ae-f3ea-4449-afdd-b5485535ed7e", "parent": "d9837f7f-e874-4c68-a602-cf81e4fdcb51"}}, {"head": {"id": "71165e90-5b7f-4b8b-a4c7-d8fc10f5e9ad", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098728660000, "endTime": 17098728678900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01bdcf69-de8a-4816-99ae-95446cb5fd55", "logId": "6aee48ab-bd4f-4c78-8f68-23bf78d96f3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6aee48ab-bd4f-4c78-8f68-23bf78d96f3e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098728660000, "endTime": 17098728678900}, "additional": {"logType": "info", "children": [], "durationId": "71165e90-5b7f-4b8b-a4c7-d8fc10f5e9ad", "parent": "d9837f7f-e874-4c68-a602-cf81e4fdcb51"}}, {"head": {"id": "d9837f7f-e874-4c68-a602-cf81e4fdcb51", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098596635400, "endTime": 17098728697100}, "additional": {"logType": "info", "children": ["694b47b5-c2c8-47f9-93dd-7d00ba2c743d", "d47e69e3-65d3-4e92-8d8b-31d1e0f92079", "6aee48ab-bd4f-4c78-8f68-23bf78d96f3e"], "durationId": "01bdcf69-de8a-4816-99ae-95446cb5fd55", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "224d5a63-d9e1-4074-ac13-2b2603d59544", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098729761300, "endTime": 17098760495300}, "additional": {"children": ["174b7bda-4aa4-4519-afae-9bcc68438d88", "015ae7c9-d877-434a-b6be-671f62b0f84a", "43cafafa-e621-4ca4-8b12-cbda3c5b9f53"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0d91b906-eaa5-4e4a-bb91-ee2595d26f76", "logId": "3423114b-f5b6-4099-b150-087d23f57f97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "174b7bda-4aa4-4519-afae-9bcc68438d88", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098734849600, "endTime": 17098734871400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "224d5a63-d9e1-4074-ac13-2b2603d59544", "logId": "5605ab03-faf9-4e3e-b70d-4cbc17ef2545"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5605ab03-faf9-4e3e-b70d-4cbc17ef2545", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098734849600, "endTime": 17098734871400}, "additional": {"logType": "info", "children": [], "durationId": "174b7bda-4aa4-4519-afae-9bcc68438d88", "parent": "3423114b-f5b6-4099-b150-087d23f57f97"}}, {"head": {"id": "015ae7c9-d877-434a-b6be-671f62b0f84a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098739448700, "endTime": 17098757679400}, "additional": {"children": ["310c93d9-277f-4c9c-8371-187bc9f9cb36", "fc408044-6241-4662-8169-7a34b61cb042"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "224d5a63-d9e1-4074-ac13-2b2603d59544", "logId": "04ba92f1-0789-4272-8e9d-b9618d13321e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "310c93d9-277f-4c9c-8371-187bc9f9cb36", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098739450300, "endTime": 17098743260400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "015ae7c9-d877-434a-b6be-671f62b0f84a", "logId": "bc5f7f5e-53ee-434c-9fd7-cd50a923e16a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc408044-6241-4662-8169-7a34b61cb042", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098743301600, "endTime": 17098757663900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "015ae7c9-d877-434a-b6be-671f62b0f84a", "logId": "a751ac3e-7592-4c07-8846-f4bc83880bfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51a83d01-127e-4550-ac56-4e493da68af0", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098739455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ee3286-fccc-43da-aa7b-d0e68c244fdf", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098743025900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5f7f5e-53ee-434c-9fd7-cd50a923e16a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098739450300, "endTime": 17098743260400}, "additional": {"logType": "info", "children": [], "durationId": "310c93d9-277f-4c9c-8371-187bc9f9cb36", "parent": "04ba92f1-0789-4272-8e9d-b9618d13321e"}}, {"head": {"id": "a7db334d-2eca-4683-91c1-e9c217f5661f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098743320600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddff7de0-845d-40c4-998f-90293c5e4ce9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753294800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f58cd2c-63b2-48fa-88b1-a9ba0d2e460f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753425400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe7475f7-5a8a-4420-89df-292c6b9a75f5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753619000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61616ab-45f6-4e82-a26b-14d285025032", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfcea76-d175-4103-9bf6-dd841dafe20b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753849100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf508d4-ce96-4631-8d45-c995783b4bf2", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14005fac-f1e7-42d0-893a-e365de7fe3dc", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098753974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05979ea3-d982-4293-86d3-f87544b65368", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098757296400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e8085a-dd8a-48f6-92c6-8737f7e8f2d3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098757467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ade3dd9-bda1-47e2-a3f8-1eef15373c34", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098757546500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9d18ac-a1e8-4c83-9979-520bc943a681", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098757609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a751ac3e-7592-4c07-8846-f4bc83880bfd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098743301600, "endTime": 17098757663900}, "additional": {"logType": "info", "children": [], "durationId": "fc408044-6241-4662-8169-7a34b61cb042", "parent": "04ba92f1-0789-4272-8e9d-b9618d13321e"}}, {"head": {"id": "04ba92f1-0789-4272-8e9d-b9618d13321e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098739448700, "endTime": 17098757679400}, "additional": {"logType": "info", "children": ["bc5f7f5e-53ee-434c-9fd7-cd50a923e16a", "a751ac3e-7592-4c07-8846-f4bc83880bfd"], "durationId": "015ae7c9-d877-434a-b6be-671f62b0f84a", "parent": "3423114b-f5b6-4099-b150-087d23f57f97"}}, {"head": {"id": "43cafafa-e621-4ca4-8b12-cbda3c5b9f53", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098760439500, "endTime": 17098760467800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "224d5a63-d9e1-4074-ac13-2b2603d59544", "logId": "ae3e7f44-80fa-43a7-970e-a42f0ff3b47d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae3e7f44-80fa-43a7-970e-a42f0ff3b47d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098760439500, "endTime": 17098760467800}, "additional": {"logType": "info", "children": [], "durationId": "43cafafa-e621-4ca4-8b12-cbda3c5b9f53", "parent": "3423114b-f5b6-4099-b150-087d23f57f97"}}, {"head": {"id": "3423114b-f5b6-4099-b150-087d23f57f97", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098729761300, "endTime": 17098760495300}, "additional": {"logType": "info", "children": ["5605ab03-faf9-4e3e-b70d-4cbc17ef2545", "04ba92f1-0789-4272-8e9d-b9618d13321e", "ae3e7f44-80fa-43a7-970e-a42f0ff3b47d"], "durationId": "224d5a63-d9e1-4074-ac13-2b2603d59544", "parent": "1d9b8ce1-f33b-4f4c-a676-a135d20beff1"}}, {"head": {"id": "1d9b8ce1-f33b-4f4c-a676-a135d20beff1", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098728713900, "endTime": 17098760516800}, "additional": {"logType": "info", "children": ["3423114b-f5b6-4099-b150-087d23f57f97"], "durationId": "0d91b906-eaa5-4e4a-bb91-ee2595d26f76", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "d1061b45-e981-4f38-b578-4c64ef93becc", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098793336100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034928f6-71d9-4c13-8ec5-c0dd8f0e399f", "name": "hvigorfile, resolve hvigorfile dependencies in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098794425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68cd1e2-e76d-4da4-8011-585b7b6bea0a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098760530600, "endTime": 17098794662600}, "additional": {"logType": "info", "children": [], "durationId": "da3835ed-3cdb-4433-a08d-1e15610121df", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "af8681b6-f4ba-412f-8ff0-f1550f9ac7b7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098796257600, "endTime": 17098796855800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "logId": "cb76ec28-b282-4c75-b464-79aaf917aef8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fe95d25-ccfc-49ad-923b-00fe4788699b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098796325000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb76ec28-b282-4c75-b464-79aaf917aef8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098796257600, "endTime": 17098796855800}, "additional": {"logType": "info", "children": [], "durationId": "af8681b6-f4ba-412f-8ff0-f1550f9ac7b7", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "38875b1e-7962-4337-8ca5-5075a3760d92", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098800249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b110465f-9a0e-4026-9f0c-992f415d434c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098809677400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf404181-bf5d-478b-bfe3-d447af8cd856", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098796884200, "endTime": 17098811290600}, "additional": {"logType": "info", "children": [], "durationId": "bf9b0735-a4e2-4047-a19b-04e9f6736ab1", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "76d2caea-db41-49d2-94a1-4aaa3f45bc3a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098811341600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33b81615-7526-4c09-8451-13517707ebee", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098819520500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c7a362-d0ec-48d4-99ce-4d4461194a1a", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098819652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d1d744-3548-4715-b8f7-1d9f9a675e1e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098819926500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84f17055-d71a-47ca-9828-b70422c3a8c3", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098822537300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d69c3cf-c56d-4683-8da2-ee5d3c31a7de", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098822627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002403c9-3956-4098-bf49-90a89949219b", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098811325400, "endTime": 17098826364800}, "additional": {"logType": "info", "children": [], "durationId": "37ac12b1-0ed4-4bb2-8f9c-66658c48b54a", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "cdd9b8f2-827c-40c4-bdaa-37cdc2f93441", "name": "Configuration phase cost:238 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098826434200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce94574-e6fc-4f51-9a7c-8188a347c623", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098826396700, "endTime": 17098826623900}, "additional": {"logType": "info", "children": [], "durationId": "14d03049-4ad2-48ff-8fce-f15b929f65ed", "parent": "16881683-4915-4af7-84df-3a6afaa93c1f"}}, {"head": {"id": "16881683-4915-4af7-84df-3a6afaa93c1f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098579116500, "endTime": 17098826699500}, "additional": {"logType": "info", "children": ["7688a0e5-50ec-45b6-be74-e50e1a614a29", "993ad7d1-f0c8-421f-97c7-9a87ac744982", "d9837f7f-e874-4c68-a602-cf81e4fdcb51", "1d9b8ce1-f33b-4f4c-a676-a135d20beff1", "b68cd1e2-e76d-4da4-8011-585b7b6bea0a", "cf404181-bf5d-478b-bfe3-d447af8cd856", "002403c9-3956-4098-bf49-90a89949219b", "5ce94574-e6fc-4f51-9a7c-8188a347c623", "cb76ec28-b282-4c75-b464-79aaf917aef8"], "durationId": "6f60e2ff-e772-49ca-9cf3-4ee2a3946e40", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "8d93a200-d5a6-4aa9-86ca-089e54444e26", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098828892100, "endTime": 17098828919900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6", "logId": "fa3ca4b1-204e-4cd4-bf02-279e9c5ccb36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa3ca4b1-204e-4cd4-bf02-279e9c5ccb36", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098828892100, "endTime": 17098828919900}, "additional": {"logType": "info", "children": [], "durationId": "8d93a200-d5a6-4aa9-86ca-089e54444e26", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "ef4ff9a1-09a3-47ed-8e1d-9cb327ffa12e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098826747800, "endTime": 17098828944600}, "additional": {"logType": "info", "children": [], "durationId": "1c456a42-ceaf-43fa-9a69-64cffbb41bdc", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "87830977-ec8c-48b0-9b77-90eb3d74c6b3", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098828953400, "endTime": 17098828956000}, "additional": {"logType": "info", "children": [], "durationId": "1e66827b-e99c-4b3e-aac1-46920928f7bd", "parent": "3794fd26-cd72-4e77-a82d-15363595d49f"}}, {"head": {"id": "3794fd26-cd72-4e77-a82d-15363595d49f", "name": "init", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098564726400, "endTime": 17098828962200}, "additional": {"logType": "info", "children": ["554da15d-6579-417c-85f8-f056bb4767ab", "16881683-4915-4af7-84df-3a6afaa93c1f", "ef4ff9a1-09a3-47ed-8e1d-9cb327ffa12e", "87830977-ec8c-48b0-9b77-90eb3d74c6b3", "c1b3457a-ffad-4882-82e2-d45d3252328b", "b1c02721-f7ff-425a-8788-a8a670084ce1", "fa3ca4b1-204e-4cd4-bf02-279e9c5ccb36"], "durationId": "12764a66-e7d8-46bc-a6d8-ee3da3db87c6"}}, {"head": {"id": "b343a9fc-b957-41b8-9f70-c2a9460361e2", "name": "Configuration task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098829155900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9198ec8b-7bc9-4a5e-9af1-48f7c69fbafd", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098837581000, "endTime": 17098849320800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6077c164-daa4-444d-b5a9-c7bae2e46c71", "logId": "36e0805e-bf28-4a72-b9ce-d53902d30454"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6077c164-daa4-444d-b5a9-c7bae2e46c71", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098832021900}, "additional": {"logType": "detail", "children": [], "durationId": "9198ec8b-7bc9-4a5e-9af1-48f7c69fbafd"}}, {"head": {"id": "f69618d0-5c54-4a1c-84c8-f17b8caa61e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098832985500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ece4eca-6a53-4344-a07d-2704fd7ff758", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098833120400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35bf5232-b450-4aae-a685-16aa2b602181", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098837598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c673018-0ffd-49fa-9cfc-003a000a98d1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098849115200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb0c8f51-bd24-417d-8e46-a98634d6408e", "name": "entry : default@PreBuild cost memory 0.27172088623046875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098849245700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36e0805e-bf28-4a72-b9ce-d53902d30454", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098837581000, "endTime": 17098849320800}, "additional": {"logType": "info", "children": [], "durationId": "9198ec8b-7bc9-4a5e-9af1-48f7c69fbafd"}}, {"head": {"id": "dec7df08-451e-48b4-bbb6-dd98f6f55616", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098864829900, "endTime": 17098867697100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d80dc384-b286-4fa1-b057-9a1261730ae9", "logId": "7fae062b-4503-4153-9c5f-c83f2981e05d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d80dc384-b286-4fa1-b057-9a1261730ae9", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098862882700}, "additional": {"logType": "detail", "children": [], "durationId": "dec7df08-451e-48b4-bbb6-dd98f6f55616"}}, {"head": {"id": "0aa6cabe-4eb2-43a0-adae-f4fe696d03aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098863539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2742956f-6639-45af-bb00-f09220997d0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098863657000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7de381b-5c27-4960-898f-d4163feac062", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098864851400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d19f57-4fc2-4e6a-b12e-099debe62c93", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098867426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2f19119-21ae-4f8a-a791-363792384ff6", "name": "entry : default@MergeProfile cost memory 0.11026763916015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098867589100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fae062b-4503-4153-9c5f-c83f2981e05d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098864829900, "endTime": 17098867697100}, "additional": {"logType": "info", "children": [], "durationId": "dec7df08-451e-48b4-bbb6-dd98f6f55616"}}, {"head": {"id": "e0746ef3-0ce7-4906-8a8b-c32eff381728", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098872454300, "endTime": 17098877283600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6cf8378e-29e0-4392-ad84-8cf681133b50", "logId": "7e686d55-1fed-4a8d-9c8f-9b125ddb8970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cf8378e-29e0-4392-ad84-8cf681133b50", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098869946200}, "additional": {"logType": "detail", "children": [], "durationId": "e0746ef3-0ce7-4906-8a8b-c32eff381728"}}, {"head": {"id": "e5516fe0-616e-4f84-9a20-70244c66fb65", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098870568900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1280da41-957a-4e12-8b3d-db5b90d3fe3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098870683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "028f1357-f9ca-4c5c-b124-fcf09e732318", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098872475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12beff5-428c-4ab1-a785-4c38ecf98ccb", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098874251300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9294f314-d68d-4ad2-83a3-524ae38570e0", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098876951500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3750d519-9ed6-42d9-b3d5-c0bfffa74ade", "name": "entry : default@CreateBuildProfile cost memory 0.09630584716796875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098877146200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e686d55-1fed-4a8d-9c8f-9b125ddb8970", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098872454300, "endTime": 17098877283600}, "additional": {"logType": "info", "children": [], "durationId": "e0746ef3-0ce7-4906-8a8b-c32eff381728"}}, {"head": {"id": "6c55990b-fdb2-45f9-884b-4546701e9cf3", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882271000, "endTime": 17098883581900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "48bfca38-6829-4fad-8453-0fbb3b09930e", "logId": "fd6f2a45-aa58-4f41-8075-afe9c3a6c267"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48bfca38-6829-4fad-8453-0fbb3b09930e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098879836700}, "additional": {"logType": "detail", "children": [], "durationId": "6c55990b-fdb2-45f9-884b-4546701e9cf3"}}, {"head": {"id": "ee240285-1919-4ff2-bdb8-6143238026cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098880670200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7291ba06-845c-444b-9ec6-3678eb5a2bef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098880829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4f593a1-fc41-46c6-8bc0-8e2f176a1e7b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc16c44f-9f04-48a8-b8b0-aaa8efcae115", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882476200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3ed6fb-c6c9-495d-a755-001742724ce0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65fcc14a-0f9e-4087-b1aa-4a6c5c9ea067", "name": "entry : default@PreCheckSyscap cost memory 0.0368194580078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882708600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb481f65-bbca-446e-a35f-f2672f29fc8c", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098883232200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6f2a45-aa58-4f41-8075-afe9c3a6c267", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098882271000, "endTime": 17098883581900, "totalTime": 581100}, "additional": {"logType": "info", "children": [], "durationId": "6c55990b-fdb2-45f9-884b-4546701e9cf3"}}, {"head": {"id": "8f5de6a0-3cda-478e-a367-4764c7786464", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098897642300, "endTime": 17098898748900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5cb1f44c-912a-4c08-877b-7076a02a83a9", "logId": "4c3cad5e-4e71-40c8-a457-97e2e68bde3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cb1f44c-912a-4c08-877b-7076a02a83a9", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098887168900}, "additional": {"logType": "detail", "children": [], "durationId": "8f5de6a0-3cda-478e-a367-4764c7786464"}}, {"head": {"id": "389fd946-30fd-4145-82b3-2224c89fe529", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098887995900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "055351a4-b473-4c9e-9653-3cb5f736f529", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098888469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abfb92ec-4045-4270-a09c-af2b26408e46", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098897663700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e163dd9-7928-4ced-ab32-382e7beade6c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098897879200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "229b5d4e-72f3-4fca-92b5-24cd355fbca7", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098898577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0065021f-e68c-463d-a5e9-8c2afceeb54b", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06481170654296875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098898677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3cad5e-4e71-40c8-a457-97e2e68bde3b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098897642300, "endTime": 17098898748900}, "additional": {"logType": "info", "children": [], "durationId": "8f5de6a0-3cda-478e-a367-4764c7786464"}}, {"head": {"id": "4d804ec1-dd83-4cfd-bab1-fc6fecf45fe8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098902740800, "endTime": 17098904144100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cf1587a9-8585-4fc1-a3de-df69241821dc", "logId": "e3a8752b-b5ae-4d75-baa7-ac64865ddbef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf1587a9-8585-4fc1-a3de-df69241821dc", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098900673900}, "additional": {"logType": "detail", "children": [], "durationId": "4d804ec1-dd83-4cfd-bab1-fc6fecf45fe8"}}, {"head": {"id": "a22bd4ae-e004-4d31-8029-00db51a7e790", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098901270200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "015872de-bb8e-4c77-b13e-2ef18a4694f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098901372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b5b32c-74cd-4649-adc1-1b1fea66e15b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098902751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c541cc6b-32f4-4e61-a61a-64ae699c3d3f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098903950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c40f4a95-6d93-4b8c-ad90-59ba75ab6af3", "name": "entry : default@ProcessProfile cost memory 0.056182861328125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098904064800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a8752b-b5ae-4d75-baa7-ac64865ddbef", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098902740800, "endTime": 17098904144100}, "additional": {"logType": "info", "children": [], "durationId": "4d804ec1-dd83-4cfd-bab1-fc6fecf45fe8"}}, {"head": {"id": "12c4127c-48e4-46d4-aed7-1f08a7c82f73", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098908669000, "endTime": 17098917365500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "847724f3-79fa-41b0-bcf9-5ae5e3d64895", "logId": "e2868d61-2195-4b3c-82ff-a70c81c35845"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "847724f3-79fa-41b0-bcf9-5ae5e3d64895", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098905925100}, "additional": {"logType": "detail", "children": [], "durationId": "12c4127c-48e4-46d4-aed7-1f08a7c82f73"}}, {"head": {"id": "e914fdb1-5a17-493c-8c8a-db707d5df408", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098906497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faeb09b1-b3c9-4fef-8f34-33c4132a290d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098906603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a779f71-74cf-4e14-b8a6-37946e5a50db", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098908685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78001a63-c457-436e-9eb9-13cf06f28d84", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098917161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd43ade-71cd-40b9-a91b-b395df343b0b", "name": "entry : default@ProcessRouterMap cost memory 0.1869964599609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098917290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2868d61-2195-4b3c-82ff-a70c81c35845", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098908669000, "endTime": 17098917365500}, "additional": {"logType": "info", "children": [], "durationId": "12c4127c-48e4-46d4-aed7-1f08a7c82f73"}}, {"head": {"id": "77e1745d-ee53-4211-b7fb-4f01fed7f6cc", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098932064500, "endTime": 17098937190200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8e8480a8-5ed5-41bf-b01d-842ed693459f", "logId": "a0e913d8-3d1a-4400-b748-70bf3e949591"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e8480a8-5ed5-41bf-b01d-842ed693459f", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098924547800}, "additional": {"logType": "detail", "children": [], "durationId": "77e1745d-ee53-4211-b7fb-4f01fed7f6cc"}}, {"head": {"id": "9e6da6e9-cd08-4852-a4f7-d916a0526a0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098925662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37cadd0-0a28-4522-a796-b96ee44115db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098925843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d908e29f-e073-4d42-969f-33e862ca6a4c", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098927696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc688ab1-6573-4eb8-9dbd-b241234afdce", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098934262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "844b8c13-9490-45d5-a4c2-c5001c0e64cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098934517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b1ffde-4f2d-4462-bc42-56657ae706e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098934631600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694311db-5c7c-4407-8fc4-85989ac37d68", "name": "entry : default@PreviewProcessResource cost memory 0.06778717041015625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098934763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d200807-bea9-4916-9da6-ca352bc69d2b", "name": "runTaskFromQueue task cost before running: 377 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098937015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e913d8-3d1a-4400-b748-70bf3e949591", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098932064500, "endTime": 17098937190200, "totalTime": 2809400}, "additional": {"logType": "info", "children": [], "durationId": "77e1745d-ee53-4211-b7fb-4f01fed7f6cc"}}, {"head": {"id": "4d35ce2b-a0a7-4e50-89e8-8c5bb0e2fd4b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098951331500, "endTime": 17098984953600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b59b82fd-0f45-4516-8954-42afea784f6a", "logId": "773c8a18-a078-4d93-b3c4-30c0e5372c83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b59b82fd-0f45-4516-8954-42afea784f6a", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098945126100}, "additional": {"logType": "detail", "children": [], "durationId": "4d35ce2b-a0a7-4e50-89e8-8c5bb0e2fd4b"}}, {"head": {"id": "43976ad8-4214-4c58-a8ac-dcd2519662e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098945994900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cc54e6b-2976-451d-9a8e-01ab803359b6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098946150800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0b2582-9b29-46cb-84af-caac5c26f65a", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098951352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ac5c22-eafe-45b9-b6de-cafeda1481d4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098984599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c592459-5fc9-456a-876f-0ea8812d3aef", "name": "entry : default@GenerateLoaderJson cost memory 0.7047882080078125", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098984803900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "773c8a18-a078-4d93-b3c4-30c0e5372c83", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098951331500, "endTime": 17098984953600}, "additional": {"logType": "info", "children": [], "durationId": "4d35ce2b-a0a7-4e50-89e8-8c5bb0e2fd4b"}}, {"head": {"id": "52c1222f-956b-470a-9370-3679010b707f", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099003840700, "endTime": 17099328072800}, "additional": {"children": ["c5039c1a-5e06-4b41-a796-fce60a999bb4", "70da476c-a253-4674-b09d-a7b681b1bc1b", "7813bf48-7e08-43aa-9af5-c53e387b62d7", "bfb31c83-c6fa-4d43-997d-956b65e03d2f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "23571d80-ae60-4623-ab96-1f1ea3f4abd0", "logId": "a93d597e-a1c8-4f38-94ee-2b8034947c9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23571d80-ae60-4623-ab96-1f1ea3f4abd0", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098998988300}, "additional": {"logType": "detail", "children": [], "durationId": "52c1222f-956b-470a-9370-3679010b707f"}}, {"head": {"id": "5bdae264-1245-4d4a-ae46-a85635e49449", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098999563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c103127-f835-4f34-be83-68bc1fba4e80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098999663900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a985f0-3502-4f1a-9ff8-e3ce7264da62", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099000774200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25cc503d-0fb8-4bfb-aae2-45f24a7e265d", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099003872400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a273d8-7144-4484-b721-29e7cc508015", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099033197900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c70c8aa8-727a-4632-ade7-2cd3e20ec51c", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099033431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5039c1a-5e06-4b41-a796-fce60a999bb4", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099035500200, "endTime": 17099054209100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52c1222f-956b-470a-9370-3679010b707f", "logId": "520c67ce-ea4c-487b-94d5-a5cf5c72326a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "520c67ce-ea4c-487b-94d5-a5cf5c72326a", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099035500200, "endTime": 17099054209100}, "additional": {"logType": "info", "children": [], "durationId": "c5039c1a-5e06-4b41-a796-fce60a999bb4", "parent": "a93d597e-a1c8-4f38-94ee-2b8034947c9f"}}, {"head": {"id": "eecbbb23-f0d2-49ff-ab97-8311c7c66002", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099054543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70da476c-a253-4674-b09d-a7b681b1bc1b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099055543200, "endTime": 17099114658700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52c1222f-956b-470a-9370-3679010b707f", "logId": "f3bdd964-4a11-4283-b12e-f799d9891c3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5ef6533-4faf-4322-856d-e7e4a2fdeac4", "name": "current process  memoryUsage: {\n  rss: 152662016,\n  heapTotal: 125394944,\n  heapUsed: 114100328,\n  external: 3133515,\n  arrayBuffers: 127380\n} os memoryUsage :6.7947845458984375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099056578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91e1cef3-efb8-4b33-b4b0-320eb8918229", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099111223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bdd964-4a11-4283-b12e-f799d9891c3d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099055543200, "endTime": 17099114658700}, "additional": {"logType": "info", "children": [], "durationId": "70da476c-a253-4674-b09d-a7b681b1bc1b", "parent": "a93d597e-a1c8-4f38-94ee-2b8034947c9f"}}, {"head": {"id": "d8d91bd9-5d4b-4d91-b9ba-735aeffe0b58", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099114870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7813bf48-7e08-43aa-9af5-c53e387b62d7", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099116594000, "endTime": 17099181113100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52c1222f-956b-470a-9370-3679010b707f", "logId": "38c63520-e999-42ca-9ef0-16fc693110f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "429a3ab0-2dd3-4e45-98b9-86546455ee52", "name": "current process  memoryUsage: {\n  rss: 174940160,\n  heapTotal: 125394944,\n  heapUsed: 114409168,\n  external: 3133641,\n  arrayBuffers: 127521\n} os memoryUsage :6.822620391845703", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099118153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a279f8e8-22f4-4b1d-9924-1d4b3a87dbfc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099176228000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c63520-e999-42ca-9ef0-16fc693110f4", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099116594000, "endTime": 17099181113100}, "additional": {"logType": "info", "children": [], "durationId": "7813bf48-7e08-43aa-9af5-c53e387b62d7", "parent": "a93d597e-a1c8-4f38-94ee-2b8034947c9f"}}, {"head": {"id": "f357ee92-1384-4c48-b929-0c77d874d82f", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099181424100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb31c83-c6fa-4d43-997d-956b65e03d2f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099183247700, "endTime": 17099326563300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52c1222f-956b-470a-9370-3679010b707f", "logId": "a3f0598e-33de-41a0-a766-9df8ffd4ee1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de116a64-7707-434b-bc8e-a42a1292572d", "name": "current process  memoryUsage: {\n  rss: 180563968,\n  heapTotal: 125394944,\n  heapUsed: 114681824,\n  external: 3133767,\n  arrayBuffers: 128461\n} os memoryUsage :6.827251434326172", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099184968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e985db6c-8254-444c-b45e-78b5da4428f5", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099322278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f0598e-33de-41a0-a766-9df8ffd4ee1f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099183247700, "endTime": 17099326563300}, "additional": {"logType": "info", "children": [], "durationId": "bfb31c83-c6fa-4d43-997d-956b65e03d2f", "parent": "a93d597e-a1c8-4f38-94ee-2b8034947c9f"}}, {"head": {"id": "c9b3fac8-3260-4307-a358-0f869d1e7748", "name": "entry : default@PreviewCompileResource cost memory 1.7429046630859375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099327669500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d106ac7-047e-42de-9db4-b005731dbe90", "name": "runTaskFromQueue task cost before running: 768 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099327919900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93d597e-a1c8-4f38-94ee-2b8034947c9f", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099003840700, "endTime": 17099328072800, "totalTime": 324010800}, "additional": {"logType": "info", "children": ["520c67ce-ea4c-487b-94d5-a5cf5c72326a", "f3bdd964-4a11-4283-b12e-f799d9891c3d", "38c63520-e999-42ca-9ef0-16fc693110f4", "a3f0598e-33de-41a0-a766-9df8ffd4ee1f"], "durationId": "52c1222f-956b-470a-9370-3679010b707f"}}, {"head": {"id": "d61a019d-ed14-4606-813b-306dd76948c0", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332285100, "endTime": 17099332656300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2e3d4790-d51a-47a7-97e1-4efbfcfea0fd", "logId": "73813061-abe6-44e0-9092-3ccbb4bd8ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e3d4790-d51a-47a7-97e1-4efbfcfea0fd", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099331340700}, "additional": {"logType": "detail", "children": [], "durationId": "d61a019d-ed14-4606-813b-306dd76948c0"}}, {"head": {"id": "d65317d4-0695-4a78-a096-add4a6be92c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ccc342-4c29-4847-9fed-a2f30a681918", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2064ab40-dc9f-4822-878c-929f5f38f020", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332294200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f3052d-4426-46ab-8741-30d2d8a2c317", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332387800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e49f650-1bdd-4fd3-b398-8031bbe981bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332442200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2fd126e-012a-44d0-a1ff-560892398d28", "name": "entry : default@PreviewHookCompileResource cost memory 0.037933349609375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c633eeb7-98e9-4045-bef5-27a67e9c5c2b", "name": "runTaskFromQueue task cost before running: 772 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332586500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73813061-abe6-44e0-9092-3ccbb4bd8ed9", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099332285100, "endTime": 17099332656300, "totalTime": 283500}, "additional": {"logType": "info", "children": [], "durationId": "d61a019d-ed14-4606-813b-306dd76948c0"}}, {"head": {"id": "d1bdc3a5-ebc0-4e78-9ebf-bff5a07ca140", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099336950800, "endTime": 17099347363200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "0b34dab2-cfa8-4878-96f3-92830be39698", "logId": "6fbd4bf2-a189-4199-a23f-8cb82c1895c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b34dab2-cfa8-4878-96f3-92830be39698", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099335183500}, "additional": {"logType": "detail", "children": [], "durationId": "d1bdc3a5-ebc0-4e78-9ebf-bff5a07ca140"}}, {"head": {"id": "78503fda-a43c-4687-b67c-c0f79969ac3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099335792200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2827322f-efd8-4cdd-aa42-91bbb74b19f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099335911100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb6c54f-e43a-4af1-b7aa-ad0c9b5f886e", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099336978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99971d87-4f15-4073-a18d-ab9c5001f631", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099339082800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acfa6be7-ff64-46d4-9de3-58a5ffa6a1ae", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099339297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145c399c-76af-4944-b689-5df6bb307294", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099339450400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0bad0d3-1143-482b-ba7d-360e8e84ba75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099339523200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15d41d5-48b2-4584-afa3-19e8e06009b6", "name": "entry : default@CopyPreviewProfile cost memory 0.212005615234375", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099347034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ca3a83-5d0e-493c-a8d8-cb979797df59", "name": "runTaskFromQueue task cost before running: 787 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099347251400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fbd4bf2-a189-4199-a23f-8cb82c1895c5", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099336950800, "endTime": 17099347363200, "totalTime": 10265800}, "additional": {"logType": "info", "children": [], "durationId": "d1bdc3a5-ebc0-4e78-9ebf-bff5a07ca140"}}, {"head": {"id": "86087da4-4fa5-479c-bab2-ad0c21f48f77", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099352766700, "endTime": 17099353424100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "aac50ce6-bf4c-42c3-8bfe-43f17b190091", "logId": "7aacba1c-dd30-4e96-9001-be7349da2568"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aac50ce6-bf4c-42c3-8bfe-43f17b190091", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099350620200}, "additional": {"logType": "detail", "children": [], "durationId": "86087da4-4fa5-479c-bab2-ad0c21f48f77"}}, {"head": {"id": "49046423-e49f-4973-b906-6cdbc416991a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099351398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf372429-59a0-4e85-96f8-f31ef4edaaab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099351544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571b3ead-659a-4996-832c-119a9fbe6b38", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099352777000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87fe903-8d8c-49bd-b71f-2ce5b6c76e5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099352941300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b798ad2f-317d-421c-ae35-bd2c27cfc220", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099353048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7454b0d-0f61-4252-a60e-47fc6b2f7071", "name": "entry : default@ReplacePreviewerPage cost memory 0.03787994384765625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099353191400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636818b9-192d-4897-a9f6-499193e517cf", "name": "runTaskFromQueue task cost before running: 793 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099353325900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aacba1c-dd30-4e96-9001-be7349da2568", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099352766700, "endTime": 17099353424100, "totalTime": 535500}, "additional": {"logType": "info", "children": [], "durationId": "86087da4-4fa5-479c-bab2-ad0c21f48f77"}}, {"head": {"id": "513345af-0f5a-4e5e-866e-810082d72073", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099355994200, "endTime": 17099356352400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4fe487d1-a0ba-4afd-8af0-57e0ada538a1", "logId": "165e5439-1250-4f3b-b8f9-c854d00aeb8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fe487d1-a0ba-4afd-8af0-57e0ada538a1", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099355941000}, "additional": {"logType": "detail", "children": [], "durationId": "513345af-0f5a-4e5e-866e-810082d72073"}}, {"head": {"id": "fb68ce42-c5f5-4771-b4b0-c78c126024d0", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099356002600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2709a0d6-8709-4dc1-beaa-6563d1296ff0", "name": "entry : buildPreviewerResource cost memory 0.01165008544921875", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099356144100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03538739-e4bd-47ad-8c3b-a70f8e621d4b", "name": "runTaskFromQueue task cost before running: 796 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099356261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165e5439-1250-4f3b-b8f9-c854d00aeb8f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099355994200, "endTime": 17099356352400, "totalTime": 243300}, "additional": {"logType": "info", "children": [], "durationId": "513345af-0f5a-4e5e-866e-810082d72073"}}, {"head": {"id": "76ab2783-2a06-4a9e-b17c-c957bd075a5d", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099360842300, "endTime": 17099365685300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "2e2daf33-18e9-431d-bc5a-0a8d52d558ba", "logId": "c2a4c793-e80d-4dde-9a5d-fa3e12fbbc4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e2daf33-18e9-431d-bc5a-0a8d52d558ba", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099358447400}, "additional": {"logType": "detail", "children": [], "durationId": "76ab2783-2a06-4a9e-b17c-c957bd075a5d"}}, {"head": {"id": "b3586d88-3f38-4347-a3e0-8ffc764d63b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099359106100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "026e4a2c-5625-4f49-87ea-2785b0351e04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099359264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefc9141-e058-464e-bf08-14d3551f3687", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099360863800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7e48dd-d392-49e8-82ef-e444650af311", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099363354500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21a0a25-7198-401e-be82-f38e1207f6fc", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099363475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fda5f6b-237a-42cb-a892-7001de21fb49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099363563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7fec329-67d7-45d1-808e-28a71d7c2467", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099363621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf22d8d-2aea-47cc-be35-a6b49e9801a3", "name": "entry : default@PreviewUpdateAssets cost memory -3.303863525390625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099365368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ebfb3c5-d7ef-4099-bfdc-fb2c7854e7fb", "name": "runTaskFromQueue task cost before running: 805 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099365564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a4c793-e80d-4dde-9a5d-fa3e12fbbc4e", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099360842300, "endTime": 17099365685300, "totalTime": 4683800}, "additional": {"logType": "info", "children": [], "durationId": "76ab2783-2a06-4a9e-b17c-c957bd075a5d"}}, {"head": {"id": "4b964ea4-682f-4f13-a979-5c8f97393379", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099376795100}, "additional": {"children": ["c18f0a70-b68f-44cc-9020-6236c8c7def2"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f79ab750-32d7-4fec-b531-df67af0c455f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f79ab750-32d7-4fec-b531-df67af0c455f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099369113100}, "additional": {"logType": "detail", "children": [], "durationId": "4b964ea4-682f-4f13-a979-5c8f97393379"}}, {"head": {"id": "836cee6b-1292-48ab-a867-a219adcfa1be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099369691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2047fe8-ca28-4f3a-9a40-99967c1d0743", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099369811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe887216-bb70-4fdd-b517-79bcc8152d99", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099376814500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c18f0a70-b68f-44cc-9020-6236c8c7def2", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker15", "startTime": 17099410231700}, "additional": {"children": ["270c5cf0-6281-4e64-aced-2326bbb09567"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4b964ea4-682f-4f13-a979-5c8f97393379"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "023ca219-f86e-4747-976b-8da4d46f6b61", "name": "entry : default@PreviewArkTS cost memory 0.9422378540039062", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17099412946700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3287fc5f-f664-4ddf-827d-432cd9c78603", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17106601312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5600d216-48fb-4ab8-9f44-4e420868ab93", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17106603833500, "endTime": 17106603876300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "bd907ffb-c6d4-4d16-a5e1-68e482545e5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd907ffb-c6d4-4d16-a5e1-68e482545e5b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17106603833500, "endTime": 17106603876300}, "additional": {"logType": "info", "children": [], "durationId": "5600d216-48fb-4ab8-9f44-4e420868ab93"}}, {"head": {"id": "9552a719-098b-4354-9e31-492786d987e3", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17107982902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270c5cf0-6281-4e64-aced-2326bbb09567", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17107984244300, "endTime": 17107984268900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c18f0a70-b68f-44cc-9020-6236c8c7def2", "logId": "6e08cfa2-8dff-4f49-ab6e-4fe58ae43a91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e08cfa2-8dff-4f49-ab6e-4fe58ae43a91", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17107984244300, "endTime": 17107984268900}, "additional": {"logType": "info", "children": [], "durationId": "270c5cf0-6281-4e64-aced-2326bbb09567"}}, {"head": {"id": "a18b108d-fa75-49c5-8d6f-27522600c0cc", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126683721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a46a9ba6-a40c-4e26-9cbe-a59bd6dc3e26", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17126684947300, "endTime": 17126684968300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "e3262648-6c3d-4a0f-a330-0664d330a7cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3262648-6c3d-4a0f-a330-0664d330a7cd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126684947300, "endTime": 17126684968300}, "additional": {"logType": "info", "children": [], "durationId": "a46a9ba6-a40c-4e26-9cbe-a59bd6dc3e26"}}, {"head": {"id": "7e970067-abd1-4105-baff-3cbb93faf41f", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17097779574900, "endTime": 17126685473500}, "additional": {"logType": "info", "children": ["bd907ffb-c6d4-4d16-a5e1-68e482545e5b", "e3262648-6c3d-4a0f-a330-0664d330a7cd"], "durationId": "c1ff4271-c56d-4ec3-88f4-610821983d31"}}, {"head": {"id": "3070b181-996a-42bc-996e-8f340e3007ea", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17103139098900, "endTime": 17106552081500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0d290bbd-e437-4648-be71-d42572294494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d290bbd-e437-4648-be71-d42572294494", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17103139098900, "endTime": 17106552081500}, "additional": {"logType": "info", "children": [], "durationId": "3070b181-996a-42bc-996e-8f340e3007ea"}}, {"head": {"id": "0b571008-0fb2-464b-b43f-d34bd501576b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17106552588100, "endTime": 17106552939400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "0cd579f5-edfa-4732-ad60-66c937192522"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cd579f5-edfa-4732-ad60-66c937192522", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17106552588100, "endTime": 17106552939400}, "additional": {"logType": "info", "children": [], "durationId": "0b571008-0fb2-464b-b43f-d34bd501576b"}}, {"head": {"id": "4265de9a-f6be-4a9d-9b78-de3b51c339d0", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Worker14", "startTime": 17106553258900, "endTime": 17126683769300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c1ff4271-c56d-4ec3-88f4-610821983d31", "logId": "f5af14e4-81b3-4dee-b669-e169a9d1de01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5af14e4-81b3-4dee-b669-e169a9d1de01", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17106553258900, "endTime": 17126683769300}, "additional": {"logType": "info", "children": [], "durationId": "4265de9a-f6be-4a9d-9b78-de3b51c339d0"}}, {"head": {"id": "52923240-c95d-4b28-9a73-7af6fa893196", "name": "Can not getEventById:'c1ff4271-c56d-4ec3-88f4-610821983d31' from MetricCacheService.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126689197500}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1890d2b5-124f-4c99-a993-61a8c809701b", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17097758336300, "endTime": 17126689183800, "totalTime": 28930822300}, "additional": {"logType": "info", "children": [], "durationId": "3c73dc6b-8b36-4dee-b789-60e893c67c9e"}}, {"head": {"id": "fbedaf5b-37ab-45c9-af50-0f75895d39e2", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696295000, "endTime": 17126696639200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d465c5a4-8c82-43a1-a11c-9d0a55b4b5f2", "logId": "a07aa545-be70-4a98-a8e1-3b343f4b7014"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d465c5a4-8c82-43a1-a11c-9d0a55b4b5f2", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696231700}, "additional": {"logType": "detail", "children": [], "durationId": "fbedaf5b-37ab-45c9-af50-0f75895d39e2"}}, {"head": {"id": "ed9478aa-c4b0-4c76-b3b3-ff5aaaa32abc", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696311700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "611ba7b6-e2bb-4838-9b74-1e28f16360e6", "name": "entry : PreviewBuild cost memory 0.02548980712890625", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de16b022-351b-4d90-8443-da7bc5d9fa78", "name": "runTaskFromQueue task cost before running: 28 s 136 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696576500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a07aa545-be70-4a98-a8e1-3b343f4b7014", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126696295000, "endTime": 17126696639200, "totalTime": 261800}, "additional": {"logType": "info", "children": [], "durationId": "fbedaf5b-37ab-45c9-af50-0f75895d39e2"}}, {"head": {"id": "e8bd9097-aa63-4aeb-917f-4c939a22d044", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703095800, "endTime": 17126703115700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f9f8b58-c580-4433-ae1d-d2781ef3c000", "logId": "0ca4881e-fcf0-4893-90f2-29bdb0dddfbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ca4881e-fcf0-4893-90f2-29bdb0dddfbf", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703095800, "endTime": 17126703115700}, "additional": {"logType": "info", "children": [], "durationId": "e8bd9097-aa63-4aeb-917f-4c939a22d044"}}, {"head": {"id": "5a745d45-5cf9-46ad-8c9a-e125fdd95a16", "name": "BUILD SUCCESSFUL in 28 s 143 ms ", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703156900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "57221c28-3f25-4bdc-9153-b9a34c2766c6", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17098560833600, "endTime": 17126703429400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 20, "minute": 35}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "c7e6b09c-3bc7-4bee-9b67-50a61d859a0a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703454400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a80fb63-3c33-4592-9cec-943cc87b8a14", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703526800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d610592-b89d-451f-9cb2-ef0078668799", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabfcabc-3f55-498b-9968-673b532b4b39", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703634000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc29ff20-82c5-4b21-b202-5c7b4105531f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703683900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e537d579-2afa-4836-aac7-5eab4e6811e9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703733400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63882071-5d78-4d7c-9615-609d0893044f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126703784600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df35949e-6860-4db3-8e92-4b2dbfef0fa9", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126704635100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63824abb-8998-473c-9f0b-0936ea1284bb", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126713302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d51e5f-3e82-4356-ba88-54e0b357bd43", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126713682300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e994b6a-15f9-4eba-b6fd-94dde147f30e", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126726498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "836f7aec-1376-4a47-8bc6-77af37726de0", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:24 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126727245000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad9af3e4-836e-4429-928e-aa86105d4a08", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126727498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bce39676-b4ca-420f-a1eb-0e54536b5908", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126728497400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378480e1-9131-4b15-8afd-4fca9ec8a7ac", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126729793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25ab803-f8cd-4b8a-b805-f5d5e86f6699", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126730367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0500a1d3-b75a-4bc6-a9ab-7b4a59df081e", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126730796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68a201ff-056f-4a12-a702-fd9d04360cd7", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126731274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9331a6d-d102-4d45-9301-6c351c1153f0", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126734450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0fe531d-4ff7-4e48-9564-281cea8b8e05", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126735479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814a336d-7fc2-47c7-9e39-b8df1c7ab703", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126735942100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f00ff5-818b-4955-b3a0-e29d87786ca6", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126736285700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7721019-29d1-431c-acca-2737ddbb6b5d", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126737268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42c507e-80de-44e7-ba11-32f39d2677d2", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126750524700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef64c725-297f-48b7-9620-cb921282863c", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126750929900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21978bd-ebfc-4103-96c4-1f1edeec6677", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126751272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a327fc-aa8a-4e40-b063-6876ef4a1b15", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126751644600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47eb85d7-e49f-4c57-a5ba-7f6741c034f7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:21 ms .", "description": "", "type": "log"}, "body": {"pid": 13256, "tid": "Main Thread", "startTime": 17126752034100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}