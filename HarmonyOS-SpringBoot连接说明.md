# HarmonyOS与Spring Boot连接说明

## 🎯 概述

本文档说明如何将HarmonyOS应用连接到现有的Spring Boot后端，实现转账等功能，同时保证Spring Boot代码不变。

## 🔧 后端配置

### 1. 新增HarmonyOS专用控制器

在Spring Boot项目中新增了 `HarmonyController.java`，提供HarmonyOS应用专用的API接口：

```java
@CrossOrigin
@RestController
public class HarmonyController {
    
    // 转账接口 - 对应您的axios调用
    @PostMapping("/transfer")
    public Result<String> transfer(@RequestParam Long userId,
                                   @RequestParam String toMobile,
                                   @RequestParam BigDecimal amount,
                                   @RequestParam(required = false) String tradeDesc)
    
    // 其他接口...
}
```

### 2. API接口列表

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 转账 | POST | `/transfer` | 对应您的axios代码 |
| 余额查询 | GET | `/balance/{userId}` | 获取用户余额 |
| 交易记录 | GET | `/transactions/{userId}` | 获取交易记录 |
| 银行卡列表 | GET | `/cards/{userId}` | 获取银行卡 |
| 充值 | POST | `/deposit` | 充值功能 |
| 提现 | POST | `/withdraw` | 提现功能 |
| 测试连接 | GET | `/test` | 测试API连接 |

## 📱 HarmonyOS配置

### 1. 转账服务 (TransferService.ts)

创建了专门的转账服务，使用您提供的axios调用方式：

```typescript
// 使用您的axios调用方式
const response: AxiosResponse<R<null>> = await axios({
  url: 'http://localhost:8080/transfer',
  method: 'post',
  params: {
    userId: this.user.userId,
    toMobile: this.toMobile,
    amount: parseFloat(this.toMoney),
    tradeDesc: this.tradeDesc
  }
})

let message = "request result: " + JSON.stringify(response.data);
let msg = response.data.msg
console.log(msg)

//弹窗
promptAction.showToast({
  message: msg,
  duration: 2000,
  bottom: 50
})

//把数据归零
if(response.data.code == 0){
  this.toMobile = ''
  this.toMoney = ''
  this.tradeDesc = ''
}
```

### 2. 页面组件

#### TransferPage.ets - 转账页面
- 完全按照您的代码逻辑实现
- 支持手机号、金额、转账说明输入
- 使用您的axios调用方式

#### TestConnectionPage.ets - 连接测试页面
- 测试与Spring Boot的连接
- 查询余额和交易记录
- 显示API接口信息

## 🚀 使用方法

### 1. 启动Spring Boot后端

```bash
cd Spring Boot3-e-wallet
mvn spring-boot:run
```

或者运行 `SpringEWalletApplication` 类

### 2. 配置HarmonyOS应用

1. 确保axios依赖已安装
2. 导入TransferService：
   ```typescript
   import { TransferService } from '../utils/TransferService'
   ```

3. 使用转账功能：
   ```typescript
   const success = await TransferService.transfer({
     userId: 1,
     toMobile: '13800138000',
     amount: 100.00,
     tradeDesc: '转账说明'
   })
   ```

### 3. 测试连接

使用 `TestConnectionPage` 测试与后端的连接：
- 点击"测试连接"验证API可用性
- 点击"查询余额"获取用户余额
- 点击"查询记录"获取交易记录

## 📋 API响应格式

所有API都返回统一的响应格式：

```json
{
  "code": 0,        // 0表示成功，其他表示失败
  "msg": "操作成功", // 响应消息
  "data": null      // 响应数据（可选）
}
```

## 🔍 调试信息

### 1. 后端日志
Spring Boot控制台会显示API调用日志

### 2. HarmonyOS日志
```typescript
console.log("request result: " + JSON.stringify(response.data))
console.log("request error: " + err.message)
```

### 3. 用户提示
所有操作都会通过toast显示结果给用户

## ⚠️ 注意事项

1. **网络权限**：确保HarmonyOS应用有网络访问权限
2. **服务器地址**：默认使用 `http://localhost:8080`，根据实际情况修改
3. **用户ID**：示例中使用固定用户ID=1，实际应用中应从登录状态获取
4. **错误处理**：已包含完整的错误处理和用户提示

## 🎉 完成状态

✅ Spring Boot后端接口已创建
✅ HarmonyOS转账服务已实现
✅ 完全按照您的axios代码逻辑
✅ 保持Spring Boot原有代码不变
✅ 提供完整的测试和调试功能

现在您可以在HarmonyOS应用中使用转账功能，完全按照您提供的代码方式调用Spring Boot后端！
