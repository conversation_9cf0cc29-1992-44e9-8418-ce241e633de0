<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { transactionApi } from '@/api/index.js'
import { TransactionStorage } from '@/utils/transactionStorage.js'

const payments = ref([])
const loading = ref(false)

// 获取当前用户ID
const getCurrentUserId = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user.id || 1
}

// 交易类型映射
const typeMap = {
  'PAYMENT': '支付',
  'DEPOSIT': '充值',
  'WITHDRAW': '提现',
  'TRANSFER': '转账',
  'RECEIVE': '收款'
}

// 支付方式映射
const methodMap = {
  'WALLET': '钱包',
  'BANK_CARD': '银行卡'
}

// 支付渠道映射
const channelMap = {
  'MERCHANT': '商户',
  'QR_CODE': '扫码',
  'NFC': 'NFC'
}

// 获取支付记录
const fetchPayments = async () => {
  try {
    loading.value = true
    const userId = getCurrentUserId()

    try {
      // 尝试从API获取所有交易记录，然后筛选支付记录
      const response = await transactionApi.getAllTransactions(userId)
      let allTransactions = []

      if (response && response.data && response.data.code === 200 && response.data.data) {
        allTransactions = Array.isArray(response.data.data) ? response.data.data : []
      } else if (response && response.data && Array.isArray(response.data)) {
        allTransactions = response.data
      }

      // 筛选出支付类型的交易记录
      payments.value = allTransactions.filter(transaction =>
        transaction.transactionType === 'PAYMENT'
      )

      console.log('支付记录获取成功，数量:', payments.value.length)
    } catch (apiError) {
      console.warn('API调用失败，使用本地存储数据:', apiError.message)

      // 从本地存储获取支付记录
      const localPayments = TransactionStorage.getTransactionsByType('PAYMENT')
      payments.value = localPayments
      console.log('使用本地存储支付数据，数量:', payments.value.length)
    }
  } catch (error) {
    console.error('获取支付记录失败:', error)
    payments.value = []
  } finally {
    loading.value = false
  }
}

// 格式化金额显示
const formatAmount = (amount) => {
  return `-¥${Number(amount).toFixed(2)}`
}

// 获取交易类型标签样式
const getTypeTagType = (type) => {
  return 'danger' // 支付都显示为红色
}



onMounted(() => {
  fetchPayments()
})
</script>

<template>
  <div class="payment-container">
    <div style="max-height: 600px; overflow-y: auto;">
      <el-table :data="payments" v-loading="loading" height="100%">
        <el-table-column prop="id" label="交易ID" width="120" />
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.transactionType)">
              {{ typeMap[row.transactionType] || row.transactionType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="金额" width="120">
          <template #default="{ row }">
            <span class="amount-negative">
              {{ formatAmount(row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="targetAccount" label="商户/描述" width="150" />
        <el-table-column label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="row.paymentMethod === 'WALLET' ? 'success' : 'primary'">
              {{ methodMap[row.paymentMethod] || row.paymentMethod }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付渠道" width="100">
          <template #default="{ row }">
            <span v-if="row.paymentChannel">{{ channelMap[row.paymentChannel] || row.paymentChannel }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" width="160">
          <template #default="{ row }">
            {{ new Date(row.transactionTime).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'SUCCESS' ? 'success' : row.status === 'PENDING' ? 'warning' : 'danger'">
              {{ row.status === 'SUCCESS' ? '成功' : row.status === 'PENDING' ? '处理中' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>

      </el-table>
    </div>
  </div>
</template>

<style scoped>
.payment-container {
  padding: 0;
}

.amount-negative {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
