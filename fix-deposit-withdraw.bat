@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 充值提现功能完整修复脚本
echo ========================================
echo.

echo 📋 问题诊断:
echo   ❌ 数据库缺少status字段导致充值提现失败
echo   ❌ 业务逻辑需要优化验证和错误处理
echo   ❌ 交易记录状态管理不完善
echo.

echo 🎯 修复方案:
echo   ✅ 修复数据库表结构
echo   ✅ 优化充值提现业务逻辑
echo   ✅ 完善交易状态管理
echo   ✅ 测试所有功能
echo.

REM 1. 修复数据库表结构
echo 🔍 步骤1: 修复数据库表结构...
echo ----------------------------------------

echo 正在连接MySQL数据库修复表结构...
mysql -u root -p -e "USE e_wallet; ALTER TABLE transaction ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '交易状态'; ALTER TABLE transaction ADD COLUMN IF NOT EXISTS description VARCHAR(200) COMMENT '交易描述'; UPDATE transaction SET status = 'SUCCESS' WHERE status IS NULL; DESCRIBE transaction;" 2>nul

if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，请手动执行以下SQL:
    echo.
    echo USE e_wallet;
    echo ALTER TABLE transaction ADD COLUMN IF NOT EXISTS status VARCHAR^(20^) DEFAULT 'SUCCESS' COMMENT '交易状态';
    echo ALTER TABLE transaction ADD COLUMN IF NOT EXISTS description VARCHAR^(200^) COMMENT '交易描述';
    echo UPDATE transaction SET status = 'SUCCESS' WHERE status IS NULL;
    echo.
    echo 或者使用提供的 fix-database.sql 文件
    echo.
) else (
    echo ✅ 数据库表结构修复完成
)

REM 2. 重启后端服务
echo.
echo 🔍 步骤2: 重启后端服务...
echo ----------------------------------------

echo 🛑 停止现有Java进程...
taskkill /f /im java.exe >nul 2>&1
timeout /t 3 >nul

echo 🚀 启动后端服务...
if exist "Spring Boot3-e-wallet\target\spring-e-wallet-1.0-SNAPSHOT.jar" (
    cd "Spring Boot3-e-wallet"
    start /min java -jar target\spring-e-wallet-1.0-SNAPSHOT.jar
    cd ..
    
    echo ⏳ 等待服务启动...
    for /l %%i in (1,1,20) do (
        timeout /t 2 >nul
        curl -s http://localhost:8080/bank/balance/1 >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ 后端服务启动成功！
            goto TEST_APIS
        )
        echo 等待中... %%i/20
    )
    echo ❌ 后端启动超时
) else (
    echo ❌ 未找到JAR文件，请使用IDE启动SpringEWalletApplication类
)

:TEST_APIS
echo.
echo 🔍 步骤3: 测试充值提现功能...
echo ----------------------------------------

REM 测试获取余额
echo 📊 测试获取余额API:
curl -s -X GET "http://localhost:8080/bank/balance/1" -w "\n状态码: %%{http_code}\n"

echo.
echo 📊 测试获取银行卡列表:
curl -s -X GET "http://localhost:8080/bank/cards/1" -w "\n状态码: %%{http_code}\n"

echo.
echo 💰 测试充值API (从银行卡1充值100元到钱包):
curl -s -X POST "http://localhost:8080/bank/deposit" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=1&amount=100" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo 💸 测试提现API (从钱包提现50元到银行卡1):
curl -s -X POST "http://localhost:8080/bank/withdraw" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -d "userId=1&cardId=1&amount=50" ^
     -w "\n状态码: %%{http_code}\n"

echo.
echo 📋 测试获取交易记录:
curl -s -X GET "http://localhost:8080/bank/transactions/1" -w "\n状态码: %%{http_code}\n"

echo.
echo 🔍 检查前端服务...
echo ----------------------------------------

REM 检查前端是否运行
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /i "node.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ 前端服务正在运行
) else (
    echo ❌ 前端服务未运行，正在启动...
    if exist "vue-e-wallet\vuedoem\package.json" (
        cd vue-e-wallet\vuedoem
        start /min cmd /c "npm run dev"
        cd ..\..
        echo ⏳ 等待前端启动...
        timeout /t 10 >nul
    )
)

echo.
echo 🌐 打开浏览器测试...
start http://localhost:5173

echo.
echo ========================================
echo 🎉 修复完成！功能说明:
echo ========================================
echo.
echo 💰 充值功能:
echo   - 从银行卡余额扣除指定金额
echo   - 向钱包余额增加相同金额
echo   - 生成DEPOSIT类型交易记录
echo   - 支持借记卡和信用卡
echo.
echo 💸 提现功能:
echo   - 从钱包余额扣除指定金额
echo   - 向银行卡余额增加相同金额
echo   - 生成WITHDRAW类型交易记录
echo   - 检查钱包余额是否足够
echo.
echo 🔒 安全特性:
echo   - 验证用户身份和银行卡归属
echo   - 检查余额和额度限制
echo   - 事务性操作，确保数据一致性
echo   - 详细的错误信息和状态记录
echo.
echo 📊 测试建议:
echo   1. 访问钱包页面测试充值功能
echo   2. 访问钱包页面测试提现功能
echo   3. 查看交易记录验证操作结果
echo   4. 检查余额变化是否正确
echo.
echo 🔧 如果仍有问题:
echo   1. 检查数据库连接和表结构
echo   2. 查看后端控制台错误日志
echo   3. 使用浏览器开发者工具检查API请求
echo   4. 确认银行卡数据存在且有足够余额
echo.
pause
