{"version": "2.0", "ppid": 13380, "events": [{"head": {"id": "f4e4fdf7-ed8e-4bb7-a355-d237510dca5c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313248989390400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673b0e88-3ae5-40f9-a5d4-0be70f417fdf", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313611707121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2396ffb3-6af8-457c-a483-49291ab68eb5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313612017698600, "endTime": 313612017789900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "6d72a3cf-9f7b-454a-b010-3608121b7c76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d72a3cf-9f7b-454a-b010-3608121b7c76", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313612017698600, "endTime": 313612017789900}, "additional": {"logType": "info", "children": [], "durationId": "2396ffb3-6af8-457c-a483-49291ab68eb5"}}, {"head": {"id": "393e702f-5eb3-4f3b-ae10-87277b47dcce", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623671218600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa6ae4d-1a15-4a48-a71a-1b7fce00f43e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623672678900, "endTime": 313623672721600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "01a80d09-38c4-4281-b61c-65cd2ee1146e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01a80d09-38c4-4281-b61c-65cd2ee1146e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623672678900, "endTime": 313623672721600}, "additional": {"logType": "info", "children": [], "durationId": "aaa6ae4d-1a15-4a48-a71a-1b7fce00f43e"}}, {"head": {"id": "734fc75b-96cb-404e-86f2-e6e0f8681289", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623761454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc0b1ed-38dd-42c4-bd5e-65a626110ac5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623763590200, "endTime": 313623763624100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "e1b6d6be-3c75-4f55-9f66-50add1faa132"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1b6d6be-3c75-4f55-9f66-50add1faa132", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313623763590200, "endTime": 313623763624100}, "additional": {"logType": "info", "children": [], "durationId": "5bc0b1ed-38dd-42c4-bd5e-65a626110ac5"}}, {"head": {"id": "68803abf-0624-4367-8b9d-dc9cd464edc6", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313634122705200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8d4095-864e-4558-91b8-7c41d4273db7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313634131877900, "endTime": 313634132002900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "caf845a6-6b57-4013-b2ea-b068d30635f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caf845a6-6b57-4013-b2ea-b068d30635f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313634131877900, "endTime": 313634132002900}, "additional": {"logType": "info", "children": [], "durationId": "af8d4095-864e-4558-91b8-7c41d4273db7"}}, {"head": {"id": "f8a102ab-50e4-42da-8525-9b0478418e15", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313635500299600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf616ed-8d56-4667-8330-586b7264c14a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313635517449400, "endTime": 313635517596400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "32258be4-c54b-458d-b112-90b2f6fe02ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32258be4-c54b-458d-b112-90b2f6fe02ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313635517449400, "endTime": 313635517596400}, "additional": {"logType": "info", "children": [], "durationId": "baf616ed-8d56-4667-8330-586b7264c14a"}}, {"head": {"id": "cd1ad2b9-4dad-428c-8ffb-308ba8e471be", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636527130400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d74783d-c55e-4bbd-bbcc-0ad526758acb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636528386200, "endTime": 313636528409800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "6251a905-28a7-4352-bdf2-fcb05ea98e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6251a905-28a7-4352-bdf2-fcb05ea98e62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636528386200, "endTime": 313636528409800}, "additional": {"logType": "info", "children": [], "durationId": "4d74783d-c55e-4bbd-bbcc-0ad526758acb"}}, {"head": {"id": "48a90914-abb9-4092-9493-bfc2010d20e5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636528518600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429a9d15-beea-4da1-bf04-addbd0aa360e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636529891300, "endTime": 313636529918700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "a94ff104-8b6a-46da-b396-5910c370fc63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a94ff104-8b6a-46da-b396-5910c370fc63", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636529891300, "endTime": 313636529918700}, "additional": {"logType": "info", "children": [], "durationId": "429a9d15-beea-4da1-bf04-addbd0aa360e"}}, {"head": {"id": "a52d9ce6-263c-48d6-ac58-217bead26601", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636530025600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50c78d99-c414-40f3-9692-b1969e891789", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636531438300, "endTime": 313636531469500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "73e8fa2f-dee1-4123-90e9-4927893b7bfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73e8fa2f-dee1-4123-90e9-4927893b7bfb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313636531438300, "endTime": 313636531469500}, "additional": {"logType": "info", "children": [], "durationId": "50c78d99-c414-40f3-9692-b1969e891789"}}, {"head": {"id": "cb3558af-9796-4fe5-aa7e-046b6e5edaab", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313856895939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64b7c4f-fd53-42f5-80b5-bcacd8240c56", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313857036674200, "endTime": 313857049618000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "02e3dd26-2e4a-4f83-8e1e-3bfdee5a9017"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02e3dd26-2e4a-4f83-8e1e-3bfdee5a9017", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313857036674200, "endTime": 313857049618000}, "additional": {"logType": "info", "children": [], "durationId": "d64b7c4f-fd53-42f5-80b5-bcacd8240c56"}}, {"head": {"id": "b13a8f40-03e7-4c54-afee-6306142ef84c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863624170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647b94d3-5da6-4a24-84ce-642310ef5911", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863709134000, "endTime": 313863709164800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "f2674087-3a3e-432a-a0e2-dc09b5c286a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2674087-3a3e-432a-a0e2-dc09b5c286a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863709134000, "endTime": 313863709164800}, "additional": {"logType": "info", "children": [], "durationId": "647b94d3-5da6-4a24-84ce-642310ef5911"}}, {"head": {"id": "a91876c7-e762-49dd-8ae5-03d89c3fd050", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863709266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4332937-5f64-4bf6-ab00-a7374130a58b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863710296800, "endTime": 313863710318600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "102de369-86df-48d7-b202-1d28cf6cdbe3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "102de369-86df-48d7-b202-1d28cf6cdbe3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863710296800, "endTime": 313863710318600}, "additional": {"logType": "info", "children": [], "durationId": "c4332937-5f64-4bf6-ab00-a7374130a58b"}}, {"head": {"id": "e4b06d40-49db-4383-9d50-e8b0e4e3cb14", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863710404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f3ef2a9-9cb4-4078-a679-e576c05304fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863712267100, "endTime": 313863712309900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "d75f569f-b1ca-4091-954b-572b943f9592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d75f569f-b1ca-4091-954b-572b943f9592", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313863712267100, "endTime": 313863712309900}, "additional": {"logType": "info", "children": [], "durationId": "1f3ef2a9-9cb4-4078-a679-e576c05304fe"}}, {"head": {"id": "eeabb115-37ab-40e5-ba1f-49271aa6de52", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313880448148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "becc717f-791c-4aef-be91-b9a1aaca764b", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313881878004400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ecbcd1c-ecc8-46d8-919c-2aaff093ca30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313881879553000, "endTime": 313881879600800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "89793aa7-1113-452d-b40d-e7d94cb1253a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89793aa7-1113-452d-b40d-e7d94cb1253a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313881879553000, "endTime": 313881879600800}, "additional": {"logType": "info", "children": [], "durationId": "8ecbcd1c-ecc8-46d8-919c-2aaff093ca30"}}, {"head": {"id": "ef02edaa-62e5-4749-a1d8-ab4b192b9c90", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313884004964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7626a457-0c45-4448-9bcf-aa90b243a286", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313884444194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f782211-4b92-4247-9f60-bfb6a9a56a36", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313884446505800, "endTime": 313884446543100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "4b099f8b-9b48-47c7-b154-d3e2e9261f8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b099f8b-9b48-47c7-b154-d3e2e9261f8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313884446505800, "endTime": 313884446543100}, "additional": {"logType": "info", "children": [], "durationId": "0f782211-4b92-4247-9f60-bfb6a9a56a36"}}, {"head": {"id": "705a7910-4966-4b48-b50a-2baacff3b8f0", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313903917407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cda534-a014-4d71-b3eb-f20641966557", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313904303120500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30c2891-fd07-412d-ab6b-d41496a78a15", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313904304775700, "endTime": 313904304802200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "01911d27-ce92-4870-8dff-36c1069db825"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01911d27-ce92-4870-8dff-36c1069db825", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313904304775700, "endTime": 313904304802200}, "additional": {"logType": "info", "children": [], "durationId": "e30c2891-fd07-412d-ab6b-d41496a78a15"}}, {"head": {"id": "2f204b77-9a77-4df6-b1bc-9ed0d0ccab09", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905016147200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ad4e04-ef5d-4903-b12b-6dd64cf0b9df", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905363239700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c69549a-8ca4-40aa-a8bd-f06063175125", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905365592800, "endTime": 313905365787900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "593be2a0-f1dc-4923-acf5-a147317fa51c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "593be2a0-f1dc-4923-acf5-a147317fa51c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905365592800, "endTime": 313905365787900}, "additional": {"logType": "info", "children": [], "durationId": "6c69549a-8ca4-40aa-a8bd-f06063175125"}}, {"head": {"id": "d9f595b9-472a-44fb-b859-973d1eb17b97", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905559219200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591ffa40-ff7c-498b-9b00-9d78be72830b", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905972617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9970c136-9dcb-4966-acf1-192c30c2402b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905974603900, "endTime": 313905974627500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "aca88679-aba5-40dc-b5fa-313f4c7b529f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aca88679-aba5-40dc-b5fa-313f4c7b529f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313905974603900, "endTime": 313905974627500}, "additional": {"logType": "info", "children": [], "durationId": "9970c136-9dcb-4966-acf1-192c30c2402b"}}, {"head": {"id": "b5a9be0b-e4de-4cd4-aa70-9b37940fc626", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915085651600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a46f50-faa9-40aa-9b10-d0e58cb28e17", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915476316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7b63cf-8aa2-4b62-a61c-bae50f6979ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915478941000, "endTime": 313915478970900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "0ecb0306-aa4e-401d-a21a-15177ace31dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ecb0306-aa4e-401d-a21a-15177ace31dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915478941000, "endTime": 313915478970900}, "additional": {"logType": "info", "children": [], "durationId": "da7b63cf-8aa2-4b62-a61c-bae50f6979ed"}}, {"head": {"id": "86f144d6-2e11-4db5-9b5f-4cc0b9d972aa", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915483245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f2e145c-0578-469a-b8cf-5662dbddbc85", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915885227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d33e47-318e-48bd-b4de-45af56da48cf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915886548200, "endTime": 313915886571400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "d135cddd-e255-45f8-bc0c-59b6fae2d1e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d135cddd-e255-45f8-bc0c-59b6fae2d1e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313915886548200, "endTime": 313915886571400}, "additional": {"logType": "info", "children": [], "durationId": "35d33e47-318e-48bd-b4de-45af56da48cf"}}, {"head": {"id": "d7a1a705-646c-4bef-9be3-4531ca636dcf", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313916052245500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "597d60e1-635b-488b-8d7c-27847292fb12", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313916414393400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006b3d1b-8f1a-4333-b089-ef8cb7e30dca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313916416188100, "endTime": 313916416215600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "514245b8-a69c-4589-8293-5790024e29d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "514245b8-a69c-4589-8293-5790024e29d2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313916416188100, "endTime": 313916416215600}, "additional": {"logType": "info", "children": [], "durationId": "006b3d1b-8f1a-4333-b089-ef8cb7e30dca"}}, {"head": {"id": "124997e3-5931-4932-80fd-3f47032ad320", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313943783680900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81f539b-bc64-446c-8a25-4ed98c8e3e5d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313943785040500, "endTime": 313943785063200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "0e26854c-51ce-45e8-84a8-5bf03f3bad8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e26854c-51ce-45e8-84a8-5bf03f3bad8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313943785040500, "endTime": 313943785063200}, "additional": {"logType": "info", "children": [], "durationId": "f81f539b-bc64-446c-8a25-4ed98c8e3e5d"}}, {"head": {"id": "4bcc03bd-82ed-409b-b4e4-17527630dcf8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313945835314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c965612-dad4-4117-86b9-bec305a421b4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313945837890500, "endTime": 313945837921500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "7091145c-5899-442d-8d3d-3e0a3bac2b69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7091145c-5899-442d-8d3d-3e0a3bac2b69", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313945837890500, "endTime": 313945837921500}, "additional": {"logType": "info", "children": [], "durationId": "8c965612-dad4-4117-86b9-bec305a421b4"}}, {"head": {"id": "6ecb2db2-92cb-4770-84f2-4db829e09389", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947755709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2d3834-79b0-4c12-98a6-c229c5d3f764", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947759830400, "endTime": 313947759891600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "53aac6ba-cc1d-495d-9811-f1fb2d6c63bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53aac6ba-cc1d-495d-9811-f1fb2d6c63bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947759830400, "endTime": 313947759891600}, "additional": {"logType": "info", "children": [], "durationId": "ef2d3834-79b0-4c12-98a6-c229c5d3f764"}}, {"head": {"id": "100dc7e2-d57a-4cad-9b3e-0ea334bdc739", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947760175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2890040a-bd99-48c3-ad1d-39cfe075d613", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947763854400, "endTime": 313947763924100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "d0ee69e0-8ece-4fc7-ad25-8dfd9d14a488"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0ee69e0-8ece-4fc7-ad25-8dfd9d14a488", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 313947763854400, "endTime": 313947763924100}, "additional": {"logType": "info", "children": [], "durationId": "2890040a-bd99-48c3-ad1d-39cfe075d613"}}, {"head": {"id": "166f6d66-938a-4ccf-bd81-ea1410e99507", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314100836302900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e730e35-41d4-4a80-a176-1fc048401efc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314100838657300, "endTime": 314100838699000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "93531481-2fb9-4062-b699-5e0ca4a8a351"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93531481-2fb9-4062-b699-5e0ca4a8a351", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314100838657300, "endTime": 314100838699000}, "additional": {"logType": "info", "children": [], "durationId": "9e730e35-41d4-4a80-a176-1fc048401efc"}}, {"head": {"id": "3eb7654f-9134-4f7f-9f67-1896b75641ec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103444610400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffbf2e23-8393-41fa-af95-886786fd3ea4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103445798200, "endTime": 314103445816600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "6d05bb3a-cb34-4a63-b6dc-35ae3bd86018"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d05bb3a-cb34-4a63-b6dc-35ae3bd86018", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103445798200, "endTime": 314103445816600}, "additional": {"logType": "info", "children": [], "durationId": "ffbf2e23-8393-41fa-af95-886786fd3ea4"}}, {"head": {"id": "2c117b2b-8246-4b53-b519-5f0f7eb0087a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103445905500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828a3555-90cc-40ee-933d-5e253b33cdfd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103446904700, "endTime": 314103446922100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "9c5bfaf0-933d-4eef-b2a5-aa535f0d8f7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c5bfaf0-933d-4eef-b2a5-aa535f0d8f7f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103446904700, "endTime": 314103446922100}, "additional": {"logType": "info", "children": [], "durationId": "828a3555-90cc-40ee-933d-5e253b33cdfd"}}, {"head": {"id": "00f5f4b6-3e19-4eae-ae05-ad9e5ab4ddf3", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103447023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf3b517c-0c1d-4326-ad9f-546b7dcea1e4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103449934600, "endTime": 314103449964300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "258fa985-24d9-496f-b1dd-5170d10806be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "258fa985-24d9-496f-b1dd-5170d10806be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314103449934600, "endTime": 314103449964300}, "additional": {"logType": "info", "children": [], "durationId": "cf3b517c-0c1d-4326-ad9f-546b7dcea1e4"}}, {"head": {"id": "79c14abd-c53d-4271-894c-3681256f3d70", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314113337699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a28a903a-b7b1-48cd-8b94-6fffd2af4a2a", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314113956489100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5be8a9b-3883-450f-8b3f-75b731b4a486", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314113958985400, "endTime": 314113959022300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "4362808b-fba0-47ae-b23a-0caa2aeb4b31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4362808b-fba0-47ae-b23a-0caa2aeb4b31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314113958985400, "endTime": 314113959022300}, "additional": {"logType": "info", "children": [], "durationId": "c5be8a9b-3883-450f-8b3f-75b731b4a486"}}, {"head": {"id": "a9d6ca1c-9610-4a78-a691-eea68b4de4a9", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314121705471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89c10357-3818-4e6e-8585-080f183c5fe7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314121707033800, "endTime": 314121707054800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "3c9fb057-d3b0-44cf-a02c-7058d6c01361"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c9fb057-d3b0-44cf-a02c-7058d6c01361", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314121707033800, "endTime": 314121707054800}, "additional": {"logType": "info", "children": [], "durationId": "89c10357-3818-4e6e-8585-080f183c5fe7"}}, {"head": {"id": "ccdb1b3d-3849-4d92-a68d-1dc4ae7a845a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123017505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec38460-94b6-49fb-bacb-30368d9d370d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123018753400, "endTime": 314123018773400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "30f12317-28c6-4595-bdaa-eebc92169952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30f12317-28c6-4595-bdaa-eebc92169952", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123018753400, "endTime": 314123018773400}, "additional": {"logType": "info", "children": [], "durationId": "4ec38460-94b6-49fb-bacb-30368d9d370d"}}, {"head": {"id": "c5047340-8ee3-4a84-ae24-cb1689b156b0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123270280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed24e60-3559-4ee5-a670-17bbd51bb10f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123271584400, "endTime": 314123271608700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "ecf00849-9a05-4032-8cb1-52943495a838"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecf00849-9a05-4032-8cb1-52943495a838", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123271584400, "endTime": 314123271608700}, "additional": {"logType": "info", "children": [], "durationId": "6ed24e60-3559-4ee5-a670-17bbd51bb10f"}}, {"head": {"id": "e382bb4f-889e-4a80-af1a-21c1c35fd979", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123271721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a417a5-9703-4e77-a370-ea2223225719", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123273476100, "endTime": 314123273506100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "e7fdd73a-43f3-4fd2-a58b-e754552abeba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7fdd73a-43f3-4fd2-a58b-e754552abeba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314123273476100, "endTime": 314123273506100}, "additional": {"logType": "info", "children": [], "durationId": "13a417a5-9703-4e77-a370-ea2223225719"}}, {"head": {"id": "377c95db-cd62-47d1-9da0-fe6ebf6aa480", "name": "watch worker: receive watch compile tata.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314136877925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a350c721-85d9-4458-b505-c9b087ebb127", "name": "watch worker: send response to session manager. Response type: WatchCompileResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314137409097400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0f17a1-7598-4843-9129-c0d51834091d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314137411381500, "endTime": 314137411422900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "dbc3bc52-9eea-4649-97de-a6e7fb52efa1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbc3bc52-9eea-4649-97de-a6e7fb52efa1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314137411381500, "endTime": 314137411422900}, "additional": {"logType": "info", "children": [], "durationId": "5a0f17a1-7598-4843-9129-c0d51834091d"}}, {"head": {"id": "8a2b84bd-eb71-4663-9a0f-387659c65c42", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314141070008300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef06704-8b87-454c-89cf-15bfc8d50aee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314141071168600, "endTime": 314141071186900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "167c5fbc-9994-43dd-82fa-c98d08a693c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "167c5fbc-9994-43dd-82fa-c98d08a693c1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314141071168600, "endTime": 314141071186900}, "additional": {"logType": "info", "children": [], "durationId": "0ef06704-8b87-454c-89cf-15bfc8d50aee"}}, {"head": {"id": "ac4e0ec4-90db-4c9c-b7ba-3bc4768d3e92", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142251125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2adad110-d940-41b6-b89b-978a0ec42c78", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142254181800, "endTime": 314142254214800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "4adca42f-1cac-48ff-ba02-8f515b3ba904"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4adca42f-1cac-48ff-ba02-8f515b3ba904", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142254181800, "endTime": 314142254214800}, "additional": {"logType": "info", "children": [], "durationId": "2adad110-d940-41b6-b89b-978a0ec42c78"}}, {"head": {"id": "342d474d-2947-4743-8ac2-3188530cd898", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142570463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5ae7c9-2c34-4f26-b2e3-415b66264cf8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142571740600, "endTime": 314142571760300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "d169eeda-2cc4-45b9-a02f-2ec72259ecdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d169eeda-2cc4-45b9-a02f-2ec72259ecdd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142571740600, "endTime": 314142571760300}, "additional": {"logType": "info", "children": [], "durationId": "9d5ae7c9-2c34-4f26-b2e3-415b66264cf8"}}, {"head": {"id": "4f2e9216-afc5-4b1d-a5f5-2e10268188e5", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142571843700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e619986-ddaa-42ec-b311-7dd193363359", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142572959100, "endTime": 314142572983200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78cc58-4ec0-41e1-b67c-35356914e042", "logId": "76aa484f-cbbf-4b05-b26a-15ddc9d6278d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76aa484f-cbbf-4b05-b26a-15ddc9d6278d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314142572959100, "endTime": 314142572983200}, "additional": {"logType": "info", "children": [], "durationId": "9e619986-ddaa-42ec-b311-7dd193363359"}}, {"head": {"id": "d6ae5524-791d-4f56-a0e2-cf6e945c5e18", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314253333918600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7998ec1-26ed-44f0-bebc-7f9082b9f1f7", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314253736136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c997b0f-123f-482e-bca3-d9d61e2c30f2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314259557803800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260339582000, "endTime": 314265624531900}, "additional": {"children": ["d5a7702e-01f8-42af-9332-bd3f3c242fa0", "12421f63-e177-4dde-a04c-f78cf9cb9e32", "d80e222b-03bd-4753-a077-77f32de3fe27", "88e2e120-61b7-40ec-830a-5a73bc0181ae", "cdda9849-aec1-4962-a4e3-64f70eb9299b", "9c639664-d21d-4223-a02e-62dc3a8b3b29", "cac6701d-f073-4ccc-8687-102c107c614f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5a7702e-01f8-42af-9332-bd3f3c242fa0", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260339583000, "endTime": 314261342479700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "5d265589-be5a-45b1-b2f3-ae28ef347852"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261342501400, "endTime": 314265601050200}, "additional": {"children": ["50b67d0b-06f1-4b9e-8dfa-1ed2f9d969b1", "fc5adbd8-c28e-43fb-8e56-c2c8b6639860", "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "66f53965-e497-4950-bd91-a0d6bdbe2ce4", "20b32c06-2a97-4559-af98-a63127557923", "b6f0de97-31d9-4672-a1c8-6ef61975f7df", "e2d07e0e-c60a-4317-8851-4969d3261fe4", "969dfdf4-9f58-4227-9ad3-44ab33466d63", "d537dbd9-0a55-464d-beda-8db0183c47d5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d80e222b-03bd-4753-a077-77f32de3fe27", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265601077900, "endTime": 314265623973200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "db650bb0-3a8c-406c-b29a-b0c96c178468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88e2e120-61b7-40ec-830a-5a73bc0181ae", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265623979700, "endTime": 314265624520600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "a2c3a836-7bcc-4914-917d-aa19fccf471b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdda9849-aec1-4962-a4e3-64f70eb9299b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260878218700, "endTime": 314260878260500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "ed3e8131-eb2b-46c0-b605-bf4217a64e95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed3e8131-eb2b-46c0-b605-bf4217a64e95", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260878218700, "endTime": 314260878260500}, "additional": {"logType": "info", "children": [], "durationId": "cdda9849-aec1-4962-a4e3-64f70eb9299b", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "9c639664-d21d-4223-a02e-62dc3a8b3b29", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260907748400, "endTime": 314260916070200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "c8913c44-ddf3-4d97-8032-b74ca01c05b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8913c44-ddf3-4d97-8032-b74ca01c05b9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260907748400, "endTime": 314260916070200}, "additional": {"logType": "info", "children": [], "durationId": "9c639664-d21d-4223-a02e-62dc3a8b3b29", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "fbac1607-35ce-4e7b-8642-4a93ae291233", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260936502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6464291e-c375-4b74-b612-91f7d37afe23", "name": "Cache service initialization finished in 404 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261342302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d265589-be5a-45b1-b2f3-ae28ef347852", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260339583000, "endTime": 314261342479700}, "additional": {"logType": "info", "children": [], "durationId": "d5a7702e-01f8-42af-9332-bd3f3c242fa0", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "50b67d0b-06f1-4b9e-8dfa-1ed2f9d969b1", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261349042100, "endTime": 314261349056900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "cdbdcc38-11bd-4bc6-bf82-2020ff78da36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc5adbd8-c28e-43fb-8e56-c2c8b6639860", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261349070600, "endTime": 314261405630900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "446f8ae8-762a-4377-bd18-b67354ee2fd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261405651800, "endTime": 314264066066600}, "additional": {"children": ["08131f40-d8cf-4723-9f3b-fd21ce77ed0c", "ac6b81ae-44e2-41c9-9bd2-6bc06808b2aa", "b5af92ee-15b3-4087-b25e-3ac576d0f699"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "3104520b-dd13-4d0c-9fcf-089d69cd9e36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66f53965-e497-4950-bd91-a0d6bdbe2ce4", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264066086600, "endTime": 314264342461700}, "additional": {"children": ["aace22ba-392a-4b54-b781-9be628f76b0c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "9a05ae15-b12d-4119-894a-7c097951d091"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20b32c06-2a97-4559-af98-a63127557923", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264342471400, "endTime": 314265122027900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "46d69c98-acf8-47ef-bc09-b6e6f7b682c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6f0de97-31d9-4672-a1c8-6ef61975f7df", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265123337600, "endTime": 314265310479000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "d240850f-da63-4be7-b918-00a47e394116"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2d07e0e-c60a-4317-8851-4969d3261fe4", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265310587000, "endTime": 314265600832900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "2b473d0b-2e82-4096-90c3-14e5bfde5bbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "969dfdf4-9f58-4227-9ad3-44ab33466d63", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265600861900, "endTime": 314265601035800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "310ca92f-2477-4bad-bc17-9c1041c015f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdbdcc38-11bd-4bc6-bf82-2020ff78da36", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261349042100, "endTime": 314261349056900}, "additional": {"logType": "info", "children": [], "durationId": "50b67d0b-06f1-4b9e-8dfa-1ed2f9d969b1", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "446f8ae8-762a-4377-bd18-b67354ee2fd4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261349070600, "endTime": 314261405630900}, "additional": {"logType": "info", "children": [], "durationId": "fc5adbd8-c28e-43fb-8e56-c2c8b6639860", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "08131f40-d8cf-4723-9f3b-fd21ce77ed0c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261406747000, "endTime": 314261406776300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "logId": "5fac45af-1952-4673-8ab5-0f8bc7fe767d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fac45af-1952-4673-8ab5-0f8bc7fe767d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261406747000, "endTime": 314261406776300}, "additional": {"logType": "info", "children": [], "durationId": "08131f40-d8cf-4723-9f3b-fd21ce77ed0c", "parent": "3104520b-dd13-4d0c-9fcf-089d69cd9e36"}}, {"head": {"id": "ac6b81ae-44e2-41c9-9bd2-6bc06808b2aa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261409553600, "endTime": 314264064710700}, "additional": {"children": ["4ebba9f9-1dca-4972-a4cc-50a112d0b4d5", "9430b0be-2273-4a18-83bf-a48f5b75c63b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "logId": "d4618ab3-3896-4115-ad87-1908b93fd2e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ebba9f9-1dca-4972-a4cc-50a112d0b4d5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261409555500, "endTime": 314262571690500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac6b81ae-44e2-41c9-9bd2-6bc06808b2aa", "logId": "35487584-ede8-4da1-a21b-32675c2155d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9430b0be-2273-4a18-83bf-a48f5b75c63b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262571717100, "endTime": 314264064691300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac6b81ae-44e2-41c9-9bd2-6bc06808b2aa", "logId": "21ab9c60-e488-4c8d-8b91-6ac025a4abd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68bf10dd-69e0-4c78-8839-dcbd4f4c0ed7", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261409580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7189c0-775d-4789-865b-ebd4708e6539", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262571469000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35487584-ede8-4da1-a21b-32675c2155d7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261409555500, "endTime": 314262571690500}, "additional": {"logType": "info", "children": [], "durationId": "4ebba9f9-1dca-4972-a4cc-50a112d0b4d5", "parent": "d4618ab3-3896-4115-ad87-1908b93fd2e2"}}, {"head": {"id": "87088123-531e-48ca-9721-8021a5520eca", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262571758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd248a57-51c9-4477-b673-f625eaf8db62", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262946977000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50fd491b-c7cb-45e2-8a13-92b5242ca54e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262947678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e83433-80e4-4e4f-9423-b967de1e954b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262947874900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f8fc78-efcc-460e-9d06-4e200c61eae5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262948695300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68aa6a5a-af09-4ad6-80ce-575d90d9b833", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263010271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ff63f0-28b0-4c59-83c0-3609b0ee4588", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263246618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a9b2ef8-c1e7-4f67-a88f-aa7d2c826ce3", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263648882600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94e9873c-afee-45fd-b93a-63d17b1c5b78", "name": "Sdk init in 624 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263937753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffbd1cb-98a9-4d69-8f3b-293a52bd4d26", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263937976000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 4}, "markType": "other"}}, {"head": {"id": "da2dd284-ca88-4a31-8e4d-8544754cfe6b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314263937999500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 4}, "markType": "other"}}, {"head": {"id": "26d9bc51-f422-41df-bf98-9d40a7615272", "name": "Project task initialization takes 101 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264064071800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532fd7d4-9aeb-424e-bfd8-d9de47800692", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264064383600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b77b161-435e-4272-b467-e53834ccb15a", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264064508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616f3e9d-205f-4b61-913e-3a38aaf859d1", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264064605000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ab9c60-e488-4c8d-8b91-6ac025a4abd1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314262571717100, "endTime": 314264064691300}, "additional": {"logType": "info", "children": [], "durationId": "9430b0be-2273-4a18-83bf-a48f5b75c63b", "parent": "d4618ab3-3896-4115-ad87-1908b93fd2e2"}}, {"head": {"id": "d4618ab3-3896-4115-ad87-1908b93fd2e2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261409553600, "endTime": 314264064710700}, "additional": {"logType": "info", "children": ["35487584-ede8-4da1-a21b-32675c2155d7", "21ab9c60-e488-4c8d-8b91-6ac025a4abd1"], "durationId": "ac6b81ae-44e2-41c9-9bd2-6bc06808b2aa", "parent": "3104520b-dd13-4d0c-9fcf-089d69cd9e36"}}, {"head": {"id": "b5af92ee-15b3-4087-b25e-3ac576d0f699", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264066014900, "endTime": 314264066046400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "logId": "5ff49b21-30b1-4f0d-b7cf-890ae92bee26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ff49b21-30b1-4f0d-b7cf-890ae92bee26", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264066014900, "endTime": 314264066046400}, "additional": {"logType": "info", "children": [], "durationId": "b5af92ee-15b3-4087-b25e-3ac576d0f699", "parent": "3104520b-dd13-4d0c-9fcf-089d69cd9e36"}}, {"head": {"id": "3104520b-dd13-4d0c-9fcf-089d69cd9e36", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261405651800, "endTime": 314264066066600}, "additional": {"logType": "info", "children": ["5fac45af-1952-4673-8ab5-0f8bc7fe767d", "d4618ab3-3896-4115-ad87-1908b93fd2e2", "5ff49b21-30b1-4f0d-b7cf-890ae92bee26"], "durationId": "dec300bd-a547-4c5a-98e6-58a2e46e34f4", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "aace22ba-392a-4b54-b781-9be628f76b0c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264067109100, "endTime": 314264342442700}, "additional": {"children": ["6a643744-3328-4704-9d41-cf55d3426bb4", "4b72296b-f25d-4007-b59f-19847e4a7b7f", "c190cef4-14ab-4aef-bb70-0c58a5d4ef2b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "66f53965-e497-4950-bd91-a0d6bdbe2ce4", "logId": "c24a5ed0-277f-45ce-943f-25b57395dc1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a643744-3328-4704-9d41-cf55d3426bb4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264070910400, "endTime": 314264070930400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aace22ba-392a-4b54-b781-9be628f76b0c", "logId": "81f4fd5e-85c6-4e90-8b3f-85b270bc9d76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81f4fd5e-85c6-4e90-8b3f-85b270bc9d76", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264070910400, "endTime": 314264070930400}, "additional": {"logType": "info", "children": [], "durationId": "6a643744-3328-4704-9d41-cf55d3426bb4", "parent": "c24a5ed0-277f-45ce-943f-25b57395dc1d"}}, {"head": {"id": "4b72296b-f25d-4007-b59f-19847e4a7b7f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264074129500, "endTime": 314264340583600}, "additional": {"children": ["9aaaf861-a76f-46bd-b9c4-4f1f16c796aa", "fa762204-6522-4773-8c9d-240d0237f7ba"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aace22ba-392a-4b54-b781-9be628f76b0c", "logId": "e13286fe-26fc-4749-884f-759612c2ff6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aaaf861-a76f-46bd-b9c4-4f1f16c796aa", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264074131000, "endTime": 314264107173900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b72296b-f25d-4007-b59f-19847e4a7b7f", "logId": "78c96479-3e0d-42fb-909d-be6470b90468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa762204-6522-4773-8c9d-240d0237f7ba", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264107190800, "endTime": 314264340568500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b72296b-f25d-4007-b59f-19847e4a7b7f", "logId": "ed857f59-2262-4d46-9055-97a43bd9141e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24716d0e-6f3c-4b79-aaba-13a2d06afde9", "name": "hvigorfile, resolving F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264074138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee06507-f55d-458e-a91f-0f4a17d1737d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264107029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c96479-3e0d-42fb-909d-be6470b90468", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264074131000, "endTime": 314264107173900}, "additional": {"logType": "info", "children": [], "durationId": "9aaaf861-a76f-46bd-b9c4-4f1f16c796aa", "parent": "e13286fe-26fc-4749-884f-759612c2ff6d"}}, {"head": {"id": "1034de36-3191-4012-be1c-296256d5ea5a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264107205200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce2684d3-b63e-46c0-9723-174414d07191", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264170446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22942480-aac3-44bf-9273-92839d882193", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264170827100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d834376-dd73-47c2-b331-ec09bcb1e7cc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264204297100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd0e6e8-3599-4074-8a91-517973509115", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264204625600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b664e9d9-ae31-4d8d-9ae5-2f9190b49e21", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264204760900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f89fb74-a2c1-43fc-b6a5-055dd825c9e8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264204864200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b81d9b6-60ff-4b48-919f-599dff43206d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264205619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "967427c1-7ece-44cd-a7f0-bb91347f1d35", "name": "Module entry task initialization takes 57 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264340058400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cddbee4-c56d-4e35-a75b-fdc30f52e011", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264340351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c554822c-7d25-419c-bfba-3537c24d959c", "name": "hvigorfile, no custom plugins were found in F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264340444500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c181260-1ad6-42d9-8f00-7846fe933afe", "name": "hvigorfile, resolve finished F:\\e-wallet\\harmony\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264340505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed857f59-2262-4d46-9055-97a43bd9141e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264107190800, "endTime": 314264340568500}, "additional": {"logType": "info", "children": [], "durationId": "fa762204-6522-4773-8c9d-240d0237f7ba", "parent": "e13286fe-26fc-4749-884f-759612c2ff6d"}}, {"head": {"id": "e13286fe-26fc-4749-884f-759612c2ff6d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264074129500, "endTime": 314264340583600}, "additional": {"logType": "info", "children": ["78c96479-3e0d-42fb-909d-be6470b90468", "ed857f59-2262-4d46-9055-97a43bd9141e"], "durationId": "4b72296b-f25d-4007-b59f-19847e4a7b7f", "parent": "c24a5ed0-277f-45ce-943f-25b57395dc1d"}}, {"head": {"id": "c190cef4-14ab-4aef-bb70-0c58a5d4ef2b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264342398500, "endTime": 314264342417400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aace22ba-392a-4b54-b781-9be628f76b0c", "logId": "980ed976-45e2-447e-ac79-e0d7c458bc58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "980ed976-45e2-447e-ac79-e0d7c458bc58", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264342398500, "endTime": 314264342417400}, "additional": {"logType": "info", "children": [], "durationId": "c190cef4-14ab-4aef-bb70-0c58a5d4ef2b", "parent": "c24a5ed0-277f-45ce-943f-25b57395dc1d"}}, {"head": {"id": "c24a5ed0-277f-45ce-943f-25b57395dc1d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264067109100, "endTime": 314264342442700}, "additional": {"logType": "info", "children": ["81f4fd5e-85c6-4e90-8b3f-85b270bc9d76", "e13286fe-26fc-4749-884f-759612c2ff6d", "980ed976-45e2-447e-ac79-e0d7c458bc58"], "durationId": "aace22ba-392a-4b54-b781-9be628f76b0c", "parent": "9a05ae15-b12d-4119-894a-7c097951d091"}}, {"head": {"id": "9a05ae15-b12d-4119-894a-7c097951d091", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264066086600, "endTime": 314264342461700}, "additional": {"logType": "info", "children": ["c24a5ed0-277f-45ce-943f-25b57395dc1d"], "durationId": "66f53965-e497-4950-bd91-a0d6bdbe2ce4", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "81eeeabd-35ab-4829-a1ff-8c8a60f788e3", "name": "watch files: [\n  'F:\\\\e-wallet\\\\harmony\\\\hvigorfile.ts',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265119049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d800b4a5-9ffd-4227-a79b-0ac76d90d6f6", "name": "hvigorfile, resolve hvigorfile dependencies in 779 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265121210000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46d69c98-acf8-47ef-bc09-b6e6f7b682c8", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314264342471400, "endTime": 314265122027900}, "additional": {"logType": "info", "children": [], "durationId": "20b32c06-2a97-4559-af98-a63127557923", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "d537dbd9-0a55-464d-beda-8db0183c47d5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265123090800, "endTime": 314265123323200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "logId": "5e826527-08b4-418c-aa1d-94e3980b7d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "552e5585-d166-4515-92e7-3eabc22384c0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265123128400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e826527-08b4-418c-aa1d-94e3980b7d60", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265123090800, "endTime": 314265123323200}, "additional": {"logType": "info", "children": [], "durationId": "d537dbd9-0a55-464d-beda-8db0183c47d5", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "dc4b20c1-046b-4428-97fd-5dc626a4e14a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265179964400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b731ce8d-a918-43ec-b033-a328593c74fe", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265306620500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d240850f-da63-4be7-b918-00a47e394116", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265123337600, "endTime": 314265310479000}, "additional": {"logType": "info", "children": [], "durationId": "b6f0de97-31d9-4672-a1c8-6ef61975f7df", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "ee53d787-b59f-46b8-b243-a026bb25b3b7", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265310670700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "107cc42e-10e9-4461-aa0b-1ac0556636e3", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265522470800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b5d905-6019-44aa-982d-c57b419ca101", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265522664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361328b6-bf7b-4024-b450-a2caa8e5d15c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265540379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df3520e-fde1-4bc7-9379-404c1f1cb40a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265544100200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5dd7c94-27b5-4a38-97f8-458f2b052959", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265544375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b473d0b-2e82-4096-90c3-14e5bfde5bbf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265310587000, "endTime": 314265600832900}, "additional": {"logType": "info", "children": [], "durationId": "e2d07e0e-c60a-4317-8851-4969d3261fe4", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "cb20b852-2691-4298-9477-2d7eb4aa6bd1", "name": "Configuration phase cost:4 s 252 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265600898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "310ca92f-2477-4bad-bc17-9c1041c015f1", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265600861900, "endTime": 314265601035800}, "additional": {"logType": "info", "children": [], "durationId": "969dfdf4-9f58-4227-9ad3-44ab33466d63", "parent": "0fa6acda-2495-442d-824b-ffe1ce6f9f13"}}, {"head": {"id": "0fa6acda-2495-442d-824b-ffe1ce6f9f13", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314261342501400, "endTime": 314265601050200}, "additional": {"logType": "info", "children": ["cdbdcc38-11bd-4bc6-bf82-2020ff78da36", "446f8ae8-762a-4377-bd18-b67354ee2fd4", "3104520b-dd13-4d0c-9fcf-089d69cd9e36", "9a05ae15-b12d-4119-894a-7c097951d091", "46d69c98-acf8-47ef-bc09-b6e6f7b682c8", "d240850f-da63-4be7-b918-00a47e394116", "2b473d0b-2e82-4096-90c3-14e5bfde5bbf", "310ca92f-2477-4bad-bc17-9c1041c015f1", "5e826527-08b4-418c-aa1d-94e3980b7d60"], "durationId": "12421f63-e177-4dde-a04c-f78cf9cb9e32", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "cac6701d-f073-4ccc-8687-102c107c614f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265623941500, "endTime": 314265623958000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8", "logId": "e9111256-f574-447f-a7aa-fb79b7950408"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9111256-f574-447f-a7aa-fb79b7950408", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265623941500, "endTime": 314265623958000}, "additional": {"logType": "info", "children": [], "durationId": "cac6701d-f073-4ccc-8687-102c107c614f", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "db650bb0-3a8c-406c-b29a-b0c96c178468", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265601077900, "endTime": 314265623973200}, "additional": {"logType": "info", "children": [], "durationId": "d80e222b-03bd-4753-a077-77f32de3fe27", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "a2c3a836-7bcc-4914-917d-aa19fccf471b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265623979700, "endTime": 314265624520600}, "additional": {"logType": "info", "children": [], "durationId": "88e2e120-61b7-40ec-830a-5a73bc0181ae", "parent": "0f6d250f-8449-4a0c-9870-1fbe95a270e9"}}, {"head": {"id": "0f6d250f-8449-4a0c-9870-1fbe95a270e9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260339582000, "endTime": 314265624531900}, "additional": {"logType": "info", "children": ["5d265589-be5a-45b1-b2f3-ae28ef347852", "0fa6acda-2495-442d-824b-ffe1ce6f9f13", "db650bb0-3a8c-406c-b29a-b0c96c178468", "a2c3a836-7bcc-4914-917d-aa19fccf471b", "ed3e8131-eb2b-46c0-b605-bf4217a64e95", "c8913c44-ddf3-4d97-8032-b74ca01c05b9", "e9111256-f574-447f-a7aa-fb79b7950408"], "durationId": "7528f3ea-06f9-4b4c-93b1-03e09562d3e8"}}, {"head": {"id": "afa84cca-f476-401e-adc6-08995671c46b", "name": "Configuration task cost before running: 5 s 289 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265624688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d886c3f2-4912-4dfa-9e88-fd323c74968a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265647372000, "endTime": 314265669052100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e4a0ca8e-2838-4faf-a8e3-a0a8f9f1ba89", "logId": "77492533-f33d-46dc-89e6-0044538e7936"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4a0ca8e-2838-4faf-a8e3-a0a8f9f1ba89", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265632408700}, "additional": {"logType": "detail", "children": [], "durationId": "d886c3f2-4912-4dfa-9e88-fd323c74968a"}}, {"head": {"id": "8e6ab95e-9e71-47d9-9ade-6e6691b2b7a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265632992000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5d4311-a48a-4d54-918b-2ba875f1c10f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265633105100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f06f3e-8268-4f6c-8247-00b651716efa", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265647395200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0773673-8833-443a-aac9-86fbe159b7d8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265668783200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ec2010-9144-4fcf-b596-cb3815603eb7", "name": "entry : default@PreBuild cost memory 0.42887115478515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265668951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77492533-f33d-46dc-89e6-0044538e7936", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265647372000, "endTime": 314265669052100}, "additional": {"logType": "info", "children": [], "durationId": "d886c3f2-4912-4dfa-9e88-fd323c74968a"}}, {"head": {"id": "31c15ae8-ea44-4040-9738-efeb06ef8471", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265674870600, "endTime": 314265696468800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8a42d29d-ab4e-4c90-90a0-d64e25a993f2", "logId": "587d7db6-5b26-421d-bda8-b797d18e0051"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a42d29d-ab4e-4c90-90a0-d64e25a993f2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265673337100}, "additional": {"logType": "detail", "children": [], "durationId": "31c15ae8-ea44-4040-9738-efeb06ef8471"}}, {"head": {"id": "db7298da-30b7-4ab8-aae7-b97c244a7817", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265673929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3f78e2-319f-495d-bd76-f2976d9e45bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265674081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777e0530-bcdc-42f9-b856-e444c28218d1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265674883900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9492ebb-a7ec-4ada-a0f6-5238771ce5a0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265685792200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b287ca-5634-4367-a34c-9eb8a82f9cb3", "name": "entry : default@MergeProfile cost memory 0.115509033203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265696311300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "587d7db6-5b26-421d-bda8-b797d18e0051", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265674870600, "endTime": 314265696468800}, "additional": {"logType": "info", "children": [], "durationId": "31c15ae8-ea44-4040-9738-efeb06ef8471"}}, {"head": {"id": "050a69de-c7da-4f0c-87bd-4d328f30cc9e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265713998500, "endTime": 314265803208500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "745001f9-19b8-4d98-907a-f5ac479e3cde", "logId": "0ee79d6a-ff5d-4316-a458-5f1569bec261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "745001f9-19b8-4d98-907a-f5ac479e3cde", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265711956700}, "additional": {"logType": "detail", "children": [], "durationId": "050a69de-c7da-4f0c-87bd-4d328f30cc9e"}}, {"head": {"id": "4a959ab6-a813-4286-92e0-6d2e6bc54e71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265712519100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8009feec-8da5-485a-aa44-240ca3f06a70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265712643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47560793-7ec6-49a2-a281-f2bd596d26ca", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265714038700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877a09f5-fd2c-4e93-889a-e4e2081e9d99", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 31 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265744712400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c722d08d-0e16-4611-8bbe-56c74cbd24a2", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 58 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265802874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f308311c-c0cc-4f99-9674-60cb73c97f49", "name": "entry : default@CreateBuildProfile cost memory 0.10199737548828125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265803101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee79d6a-ff5d-4316-a458-5f1569bec261", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265713998500, "endTime": 314265803208500}, "additional": {"logType": "info", "children": [], "durationId": "050a69de-c7da-4f0c-87bd-4d328f30cc9e"}}, {"head": {"id": "e35f02be-7ef9-41c2-aa0d-66a33e6e8eee", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814063200, "endTime": 314265814835200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ea621a7d-76f1-4dd8-bdac-58223f0540f1", "logId": "5269a253-7063-464c-bf5b-b02a23775c78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea621a7d-76f1-4dd8-bdac-58223f0540f1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265805843600}, "additional": {"logType": "detail", "children": [], "durationId": "e35f02be-7ef9-41c2-aa0d-66a33e6e8eee"}}, {"head": {"id": "80ae654c-bdc1-4562-a4bb-0e448c4c5b45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265806400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a2e36a-ef09-485e-8882-a3189bb85d93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265806514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13bd7ea1-de83-4b76-b780-8a8abbcc8834", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814082300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dfe4b0c-b669-4b16-8620-c7c5f3b38f59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b281dbcf-de86-4781-917b-960c703ebdb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814548000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c827f00-3016-4643-ad0c-cb45d0df3e4e", "name": "entry : default@PreCheckSyscap cost memory 0.0374298095703125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814647300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0f3418-84fa-4390-87c7-6476b97e04e9", "name": "runTaskFromQueue task cost before running: 5 s 479 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814758900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5269a253-7063-464c-bf5b-b02a23775c78", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265814063200, "endTime": 314265814835200, "totalTime": 673200}, "additional": {"logType": "info", "children": [], "durationId": "e35f02be-7ef9-41c2-aa0d-66a33e6e8eee"}}, {"head": {"id": "366a36c6-06b4-4075-9075-32776071e72b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265842772400, "endTime": 314265857172200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cdb9641a-1fb2-4cef-bf2c-f53f44e48c87", "logId": "6b478759-27c2-4541-a73d-212f7489ee4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdb9641a-1fb2-4cef-bf2c-f53f44e48c87", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265817465400}, "additional": {"logType": "detail", "children": [], "durationId": "366a36c6-06b4-4075-9075-32776071e72b"}}, {"head": {"id": "f5107a4e-40ac-42f0-9f03-1d0212713420", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265818055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc9e4b9-777b-48d3-9f73-e392544a807e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265818180200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c2322d-42da-4175-8c7a-b5e27ceaa537", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265842797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601ed2ff-3cf1-4a38-bbeb-253f4debcb9b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265843786900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed816d5a-91f4-4820-a3f3-8a28498784be", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265844628900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7577fa-5d52-4c55-bb12-e6f2e453155b", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06732177734375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265856951200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b478759-27c2-4541-a73d-212f7489ee4b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265842772400, "endTime": 314265857172200}, "additional": {"logType": "info", "children": [], "durationId": "366a36c6-06b4-4075-9075-32776071e72b"}}, {"head": {"id": "bc1c2c9d-b412-4f63-bfdf-1586c7acb0a8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265862018000, "endTime": 314265906030500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c1d52d0b-f008-42af-aa19-7a43adee6e1e", "logId": "8d07126a-cbbb-42a2-a35e-d923be6de5e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1d52d0b-f008-42af-aa19-7a43adee6e1e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265859662100}, "additional": {"logType": "detail", "children": [], "durationId": "bc1c2c9d-b412-4f63-bfdf-1586c7acb0a8"}}, {"head": {"id": "0d680152-ad56-4fd5-8a3a-4344d1797a32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265860325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9687f713-0f3e-46ab-8fda-7c371f99be85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265860471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1614c5fa-1dab-4876-992e-ac05c842dabb", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265862033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f2d2f62-5606-44f9-98f0-2aaf6fa638a6", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 44 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265905699200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c19f4a6-697c-4ae5-9a19-a9d8c3042d71", "name": "entry : default@ProcessProfile cost memory 0.05928802490234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265905908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d07126a-cbbb-42a2-a35e-d923be6de5e5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265862018000, "endTime": 314265906030500}, "additional": {"logType": "info", "children": [], "durationId": "bc1c2c9d-b412-4f63-bfdf-1586c7acb0a8"}}, {"head": {"id": "b9483298-187e-4cc5-af5f-7b03f09b0eb4", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265911109900, "endTime": 314265943356600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "41287e07-22ba-449e-b436-f3df5dd6feb6", "logId": "009da752-54af-46cf-bae3-244b113c3b54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41287e07-22ba-449e-b436-f3df5dd6feb6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265908353700}, "additional": {"logType": "detail", "children": [], "durationId": "b9483298-187e-4cc5-af5f-7b03f09b0eb4"}}, {"head": {"id": "9a1d48bb-7ee6-4cf2-8cf2-4c496b4bafb3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265908919900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7031adfe-5583-4671-8956-7ae99be365e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265909062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c158fd57-b06d-4b72-ae8c-a9f36536dbfd", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265911125800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a00c83-2096-4f24-8a0e-22825a09aeb7", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265942996600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89391142-e531-4f5f-98fa-717cc984966e", "name": "entry : default@ProcessRouterMap cost memory 0.19829559326171875", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265943255800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "009da752-54af-46cf-bae3-244b113c3b54", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265911109900, "endTime": 314265943356600}, "additional": {"logType": "info", "children": [], "durationId": "b9483298-187e-4cc5-af5f-7b03f09b0eb4"}}, {"head": {"id": "ed89e77c-8506-4867-8d73-8b84b929a0c5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265972242600, "endTime": 314265996815500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3c8cba8d-0a0c-47ab-8da3-c96eabbcceeb", "logId": "10072142-1d1d-45ca-acb5-4e9aa2c1bf65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c8cba8d-0a0c-47ab-8da3-c96eabbcceeb", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265946969500}, "additional": {"logType": "detail", "children": [], "durationId": "ed89e77c-8506-4867-8d73-8b84b929a0c5"}}, {"head": {"id": "96bb4b71-8361-40ec-901f-a9fa625af9d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265947749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96097f9e-4d66-4289-9abe-41ccf4189a93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265947893100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526dae53-70e0-4ae7-b0d7-9bb8b22cbe6d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265949416100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6811b2-d567-4af4-a965-3011bd56c714", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265991933400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64614cb4-a878-48cd-80b1-419210651c51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265992322000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074e0ceb-b5a2-4665-927f-408379235d0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265992530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfc20f2-eacb-4734-87ee-974b7b1f1ef4", "name": "entry : default@PreviewProcessResource cost memory 0.06905364990234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265993213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "573f920f-f638-46ac-b793-0bed1591ddeb", "name": "runTaskFromQueue task cost before running: 5 s 661 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265996531200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10072142-1d1d-45ca-acb5-4e9aa2c1bf65", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314265972242600, "endTime": 314265996815500, "totalTime": 21223800}, "additional": {"logType": "info", "children": [], "durationId": "ed89e77c-8506-4867-8d73-8b84b929a0c5"}}, {"head": {"id": "848a6339-1b45-42ab-bba8-90d25cba1f27", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266036672300, "endTime": 314266118483000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8bb88dd9-66be-4a02-9e15-f1cfaebbd4fb", "logId": "719be982-65c0-4280-b40e-ff40c19f73d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bb88dd9-66be-4a02-9e15-f1cfaebbd4fb", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266004284600}, "additional": {"logType": "detail", "children": [], "durationId": "848a6339-1b45-42ab-bba8-90d25cba1f27"}}, {"head": {"id": "a6ddc828-604a-424a-86de-f2bc46cc2fe4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266005549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808ac365-cd5d-4544-9184-29cb5269bd3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266005786900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "472101c7-2b08-4080-b7e1-6f68892e9608", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266036708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c662c7-a0f9-424e-90c7-603f8618d0a4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266100608100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f2b527a-9210-4cb6-a951-5bbf360dfc14", "name": "entry : default@GenerateLoaderJson cost memory 0.7276992797851562", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266100786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719be982-65c0-4280-b40e-ff40c19f73d0", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266036672300, "endTime": 314266118483000}, "additional": {"logType": "info", "children": [], "durationId": "848a6339-1b45-42ab-bba8-90d25cba1f27"}}, {"head": {"id": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266135020000, "endTime": 314269386843300}, "additional": {"children": ["dca1100c-3ee5-4312-9e5b-6b14f3b162ec", "243797e0-fb61-4016-b57f-515a390e38b7", "d4797f2d-8e72-4abf-a37d-ad73309ca621", "9d706cd3-2422-438e-b0d0-034fe3798558"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\resources' has been changed."], "detailId": "f1c7c9c1-a14d-4833-b6b0-fdb7dbc54c31", "logId": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1c7c9c1-a14d-4833-b6b0-fdb7dbc54c31", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266126981900}, "additional": {"logType": "detail", "children": [], "durationId": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84"}}, {"head": {"id": "5b4d00df-a947-4968-80ab-75521993c944", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266127523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00fd113f-f0c8-4105-ab65-9b2b55e7bbcf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266127631700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00bd50f-03c3-4c67-bbce-55d11463eccf", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266128654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958fd900-3fd1-415a-82c9-1cd4e3979460", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266135087200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b4fd20d-b659-4000-8ee8-39531104c702", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266296064900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ddbf5e-b52a-4bce-9e81-ae72b3533885", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 170 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266307530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca1100c-3ee5-4312-9e5b-6b14f3b162ec", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266322199200, "endTime": 314266985505600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84", "logId": "52e134e6-6d88-4688-9558-8d70bc37a097"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52e134e6-6d88-4688-9558-8d70bc37a097", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266322199200, "endTime": 314266985505600}, "additional": {"logType": "info", "children": [], "durationId": "dca1100c-3ee5-4312-9e5b-6b14f3b162ec", "parent": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0"}}, {"head": {"id": "43dd733e-995f-4a8b-8e59-95b2d4f302f2", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\AppScope\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314267014530600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243797e0-fb61-4016-b57f-515a390e38b7", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314267019118000, "endTime": 314268834506800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84", "logId": "a74ea2b6-aff7-4445-a570-bd6db6b60009"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f2b38ab-9f3c-426c-b4d0-d5168cc1cd7a", "name": "current process  memoryUsage: {\n  rss: 64716800,\n  heapTotal: 119869440,\n  heapUsed: 112150680,\n  external: 3141669,\n  arrayBuffers: 135534\n} os memoryUsage :4.812644958496094", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314267026767000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8284bd2f-bd32-4e06-b43f-162de23c9a29", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268827425000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74ea2b6-aff7-4445-a570-bd6db6b60009", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314267019118000, "endTime": 314268834506800}, "additional": {"logType": "info", "children": [], "durationId": "243797e0-fb61-4016-b57f-515a390e38b7", "parent": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0"}}, {"head": {"id": "2551ec96-2c8b-4d63-a046-1fcf47618744", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268835382500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4797f2d-8e72-4abf-a37d-ad73309ca621", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268875850800, "endTime": 314268963027100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84", "logId": "d63785f1-ccfa-4501-a145-497a8c025f7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4434bc2-0d69-4195-8cf8-b9e21cd3e07a", "name": "current process  memoryUsage: {\n  rss: 87633920,\n  heapTotal: 119869440,\n  heapUsed: 102889696,\n  external: 3113736,\n  arrayBuffers: 108035\n} os memoryUsage :4.8316802978515625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268879156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabf1a51-d9de-4180-8b75-0f319cd1bde1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268960594600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63785f1-ccfa-4501-a145-497a8c025f7c", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268875850800, "endTime": 314268963027100}, "additional": {"logType": "info", "children": [], "durationId": "d4797f2d-8e72-4abf-a37d-ad73309ca621", "parent": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0"}}, {"head": {"id": "283485e6-3f37-441c-85c5-18f8b25804da", "name": "Use tool [E:\\hongmeng\\HM\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'E:\\\\hongmeng\\\\HM\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***y',\n  '-r',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'F:\\\\e-wallet\\\\harmony\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268963743700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d706cd3-2422-438e-b0d0-034fe3798558", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268964837700, "endTime": 314269374346700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84", "logId": "233f146b-58df-4b12-959f-d9e1bc90303f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ff5427a-600a-49b3-ab75-68892e5951bf", "name": "current process  memoryUsage: {\n  rss: 87711744,\n  heapTotal: 119869440,\n  heapUsed: 102132840,\n  external: 3074608,\n  arrayBuffers: 69302\n} os memoryUsage :4.838596343994141", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268965749100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b46800-40cb-40d3-a3c8-01c4a708371e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269307425200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "233f146b-58df-4b12-959f-d9e1bc90303f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314268964837700, "endTime": 314269374346700}, "additional": {"logType": "info", "children": [], "durationId": "9d706cd3-2422-438e-b0d0-034fe3798558", "parent": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0"}}, {"head": {"id": "8bf420c7-e06f-493c-802b-21ff011fe69d", "name": "entry : default@PreviewCompileResource cost memory -10.378410339355469", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269385274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0464b724-194a-46e6-9757-3732f0eb648c", "name": "runTaskFromQueue task cost before running: 9 s 50 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269386184300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26818dc-b99e-456c-aeb6-41d37cd1dfb0", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314266135020000, "endTime": 314269386843300, "totalTime": 3250851100}, "additional": {"logType": "info", "children": ["52e134e6-6d88-4688-9558-8d70bc37a097", "a74ea2b6-aff7-4445-a570-bd6db6b60009", "d63785f1-ccfa-4501-a145-497a8c025f7c", "233f146b-58df-4b12-959f-d9e1bc90303f"], "durationId": "8888dd9f-f7c2-4476-92aa-8c1d23c64a84"}}, {"head": {"id": "4a2d447d-cc58-425b-b16b-42dd8f50c6fe", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269406902000, "endTime": 314269408241800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f85ca18c-9ddd-41b8-9a97-a2bbab7cb125", "logId": "67e62292-c045-48ee-ab5d-b8bfb69c0de7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f85ca18c-9ddd-41b8-9a97-a2bbab7cb125", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269403542400}, "additional": {"logType": "detail", "children": [], "durationId": "4a2d447d-cc58-425b-b16b-42dd8f50c6fe"}}, {"head": {"id": "30fe680a-c6e1-4277-bd5e-16582d03d319", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269406103300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740af416-ec78-4109-90a0-88979ca3b32a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269406456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb80904b-afc8-48f7-97d8-2dfc801c4571", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269406931400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415c590a-cf3c-4be2-97a6-5367d9d2724a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269407265500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2b47ad3-19f0-4f8b-9371-cc7fe997ede3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269407447600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87b942d-66ae-4ea6-aa92-778ffe2f1d5f", "name": "entry : default@PreviewHookCompileResource cost memory 0.03863525390625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269407755900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5097a1de-a488-4a7f-90ef-4ddce8c23a5b", "name": "runTaskFromQueue task cost before running: 9 s 72 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269408045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e62292-c045-48ee-ab5d-b8bfb69c0de7", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269406902000, "endTime": 314269408241800, "totalTime": 1078800}, "additional": {"logType": "info", "children": [], "durationId": "4a2d447d-cc58-425b-b16b-42dd8f50c6fe"}}, {"head": {"id": "fa1e058f-c40d-4c60-ba81-4133deb23404", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269418439600, "endTime": 314269652733800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "1cb2818c-6c8e-40a4-8f66-c6fb75d0d5a0", "logId": "2b8bc71b-2105-44c0-a8a5-f322017cb9ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cb2818c-6c8e-40a4-8f66-c6fb75d0d5a0", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269414324200}, "additional": {"logType": "detail", "children": [], "durationId": "fa1e058f-c40d-4c60-ba81-4133deb23404"}}, {"head": {"id": "348ae64d-fcb3-4f45-b9d8-cbf642afcf0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269416039600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f382b4e8-d640-494c-8480-d94e472bf877", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269416334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a527e585-8b9b-41fa-aba3-1aad5c9977f4", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269418470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da826122-d8d4-4a25-b3bc-c0b5672564de", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269423393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1fc8d9-0015-4fa4-8982-4ae4593b0a90", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269423754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8526796-f741-40be-a9e4-44449f29b90e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269424033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf32fdc-aa83-41ff-8449-e226fe388c8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269424211800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc0bc68-e297-4ce4-ac59-2cec4a504d77", "name": "entry : default@CopyPreviewProfile cost memory 0.23740386962890625", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269651535800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f48faa-04ff-48c0-809b-8093515e4cf3", "name": "runTaskFromQueue task cost before running: 9 s 317 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269652307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8bc71b-2105-44c0-a8a5-f322017cb9ca", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269418439600, "endTime": 314269652733800, "totalTime": 233709700}, "additional": {"logType": "info", "children": [], "durationId": "fa1e058f-c40d-4c60-ba81-4133deb23404"}}, {"head": {"id": "5ba3df33-c8b0-4c2a-a719-80960958875b", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269866970300, "endTime": 314269867546000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "285cfdde-e089-4622-83a5-f061221e46ec", "logId": "548dbcb0-f225-453a-9e3d-a2cb3287dfbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "285cfdde-e089-4622-83a5-f061221e46ec", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269663404900}, "additional": {"logType": "detail", "children": [], "durationId": "5ba3df33-c8b0-4c2a-a719-80960958875b"}}, {"head": {"id": "5e9fd658-f1d4-492b-87cf-b37d03646b77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269666559500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80b201a-c634-46c6-a551-c71eac927958", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269667089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6574778-cb9a-4058-96cd-06dc9a5ef9f4", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269866987200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d97329-2243-45e3-88cc-89f2f4851e22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269867170700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16923b52-9502-4efc-bec7-7ca53a882cb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269867246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58554107-b71a-4056-b520-313ef9106987", "name": "entry : default@ReplacePreviewerPage cost memory 0.03845977783203125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269867362600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe932c6a-a5b5-46a5-b63a-983c321534b3", "name": "runTaskFromQueue task cost before running: 9 s 532 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269867474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548dbcb0-f225-453a-9e3d-a2cb3287dfbd", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269866970300, "endTime": 314269867546000, "totalTime": 478800}, "additional": {"logType": "info", "children": [], "durationId": "5ba3df33-c8b0-4c2a-a719-80960958875b"}}, {"head": {"id": "8144f070-92f9-4888-aca3-5ebbcf2ffc46", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869665100, "endTime": 314269869994000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "eacdddd2-9bf2-4ab0-b936-4111d512aacf", "logId": "d8608337-2ce3-4d96-92f8-f80f9fd6f974"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eacdddd2-9bf2-4ab0-b936-4111d512aacf", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869582500}, "additional": {"logType": "detail", "children": [], "durationId": "8144f070-92f9-4888-aca3-5ebbcf2ffc46"}}, {"head": {"id": "e2a8a975-3e09-4fbc-8913-b221a4123794", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869674200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa7a82d6-078b-4ced-b7b6-be5c31bf3012", "name": "entry : buildPreviewerResource cost memory 0.01186370849609375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45dfbc6e-a493-4ffc-bde6-d333f0f46306", "name": "runTaskFromQueue task cost before running: 9 s 534 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8608337-2ce3-4d96-92f8-f80f9fd6f974", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269869665100, "endTime": 314269869994000, "totalTime": 241000}, "additional": {"logType": "info", "children": [], "durationId": "8144f070-92f9-4888-aca3-5ebbcf2ffc46"}}, {"head": {"id": "a3a2c10d-4613-4640-8043-cc9aa354cf52", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269873651400, "endTime": 314269877577700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "d8b02ec3-8862-424a-8759-ccef8fbc496b", "logId": "3998c247-49d7-45f1-b9c5-02687bad5c25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8b02ec3-8862-424a-8759-ccef8fbc496b", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269872001600}, "additional": {"logType": "detail", "children": [], "durationId": "a3a2c10d-4613-4640-8043-cc9aa354cf52"}}, {"head": {"id": "824296d2-a5f4-4352-80c2-53df0d5526f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269872597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6459224-35c3-4668-927d-127a54e188dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269872749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4878f2-5bc5-4974-889f-d66496cb206e", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269873662800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d8d21c-c13f-4db2-ae4c-b6f273533e75", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269876013600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db80819-5924-4984-b673-acc5e3fa333a", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269876172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "237a87f0-fc19-4508-a53a-f95797c00c83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269876286800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ee98f7-d9c2-4e74-879e-2f70dee0cfb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269876351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4beab3c-bae9-46f2-b390-e4e897ff2ba6", "name": "entry : default@PreviewUpdateAssets cost memory 0.14034271240234375", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269877368700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee85410-32f5-4bcd-b9ab-43833971db69", "name": "runTaskFromQueue task cost before running: 9 s 542 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269877503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3998c247-49d7-45f1-b9c5-02687bad5c25", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269873651400, "endTime": 314269877577700, "totalTime": 3825300}, "additional": {"logType": "info", "children": [], "durationId": "a3a2c10d-4613-4640-8043-cc9aa354cf52"}}, {"head": {"id": "e3aec278-ce26-4443-9455-66d008a286f2", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269886701700}, "additional": {"children": ["1254ed12-0e2a-466e-930d-797917f53a56"], "state": "running", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "d804fb82-a841-457c-9cb6-59a4348483f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d804fb82-a841-457c-9cb6-59a4348483f1", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269880328500}, "additional": {"logType": "detail", "children": [], "durationId": "e3aec278-ce26-4443-9455-66d008a286f2"}}, {"head": {"id": "76f7aeaa-8a51-4654-8d35-202431600bc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269880908300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ee6090-f808-40a3-b118-5650d5afdef7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269881017900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d78ab3aa-d851-4461-a5f4-b9c2f11d1626", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269886717100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a322794-de08-4641-9e15-549d0b4dcba4", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269902062400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872016ae-5bb8-4d15-957f-079229ec992e", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314269902297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1254ed12-0e2a-466e-930d-797917f53a56", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21348, "tid": "Worker2", "startTime": 314270076360000}, "additional": {"children": [], "state": "running", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e3aec278-ce26-4443-9455-66d008a286f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95d13ba5-6737-475c-9d92-af7f41d34a41", "name": "entry : default@PreviewArkTS cost memory 1.07501220703125", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314270088371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b87150c-c9e3-4672-8c08-6b4fa81f5007", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235055300, "endTime": 314303235266600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f14ad29f-0011-4624-ac65-39a9e2e8348e", "logId": "f1281cc2-816b-4712-8f0f-bb23e5c38649"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1281cc2-816b-4712-8f0f-bb23e5c38649", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235055300, "endTime": 314303235266600}, "additional": {"logType": "info", "children": [], "durationId": "7b87150c-c9e3-4672-8c08-6b4fa81f5007"}}, {"head": {"id": "4de1dae6-ff78-442b-a756-a905879594a8", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314260336059300, "endTime": 314303235430500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 5}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "705dc700-d4ac-4cc6-86bd-019dff39fb07", "name": "BUILD FAILED in 42 s 900 ms ", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235527300}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "dcbb5d88-4656-4e3c-98b3-e51145f4a309", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09368b2d-a2a9-4930-8eca-c617490a3b85", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f24073-b312-4841-8ebb-5d2330a9015e", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235872500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569c0d9b-e0a0-4af0-abde-588e917ed4a5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303235939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b414809f-9a80-4f36-ad98-2f6d06838a31", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303236003700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb467ad-7572-4d29-bbb0-94abb27cc894", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303236065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4282a32-b041-48ea-b412-a007bc9bf062", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303236128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341c13b0-ff3f-4f67-b6e7-96b7254ac671", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303238779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454307a6-b59a-4f27-adb2-ab39bc19c9d1", "name": "Update task entry:default@PreviewCompileResource input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303239003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93236e1-c80e-46b9-9b8a-feff83bef705", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303239686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8afdb9b0-c453-421b-a5d8-21b0d6e5d7f4", "name": "Update task entry:default@PreviewCompileResource output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303251239600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6feab578-33cb-4c3d-9d76-7b5fa0935fc0", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303252123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c0a040a-132d-488b-865b-5dd651b4fb18", "name": "Update task entry:default@CopyPreviewProfile input file:F:\\e-wallet\\harmony\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303252379300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5cf787e-dc35-4e2e-92a5-6ca6bc1ef207", "name": "Update task entry:default@CopyPreviewProfile output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303253302200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d04e08-d12d-4a64-9296-96b2c7616d71", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303254261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "608aaf54-7d8e-4be5-a5ad-2d8504020d7c", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303254751700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03287707-0f80-4c0b-b94d-b159c4af526a", "name": "Update task entry:default@PreviewUpdateAssets output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303255114600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849aa15e-7333-4912-bfaa-21a92626af21", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303255613700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67253727-305d-413b-be56-658d8b5b6d20", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303259076900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfae6afc-445f-4eb8-97f4-c49269af40ba", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303260042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc58aa9-f97a-4e53-97a2-ce4bdf85441b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303260138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f6cd94-e151-4689-93e4-0ae07309cffc", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303260483800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78aac56d-4ea6-4a7c-9566-9b6f762997f1", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303261435000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a88c40-90af-4233-9912-06c37e06d85b", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303406005600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d997f2-05b3-47fe-97cd-a63d88078194", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303406646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f42074-c38b-4bbb-811b-62e6290fc96f", "name": "Update task entry:default@PreviewArkTS input file:F:\\e-wallet\\harmony\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303406986000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420bb446-2c5a-4be8-a118-828191033ffd", "name": "Update task entry:default@PreviewArkTS output file:F:\\e-wallet\\harmony\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303407336400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb2d846-ac7c-41eb-bc8f-95ce773645ca", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:152 ms .", "description": "", "type": "log"}, "body": {"pid": 21348, "tid": "Main Thread", "startTime": 314303407606600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}